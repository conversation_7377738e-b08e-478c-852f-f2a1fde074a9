tags:
  - Driver_admin
summary: Get Registered Driver List
description: >
  This endpoint retrieves a list of drivers who registered within the specified time range and/or regions. 
  It provides details like driver ID, name, mobile, region, registration date, approval status, 
  completed and upcoming bookings, and remarks. 
  It supports filtering by timestamps and regions.
parameters:
  - name: timestamp_gt
    in: formData
    required: false
    type: string
    format: date-time
    description: The start date for filtering drivers (inclusive)
    example: "2024-01-01 00:00:00"
  - name: timestamp_lt
    in: formData
    required: false
    type: string
    format: date-time
    description: The end date for filtering drivers (inclusive)
    example: "2024-12-31 23:59:59"
  - name: regions
    in: formData
    required: false
    type: string
    description: Comma-separated region IDs for filtering drivers. Use '-1' to fetch drivers from all regions.
    example: "1,2,3"
responses:
  200:
    description: Successfully retrieved the registered driver list
    schema:
      type: object
      properties:
        data:
          type: array
          items:
            type: object
            properties:
              driver_id:
                type: integer
                description: The unique identifier for the driver
                example: 101
              mobile:
                type: string
                description: Driver's mobile number
                example: "9876543210"
              name:
                type: string
                description: Driver's full name
                example: "John <PERSON>"
              region:
                type: integer
                description: The region ID where the driver is located
                example: 1
              registration_date:
                type: string
                format: date-time
                description: Driver's registration date (converted to IST)
                example: "2024-07-15 14:30:00"
              approval:
                type: integer
                description: Driver's approval status (1 for approved, 0 for pending)
                example: 1
              status:
                type: integer
                description: Driver's availability status (1 for available, 0 for not available)
                example: 1
              completed:
                type: integer
                description: The number of completed bookings by the driver
                example: 50
              upcoming:
                type: integer
                description: The number of upcoming bookings for the driver
                example: 2
              remark:
                type: string
                description: Any approval remarks associated with the driver
                example: "Verified"
        length:
          type: integer
          description: The total number of drivers returned
          example: 10
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
  400:
    description: Bad request (invalid timestamp or region format)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for errors)
          example: -1
        error:
          type: string
          description: Error message
          example: "Invalid timestamp or region format"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for server error)
          example: -1
        error:
          type: string
          description: Error message describing the server issue
          example: "Internal server error"

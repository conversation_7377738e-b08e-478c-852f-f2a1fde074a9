tags:
  - Payments
summary: Initiate Credit Order
description: >
  This endpoint allows a user to initiate a credit order.
security:
  - Bearer: []
  - X-CSRF-Token: []
parameters:
  - name: amount
    in: formData
    type: integer
    required: true
    description: The amount for the credit order
  - name: gateway
    in: formData
    type: integer
    required: false
    description: The payment gateway (default is Razorpay)
responses:
  200:
    description: Credit order initiated successfully
    schema:
      type: object
      properties:
        trans_id:
          type: integer
          description: Transaction ID
          example: 12345
        order_id:
          type: string
          description: Order ID generated by the payment gateway
          example: "order_abc123"
        checksumhash:
          type: string
          description: Payment gateway checksum hash for security
          example: "checksumhash123"
        message:
          type: string
          description: Success message
          example: "Initiated credit order successfully"
    examples:
      application/json:
        trans_id: 12345
        order_id: "order_abc123"
        checksumhash: "checksumhash123"
        message: "Initiated credit order successfully"
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for incomplete form details)
          example: -3
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -3
        message: "Incomplete form details"
  401_a:
    description: Failed to get identity
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for identity failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  401_c:
    description: Driver not approved
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unapproved driver)
          example: -1
        message:
          type: string
          description: Error message
          example: "Driver not approved"
    examples:
      application/json:
        success: -1
        message: "Driver not approved"
  401_d:
    description: User is driver, not allowed
    schema:
      type: object
      properties:
        order_id:
          type: integer
          description: Failure flag (-3 for driver restriction)
          example: -3
        message:
          type: string
          description: Error message
          example: "User is driver, not allowed"
    examples:
      application/json:
        order_id: -3
        message: "User is driver, not allowed"
  500:
    description: DB Error
    schema:
      type: object
      properties:
        order_id:
          type: integer
          description: Failure flag (-2 for DB error)
          example: -2
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        order_id: -2
        message: "DB Error"
  500_b:
    description: Razorpay payment order failed
    schema:
      type: object
      properties:
        order_id:
          type: integer
          description: Failure flag (-4 for Razorpay order failure)
          example: -4
        message:
          type: string
          description: Error message
          example: "Razorpay payment order failed"
    examples:
      application/json:
        order_id: -4
        message: "Razorpay payment order failed"

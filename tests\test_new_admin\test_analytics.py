from datetime import datetime,timedelta,date 
from models import Users,Drivers,Bookings,Trip,db,UserTrans, DriverTrans, BookingCancelled,DriverInfo, \
    UserRegistrationDetails,BookingAlloc,AdminAccess,TripLog,TripPricing
from affiliate_b2b.affiliate_models import Affiliate, AffBookingLogs,AffiliateWalletLogs
from conftest import unique_user_data,driver_bookings,driver_trip,driver_bookings_b2b,create_user_and_driver
from unittest.mock import patch
from booking_params import BookingParams
from sqlalchemy import exc
import random
from pytest import approx

def create_booking_and_trip(start_time,key, duration, price,net_rev=400, valid=1):
    try:
        # Create a booking
        booking = Bookings(
            user="4",
            skey=key,
            driver="5",
            lat=0.0,
            long=0.0,
            starttime=start_time.strftime("%H:%M:%S"),
            startdate=start_time.strftime("%Y-%m-%d"),
            dur=duration.total_seconds(),
            endtime=(start_time + duration).strftime("%H:%M:%S"),
            enddate=(start_time + duration).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
        )
        booking.valid = valid
        db.session.add(booking)
        db.session.flush()

        # Create a trip
        trip = Trip(
            booking.id,  
            start_time,  
            1, 1, 0
        )
        trip.price = price
        trip.net_rev=net_rev
        trip.endtime = start_time + duration  
        db.session.add(trip)
        data = unique_user_data()
        admin_user = Users(
            fname=data['fname'],
            lname=data['lname'],
            mobile=data['mobile'],
            email=data['email'],
            pwd=data['pwd'],
            role=Users.ROLE_SUPERADMIN,
            enabled=1, 
            )
        db.session.add(admin_user)
        db.session.flush()
        access=AdminAccess(admin_user_id=admin_user.id)
        db.session.add(access)
        db.session.flush()
        booking_alloc = BookingAlloc(booking.id, 10, access.admin_user_id)
        db.session.add(booking_alloc)
        
        db.session.commit()
        return booking

    except Exception as e:
        print(f"Unexpected error: {e}")
        db.session.rollback()
        
        
def create_booking_b2b_and_trip(start_time,key, duration, price,net_rev=400, valid=1,client_name="dummy_name"):
    try:
        # Create a booking
        master_affiliate = Affiliate( client_name, "dummy_name", 0 , -1, 10,None)
        db.session.add(master_affiliate)
        db.session.flush()
        master_affiliate.mapped_wallet_affiliate = master_affiliate.id
        db.session.commit()
        booking = Bookings(
            user=None,
            skey=key,
            driver="5",
            lat=0.0,
            long=0.0,
            starttime=start_time.strftime("%H:%M:%S"),
            startdate=start_time.strftime("%Y-%m-%d"),
            dur=duration.total_seconds(),
            endtime=(start_time + duration).strftime("%H:%M:%S"),
            enddate=(start_time + duration).strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
            type=BookingParams.TYPE_B2B
        )
        booking.valid = valid
        db.session.add(booking)
        db.session.flush()
        affbooklogs = AffBookingLogs(aff_id=master_affiliate.id, book_id=booking.id)
        db.session.add(affbooklogs)
        db.session.commit()
        # Create a trip
        trip = Trip(
            booking.id,  
            start_time,  
            1, 1, 0
        )
        trip.price = price
        trip.net_rev=net_rev
        trip.endtime = start_time + duration  
        db.session.add(trip)
        db.session.commit()
        return booking,master_affiliate,trip

    except Exception as e:
        print(f"Unexpected error: {e}")
        db.session.rollback()
          
def create_booking_with_rating(key,rating):
    try:
        booking = Bookings(
        user='1',
        skey=key,
        driver='2',
        lat=0.0,
        long=0.0,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.utcnow() + timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
        booking.valid = 1
        booking.user_rating=rating
        db.session.add(booking)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
       
#  API - /api/admin/analytics_count

def test_admin_analytics_count_missing_dates(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    # Send request without dates
    response = client.post('/api/admin/analytics_count', data=form_data,headers=auth_headers)

    # Assert failure response
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == -3
    assert 'Missing or invalid date values' in json_data['error']
    
    
def test_admin_analytics_count_success(client,admin_login):
    from_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    auth_headers, admin = admin_login
    form_data = {
        'from_date': from_date,
        'to_date': to_date,
        'region': '0'
    }
    data = unique_user_data()  
    user = Users(
            fname=data['fname'],
            lname=data['lname'],
            mobile=data['mobile'],
            email=data['email'],
            pwd=data['pwd'],
            role=Users.ROLE_USER,
            enabled=1, 
            )
    db.session.add(user)
    data_driver = unique_user_data()  
    driver_user = Users(
            fname=data_driver['fname'],
            lname=data_driver['lname'],
            mobile=data_driver['mobile'],
            email=data_driver['email'],
            pwd=data_driver['pwd'],
            role=Users.ROLE_DRIVER
            )
    db.session.add(driver_user)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    driver= Drivers(driver_user, data['license'], 'doc_path', 'pic_path', perma=True)
    db.session.add(driver)
    driver.approved=1
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    user_booking=driver_bookings(user.id,driver_user.id)
    user_trip=driver_trip(user_booking,0)

    response = client.post('/api/admin/analytics_count', data=form_data,headers=auth_headers)
    response_data = {
        'active_customers': 1,
        'active_drivers': 0,
        'average_sales_per_day': 167,
        'average_trips_per_day': 0.33,
        'average_trips_per_driver': 0,
        'average_trips_per_user': 1,
        'registered_customer': 1,
        'registered_drivers': 3,
        'success': 1,
        'total_bookings': 1,
        'total_revenue': 0,
        'total_sales': 500,
        'total_trips_count': 1,
        'trend_driver': 0,
        'trend_sales': 500,
        'trend_trips': 1,
        'trend_user': 1
    }
    response_json = response.json
    assert response_json['success'] == response_data['success']
    assert response_json['active_customers'] == response_data['active_customers']
    assert response_json['active_drivers'] == response_data['active_drivers']
    assert response_json['average_sales_per_day'] == response_data['average_sales_per_day']
    assert response_json['average_trips_per_day'] == response_data['average_trips_per_day']
    assert response_json['average_trips_per_driver'] == response_data['average_trips_per_driver']
    assert response_json['average_trips_per_user'] == response_data['average_trips_per_user']
    assert response_json['registered_customer'] == response_data['registered_customer']
    assert response_json['registered_drivers'] == response_data['registered_drivers']
    assert response_json['total_bookings'] == response_data['total_bookings']
    assert response_json['total_revenue'] == response_data['total_revenue']
    assert response_json['total_sales'] == response_data['total_sales']
    assert response_json['total_trips_count'] == response_data['total_trips_count']
    assert response_json['trend_driver'] == response_data['trend_driver']
    assert response_json['trend_sales'] == response_data['trend_sales']
    assert response_json['trend_trips'] == response_data['trend_trips']
    assert response_json['trend_user'] == response_data['trend_user']
    
def test_admin_analytics_count_exception(client,admin_login):
    from_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    auth_headers, admin = admin_login
    form_data = {
        'from_date': from_date,
        'to_date': to_date,
        'region': '0'
    }
    with patch('models.db.session.query') as mock_commit:
        mock_commit.side_effect = exc.IntegrityError(None, None, None)
        
        response = client.post('/api/admin/analytics_count', data=form_data,headers=auth_headers)
        
        # Assert that a failure response is returned
        assert response.status_code == 500
        json_data = response.get_json()
        assert json_data['success'] == 0
        assert 'error' in json_data
        
# ---------------------------------   
   
#  API - /api/admin/analytics_graph_daily_sales

def test_admin_analytics_daily_sales_success(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    user_booking=driver_bookings("2","2")
    user_trip=driver_trip(user_booking,0)
    today = datetime.utcnow()
    day_before_yesterday = today - timedelta(days=2)
    try:
        booking_yesterday = Bookings(
            user="4",
            skey='some_secret_key_2',
            driver="5",
            lat=0.0,
            long=0.0,
            starttime=(day_before_yesterday + timedelta(hours=9)).strftime("%H:%M:%S"),  
            startdate=day_before_yesterday.strftime("%Y-%m-%d"),
            dur=(timedelta(minutes=60)).total_seconds(), 
            endtime=(day_before_yesterday + timedelta(minutes=60)).strftime("%H:%M:%S"),
            enddate=day_before_yesterday.strftime("%Y-%m-%d"),
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
        )
        booking_yesterday.valid = 1
        db.session.add(booking_yesterday)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
    try:  
        trip_yesterday = Trip(
            booking_yesterday.id, 
            day_before_yesterday,  # Start time is the day before yesterday
            1,1,0
        )
        trip_yesterday.price=1000
        trip_yesterday.endtime=(day_before_yesterday + timedelta(minutes=60))
        db.session.add(trip_yesterday)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
    response = client.post('/api/admin/analytics_graph_daily_sales', data=form_data,headers=auth_headers)
    response_data=response.json
    assert response_data['success'] == 1
    day_data = response_data['data'][0]['day_data']
    assert isinstance(day_data, list)
    
    
# ---------------------------------
   
#  API - /api/admin/analytics_graph_sales
           
def test_admin_analytics_sales_success(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    today = datetime.utcnow()
    create_booking_and_trip(today,"skey", timedelta(minutes=60), 1000,800)  # This is for the week

    response = client.post('/api/admin/analytics_graph_sales', data=form_data,headers=auth_headers)
    response_data=response.json
    current_month = today.month  # Get current month (integer)
    current_week = today.isocalendar()[1]  # Get current week number (ISO calendar)
    current_year = today.year  # Get current year

    # Extracting month_data
    month_data = response_data['data'][0]['month_data'][0]  # Access the first item in the list
    assert month_data['month'] == current_month  # Check the current month
    assert month_data['sales'] == 1000  # Check sales

    # Extracting week_data
    week_data = response_data['data'][0]['week_data'][0]  # Access the first item in the list
    assert week_data['week'] == current_week  # Check the current week
    assert week_data['sales'] == 1000  # Check sales

    # Extracting year_data
    year_data = response_data['data'][0]['year_data'][0]  # Access the first item in the list
    assert year_data['year'] == current_year  # Check the current year
    assert year_data['sales'] == 1000  # Check sales
    
# ---------------------------------

#  API - /api/admin/analytics_graph_daily_revenue

def test_admin_analytics_daily_revenue_success(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    today = datetime.utcnow()
    today_ist = today + timedelta(hours=5, minutes=30)
    create_booking_and_trip(today,"skey", timedelta(minutes=60), 1000,600)
    response = client.post('/api/admin/analytics_graph_daily_revenue', data=form_data,headers=auth_headers)
    response_data=response.json
    assert response_data['success'] == 1
    day_data = response_data['data'][0]['day_data']
    assert isinstance(day_data, list)
    assert len(day_data) == 1
    day_entry = day_data[0]
    assert day_entry['day'] == today_ist.strftime('%d-%m-%Y')
    assert day_entry['revenues'] == 600

# ---------------------------------
    
#  API - /api/admin/analytics_graph_revenue

def test_admin_analytics_revenue_success(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    today = datetime.utcnow()
    create_booking_and_trip(today,"skey", timedelta(minutes=60), 1000,700) 

    response = client.post('/api/admin/analytics_graph_revenue', data=form_data,headers=auth_headers)
    response_data=response.json
    current_month = today.month  # Get current month (integer)
    current_week = today.isocalendar()[1]  # Get current week number (ISO calendar)
    current_year = today.year  # Get current year

    # Extracting month_data
    month_data = response_data['data'][0]['month_data'][0]  # Access the first item in the list
    assert month_data['month'] == current_month  # Check the current month
    assert month_data['revenues'] == 700  # Check sales

    # Extracting week_data
    week_data = response_data['data'][0]['week_data'][0]  # Access the first item in the list
    assert week_data['week'] == current_week  # Check the current week
    assert week_data['revenues'] == 700  # Check sales

    # Extracting year_data
    year_data = response_data['data'][0]['year_data'][0]  # Access the first item in the list
    assert year_data['year'] == current_year  # Check the current year
    assert year_data['revenues'] == 700  # Check sales

# ---------------------------------

#  API - /api/admin/analytics_graph_revenue

def test_admin_analytics_daily_trips_success(client,admin_login):
    auth_headers, admin = admin_login
    today = datetime.utcnow()
    today_ist = today + timedelta(hours=5, minutes=30)
    form_data = {
        'region': '0'
    }
    create_booking_and_trip(today,"skey", timedelta(minutes=60), 1000, 600)
    create_booking_and_trip(today - timedelta(days=1),"skey1", timedelta(minutes=60), 1000, 600)
    response = client.post('/api/admin/analytics_graph_daily_trips', data=form_data,headers=auth_headers)
    response_data = response.get_json()

    assert response_data['success'] == 1
    day_data = response_data['data'][0]['day_data']
    
    # Assertions for the trips created
    assert len(day_data) == 2  # Expecting data for two different days
    assert day_data[0]['day'] == (today_ist - timedelta(days=1)).strftime('%d-%m-%Y')
    assert day_data[0]['trips'] == 1  # One trip for the previous day
    assert day_data[1]['day'] == today_ist.strftime('%d-%m-%Y')
    assert day_data[1]['trips'] == 1  # One trip for today
    
# ---------------------------------

#  API - /api/admin/analytics_graph_trips
    
def test_admin_analytics_trips_success(client,admin_login):
    auth_headers, admin = admin_login
    today = datetime.utcnow()
    form_data = {
        'region': '0'
    }
    yesterday = today - timedelta(days=1)  # Yesterday
    last_week = today - timedelta(weeks=1)  # One week ago
    last_month = today - timedelta(weeks=4)  # One month ago
    two_months_ago = today - timedelta(weeks=8)  # Two months ago
    last_year = today - timedelta(days=365)  # One year ago
    
    create_booking_and_trip(today,"skey", timedelta(minutes=60), 1000)  # This is for the week

    # # Monthly data: booking and trip from last month
    # create_booking_and_trip(last_month,"skey2",timedelta(minutes=60), 1500)  # Different sales amount

    # # Monthly data: booking and trip from two months ago (for different month)
    # create_booking_and_trip(two_months_ago,"skey3", timedelta(minutes=60), 2000)  # Different sales amount

    # # Yearly data: booking and trip from last year
    # create_booking_and_trip(last_year,"skey4", timedelta(minutes=60), 2500)  # Different sale
    
    response = client.post('/api/admin/analytics_graph_trips', data=form_data,headers=auth_headers)
    response_data=response.json
    current_month = today.month  # Get current month (integer)
    current_week = today.isocalendar()[1]  # Get current week number (ISO calendar)
    current_year = today.year  # Get current year

    # Extracting month_data
    month_data = response_data['data'][0]['month_data'][0]  # Access the first item in the list
    assert month_data['month'] == current_month  # Check the current month
    assert month_data['trips'] == 1  # Check sales

    # Extracting week_data
    week_data = response_data['data'][0]['week_data'][0]  # Access the first item in the list
    assert week_data['week'] == current_week  # Check the current week
    assert week_data['trips'] == 1  # Check sales

    # Extracting year_data
    year_data = response_data['data'][0]['year_data'][0]  # Access the first item in the list
    assert year_data['year'] == current_year  # Check the current year
    assert year_data['trips'] == 1  # Check sales
 
# ---------------------------------

#  API - /api/admin/total_ratings

def test_admin_analytics_rating_count_success(client,admin_login):
    auth_headers, admin = admin_login   
    from_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    form_data = {
        'from_date': from_date,
        'to_date': to_date,
        'search_region': '0'
    }  
    create_booking_with_rating("skey",5)
    create_booking_with_rating("skey1",5)
    create_booking_with_rating("skey2",4)
    create_booking_with_rating("skey3",3)
    create_booking_with_rating("skey4",2)
    create_booking_with_rating("skey5",1)
    create_booking_with_rating("skey6",0)
    response = client.post('/api/admin/total_ratings', data=form_data,headers=auth_headers)
    response_data=response.json
    assert response_data['success'] == 1
     
# ---------------------------------
    

#  API - /api/admin/b2b_analytics_count

def test_admin_analytics_count_missing_dates_b2b(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0'
    }
    # Send request without dates
    response = client.post('/api/admin/b2b_analytics_count', data=form_data,headers=auth_headers)

    # Assert failure response
    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == -3
    assert 'Missing or invalid date values' in json_data['error']
    
    
def test_admin_analytics_count_b2b_success(client,admin_login):
    from_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')
    auth_headers, admin = admin_login
    form_data = {
        'from_date': from_date,
        'to_date': to_date,
        'region': '0'
    }

    data_driver = unique_user_data()  
    driver_user = Users(
            fname=data_driver['fname'],
            lname=data_driver['lname'],
            mobile=data_driver['mobile'],
            email=data_driver['email'],
            pwd=data_driver['pwd'],
            role=Users.ROLE_DRIVER
            )
    db.session.add(driver_user)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    driver= Drivers(driver_user, data_driver['license'], 'doc_path', 'pic_path', perma=True)
    db.session.add(driver)
    db.session.flush()
    driver_info = DriverInfo(driver.id, "AB123456", date(2030, 12, 31), date(1990, 1, 1), "Delhi",
            "test", "John Doe", "**********", "Brother", 28.7041,  77.1025,  "id_front.jpg",
            "id_back.jpg", "lic_front.jpg", "lic_back.jpg", "profile.jpg",)
    db.session.add(driver_info)
    db.session.flush()
    driver_info.driver_trip_pref = 31
    driver.approved=1
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    user_booking,aff_id=driver_bookings_b2b(None,driver.id)
    user_trip=driver_trip(user_booking,0)

    response = client.post('/api/admin/b2b_analytics_count', data=form_data,headers=auth_headers)
    response_data = {
        'active_affiliates': 1,
        'active_drivers': 1,
        'average_sales_per_day': 167,
        'average_trips_per_day': 0.33,
        'average_trips_per_affiliate':1,
        'average_trips_per_driver': 1.0,
        'registered_affiliates': 1,
        'registered_drivers': 1,
        'success': 1,
        'total_bookings': 1,
        'total_revenue': 0,
        'total_sales': 500,
        'total_trips_count': 1,
        'trend_driver': 1,
        'trend_sales': 500,
        'trend_trips': 1,
        'trend_affiliates': 1
    }
    response_json = response.json
    assert response_json['success'] == response_data['success']
    assert response_json['active_affiliates'] == response_data['active_affiliates']
    assert response_json['active_drivers'] == response_data['active_drivers']
    assert response_json['average_sales_per_day'] == response_data['average_sales_per_day']
    assert response_json['average_trips_per_affiliate'] == response_data['average_trips_per_affiliate']
    assert response_json['average_trips_per_day'] == response_data['average_trips_per_day']
    assert response_json['average_trips_per_driver'] == response_data['average_trips_per_driver']
    assert response_json['registered_affiliates'] == response_data['registered_affiliates']
    assert response_json['registered_drivers'] == response_data['registered_drivers']
    assert response_json['total_bookings'] == response_data['total_bookings']
    assert response_json['total_revenue'] == response_data['total_revenue']
    assert response_json['total_sales'] == response_data['total_sales']
    assert response_json['total_trips_count'] == response_data['total_trips_count']
    assert response_json['trend_driver'] == response_data['trend_driver']
    assert response_json['trend_sales'] == response_data['trend_sales']
    assert response_json['trend_trips'] == response_data['trend_trips']
    assert response_json['trend_affiliates'] == response_data['trend_affiliates']
    
    
def test_admin_analytics_count_b2b_success_aff_filter(client,admin_login):
    from_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
    to_date = (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d')


    data_driver = unique_user_data()  
    driver_user = Users(
            fname=data_driver['fname'],
            lname=data_driver['lname'],
            mobile=data_driver['mobile'],
            email=data_driver['email'],
            pwd=data_driver['pwd'],
            role=Users.ROLE_DRIVER
            )
    db.session.add(driver_user)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    driver= Drivers(driver_user, data_driver['license'], 'doc_path', 'pic_path', perma=True)
    db.session.add(driver)
    db.session.flush()
    driver_info = DriverInfo(driver.id, "AB123456", date(2030, 12, 31), date(1990, 1, 1), "Delhi",
            "test", "John Doe", "**********", "Brother", 28.7041,  77.1025,  "id_front.jpg",
            "id_back.jpg", "lic_front.jpg", "lic_back.jpg", "profile.jpg",)
    db.session.add(driver_info)
    db.session.flush()
    driver_info.driver_trip_pref = 31
    driver.approved=1

    auth_headers, admin = admin_login
    master_affiliate = Affiliate("Master_Client", "dummy_name", 0 , -1, 10,None)
    db.session.add(master_affiliate)
    db.session.flush()
    slave_affiliate = Affiliate("Slave_Client_1", "dummy_name", 0 , master_affiliate.id, 10,None)
    db.session.add(slave_affiliate)
    db.session.flush()
    master_affiliate.slave = {"slaves": [slave_affiliate.id]}
    master_affiliate.mapped_wallet_affiliate = master_affiliate.id
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    user_booking,aff_id=driver_bookings_b2b(master_affiliate.id,driver.id)
    user_trip=driver_trip(user_booking,0)
    form_data = {
        'from_date': from_date,
        'to_date': to_date,
        'region': '0',
        'affiliatefilter': master_affiliate.id
    }
    response = client.post('/api/admin/b2b_analytics_count', data=form_data,headers=auth_headers)
    response_data = {
        'active_affiliates': 1,
        'active_drivers': 1,
        'average_sales_per_day': 167,
        'average_trips_per_day': 0.33,
        'average_trips_per_affiliate':1,
        'average_trips_per_driver': 1.0,
        'registered_affiliates': 2,
        'registered_drivers': 1,
        'success': 1,
        'total_bookings': 1,
        'total_revenue': 0,
        'total_sales': 500,
        'total_trips_count': 1,
        'trend_driver': 1,
        'trend_sales': 500,
        'trend_trips': 1,
        'trend_affiliates': 1
    }
    response_json = response.json
    assert response_json['success'] == response_data['success']
    assert response_json['active_affiliates'] == response_data['active_affiliates']
    assert response_json['active_drivers'] == response_data['active_drivers']
    assert response_json['average_sales_per_day'] == response_data['average_sales_per_day']
    assert response_json['average_trips_per_affiliate'] == response_data['average_trips_per_affiliate']
    assert response_json['average_trips_per_day'] == response_data['average_trips_per_day']
    assert response_json['average_trips_per_driver'] == response_data['average_trips_per_driver']
    assert response_json['registered_affiliates'] == response_data['registered_affiliates']
    assert response_json['registered_drivers'] == response_data['registered_drivers']
    assert response_json['total_bookings'] == response_data['total_bookings']
    assert response_json['total_revenue'] == response_data['total_revenue']
    assert response_json['total_sales'] == response_data['total_sales']
    assert response_json['total_trips_count'] == response_data['total_trips_count']
    assert response_json['trend_driver'] == response_data['trend_driver']
    assert response_json['trend_sales'] == response_data['trend_sales']
    assert response_json['trend_trips'] == response_data['trend_trips']
    assert response_json['trend_affiliates'] == response_data['trend_affiliates']
    
# ---------------------------------

 
#  API - /api/admin/analytics_graph_sales
           
def test_admin_analytics_sales_success_b2b(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0',
        'is_b2b': '1'
    }
    today = datetime.utcnow()
    create_booking_b2b_and_trip(today,"skey", timedelta(minutes=60), 1000,800)  # This is for the week

    response = client.post('/api/admin/analytics_graph_sales', data=form_data,headers=auth_headers)
    response_data=response.json
    current_month = today.month  # Get current month (integer)
    current_week = today.isocalendar()[1]  # Get current week number (ISO calendar)
    current_year = today.year  # Get current year

    # Extracting month_data
    month_data = response_data['data'][0]['month_data'][0]  # Access the first item in the list
    assert month_data['month'] == current_month  # Check the current month
    assert month_data['sales'] == 1000  # Check sales

    # Extracting week_data
    week_data = response_data['data'][0]['week_data'][0]  # Access the first item in the list
    assert week_data['week'] == current_week  # Check the current week
    assert week_data['sales'] == 1000  # Check sales

    # Extracting year_data
    year_data = response_data['data'][0]['year_data'][0]  # Access the first item in the list
    assert year_data['year'] == current_year  # Check the current year
    assert year_data['sales'] == 1000  # Check sales
    
# ---------------------------------

#  API - /api/admin/analytics_graph_daily_revenue

def test_admin_analytics_daily_revenue_success_b2b(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0',
        'is_b2b': '1'
    }
    today = datetime.utcnow()
    today_ist = today + timedelta(hours=5, minutes=30)
    create_booking_b2b_and_trip(today,"skey", timedelta(minutes=60), 1000,600)
    response = client.post('/api/admin/analytics_graph_daily_revenue', data=form_data,headers=auth_headers)
    response_data=response.json
    assert response_data['success'] == 1
    day_data = response_data['data'][0]['day_data']
    assert isinstance(day_data, list)
    assert len(day_data) == 1
    day_entry = day_data[0]
    assert day_entry['day'] == today_ist.strftime('%d-%m-%Y')
    assert day_entry['revenues'] == 600

# ---------------------------------
    
#  API - /api/admin/analytics_graph_revenue

def test_admin_analytics_revenue_success_b2b(client,admin_login):
    auth_headers, admin = admin_login
    form_data = {
        'region': '0',
        'is_b2b': '1'
    }
    today = datetime.utcnow()
    create_booking_b2b_and_trip(today,"skey", timedelta(minutes=60), 1000,700) 

    response = client.post('/api/admin/analytics_graph_revenue', data=form_data,headers=auth_headers)
    response_data=response.json
    current_month = today.month  # Get current month (integer)
    current_week = today.isocalendar()[1]  # Get current week number (ISO calendar)
    current_year = today.year  # Get current year

    # Extracting month_data
    month_data = response_data['data'][0]['month_data'][0]  # Access the first item in the list
    assert month_data['month'] == current_month  # Check the current month
    assert month_data['revenues'] == 700  # Check sales

    # Extracting week_data
    week_data = response_data['data'][0]['week_data'][0]  # Access the first item in the list
    assert week_data['week'] == current_week  # Check the current week
    assert week_data['revenues'] == 700  # Check sales

    # Extracting year_data
    year_data = response_data['data'][0]['year_data'][0]  # Access the first item in the list
    assert year_data['year'] == current_year  # Check the current year
    assert year_data['revenues'] == 700  # Check sales

# ---------------------------------

#  API - /api/admin/analytics_graph_revenue

def test_admin_analytics_daily_trips_success_b2b(client,admin_login):
    auth_headers, admin = admin_login
    today = datetime.utcnow()
    today_ist = today + timedelta(hours=5, minutes=30)
    form_data = {
        'region': '0',
        'is_b2b': '1'
    }
    create_booking_b2b_and_trip(today,"skey", timedelta(minutes=60), 1000, 600)
    response = client.post('/api/admin/analytics_graph_daily_trips', data=form_data,headers=auth_headers)
    response_data = response.get_json()

    assert response_data['success'] == 1
    day_data = response_data['data'][0]['day_data']
    
    # Assertions for the trips created
    assert len(day_data) == 1  # Expecting data for two different days
    assert day_data[0]['day'] == today_ist.strftime('%d-%m-%Y')
    assert day_data[0]['trips'] == 1  # One trip for today
    
# ---------------------------------

#  API - /api/admin/analytics_graph_trips
    
def test_admin_analytics_trips_success_b2b(client,admin_login):
    auth_headers, admin = admin_login
    today = datetime.utcnow()
    form_data = {
        'region': '0',
        'is_b2b': '1'
    }
    yesterday = today - timedelta(days=1)  # Yesterday
    last_week = today - timedelta(weeks=1)  # One week ago
    last_month = today - timedelta(weeks=4)  # One month ago
    two_months_ago = today - timedelta(weeks=8)  # Two months ago
    last_year = today - timedelta(days=365)  # One year ago
    
    create_booking_b2b_and_trip(today,"skey", timedelta(minutes=60), 1000)  # This is for the week

    # # Monthly data: booking and trip from last month
    # create_booking_and_trip(last_month,"skey2",timedelta(minutes=60), 1500)  # Different sales amount

    # # Monthly data: booking and trip from two months ago (for different month)
    # create_booking_and_trip(two_months_ago,"skey3", timedelta(minutes=60), 2000)  # Different sales amount

    # # Yearly data: booking and trip from last year
    # create_booking_and_trip(last_year,"skey4", timedelta(minutes=60), 2500)  # Different sale
    
    response = client.post('/api/admin/analytics_graph_trips', data=form_data,headers=auth_headers)
    response_data=response.json
    current_month = today.month  # Get current month (integer)
    current_week = today.isocalendar()[1]  # Get current week number (ISO calendar)
    current_year = today.year  # Get current year

    # Extracting month_data
    month_data = response_data['data'][0]['month_data'][0]  # Access the first item in the list
    assert month_data['month'] == current_month  # Check the current month
    assert month_data['trips'] == 1  # Check sales

    # Extracting week_data
    week_data = response_data['data'][0]['week_data'][0]  # Access the first item in the list
    assert week_data['week'] == current_week  # Check the current week
    assert week_data['trips'] == 1  # Check sales

    # Extracting year_data
    year_data = response_data['data'][0]['year_data'][0]  # Access the first item in the list
    assert year_data['year'] == current_year  # Check the current year
    assert year_data['trips'] == 1  # Check sales
 
# ---------------------------------


def setup_customer_registration_test_data():
    # Create Users
    now = datetime.utcnow()
    data = unique_user_data()  
    user = Users(
            fname=data['fname'],
            lname=data['lname'],
            mobile=data['mobile'],
            email=data['email'],
            pwd=data['pwd'],
            role=Users.ROLE_USER,
            enabled=1, 
            )
    db.session.add(user)
    data2 = unique_user_data()  
    user2 = Users(
            fname=data2['fname'],
            lname=data2['lname'],
            mobile=data2['mobile'],
            email=data2['email'],
            pwd=data2['pwd'],
            role=Users.ROLE_USER,
            enabled=1, 
            )
    db.session.add(user2)
    user2.region=1
    db.session.flush()
    # Create UserRegistrationDetails
    reg1 = UserRegistrationDetails(user_id=user.id, city="Kolkata", address=101, source="Google Search", region=0, timestamp=now)
    reg2 = UserRegistrationDetails(user_id=user2.id, city="Hyderabad", address=202, source="Instagram", region=1, timestamp=now)
    db.session.add_all([reg1, reg2])

    db.session.commit()

 
#  API - /api/admin/customer_register_reg_count
    
def test_customer_register_reg_count(client, admin_login):
    setup_customer_registration_test_data()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = datetime.utcnow().strftime('%Y-%m-%d')
    auth_headers, admin = admin_login
    response = client.post(
        '/api/admin/customer_register_reg_count',
        headers=auth_headers,
        data={'from_date': from_date, 'to_date': to_date, 'search_region': '0'}
    )

    assert response.status_code == 200
    data = response.json
    assert data['success'] == 1
    assert 'city_registration' in data['data']
    assert 'region_registration' in data['data']
    assert any(item['city'] == 'Kolkata' for item in data['data']['city_registration'])
 
# ---------------------------------   
    
#  API - /api/admin/customer_register_source_count

def test_customer_register_source_count(client, admin_login):
    setup_customer_registration_test_data()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = datetime.utcnow().strftime('%Y-%m-%d')
    auth_headers, admin = admin_login
    response = client.post(
        '/api/admin/customer_register_source_count',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'search_region': '1'
        }
    )

    assert response.status_code == 200
    data = response.json
    assert data['success'] == 1
    assert isinstance(data['data'], list)
    assert any(item['source'] == 'Instagram' for item in data['data'])

# ---------------------------------      

def setup_transaction_test_data_customer():
    # Create a customer
    data = unique_user_data()
    user = Users(
        fname=data['fname'],
        lname=data['lname'],
        mobile=data['mobile'],
        email=data['email'],
        pwd=data['pwd'],
        role=Users.ROLE_USER,
        enabled=1,
    )
    user.region = 0
    db.session.add(user)
    db.session.flush()

    # Add customer transactions
    t1 = UserTrans(uid=user.id, amt=50000, status=UserTrans.COMPLETED, method="Gift", wall_a=1500, wall_b=1000)
    t1.timestamp = datetime.utcnow() - timedelta(days=2)
    db.session.add(t1)

    # Transaction 2: Fine (negative amount)
    t2 = UserTrans(uid=user.id, amt=-20000, status=UserTrans.COMPLETED, method="Fine", wall_a=1300, wall_b=1500)
    t2.timestamp = datetime.utcnow() - timedelta(days=1)
    t2.admin_id = 1
    db.session.add(t2)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        
def setup_transaction_test_data_driver():
    # Create a driver
    data1 = unique_user_data()
    driver_user1, driver1 = create_user_and_driver(data1)

    # Second driver
    data2 = unique_user_data()
    driver_user2, driver2 = create_user_and_driver(data2)

    # Third driver
    data3 = unique_user_data()
    driver_user3, driver3 = create_user_and_driver(data3)

    # Add driver transactions
    t1 = DriverTrans(did=driver3.id, amt=30000, wall_a=900, wall_b=600, status=DriverTrans.COMPLETED)
    db.session.add(t1)
    db.session.flush()
    t1.timestamp = datetime.utcnow() - timedelta(days=2)
    t1.method = "Gift"
    t1.admin_id = 1
    t1.admin_name = "AdminUser1"
    t2 = DriverTrans(did=driver3.id, amt=10000, wall_a=800, wall_b=900, status=DriverTrans.COMPLETED)
    db.session.add(t2)
    db.session.flush()
    t2.timestamp = datetime.utcnow() - timedelta(days=1)
    t2.method = "Withdraw"
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    
#  API - /api/admin/transaction_summary
      
def test_transaction_summary_customer(client, admin_login):
    setup_transaction_test_data_customer()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = datetime.utcnow().strftime('%Y-%m-%d')
    auth_headers, admin = admin_login
    ut = db.session.query(UserTrans).all()
    response = client.post(
        '/api/admin/transaction_summary',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'region': '0',
            'data_type': 'Customers'
        }
    )

    assert response.status_code == 200
    data = response.json
    assert data['success'] == 1
    assert isinstance(data['data'], list)
    data_dict = dict(data['data'])
    assert abs(data_dict.get('gift_amount', 0)) + abs(data_dict.get('fine_amount', 0)) == 700
 
def test_transaction_summary_driver(client, admin_login):
    setup_transaction_test_data_driver()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = datetime.utcnow().strftime('%Y-%m-%d')
    auth_headers, admin = admin_login

    response = client.post(
        '/api/admin/transaction_summary',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'search_region': '0',
            'data_type': 'drivers'
        }
    )

    assert response.status_code == 200
    data = response.json
    assert data['success'] == 1
    assert isinstance(data['data'], list)
    data_dict = dict(data['data'])
    assert abs(data_dict.get('gift_amount', 0)) + abs(data_dict.get('withdraw_amount', 0)) == 400
    
# ---------------------------------   

#  API - /api/admin/transaction_summary_customer_admin

def test_transaction_summary_admin_customer(client, admin_login):
    setup_transaction_test_data_customer()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = datetime.utcnow().strftime('%Y-%m-%d')
    auth_headers, admin = admin_login

    response = client.post(
        '/api/admin/transaction_summary_customer_admin',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'search_region': '0',
            'data_type': 'Fine'
        }
    )

    assert response.status_code == 200
    data = response.json
    assert data['success'] == 1
    assert isinstance(data['data'], list)
    assert len(data['data']) > 0

    record = data['data'][0]
    assert isinstance(record, dict)
    assert record.get('total_amount') == 200.0
 
# ---------------------------------   

#  API - /api/admin/transaction_summary_driver_admin
   
def test_transaction_summary_admin_driver(client, admin_login):
    setup_transaction_test_data_driver()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date = datetime.utcnow().strftime('%Y-%m-%d')
    auth_headers, admin = admin_login

    response = client.post(
        '/api/admin/transaction_summary_driver_admin',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'search_region': '0',
            'data_type': 'Gift'
        }
    )

    assert response.status_code == 200
    data = response.json
    assert data['success'] == 1
    assert isinstance(data['data'], list)
    assert len(data['data']) == 1

    entry = data['data'][0]
    assert entry['admin_name'] == 'AdminUser1'
    assert abs(entry['total_amount']) == 300.0  # or use a tolerance
   
# ---------------------------------   


    
def setup_driver_inventory_data():
    """
    Create:
     - One driver in region 1
     - DriverTrans entries covering T-Shirt, Registration and Bag within last 3 days
    """
    # create user & driver
    data1 = unique_user_data()
    driver_user1, driver1 = create_user_and_driver(data1)

    # Second driver
    data2 = unique_user_data()
    driver_user2, driver2 = create_user_and_driver(data2)

    now = datetime.utcnow()
    # T-Shirt M-02
    dt1 = DriverTrans(did=driver2.id, method="T-Shirt", amt=100, status=DriverTrans.COMPLETED)
    dt1.description = "M-02"; dt1.timestamp = now - timedelta(days=2)
    db.session.add(dt1)

    # Registration with multiple tokens
    dt2 = DriverTrans(did=driver2.id, method="Registration", amt=100, status=DriverTrans.COMPLETED)
    dt2.description = "S-1, B-3"; dt2.timestamp = now - timedelta(days=1)
    db.session.add(dt2)

    # Bag entry
    dt3 = DriverTrans(did=driver2.id, method="Bag",amt=200, status=DriverTrans.COMPLETED)
    dt3.description = "4"; dt3.timestamp = now
    db.session.add(dt3)

    db.session.commit()
    return driver2

#  API - /api/admin/driver_inventory_count

def test_driver_inventory_count_success(client, admin_login):
    auth_headers, admin = admin_login
    # populate transactions
    driver = setup_driver_inventory_data()

    from_date = (datetime.utcnow() - timedelta(days=3)).strftime("%Y-%m-%d")
    to_date   = datetime.utcnow().strftime("%Y-%m-%d")

    resp = client.post(
        "/api/admin/driver_inventory_count",
        headers=auth_headers,
        data={
            "from_date": from_date,
            "to_date": to_date,
            "search_region": '0'
        }
    )
    assert resp.status_code == 200
    body = resp.get_json()
    assert body["success"] == 1
    # Expect: M:2, S:1, Bags:3+4=7
    assert body["data"]["M"] == 2
    assert body["data"]["S"] == 1
    assert body["data"]["Bags"] == 7
    
# ---------------------------------  
    
#  API - /api/admin/booking_summary_admin

def test_booking_summary_admin_allocation_success(client, admin_login):
    auth_headers, admin_user = admin_login
    now = datetime.utcnow()

    # create 2 normal bookings
    create_booking_and_trip(now - timedelta(days=2), key="A1", duration=timedelta(minutes=60), price=100)
    create_booking_and_trip(now - timedelta(days=1), key="A2", duration=timedelta(minutes=60), price=120)
    from_date = (now - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date   = now.strftime('%Y-%m-%d')

    response = client.post(
        '/api/admin/booking_summary_admin',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'data_type': 'Allocation',
            'is_b2b': '0',
            'sort': '1',
            'region': '0'
        }
    )
    assert response.status_code == 200
    body = response.get_json()
    assert body['success'] == 1
    assert isinstance(body['data'], list)
    # both bookings allocated by same admin -> one entry with count=2
    entry = body['data'][0]
    assert entry['counts'] == 1
    assert 'admin_name' in entry


def test_booking_summary_admin_cancellation_success(client, admin_login):
    auth_headers, admin_user = admin_login
    admin_user = db.session.query(Users).filter_by(id=admin_user).first()
    now = datetime.utcnow()

    # create 2 normal bookings then cancel
    b1 = create_booking_and_trip(now - timedelta(days=2), key="C1", duration=timedelta(minutes=60), price=100)
    b2  = create_booking_and_trip(now - timedelta(days=1), key="C2", duration=timedelta(minutes=60), price=120)
    b1.valid=-1
    b2.valid=-1
    # add cancellations
    cancel1 = BookingCancelled(
                booking=b1.id,
                user=admin_user.id, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                uid=1,
                did=1, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    cancel2 = BookingCancelled(
                booking=b2.id,
                user=admin_user.id, 
                cancel_source=BookingCancelled.SRC_ADMIN, 
                uid=1,
                did=1, 
                penalty_user="99", 
                penalty_driver='99', 
            )
    db.session.add_all([cancel1, cancel2])
    db.session.commit()

    from_date = (now - timedelta(days=3)).strftime('%Y-%m-%d')
    to_date   = now.strftime('%Y-%m-%d')

    response = client.post(
        '/api/admin/booking_summary_admin',
        headers=auth_headers,
        data={
            'from_date': from_date,
            'to_date': to_date,
            'data_type': 'Cancellation',
            'is_b2b': '0',
            'sort': '1',
            'search_region': '0'
        }
    )
    assert response.status_code == 200
    body = response.get_json()
    assert body['success'] == 1
    assert isinstance(body['data'], list)
    entry = body['data'][0]
    assert entry['counts'] == 2
    assert 'admin_name' in entry

# ---------------------------------  
    

def create_triplog_reached_src(booking_id, driver_user=5, action_user=0, timestamp=None):
    if not timestamp:
        timestamp = datetime.utcnow()
    triplog = TripLog(
        booking=booking_id,
        driver=driver_user,
        action_user=action_user,
        action=TripLog.ACTION_REACHED_SRC,
        lat=12.9716,
        lng=77.5946
    )
    triplog.timestamp = timestamp
    db.session.add(triplog)
    db.session.commit()
    return triplog

#  API - /api/admin/analytics_graph_trip_metrics

def test_analytics_graph_trip_metrics_success(client, admin_login):
    auth_headers, _ = admin_login
    now = datetime.utcnow()

    # Create bookings and trips
    b1 = create_booking_and_trip(
        start_time=now - timedelta(days=2),
        key="B1",
        duration=timedelta(minutes=45),
        price=150
    )
    b2 = create_booking_and_trip(
        start_time=now - timedelta(days=1),
        key="B2",
        duration=timedelta(minutes=90),
        price=200
    )

    # Create TripLog entries marking 'REACHED_SRC'
    create_triplog_reached_src(b1.id, timestamp=now - timedelta(days=2, hours=2))
    create_triplog_reached_src(b2.id, timestamp=now - timedelta(days=1, hours=1, minutes=15))

    # Make POST request to the analytics endpoint
    response = client.post(
        '/api/admin/analytics_graph_trip_metrics',
        headers=auth_headers,
        data={
            'is_b2b': '0',        # Non-B2B test
            'region': '-1',       # All regions
            'affiliatefilter': '' # No affiliate filter
        }
    )
    
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] == 1
    assert 'data' in data
    # Check daily, weekly, monthly, yearly keys
    for key in ['daily', 'weekly', 'monthly', 'yearly']:
        assert key in data['data']
        assert isinstance(data['data'][key], list)
        if data['data'][key]:  # Only validate metrics if records exist
            sample = data['data'][key][0]
            for metric in ['avg_duration_hrs', 'avg_overtime_hrs', 'avg_delay_hrs']:
                assert metric in sample
    data = response.get_json()['data']
    assert data['daily'][0]['avg_delay_hrs'] == approx(2.0, abs=0.01)
    assert data['daily'][0]['avg_duration_hrs'] == approx(0.75, abs=0.01)
    assert data['daily'][0]['avg_overtime_hrs'] == approx(0.3, abs=0.01)

    assert data['daily'][1]['avg_delay_hrs'] == approx(1.25, abs=0.01)
    assert data['daily'][1]['avg_duration_hrs'] == approx(1.5, abs=0.01)
    assert data['daily'][1]['avg_overtime_hrs'] == approx(0.6, abs=0.01)
    

# ---------------------------------  

#  API - /api/admin/analytics_driver_earning
    
def test_analytics_driver_earning(client, admin_login):
    # Setup: create B2B booking, trip and pricing
    today = datetime.utcnow()
    b1,aff1,trip1 = create_booking_b2b_and_trip(today,"skey", timedelta(minutes=60), 1000,600,client_name="dummy_name1")
    b2,aff2,trip2 = create_booking_b2b_and_trip(today,"skey1", timedelta(minutes=45), 1000,600,client_name="dummy_name2")
    pricing = TripPricing(
        book_id=b1.id,
        base_ch=100.0,
        night_ch=50.0,
        ot_ch=25.0,
        booking_ch=0,
        insurance_ch=0,
        driver_base_ch=200.0,
        driver_night_ch=30.0,
        driver_ot_ch=20.0
    )
    pricing2 = TripPricing(
        book_id=b2.id,
        base_ch=100.0,
        night_ch=50.0,
        ot_ch=25.0,
        booking_ch=0,
        insurance_ch=0,
        driver_base_ch=200.0,
        driver_night_ch=30.0,
        driver_ot_ch=20.0
    )

    db.session.add(pricing)
    db.session.add(pricing2)
    db.session.commit()

    # Payload
    payload = {
        'region': '0',
        'is_b2b': '1'
    }
    auth_headers, _ = admin_login
    # Request
    response = client.post(
        '/api/admin/analytics_driver_earning',
        data=payload,
        headers=auth_headers
    )
    assert response.status_code == 200
    res = response.get_json()
    assert res['success'] == 1
    # Validate structure
    for section in ['daily', 'monthly', 'weekly', 'yearly']:
        assert section in res['data']
        for entry in res['data'][section]:
            assert 'avg_driver_earning' in entry
            assert 'avg_earning_per_driver' in entry
            # Accept small rounding variations
            assert isinstance(entry['avg_driver_earning'], float)
            assert isinstance(entry['avg_earning_per_driver'], float)

    # Check specific value (200 + 30 + 20) = 250.0
    expected_avg_per_booking = 250.0
    expected_avg_per_driver = 500.0

    actual_avg_per_booking = res['data']['daily'][0]['avg_driver_earning']
    actual_avg_per_driver = res['data']['daily'][0]['avg_earning_per_driver']

    assert abs(actual_avg_per_booking - expected_avg_per_booking) < 0.01
    assert abs(actual_avg_per_driver - expected_avg_per_driver) < 0.01
    
# ---------------------------------  

#  API - /api/admin/analytics_affiliate_transactions  
    
def test_analytics_affiliate_transactions(client, admin_login):
    # Create a test affiliate
    b1,aff,trip = create_booking_b2b_and_trip(datetime.utcnow(),"skey", timedelta(minutes=60), 1000,600,client_name="dummy_name1")
    # Create an AffiliateWalletLog with valid method and amount
    log = AffiliateWalletLogs(
        amt=10000,  # ₹100.00
        method="Booking (Test)",
        aff_id=aff.id,
        status=1,
        wallet_before=0,
        wallet_after=10000
    )
    db.session.add(log)
    db.session.flush()

    # Link Trip to the wallet log
    trip.aff_trans = log.id
    db.session.flush()

    # Commit all records
    db.session.commit()
    auth_headers, _ = admin_login
    payload = {
        'region': '0',
    }
    # Call the API
    res = client.post(
        '/api/admin/analytics_affiliate_transactions',
        data=payload,
        headers=auth_headers
    )

    assert res.status_code == 200
    data = res.get_json()
    assert data['success'] == 1
    # Verify presence of keys
    for period in ['daily', 'weekly', 'monthly', 'yearly']:
        assert period in data['data']
        assert len(data['data'][period]) > 0

    # Validate total_amount and avg_amount
    daily_data = data['data']['daily'][-1]  # earliest day
    assert 'total_amount' in daily_data
    assert abs(daily_data['total_amount'] - 100.00) < 0.01
    
# ---------------------------------  

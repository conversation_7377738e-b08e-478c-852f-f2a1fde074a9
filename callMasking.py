
from flask import Blueprint, jsonify, request
from models import Users, Drivers, Bookings, Trip
from models import CallLog, CallerInput, CallRequestLog
from db_config import db
import datetime
from flask_jwt_extended import (
    jwt_required, get_jwt_identity, get_jwt
)
from sqlalchemy import desc
from _utils import get_safe
from booking_params import BookingParams

callMasking = Blueprint('callMasking', __name__)

def unix_to_formatted(unix_timestamp, format='%Y-%m-%d %H:%M:%S'):
    return datetime.datetime.utcfromtimestamp(unix_timestamp).strftime(format)

def within_n_seconds(current_ts, callReq_ts, format='%Y-%m-%d %H:%M:%S'):
    dt1 = datetime.datetime.strptime(current_ts, format)
    dt2 = datetime.datetime.strptime(callReq_ts, format)
    diff = abs((dt1 - dt2).total_seconds())
    return diff <= BookingParams.COOLING_TIME_PERIOD

def update_call_log(call_log):
    call_log.call_success = True
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'result': -1})

@callMasking.route('/api/calling/directCalling', methods=['POST'])
@jwt_required()
def calling():
    data = request.form
    uid = data['uid']
    node_id = data['node_id']
    calling_ts = unix_to_formatted(int(data['timestamp']))
    mobileNo = data['clid']

    call_log = CallLog(uid, node_id, calling_ts, mobileNo)
    try:
        db.session.add(call_log)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'result': -1})

    user = db.session.query(Users).filter(Users.mobile == mobileNo).first()
    if not user:
        message = "Sorry you are not registered with us. Please try to call from your registered number and try again"
        response = {
            "action": "tts",
            "value": message,
            "operation": "hangup"
        }
        return jsonify(response)
    is_driver = user.role == Users.ROLE_DRIVER
    call_log = db.session.query(CallLog).filter(CallLog.uid == uid).first()
    if not call_log:
        message = "Sorry there is some issue. Please try again later."
        response = {
            "action": "tts",
            "value": message,
            "operation": "hangup"
        }
        return jsonify(response)
    
    callRequestLog = db.session.query(CallRequestLog).filter(CallRequestLog.from_mobile == mobileNo). \
                    order_by(desc(CallRequestLog.timestamp)).first()
    if callRequestLog:
        callRequest_ts = str(callRequestLog.timestamp)
        if within_n_seconds(callRequest_ts, calling_ts):
            booking_code = callRequestLog.book_code
            booking_status = db.session.query(Bookings).filter(Bookings.code == booking_code).first().valid
            if booking_status < 0:
                response = {   
                    "action": "tts",
                    "value": "Please wait, while we are connecting your call to the support executive",
                    "operation": "dial-numbers",
                    "operation_data":
                    {
                        "data":[f"+91-{BookingParams.ALTERNATE_MOBILE_NO}"],
                        "dial_method":"serial",
                        "anon_uuid":"65fad49f69355644"
                    }
                }
                update_call_log(call_log)
                return jsonify(response)
            upcomingBooking = db.session.query(Bookings, Trip).filter(Bookings.code == booking_code). \
                                filter(Bookings.id == Trip.book_id).first()
            if upcomingBooking is not None and upcomingBooking[1].status == Trip.TRIP_STOPPED:
                response = {   
                    "action": "tts",
                    "value": "Please wait, while we are connecting your call to the support executive",
                    "operation": "dial-numbers",
                    "operation_data":
                    {
                        "data":[f"+91-{BookingParams.ALTERNATE_MOBILE_NO}"],
                        "dial_method":"serial",
                        "anon_uuid":"65fad49f69355644"
                    }
                }
            else:
                if is_driver:
                    response = {   
                        "action": "tts",
                        "value": "Please wait, while we are connecting your call to the customer",
                        "operation": "dial-numbers",
                        "operation_data":
                        {
                            "data":[f"+91-{callRequestLog.to_mobile}"],
                            "dial_method":"serial",
                            "anon_uuid":"65fad49f69355644"
                        }
                    }
                else:
                    response = {   
                        "action": "tts",
                        "value": "Please wait, while we are connecting your call to the driver",
                        "operation": "dial-numbers",
                        "operation_data":
                        {
                            "data":[f"+91-{callRequestLog.to_mobile}"],
                            "dial_method":"serial",
                            "anon_uuid":"65fad49f69355644"
                        }
                    }
            update_call_log(call_log)
            return jsonify(response)
        
    user_id  = user.id
    prev_dt = datetime.datetime.utcnow() - datetime.timedelta(days=1)
    prevdate = prev_dt.strftime("%Y-%m-%d")
    if is_driver:
        driver = db.session.query(Drivers).filter(Drivers.user == user_id).first()
        driver_id = driver.id
        allUpcomingBooking = db.session.query(Bookings, Users).filter(Bookings.driver == driver_id). \
                    filter(Bookings.user == Users.id). \
                    filter(Bookings.valid == 1). \
                    filter(Bookings.startdate >= prevdate). \
                    order_by(Bookings.starttime).all()
        book_ids = []
        book_mobile = []
        for booking in allUpcomingBooking:
            upcomingBooking = db.session.query(Bookings, Trip).filter(Trip.book_id == booking[0].id).first()
            if not upcomingBooking:
                book_ids.append(booking[0].code)
                book_mobile.append(booking[1].mobile)
            else:
                if upcomingBooking[1].status != Trip.TRIP_STOPPED:
                    book_ids.append(booking[0].code)
                    book_mobile.append(booking[1].mobile)
        if not book_ids:
            message = "Sorry, currently you don't have any bookings"
            response = {
                "action": "tts",
                "value": message,
                "operation": "hangup"
            }
        elif len(book_ids) == 1:
            message = "Please wait, while we are connecting your call to the customer"
            response = {   
                "action": "tts",
                "value":message,
                "operation": "dial-numbers",
                "operation_data":
                {
                    "data":[f"+91-{book_mobile[0]}"],
                    "dial_method":"serial",
                    "anon_uuid":"65fad49f69355644"
                }
            }
            update_call_log(call_log)
        else:
            length = len(book_ids)
            message = f"You have {length} upcoming bookings:\n"
            for i in range(len(book_ids)):
                callerInput = CallerInput(uid, call_log.id, i+1, book_ids[i], book_mobile[i])
                message += f"Press {i+1} for booking ID: {book_ids[i]}\n"
                try:
                    db.session.add(callerInput)
                    db.session.commit()
                except Exception as e:
                    print(e)
                    db.session.rollback()
                    return jsonify({'result': -1})
            response = {
            "action": "tts",
            "value": message
            }
    else:
        allUpcomingBooking = db.session.query(Bookings, Users, Drivers).filter(Bookings.user == user_id). \
                    filter(Bookings.driver == Drivers.id). \
                    filter(Users.id == Drivers.user). \
                    filter(Bookings.valid == 1). \
                    filter(Bookings.startdate >= prevdate). \
                    order_by(Bookings.starttime).all()
        book_ids = []
        book_mobile = []
        for booking in allUpcomingBooking:
            upcomingBooking = db.session.query(Bookings, Trip).filter(Trip.book_id == booking[0].id).first()
            if not upcomingBooking:
                book_ids.append(booking[0].code)
                book_mobile.append(booking[1].mobile)
            else:
                if upcomingBooking[1].status != Trip.TRIP_STOPPED:
                    book_ids.append(booking[0].code)
                    book_mobile.append(booking[1].mobile)
        if not allUpcomingBooking:
            message = "Sorry, Currently you don't have any upcoming bookings"
            response = {
                "action": "tts",
                "value": message,
                "operation": "hangup"
            } 
        elif len(book_ids) == 1:
            message = "Please wait, while we are connecting your call to the driver"
            response = {   
                "action": "tts",
                "value":message,
                "operation": "dial-numbers",
                "operation_data":
                {
                    "data":[f"+91-{book_mobile[0]}"],
                    "dial_method":"serial",
                    "anon_uuid":"65fad49f69355644"
                }
            }
            update_call_log(call_log)
        else:
            length = len(book_ids)
            message = f"You have {length} upcoming bookings:\n"
            for i in range(len(book_ids)):
                callerInput = CallerInput(uid, call_log.id, i+1, book_ids[i], book_mobile[i])
                message += f"Press {i+1} for booking ID: {book_ids[i]}\n"
                try:
                    db.session.add(callerInput)
                    db.session.commit()
                except Exception as e:
                    print(e)
                    db.session.rollback()
                    return jsonify({'result': -1})
            response = {
            "action": "tts",
            "value": message
            }

    return jsonify(response)

@callMasking.route('/api/calling/multipleBooking', methods=['POST'])
@jwt_required()
def multipleBooking():
    data = request.form
    uid = data['uid']
    input_value = get_safe(request.form, 'input', -1)
    #keyPress = int(get_safe(request.form, 'input', -1))
    callLogDb = db.session.query(CallLog).filter(CallLog.uid == uid)
    callLog = callLogDb.first()
    if callLog.call_success == True:
        message = "Sorry, the number you are trying to call is not reachable at this moment, Please try again later."
        response = {
            "action": "tts",
            "value": message,
            "operation": "hangup"
        } 
        return jsonify(response)
    print(input_value)
    if input_value == '':
        keyPress = -1
    else:
        try:
            keyPress = int(input_value)
        except ValueError:
            keyPress = -1  # Or handle the error as appropriate
    validInput = {}
    validBookCode = {}
    callerInput = db.session.query(CallerInput).filter(CallerInput.uid == uid).all()
    for ci in callerInput:
        validInput[ci.key_press] = ci.mobile
        validBookCode[ci.key_press] = ci.book_code
    if keyPress in validInput:
        message = "Please wait, while we are connecting your call"
        response = {   
            "action": "tts",
            "value":message,
            "operation": "dial-numbers",
            "operation_data":
            {
                "data":[f"+91-{validInput[keyPress]}"],
                "dial_method":"serial",
                "anon_uuid":"65fad49f69355644"
            }
        }
        update_call_log(callLog)
    else:
        if callLog.invalid_count < BookingParams.WRONG_INPUT_THRESH:
            callLogDb.update({CallLog.invalid_count: CallLog.invalid_count + 1})
            try:
                db.session.commit()
            except Exception as e:
                print(e)
                db.session.rollback()
                return jsonify({'result': -1})
            message = "Sorry you have selected a wrong input. Please try again."
            length = len(callerInput)
            for i in range(length):
                message += f"Press {i+1} for booking ID: {validBookCode[i+1]}\n"
            response = {   
                "action": "tts",
                "value": message,
                "operation": "jump-node",
                "operation_data":
                {
                    "node_id":"65ddca2bb1f02831"
                }
            }
        else:
            message = "Sorry you have exceeded your input"
            response = {
                "action": "tts",
                "value": message,
                "operation": "hangup"
            }

    return jsonify(response)

# Api when customer/driver click on particular booking on app
@callMasking.route('/api/calling/callRequest', methods=['POST'])
@jwt_required()
def callRequest(): 
    claims = get_jwt()
    bookCode = request.form['bookCode']
    is_driver = claims['roles'] == Users.ROLE_DRIVER
    userMobile = db.session.query(Bookings, Users).filter(Bookings.code == bookCode). \
                        filter(Bookings.user == Users.id).first()[1].mobile
    driverMobile = db.session.query(Bookings, Users, Drivers).filter(Bookings.code == bookCode). \
                        filter(Bookings.driver == Drivers.id). \
                        filter(Drivers.user == Users.id).first()[1].mobile
    print(is_driver)
    if is_driver:
        to_mobileNo = userMobile
        from_mobileNo = driverMobile
    else:
        to_mobileNo = driverMobile
        from_mobileNo = userMobile
    print(from_mobileNo)
    callRequestLog = CallRequestLog(bookCode, from_mobileNo, to_mobileNo)

    try:
        db.session.add(callRequestLog)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'result': -1, 'message': 'Error logging call request in database'})
    
    return jsonify({'success': 1, 'message': 'Call request successfully logged'})

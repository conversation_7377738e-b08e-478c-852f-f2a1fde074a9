tags:
  - Driver
summary: Get Driver Monthly Earnings
description: >
  This API allows a driver to retrieve their monthly earnings, the total number of trips, and hours worked for a specified month and year.
parameters:
  - name: month
    in: formData
    type: integer
    required: true
    description: The month for which the earnings are being requested (1 for January, 12 for December).
  - name: year
    in: formData
    type: integer
    required: true
    description: The year for which the earnings are being requested.
responses:
  200:
    description: Successful retrieval of monthly earnings data.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for successful retrieval)
          example: 1
        trip_count:
          type: integer
          description: Total number of trips completed in the specified month
          example: 15
        earning:
          type: number
          format: float
          description: Total earnings for the month
          example: 12000.50
        hour_count:
          type: integer
          description: Total hours worked for the month
          example: 40
        avg_earning:
          type: number
          format: float
          description: Average earnings per trip
          example: 800.03
        avg_hours:
          type: number
          format: float
          description: Average hours per trip
          example: 2.67
        owed:
          type: number
          format: float
          description: Total amount owed to the driver
          example: 5000.75
        withdrawable:
          type: number
          format: float
          description: Total amount withdrawable by the driver
          example: 3000.25
        wallet:
          type: number
          format: float
          description: Driver's wallet balance
          example: 2000.50
    examples:
      application/json:
        success: 1
        trip_count: 15
        earning: 12000.50
        hour_count: 40
        avg_earning: 800.03
        avg_hours: 2.67
        owed: 5000.75
        withdrawable: 3000.25
        wallet: 2000.50
  400:
    description: Unauthorized access, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized access)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401:
    description: User account restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  201_a:
    description: Incomplete form data, missing month or year.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete form)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"
  500:
    description: Server error occurred while retrieving the monthly earnings.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for server error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Server error"
    examples:
      application/json:
        success: -1
        message: "Server error"

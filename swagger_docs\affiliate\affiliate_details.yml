tags:
  - Affiliate
summary: Get Detailed Information for an Affiliate
description: >
  Retrieves detailed affiliate information including contact, address, wallet status, and performance metrics.
parameters:
  - in: formData
    name: affiliate_id
    required: true
    type: integer
    description: ID of the affiliate to retrieve details for.
    example: 42
  - in: formData
    name: type
    required: true
    type: integer
    description: Type of detail to fetch (e.g., 1 for summary, 2 for full profile).
    example: 1
  - in: formData
    name: admin_call
    required: false
    type: integer
    description: Flag to indicate if it's an admin-initiated call. Defaults to 0.
    example: 1
  - in: formData
    name: regions
    required: false
    type: string
    description: >
      Comma-separated region codes that the representative should have access to.
    example: "0,1"
responses:
  200:
    description: Successfully retrieved affiliate details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        data:
          type: object
          properties:
            name:
              type: string
              example: "ABC Travels"
            email:
              type: string
              example: "<EMAIL>"
            balance:
              type: number
              example: 1200.50
  400:
    description: Failed to retrieve affiliate details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        msg:
          type: string
          example: "Invalid affiliate ID or detail type"
  201:
    description: Incomplete form.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Incomplete form"

tags:
  - Affiliate_Login
summary: Validate OTP for Affiliate Login
description: >
  This endpoint validates the OTP provided by the affiliate representative for login purposes.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the affiliate representative.
  - name: otp
    in: formData
    type: string
    required: true
    description: The OTP sent to the affiliate representative's mobile.
responses:
  200:
    description: OTP verified successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        msg:
          type: string
          description: Success message.
          example: "OTP verified"
  400:
    description: Missing required fields.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Missing required fields"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        msg:
          type: string
          description: Error message.
          example: "Affiliate representative not found"
  401:
    description: OTP validation failed.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for validation failure).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Failed to verify"

tags:
  - Utilities_admin
summary: List Admin Users
description: >
  This endpoint retrieves a list of admin users based on filters such as region, role, and search query. It supports searching by admin ID, name, or mobile number. The response includes details like tab access, regions, notifications, and timestamps.
  Only accessible by users with admin privileges.
parameters:
  - name: search_query
    in: formData
    required: false
    type: string
    description: >
      A search string used to search admin users by admin ID, name, or mobile number.
    example: "John"
  - name: search_by
    in: formData
    required: false
    type: string
    description: >
      Specifies the criteria to search by. Use 1 for admin ID, 2 for name or mobile number.
    example: "1"
    default: "1"
  - name: regions
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of region IDs to filter admin users.
    example: "1,2,3"
  - name: role
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of role IDs to filter admin users by their roles.
    example: "2,3"
responses:
  200:
    description: Successfully retrieved the list of admin users
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        admins:
          type: array
          description: List of admin users
          items:
            type: object
            properties:
              admin_id:
                type: integer
                description: The ID of the admin.
                example: 1
              user_id:
                type: integer
                description: The ID of the user.
                example: 101
              name:
                type: string
                description: Full name of the admin user.
                example: "John Doe"
              mobile:
                type: string
                description: The mobile number of the admin.
                example: "9876543210"
              role:
                type: integer
                description: The role ID of the admin user.
                example: 2
              tab_access:
                type: string
                description: Comma-separated list of tab IDs that the admin has access to.
                example: "1,4,7"
              regions_access:
                type: string
                description: Comma-separated list of region IDs that the admin has access to.
                example: "1,2,3"
              notification_access:
                type: string
                description: Comma-separated list of notification IDs that the admin will receive.
                example: "1,2"
              created_at:
                type: string
                description: The date and time when the admin access was created, in IST format.
                example: "01 Jan 2024, 12:00"
              recent_edited_at:
                type: string
                description: The date and time when the admin access was last edited, in IST format.
                example: "15 Jan 2024, 14:00"
  400:
    description: Error in search regions string
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        error:
          type: string
          description: Error message
          example: "Error in search regions string"
  500:
    description: Internal server error or general failure
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        message:
          type: string
          description: Error message detailing the exception
          example: "Internal server error or exception details"

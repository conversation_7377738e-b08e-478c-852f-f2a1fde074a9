tags:
  - Coupon_admin
summary: Change Coupon State
description: >
  This endpoint allows admins to change the state of a coupon. If the coupon is active, it will be set to inactive and vice versa. Admin authorization is required.
parameters:
  - in: formData
    name: coupon_id
    type: integer
    required: true
    description: The ID of the coupon whose state is to be changed.
    example: 123
responses:
  200:
    description: Successfully changed the coupon state.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
  400:
    description: Bad request due to invalid or missing coupon ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Invalid coupon ID"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

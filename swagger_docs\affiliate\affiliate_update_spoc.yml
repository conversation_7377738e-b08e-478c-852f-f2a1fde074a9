tags:
  - Affiliate
summary: Update SPOC for an Affiliate Representative
description: >
  Updates the SPOC details for an affiliate representative.
parameters:
  - in: body
    name: body
    required: true
    description: JSON payload for updating SPOC.
    schema:
      type: object
      properties:
        rep_id:
          type: integer
          description: ID of the affiliate representative.
          example: 101
        spoc:
          type: object
          description: SPOC details to update.
          properties:
            spoc_id:
              type: integer
              example: 10
            spoc_name:
              type: string
              example: "<PERSON>"
            spoc_number:
              type: string
              example: "9123456789"
            spoc_type:
              type: string
              example: "Global"
            spoc_region:
              type: string
              example: "0,1"
        regions:
          type: string
          required: true
          description: >
            Comma-separated region codes that the representative should have access to.
          example: "0,1"
responses:
  200:
    description: SPOC updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "SPOC updated successfully"
        spoc_id:
          type: integer
          example: 10
  400:
    description: Missing or invalid input.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Affiliate representative ID is required"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Affiliate representative not found"
  500:
    description: Internal Server Error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        error:
          type: string
          example: "Detailed error message"

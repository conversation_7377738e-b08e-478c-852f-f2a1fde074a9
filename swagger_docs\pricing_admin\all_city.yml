tags:
  - Pricing_admin
summary: Fetch All Cities for Pricing
description: >
  This endpoint allows admins to fetch a list of all cities for which pricing information is available. 
  It requires the admin to be authorized and returns the city names from the pricing configuration.
responses:
  200:
    description: Successfully fetched the list of cities
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        cities:
          type: array
          items:
            type: string
          description: List of cities available for pricing
          example: ["Los Angeles", "New York", "Chicago"]
  401:
    description: Unauthorized access
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

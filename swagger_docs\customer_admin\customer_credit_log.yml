tags:
  - Customer_admin
summary: Get Customer Credit Log
description: >
  This endpoint allows admins to retrieve the credit transaction log for a customer, filtered by mobile number. 
  Admins can optionally filter the results by payment method and transaction status. Only users with the appropriate 
  admin roles can access this data, and their account must be enabled.
parameters:
  - name: mobile
    in: formData
    required: true
    type: string
    description: Mobile number of the customer for whom the credit log is being retrieved
    example: "**********"
  - name: method
    in: formData
    required: false
    type: string
    description: (Optional) Filter transactions by payment method (e.g., D4M credit, Admin panel, Paytm)
    example: "Admin panel"
  - name: status
    in: formData
    required: false
    type: integer
    description: (Optional) Filter transactions by their status. Defaults to completed transactions.
    enum: 
      - 1  # Completed
      - 2  # Pending
    example: 1
responses:
  200:
    description: Successfully retrieved the customer credit log
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        balance:
          type: number
          format: float
          description: The customer's remaining credit balance
          example: 500.00
        data:
          type: array
          description: Array of transaction details
          items:
            type: object
            properties:
              amt:
                type: number
                format: float
                description: The amount involved in the transaction
                example: 50.00
              method:
                type: string
                description: The payment method used in the transaction
                example: "Admin panel"
              remark:
                type: string
                description: A remark about the transaction
                example: "Added credit"
              timestamp:
                type: string
                format: date-time
                description: The timestamp of the transaction in IST format
                example: "01 Jan 2024, 12:34:56"
              status:
                type: integer
                description: The status of the transaction (1 = Completed)
                example: 1
              changed_by:
                type: string
                description: Admin name who performed the change
                example: "Admin John Doe"
              trans_id:
                type: string
                description: Transaction ID associated with the payment
                example: "TXN12345"
  201:  # Missing or incomplete request parameters
    description: Required parameters are missing or incomplete
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  401:  # Unauthorized access or disabled admin account
    description: The admin does not have the required permissions or the account is disabled
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  500:  # Internal server error or user not found
    description: Internal server error or user not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message if no transactions were found
          example: "No transactions found"
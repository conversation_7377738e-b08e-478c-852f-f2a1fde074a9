tags:
  - Affiliate
summary: Update Addresses for an Affiliate Representative
description: >
  Updates one or more addresses for an affiliate representative.
parameters:
  - in: body
    name: body
    required: true
    description: JSON payload containing updated address details.
    schema:
      type: object
      required:
        - rep_id
        - regions
      properties:
        rep_id:
          type: integer
          description: ID of the affiliate representative.
          example: 101
        address:
          type: array
          description: List of updated address entries.
          items:
            type: object
            properties:
              add_id:
                type: integer
                example: 1
              address:
                type: string
                example: "123 Updated St, City"
              nickname:
                type: string
                example: "Office"
              latitude:
                type: number
                format: float
                example: 12.9716
              longitude:
                type: number
                format: float
                example: 77.5946
              address_type:
                type: string
                example: "Commercial"
        regions:
          type: string
          description: >
            Comma-separated region codes that the representative should have access to.
          example: "0,1"
responses:
  200:
    description: Addresses updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Addresses updated successfully"
  400:
    description: Missing required fields.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Affiliate representative ID is required"
  500:
    description: Internal Server Error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: "Detailed error message"

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
<title>Map with Available Drivers</title>
<link rel="shortcut icon" href="{{ url_for('static', filename='assets/images/logo-265x265.png') }}" type="image/x-icon">
<!-- Bootstrap CSS -->
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
<!-- he library JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/he/1.2.0/he.min.js"></script>
<!-- Include jQuery -->
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
<!-- Include DataTable -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.css" />
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.js"></script>
<!-- Include Google Maps JavaScript API -->
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCuXbo1CBK_rRlVGP6zaAiXcJV2a3OAis8" type="text/javascript"></script>
<style>
.mapdiv { /* Adjust the height as needed */
  width: 100%; /* Adjust the width as needed */
  height: 75vh
}
#drivertable{
  display: none;
  font-size: 0.8rem;
}
.drivertablediv {
position: absolute;
bottom: 6%;
max-height: 160px; /* Set maximum height for the table container */
overflow-y: auto;
right: 1%;
background-color: white;
}
.table td, .table th {
padding:0.30rem !important
}
.table{
cursor: pointer;
margin-bottom: 0;
}
.gm-style-iw button:focus {
outline: 0;
}
.gm-style-iw button span{ /* Set InfoWindow Button Icon height and Width */
    height: 20px !important;
    width: 20px !important;
    margin: 0 !important;
}
.gm-style-iw button { 
  width: 20px !important; /* Adjust the width as needed */
  height: 22px !important; /* Adjust the height as needed */
}
#drivertable thead{
position: sticky;
top: 0;
z-index: 1;
background: aliceblue;
}
.nodriver{
text-align: center;
font-size: medium;
}
.dt-column-order{
display: none;
}
.dt-layout-cell{
padding: 0 !important;
} 
colgroup{
display: none;
}
.display-4{
    font-size: 2.5rem;
}
@media screen and (max-width: 768px){ /* Mobile Device Styling */
    #drivertable{
        font-size: 0.5rem;
        width: 100% !important;
    }
    .drivertablediv{
        right: 15%;
        max-height: 100px;
    }
}
</style>
</head>
<body>
    <nav id="topMenu" class="navbar navbar-default navbar-fixed-top" style="margin-bottom: 0;">
        <a id="brandName" class="navbar-brand" href="/" style="padding-left: 15px!important; padding-top: 0!important;"><img src="{{ url_for('static',filename='assets/images/brandLogoMerger.png') }}" class="img-rounded" alt="Drivers4me" style="height: 50px;"></a>
    </nav>
    <h1 class="display-4 text-center m-1">Map with Available Drivers</h1>
    <div id="map" class="mapdiv"></div>
    <div class="drivertablediv">
    <table id="drivertable" class="table table-hover">
        <thead>
          <tr>
            <th scope="col">Driver ID</th>
            <th scope="col">Name</th>
            <th scope="col">Mobile No.</th>
            <th scope="col">Book Dist (Km)</th>
            <th scope="col">Dest Dist (Km)</th>
            <th scope="col">Last Trip</th>
          </tr>
        </thead>
        <div class="tablebodydiv">
        <tbody id="tablebody">
        </tbody>
        </div>
    </table>
    </div>
<script>
var REFRESH_TIME= parseInt('{{refresh_time}}')
var message = '{{message}}';
document.getElementById('map').innerHTML = "<p class='nodriver'>"+message+"</p>";
//Data Table Script
 $(document).ready( function () {
     $('#drivertable').DataTable({
         "info": false,
         "responsive":false,
         "paging": false,  // Enable pagination
         "searching": false,  
 })
 } );
function hideRowsWithZeroDestDistance() {
    var tableBody = document.getElementById("tablebody");
    var rows = tableBody.getElementsByTagName("tr");
    for (var i = 0; i < rows.length; i++) {
    var destDistanceCell = rows[i].getElementsByTagName("td")[4]; // Index 4 represents the cell containing dest_distance
        if (parseFloat(destDistanceCell.textContent) === 0.00){
            var table = document.getElementById("drivertable");
            var rows = table.getElementsByTagName("tr");
            var destDistanceCellhead = rows[0].getElementsByTagName("th")[4]; 
            destDistanceCellhead.style.display = "none"; 
            destDistanceCell.style.display = "none"; // Hide the row if dest_distance is 0
        } else {
            destDistanceCell.style.display = ""; // Show the row if dest_distance is not 0
        }
    }
}
function refreshPage() {
    location.reload(); // Reload the page
}
//main function
// Function to display the map with available drivers
function displayMapWithAvailableDrivers() {
    var table = document.getElementById("drivertable").getElementsByTagName('tbody')[0];
    table.innerHTML = ""
    var greenicon = {
    url: "{{ url_for('static', filename='assets/green.png') }}",
    origin: new google.maps.Point(0,0), // origin
    anchor: new google.maps.Point(27.5, 45), // anchor
    };
    var redicon = {
    url: "{{ url_for('static', filename='assets/red.png') }}",
    origin: new google.maps.Point(0,0), // origin
    anchor: new google.maps.Point(27.5, 45), // anchor
    };
    var drivericon = {
    url: "{{ url_for('static', filename='assets/Tracker.svg') }}",
    origin: new google.maps.Point(0,0), // origin
    anchor: new google.maps.Point(27.5, 45), // anchor
    scaledSize: new google.maps.Size(55, 90)
    };
    var dist_with_book_start= parseInt('{{dist_with_book_start}}')
    var bookingid = '{{booking_id}}';
    var driversInfojson = '{{nearby_driver_info}}';
    var decodedDriversInfoJson = he.decode(driversInfojson);
    var correctedJson = decodedDriversInfoJson.replace(/'/g, '"');
    var driversInfoArray = JSON.parse(correctedJson);
    driversInfoArray.sort(function(a, b) {
        return parseFloat(a.distancebook) - parseFloat(b.distancebook); // Sort by distance in ascending order
    });
    if (driversInfoArray.length > 0) {
        document.getElementById('drivertable').style.display = 'table';
        var firstDriver = driversInfoArray[0];
        var booklat = firstDriver.booklat; // Latitude;
        var booklng = firstDriver.booklng; // Longitude;
        var bookdestlat=firstDriver.bookdestlat;
        var bookdestlng=firstDriver.bookdestlng;
        }
    if (bookdestlat !== 0.0 && bookdestlng !== 0.0) {
        centerlat = (booklat + bookdestlat) / 2;
        centerlng = (booklng + bookdestlng) / 2;
        }
    else {
    centerlat = booklat;
    centerlng = booklng;
    }
    try {
        if (driversInfoArray.length === 0) {
            document.getElementById('map').innerHTML = "<p class='nodriver'>No drivers available</p>";
            return;
        }
        //Adjusted Zoom Level according to distance
        if (dist_with_book_start>=150){
            zoomlevel=8
        }
        else{
            zoomlevel=12
        }
        var map = new google.maps.Map(document.getElementById('map'), {
        zoom: zoomlevel, // Adjust zoom level as needed
        streetViewControl: false,
        center: new google.maps.LatLng(centerlat, centerlng), // Default center
        zoomControlOptions: {
            position: google.maps.ControlPosition.TOP_RIGHT, // Position the zoom controls at the top right corner
            style: google.maps.ZoomControlStyle.SMALL // Optionally, customize the style of the zoom controls
        }
        });
        var infoWindow=new google.maps.InfoWindow()
        if(bookdestlat!==0.0){
            var bookingDestMarker = new google.maps.Marker({
            position: { lat: bookdestlat, lng: bookdestlng },
            map: map, 
            title: 'Booking Destination Location',
            icon:redicon
        })
        bookingDestMarker.addListener('mouseover', function() {
        var content = '<div><strong>Booking Destination Location</strong></br>ID:'+bookingid+'</div>'
        infoWindow.setContent(content);
        infoWindow.open(map, bookingDestMarker);
        })
        document.addEventListener('DOMContentLoaded', function() {
        var infoWindow1 = new google.maps.InfoWindow();
        var content = '<div><strong>Booking Destination Location</strong></br>ID:'+bookingid+'</div>'
        infoWindow1.setContent(content);
        infoWindow1.open(map, bookingDestMarker)
        setTimeout(function() {
        infoWindow1.close();
    }, 2000); 
        })}
        var bookingMarker = new google.maps.Marker({
        position: { lat: booklat, lng: booklng },
        map: map,
        title: 'Booking Location',
        icon:greenicon
        })
        bookingMarker.addListener('mouseover', function() {
        var content = '<div><strong>Booking Start Location</strong></br>ID:'+bookingid+'</div>'
        infoWindow.setContent(content);
        infoWindow.open(map, bookingMarker);
        })
        document.addEventListener('DOMContentLoaded', function() {
        var infoWindow2 = new google.maps.InfoWindow();
        var content = '<div><strong>Booking Start Location</strong></br>ID:'+bookingid+'</div>'
        infoWindow2.setContent(content);
        infoWindow2.open(map, bookingMarker);
        setTimeout(function() {
        infoWindow2.close();
        }, 2000)
        })
        driversInfoArray.forEach(function(driver) {
            var row = table.insertRow();
            var idCell = row.insertCell(0);
            var nameCell = row.insertCell(1);
            var mobileCell = row.insertCell(2);
            var distancebook = row.insertCell(3);
            var distancedest = row.insertCell(4);
            var lasttrip=row.insertCell(5);
            idCell.innerHTML = driver.driver_id;
            nameCell.innerHTML = driver.name;
            mobileCell.innerHTML = driver.mobile;
            distancebook.innerHTML=parseFloat(driver.distancebook).toFixed(2)
            lasttrip.innerHTML=driver.lasttrip
            distancedest.innerHTML=parseFloat(driver.distancedest).toFixed(2)
            row.addEventListener('click', function() {
            var content = '<div><strong>Driver ID:</strong> ' + driver.driver_id + '</div>' +
            '<div><strong>Driver Name:</strong> ' + driver.name + '</div>' +
            '<div><strong>Driver Number:</strong> ' + driver.mobile + '</div>'+
            '<div><strong>Driver Last Updated:</strong> ' + driver.last_updated_time + '</div>';
            infoWindow.setContent(content);
            map.setCenter({ lat: driver.lat, lng: driver.lng });
            infoWindow.open(map, marker);
        });
        var marker = new google.maps.Marker({
        position: { lat: driver.lat, lng: driver.lng },
        map: map,
        icon:drivericon
        });
        marker.addListener('mouseover', function() {
        var content = '<div><strong>Driver ID:</strong> ' + driver.driver_id + '</div>' +
        '<div><strong>Driver Name:</strong> ' + driver.name + '</div>' +
        '<div><strong>Driver Number:</strong> ' + driver.mobile + '</div>'+
        '<div><strong>Driver Last Updated:</strong> ' + driver.last_updated_time + '</div>';
        infoWindow.setContent(content);
        infoWindow.open(map, marker);
        });
        marker.addListener('mouseout', function() {
        infoWindow.close();
        });
        });
    }
    catch (error) {
        console.error('Error parsing JSON or Map:', error);
    }
    setTimeout(refreshPage, REFRESH_TIME);
    }
    displayMapWithAvailableDrivers();
    document.addEventListener("DOMContentLoaded", function() {
        hideRowsWithZeroDestDistance();
    })
    </script>
</body>
</html>
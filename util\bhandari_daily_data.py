import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str = (datetime.now() - timedelta(days=0)).date().strftime("%d%m%Y")
filepathK = D4M_UTIL_PATH + 'output/kolkata-bhandari.csv'
subjectK = "Bhandari Daily Update - Kolkata - "+date_str
content = "Please find the attached data."
from_addr = "<EMAIL>"
to_addr_list_kolkata = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
send_mail(from_addr, to_addr_list_kolkata, subjectK, content, filepathK)
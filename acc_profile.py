#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  acc_profile.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import html
import re
from flasgger import swag_from
from flask import Blueprint, jsonify, request
from flask_jwt_extended import (
    jwt_required, get_jwt_identity,
    get_jwt)

from models import Users, Drivers, DriverInfo, DriverRegion
from models import db
from _utils import complete, get_pwd, get_salt
from _utils_acc import account_enabled
from login_common import validate_pass
from _ops_message import send_slack_msg
from cluster_loc import LocStr
from referral import get_user_ref_code
from socketio_app import live_update_to_channel

acc_profile = Blueprint('acc_profile', __name__)


def is_driver(user):
    user_is_driver = db.session.query(Users, Drivers).filter(Users.id == Drivers.user). \
        filter(Users.id == user).first()
    if not user_is_driver:
        return False
    else:
        return True


@acc_profile.route('/api/profile/details', methods=['POST'])
@jwt_required()
def get_profile():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'user_mobile': -1}), 401
    if not account_enabled(user):
        return jsonify({'user_mobile': -1}), 401
    user_detail = db.session.query(Users).filter(Users.id == user).first()
    if not user_detail:
        return jsonify({'user_mobile': -1}), 201
    return jsonify({'user_fname': user_detail.fname, 'user_lname': user_detail.lname, 'user_mobile': user_detail.mobile,
                    'user_email': user_detail.email, 'user_sex': user_detail.sex, 'user_ref_code': get_user_ref_code(user_detail)})


@acc_profile.route('/api/profile/type', methods=['POST'])
@jwt_required()
def get_profile_type():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    return is_driver(user), 200


@acc_profile.route('/api/profile/change_name', methods=['POST'])
@swag_from('/app/swagger_docs/users/change_name.yml')
@jwt_required()
def change_name():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    #if is_driver(user):
       #return jsonify({'success': -2}), 200  # drivers can't change name
    if not complete(request.form, ['fname', 'lname']):
        return jsonify({'success': -3,'message': 'First name or last name is missing'}), 201  # incomplete
    fname = html.escape(request.form['fname'])
    lname = html.escape(request.form['lname'])
    if len(fname) < 1:
        return jsonify({'success': -5, 'message': 'Incomplete form details'}) # incomplete
    try:
        db.session.query(Users).filter(Users.id == user).update({Users.fname: fname})
        db.session.query(Users).filter(Users.id == user).update({Users.lname: lname})
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Name changed successfully'}), 200
    except Exception as e:
        db.session.rollback()  # rollback in case of an error
        print(f"Error updating name: {e}")
        return jsonify({'success': -4, 'error': str(e), 'message': 'DB Error'}), 500 


@acc_profile.route('/api/profile/change_email', methods=['POST'])
@swag_from('/app/swagger_docs/users/change_email.yml')
@jwt_required()
def change_email():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    if is_driver(user):
        return jsonify({'success': -2, 'message': 'Driver can not change Email'}), 200  # drivers can't change name
    if not complete(request.form, ['email']):
        return jsonify({'success': -3, 'message': 'Incomplete form details'}), 201  # incomplete
    if not re.search(r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)", request.form['email']):
        return jsonify({'success': 0, 'message': 'Invalid e-mail'})  # invalid mail
    try:
        db.session.query(Users).filter(Users.id == user).update({Users.email: request.form['email']})
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Successfully changed Email ID'}), 200
    except Exception as e:
        db.session.rollback()  # rollback in case of an error
        print(f"Error updating email: {e}") 
        return jsonify({'success': -4, 'error': str(e), 'message': 'DB Error'}), 500  


@acc_profile.route('/api/profile/get/ref_code', methods=['POST'])
@jwt_required()
def get_ref_code():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    if is_driver(user):
        return jsonify({'success': -2}), 200  # drivers can't change name
    try:
        user_det = db.session.query(Users).filter(Users.id == user).first()
        ref_code = get_user_ref_code(user_det)
        return jsonify({'success': 1, 'ref_code': ref_code})
    except Exception:
        return jsonify({'success': -3}), 201


@acc_profile.route('/api/profile/change_base_loc', methods=['POST'])
@jwt_required()
def change_base_loc():
    try:
        user = get_jwt_identity()
        claims = get_jwt()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -2}), 200  # only drivers have base loc
    if not complete(request.form, ['lat', 'long']):
        return jsonify({'success': -3}), 201  # incomplete
    driver_id = db.session.query(Drivers).filter(Drivers.user == user).first().id
    user_name = db.session.query(Users).filter(Users.id == user).first().get_name()
    lat = float(request.form['lat'])
    lng = float(request.form['long'])
    exists = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id).first()
    regions = LocStr.get_loc_list(lat, lng)
    for region in regions:
        exists = db.session.query(DriverRegion).filter(DriverRegion.driver==driver_id).filter(DriverRegion.region == region).first()
        if not exists:
            dr = DriverRegion(driver_id, region)
            db.session.add(dr)
            send_slack_msg(0, "Added" + user_name + " to region " + str(region))
    db.session.commit()
    if not exists:
        new = True
    else:
        new = False
    try:
        if not new:
            db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id).\
                update({DriverInfo.pres_addr_lat: lat,
                        DriverInfo.pres_addr_lng: lng,
                        DriverInfo.pres_region: LocStr.get_loc_str(lat, lng)})
            db.session.commit()
        return jsonify({'success': 1}), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -4}), 201


@acc_profile.route('/api/profile/available', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/change_available.yml')
@jwt_required()
def change_available():
    try:
        user = get_jwt_identity()
        claims = get_jwt()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -2, 'message': 'Unauthorized role: not Driver'}), 200  # only drivers have base loc
    if not complete(request.form, ['available']):
        return jsonify({'success': -3, 'message': 'Incomplete for details'}), 201  # incomplete
    available = bool(int(request.form['available']))
    try:
        driver_det = db.session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.user == user).first()
        driver_det[0].available = int(available)
        driver_id = driver_det[0].id
        db.session.commit()
        #user_name = db.session.query(Users).filter(Users.id == user).first().get_name()
        #if not available:
        #    send_slack_msg(0, user_name + " turned availability off")
        driver_data = {
            'driver_id': driver_id,
        }
        live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[1].region, channel= 'update_driver')
        return jsonify({'success': 1, 'message': 'Updated available state'}), 200
    except Exception:
        return jsonify({'success': -4, 'message': 'Server error'}), 201

@acc_profile.route('/api/profile/check', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/get_available.yml')
@jwt_required()
def get_available():
    try:
        user = get_jwt_identity()
        claims = get_jwt()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -2, 'message': 'Unauthorized role: not Driver'}), 200  # only drivers have base loc
    try:
        available = db.session.query(Drivers).filter(Drivers.user == user).first().available
        return jsonify({'success': 1, 'available': available, 'message': 'Fetched available status'})
    except Exception:
        return jsonify({'success': -3, 'message': 'Server error'}), 201

@acc_profile.route('/api/profile/change_pwd', methods=['POST'])
@swag_from('/app/swagger_docs/users/change_pwd.yml')
@jwt_required()
def change_pwd():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    if not complete(request.form, ['oldpwd', 'newpwd']):
        return jsonify({'success': -2, 'message': 'Incomplete form details'}), 201  # incomplete
    if len(request.form['newpwd']) < 6:
        return jsonify({'success': -3, 'message': 'Password should be atleast 6 letters long'}), 201  # incomplete
    user_obj = db.session.query(Users).filter(Users.id == user).first()
    correct = validate_pass(user_obj, request.form['oldpwd'])
    if not correct:
        return jsonify({'success': 0, 'message': 'Invalid password'})  # wrong pwd
    salt = get_salt()
    pwd = get_pwd(request.form['newpwd'], salt)
    try:
        db.session.query(Users).filter(Users.id == user).\
            update({Users.pwd: pwd})
        db.session.query(Users).filter(Users.id == user). \
            update({Users.salt: salt})
        db.session.commit()
        return jsonify({'success': 1, 'message': 'Password changed successfully'}), 200
    except Exception as e:
        db.session.rollback()  # rollback in case of an error
        print(f"Error updating password: {e}")
        return jsonify({'success': -4, 'error': str(e), 'message': 'DB Error'}), 500 


@acc_profile.route('/api/profile/disable', methods=['POST'])
@jwt_required()
def disable_account():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['pwd']):
        return jsonify({'success': -2}), 201  # incomplete
    user_obj = db.session.query(Users).filter(Users.id == user).first()
    correct = validate_pass(user_obj, request.form['pwd'])
    if not correct:
        return jsonify({'success': 0})  # wrong pwd
    try:
        db.session.query(Users).filter(Users.id == user).\
            update({Users.enabled: False})
        db.session.commit()
        return jsonify({'success': 1}), 200
    except Exception:
        return jsonify({'success': -4}), 201
tags:
  - Booking
summary: Decline User Charge
description: >
  This endpoint allows a user to cancel the charge for a booking.
parameters:
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The booking ID of the ride
  - name: reason
    in: formData
    type: integer
    required: false
    description: Reason for cancellation
responses:
  200_a:
    description: No charges applied
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        charge:
          type: array
          items:
            type: integer
          description: List of charges
          example: [0, 0]
        message:
          type: string
          description: Success message
          example: "No charges applied"
    examples:
      application/json:
        success: 1
        charge: [0, 0]
        message: "No charges applied"
  200_b:
    description: Fetched charges successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        charge:
          type: integer
          description: Penalty charge for the cancellation
          example: 500
        message:
          type: string
          description: Success message
          example: "Fetched charges successfully"
    examples:
      application/json:
        success: 1
        charge: 500
        message: "Fetched charges successfully"
  401_a:
    description: Unauthorized - Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"
  401_b:
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Server Error"
    examples:
      application/json:
        success: -1
        message: "Server Error"

var pos;
     if( navigator.geolocation )
                  {
                     // Call getCurrentPosition with success and failure callbacks
                     navigator.geolocation.getCurrentPosition( function(position) {
                       pos = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                      };
                      //alert("x");

                     },function(error) {
                       pos = {
                        lat: 22.563518,
                        lng: 88.351013
                      };
                     },{timeout:2000,enableHighAccuracy: true} );
                  }
                  else
                  {
                     //alert("Sorry, your browser does not support geolocation services.");
                     pos = {
                        lat: 22.563518,
                        lng: 88.351013
                      };

                }
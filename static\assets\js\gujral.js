$(document).ready(function () {

// <editor-fold desc="Ready Operations">
    //Initialize Date-Time pickers
    initDTPickers();
    //Load Map Inputs in form
    //load all Source SPOC info
    var notSet = true;
    var c = getCookie();
    if (c["region"] == 1) {
        $("#region option[value='0']").remove();
        $('#region').append('<option value="1">Hyderabad</option>');
    }
    if (c["region"] == -1) {
        $('#region').append('<option value="1">Hyderabad</option>');
    }
// </editor-fold>

// <editor-fold desc="Events">
    //opening the correct panel....................................................................................

    //Necessary Hack - Absolutely no idea why the next function ignores this particular selector
    $("#newBooking").click(function () {
        if (!$(this).hasClass('inactive')) return;
        $("#topBar").find("#navComponents").find(".active").removeClass('active').addClass('inactive');
        $(this).removeClass('inactive').addClass('active');
        //code for close all other panels
        $(".function-panel").addClass('collapse');
        //open the correct panel
        $("#"+$(this).attr('id')+"Panel").removeClass('collapse');
    });
    $("#bookings").click(function() {
        if (!$(this).hasClass('inactive')) return;
        $("#topBar").find("#navComponents").find(".active").removeClass('active').addClass('inactive');
        $(this).removeClass('inactive').addClass('active');
        //code for close all other panels
        $(".function-panel").addClass('collapse');
        //open the correct panel
        $("#"+$(this).attr('id')+"Panel").removeClass('collapse');
        if (notSet) {
            $("#newBookingTab").removeClass('collapse')
            $("#new").removeClass('inactive').addClass('active');
            $("#new").click();
            notSet = false;
        }
    });

    //opening the correct booking tabs....................................................................................
    $("#bookingTypeBar").find(".navbar-nav").find(".inactive").click(function() {
        //clear all entry areas
        $(".booking-tab").html('');
        $("#bookingTypeBar").find(".navbar-nav").find(".active").attr('class','book-tab inactive');
        $(this).attr('class','book-tab active');
        //close all other booking tabs
        $(".booking-tab").addClass("collapse");
        //open the correct tab
        $("#"+$(this).attr('id')+"BookingTab").removeClass("collapse");
        var dropDownChoiceInfo = $(this).closest(".navbar-nav").find(".dropdown-choice-info");
        dropDownChoiceInfo.html();
        if(!dropDownChoiceInfo.hasClass("collapse")) dropDownChoiceInfo.addClass("collapse");
        //proceed to fetching entries via AJAX call
        populateBookingEntries($(this).attr("id"));
    });

    //opening booking tabs from dropdowns
    $("#bookingTypeBar").find(".dropdown-menu").find(".inactive").click(function() {
        var dropDownChoiceInfo = $(this).closest(".navbar-nav").find(".dropdown-choice-info");
        dropDownChoiceInfo.html(" (" + $(this).find("a").html() + ") ");
        dropDownChoiceInfo.removeClass("collapse");
    });

    //Submit new booking form
    //TO DO - Need to think of better ways to get lat long directly from autocomplete string
    $("#book").click(function() {
        //validate all fields
        var valid = true;
        var validationFieldList = [];
        if(valid) {
            var region = parseInt($("#region").val());
            //the following snippet extracts date object from the two datetime fields and then merges them into one
            //datetime object with the date values of the date field and time values of the time field
            var scheduledDateTime = new Date($('#datetimepicker_pickup_date').data("DateTimePicker").date());
            var tempScheduledTime = new Date($('#datetimepicker_pickup_time').data("DateTimePicker").date());
            scheduledDateTime.setHours(tempScheduledTime.getHours(), tempScheduledTime.getMinutes(),
                tempScheduledTime.getSeconds(), tempScheduledTime.getMilliseconds());
            var scheduledDateTimeUTC = scheduledDateTime.toLocaleString
                                        ('en-GB',
                                            {   day: "numeric",
                                                month: "numeric",
                                                year: "numeric",
                                                hour: 'numeric',
                                                minute: 'numeric',
                                                second: 'numeric',
                                                timeZone: 'UTC'

                                            }
                                        ).replace(new RegExp(/\//g), "-").replace(',', '');
            var numDrivers = $('#num-drivers').val();
            var numHours = $('#num-hours').val();
            var shift = $('#shift-select').val();

            registerNewBooking  (numDrivers, numHours, shift, scheduledDateTimeUTC, region);
            $("#newBookingForm").trigger("reset");
            initDTPickers();
        }
        else {
            var validationList = getValidationMessageListHtml(validationFieldList);
            openInformationModal("Information", "<p>The following issues were found with some of the fields:</p>" + validationList, "danger");
        }

    });

    $('body').delegate("#AfterBookingModal .continue-button", "click", function () {
        if($("#collapsedMainMenu").css('display') === "none") {
            $("#new").click();
            $('#bookingsPanel').removeClass('collapse');
            $('#newBookingPanel').addClass('collapse');
            $("#newBooking").removeClass("active").addClass("inactive");
            $("#bookings").removeClass("active").addClass("inactive");
        }
        else {
            $("#bookingsResp").trigger('click');
        }
        $("#AfterBookingModal").modal('hide');
    });

    if(window.location.hash) {
      if (window.location.hash == "#newBooking") {
          $("#newBooking").click();
          $('#bookingsPanel').addClass('collapse');
          $('#newBookingPanel').removeClass('collapse');
      } else if (window.location.hash == "#bookingList") {
          $("#new").click();
          $('#bookingsPanel').removeClass('collapse');
          $('#newBookingPanel').addClass('collapse');

      } else if (window.location.hash == "#allBookings") {
          $('#bookings').click();
          $('#all').click();
      } else if (window.location.hash == "#newBookings") {
          $('#bookings').click();
          $("#new").click();
      } else if (window.location.hash == "#upcomingBookings") {
          $('#bookings').click();
          $("#upcoming").click();
      }  else if (window.location.hash == "#ongoingBookings") {
          $('#bookings').click();
          $("#ongoing").click();
      }  else if (window.location.hash == "#completedBookings") {
          $('#bookings').click();
          $("#completed").click();
      }  else if (window.location.hash == "#d4mCancelBookings") {
          $('#bookings').click();
          $("#cancel-d4m").click();
      } else if (window.location.hash == "#gujralCancelBookings") {
          $('#bookings').click();
          $("#cancel-gujral").click();
      } else {
           $("#newBooking").click();
           $('#bookingsPanel').addClass('collapse');
           $('#newBookingPanel').removeClass('collapse');
      }
    } else {
        $("#newBooking").click();
        $('#bookingsPanel').addClass('collapse');
        $('#newBookingPanel').removeClass('collapse');
    }

    $('body').delegate(".track", "click", function() {
      showCurrentLocation(db, $(this).attr('data'));
    });

    $("#logout").click(function() {
        logoutGujral();
        deleteCookie("access_token_cookie");
        deleteCookie("csrf_access_token");
        deleteCookie("csrf_refresh_token");
        deleteCookie("refresh_token_cookie");
        window.location = "/affiliate/gujral/login";
    });

    $('#shift-select').change(function() {
    	var curDate = new Date($('#datetimepicker_pickup_date').data("DateTimePicker").date());
    	if ($(this).val() == 0) {
    		var mornStart = new Date(curDate.setHours(8,30,0,0));
        	$('#datetimepicker_pickup_time').data("DateTimePicker").date(mornStart);
        } else {
    		var nightStart = new Date(curDate.setHours(20,30,0,0));
        	$('#datetimepicker_pickup_time').data("DateTimePicker").date(nightStart);
        }
    });
    //
    // </editor-fold>

// </editor-fold>

});

function initDTPickers() {
    //Initialize datetime pickers
    var start_Date = new Date(new Date().getTime() + 2 * 60 * 60 * 1000);
    var pickupDateElement = $('#datetimepicker_pickup_date');
    var pickupTimeElement = $('#datetimepicker_pickup_time');
    //initialize datepicker
    pickupDateElement.datetimepicker({
        locale: 'en',
        //minDate: min_Date,
        //maxDate: max_Date,
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'bottom'
        },
        format: 'DD/MM/YYYY'
    });
    pickupDateElement.data("DateTimePicker").date(start_Date);
    //initialize timepicker
    pickupTimeElement.datetimepicker({
        locale: 'en',
        //minDate: min_Date,
        //maxDate: max_Date,
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'bottom'
        },
        format : 'LT'
    });

	var mornStart = new Date(start_Date.setHours(8,30,0,0));
    pickupTimeElement.data("DateTimePicker").date(mornStart);
}

/**
 * gathers the parameters for a new booking and fires the AJAX call
 * TO DO - As all validation is handled in the caller methods, a quick check to
 * see if more validation is necessary must be done
 * @param numDrivers - Number of drivers requested
 * @param numHours Number of hours requested
 * @param shift Day / Night shift
 * @param scheduledDateTime - in UTC
 * @param region - region id
 * The AJAX call equivalent parameters are mentioned in []
 */
function registerNewBooking(numDrivers, numHours, shift, scheduledDateTime, region) {
    var data = new FormData();
    data.append("drivers", numDrivers);
    data.append("dur", numHours);
    data.append("shift", shift);
    data.append("book_dt", scheduledDateTime);
    data.append("region", region);
    for (var pair of data.entries()) {
        console.log(pair[0]+ ', ' + pair[1]);
    }
    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/gujral/book',
        data: data,
        beforeSend: function (request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            if (refresh_token) {


            }
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        dataType: "json",
        contentType: false,
        processData: false,
        success: function (response) {
            if(response["success"] == 1) {
                openModal("AfterBooking", "success");
            }
            else {
                openModal("AfterBooking", "failure");
            }
        }
    });
}

/**
 * TO DO - Make a separate JS for this function, it doesn't belong here
 */
function getCookie() {
    cookieSplit = document.cookie.split("; ");
    var cookieObject = {};
    cookieSplit.forEach( function(value, index) {
        var splitResult = value.split("=");
        cookieObject[splitResult[0]] = splitResult[1];
    });
    return cookieObject;
}

// <editor-fold desc="HTML content generation">

function getMainMenuModalContent() {
    $("#MainMenuModal").find('.modal-body').html(
        '<ul class="nav navbar-nav">'+
            '<li id="newBookingResp" class="resp-menu-item">New Booking</li>'+
            '<li id="bookingsResp" class="resp-menu-item">Bookings</li>'+
            '<li class="resp-menu-item">' +
            '<button id="logout_resp" class="btn navbar-btn d4m-primary-button navbar-right">\n' +
            'Logout&emsp;<span class="glyphicon glyphicon-off"></span>'+
            '</button>'+
            '</li><br>'+
        '</ul>'
    );
}

function getBookingMenuModalContent() {
    $("#BookingMenuModal").find('.modal-body').html(
        '<ul class="nav navbar-nav">'+
            '<li id="allResp" class="resp-menu-item">All</li>'+
            '<li id="newResp" class="resp-menu-item">New</li>'+
            '<li id="upcomingResp" class="resp-menu-item">Upcoming</li>'+
            '<li id="ongoingResp" class="resp-menu-item">Ongoing</li>'+
            '<li id="completedResp" class="resp-menu-item">Completed</li>'+
            '<li id="cancel-d4mResp" class="resp-menu-item">Cancelled (by D4M)</li>'+
            '<li id="cancel-gujralResp" class="resp-menu-item">Cancelled (by Gujral Car Rentals)</li>'+
            '<li class="resp-menu-item">' +
                '<button id="logout_resp" class="btn navbar-btn d4m-primary-button navbar-right">\n' +
                    'Logout&emsp;<span class="glyphicon glyphicon-off"></span>'+
                '</button>'+
            '</li><br>'+
        '</ul>'
    );
}

function getAfterBookingModalContent(status) {
    var header = status === "success" ? "Booking Successful!" : "Booking Failed!"
    var message =   status === "success" ?
                    "Booking successful!." :
                    "Your booking could not be registered. Please report the situation to your administrators.";
    $("#AfterBookingModal").find('.modal-header').html(header);
    $("#AfterBookingModal").find('.modal-body').html(message);
    if(status === "success") {
        $("#AfterBookingModal").find('.modal-footer').find('.continue-button').removeClass("collapse");
    }
    else {
        $("#AfterBookingModal").find('.modal-footer').find('.continue-button').addClass("collapse");
    }
}


function getInformationModalContent(message, color) {
    $("#InformationModal").find(".modal-header").removeClass("d4m-element-primary d4m-element-danger").addClass("d4m-element-" + color);
    $("#InformationModal").find(".modal-footer").removeClass("d4m-element-primary d4m-element-danger").addClass("d4m-element-" + color);
    $("#InformationModal").find(".modal-body").html(message);
}

function getDriverListModalContent() {
    $("#DriverListModal").find(".modal-body").html
    (
        '<div class="row no-padding standard-top-padding">' +
            '<div class="col-lg-12 col-md-12 col-sm-12 col-s-12 no-padding driver-list">' +
            '</div>' +
        '</div>'
    );
}

/**
 * With a given booking id, driver id, name, contact and a set of action buttons
 * creates a driver entry for a list
 * @param bookingID
 * @param driverID
 * @param fullname
 * @param contact
 * @param actions - string array of action button names
 * @return {string} HTML for driver entry
 */
function getDriverEntryHtml(bookingID ,driverID, fullname, contact, actions) {
    var actionButtonSection = '';
    $.each(actions, function (index, value) {
        switch(value) {
            case "Cancel":
            case "Close":
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-danger-button">' + value + '</button>' +
                    '</div>';
            break;
            //TO DO - Write CSS for Info, warning and success
            case "Details":
            case "More Info":
            case "Info":
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-info-button">' + value + '</button>' +
                    '</div>';
            break;
            case "Allocate":
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding  align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-primary-button allocate-driver">' + value + '</button>' +
                    '</div>';
            break;
            default:
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-primary-button">' + value + '</button>' +
                    '</div>';
        }
    });
    //driver_bookingID_driverID
    return (
        '<div id="driver_' + bookingID + "_" + driverID + '" class="row driver-entry tiny-top-padding tiny-bottom-padding" style="text-align: center; margin: 0;">' +
            '<div class="col-lg-12 col-md-12 col-sm-12 col-s-12 no-padding">' +
                '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-6 no-padding"><p class="name">' + fullname + '</p></div>'+
                '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-6 no-padding"><p class="contact">' + contact + '</p></div>'+
                '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding">' +
                    '<div class="row no-padding action-section">' + actionButtonSection +
                    '</div>' +
                '</div> ' +
            '</div>' +
        '</div>'
    );
}


/**
 * Dumb implementation - try to logout, if we can't pretend we did!
 */

function logoutGujral() {
    $.ajax({
        type:"POST",
        url: window.location.protocol + '//' + window.location.host + '/token/remove',
        data: "",
        beforeSend: function(request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            request.setRequestHeader('X-CSRF-Token', csrf_token);
         },
        dataType: "json",
        contentType: false,
        processData: false
    });
}
// </editor-fold>
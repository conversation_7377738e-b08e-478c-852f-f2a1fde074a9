tags:
  - Affiliate_Profile
summary: Delete Address
description: >
  Deletes an existing address by its ID.
parameters:
  - name: address_id
    in: body
    type: integer
    required: true
    description: The ID of the address to delete.
responses:
  200:
    description: Address deleted successfully.
    schema:
      type: object
      properties:
        message:
          type: string
          example: Address deleted successfully.
  400:
    description: Missing address_id.
    schema:
      type: object
      properties:
        error:
          type: string
          example: address_id is required.
  404:
    description: Address not found.
    schema:
      type: object
      properties:
        error:
          type: string
          example: Address with address_id 123 does not exist.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        error:
          type: string
          example: An error occurred.

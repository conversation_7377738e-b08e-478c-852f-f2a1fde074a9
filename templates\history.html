<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
  <title>Drivers4Me - Tracking History</title>
  <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">
  <script src="https://maps.google.com/maps/api/js?key=AIzaSyCuXbo1CBK_rRlVGP6zaAiXcJV2a3OAis8"
          type="text/javascript"></script>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.1/jquery.min.js"></script>
</head>
<body>
  <div id="map" style="height: 100vh;"></div>

  <script type="text/javascript">
	var locations;
    var map = new google.maps.Map(document.getElementById('map'), {
      zoom: 10,
      center: new google.maps.LatLng(28.514160, 77.297310),
      mapTypeId: google.maps.MapTypeId.ROADMAP
    });

    var infowindow = new google.maps.InfoWindow();;

    var marker, i;
	var jsonData;
	var length = 0;
	$.getJSON('https://drivers4me-prod.appspot.com.storage.googleapis.com/locationHistory/{{booking_id}}.json', function(data) {
		jsonData = data;// JSON result in `data` variable
		console.log(data);
		Object.keys(jsonData).forEach(function(key, i) {
			length +=1
			//console.log('Count : ' + i + 'Key : ' + key + ', Latitude : ' + data[key].latitude + ', Latitude : ' + data[key].longitude)
		});
		console.log(length);
		locations = new Array(length);
		/*for (var i = 0; i < length; i++) {
			location[i] = [0,0,0,0];

		}
		console.log(location);*/
		Object.keys(jsonData).forEach(function(key, i) {
			//console.log();
			//x = key.toString();
			locations[i] = new Array(key, jsonData[key].latitude, jsonData[key].longitude, i+1);
			/*locations[i][0] = key;
			locations[i][1] = jsonData[key].latitude;
			locations[i][2] = jsonData[key].longitude;
			locations[i][3] = i+1;*/
			//console.log('Count : ' + i + 'Key : ' + key + ', Latitude : ' + data[key].latitude + ', Latitude : ' + data[key].longitude)
		});
		myLatlng = new google.maps.LatLng(locations[0][1], locations[0][2]);
        map.setCenter(myLatlng);
		for (i = 0; i < locations.length; i+=5) {
		  marker = new google.maps.Marker({
			position: new google.maps.LatLng(locations[i][1], locations[i][2]),
			map: map,
			icon: {
				path: google.maps.SymbolPath.CIRCLE,
				fillColor: '#ff5733',
				fillOpacity: 0.6,
				strokeColor: '#ff5733',
				strokeOpacity: 0.9,
				strokeWeight: 1,
				scale: 5
			}
		  });

		  google.maps.event.addListener(marker, 'mouseover', (function(marker, i) {
			return function() {
			  infowindow.setContent(locations[i][0]);
			  infowindow.open(map, marker);
			}
		  })(marker, i));
		}

	});
  </script>
</body>
</html>
import sys
sys.path.append("/app/")
import pandas as pd

from export_csv import D4M_UTIL_PATH

def measure_api_times(log_path, output_file):
    output_file1 = D4M_UTIL_PATH + 'output/' + output_file + ".csv"
    output_file2 = D4M_UTIL_PATH + 'output/' + output_file + "-bycode.csv"
    print("Reading from", log_path)
    raw_df = pd.read_csv(log_path, header=None, delimiter=r"\s+")
    processed_df = raw_df[[5, 6, 11]].copy()
    processed_df.columns = ["api", "code", "time"]
    processed_df["api"] = processed_df["api"].str.split(" ").str[1]
    processed_df["time"] = processed_df["time"].str.replace(
                                "response-time=", "").astype("float")
    output_df1 = processed_df.groupby("api").agg(
                    count=('api', 'size'),
                    mean_time=('time', 'mean')).reset_index(
                    ).sort_values("mean_time", ascending=False)
    output_df2 = processed_df.groupby(["api", "code"]).agg(
                    count=('api', 'size'),
                    mean_time=('time', 'mean')).reset_index(
                    ).sort_values("mean_time", ascending=False)
    print("Writing to", output_file1)
    output_df1.to_csv(output_file1, index=True)
    print("Writing to", output_file2)
    output_df2.to_csv(output_file2, index=True)
    print("Done")

if __name__ == '__main__':
    if len(sys.argv) < 2:
        log_path = "/var/log/www.drivers4me.com.access.log"
        output_file = "measured-stats"
    else:
        log_path = sys.argv[1]
        if len(sys.argv) >= 3:
            output_file = sys.argv[2]
    measure_api_times(log_path, output_file)
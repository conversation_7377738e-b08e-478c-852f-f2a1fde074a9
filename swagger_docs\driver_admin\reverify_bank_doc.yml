tags:
  - Driver_admin
summary: Reverify Driver's Bank Document
description: >
  This endpoint re-verifies a driver's bank account details. It either forces the verification process or matches the driver's details against the external service, updating the verification status accordingly.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose bank account is to be reverified
    example: 101
  - name: force_verify
    in: formData
    required: false
    type: integer
    description: Indicates if the verification should be forced (1 for true, 0 for false)
    example: 1
  - name: remarks
    in: formData
    required: false
    type: string
    description: Remarks or comments regarding the verification process
    example: "Reverifying due to discrepancies"
responses:
  200:
    description: Successfully reverified the driver's bank account details
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the re-verification process (1 for successful verification, 3 for forced verification)
          example: 3
        message:
          type: string
          description: Message describing the outcome of the re-verification
          example: "Bank Details Forced Verified"
        details:
          type: object
          description: Verification details including name match and other relevant information
          properties:
            name_match:
              type: boolean
              description: Indicates whether the account holder's name matches
              example: true
  400:
    description: Bad request (verification failed due to issues in the re-verification process)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for re-verification failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Bank Details Failed to Reverify"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-2 for database errors)
          example: -2
        message:
          type: string
          description: Error message
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Internal server error"

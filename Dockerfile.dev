# Use a lightweight Python image
FROM python:3.11-slim

# Set the working directory
WORKDIR /app

# Copy the application code
COPY . .

ENV PYTHONPATH=/app

ENV PYTHONBUFFERED = 1
RUN find . -type f -name “*.pyc” -delete && find . -type d -name “__pycache__” -exec rm -r {} +
# Install dependencies
RUN pip install --no-cache-dir --upgrade pip setuptools wheel
RUN pip install --no-cache-dir -r requirements.txt
RUN apt-get update && apt-get install -y redis-server && \
    service redis-server start 
RUN apt-get update && apt-get install -y tmux && apt-get install -y cron && apt-get install -y vim  && apt-get install -y sudo 
# Expose the application port
EXPOSE 8000

# Run Gunicorn with Gevent
#CMD ["python", "main.py"]
# CMD ["gunicorn", "-w", "4", "-k", "geventwebsocket.gunicorn.workers.GeventWebSocketWorker", "-b", "0.0.0.0:8000", "main:app"]
CMD ["gunicorn", "-w", "4", "--threads", "8", "-k", "geventwebsocket.gunicorn.workers.GeventWebSocketWorker", "-b", "0.0.0.0:8000", "--timeout", "120", "--keep-alive", "3", "--limit-request-line", "8190", "--limit-request-field_size", "8190", "--log-level", "debug", "--access-logfile", "-", "--error-logfile", "-", "main:app"]
#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  price_zoomcar_delhi.py
#
#  Copyright 2017-2021 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime
from booking_params import BookingParams
from _utils import get_dt_ist
from db_config import db
from models import Bookings, Trip
import math

class PriceZoomcarDelhi:
    MIN_DIST_THRESH = 16

    BASE_MINIMUM = 170
    BASE_FLAT = 8
    NIGHT_HIKE = 100

    BASE_MINIMUM_OP = (200, 170, 170)
    BASE_FLAT_OP = (8, 8, 8)
    NIGHT_HIKE_OP = (100, 100, 100)

    NIGHT_THRESH_0 = datetime.time(17, 0, 0)
    NIGHT_THRESH_1 = datetime.time(0, 30, 0)

    OP_DAYS = [[], [], []] #changed 90 120 120

    @staticmethod
    def update_op_days():
        PriceZoomcarDelhi.OP_DAYS[0] = [datetime.date(2022, 10, 24),datetime.date(2022, 10, 25),datetime.date(2022, 10, 26)]
        PriceZoomcarDelhi.OP_DAYS[1] = []
        PriceZoomcarDelhi.OP_DAYS[2] = []

    @staticmethod
    def get_cancel_ch(curtime, starttime):
        return 0

    @staticmethod
    def get_booking_ch(car_type, driver_id, booking_id):
        return 0

    @staticmethod
    def get_insurance_ch(booking_id, city=0):
        return 0

    @staticmethod
    def get_dist_fare(dist, city=0):
        extra_dist = max(0, dist - PriceZoomcarDelhi.MIN_DIST_THRESH)
        return extra_dist * PriceZoomcarDelhi.BASE_FLAT

    @staticmethod
    def get_dist_fare_op(dist, op_level, city=0):
        extra_dist = max(0, dist - PriceZoomcarDelhi.MIN_DIST_THRESH)
        return extra_dist * PriceZoomcarDelhi.BASE_FLAT_OP[op_level]

    @staticmethod
    def get_op_level(startdate, starttime, enddate, endtime):
        start_ist = get_dt_ist(startdate, starttime)
        for level in range(len(PriceZoomcarDelhi.OP_DAYS) - 1, -1, -1):

            if start_ist.date() in PriceZoomcarDelhi.OP_DAYS[level]:
                return level
        return -1

    @staticmethod
    def get_price(dur, starttime, endtime, dist, startdate, enddate):
        PriceZoomcarDelhi.update_op_days()
        op_level = PriceZoomcarDelhi.get_op_level(startdate, starttime, enddate, endtime)
        if op_level == -1:
            return PriceZoomcarDelhi.get_price_flat(dur, starttime, endtime, dist, startdate, enddate)
        else:
            return PriceZoomcarDelhi.get_price_op(op_level, dur, starttime, endtime, dist, startdate, enddate)

    @staticmethod
    def get_price_flat(dur, starttime, endtime, dist, startdate, enddate):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0

        base_fare = PriceZoomcarDelhi.BASE_MINIMUM
        dist_fare = PriceZoomcarDelhi.get_dist_fare(dist)
        night = PriceZoomcarDelhi.is_trip_night(starttime, endtime)
        night_fare = PriceZoomcarDelhi.NIGHT_HIKE * int(night)
        total_fare = base_fare + dist_fare + night_fare
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), \
            int(round(dist_fare)), 0, 0, int(round(total_fare)), 0, 0, \
            int(round(total_fare)), 0, False  # surcharge


    @staticmethod
    def get_price_op(op_level, dur, starttime, endtime, dist, startdate, enddate):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0

        base_fare = PriceZoomcarDelhi.BASE_MINIMUM_OP[op_level]
        dist_fare = PriceZoomcarDelhi.get_dist_fare_op(dist, op_level)
        night = PriceZoomcarDelhi.is_trip_night(starttime, endtime)
        night_fare = PriceZoomcarDelhi.NIGHT_HIKE_OP[op_level] * int(night)
        total_fare = base_fare + dist_fare + night_fare
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), \
            int(round(dist_fare)), 0, 0, int(round(total_fare)), 0, 0, \
            int(round(total_fare)), 0, False  # surcharge


    @staticmethod
    def get_trip_price(est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime,
                       startdate, enddate):

        PriceZoomcarDelhi.update_op_days()
        op_level = PriceZoomcarDelhi.get_op_level(startdate, book_starttime, enddate, book_stoptime)
        if op_level == -1:
            return PriceZoomcarDelhi.get_trip_price_gen(est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime)
        else:
            # assume no 7 day roundtrip (lol)
            return PriceZoomcarDelhi.get_trip_price_op(est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime, op_level)

    @staticmethod
    def get_trip_price_gen(est, book_starttime, book_stoptime, trip_starttime, trip_stoptime):
        price = est
        night = PriceZoomcarDelhi.is_trip_night(trip_starttime, trip_stoptime)
        est_night = PriceZoomcarDelhi.is_trip_night(book_starttime, book_stoptime)
        if night and not est_night:
            price += PriceZoomcarDelhi.NIGHT_HIKE
        elif est_night and not night:
            price -= PriceZoomcarDelhi.NIGHT_HIKE
        return int(round(price))

    @staticmethod
    def get_trip_price_op(est, book_starttime, book_stoptime, trip_starttime, trip_stoptime,
                          op_level):
        price = est
        night = PriceZoomcarDelhi.is_trip_night(trip_starttime, trip_stoptime)
        est_night = PriceZoomcarDelhi.is_trip_night(book_starttime, book_stoptime)
        if night and not est_night:
            price += PriceZoomcarDelhi.NIGHT_HIKE_OP[op_level]
        elif est_night and not night:
            price -= PriceZoomcarDelhi.NIGHT_HIKE_OP[op_level]
        return int(round(price))

    @staticmethod
    def is_trip_night(start, end):
        start = datetime.time(start.hour, start.minute)
        end = datetime.time(end.hour, end.minute)
        if PriceZoomcarDelhi.NIGHT_THRESH_0 < PriceZoomcarDelhi.NIGHT_THRESH_1:
            return ((start >= PriceZoomcarDelhi.NIGHT_THRESH_0 and
                     start <= PriceZoomcarDelhi.NIGHT_THRESH_1) or
                    (end >= PriceZoomcarDelhi.NIGHT_THRESH_0 and
                     end <= PriceZoomcarDelhi.NIGHT_THRESH_1))
        else:
            # end of night is after midnight UTC
            return ((start >= PriceZoomcarDelhi.NIGHT_THRESH_0 or
                     start <= PriceZoomcarDelhi.NIGHT_THRESH_1) or
                    (end >= PriceZoomcarDelhi.NIGHT_THRESH_0 or
                     end <= PriceZoomcarDelhi.NIGHT_THRESH_1))
#!/usr/bin/env python
# -*- coding: utf-8 -*-
__version__ = '0.2.26'

from flask import Response
from markupsafe import Markup
from flask import render_template_string

FLASK_UTIL_JS_PATH = '/flask_util.js'

FLASK_UTIL_JS_TPL_STRING = r'''
{%- autoescape false -%}
var flask_util = function() {
    var rule_map = {{ rule_map }};

    function url_for(endpoint, params) {
        if (!params) {
            params = {};
        }

        if (!rule_map[endpoint]) {
            throw('endpoint does not exist: ' + endpoint);
        }

        var rule = rule_map[endpoint];

        var used_params = {};


        var rex = /\<\s*(\w+:)*(\w+)\s*\>/ig;

        var path = rule.replace(rex, function(_i, _0, _1) {
            if (params.hasOwnProperty(_1)) {
                used_params[_1] = params[_1];
                return encodeURIComponent(params[_1]);
            } else {
                throw(_1 + ' does not exist in params');
            }
        });

        var query_string = '';

        for(var k in params) {
            if (used_params.hasOwnProperty(k)) {
                continue;
            }

            var v = params[k];
            if(query_string.length > 0) {
                query_string += '&';
            }
            query_string += encodeURIComponent(k)+'='+encodeURIComponent(v);
        }

        var url = path;
        if (query_string.length > 0) {
            url += '?'+query_string;
        }

        return url;
    }

    return {
        url_for: url_for,
        rule_map: rule_map
    }
}();
{%- endautoescape -%}
'''

class FlaskUtilJs(object):
    """FlaskUtilJs"""

    def __init__(self, app=None):
        """init with app

        :app: Flask instance

        """
        if app:
            self.init_app(app)
            self.allowed_endpoints = []

    def init_app(self, app):
        path = app.config.get('FLASK_UTIL_JS_PATH', FLASK_UTIL_JS_PATH)
        endpoint = app.config.get('FLASK_UTIL_JS_ENDPOINT', None)

        @app.route(path, endpoint=endpoint)
        def flask_util_js():
            return Response(
                self.content,
                content_type='text/javascript; charset=UTF-8',
                headers={
                    'Cache-Control':'no-cache',
                }
            )

        @app.context_processor
        def inject_fujs():
            return dict(flask_util_js=self)

        self._path = path
        self._endpoint = endpoint or flask_util_js.__name__

    @property
    def path(self):
        return self._path

    @property
    def endpoint(self):
        return self._endpoint

    @property
    def content(self):
        rule_map = dict()

        data = render_template_string(
            FLASK_UTIL_JS_TPL_STRING,
            rule_map=rule_map,
        )

        return data

    @property
    def js(self):
        return Markup('<script src="%s" type="text/javascript" charset="utf-8"></script>' % self.path)

    @property
    def embed_js(self):
        return Markup('<script type="text/javascript" charset="utf-8">\n%s\n</script>' % self.content)

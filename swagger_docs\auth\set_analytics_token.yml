tags:
  - Authentication
summary: Set the Analytics token for push notifications
description: Stores or updates the Analytics token associated with a user's device for google add.
parameters:
  - in: formData
    name: analytics_token
    type: string
    required: true
    description: The Google Analytics token
  - in: formData
    name: device
    type: string
    required: true
    description: The device type (e.g., "android", "ios")
responses:
  200:
    description: Analytics token added successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Analytics token added successfully"
    examples:
      application/json:
        success: 1
        message: "Analytics token added successfully"
  400:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: 0
        message: "Incomplete form details"
  500:
    description: Database error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

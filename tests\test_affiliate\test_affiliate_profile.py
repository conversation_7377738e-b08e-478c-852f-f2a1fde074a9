import pytest
import json
from affiliate_b2b.affiliate_models import Affiliate<PERSON>po<PERSON>, Affiliate<PERSON><PERSON><PERSON>, AddressSpoc,AffiliateRep
from db_config import db
from unittest.mock import patch
from sqlalchemy.exc import SQLAlchemyError

""" Test cases for api: /api/affiliate/add_spoc """

# def test_invalid_jwt(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     spoc_data = [
#         {'name': '<PERSON>', 'mobile': '1234567890', 'global': False, 'region': '0'}
#     ]

#     response = client.post('/api/affiliate/add_spoc', json=spoc_data)
#     json_data = response.get_json()
#     assert response.status_code == 401
#     assert json_data['success'] == -1
#     assert json_data['result'] == 'FAILURE'

# def test_affiliate_rep_not_found(client, auth_token_factory):
#     # JWT for a rep_id that does not exist
#     fake_rep_id = 99999
#     headers = auth_token_factory(fake_rep_id)

#     spoc_data = [
#         {'name': 'Ghost Rep', 'mobile': '5555555555', 'global': False, 'region': '0'}
#     ]

#     response = client.post(
#         '/api/affiliate/add_spoc',
#         headers=headers,
#         json=spoc_data
#     )

#     json_data = response.get_json()
#     print(json_data)

#     assert response.status_code == 404
#     assert json_data['success'] == 0
#     assert json_data['message'] == "Affiliate representative not found"

# def test_add_spoc_success(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     spoc_data = [
#         {'name': 'John Doe', 'mobile': '1234567890', 'global': False, 'region': '0'}
#     ]

#     response = client.post(
#         '/api/affiliate/add_spoc',
#         headers=auth_headers,
#         json=spoc_data
#     )
#     json_data = response.get_json()
#     print(json_data)
#     assert response.status_code == 200
#     assert json_data['success'] == 1
#     assert json_data['message'] == 'SPOC data inserted successfully'


# def test_add_spoc_invalid_payload(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login

#     spoc_data = {'invalid': 'payload'}

#     response = client.post(
#                         '/api/affiliate/add_spoc',
#                         headers=auth_headers,
#                         json=spoc_data)
#     json_data = response.get_json()
#     assert response.status_code == 400
#     assert json_data['success'] == -1
#     assert 'Invalid payload' in json_data['message']

# def test_add_spoc_missing_name_or_mobile(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login

#     spoc_data = [
#         {'name': 'John Doe', 'global': False, 'region': '0'}
#     ]

#     response = client.post(
#                         '/api/affiliate/add_spoc',
#                         headers=auth_headers,
#                         json=spoc_data)
    
#     json_data = response.get_json()
#     assert response.status_code == 400
#     assert json_data['success'] == -2
#     assert 'Missing' in json_data['message']

# def test_add_spoc_invalid_mobile_format(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login

#     spoc_data = [
#         {'name': 'John Doe', 'mobile': '123456789', 'global': False, 'region': '0'}
#     ]

#     response = client.post(
#                             '/api/affiliate/add_spoc',
#                            headers=auth_headers,
#                            json=spoc_data)
#     json_data = response.get_json()
#     assert response.status_code == 400
#     assert json_data['success'] == -3
#     assert 'Invalid' in json_data['message']

# def test_add_spoc_mobile_already_globally_registered(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     mobile = '9999999999'

#     # existing GLOBAL SPOC with same mobile
#     global_spoc = AffiliateSpoc(
#         name = 'Existing Global',
#         mobile = mobile,
#         type = AffiliateSpoc.GLOBAL_SPOC,
#         rep_id = rep.id,
#         aff_id = rep.affiliate_id,
#         region = 0
#     )
#     db.session.add(global_spoc)
#     db.session.commit()

#     spoc_data = [{'name': 'John Doe', 'mobile': mobile, 'global': False, 'region': '0'}]

#     response = client.post('/api/affiliate/add_spoc', headers=auth_headers, json=spoc_data)
#     json_data = response.get_json()
#     assert response.status_code == 400
#     assert json_data['success'] == -4
#     assert f"{mobile} is globally registered" in json_data['message']

# def test_add_spoc_mobile_already_locally_registered_as_local(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     mobile = '8888888888'

#     # Add an existing LOCAL SPOC
#     local_spoc = AffiliateSpoc(
#         name = 'Existing Local',
#         mobile = mobile,
#         type = AffiliateSpoc.LOCAL_SPOC,
#         rep_id = rep.id,
#         aff_id = rep.affiliate_id,
#         region = 0
#     )
#     db.session.add(local_spoc)
#     db.session.commit()

#     spoc_data = [{'name': 'John Doe', 'mobile': mobile, 'global': False, 'region': '0'}]

#     response = client.post('/api/affiliate/add_spoc', headers=auth_headers, json=spoc_data)
#     json_data = response.get_json()
#     assert response.status_code == 400
#     assert json_data['success'] == -5
#     assert f"{mobile} is locally registered" in json_data['message']

# def test_add_multiple_spocs_success(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login

#     spoc_data = [
#         {'name': 'Alice', 'mobile': '1111111111', 'global': True, 'region': '1'},
#         {'name': 'Bob', 'mobile': '2222222222', 'global': False, 'region': '2'}
#     ]

#     response = client.post('/api/affiliate/add_spoc', headers=auth_headers, json=spoc_data)
#     json_data = response.get_json()
#     assert response.status_code == 200
#     assert json_data['success'] == 1
#     assert 'inserted successfully' in json_data['message']

# def test_add_spoc_db_exception(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login

#     spoc_data = [{'name': 'Jane', 'mobile': '3333333333', 'global': False, 'region': '3'}]

#     # Simulate DB commit failure
#     with patch('models.db.session.commit', side_effect=Exception('Simulated DB Error')):
#         response = client.post('/api/affiliate/add_spoc', headers=auth_headers, json=spoc_data)
#         json_data = response.get_json()
#         assert response.status_code == 500
#         assert json_data['success'] == -6
#         assert 'Simulated DB Error' in json_data['message']


# """ Test cases for api: /api/affiliate/update_spoc """

# @pytest.fixture
# def create_spoc(affiliate_rep_login):
#     _, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()

#     spoc = AffiliateSpoc(
#         aff_id=rep.affiliate_id,
#         rep_id=rep.id,
#         name="Initial SPOC",
#         mobile="9123456789",
#         region=0,
#         type=AffiliateSpoc.LOCAL_SPOC
#     )
#     db.session.add(spoc)
#     db.session.commit()
#     return spoc

# def test_update_spoc_success(client, affiliate_rep_login, create_spoc):
#     headers, _ = affiliate_rep_login
#     spoc = create_spoc

#     payload = {
#         "id": spoc.spoc_id,
#         "name": "Updated Name",
#         "mobile": "9988776655",
#         "global": False,
#         "region": 1
#     }

#     response = client.put('/api/affiliate/update_spoc', headers=headers, json=payload)
#     assert response.status_code == 200
#     data = response.get_json()
#     assert data["success"] == 1
#     assert data["message"] == "SPOC updated successfully"

# def test_update_spoc_not_found(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     payload = {
#         "id": 99999,  # Non-existent spoc_id
#         "name": "Ghost",
#         "mobile": "9999999999",
#         "global": False,
#         "region": 0
#     }

#     response = client.put('/api/affiliate/update_spoc', headers=headers, json=payload)
#     assert response.status_code == 404
#     assert response.get_json()["message"] == "SPOC not found"

# def test_update_spoc_affiliate_rep_not_found(client, auth_token_factory):
#     fake_headers = auth_token_factory(99999)  # Non-existent rep_id

#     payload = {
#         "id": 1,
#         "name": "Fake Update",
#         "mobile": "9000000000",
#         "global": False,
#         "region": 0
#     }

#     response = client.put('/api/affiliate/update_spoc', headers=fake_headers, json=payload)
#     assert response.status_code == 404
#     assert response.get_json()["message"] == "Affiliate representative not found"

# def test_update_spoc_to_global_conflict(client, affiliate_rep_login, create_spoc):
#     headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     existing_global = AffiliateSpoc(
#         aff_id=rep.affiliate_id,
#         rep_id=rep.id,
#         name="Global User",
#         mobile="9988776655",  # Conflict mobile
#         type=AffiliateSpoc.GLOBAL_SPOC,
#         region=1
#     )
#     db.session.add(existing_global)
#     db.session.commit()

#     payload = {
#         "id": create_spoc.spoc_id,
#         "name": "Updated",
#         "mobile": "9988776655",
#         "global": True,  # Convert to global
#         "region": 1
#     }
    
#     response = client.put('/api/affiliate/update_spoc', headers=headers, json=payload)
#     assert response.status_code == 400
#     assert "exists globally" in response.get_json()["message"]

# def test_update_spoc_to_local_conflict(client, affiliate_rep_login, create_spoc):
#     headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     existing_local = AffiliateSpoc(
#         aff_id=rep.affiliate_id,
#         rep_id=rep.id,
#         name="Local User",
#         mobile="9999888877",  # Conflict mobile
#         type=AffiliateSpoc.LOCAL_SPOC,
#         region=2
#     )
#     db.session.add(existing_local)
#     db.session.commit()

#     create_spoc.spoc_type = AffiliateSpoc.GLOBAL_SPOC
#     db.session.commit()

#     payload = {
#         "id": create_spoc.spoc_id,
#         "name": "Back to Local",
#         "mobile": "9999888877",
#         "global": False,  # Convert to local
#         "region": 2
#     }

#     response = client.put('/api/affiliate/update_spoc', headers=headers, json=payload)
#     assert response.status_code == 400
#     assert "exists locally" in response.get_json()["message"]


# def test_update_spoc_unauthorized(client):
#     # No token passed
#     payload = {
#         "id": 1,
#         "name": "Unauthorized Update",
#         "mobile": "8888888888",
#         "global": False,
#         "region": 0
#     }

#     response = client.put('/api/affiliate/update_spoc', json=payload)
#     json_data = response.get_json()
#     assert response.status_code == 401
#     assert json_data['success'] == -1
#     assert json_data['result'] == 'FAILURE'

# def test_update_spoc_commit_failure(client, affiliate_rep_login, create_spoc):
#     headers, _ = affiliate_rep_login
#     spoc = create_spoc

#     payload = {
#         "id": spoc.spoc_id,
#         "name": "Failing Commit",
#         "mobile": "7777777777",
#         "global": False,
#         "region": 0
#     }

#     with patch('models.db.session.commit', side_effect=SQLAlchemyError("Commit failed")):
#         response = client.put('/api/affiliate/update_spoc', headers=headers, json=payload)
#         assert response.status_code == 500
#         json_data = response.get_json()
#         assert json_data["success"] == 0
#         assert "Commit failed" in json_data["error"]

# def test_update_spoc_query_failure(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login
#     payload = {
#         "id": 1,
#         "name": "Query Fail",
#         "mobile": "1234567890",
#         "global": False,
#         "region": 0
#     }

#     with patch('models.db.session.query', side_effect=SQLAlchemyError("Query failed")):
#         response = client.put('/api/affiliate/update_spoc', headers=headers, json=payload)

#         assert response.status_code == 500
#         assert response.json['success'] == 0
#         assert "Query failed" in response.json['error']


# """ Test cases for api: /api/affiliate/delete_spoc """

# # Missing spoc_id
# def test_delete_spoc_missing_id(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login
#     response = client.delete('/api/affiliate/delete_spoc', headers=headers, data={})
#     assert response.status_code == 400
#     assert response.json['success'] == 0
#     assert 'Missing spoc id' in response.json['message']

# # Invalid spoc_id (not found)
# def test_delete_spoc_not_found(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login
#     response = client.delete('/api/affiliate/delete_spoc', headers=headers, data={'spoc_id': 99999})
#     assert response.status_code == 404
#     assert response.json['success'] == -1
#     assert 'SPOC not found' in response.json['message']

# # Successful deletion
# def test_delete_spoc_success(client, affiliate_rep_login, create_spoc):
#     headers, _ = affiliate_rep_login
#     spoc = create_spoc
#     spocid = spoc.spoc_id

#     response = client.delete('/api/affiliate/delete_spoc', headers=headers, data={'spoc_id': spoc.spoc_id})
#     assert response.status_code == 200
#     spoc = db.session.query(AffiliateSpoc).filter_by(spoc_id=spocid).first()
#     assert response.json['success'] == 1
#     assert response.json['spoc_id'] == spocid

#     # Check in DB to confirm deletion
#     deleted = db.session.query(AffiliateSpoc).filter_by(spoc_id=spocid).first()
#     assert deleted is None

# # DB failure (simulate SQLAlchemyError)
# def test_delete_spoc_db_error(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     with patch('models.db.session.query', side_effect=SQLAlchemyError("DB error")):
#         response = client.delete('/api/affiliate/delete_spoc', headers=headers, data={'spoc_id': 1})
#         assert response.status_code == 500
#         assert 'error' in response.json
#         assert 'DB error' in response.json['error']

# """ Test cases for api: /api/affiliate/spoc_list """

# def create_spoc_entry(aff_id, rep_id, name, mobile, spoc_type, region):
#     spoc = AffiliateSpoc(
#         aff_id=aff_id,
#         rep_id=rep_id,
#         name=name,
#         mobile=mobile,
#         type=spoc_type,
#         region=region
#     )
#     db.session.add(spoc)
#     db.session.commit()
#     return spoc

# # Unauthorized (missing token)
# def test_spoc_list_unauthorized(client):

#     response = client.get('/api/affiliate/spoc_list')
#     assert response.status_code == 401
#     json_data = response.get_json()
#     assert json_data['success'] == -1
#     assert json_data['result'] == 'FAILURE'

# # Valid request with both local and global SPOCs
# def test_spoc_list_success(client, affiliate_rep_login):
#     headers, aff_rep = affiliate_rep_login
#     aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    
#     # Create local SPOC
#     local = create_spoc_entry(aff_id=aff_rep.affiliate_id, rep_id=aff_rep.id, name='Local Spoc', mobile='9999990000',
#                         spoc_type=AffiliateSpoc.LOCAL_SPOC, region=0)

#     # Global SPOC in matching region with unique mobile
#     global1 = create_spoc_entry(aff_id=aff_rep.affiliate_id, rep_id=aff_rep.id, name='Global Spoc 1', mobile='8888880000',
#                           spoc_type=AffiliateSpoc.GLOBAL_SPOC, region=0)

#     # Global SPOC with duplicate mobile as local -> should be excluded
#     global2 = create_spoc_entry(aff_id=aff_rep.affiliate_id, rep_id=aff_rep.id, name='Global Spoc 2', mobile='9999990000',
#                           spoc_type=AffiliateSpoc.GLOBAL_SPOC, region=0)

#     response = client.get('/api/affiliate/spoc_list', headers=headers)
#     assert response.status_code == 200
#     assert response.json['success'] == 1
#     spocs = response.json['spocs']
#     assert len(spocs) == 2  # Only local + unique global
#     spoc_mobiles = [spoc['spoc_number'] for spoc in spocs]
#     assert '9999990000' in spoc_mobiles  # local
#     assert '8888880000' in spoc_mobiles  # unique global
#     assert 'Global Spoc 2' not in [spoc['spoc_name'] for spoc in spocs]  # excluded duplicate

# # No SPOCs available
# def test_spoc_list_empty(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     response = client.get('/api/affiliate/spoc_list', headers=headers)
#     assert response.status_code == 200
#     assert response.json['success'] == 1
#     assert isinstance(response.json['spocs'], list)
#     assert len(response.json['spocs']) == 0

# # Internal server error (simulate exception)
# def test_spoc_list_server_error(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     with patch('models.db.session.query', side_effect=Exception("DB Error")):
#         response = client.get('/api/affiliate/spoc_list', headers=headers)
#         assert response.status_code == 500
#         assert response.json['success'] == 0
#         assert 'error' in response.json


# """ Test cases for api: /api/affiliate/add_address """


# def test_add_address_invalid_jwt(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     payload = [
#         {
#             "address": "123 Local Street",
#             "lat": "22.5726",
#             "long": "88.3639",
#             "nickname": "Home",
#             "global": False,
#             "spoc_list": [{"spoc_id": 1, "spoc_name": "Local Spoc"}],
#             "region": 1
#         }
#     ]

#     response = client.post('/api/affiliate/add_address', json=payload)
#     json_data = response.get_json()
#     assert response.status_code == 401
#     assert json_data['success'] == -1
#     assert json_data['result'] == 'FAILURE'

# def test_add_address_affiliate_rep_not_found(client, auth_token_factory):
#     # JWT for a rep_id that does not exist
#     fake_rep_id = 99999
#     headers = auth_token_factory(fake_rep_id)

#     payload = [
#         {
#             "address": "123 Local Street",
#             "lat": "22.5726",
#             "long": "88.3639",
#             "nickname": "Home",
#             "global": False,
#             "spoc_list": [{"spoc_id": 1, "spoc_name": "Local Spoc"}],
#             "region": 1
#         }
#     ]

#     response = client.post(
#         '/api/affiliate/add_address',
#         headers=headers,
#         json=payload
#     )

#     json_data = response.get_json()

#     assert response.status_code == 404
#     assert json_data['success'] == 0
#     assert json_data['message'] == "Affiliate representative not found"

# def test_add_address_success(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     payload = [
#         {
#             "address": "123 Local Street",
#             "lat": "22.5726",
#             "long": "88.3639",
#             "nickname": "Home",
#             "global": False,
#             "spoc_list": [{"spoc_id": 1, "spoc_name": "Local Spoc"}],
#             "region": 1
#         },
#         {
#             "address": "456 Global Plaza",
#             "lat": "28.7041",
#             "long": "77.1025",
#             "nickname": "HQ",
#             "global": True,
#             "spoc_list": [{"spoc_id": 2, "spoc_name": "Global Spoc"}],
#             "region": 1
#         }
#     ]

#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 200
#     assert res.json['success'] == 1

# def test_add_address_invalid_payload_type(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     res = client.post('/api/affiliate/add_address', headers=headers, json={"address": "Invalid"})
#     assert res.status_code == 400
#     assert res.json['success'] == -1

# def test_add_address_region_missing(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     payload = [{
#         "address": "Test Address",
#         "lat": "22.0",
#         "long": "88.0",
#         "nickname": "TestNick",
#         "global": False,
#         "spoc_list": []
#     }]
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['message'] == 'Region is required'

# def test_add_address_invalid_region_type(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     payload = [{
#         "address": "Test Address",
#         "lat": "22.0",
#         "long": "88.0",
#         "nickname": "TestNick",
#         "global": False,
#         "spoc_list": [],
#         "region": "abc"
#     }]
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['message'] == 'Region must be an integer'

# def test_add_address_missing_fields(client, affiliate_rep_login):
#     headers, aff_rep = affiliate_rep_login

#     payload = [{
#         "lat": "22.0",
#         "long": "88.0",
#         "nickname": "",
#         "global": False,
#         "spoc_list": [],
#         "address": '',
#         "region": 1
#     }]
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['message'].startswith("Missing 'address' or 'nickname'")


# def test_add_address_duplicate_global_nickname(client, affiliate_rep_login):
#     headers, aff_rep = affiliate_rep_login
#     aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()

#     # Create an existing global address with the same nickname
#     existing = AffiliateAddress("Existing Addr", "TestNick", 22.0, 88.0, AffiliateAddress.GLOBAL_ADDRESS, aff_rep.id, aff_rep.affiliate_id, 1)
#     db.session.add(existing)
#     db.session.commit()

#     payload = [{
#         "address": "New Address",
#         "lat": "22.0",
#         "long": "88.0",
#         "nickname": "TestNick",
#         "global": True,
#         "spoc_list": [],
#         "region": 1
#     }]
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['message'] == 'TestNick is globally registered'

# def test_add_address_spoc_invalid(client, affiliate_rep_login):
#     headers, aff_rep = affiliate_rep_login
#     payload = [{
#         "address": "Some Addr",
#         "lat": "22.0",
#         "long": "88.0",
#         "nickname": "Nick1",
#         "global": False,
#         "region": 1,
#         "spoc_list": [{"spoc_id": 2, "spoc_name": ""}],  # Missing spoc_id
#     }]
    
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
    
#     assert res.status_code == 400
#     assert res.json['success'] == -5
#     assert "Missing 'id' or 'spoc_name'" in res.json['message']

# def test_add_address_partial_spoc_invalid_entry(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     payload = [
#         {
#             "address": "Valid Address",
#             "lat": "12.0",
#             "long": "77.0",
#             "nickname": "NickValid",
#             "global": False,
#             "spoc_list": [{"spoc_id": 10, "spoc_name": "Valid Spoc"}],
#             "region": 1
#         },
#         {
#             "address": "Invalid Address",
#             "lat": "11.0",
#             "long": "76.0",
#             "nickname": "NickInvalid",
#             "global": False,
#             "spoc_list": [{"spoc_id": None, "spoc_name": "Missing ID"}],
#             "region": 1
#         }
#     ]

#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['success'] == -5
#     assert "Missing 'id' or 'spoc_name'" in res.json['message']

# def test_add_address_duplicate_local_nickname(client, affiliate_rep_login):
#     headers, aff_rep = affiliate_rep_login
#     aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()

#     # Add existing local address
#     existing = AffiliateAddress("Local Address", "LocalNick", 10.0, 20.0, AffiliateAddress.LOCAL_ADDRESS, aff_rep.id, aff_rep.affiliate_id, 1)
#     db.session.add(existing)
#     db.session.commit()

#     payload = [{
#         "address": "New Local Address",
#         "lat": "10.1",
#         "long": "20.1",
#         "nickname": "LocalNick",
#         "global": False,
#         "spoc_list": [],
#         "region": 1
#     }]
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['message'] == 'LocalNick is locally registered'


# def test_add_address_empty_payload(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login
#     res = client.post('/api/affiliate/add_address', headers=headers, json=[])
#     assert res.status_code == 200
#     assert res.json['success'] == 1

# def test_add_address_invalid_lat_long_type(client, affiliate_rep_login):
#     headers, _ = affiliate_rep_login

#     payload = [{
#         "address": "Location X",
#         "lat": "not-a-float",
#         "long": "88.0",
#         "nickname": "NickX",
#         "global": False,
#         "spoc_list": [],
#         "region": 1
#     }]

#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 500
#     assert res.json['success'] == 0
#     assert "could not convert string to float" in res.json['message']

# def test_add_address_nickname_case_insensitive(client, affiliate_rep_login):
#     headers, aff_rep = affiliate_rep_login
#     aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()

#     existing = AffiliateAddress("Some Addr", "NickCase", 11.0, 22.0, AffiliateAddress.LOCAL_ADDRESS, aff_rep.id, aff_rep.affiliate_id, 1)
#     db.session.add(existing)
#     db.session.commit()

#     payload = [{
#         "address": "New Addr",
#         "lat": "11.5",
#         "long": "22.5",
#         "nickname": "nickcase",  # same as existing but lowercase
#         "global": False,
#         "spoc_list": [],
#         "region": 1
#     }]
#     res = client.post('/api/affiliate/add_address', headers=headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['message'] == 'nickcase is locally registered'


# """ Test cases for api: /api/affiliate/update_address """

# def test_update_address_invalid_jwt(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     payload = {
#             "id": 1,
#             "address": "123 Local Street",
#             "lat": "22.5726",
#             "long": "88.3639",
#             "nickname": "Home",
#             "global": False,
#             "spoc_list": [{"spoc_id": 1, "spoc_name": "Local Spoc"}],
#             "region": 1
#         }

#     response = client.put('/api/affiliate/update_address', json=payload)
#     json_data = response.get_json()
#     assert response.status_code == 401
#     assert json_data['success'] == -1
#     assert json_data['result'] == 'FAILURE'

# def test_update_address_affiliate_rep_not_found(client, auth_token_factory):
#     # JWT for a rep_id that does not exist
#     fake_rep_id = 99999
#     headers = auth_token_factory(fake_rep_id)

#     payload = {
#             "id": 1,
#             "address": "123 Local Street",
#             "lat": "22.5726",
#             "long": "88.3639",
#             "nickname": "Home",
#             "global": False,
#             "spoc_list": [{"spoc_id": 1, "spoc_name": "Local Spoc"}],
#             "region": 1
#         }

#     response = client.put(
#         '/api/affiliate/update_address',
#         headers=headers,
#         json=payload
#     )

#     json_data = response.get_json()

#     assert response.status_code == 404
#     assert json_data['success'] == 0
#     assert json_data['message'] == "Affiliate representative not found"


# def test_update_address_affiliate_address_not_found(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login

#     payload = {
#             "id": 1,
#             "address": "123 Local Street",
#             "lat": "22.5726",
#             "long": "88.3639",
#             "nickname": "Home",
#             "global": False,
#             "spoc_list": [{"spoc_id": 1, "spoc_name": "Local Spoc"}],
#             "region": 1
#         }

#     response = client.put(
#         '/api/affiliate/update_address',
#         headers=auth_headers,
#         json=payload
#     )

#     json_data = response.get_json()

#     assert response.status_code == 404
#     assert json_data['success'] == -1
#     assert json_data['message'] == "Address not found"

# def test_update_address_nickname_conflict_on_global_conversion(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     addr1 = AffiliateAddress('Addr', 'HQ', 0, 0, AffiliateAddress.GLOBAL_ADDRESS, rep.id, rep.affiliate_id,
#                             region=1)
#     addr2 = AffiliateAddress('Other', 'HQ', 1, 1, AffiliateAddress.LOCAL_ADDRESS, rep.id, rep.affiliate_id,
#                             region=1)

#     db.session.add_all([addr1, addr2])
#     db.session.commit()

#     payload = {
#         'id': addr2.add_id, 
#         'address': 'Updated Addr', 
#         'lat': 12.0, 
#         'long': 77.0, 
#         'nickname': 'HQ',
#         'global': True, 
#         'spoc_list': [], 
#         'region': 2
#     }

#     res = client.put('/api/affiliate/update_address', headers=auth_headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['success'] == -2
#     assert 'exists globally' in res.json['message']


# def test_update_address_nickname_conflict_on_local(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     addr1 = AffiliateAddress('Addr', 'HQ', 0, 0, AffiliateAddress.GLOBAL_ADDRESS, rep.id, rep.affiliate_id,
#                             region=1)
#     addr2 = AffiliateAddress('Other', 'HQ', 1, 1, AffiliateAddress.LOCAL_ADDRESS, rep.id, rep.affiliate_id,
#                             region=1)

#     db.session.add_all([addr1, addr2])
#     db.session.commit()

#     payload = {
#         'id': addr1.add_id, 
#         'address': 'Addr',
#         'lat': 12.0, 
#         'long': 77.0, 
#         'nickname': 'HQ',
#         'global': False, 
#         'spoc_list': [], 
#         'region': 2
#     }

#     res = client.put('/api/affiliate/update_address', headers=auth_headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['success'] == -3
#     assert 'exists locally' in res.json['message']

# def test_successful_address_update_with_spoc(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     addr = AffiliateAddress('Old Addr', 'Warehouse', 0, 0, AffiliateAddress.LOCAL_ADDRESS, rep.id, rep.affiliate_id,
#                             region=1)
#     db.session.add(addr)
#     db.session.commit()

#     payload = {
#         'id': addr.add_id,
#         'address': 'New Warehouse Address',
#         'lat': 13.0,
#         'long': 80.0,
#         'nickname': 'Warehouse Updated',
#         'global': False,
#         'spoc_list': [{'spoc_id': '1', 'spoc_name': 'John'}],
#         'region': 1
#     }

#     res = client.put('/api/affiliate/update_address', headers=auth_headers, json=payload)
#     json_data = res.get_json()
#     assert res.status_code == 200
#     assert res.json['success'] == 1

# def test_update_address_spoc_missing_fields(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     addr = AffiliateAddress('Old Addr', 'Warehouse', 0, 0, AffiliateAddress.LOCAL_ADDRESS, rep.id, rep.affiliate_id,
#                             region=1)
#     db.session.add(addr)
#     db.session.commit()

#     payload = {
#         'id': addr.add_id,
#         'address': 'Office Addr',
#         'lat': 11.0,
#         'long': 76.0,
#         'nickname': 'Office',
#         'global': False,
#         'spoc_list': [{'spoc_id': '1', 'spoc_name': ''}],  # Missing 'spoc_name'
#         'region': 4
#     }

#     res = client.put('/api/affiliate/update_address', headers=auth_headers, json=payload)
#     assert res.status_code == 400
#     assert res.json['success'] == -4

# """ Test cases for api: /api/affiliate/delete_address """
    
# # Test case: Successful deletion of an existing address
# def test_delete_address_success(client, affiliate_rep_login):
#     auth_headers, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
#     addr = AffiliateAddress('Addr to delete', 'DelAddr', 10.0, 20.0, AffiliateAddress.LOCAL_ADDRESS,
#                             rep.id, rep.affiliate_id, region=1)
#     db.session.add(addr)
#     db.session.commit()

#     form_data = {
#         'address_id': addr.add_id
#     }

#     res = client.delete('/api/affiliate/delete_address',
#                         headers=auth_headers,
#                         data=form_data,
#                         content_type='multipart/form-data')

#     assert res.status_code == 200
#     assert res.json['success'] == 1
#     assert 'deleted' in res.json['message'].lower()


# # Test case: Missing address_id field
# def test_delete_address_missing_id(client, affiliate_rep_login):
#     auth_headers, _ = affiliate_rep_login

#     res = client.delete('/api/affiliate/delete_address',
#                         headers=auth_headers,
#                         data={},
#                         content_type='multipart/form-data')

#     assert res.status_code == 400
#     assert res.json['success'] == 0
#     assert 'missing' in res.json['message'].lower()


# # Test case: Address not found for given id
# def test_delete_address_not_found(client, affiliate_rep_login):
#     auth_headers, _ = affiliate_rep_login

#     form_data = {
#         'address_id': 99999
#     }

#     res = client.delete('/api/affiliate/delete_address',
#                         headers=auth_headers,
#                         data=form_data,
#                         content_type='multipart/form-data')

#     assert res.status_code == 404
#     assert res.json['success'] == -1
#     assert 'not found' in res.json['message'].lower()


# # Test case: Unauthorized request (no JWT)
# def test_delete_address_unauthorized(client):

#     form_data = {
#         'address_id': 1
#     }
#     response = client.delete('/api/affiliate/delete_address',
#                         data=form_data,
#                         content_type='multipart/form-data')

#     assert response.status_code == 401
#     json_data = response.get_json()
#     assert json_data['success'] == -1
#     assert json_data['result'] == 'FAILURE'


# # Test case: Internal server error (simulate by passing invalid address_id)
# def test_delete_address_invalid_id_format(client, affiliate_rep_login):
#     auth_headers, _ = affiliate_rep_login

#     form_data = {
#         'address_id': 'invalid_id'  # Non-integer value to trigger ValueError
#     }

#     res = client.delete('/api/affiliate/delete_address',
#                         headers=auth_headers,
#                         data=form_data,
#                         content_type='multipart/form-data')

#     assert res.status_code == 400
#     assert res.json['success'] == 0
#     assert 'Invalid address id' in res.json['message']


# """ Test cases for api: /api/affiliate/address_list """

# # Helper to create address
# @pytest.fixture
# def setup_addresses(affiliate_rep_login):
#     _, rep = affiliate_rep_login
#     rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()

#     addr1 = AffiliateAddress(
#         address='Local Addr', nickname='HQ', latitude=12.0, longitude=77.0,
#         type=AffiliateAddress.LOCAL_ADDRESS, rep_id=rep.id, aff_id=rep.affiliate_id, region=0
#     )
#     addr2 = AffiliateAddress(
#         address='Global Addr', nickname='HQ_GLOBAL', latitude=15.0, longitude=80.0,
#         type=AffiliateAddress.GLOBAL_ADDRESS, rep_id=rep.id, aff_id=rep.affiliate_id, region=0
#     )
#     db.session.add_all([addr1, addr2])
#     db.session.commit()

#     spoc = AddressSpoc(address_id=addr1.add_id, spoc_id=1, name='John')
#     db.session.add(spoc)
#     db.session.commit()

#     return addr1, addr2


# def test_get_all_addresses_success(client, affiliate_rep_login, setup_addresses):
#     auth_headers, _ = affiliate_rep_login
#     res = client.get('/api/affiliate/address_list', headers=auth_headers)

#     assert res.status_code == 200
#     assert res.json['success'] == 1
#     assert isinstance(res.json['addresses'], list)

#     # Check that both local and global addresses are returned
#     returned_nicknames = [a['nickname'] for a in res.json['addresses']]
#     assert 'HQ' in returned_nicknames
#     assert 'HQ_GLOBAL' in returned_nicknames


def test_get_all_addresses_unauthorized(client):
    response = client.get('/api/affiliate/address_list')  # No headers
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'



def test_get_all_addresses_internal_error(monkeypatch, client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login

    def fake_query(*args, **kwargs):
        raise Exception("DB failure")

    with monkeypatch.context() as m:
        m.setattr('models.db.session.query', fake_query)

        res = client.get('/api/affiliate/address_list', headers=auth_headers)
        assert res.status_code == 500
        assert res.json['success'] == 0
        assert 'error' in res.json
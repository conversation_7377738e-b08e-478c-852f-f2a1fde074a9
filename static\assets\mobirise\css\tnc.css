.is-builder .animated {
  -webkit-animation-name: none !important;
  animation-name: none !important;
}
html {
  position: relative;
  min-height: 100%;
}
.mbr-embedded-video {
  position: relative;
}
.mbr-background-video,
.mbr-background-video-preview {
  bottom: 0;
  left: 0;
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
}
.mbr-parallax-background,
.mbr-background {
  background-attachment: local !important;
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: cover !important;
}
.mbr-hidden-scrollbar .mbr-parallax-background {
  background-size: auto 130%;
}
.mobile .mbr-parallax-background {
  background-attachment: fixed !important;
}
.mbr-background {
  background-attachment: fixed !important;
}
.__ {
  position: relative;
  width: 100%;
}
.mbr-navbar:before {
  content: "";
  display: block;
}
.mbr-navbar__brand-link:after,
.mbr-navbar__brand-img {
  max-height: 100px;
  height: 100%;
}
.mbr-navbar:before,
.mbr-navbar__container {
  height: 98px;
}
.mbr-navbar--ss .mbr-navbar__brand-link:after,
.mbr-navbar--ss .mbr-navbar__brand-img {
  height: 74px;
}
.mbr-navbar--ss:before,
.mbr-navbar--ss .mbr-navbar__container {
  height: 98px;
}
.mbr-navbar--xs .mbr-navbar__brand-link:after,
.mbr-navbar--xs .mbr-navbar__brand-img {
  height: 32px;
}
.mbr-navbar--xs:before,
.mbr-navbar--xs .mbr-navbar__container {
  height: 56px;
}
.mbr-navbar--s .mbr-navbar__brand-link:after,
.mbr-navbar--s .mbr-navbar__brand-img {
  height: 48px;
}
.mbr-navbar--s:before,
.mbr-navbar--s .mbr-navbar__container {
  height: 72px;
}
.mbr-navbar--m .mbr-navbar__brand-link:after,
.mbr-navbar--m .mbr-navbar__brand-img {
  height: 64px;
}
.mbr-navbar--m:before,
.mbr-navbar--m .mbr-navbar__container {
  height: 88px;
}
.mbr-navbar--l .mbr-navbar__brand-link:after,
.mbr-navbar--l .mbr-navbar__brand-img {
  height: 96px;
}
.mbr-navbar--l:before,
.mbr-navbar--l .mbr-navbar__container {
  height: 120px;
}
.mbr-navbar--xl .mbr-navbar__brand-link:after,
.mbr-navbar--xl .mbr-navbar__brand-img {
  height: 128px;
}
.mbr-navbar--xl:before,
.mbr-navbar--xl .mbr-navbar__container {
  height: 152px;
}
.mbr-navbar--short .mbr-navbar__brand-link:after,
.mbr-navbar--short .mbr-navbar__brand-img {
  height: 40px;
}
.mbr-navbar--short .mbr-brand__logo .mbr-iconfont{
  font-size: 40px;
}
.mbr-navbar--short:before,
.mbr-navbar--short .mbr-navbar__container {
  height: 64px;
}
.mbr-navbar--short .mbr-navbar__container {
  padding: 12px 0;
}
@media (max-width: 767px) {
  .mbr-navbar--short .mbr-navbar__brand-link:after,
  .mbr-navbar--short .mbr-navbar__brand-img {
    height: 31px;
  }
  .mbr-navbar--short:before,
  .mbr-navbar--short .mbr-navbar__container {
    height: 45px;
  }
  .mbr-navbar--short .mbr-navbar__container {
    padding: 7px 0;
  }
}
.mbr-navbar__brand-img {
  position: relative;
}
.mbr-navbar__brand-img,
.mbr-navbar__container,
.mbr-navbar__section {
  -webkit-transition: all 300ms ease-in-out 0s;
  -o-transition: all 300ms ease-in-out 0s;
  transition: all 300ms ease-in-out 0s;
}
.mbr-navbar__section {
  background: #ffffff;
  height: auto;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1000;
}
.mbr-navbar__container {
  display: table;
  padding: 12px 0;
  width: 100%;
}
.mbr-navbar__menu-box {
  display: table;
  width: 100%;
}
.mbr-navbar__menu-box--inline-left,
.mbr-navbar__menu-box--inline-center,
.mbr-navbar__menu-box--inline-right {
  display: block;
  text-align: left;
}
.mbr-navbar__menu-box--inline-center {
  text-align: center;
}
.mbr-navbar__menu-box--inline-right {
  text-align: right;
}
.mbr-navbar__column {
  display: table-cell;
  vertical-align: middle;
}
.mbr-navbar__column--xxs {
  width: 1%;
}
.mbr-navbar__column--xs {
  width: 10%;
}
.mbr-navbar__column--s {
  width: 20%;
}
.mbr-navbar__column--m {
  width: 30%;
}
.mbr-navbar__column--l {
  width: 40%;
}
.mbr-navbar__column--xl {
  width: 50%;
}
.mbr-navbar__menu-box--inline-left .mbr-navbar__column,
.mbr-navbar__menu-box--inline-center .mbr-navbar__column,
.mbr-navbar__menu-box--inline-right .mbr-navbar__column {
  display: inline-block;
}
.mbr-navbar__items {
  float: left;
  padding-left: 0px;
  position: relative;
  left: -20px;
}
.mbr-navbar__items--right {
  float: right;
  left: 0;
}
.float-left {
  float: left;
}
.mbr-navbar__item {
  display: block;
  float: left;
  position: relative;
}
.mbr-navbar__hamburger {
  display: none;
  margin-top: -11px;
  position: absolute;
  right: 0;
  top: 50%;
  z-index: 10000;
}
.mbr-navbar--collapsed .mbr-navbar__container {
  position: relative;
}
.mbr-navbar--collapsed .mbr-navbar__column {
  display: block;
  width: 100%;
}
.mbr-navbar--collapsed .mbr-navbar__items--right {
  padding-top: 13px;
}
.mbr-navbar--collapsed .mbr-navbar__menu {
  background: rgba(0, 0, 0, 0.9);
  display: none;
  height: 100%;
  left: 0;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 9999;
}
.mbr-navbar--collapsed .mbr-navbar__menu-box {
  display: table-cell;
  vertical-align: middle;
}
.mbr-navbar--collapsed .mbr-navbar__items {
  float: none;
}
.mbr-navbar--collapsed .mbr-navbar__item {
  float: none;
}
.mbr-navbar--collapsed .mbr-navbar__hamburger {
  display: block;
}
.mbr-navbar--collapsed.mbr-navbar--open .mbr-navbar__menu {
  display: table;
}
.mbr-navbar--collapsed.mbr-navbar--open:not(.mbr-navbar--sticky) .mbr-navbar__section {
  background: none;
  position: fixed;
}
.mbr-navbar--collapsed.mbr-navbar--open .mbr-navbar__brand {
  visibility: hidden;
}
.mbr-navbar--collapsed.mbr-navbar--sticky.mbr-navbar--open .mbr-navbar__brand {
  visibility: visible;
}
.mbr-navbar--collapsed.mbr-navbar--open .mbr-navbar__brand-img,
.mbr-navbar--collapsed.mbr-navbar--open .mbr-navbar__container {
  -webkit-transition: none;
  -o-transition: none;
  transition: none;
}
.mbr-navbar--freeze.mbr-navbar--collapsed.mbr-navbar--open .mbr-navbar__hamburger,
.mbr-navbar--freeze.mbr-navbar--collapsed.mbr-navbar--open .mbr-navbar__hamburger:hover {
  color: #fff !important;
}
.mbr-navbar--sticky .mbr-navbar__section {
  position: fixed;
}
.mbr-navbar--absolute {
  position: absolute;
}
/* fix for popup menu conflict */
@media (max-width: 480px) {
  
  .mbr-navbar--absolute.mbr-navbar[id^=menu-] {
    position: absolute;
  }
}
.mbr-navbar--transparent .mbr-navbar__section {
  /**background: none;**/
}
.mbr-navbar--stuck .mbr-navbar__section,
.mbr-navbar--relative .mbr-navbar__section {
  background: #ffffff;
}
@media (max-width: 991px) {
  .mbr-navbar--auto-collapse .mbr-navbar__container {
    position: relative;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__column {
    display: block;
    width: 100%;
  }
  .mbr-navbar__column {
    max-height: 100vh;
    overflow-x: hidden;
    overflow-y:auto;
  }
  .mbr-navbar__column::-webkit-scrollbar {
    display:none;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__items--right {
    padding-top: 13px;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__menu {
    background: rgba(0, 0, 0, 0.7);
    display: none;
    height: 100%;
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 9999;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__menu-box {
    display: table-cell;
    vertical-align: middle;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__items {
    float: none;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__item {
    float: none;
  }
  .mbr-navbar--auto-collapse .mbr-navbar__hamburger {
    display: block;
  }
  .mbr-navbar--auto-collapse.mbr-navbar--open .mbr-navbar__menu {
    display: table;
  }
  .mbr-navbar--auto-collapse.mbr-navbar--open:not(.mbr-navbar--sticky) .mbr-navbar__section {
    background: none;
    position: fixed;
  }
  .mbr-navbar--auto-collapse.mbr-navbar--open .mbr-navbar__brand {
    visibility: hidden;
  }
  .mbr-navbar--auto-collapse.mbr-navbar--sticky.mbr-navbar--open .mbr-navbar__brand {
    visibility: visible;
  }
  .mbr-navbar--auto-collapse.mbr-navbar--open .mbr-navbar__brand-img,
  .mbr-navbar--auto-collapse.mbr-navbar--open .mbr-navbar__container {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
}
.mbr-after-navbar:before {
  content: "";
  display: block;
  height: 98px;
}
.mbr-hamburger {
  cursor: pointer;
  height: 23px;
  width: 30px;
}
.mbr-hamburger:focus {
  outline: none;
}
.mbr-hamburger__line,
.mbr-hamburger__line:before,
.mbr-hamburger__line:after {
  content: "";
  position: absolute;
  display: block;
  height: 1px;
  cursor: pointer;
}
.mbr-hamburger__line,
.mbr-hamburger__line:before,
.mbr-hamburger__line:after {
  width: 30px;
  border-bottom: 5px solid;
  top: 9px;
}
.mbr-hamburger__line:before {
  top: -9px;
}
.mbr-hamburger__line:after {
  top: 9px;
}
.mbr-hamburger__line,
.mbr-hamburger__line:before,
.mbr-hamburger__line:after {
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.mbr-hamburger--open .mbr-hamburger__line {
  border-color: transparent;
}
.mbr-hamburger--open .mbr-hamburger__line:before,
.mbr-hamburger--open .mbr-hamburger__line:after {
  top: 0;
}
.mbr-hamburger--open .mbr-hamburger__line:before {
  -ms-transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}
.mbr-hamburger--open .mbr-hamburger__line:after {
  top: 10px;
  -ms-transform: translatey(-10px) rotate(-45deg);
  -webkit-transform: translatey(-10px) rotate(-45deg);
  transform: translatey(-10px) rotate(-45deg);
}
@media (max-width: 767px) {
  .mbr-hamburger {
    height: 23px;
    width: 27px;
  }
  .mbr-hamburger__line,
  .mbr-hamburger__line:before,
  .mbr-hamburger__line:after {
    width: 27px;
    border-bottom: 4px solid;
    top: 9px;
  }
  .mbr-hamburger__line:before {
    top: -9px;
  }
  .mbr-hamburger__line:after {
    top: 9px;
  }
}

.navbar-dropdown .hamburger-icon {
  content: "";
  width: 16px;
  -webkit-box-shadow: 0 -6px 0 1px,0 0 0 1px,0 6px 0 1px;
  -moz-box-shadow: 0 -6px 0 1px,0 0 0 1px,0 6px 0 1px;
  box-shadow: 0 -6px 0 1px,0 0 0 1px,0 6px 0 1px;
}

.mbr-brand {
  display: block;
  float: left;
  position: relative;
}
.mbr-brand,
.mbr-brand:hover {
  text-decoration: none;
}
.mbr-brand__name {
  display: block;
  font-weight: bold;
  margin-top: 5px;
  text-align: center;
}
.mbr-brand__name,
.mbr-brand__name:hover {
  text-decoration: none;
}
.mbr-brand--inline {
  display: table;
}
.mbr-brand--inline:after {
  content: "";
  display: table-cell;
  width: 1px;
}
.mbr-brand--inline .mbr-brand__logo,
.mbr-brand--inline .mbr-brand__name {
  display: table-cell;
  vertical-align: middle;
}
.mbr-brand--inline .mbr-brand__logo {
  padding-right: 10px;
}
.mbr-brand--inline .mbr-brand__name {
  margin: 0;
  text-align: left;
}
.mbr-form {
  display: table;
  margin-top: -13px;
  position: relative;
  top: 14px;
  width: 100%;
}
.mbr-form__left,
.mbr-form__right {
  display: table-cell;
  vertical-align: top;
}
.mbr-form__left {
  padding-right: 3px;
}
.mbr-form__right {
  width: 1px;
}
@media (max-width: 530px) {
  .mbr-form {
    display: block;
    margin-top: -27px;
    position: relative;
    top: 26px;
  }
  .mbr-form__left,
  .mbr-form__right {
    display: block;
  }
  .mbr-form__left {
    margin-bottom: 12px;
    padding-right: 0;
  }
  .mbr-form__right {
    width: 100%;
  }
}
.mbr-section {
  padding: 0 20px;
}
.mbr-section--no-padding {
  padding: 0;
}
.mbr-section--relative {
  position: relative;
}
.mbr-section--fixed-size {
  overflow: hidden;
}
.mbr-section--full-height {
  height: 100vh;
}
.mbr-section--full-height.mbr-after-navbar:before {
  display: none;
}
.mbr-section--bg-adapted {
  background-attachment: scroll;
  background-position: 50% 0;
  background-repeat: no-repeat;
  background-size: cover;
}
.mbr-section--gray {
  background-color: #444444;
}
.mbr-section--light-gray {
  background-color: #f0f0f0;
}
.mbr-section--dark-gray {
  background-color: #3c3c3c;
}
.mbr-section__container {
  padding: 0;
  position: relative;
  z-index: 3;
}
.mbr-section__container--center {
  text-align: center;
}
.mbr-section__container--std-padding {
  padding: 93px 0;
}
.mbr-section__container--std-top-padding {
  padding-top: 93px;
}
.mbr-section__container--std-bot-padding {
  padding-bottom: 93px;
}
.mbr-section__container--sm-padding {
  padding: 41px 0;
}
.mbr-section__container--sm-top-padding {
  padding-top: 41px;
}
.mbr-section__container--sm-bot-padding {
  padding-bottom: 38px;
}
.mbr-section__container--isolated {
  padding-bottom: 93px;
  padding-top: 93px;
}
.mbr-section__container--first {
  padding-top: 93px;
  padding-bottom: 41px;
}
.mbr-section__container--middle {
  padding-bottom: 20px;
}
.mbr-section__container--last {
  padding-bottom: 93px;
}
.mbr-section__row {
  margin-left: -24px;
  margin-right: -24px;
}
.mbr-section__col {
  overflow: hidden;
  padding-top: 20px;
  margin-left: 10px;
  margin-right: 10px;
  padding-bottom: 12px;
  border-radius: 24px;
}
.mbr-section__left {
  padding-right: 40px;
}
.mbr-section__right {
  padding-left: 15px;
}
.mbr-section__header {
  line-height: 1.5em;
  margin: -10px 0 0;
  text-align: center;
}
@media (min-width: 768px) {
  .mbr-section--short-paddings .mbr-section__container--std-padding {
    padding: 59px 0;
  }
  .mbr-section--short-paddings .mbr-section__container--std-top-padding {
    padding-top: 59px;
  }
  .mbr-section--short-paddings .mbr-section__container--std-bot-padding {
    padding-bottom: 59px;
  }
  .mbr-section--short-paddings .mbr-section__container--sm-padding {
    padding: 41px 0;
  }
  .mbr-section--short-paddings .mbr-section__container--sm-top-padding {
    padding-top: 41px;
  }
  .mbr-section--short-paddings .mbr-section__container--sm-bot-padding {
    padding-bottom: 41px;
  }
  .mbr-section--short-paddings .mbr-section__container--isolated {
    padding-bottom: 59px;
    padding-top: 59px;
  }
  .mbr-section--short-paddings .mbr-section__container--first {
    padding-top: 59px;
    padding-bottom: 41px;
  }
  .mbr-section--short-paddings .mbr-section__container--middle {
    padding-bottom: 41px;
  }
  .mbr-section--short-paddings .mbr-section__container--last {
    padding-bottom: 59px;
  }
}
@media (max-width: 767px) {
  .mbr-section__left {
    padding-right: 15px;
  }
  .mbr-section__right {
    padding-left: 15px;
    padding-top: 51px;
  }
}
.mbr-arrow {
  bottom: 71px;
  left: 0;
  line-height: 1px;
  padding: 0 20px;
  position: absolute;
  width: 100%;
  z-index: 3;
}
.mbr-arrow__link {
  display: inline-block;
  font-size: 26px;
}
.mbr-arrow__link,
.mbr-arrow__link:hover,
.mbr-arrow__link:focus {
  color: #fff;
}
.mbr-arrow--floating .mbr-arrow__link {
  -webkit-animation: floating-arrow 1.6s infinite ease-in-out 0s;
  -o-animation: floating-arrow 1.6s infinite ease-in-out 0s;
  animation: floating-arrow 1.6s infinite ease-in-out 0s;
}
@-webkit-keyframes floating-arrow {
  from {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  65% {
    -webkit-transform: translateY(11px);
    transform: translateY(11px);
  }
  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@-o-keyframes floating-arrow {
  from {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  65% {
    -webkit-transform: translateY(11px);
    transform: translateY(11px);
  }
  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
@keyframes floating-arrow {
  from {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
  65% {
    -webkit-transform: translateY(11px);
    transform: translateY(11px);
  }
  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
  }
}
.mbr-arrow--dark .mbr-arrow__link,
.mbr-arrow--dark .mbr-arrow__link:hover,
.mbr-arrow--dark .mbr-arrow__link:focus {
  color: #252525;
}
@media (max-width: 767px) {
  .mbr-arrow {
    bottom: 41px;
  }
}
@media (max-width: 320px) {
  .mbr-arrow {
    bottom: 21px;
    text-align: center;
  }
}
@media all and (device-width: 320px) and (device-height: 568px) and (orientation: portrait) {
  .mbr-arrow {
    bottom: 31px;
  }
}
.mbr-box {
  display: table;
  width: 100%;
}
.mbr-box--fixed {
  table-layout: fixed;
}
.mbr-box--stretched {
  height: 100%;
}
.mbr-box__magnet {
  display: table-cell;
  float: none;
  height: 100%;
  margin-bottom: 0;
  margin-top: 0;
  text-align: center;
  vertical-align: middle;
}
.mbr-box__magnet--sm-padding {
  padding: 41px 0;
}
.mbr-box__magnet--top-left,
.mbr-box__magnet--top-center,
.mbr-box__magnet--top-right {
  vertical-align: top;
}
.mbr-box__magnet--bottom-left,
.mbr-box__magnet--bottom-center,
.mbr-box__magnet--bottom-right {
  vertical-align: bottom;
}
.mbr-box__magnet--top-left,
.mbr-box__magnet--center-left,
.mbr-box__magnet--bottom-left {
  text-align: left;
}
.mbr-box__magnet--top-right,
.mbr-box__magnet--center-right,
.mbr-box__magnet--bottom-right {
  text-align: right;
}
.mbr-box__container {
  height: 50%;
}
@media (max-width: 767px) {
  .mbr-box__container {
    height: 100%;
  }
  .mbr-box--adapted {
    display: block;
  }
  .mbr-box--adapted > .mbr-box__magnet {
    display: block;
    height: auto;
  }
}
.mbr-overlay {
  background: #222;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}
.mbr-google-map__marker {
  color: #252525;
  display: none;
  margin: 0;
}
.mbr-google-map--loaded .mbr-google-map__marker {
  display: block;
}
.mbr-hero {
  color: #fff;
  position: relative;
}
.mbr-hero__text {
  font-size: 40px;
  font-weight: bold;
  left: -2px;
  letter-spacing: 2px;
  line-height: 50px;
  margin: -18px 0 1px 0;
  padding-bottom: 10px;
  position: relative;
  top: 8px;
}
.mbr-hero__subtext {
  font-size: 21px;
  line-height: 29px;
  margin: -32px 0 3px 0;
  padding: 0 0 41px 0;
  position: relative;
  top: 6px;
}
.mbr-figure {
  display: inline-block;
  /*line-height: 1px;*/
  margin: 0;
  max-width: 83%;
  overflow: hidden;
  position: relative;
  border: 8px solid;
  border-radius: 50%;
  border-color: white;
}
.mbr-figure--no-bg {
  background: none;
}
.mbr-figure--full-width {
  display: block;
  width: 100%;
}
.mbr-figure.mbr-after-navbar:before {
  display: none;
}
.mbr-figure--full-width iframe,
.mbr-figure--full-width .mbr-figure__img,
.mbr-figure--full-width .mbr-figure__map {
  width: 100%;
}
.mbr-figure iframe,
.mbr-figure__img,
.mbr-figure__map {
  max-width: 100%;
}
@-webkit-keyframes mapCircleLoading {
  from {
    -webkit-transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(359deg);
  }
}
@keyframes mapCircleLoading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(359deg);
  }
}
.mbr-figure__map {
  height: 400px;
  position: relative;
}
.mbr-figure__map--short {
  height: 300px;
}
.mbr-figure__map iframe {
  height: 100%;
  width: 100%;
}
.mbr-figure__map [data-state-details] {
  color: #6b6763;
  /*font-family: "Roboto", Helvetica, Arial, sans-serif;*/
  height: 1.5em;
  margin-top: -0.75em;
  padding-left: 20px;
  padding-right: 20px;
  position: absolute;
  text-align: center;
  top: 50%;
  width: 100%;
}
.mbr-figure__map[data-state] {
  background: #e9e5dc;
}
.mbr-figure__map[data-state="loading"] [data-state-details] {
  display: none;
}
.mbr-figure__map[data-state="loading"]::after {
  content: "";
  -webkit-animation: mapCircleLoading .6s infinite linear;
  animation: mapCircleLoading .6s infinite linear;
  border-radius: 50%;
  border: 6px rgba(255, 255, 255, 0.35) solid;
  border-top-color: #fff;
  height: 40px;
  left: 50%;
  margin-left: -20px;
  margin-top: -20px;
  position: absolute;
  top: 50%;
  width: 40px;
}
.mbr-figure__caption {
  background: rgba(0, 0, 0, 0.5);
  bottom: 0;
  color: #fff;
  display: block;
  font-size: 17px;
  left: 0;
  line-height: 1.3em;
  min-height: 53px;
  padding: 17px 20px;
  position: absolute;
  text-align: left;
  width: 100%;
}
.mbr-figure__caption--no-padding {
  padding: 17px 0;
}
.mbr-figure--wysiwyg .mbr-figure__caption a,
.mbr-figure--wysiwyg .mbr-figure__caption a:hover {
  color: inherit;
  text-decoration: underline;
}
.mbr-figure--caption-inside-top .mbr-figure__caption {
  bottom: auto;
  top: 0;
}
.mbr-figure--caption-outside-top .mbr-figure__caption,
.mbr-figure--caption-outside-bottom .mbr-figure__caption {
  background: none;
  position: relative;
}
.mbr-figure--no-bg.mbr-figure--caption-outside-top .mbr-figure__caption,
.mbr-figure--no-bg.mbr-figure--caption-outside-bottom .mbr-figure__caption {
  color: #252525;
}
.mbr-figure--no-bg.mbr-figure--caption-outside-top .mbr-figure__caption {
  margin-top: -3px;
  padding-top: 0;
}
.mbr-figure--no-bg.mbr-figure--caption-outside-bottom .mbr-figure__caption {
  margin-top: -2px;
  padding-bottom: 0;
  top: 2px;
}
.mbr-figure__caption--std-grid {
  background: none;
  z-index: 2;
  overflow: hidden;
}
@media (min-width: 768px) {
  .mbr-figure__caption--std-grid {
    width: 715px;
    left: 50%;
    margin-left: -357.5px;
    padding: 17px 0;
  }
}
@media (min-width: 992px) {
  .mbr-figure__caption--std-grid {
    width: 935px;
    margin-left: -467.5px;
  }
}
@media (min-width: 1200px) {
  .mbr-figure__caption--std-grid {
    width: 1150px;
    margin-left: -575px;
  }
}
.mbr-figure__caption--std-grid:before {
  bottom: 0;
  content: "";
  position: absolute;
  top: 0;
  width: 200%;
  z-index: -1;
  margin-left: -50%;
}
.mbr-figure--caption-inside-top .mbr-figure__caption--std-grid:before,
.mbr-figure--caption-inside-bottom .mbr-figure__caption--std-grid:before {
  background: rgba(0, 0, 0, 0.6);
}
.mbr-figure__caption-small {
  color: #ccc;
  display: block;
  font-size: 14px;
  line-height: 1.3em;
}
.mbr-figure--no-bg.mbr-figure--caption-outside-top .mbr-figure__caption-small,
.mbr-figure--no-bg.mbr-figure--caption-outside-bottom .mbr-figure__caption-small {
  color: #777;
}
@media (max-width: 767px) {
  .mbr-figure--adapted {
    display: block;
    width: 100%;
  }
  .mbr-figure--adapted iframe,
  .mbr-figure--adapted .mbr-figure__img,
  .mbr-figure--adapted .mbr-figure__map {
    width: 100%;
  }
  .mbr-figure--caption-inside-top .mbr-figure__caption,
  .mbr-figure--caption-inside-bottom .mbr-figure__caption {
    background: none;
    position: relative;
  }
  .mbr-figure--caption-inside-top .mbr-figure__caption--std-grid:before,
  .mbr-figure--caption-inside-bottom .mbr-figure__caption--std-grid:before {
    display: none;
  }
}
.mbr-reviews {
  list-style: none;
  margin: 0 -15px;
  padding: 3px 0 0 0;
}
.mbr-reviews__item {
  position: relative;
  margin-top: 39px;
}
.mbr-reviews__text {
  background: #fafafa;
  border-radius: 3px;
  border: 1px solid #ededed;
  color: #777;
  font-size: 16px;
  line-height: 26px;
  padding: 20px;
  position: relative;
}
.mbr-reviews__text:before {
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  width: 14px;
  height: 14px;
  background-color: #fafafa;
  border-color: #ededed;
  border-style: none solid solid none;
  border-width: 0 1px 1px 0;
  bottom: -8px;
  content: "";
  display: block;
  left: 50px;
  position: absolute;
}
.mbr-reviews__p {
  margin: 0;
}
.mbr-reviews__author {
  margin-top: 30px;
  padding-left: 102px;
  position: relative;
}
.mbr-reviews__author--short {
  margin-top: 27px;
  padding-left: 32px;
}
.mbr-reviews__author-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  left: 33px;
  position: absolute;
  top: 0;
}
.mbr-reviews__author-name {
  color: #777;
  font-size: 14px;
  font-weight: bold;
  position: relative;
  top: -3px;
}
.mbr-reviews__author-bio {
  color: #999;
  font-size: 12px;
}
@media (max-width: 767px) {
  .mbr-reviews__author {
    padding-bottom: 32px;
  }
  .mbr-reviews__author--short {
    padding-bottom: 1px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .mbr-reviews__item:nth-of-type(2n+1) {
    clear: left;
  }
}
@media (min-width: 992px) {
  .mbr-reviews__item:nth-of-type(3n+1) {
    clear: left;
  }
}
@media (max-width: 991px) {
  .mbr-header--reduce .mbr-header__text {
    padding-top: 1em;
    margin-top: -1em;
  }
}
.mbr-header {
  margin-top: -20px;
  padding: 0;
  position: relative;
  text-align: left;
  top: 10px;
}
.mbr-header--std-padding {
  padding-bottom: 41px;
}
.mbr-header--center {
  text-align: center;
}
.mbr-header__text {
  display: block;
  font-size: 25px;
  font-weight: bold;
  letter-spacing: 6px;
  line-height: 1.5em;
  margin: 0;
}
.mbr-header__subtext {
  color: #777;
  font-size: 14px;
  font-style: italic;
  letter-spacing: 1px;
  margin: 8px 0 7px 0;
}
.mbr-header--inline {
  margin-top: 0;
  padding: 41px 0 28px 0;
  top: 0;
}
.mbr-header--inline .mbr-header__text {
  letter-spacing: 4px;
  line-height: 1em;
  margin: 15px 0 0 0;
}
@media (max-width: 767px) {
  .mbr-header--inline {
    padding: 47px 0 38px 0;
  }
  .mbr-header--inline .mbr-header__text {
    display: block;
    margin: 0 0 38px 0;
  }
  .mbr-header--auto-align .mbr-header__text,
  .mbr-header--auto-align .mbr-header__subtext {
    left: 0;
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .mbr-header--reduce {
    margin-top: -5px;
    top: 2px;
  }
  .mbr-header--reduce .mbr-header__text {
    font-size: 16px;
    letter-spacing: 1px;
    line-height: 1.1em;
    padding-top: 0.4em;
    margin-top: -0.4em;
  }
}
.mbr-social-icons__icon {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-size: 29px;
  height: 56px;
  line-height: 61px;
  margin: 0 10px 13px 0;
  position: relative;
  text-align: center;
  width: 56px;
}
.mbr-social-icons__icon:hover {
  color: #fff;
}
.mbr-social-icons--style-1 .mbr-social-icons__icon:hover {
  background: #252525 !important;
}
.mbr-contacts {
  color: #9c9c9c;
  font-size: 14px;
  line-height: 1.7em;
  padding: 45px 0 46px;
}
.mbr-contacts__img {
  max-width: 100%;
  margin: 6px 0 5px 40px;
}
.mbr-contacts__img--left {
  margin-left: 0;
}
.mbr-contacts__text {
  margin: 0;
}
.mbr-contacts__header {
  color: #fff;
  font-size: 14px;
  letter-spacing: 1px;
  margin-bottom: 20px;
  margin-top: 3px;
}
.mbr-contacts__list {
  list-style: none;
  margin: 0;
  padding: 0;
}
@media (max-width: 767px) {
  .mbr-contacts__img {
    margin-bottom: 10px;
  }
  .mbr-contacts__header {
    margin-top: 20px;
    margin-bottom: 10px;
  }
  .mbr-contacts__column {
    margin-top: 37px;
  }
}
.mbr-footer {
  color: #9c9c9c;
  font-size: 13px;
  letter-spacing: 1px;
  line-height: 1.5em;
  padding: 37px 0 39px;
  word-spacing: 1px;
}
.mbr-footer__copyright {
  margin: 0;
}
.mbr-buttons {
  margin: -26px 0 13px 0;
  position: relative;
  text-align: left;
  top: 26px;
}
.mbr-buttons__btn,
.mbr-buttons__link {
  margin: 0 10px 13px 0;
  border-radius: 5px;
}
.mbr-buttons__btn,
.mbr-buttons__link,
.mbr-buttons__btn:hover,
.mbr-buttons__link:hover {
  text-decoration: none;
}
.mbr-buttons--top {
  top: 44px;
  margin-top: -62px;
}
.mbr-buttons--no-offset {
  margin-top: 0;
  top: 0;
}
.mbr-buttons--only-links {
  left: -20px;
}
.mbr-buttons--center {
  left: 5px;
  text-align: center;
}
.mbr-buttons--center.mbr-buttons--only-links {
  left: 0;
}
.mbr-buttons--right {
  text-align: right;
}
.mbr-buttons--right .mbr-buttons__btn,
.mbr-buttons--right .mbr-buttons__link {
  margin: 0 0 13px 10px;
}
.mbr-buttons--right.mbr-buttons--only-links {
  left: 20px;
}
.mbr-buttons--activated {
  left: 5px;
  text-align: center;
}
.mbr-buttons--activated .mbr-buttons__btn,
.mbr-buttons--activated .mbr-buttons__link {
  margin-left: 0;
  margin-right: 0;
}
.mbr-buttons--activated .mbr-buttons__link {
  font-size: 25px;
  padding: 10px 30px 2px;
}
.mbr-buttons--activated .mbr-buttons__btn {
  font-size: 15px;
  margin-top: 9px;
  padding: 15px 30px;
}
.mbr-buttons--freeze.mbr-buttons--activated .mbr-buttons__link {
  font-size: 25px !important;
}
.mbr-buttons--freeze.mbr-buttons--activated .mbr-buttons__link,
.mbr-buttons--freeze.mbr-buttons--activated .mbr-buttons__link:hover {
  color: #fff !important;
}
.mbr-buttons--freeze.mbr-buttons--activated .mbr-buttons__btn {
  font-size: 15px !important;
}
@media (max-width: 991px) {
  .mbr-buttons {
    top: 0;
  }
  form:not(.mbr-form) .mbr-buttons {
    top: 26px;
  }
  .mbr-buttons:first-child {
    margin-top: 0;
  }
  .mbr-buttons--active {
    left: 5px;
    text-align: center;
  }
  .mbr-buttons--active .mbr-buttons__btn,
  .mbr-buttons--active .mbr-buttons__link {
    margin-left: 0;
    margin-right: 0;
  }
  .mbr-buttons--right.mbr-buttons--only-links {
    left: 0;
  }
  .mbr-buttons--active .mbr-buttons__link {
    font-size: 25px;
    padding: 10px 30px 2px;
  }
  .mbr-buttons--active .mbr-buttons__btn {
    font-size: 15px;
    margin-top: 9px;
    padding: 15px 30px;
  }
  .mbr-buttons--freeze.mbr-buttons--active .mbr-buttons__link {
    font-size: 25px !important;
  }
  .mbr-buttons--freeze.mbr-buttons--active .mbr-buttons__link,
  .mbr-buttons--freeze.mbr-buttons--active .mbr-buttons__link:hover {
    color: #fff !important;
  }
  .mbr-buttons--freeze.mbr-buttons--active .mbr-buttons__btn {
    font-size: 15px !important;
  }
}
@media (max-width: 767px) {
  .mbr-buttons--auto-align {
    left: 5px;
    margin-top: -26px;
    text-align: center;
    top: 26px;
  }
  .mbr-buttons--auto-align.mbr-buttons--only-links {
    left: 0;
  }
}
@media (max-width: 530px) {
  .mbr-buttons {
    left: 0;
  }
  .mbr-buttons__btn,
  .mbr-buttons__link,
  .mbr-buttons--right .mbr-buttons__btn,
  .mbr-buttons--right .mbr-buttons__link {
    display: inline-block;
    margin: 0 0 13px 0;
    text-align: center;
    width: 100%;
  }
  .mbr-buttons--activated .mbr-buttons__btn,
  .mbr-buttons--activated .mbr-buttons__link,
  .mbr-buttons--active .mbr-buttons__btn,
  .mbr-buttons--active .mbr-buttons__link {
    width: auto;
  }
  .mbr-buttons--activated .mbr-buttons__btn,
  .mbr-buttons--active .mbr-buttons__btn {
    margin-top: 9px;
  }
}
.mbr-article {
  color: #777;
  font-size: 17px;
  line-height: 27px;
  text-align: left;
  position: relative;
  margin-top: -21px;
  top: 14px;
}
.mbr-article--wysiwyg h1,
.mbr-article--wysiwyg h2,
.mbr-article--wysiwyg h3,
.mbr-article--wysiwyg h4,
.mbr-article--wysiwyg h5,
.mbr-article--wysiwyg h6 {
  color: #252525;
  display: block;
  font-weight: bold;
  line-height: 1.3em;
  text-align: left;
}
.mbr-article--wysiwyg h1 {
  font-size: 27px;
  letter-spacing: 3px;
}
.mbr-article--wysiwyg h2 {
  font-size: 23px;
  letter-spacing: 2px;
}
.mbr-article--wysiwyg h3 {
  font-size: 19px;
  letter-spacing: 1px;
}
.mbr-article--wysiwyg h4 {
  font-size: 14px;
}
.mbr-article--wysiwyg h5 {
  font-size: 11px;
}
.mbr-article--wysiwyg h6 {
  font-size: 10px;
}
.mbr-article--wysiwyg p,
.mbr-article--wysiwyg ul,
.mbr-article--wysiwyg ol,
.mbr-article--wysiwyg blockquote {
  margin: 0 0 10px 0;
}
.mbr-article--wysiwyg blockquote {
  font-size: 17px;
  border-color: #f97352;
}
@media (max-width: 767px) {
  .mbr-article--auto-align.mbr-article--wysiwyg p,
  .mbr-article--auto-align {
    text-align: left !important;
  }
}
.social-likes__counter {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  background: #3c3c3c;
  border-radius: 23px;
  font-size: 12px;
  height: 23px;
  line-height: 24px;
  min-width: 23px;
  padding: 0 5px;
  position: absolute;
  right: -7px;
  text-align: center;
  top: -7px;
}
.social-likes__counter_empty {
  display: none;
}
.social-likes_style-1 .social-likes__icon:hover {
  background: #252525 !important;
}
.social-likes_style-1 .social-likes__icon:hover .social-likes__counter {
  background: #f97352;
}
.social-likes_style-2 .social-likes__icon {
  background: #252525;
}
.social-likes_style-2 .social-likes__counter {
  background: #f97352;
}
.social-likes_style-2 .social-likes__icon:hover .social-likes__counter {
  background: #3c3c3c;
}
.mbr-plan {
  padding-bottom: 31px;
  padding-left: 1px;
  padding-right: 0;
  position: relative;
}
.mbr-plan--first,
.mbr-plan:first-child {
  padding-left: 0;
}
.mbr-plan--last,
.mbr-plan:last-child {
  padding-bottom: 93px;
}
.mbr-plan__box {
  background: #fff;
}
.mbr-plan__header {
  background: #444;
  overflow: hidden;
  padding: 20px 15px;
  color: #fff;
}
.mbr-plan__number {
  border-bottom: 1px dotted #ddd;
  color: #333;
  font-size: 80px;
  line-height: 0;
  margin: 0px 6px;
  padding: 25px 0;
  text-align: center;
  margin-bottom: 31px;
  background-color: rgb(236,237,239);
}
.mbr-plan__details {
  padding-bottom: 41px;
  background-color: rgb(200,200,200);
  opacity: 0.8;
  font-family: 'Exo2 Bold';
}
.mbr-plan__details ul,
.mbr-plan__list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.mbr-plan__details ul {
  text-align: center;
}
.mbr-plan__details li,
.mbr-plan__item {
  line-height: 15px;
  padding: 0 15px;
}
.mbr-plan__item {
  text-align: center;
}
.mbr-plan__buttons {
  overflow: hidden;
  padding: 0 15px 41px;
}
.mbr-plan--favorite {
  margin-right: -1px;
  margin-top: -30px;
  padding-left: 0;
  top: 15px;
  z-index: 5;
}
.mbr-plan--favorite .mbr-plan__number:before {
  content: "";
  display: block;
  height: 15px;
}
.mbr-plan--favorite .mbr-plan__box {
  padding-bottom: 15px;
  box-shadow: 0px 0px 20px 5px rgba(0, 0, 0, 0.1);
}
.mbr-plan--primary .mbr-plan__header {
  background: #4c6972;
}
.mbr-plan--success .mbr-plan__header {
  background: #7ac673;
}
.mbr-plan--info .mbr-plan__header {
  background: #27aae0;
}
.mbr-plan--warning .mbr-plan__header {
  background: #faaf40;
}
.mbr-plan--danger .mbr-plan__header {
  background: #f97352;
}
@media (max-width: 767px) {
  .mbr-plan,
  .mbr-plan--first,
  .mbr-plan:first-child {
    padding-left: 15px;
    padding-right: 15px;
  }
  .mbr-plan__number {
    font-size: 79px;
  }
  .mbr-plan__details {
    font-size: 17px;
  }
  .mbr-plan--favorite {
    margin: 0;
    top: 0;
  }
}
.mbr-number {
  display: inline-block;
  margin-top: -0.12em;
}
.mbr-number__num {
  display: inline-table;
  height: 1em;
}
.mbr-number__group {
  display: table-cell;
  font-weight: bold;
  position: relative;
  vertical-align: middle;
}
.mbr-number__left {
  display: none;
  font-size: 0.34em;
  line-height: 0;
  padding: 0px 5px;
  vertical-align: super;
}
.mbr-number__right {
  display: none;
  font-size: 0.25em;
  padding: 0px 5px;
  vertical-align: middle;
  white-space: nowrap;
}
.mbr-number__caption {
  display: block;
  font-size: 0.19em;
  line-height: 1em;
  opacity: 0.5;
  padding-top: 0.5em;
  text-align: center;
}
.mbr-number--price .mbr-number__value {
  padding-right: 0.28em;
}
.mbr-number--price .mbr-number__left {
  display: inline;
}
.mbr-number--short-price .mbr-number__left,
.mbr-number--short-price .mbr-number__right {
  display: inline;
}
.mbr-number--short-price .mbr-number__caption {
  display: none;
}
.mbr-number--inverse-price .mbr-number__group {
  top: 0.1em;
}
.mbr-number--inverse-price .mbr-number__left {
  display: none;
}
.mbr-number--inverse-price .mbr-number__value {
  padding-left: 0.28em;
}
.mbr-number--inverse-price .mbr-number__right {
  display: inline;
}


/* iconfont default styling */
/* for buttons */
.mbr-iconfont.mbr-iconfont-btn,
.mbr-buttons__btn .mbr-iconfont{ /* depricated, used only for compatibility */
  padding-right: 0.3em;
  font-size: 2em;
  line-height: 0.4em;
  vertical-align: text-bottom;
  position: relative;
  top: -0.1em;
  text-decoration:none;
}

/* menu links */
.mbr-iconfont.mbr-iconfont-btn-parent,
.mbr-buttons__link .mbr-iconfont{ /* depricated, used only for compatibility */
  padding-right: 0.3em;
  font-size: 1.5em;
  position: relative;
  top: -0.2em;
  vertical-align:middle
}

/* msg-box4 */
.mbr-iconfont.mbr-iconfont-msg-box4,
.mbr-iconfont.mbr-iconfont-msg-box5{
  font-size: 357px;
  text-decoration:none;
  color:#FFFFFF;
}


/*menu logo */
.mbr-iconfont.mbr-iconfont-menu,
.mbr-iconfont.mbr-iconfont-ext__menu{
  font-size: 74px;
  text-decoration:none;
  color:#FFFFFF;
}

/* contacts1 */
.mbr-iconfont.mbr-iconfont-contacts1{
  font-size: 119px;
  text-decoration:none;
  color:#9C9C9C;
}

.mbr-iconfont.mbr-iconfont-features1{
  font-size: 250px; /* ~ image.height */
  text-decoration:none;
}

@media (max-width: 768px) {
  .image-size{
    width: 100% !important;
  }
  .content-size{
    width: 100%;
  }
}.engine {
	position: absolute;
	text-indent: -2400px;
	text-align: center;
	padding: 0;
	top: 0;
	left: -2400px;
}

/*custom css*/
@font-face
{
  font-family: 'Exo2 Medium';
  src: url('/static/assets/fonts/Exo2-Medium.otf') format("opentype");
}
@font-face
{
  font-family: 'Exo2 ExtraBold';
  src: url('/static/assets/fonts/Exo2-ExtraBold.otf') format("opentype");
}
@font-face
{
  font-family: 'Exo2 Bold';
  src: url('/static/assets/fonts/Exo2-Bold.otf') format("opentype");
}
@font-face
{
  font-family: 'Exo2 Regular';
  src: url('/static/assets/fonts/Exo2-Regular.otf') format("opentype");
}
@font-face
{
  font-family: 'Uni Sans Heavy';
  src: url('/static/assets/fonts/Uni_Sans_Heavy.otf') format("opentype");
}
body {
  font-family: 'Exo2 Medium'!important;
}
input {
  font-family: 'Arial';
}
.mbr-navbar--short .mbr-navbar__section {
  border-style: solid;
  border-bottom-width: 2px;
  border-top: none;
  border-left: none;
  border-right: none;
}
.put-border {
  border-right: solid;
  border-right-color: white;
}
.btn-success {
  background-color: rgb(56, 149, 130);
  border-color: rgb(56, 149, 130);
}
.btn-warning {
  background-color: rgb(11, 55, 102);
  border-color: rgb(11, 55, 102);
}
.standard-bottom-padding {
  padding-bottom: 30px;
}
.justified-text {
  text-align: justify;
}
.very-small-bottom-padding {
    padding-bottom: 3px;
    padding-top: 0px;
}
.dropdown-menu a {
    width: 100%;
    text-decoration: none;
}
.caret.caret-up {
    border-top-width: 0;
    border-bottom: 4px solid #fff;
}
.header-section h1 {
  font-family: 'Exo2 ExtraBold';
  font-size: 44px;
  letter-spacing: 4px;
  color: rgb(50,182,243);
}
.termsText .welcomeText {
  font-size: 24px;
  letter-spacing: 3px;
}
.section-header {
  font-family: 'Exo2 Bold';
  font-size: 28px;
  letter-spacing: 3px;
  color: #00374c;
}
.termsText p {
  font-size: 16px;
  letter-spacing: 0.5px;
}
.emphasis {
  color: #00374c;
}
.standard-top-padding {
  padding-top: 30px;
}
.clauses {
  font-size: 125%;
  list-style-type: upper-roman;
  list-style-position: outside;
}
.indented {
  text-indent: 3%;
}
hr.d4m-ending {
padding: 0;
border: none;
height: 1.5px;
background-image: -webkit-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
background-image: -moz-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
background-image: -ms-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
background-image: -o-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
color: #333;
text-align: center;
}
hr.d4m-ending:after {
content:" ";
display: inline-block;
position: relative;
top: -2.1em;
font-size: 1.5em;
padding: 19px 1.75em;
background: ivory url('/static/assets/images/New_Logo_hr.png') no-repeat scroll center;
background-size: 30px 51px;
height: 75px;
}
.indent1 {
  list-style: outside url("/static/assets/images/New_Logo_bullet.png");
  font-size: 50%;
}
@media screen and (max-width: 1080px) and (max-height: 1080px) {
    .mbr-brand__img { width: 222px!important; height: 67px!important; }
}

tags:
  - Affiliate_Wallet
summary: Transfer Money Between Affiliate Accounts
description: >
  This endpoint allows an affiliate representative to transfer money from one affiliate account to another.
parameters:
  - name: from_aff_id
    in: formData
    type: integer
    required: true
    description: The ID of the sender affiliate.
  - name: to_aff_id
    in: formData
    type: integer
    required: true
    description: The ID of the receiver affiliate.
  - name: amount
    in: formData
    type: integer
    required: true
    description: The amount to be transferred.
responses:
  200:
    description: Transfer successful.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        message:
          type: string
          description: Success message.
          example: "Transfer successful"
        sender_wallet:
          type: number
          format: float
          description: Sender's wallet balance after the transfer.
          example: 80.25
        receiver_wallet:
          type: number
          format: float
          description: Receiver's wallet balance after the transfer.
          example: 120.50
  400:
    description: Invalid data types or insufficient balance.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 or -5 for errors).
          example: -3
        message:
          type: string
          description: Error message.
          example: "Invalid data types for fields."
  401:
    description: Failed to get identity.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error).
          example: -1
        message:
          type: string
          description: Error message.
          example: "Failed to get identity."
  404:
    description: One or both affiliates not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for error).
          example: -4
        message:
          type: string
          description: Error message.
          example: "One or both affiliates not found."
  500:
    description: Server error during the transfer.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-7 for error).
          example: -7
        message:
          type: string
          description: Error message.
          example: "An error occurred during the transfer."

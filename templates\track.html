<!DOCTYPE html>
<html>
<head>


<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>

<!-- Add Firebase products that you want to use -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo" type="text/javascript"></script>
</head>
<body>

<title>Drivers4Me - Track your driver</title>
<link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">
<style>
    html, body, #googleMap {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
    }
#googleMap {
    position: relative;
}
</style>
<div id="googleMap"></div><script src="{{ url_for("static", filename="jquery.js") }}"></script>

<script>
function toRadians(degrees) {
  return degrees * Math.PI / 180;
};

// Converts from radians to degrees.
function toDegrees(radians) {
  return radians * 180 / Math.PI;
}


function getBearing(startLat, startLng, destLat, destLng){
  startLat = toRadians(startLat);
  startLng = toRadians(startLng);
  destLat = toRadians(destLat);
  destLng = toRadians(destLng);

  y = Math.sin(destLng - startLng) * Math.cos(destLat);
  x = Math.cos(startLat) * Math.sin(destLat) -
        Math.sin(startLat) * Math.cos(destLat) * Math.cos(destLng - startLng);
  brng = Math.atan2(y, x);
  brng = toDegrees(brng);
  return (brng + 180) % 360;
}
var myLatlng = new google.maps.LatLng(22.5726, 88.3639);
var isCenter = false;
// Get a reference to the database service
var config = {
    apiKey: "AIzaSyCY7Bp_IhHJ8fySdrOswK8PE-kLjU6Z77Y",
    databaseURL: "https://drivers4me-prod-default-rtdb.asia-southeast1.firebasedatabase.app/",
  };
firebase.initializeApp(config);
var database = firebase.database();
var mapOptions = {
  zoom: 13,
  streetViewControl: false,
  mapTypeControl: false,
  //center: myLatlng
}
var map = new google.maps.Map(document.getElementById("googleMap"), mapOptions);

var icon = {
    url: "{{ url_for("static", filename="assets/Tracker.svg") }}",
    //path: ,
    origin: new google.maps.Point(0,0), // origin
    anchor: new google.maps.Point(27.5, 45), // anchor
    //scale:
    //scaledSize: new google.maps.Size(200, 200)
    scaledSize: new google.maps.Size(55, 90)
};
var mapSet = false;
var marker = new google.maps.Marker({
    position: myLatlng,
    title: '{{ driver_name }}' + "'s Current Location",
    icon: icon
});
var infowindow = new google.maps.InfoWindow();

// To add the marker to the map, call setMap();

$('img[src="{{ url_for("static", filename="assets/Tracker.svg") }}"]').css({
    'transform': 'rotate(180deg)'
});
addCenterControl(map);

google.maps.event.addListener(marker, "click", function(evt) {
    infowindow.setContent(this.get('title'));
    infowindow.open(map,this);
});

database.ref('/Location/' + {{ driver_id }}).on('value', function(snapshot) {
    move = function(marker, latlngs, index, wait, newDestination, timestamp) {
        marker.setPosition(latlngs[index]);
        marker.setTitle(timestamp.toLocaleString());
        infowindow.setContent(timestamp.toLocaleString());
        myLatlng = newDestination;
        if(!isCenter){
            map.setCenter(newDestination);
            isCenter = true;
        }
        if(index != latlngs.length-1) {
          // call the next "frame" of the animation
          setTimeout(function() {
            move(marker, latlngs, index+1, wait, newDestination, timestamp);
          }, wait);
        } else {
            marker.position = newDestination;
            marker.setPosition(newDestination);
            marker.setTitle(timestamp.toLocaleString());
            infowindow.setContent(timestamp.toLocaleString());
        }
    }

    frames = [];
    var fromLat = marker.getPosition().lat();
    var fromLng = marker.getPosition().lng();
    try {
        var toLat = snapshot.val().latitude;
        var toLng = snapshot.val().longitude;
        var timestamp = new Date(snapshot.val().timestamp.time);
        //console.log(snapshot.val().timestamp.time);
        //console.log(timestamp.toLocaleString());
        for (var percent = 0; percent < 1; percent += 0.01) {
            curLat = fromLat + percent * (toLat - fromLat);
            curLng = fromLng + percent * (toLng - fromLng);
            frames.push(new google.maps.LatLng(curLat, curLng));
        }
        var latLng = new google.maps.LatLng(toLat, toLng);
        marker.position = latLng;
        var ic = marker.getIcon();
        var bearing = getBearing(fromLat, fromLng, toLat, toLng) + 180;
        if (bearing > 360)
            bearing = bearing - 360;
        $('img[src="{{ url_for("static", filename="assets/Tracker.svg") }}"]').css({
            'transform': 'rotate(' + bearing + 'deg)'
          });
        marker.setIcon(ic);
        move(marker, frames, 0, 10, marker.position, timestamp);
        if (!mapSet) {
            marker.setMap(map);
            mapSet = true;
        }
    } catch(err) {
        marker.setMap(null);
    }
});

function addCenterControl(map) {
    var controlDiv = document.createElement('div');
    // Set CSS for the control border.
    var firstChild = document.createElement('button');
    firstChild.style.backgroundColor = '#fff';
    firstChild.style.border = 'none';
    firstChild.style.outline = 'none';
    firstChild.style.width = '28px';
    firstChild.style.height = '28px';
    firstChild.style.borderRadius = '2px';
    firstChild.style.boxShadow = '0 1px 4px rgba(0,0,0,0.3)';
    firstChild.style.cursor = 'pointer';
    firstChild.style.marginRight = '10px';
    firstChild.style.padding = '0px';
    firstChild.title = 'Driver\'s Location';
    controlDiv.appendChild(firstChild);

    var secondChild = document.createElement('div');
    secondChild.style.margin = '5px';
    secondChild.style.width = '18px';
    secondChild.style.height = '18px';
    secondChild.style.backgroundImage = 'url(https://maps.gstatic.com/tactile/mylocation/mylocation-sprite-1x.png)';
    secondChild.style.backgroundSize = '180px 18px';
    secondChild.style.backgroundPosition = '0px 0px';
    secondChild.style.backgroundRepeat = 'no-repeat';
    secondChild.id = 'cur_loc_img';
    firstChild.appendChild(secondChild);
    google.maps.event.addListener(map, 'dragend', function() {
        $('#you_location_img').css('background-position', '0px 0px');
    });
    // Setup the click event listeners: simply set the map to Chicago.
    firstChild.addEventListener('click', function() {
      map.setCenter(marker.getPosition());
      map.panTo(marker.getPosition());
    });
    controlDiv.index = 1;
    map.controls[google.maps.ControlPosition.RIGHT_BOTTOM].push(controlDiv);
}

</script>

</body>
</html>
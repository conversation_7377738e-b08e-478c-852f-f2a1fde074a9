tags:
  - Driver_admin
summary: Fetch New Driver Data
description: >
  This endpoint fetches new driver entries since the last recorded timestamp. It checks for new entries 
  in the database and retrieves relevant driver data if available.
parameters:
  - name: last_timestamp
    in: formData
    required: true
    type: string
    description: The timestamp of the last recorded entry from the local storage. New entries after this timestamp will be retrieved.
    example: "2023-10-01 12:00:00"
responses:
  200:
    description: Successfully retrieved new driver data.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        count:
          type: integer
          description: The total number of new entries found.
          example: 5
        lastentry:
          type: string
          description: The timestamp of the last entry retrieved.
          example: "2023-10-10 14:45:00"
        data:
          type: array
          items:
            type: object
            properties:
              driver_id:
                type: integer
                description: The ID of the driver.
                example: 12345
              mobile:
                type: string
                description: The mobile number of the driver.
                example: "+919876543210"
              name:
                type: string
                description: The full name of the driver.
                example: "John Doe"
              location:
                type: string
                description: The location or region of the driver.
                example: "New York"
              region:
                type: string
                description: The region of the driver.
                example: "2"
              rating:
                type: number
                description: The rating of the driver.
                example: 4.7
              rating_count:
                type: integer
                description: The number of ratings the driver has received.
                example: 100
              approval:
                type: integer
                description: The approval status of the driver.
                example: 1
              status:
                type: integer
                description: The availability status of the driver (1 for available, 0 for unavailable).
                example: 1
              image:
                type: string
                description: URL to the driver's profile picture.
                example: "https://example.com/profile.jpg"
              timestamp:
                type: string
                description: The timestamp when the driver registered.
                example: "2023-10-10 12:45:00"
              label:
                type: string
                description: The label associated with the driver.
                example: "VIP"
  400:
    description: Missing or invalid parameters (e.g., missing last_timestamp).
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Error message detailing what went wrong.
          example: "last_timestamp parameter is required"
  500:
    description: Internal server error during the data fetching process.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Error message returned during server-side processing.
          example: "An error occurred while fetching new data"

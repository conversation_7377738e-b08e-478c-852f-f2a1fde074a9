tags:
  - Booking
summary: Book a driver for the user
description: >
  This API allows a user to book a driver based on search details and booking preferences.
parameters:
  - name: search_id
    in: formData
    type: string
    required: true
    description: The search ID of the trip
  - name: payment_type
    in: formData
    type: string
    required: false
    description: Type of payment (e.g., cash, card)
  - name: loc
    in: formData
    type: string
    required: false
    description: Pickup location
  - name: dest_lat
    in: formData
    type: number
    required: false
    description: Latitude of the destination
  - name: dest_long
    in: formData
    type: number
    required: false
    description: Longitude of the destination
  - name: dest_loc
    in: formData
    type: string
    required: false
    description: Destination location name
  - name: insurance
    in: formData
    type: integer
    required: false
    description: Insurance flag (0 for no insurance, 1 for insurance)
responses:
  200_a:
    description: Booking created successfully
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Booking created successfully"
  200_b:
    description: Booking already exists for the search
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Success flag (1 for success)
          example: 1
        exists:
          type: integer
          description: Indicates that the booking already exists
          example: 1
        message:
          type: string
          description: Success message
          example: "Booking already created for this search"
  401_a:
    description: Failed to fetch identity
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to get identity"
  401_b:
    description: Invalid role - Driver
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Invalid role - Driver"
  401_c:
    description: User restricted
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
  400:
    description: Location is outside service area
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Location is outside service area"
  403_a:
    description: User banned from creating a booking
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "User banned to create booking"
  403_b:
    description: User already has a booking in this time frame
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-2 for failure)
          example: -2
        message:
          type: string
          description: Error message
          example: "User already have booking in this time frame"
  500_a:
    description: Invalid search ID
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Invalid search ID"
  500_b:
    description: Database error during booking
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
  403_c:
    description: Not enough credits to book a ride
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-3 for failure)
          example: -3
        message:
          type: string
          description: Error message
          example: "Not enough credits"
  500_c:
    description: Invalid search ID (retry after booking already exists check)
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Invalid search ID"
  403_d:
    description: Booking created but no drivers were available
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Success flag (1 for success)
          example: 1
        exists:
          type: integer
          description: Indicates that the booking was created, but no drivers were available
          example: 1
        message:
          type: string
          description: Success message
          example: "Booking created, but no drivers were available"
  400_b:
    description: User already has a booking for the same time
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-2 for failure)
          example: -2
        message:
          type: string
          description: Error message
          example: "User already has booking in this time frame"

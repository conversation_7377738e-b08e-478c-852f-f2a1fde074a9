var searchId = Math.random();
$(document).ready(function() {
    // <editor-fold desc="Events">
    //expand function on a booking entry (UNUSED)
    $('body').delegate(".book-entry-expand", 'click', function() {
        if ($(this).find('i').attr('class').indexOf("expand") >= 0) {
            //expand additionals
            $(this).closest(".booking-entry").find(".additional").attr('class', 'col-lg-3 col-md-3 col-sm-6 col-xs-6 additional standard-top-padding');
            $(this).find('i').attr('class', 'fa fa-sm fa-compress');
            $(this).attr('title', 'less...');
        } else {
            //collapse additionals
            $(this).closest(".booking-entry").find(".additional").attr('class', 'col-lg-3 col-md-3 col-sm-6 col-xs-6 additional standard-top-padding collapse');
            $(this).find('i').attr('class', 'fa fa-sm fa-expand');
            $(this).attr('title', 'more...');
        }
    });

    //Trip Photo
    var fileURLs = [];
    var zip = new JSZip();
    var count = 0;
    var fileNameId = "";
    $("body").delegate(".display-photo", 'click', function() {
        //get booking id
        fileURLs = [];
        var bookId = $(this).closest(".booking-entry").find(".book-id").html();
        fileNameId = bookId;
        console.log(bookId);
        data = new FormData();
        data.append('booking_id', bookId)
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/api/mahindra/booking_pic',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/affiliate/jyoti/login";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response['success'] == 1) {
                    //do stuff
                    var photo_entries = response['pic_list'].length;
                    var i = 0;
                    while (i < photo_entries) {
                        var this_photo = response['pic_list'][i];
                        var totalItems = $(".item").length;
                        if (totalItems === 0) {
                            itemClass = "item active";
                            slideText = "active";
                        } else {
                            itemClass = "item";
                            slideText = "";
                        }
                        console.log(totalItems);
                        var thisImage = 'https://storage.drivers4me.com/' + this_photo.pic_url;
                        $('.photo-count').append('<li data-target="#TripPhotoCarousel" data-slide-to="' + i + '" class="added ' + slideText + '"></li>');
                        $('.photo-display').append('<div class="added ' + itemClass + '">' +
                            '<img class="d-block" src="' + thisImage + '" alt="" style="width:100%;">' +
                            '<div class="carousel-caption">' +
                            '<h3>' + this_photo.type + '</h3>' +
                            '<p>' + this_photo.timestamp + '</p>' +
                            '</div>' + '</div>');
                        i++;
                        fileURLs = fileURLs.concat([thisImage]);
                    }
                }
                if (i == 0) {
                    alert("No photo found!")
                } else {
                    $("#photoModal").modal('show');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background', 'gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function() {
                    $("#infoModal").modal('show');
                }, 100);
                setTimeout(function() {
                    $("#infoModal").modal('hide');
                }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });
    $("#downloadZip").click(function() {
        var zip = new JSZip();
        var count = 0;
        var zipFilename = fileNameId + ".zip";
        // loading a file and add it in a zip file
        fileURLs.forEach(async function(url) {
            const urlArr = url.split('/');
            const filename = urlArr[urlArr.length - 1];
            try {
                const file = await JSZipUtils.getBinaryContent(url)
                zip.file(filename, file, {
                    binary: true
                });
                count++;
                if (count === fileURLs.length) {
                    zip.generateAsync({
                        type: 'blob'
                    }).then(function(content) {
                        saveAs(content, zipFilename);
                    });
                }
            } catch (err) {
                console.log(err);
            }
        });
    });
    $("#photoModal").on('hide.bs.modal', function() {
        $('.added').remove();
    });
    
    //booking entry - allocate button click event (new and upcoming)
    $('body').delegate(".allocate-button", 'click', function() {
        //prepare data for AJAX call
        data = new FormData();
        var bookingID = parseInt($(this).closest(".booking-entry").find(".book-id").html());
        data.append('booking_id', bookingID);
        if ($("#bookingTypeBar .active").attr("id") == 'upcoming')
            data.append('all', 1);
        else
            data.append('all', 0);
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/api/mahindra/alloc_list',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                openModal("DriverList");
                if (response["success"] === 1) {
                    if (response["data"] !== "") {
                        $("#DriverListModal").find(".modal-body").find('.driver-list').html();
                        var driverList = "";
                        driverList += '<div class="row driver-entry-container" style="text-align: center; margin: 10px 0;">' +
                            '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' +
                            '<input type="text" id="search" placeholder="Search drivers..." class="form-control">' +
                            '</div>' +
                            '</div>'
                        $.each(response["data"], function(index, value) {
                            var driverEntry = value;
                            driverList += getDriverEntryHtml(bookingID, driverEntry.id, driverEntry.name, driverEntry.mobile, ["Allocate"]);
                        });
                        $("#DriverListModal").find('.driver-list').html(driverList);
                    } else {
                        $("#DriverListModal").find('.driver-list').html("<h3>No drivers available</h3>");
                    }
                } else {
                    $("#DriverListModal").find('.driver-list').html("<h3>No drivers list</h3>");
                }
            }
        });
    });

    $('body').delegate("#refreshBookingList", 'click', function() {
        if ($('#day').is(":checked")) {
            startdate = new Date();
            startdate.setTime(startdate.getTime() + (5.5 * 60 * 60 * 1000));
            $('#completedBookingTab').empty();
            searchId = Math.random();
            console.log(searchId);
            refreshBookingFilter(startdate, startdate);

        } else if ($('#specific_date').is(":checked")) {
            startdate = new Date($('#start_date').val().split("/").reverse().join("/") + " 00:00 UTC")
            $('#completedBookingTab').empty();
            searchId = Math.random();
            refreshBookingFilter(startdate, startdate);

        } else if ($('#date_range').is(":checked")) {
            startdate = new Date($('#start_date').val().split("/").reverse().join("/") + " 00:00 UTC")
            enddate = new Date($("#end_date").val().split("/").reverse().join("/") + " 00:00 UTC")
            if (startdate > enddate) {
                alert("Sorry Start date cannot be greater than end date!");
                return;
            }
            $('#completedBookingTab').empty();
            searchId = Math.random();
            refreshBookingFilter(startdate, enddate);
        }

    });

    function refreshBookingFilter(from_date, to_date) {
        if (window.pastCalled) return;
        const sid = searchId;
        var sequence = Promise.resolve(); //initialize to empty resolved promise
        var deferred = getDatesInRange(from_date,
            to_date).reverse()
        console.log(deferred);
        //This will run once all async operations have successfully finished
        deferred.forEach(function(entry) {
            sequence = sequence.then(function() {
                return getRefreshData(entry); // return a new Promise
            }).then(function(response) {
                setTimeout(function() {
                    if (response["success"] == 1) {
                        if (response["data"] !== "") {
                            $.each(response["data"], function(index, value) {
                                var bookingEntry = JSON.parse(value);
                                if (sid == searchId) {
                                    console.log(sid + " " + searchId);
                                    addCompletedBooking(bookingEntry);
                                }
                            })
                        }
                    }
                })
            }).catch(function() {

                $("#infoModal").find(".modal-header").css('background', 'gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function() {
                    $("#infoModal").modal('show');
                }, 100);
                setTimeout(function() {
                    $("#infoModal").modal('hide');
                }, 2900);
            });
        });

    }

    //allocate driver from list click
    $('body').delegate(".allocate-driver", 'click', function() {
        var driverEntryID = $(this).closest(".driver-entry").attr("id").split('_');
        var bookingID = parseInt(driverEntryID[1]);
        var driverID = parseInt(driverEntryID[2]);
        if (!confirm(
                "#" + driverID + " : " + $(this).closest(".driver-entry").find(".name").html() + " will be allocated to " +
                "Booking #" + bookingID + ". Proceed?"
            )) return true;
        //prepare data for AJAX call
        data = new FormData();
        data.append('booking_id', bookingID);
        data.append('driver_id', driverID);
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/api/mahindra/alloc_driver',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                $("#DriverListModal").find('.driver-list').html("");
                $("#DriverListModal").find(".close-button").click();
                if (response["success"] === 1) {
                    alert("Allocation Successful!");
                    $("#upcoming").click();
                } else {
                    alert("Allocation failed!");
                }
            }
        });
    });

    //refresh
    $('body').delegate("#refreshCurrent", "click", function() {
        $("#" + $("#bookingTypeBar .active").attr("id") + "BookingTab").html('')
        popBooking($("#bookingTypeBar .active").attr("id"));
    });


    //cancel booking
    $('body').delegate(".btn-cancel", "click", function() {
        data = new FormData();
        data.append('booking_id', $(this).closest(".booking-entry").find(".book-id").html());
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/api/mahindra/cancel',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {


                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response["success"] == 1) {
                    var bookingType = $(this).closest(".booking-entry");
                    if (bookingType.hasClass("new-booking-entry")) {
                        window.location.hash = "#newBookings";
                    }
                    if (bookingType.hasClass("upcoming-booking-entry")) {
                        window.location.hash = "#upcomingBookings";
                    }
                    if (bookingType.hasClass("ongoing-booking-entry")) {
                        window.location.hash = "#ongoingBookings";
                    }
                    if (bookingType.hasClass("completed-booking-entry")) {
                        window.location.hash = "#completedBookings";
                    }
                    if (bookingType.hasClass("cancel-d4m-booking-entry")) {
                        window.location.hash = "#d4mCancelBookings";
                    }
                    if (bookingType.hasClass("cancel-mahindra-booking-entry")) {
                        window.location.hash = "#mahindraCancelBookings";
                    }
                    window.location.reload();
                    openModal("Information", "Booking Cancelled!", "primary");
                } else {
                    openModal("Information", "Cancellation failed!", "danger");
                }
            }
        });
    });
    // </editor-fold>
});

function popBooking(bookingType) {
    //get the Request URL
    var urlDetails = getRequestURL(bookingType);
    var listOfURLs = urlDetails.split(':');
    //for each URL make the AJAX call, process the data and feed to the respective panel
    // (TO DO - Remove this concept of each URL, there is only one URL)
    // TO DO - implement promises
    // TO DO - implement filters
    $.each(listOfURLs, function(index, value) {
        toFetch = value.split('|')[1];
        fromURL = "/api/mahindra/" + value.split('|')[0];
        if (value == "past|1") {
            $("#completedBookingTab").html('');
            searchId = Math.random();
            refreshBooking();
            return;
        }
        //prepare data for AJAX call
        data = new FormData();
        data = addBookingFilters(data);
        if (toFetch !== 0) data.append('to_fetch', parseInt(toFetch));
        if (toFetch >= 2) data.append('cancelled_by', value.split('|')[2]);
        //AJAX call
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + fromURL,
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response["success"] == 1) {
                    if (response["data"] !== "") {
                        $.each(response["data"], function(index, value) {
                            var bookingEntry = JSON.parse(value);
                            switch (bookingType) {
                                case "new":
                                    addNewBooking(bookingEntry);
                                    break;
                                case "upcoming":
                                    addUpcomingBooking(bookingEntry);
                                    break;
                                case "ongoing":
                                    addOngoingBooking(bookingEntry);
                                    break;
                                case "completed":
                                    addCompletedBooking(bookingEntry);
                                    break;
                                    // TO DO - split these two cases
                                case "cancel-d4m":
                                    addCancelledBooking(bookingEntry, "d4m");
                                    break;
                                case "cancel-mahindra":
                                    addCancelledBooking(bookingEntry, "mahindra");
                                case "all":
                                default:
                                    // TO DO - review if needed
                            }
                        });
                        //center all address text
                        forceAllAddressToCenter();
                    } else {
                        $("#" + bookingType + "BookingTab").html(
                            '<div class="row standard-top-padding">' +
                            '<div class="col-lg-offset-4 col-md-offset-4 col-sm-offset-4 col-xs-offset-4 col-lg-4 col-md-4 col-sm-4 col-xs-4"' +
                            'style="text-align: center;">' +
                            '<h3>No Bookings Found</h3>' +
                            '</div>' +
                            '</div>'
                        );
                    }
                    //reverse map to book entry processing functions
                } else {
                    $("#" + bookingType + "BookingTab").html(
                        '<div class="row standard-top-padding">' +
                        '<div class="col-lg-offset-4 col-md-offset-4 col-sm-offset-4 col-xs-offset-4 col-lg-4 col-md-4 col-sm-4 col-xs-4"' +
                        'style="text-align: center;">' +
                        '<h3>No Bookings Found</h3>' +
                        '</div>' +
                        '</div>'
                    );
                }
            }
        });
    });
}

function dateRange(startDate, endDate) {
    var dateArray = new Array();
    var startTime = startDate.getTime(),
        endTime = endDate.getTime();
    for (loopTime = startTime; loopTime <= endTime; loopTime += 86400000) {
        var loopDay = new Date(loopTime);
        dateArray.push(loopDay.toISOString().split('T')[0]);
    }
    return dateArray;
}

function getDatesInRange(startDate, endDate) {
    const date = new Date(startDate.getTime());

    const dates = [];

    while (date <= endDate) {
        dates.push(new Date(date).toISOString().split('T')[0]);
        date.setDate(date.getDate() + 1);
    }

    return dates;
}



function nDaysAgo(n) {
    d = new Date();
    d.setTime(d.getTime() + (5.5 * 60 * 60 * 1000));
    var ndays_ago = d - n * 86400000;
    nd = new Date(ndays_ago);
    return nd
}


function refreshBooking() {
    if (window.pastCalled) return;
    const sid = searchId;
    var sequence = Promise.resolve(); //initialize to empty resolved promise
    var n_days_ago = nDaysAgo(7)
    var startdate = new Date();
    startdate.setTime(startdate.getTime() + (5.5 * 60 * 60 * 1000));
    var deferred = dateRange(n_days_ago,
        startdate).reverse()
    console.log(deferred);
    //This will run once all async operations have successfully finished
    deferred.forEach(function(entry) {
        sequence = sequence.then(function() {
            return getRefreshData(entry); // return a new Promise
        }).then(function(response) {
            setTimeout(function() {
                if (response["success"] == 1) {
                    if (response["data"] !== "") {
                        $.each(response["data"], function(index, value) {
                            var bookingEntry = JSON.parse(value);
                            if (sid == searchId) {
                                console.log(sid + " " + searchId);
                                addCompletedBooking(bookingEntry);
                            }
                        })
                    }
                }
            })
        }).catch(function() {

            $("#infoModal").find(".modal-header").css('background', 'gold');
            //change text
            $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
            setTimeout(function() {
                $("#infoModal").modal('show');
            }, 100);
            setTimeout(function() {
                $("#infoModal").modal('hide');
            }, 2900);
        });
    });

}


function getRefreshData(date) {
    data = new FormData();
    day = parseInt(date.split('-')[2])
    month = parseInt(date.split('-')[1])
    year = parseInt(date.split('-')[0])
    data.append("day", day);
    data.append("month", month);
    data.append("year", year);
    console.log(data)
    return new Promise(function(resolve) {
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/api/mahindra/past',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/affiliate/jyoti"
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                resolve(response);
            }
        });
    });
}
/**
 * Accepts a booking type as string and determines the API call
 * and fetch parameters for it. Where no fetch (to_fetch) is required
 * zero is returned. Multiple URL fragments are ':' separated
 * @param bookingType - new/upcoming/ongoing/completed/cancelled by mahindra/cancelled by d4m
 * @return {string} - url_fragment|to_fetch:url_fragment|to_fetch:url_fragment|to_fetch....
 */
function getRequestURL(bookingType) {
    switch (bookingType) {
        case "new":
            return "upcoming|2";
        case "upcoming":
            return "upcoming|1";
        case "ongoing":
            return "ongoing|0";
        case "completed":
            return "past|1";
            // Past 2 and Past 3 corresponds to D4M cancel (cancelled_by = 0), mahindra cancel (cancelled_by = 1)
        case "cancel-d4m":
            return "past|2|1";
        case "cancel-mahindra":
            return "past|2|0";
            // TO DO - Review if needed
        case "all":
        default:
            return "upcoming|1:upcoming|2:ongoing|0:past|1:past|2";
    }
}

// <editor-fold desc="Booking Entry Data Processing">

function addNewBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $("#newBookingTab").append(
        constructNewBookingEntry(
            bookingEntry.bookingid, bookingEntry.appt, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, startDateTime, bookingEntry.trip_type,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.comment
        )
    );
}

function addUpcomingBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $("#upcomingBookingTab").append(
        constructUpcomingBookingEntry(
            bookingEntry.bookingid, bookingEntry.appt, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, startDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.comment
        )
    );
}

function addOngoingBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $("#ongoingBookingTab").append(
        constructOngoingBookingEntry(
            bookingEntry.bookingid, bookingEntry.appt, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, startDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.driver_id,
            bookingEntry.comment
        )
    );
}

function addCompletedBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var scheduledDateTime = mergeDateTime(bookingEntry.sch_startdate, bookingEntry.sch_starttime);
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    var endDateTime = mergeDateTime(bookingEntry.enddate, bookingEntry.endtime);
    $("#completedBookingTab").append(
        constructCompletedBookingEntry(
            bookingEntry.bookingid, bookingEntry.appt, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, scheduledDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile, startDateTime, bookingEntry.dur,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            endDateTime, bookingEntry.ot, bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.comment, bookingEntry.file_url, bookingEntry.file_count
        )
    );
}

function addCancelledBooking(bookingEntry, cancelledBy) {
    //merge date and time into one date object convert to local
    var scheduledDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $('#cancel-' + cancelledBy + 'BookingTab').append(
        constructCancelledBookingEntry(
            cancelledBy, bookingEntry.bookingid, bookingEntry.appt, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, scheduledDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile, bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc, bookingEntry.comment
        )
    );
}

// </editor-fold>

// <editor-fold desc="HTML content generation">

/**
 * Construct the HTML for a new booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param appointmentID - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by mahindra
 * @return {string} - HTML content as string
 */
function constructNewBookingEntry(bookingID, appointmentID, vehicleModel, transmissionType,
    vehicleRegistrationNumber, scheduledDateTime, tripType,
    sourceLat, sourceLong, sourceAddressString,
    destLat, destLong, destAddressString, comments) {
    return (
        '<div class="container row booking-entry new-booking-entry full-width">' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Booking ID:</span>&emsp;' +
        '<span class="book-id">' + bookingID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Appointment ID:</span>&emsp;' +
        '<span class="appointment-id">' + appointmentID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Car Model:</span>&emsp;' +
        '<span>' +
        '<span class="vehicle-model">' + vehicleModel + '</span>' +
        '<span class="transmission-type"> (' + getTransmissionTypeText(transmissionType) + ')</span>' +
        '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Registration Number:</span>&emsp;' +
        '<span class="registration-number">' + vehicleRegistrationNumber + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">' + getTripTypeText(tripType) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>' +
        '<div class="row booking-info-label"><span class="driver-name">---</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">---</span></div>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container center-elements location-graphics">' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding left-image">' +
        '<span class="source-lat" style="display: none">' + sourceLat + '</span><span class="source-long" style="display: none">' + sourceLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Green_Pin.svg"/>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 no-padding arc-image">' +
        '<img src="../static/assets/images/booking-form/Arc.svg"/>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding right-image">' +
        '<span class="dest-lat" style="display: none">' + destLat + '</span><span class="dest-long" style="display: none">' + destLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Red_Pin.svg"/>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container location-text">' +
        '<div class="col-lg-5 col-md-5 col-sm-5 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="source-address">' + sourceAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '<div class="col-lg-offset-2 col-lg-5 col-md-offset-2 col-md-5 col-sm-offset-2 col-sm-5 col-xs-offset-2 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="destination-address">' + destAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 full-width">' +
        '<button class="btn btn-md d4m-primary-button allocate-button" style="width: 100%">Allocate</button>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 full-width align-right">' +
        '<button class="btn btn-md d4m-danger-button btn-cancel" style="width: 100%">Cancel Booking</button>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<textarea class="form-control form_element remarks" rows="2" id="cmt-area-' + bookingID + '" placeholder="Remarks...">' + comments + '</textarea>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-xs d4m-primary-button save-cmt disabled" data="' + bookingID + '" style="float: right;">Save</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>'
    );
}

/**
 * Construct the HTML for an upcoming booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param appointmentID - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by mahindra
 * @return {string} - HTML content as string
 */
function constructUpcomingBookingEntry(bookingID, appointmentID, vehicleModel, transmissionType,
    vehicleRegistrationNumber, scheduledDateTime, tripType,
    driverName, driverContact,
    sourceLat, sourceLong, sourceAddressString,
    destLat, destLong, destAddressString, comments) {
    return (
        '<div class="container row booking-entry upcoming-booking-entry full-width">' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Booking ID:</span>&emsp;' +
        '<span class="book-id">' + bookingID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Appointment ID:</span>&emsp;' +
        '<span class="appointment-id">' + appointmentID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Car Model:</span>&emsp;' +
        '<span>' +
        '<span class="vehicle-model">' + vehicleModel + '</span>' +
        '<span class="transmission-type"> (' + getTransmissionTypeText(transmissionType) + ')</span>' +
        '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Registration Number:</span>&emsp;' +
        '<span class="registration-number">' + vehicleRegistrationNumber + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">' + getTripTypeText(tripType) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>' +
        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container center-elements location-graphics">' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding left-image">' +
        '<span class="source-lat" style="display: none">' + sourceLat + '</span><span class="source-long" style="display: none">' + sourceLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Green_Pin.svg"/>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 no-padding arc-image">' +
        '<img src="../static/assets/images/booking-form/Arc.svg"/>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding right-image">' +
        '<span class="dest-lat" style="display: none">' + destLat + '</span><span class="dest-long" style="display: none">' + destLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Red_Pin.svg"/>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container location-text">' +
        '<div class="col-lg-5 col-md-5 col-sm-5 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="source-address">' + sourceAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '<div class="col-lg-offset-2 col-lg-5 col-md-offset-2 col-md-5 col-sm-offset-2 col-sm-5 col-xs-offset-2 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="destination-address">' + destAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 full-width">' +
        '<button class="btn btn-md d4m-primary-button allocate-button" style="width: 100%">Allocate</button>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 full-width align-right">' +
        '<button class="btn btn-md d4m-danger-button btn-cancel" style="width: 100%">Cancel Booking</button>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<textarea class="form-control form_element remarks" rows="2" id="cmt-area-' + bookingID + '" placeholder="Remarks...">' + comments + '</textarea>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-xs d4m-primary-button save-cmt disabled" data="' + bookingID + '" style="float: right;">Save</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>'
    );
}

/**
 * Construct the HTML for an ongoing booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param appointmentID - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param startDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param driverId
 * @param comments - Remarks by mahindra
 * @return {string} - HTML content as string
 */
function constructOngoingBookingEntry(bookingID, appointmentID, vehicleModel, transmissionType,
    vehicleRegistrationNumber, startDateTime, tripType,
    driverName, driverContact,
    sourceLat, sourceLong, sourceAddressString,
    destLat, destLong, destAddressString, driverId, comments) {
    return (
        '<div class="container row booking-entry ongoing-booking-entry full-width">' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Booking ID:</span>&emsp;' +
        '<span class="book-id">' + bookingID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Appointment ID:</span>&emsp;' +
        '<span class="appointment-id">' + appointmentID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Car Model:</span>&emsp;' +
        '<span>' +
        '<span class="vehicle-model">' + vehicleModel + '</span>' +
        '<span class="transmission-type"> (' + getTransmissionTypeText(transmissionType) + ')</span>' +
        '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Registration Number:</span>&emsp;' +
        '<span class="registration-number">' + vehicleRegistrationNumber + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Start Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(startDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">' + getTripTypeText(tripType) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>' +
        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container center-elements location-graphics">' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding left-image">' +
        '<span class="source-lat" style="display: none">' + sourceLat + '</span><span class="source-long" style="display: none">' + sourceLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Green_Pin.svg"/>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 no-padding arc-image">' +
        '<img src="../static/assets/images/booking-form/Arc.svg"/>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding right-image">' +
        '<span class="dest-lat" style="display: none">' + destLat + '</span><span class="dest-long" style="display: none">' + destLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Red_Pin.svg"/>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container location-text">' +
        '<div class="col-lg-5 col-md-5 col-sm-5 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="source-address">' + sourceAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '<div class="col-lg-offset-2 col-lg-5 col-md-offset-2 col-md-5 col-sm-offset-2 col-sm-5 col-xs-offset-2 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="destination-address">' + destAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-md d4m-primary-button track" data="' + driverId + '" style="width: 100%">Track Driver</button>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<textarea class="form-control form_element remarks" rows="2" id="cmt-area-' + bookingID + '" placeholder="Remarks...">' + comments + '</textarea>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-xs d4m-primary-button save-cmt disabled" data="' + bookingID + '" style="float: right;">Save</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>'
    );
}

/**
 * Construct the HTML for a completed booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param appointmentID - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param startDateTime
 * @param duration
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param stopDateTime
 * @param overtime
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by mahindra
 * @param fileUrl - URL of zip file containing uploaded pictures
 * @param fileCount - Number of files in the fileUrl zip
 * @return {string} - HTML content as string
 */
function constructCompletedBookingEntry(bookingID, appointmentID, vehicleModel, transmissionType, vehicleRegistrationNumber,
    scheduledDateTime, tripType, driverName, driverContact,
    startDateTime, duration, sourceLat, sourceLong, sourceAddressString,
    stopDateTime, overtime, destLat, destLong, destAddressString, comments,
    fileUrl, fileCount) {
    var dis = "";
    if (!fileUrl) {
        dis = "disabled";
    }
    var fileCountStr = "";
    if (fileCount > 0) {
        fileCountStr = " (" + fileCount + " uploaded)"
    }
    return (
        '<div class="container row booking-entry completed-booking-entry full-width">' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Booking ID:</span>&emsp;' +
        '<span class="book-id">' + bookingID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Appointment ID:</span>&emsp;' +
        '<span class="appointment-id">' + appointmentID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Car Model:</span>&emsp;' +
        '<span>' +
        '<span class="vehicle-model">' + vehicleModel + '</span>' +
        '<span class="transmission-type"> (' + getTransmissionTypeText(transmissionType) + ')</span>' +
        '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Registration Number:</span>&emsp;' +
        '<span class="registration-number">' + vehicleRegistrationNumber + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Scheduled Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">' + getTripTypeText(tripType) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>' +
        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container location-graphics">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Start Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="start-date-time">' + getFormattedDateTimeString(startDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Duration</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="duration">' + duration + '</span></div>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 full-width">' +
        '<div class="row booking-info-label">' +
        '<div class="col-lg-1 col-md-1 col-sm-3 col-xs-3 small-image no-padding">' +
        '<span class="source-lat" style="display: none">' + sourceLat + '</span><span class="source-long" style="display: none">' + sourceLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Green_Pin.svg"/>' +
        '</div>' +
        '<div class="col-lg-11 col-md-11 col-sm-9 col-xs-9 no-padding">' +
        '<span class="source-address break-long-text height-constrained">' + sourceAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container location-graphics">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Stop Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="stop-date-time">' + getFormattedDateTimeString(stopDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Overtime</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="overtime">' + overtime + '</span></div>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 full-width">' +
        '<div class="row booking-info-label">' +
        '<div class="col-lg-1 col-md-1 col-sm-3 col-xs-3 small-image no-padding">' +
        '<span class="dest-lat" style="display: none">' + destLat + '</span><span class="dest-long" style="display: none">' + destLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Red_Pin.svg"/>' +
        '</div>' +
        '<div class="col-lg-11 col-md-11 col-sm-9 col-xs-9 no-padding">' +
        '<span class="source-address break-long-text height-constrained">' + destAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-md d4m-primary-button display-photo" style="width: 100%">Photos</button>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<textarea class="form-control form_element remarks" rows="2" id="cmt-area-' + bookingID + '" placeholder="Remarks...">' + comments + '</textarea>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-xs d4m-primary-button save-cmt disabled" data="' + bookingID + '" style="float: right;">Save</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>'
    );
}

/**
 * Construct the HTML for a cancelled booking entry using
 * the data in the following parameters
 * @param cancelledBy - d4m or mahindra
 * @param bookingID - Booking ID unique to D4M
 * @param appointmentID - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by mahindra
 * @return {string} - HTML content as string
 */
function constructCancelledBookingEntry(cancelledBy, bookingID, appointmentID, vehicleModel, transmissionType,
    vehicleRegistrationNumber, scheduledDateTime, tripType,
    driverName, driverContact,
    sourceLat, sourceLong, sourceAddressString,
    destLat, destLong, destAddressString, comments) {
    return (
        '<div class="container row booking-entry cancel-' + cancelledBy + '-booking-entry full-width">' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Booking ID:</span>&emsp;' +
        '<span class="book-id">' + bookingID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Appointment ID:</span>&emsp;' +
        '<span class="appointment-id">' + appointmentID + '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Car Model:</span>&emsp;' +
        '<span>' +
        '<span class="vehicle-model">' + vehicleModel + '</span>' +
        '<span class="transmission-type"> (' + getTransmissionTypeText(transmissionType) + ')</span>' +
        '</span>' +
        '</div>' +
        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">' +
        '<span class="param-label">Registration Number:</span>&emsp;' +
        '<span class="registration-number">' + vehicleRegistrationNumber + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">' + getTripTypeText(tripType) + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>' +
        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">' +
        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>' +
        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container center-elements location-graphics">' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding left-image">' +
        '<span class="source-lat" style="display: none">' + sourceLat + '</span><span class="source-long" style="display: none">' + sourceLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Green_Pin.svg"/>' +
        '</div>' +
        '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 no-padding arc-image">' +
        '<img src="../static/assets/images/booking-form/Arc.svg"/>' +
        '</div>' +
        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 no-padding right-image">' +
        '<span class="dest-lat" style="display: none">' + destLat + '</span><span class="dest-long" style="display: none">' + destLong + '</span>' +
        '<img src="../static/assets/images/booking-form/Red_Pin.svg"/>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container location-text">' +
        '<div class="col-lg-5 col-md-5 col-sm-5 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="source-address">' + sourceAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '<div class="col-lg-offset-2 col-lg-5 col-md-offset-2 col-md-5 col-sm-offset-2 col-sm-5 col-xs-offset-2 col-xs-5 no-padding">' +
        '<div class="row booking-info-label break-long-text center-elements height-constrained">' +
        '<span class="destination-address">' + destAddressString + '</span>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>' +

        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<textarea class="form-control form_element remarks" rows="2" id="cmt-area-' + bookingID + '" placeholder="Remarks...">' + comments + '</textarea>' +
        '</div>' +
        '</div>' +
        '<div class="row padded-container">' +
        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">' +
        '<button class="btn btn-xs d4m-primary-button save-cmt disabled" data="' + bookingID + '" style="float: right;">Save</button>' +
        '</div>' +
        '</div>' +
        '</div>' +
        '</div>'
    );
}

// </editor-fold>
import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str_1 = (datetime.now() - timedelta(days=8)).date().strftime("%d%m%Y")
date_str_2 = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepathH = D4M_UTIL_PATH + 'output/hyderabad-zoomcar-driver.csv'
subjectH = "Zoomcar Driver Payment - Hyderabad - " + date_str_2
filepathCH = D4M_UTIL_PATH + 'output/hyderabad-cardekho-driver.csv'
subjectCH = "Cardekho Driver Payment - Hyderabad - " + date_str_2
filepathSP = D4M_UTIL_PATH + 'output/hyderabad-spinny-driver.csv'
subjectSP = "Spinny Driver Payment - Hyderabad - " + date_str_2
filepathPH = D4M_UTIL_PATH + 'output/hyderabad-pridehonda-driver.csv'
subjectPH = "Pride Honda Driver Payment - Hyderabad - " + date_str_2
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
#send_mail(from_addr, to_addr_list, subjectH, content, filepathH)
#send_mail(from_addr, to_addr_list, subjectCD, content, filepathCD)
send_mail(from_addr, to_addr_list, subjectSP, content, filepathSP)
send_mail(from_addr, to_addr_list, subjectPH, content, filepathPH)
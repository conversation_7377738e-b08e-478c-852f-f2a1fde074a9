tags:
  - Coupon_admin
summary: Add New Coupon
description: >
  This endpoint allows admins to add a new coupon. Admin authorization is required.
parameters:
  - in: body
    name: coupon
    required: true
    description: The details of the coupon to be added.
    schema:
      type: object
      properties:
        code:
          type: string
          description: The unique code for the coupon.
          example: "SUMMER2024"
        discount:
          type: number
          format: float
          description: The discount value of the coupon.
          example: 15.5
        expiry_date:
          type: string
          format: date
          description: The expiration date of the coupon.
          example: "2024-12-31"
responses:
  201:
    description: Successfully added the new coupon.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        coupon_id:
          type: integer
          description: The ID of the newly added coupon.
          example: 124
  400:
    description: Bad request due to invalid coupon details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Invalid coupon details"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

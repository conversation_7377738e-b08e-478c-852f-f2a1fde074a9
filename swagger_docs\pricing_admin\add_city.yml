tags:
  - Pricing_admin
summary: Add City Pricing
description: >
  This endpoint allows admins to add pricing information for a specific city and trip type. 
  It requires the admin to be authorized and provides detailed fare structures for both in-city and outstation trips.
parameters:
  - name: city
    in: formData
    required: true
    type: string
    description: The name of the city for which pricing is being added.
    example: "New York"
  - name: trip_type
    in: formData
    required: true
    type: string
    description: The type of trip (inCity or outStation).
    example: "inCity"
  - name: start_night_time
    in: formData
    required: false
    type: string
    description: The start time for night fares (only for inCity trips).
    example: "20:00"
  - name: end_night_time
    in: formData
    required: false
    type: string
    description: The end time for night fares (only for inCity trips).
    example: "06:00"
  - name: part_night_time
    in: formData
    required: false
    type: string
    description: The part of the night for calculating charges (only for inCity trips).
    example: "22:00"
  - name: night_charge
    in: formData
    required: false
    type: number
    description: The charge for night trips (only for inCity trips).
    example: 50.00
  - name: part_night_charge
    in: formData
    required: false
    type: number
    description: The charge for part night trips (only for inCity trips).
    example: 25.00
  - name: min_travel_cost
    in: formData
    required: false
    type: number
    description: The minimum travel cost (only for inCity trips).
    example: 100.00
  - name: travel_cost
    in: formData
    required: false
    type: number
    description: The standard travel cost (only for inCity trips).
    example: 200.00
  - name: base_fare
    in: formData
    required: false
    type: number
    description: The base fare for the trip.
    example: 150.00
  - name: add_fare
    in: formData
    required: false
    type: number
    description: Any additional fare for the trip.
    example: 20.00
  - name: minios_base_fare
    in: formData
    required: false
    type: number
    description: The base fare for MiniOS trips (only for inCity trips).
    example: 100.00
  - name: minios_add_fare
    in: formData
    required: false
    type: number
    description: Any additional fare for MiniOS trips (only for inCity trips).
    example: 15.00
  - name: booking_percent
    in: formData
    required: false
    type: number
    description: The percentage charge for bookings.
    example: 10.00
  - name: one_way_fare
    in: formData
    required: false
    type: number
    description: The fare for one-way trips.
    example: 80.00
  - name: first_overtime
    in: formData
    required: false
    type: number
    description: The duration for the first overtime.
    example: 30
  - name: second_overtime
    in: formData
    required: false
    type: number
    description: The duration for the second overtime.
    example: 60
  - name: first_overtime_charge
    in: formData
    required: false
    type: number
    description: The charge for the first overtime.
    example: 25.00
  - name: second_overtime_charge
    in: formData
    required: false
    type: number
    description: The charge for the second overtime.
    example: 50.00
  - name: add_overtime_charge
    in: formData
    required: false
    type: number
    description: Additional charge for overtimes.
    example: 10.00
  - name: hatch_man
    in: formData
    required: false
    type: number
    description: The fare for hatchback cars (manual).
    example: 200.00
  - name: sedan_man
    in: formData
    required: false
    type: number
    description: The fare for sedan cars (manual).
    example: 250.00
  - name: suv_man
    in: formData
    required: false
    type: number
    description: The fare for SUVs (manual).
    example: 300.00
  - name: lux_man
    in: formData
    required: false
    type: number
    description: The fare for luxury cars (manual).
    example: 350.00
  - name: hatch_auto
    in: formData
    required: false
    type: number
    description: The fare for hatchback cars (automatic).
    example: 220.00
  - name: sedan_auto
    in: formData
    required: false
    type: number
    description: The fare for sedan cars (automatic).
    example: 270.00
  - name: suv_auto
    in: formData
    required: false
    type: number
    description: The fare for SUVs (automatic).
    example: 320.00
  - name: lux_auto
    in: formData
    required: false
    type: number
    description: The fare for luxury cars (automatic).
    example: 370.00
  - name: round
    in: formData
    required: false
    type: number
    description: The fare for round trips.
    example: 150.00
  - name: out_station
    in: formData
    required: false
    type: number
    description: The fare for outstation trips.
    example: 200.00
  - name: one_way
    in: formData
    required: false
    type: number
    description: The fare for one-way outstation trips.
    example: 180.00
  - name: mini_os
    in: formData
    required: false
    type: number
    description: The fare for MiniOS outstation trips.
    example: 160.00
  - name: oustation_oneway
    in: formData
    required: false
    type: number
    description: The fare for outstation one-way trips.
    example: 220.00
  - name: minios_oneway
    in: formData
    required: false
    type: number
    description: The fare for MiniOS one-way trips.
    example: 200.00
responses:
  200:
    description: Successfully added city pricing details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        msg:
          type: string
          description: Success message
          example: "inCity data added successfully for city New York"
  400:
    description: Request failed due to missing or invalid parameters
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        msg:
          type: string
          description: Error message
          example: "City/Trip missing."
  401:
    description: Unauthorized access
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  500:
    description: Failed to add pricing due to internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0

tags:
  - Trips
summary: Update Driver Rating
description: >
  This API allows the driver to update their rating for a specific trip. Only drivers are authorized to use this endpoint.
parameters:
  - name: book_id
    in: formData
    type: integer
    required: true
    description: The booking ID for which the driver rating is being submitted.
    example: 12345
  - name: driver_rating
    in: formData
    type: number
    format: float
    required: true
    description: The rating to be assigned to the driver for the trip.
    example: 4.5
responses:
  200:
    description: Rating updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for successful update)
          example: 1
        message:
          type: string
          description: Success message
          example: "Rating updated successfully"
    examples:
      application/json:
        success: 1
        message: "Rating updated successfully"
  401_a:
    description: Unauthorized role, Not a Driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized role)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: User account is restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  201_a:
    description: Invalid input or missing form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for input error)
          example: -2
        message:
          type: string
          description: Error message
          example: "Server error"
    examples:
      application/json:
        success: -2
        message: "Server error"
  201_b:
    description: Database error occurred while updating rating.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for DB error)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

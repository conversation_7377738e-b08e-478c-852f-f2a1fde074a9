from main import app
from adminnew.pricing.pricing_admin import read_redis_data, write_redis_data, convert_time_to_utc
from models import Pricing
from db_config import db
from sqlalchemy import func

# def get_redis_key_memory_usage(key):
#     redis_client = get_redis_client()
#     if redis_client is not None:
#         memory_usage = redis_client.memory_usage(key)
#         if memory_usage is not None:
#             print(f"The key '{key}' is using {memory_usage} bytes in Redis.")
#             return memory_usage
#         else:
#             print(f"The key '{key}' does not exist.")
#             return 0
#     else:
#         print("Redis client is not connected.")
#         return 0

# def format_memory_size(bytes):
#     if bytes < 1024:
#         return f"{bytes} bytes"
#     elif bytes < 1024**2:
#         return f"{bytes / 1024:.2f} KB"
#     elif bytes < 1024**3:
#         return f"{bytes / 1024**2:.2f} MB"
#     else:
#         return f"{bytes / 1024**3:.2f} GB"

# def get_redis_memory_usage():
#     memory_used = get_redis_key_memory_usage('price_config')
#     formatted_memory = format_memory_size(memory_used)
#     print(f"Memory used by 'price_config': {formatted_memory}")


def add_price_config():
    price_config = read_redis_data('price_config')

    trip_types = ["inCity", "outStation"]

    # List of cities to add
    cities = ["kolkata", "delhi", "jaipur", "hyderabad", "bangalore"]

    # Initialize each city entry with the standard structure
    price_config['cities'] = {city: {'trip_type': trip_types, 'inCity': {}, 'outStation': {}} for city in cities}

    write_redis_data('price_config', price_config)
    print("price_config added successfully")

variable_charges = [
            {
                'hour_range': "Max",
                'cust_charge': "45",
                'driver_charge': "120",
                'both_cust_charge': "99",
                'both_driver_charge': "120"
            },
            {
                'hour_range': "1",
                'cust_charge': "45",
                'driver_charge': "100",
                'both_cust_charge': "75",
                'both_driver_charge': "100"
            },
            {
                'hour_range': "2",
                'cust_charge': "30",
                'driver_charge': "75",
                'both_cust_charge': "55",
                'both_driver_charge': "75"
            },
            {
                'hour_range': "6",
                'cust_charge': "20",
                'driver_charge': "50",
                'both_cust_charge': "40",
                'both_driver_charge': "50"
            },
            {
                'hour_range': "12",
                'cust_charge': "10",
                'driver_charge': "30",
                'both_cust_charge': "25",
                'both_driver_charge': "30"
            },
            {
                'hour_range': "24",
                'cust_charge': "5",
                'driver_charge': "15",
                'both_cust_charge': "15",
                'both_driver_charge': "15"
            }
        ]

static_charges = {
            'cust_charge': "45",
            'driver_charge': "99",
            'both_cust_charge': "99",
            'both_driver_charge': "99"
        }


def add_price_incity_kolkata(city):
    city_key = f'price_{city}'
    trip_type = "inCity"
    city_data = read_redis_data(city_key)
    night_fare = {
                'start_night_time': convert_time_to_utc("23:30:00"),
                'end_night_time': convert_time_to_utc("05:30:00"),
                'part_night_time': convert_time_to_utc("22:30:00"),
                'night_charge': "100",
                'part_night_charge': "50"
            }

    # basic fare
    basic_fare = {
            'min_travel_cost': "30",
            'travel_cost': "4",
            'base_fare': "150",
            'add_fare': "74",
            'minios_base_fare': "250",
            'minios_add_fare': "74",
            'booking_percent': "8",
            'oneway_fare': "50",
            'first_overtime': "60",
            'second_overtime': "120",
            'first_overtime_charge': "90",
            'second_overtime_charge': "90",
            'add_overtime_charge': "90"
        }

    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }
    
    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "25",
            'suv_man': "50",
            'lux_man': "100",
            'hatch_auto': "20",
            'sedan_auto': "45",
            'suv_auto': "70",
            'lux_auto': "120",
            
            'hatch_ev': "0",
            'sedan_ev': "25",
            'suv_ev': "50",
            'lux_ev': "100",
            'muv_man': "0",
            'muv_auto': "20",
            'muv_ev': "30"
        }

    
    
    # insurance fare 
    insurance_fare = {
                'round': "18",
                'out_station': "50",
                'one_way': "18",
                'mini_os': "18",
                'oustation_oneway': "50",
                'minios_oneway': "18"
            }

    # Store inCity data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['inCity'] = {
        'night_details': night_fare,
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'insurance_fares': insurance_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare, 
                                        Pricing.night_fare: night_fare, 
                                        Pricing.insurance_fare: insurance_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for kolkata in-city')

def add_price_out_station_kolkata(city):
    city_key = f'price_{city}'
    city_data = read_redis_data(city_key)

    basic_fare = {
            'base_fare': "600",
            'booking_percent': "10",
            'extra_fare': "60"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }
    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "40",
            'suv_man': "80",
            'lux_man': "200",
            'hatch_auto': "30",
            'sedan_auto': "70",
            'suv_auto': "110",
            'lux_auto': "230",
            
            'hatch_ev': "0",
            'sedan_ev': "40",
            'suv_ev': "80",
            'lux_ev': "200",
            'muv_man': "0",
            'muv_auto': "30",
            'muv_ev': "40"
        }

    # Store outStation data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['outStation'] = {
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    trip_type = "outStation"
    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for kolkata out-station')

def add_price_incity_delhi(city):
    city_key = f'price_{city}'
    trip_type = "inCity"
    city_data = read_redis_data(city_key)
    night_fare = {
                'start_night_time': convert_time_to_utc("23:30:00"),
                'end_night_time': convert_time_to_utc("05:30:00"),
                'part_night_time': convert_time_to_utc("22:30:00"),
                'night_charge': "100",
                'part_night_charge': "50"
            }

    # basic fare
    basic_fare = {
            'min_travel_cost': "30",
            'travel_cost': "5",
            'base_fare': "145",
            'add_fare': "85",
            'minios_base_fare': "345",
            'minios_add_fare': "85",
            'booking_percent': "5",
            'oneway_fare': "50",
            'first_overtime': "60",
            'second_overtime': "120",
            'first_overtime_charge': "90",
            'second_overtime_charge': "90",
            'add_overtime_charge': "90"
        }
    
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30",
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }

    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "25",
            'suv_man': "50",
            'lux_man': "100",
            'hatch_auto': "20",
            'sedan_auto': "45",
            'suv_auto': "70",
            'lux_auto': "120",
            
            'hatch_ev': "0",
            'sedan_ev': "25",
            'suv_ev': "50",
            'lux_ev': "100",
            'muv_man': "0",
            'muv_auto': "20",
            'muv_ev': "30"
        }

    # insurance fare
    insurance_fare = {
                'round': "18",
                'out_station': "50",
                'one_way': "18",
                'mini_os': "18",
                'oustation_oneway': "50",
                'minios_oneway': "18"
            }


    # Store inCity data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['inCity'] = {
        'night_details': night_fare,
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'insurance_fares': insurance_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare, 
                                        Pricing.night_fare: night_fare, 
                                        Pricing.insurance_fare: insurance_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for delhi in-city')

def add_price_out_station_delhi(city):
    city_key = f'price_{city}'

    city_data = read_redis_data(city_key)

    basic_fare = {
            'base_fare': "700",
            'booking_percent': "12",
            'extra_fare': "72"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }
    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "40",
            'suv_man': "80",
            'lux_man': "200",
            'hatch_auto': "30",
            'sedan_auto': "70",
            'suv_auto': "110",
            'lux_auto': "230",
            
            'hatch_ev': "0",
            'sedan_ev': "40",
            'suv_ev': "80",
            'lux_ev': "200",
            'muv_man': "0",
            'muv_auto': "30",
            'muv_ev': "40"
        }

    # Store outStation data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['outStation'] = {
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    trip_type = "outStation"
    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for delhi out-station')

def add_price_incity_jaipur(city):
    city_key = f'price_{city}'
    trip_type = "inCity"
    city_data = read_redis_data(city_key)
    night_fare = {
                'start_night_time': convert_time_to_utc("23:30:00"),
                'end_night_time': convert_time_to_utc("05:30:00"),
                'part_night_time': convert_time_to_utc("22:30:00"),
                'night_charge': "100",
                'part_night_charge': "50"
            }

    # basic fare
    basic_fare = {
            'min_travel_cost': "80",
            'travel_cost': "3",
            'base_fare': "120",
            'add_fare': "80",
            'minios_base_fare': "300",
            'minios_add_fare': "80",
            'booking_percent': "0",
            'oneway_fare': "0",
            'first_overtime': "60",
            'second_overtime': "120",
            'first_overtime_charge': "90",
            'second_overtime_charge': "120",
            'add_overtime_charge': "120"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }

    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "25",
            'suv_man': "50",
            'lux_man': "100",
            'hatch_auto': "20",
            'sedan_auto': "45",
            'suv_auto': "70",
            'lux_auto': "120",
            
            'hatch_ev': "0",
            'sedan_ev': "25",
            'suv_ev': "50",
            'lux_ev': "100",
            'muv_man': "0",
            'muv_auto': "20",
            'muv_ev': "30"
        }

    # insurance fare
    insurance_fare = {
                'round': "18",
                'out_station': "50",
                'one_way': "18",
                'mini_os': "18",
                'oustation_oneway': "50",
                'minios_oneway': "18"
            }


    # Store inCity data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['inCity'] = {
        'night_details': night_fare,
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'insurance_fares': insurance_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare, 
                                        Pricing.night_fare: night_fare, 
                                        Pricing.insurance_fare: insurance_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for jaipur in-city')

def add_price_out_station_jaipur(city):
    city_key = f'price_{city}'

    city_data = read_redis_data(city_key)

    basic_fare = {
            'base_fare': "800",
            'booking_percent': "0",
            'extra_fare': "72"
        }
    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "40",
            'suv_man': "80",
            'lux_man': "200",
            'hatch_auto': "30",
            'sedan_auto': "70",
            'suv_auto': "110",
            'lux_auto': "230",
            
            'hatch_ev': "0",
            'sedan_ev': "40",
            'suv_ev': "80",
            'lux_ev': "200",
            'muv_man': "0",
            'muv_auto': "30",
            'muv_ev': "40"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }

    # Store outStation data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['outStation'] = {
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    trip_type = "outStation"
    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for jaipur out-station')

def add_price_incity_hyderabad(city):
    city_key = f'price_{city}'
    trip_type = "inCity"
    city_data = read_redis_data(city_key)
    night_fare = {
                'start_night_time': convert_time_to_utc("23:30:00"),
                'end_night_time': convert_time_to_utc("05:30:00"),
                'part_night_time': convert_time_to_utc("22:30:00"),
                'night_charge': "100",
                'part_night_charge': "50"
            }

    # basic fare
    basic_fare = {
            'min_travel_cost': "30",
            'travel_cost': "5",
            'base_fare': "170",
            'add_fare': "90",
            'minios_base_fare': "370",
            'minios_add_fare': "90",
            'booking_percent': "5",
            'oneway_fare': "50",
            'first_overtime': "60",
            'second_overtime': "120",
            'first_overtime_charge': "90",
            'second_overtime_charge': "90",
            'add_overtime_charge': "90"
        }

    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }

    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "25",
            'suv_man': "50",
            'lux_man': "100",
            'hatch_auto': "20",
            'sedan_auto': "45",
            'suv_auto': "70",
            'lux_auto': "120",
            
            'hatch_ev': "0",
            'sedan_ev': "25",
            'suv_ev': "50",
            'lux_ev': "100",
            'muv_man': "0",
            'muv_auto': "20",
            'muv_ev': "30"
        }

    # insurance fare
    insurance_fare = {
                'round': "18",
                'out_station': "50",
                'one_way': "18",
                'mini_os': "18",
                'oustation_oneway': "50",
                'minios_oneway': "18"
            }


    # Store inCity data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['inCity'] = {
        'night_details': night_fare,
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'insurance_fares': insurance_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare, 
                                        Pricing.night_fare: night_fare, 
                                        Pricing.insurance_fare: insurance_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})
    
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for hyderabad in-city')

def add_price_out_station_hyderabad(city):
    city_key = f'price_{city}'

    city_data = read_redis_data(city_key)

    basic_fare = {
            'base_fare': "700",
            'booking_percent': "5",
            'extra_fare': "72"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }   
    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "40",
            'suv_man': "80",
            'lux_man': "200",
            'hatch_auto': "30",
            'sedan_auto': "70",
            'suv_auto': "110",
            'lux_auto': "230",
            
            'hatch_ev': "0",
            'sedan_ev': "40",
            'suv_ev': "80",
            'lux_ev': "200",
            'muv_man': "0",
            'muv_auto': "30",
            'muv_ev': "40"
        }

    # Store outStation data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['outStation'] = {
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    trip_type = "outStation"
    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for hyderabad out-station')

def add_price_incity_bangalore(city):
    city_key = f'price_{city}'
    trip_type = "inCity"
    city_data = read_redis_data(city_key)
    night_fare = {
                'start_night_time': convert_time_to_utc("23:30:00"),
                'end_night_time': convert_time_to_utc("05:30:00"),
                'part_night_time': convert_time_to_utc("22:30:00"),
                'night_charge': "100",
                'part_night_charge': "50"
            }

    # basic fare
    basic_fare = {
            'min_travel_cost': "30",
            'travel_cost': "5",
            'base_fare': "180",
            'add_fare': "99",
            'minios_base_fare': "380",
            'minios_add_fare': "99",
            'booking_percent': "5",
            'oneway_fare': "50",
            'first_overtime': "60",
            'second_overtime': "120",
            'first_overtime_charge': "108",
            'second_overtime_charge': "108",
            'add_overtime_charge': "108"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }

    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "25",
            'suv_man': "50",
            'lux_man': "100",
            'hatch_auto': "20",
            'sedan_auto': "45",
            'suv_auto': "70",
            'lux_auto': "120",
            
            "hatch_ev": "0",
            'sedan_ev': "25",
            'suv_ev': "50",
            'lux_ev': "100",
            'muv_man': "0",
            'muv_auto': "20",
            'muv_ev': "30"
        }

    # insurance fare
    insurance_fare = {
                'round': "18",
                'out_station': "50",
                'one_way': "18",
                'mini_os': "18",
                'oustation_oneway': "50",
                'minios_oneway': "18"
            }


    # Store inCity data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['inCity'] = {
        'night_details': night_fare,
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'insurance_fares': insurance_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare, 
                                        Pricing.night_fare: night_fare, 
                                        Pricing.insurance_fare: insurance_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for bangalore in-city')

def add_price_out_station_bangalore(city):
    city_key = f'price_{city}'

    city_data = read_redis_data(city_key)

    basic_fare = {
            'base_fare': "750",
            'booking_percent': "5",
            'extra_fare': "90"
        }
    extra_fare = {
        'premium_driver_percent': "15",
        'premium_driver_flat': "100",

        'threshold_forward_night': "30", #min
        'threshold_backward_night': "30",
        'threshold_end_night_forward': "30",
        'threshold_forward_op': "30",
        'threshold_backward_op': "30"
        }
    # car fare
    car_fare = {
            'hatch_man': "0",
            'sedan_man': "40",
            'suv_man': "80",
            'lux_man': "200",
            'hatch_auto': "30",
            'sedan_auto': "70",
            'suv_auto': "110",
            'lux_auto': "230",
            
            'hatch_ev': "0",
            'sedan_ev': "40",
            'suv_ev': "80",
            'lux_ev': "200",
            'muv_man': "0",
            'muv_auto': "30",
            'muv_ev': "40"
        }

    # Store outStation data
    city_data['trip_types'] = city_data.get('trip_types', {})
    city_data['trip_types']['outStation'] = {
        'fare_details': {
            'basic_fares': basic_fare,
            'car_fares': car_fare,
            'extra_fares': extra_fare
        },
        'cancellation_details': {
            'variable_charges': variable_charges,
            'static_charges': static_charges
        }
    }

    write_redis_data(city_key, city_data)

    trip_type = "outStation"
    db.session.query(Pricing).filter(Pricing.city == city,
                                    Pricing.trip_type == trip_type,
                                    func.json_contains(Pricing.occasion, 'null').is_(True)). \
                                    update({
                                        Pricing.basic_fare: basic_fare, 
                                        Pricing.car_fare: car_fare,
                                        Pricing.variable_charges: variable_charges,
                                        Pricing.static_charges: static_charges})

    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        print('msg', "DB fail")

    print(f'data added successfully for bangalore out-station')

if __name__ == '__main__':
    with app.app_context():
        add_price_config()
        add_price_incity_kolkata("kolkata")
        add_price_out_station_kolkata("kolkata")

        add_price_incity_delhi("delhi")
        add_price_out_station_delhi("delhi")

        add_price_incity_jaipur("jaipur")
        add_price_out_station_jaipur("jaipur")

        add_price_incity_hyderabad("hyderabad")
        add_price_out_station_hyderabad("hyderabad")

        add_price_incity_bangalore("bangalore")
        add_price_out_station_bangalore("bangalore")
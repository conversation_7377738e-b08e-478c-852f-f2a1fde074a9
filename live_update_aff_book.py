from flask import request, jsonify
from db_config import db
from sqlalchemy.sql import func
from affiliate_b2b.affiliate_models import  AffBooking<PERSON><PERSON><PERSON>, AffiliateAddress,AffiliateCollections
import traceback
from _utils import combine_and_convert_to_ist, split_date_time
from models import Bookings, Users, Drivers, BookDest, BookPricing, Trip, BookPending
from sqlalchemy.orm import aliased
from booking_params import  BookingParams
from socketio_b2b import send_live_update_to_affiliate
from gevent import spawn
from redis_config import execute_with_fallback
from flask import current_app as app
#this is for testing purpose
"""
@app.route('/api/testing/affbooking_list/single_row', methods=['POST'])
def live_update_single_row_testing_aff():
    try:
        bookid = request.form.get('bookid', type=int)
        booking_region = request.form.get('booking_region', 0, type=int)
        send_live_aff_booking_table(bookid, channel='table', dest_aff_id=14, booking_region=0)
        return jsonify({'success': 1, 'data': 'api call completed'}), 200
    except  Exception as e:
        return jsonify({'success': -1, 'error': f'failed {str(e)}'}), 400
"""

def send_live_aff_booking_table(bookid, channel, dest_aff_id, booking_region):
    try:
        spawn(parallel_booking_update_send, bookid, channel, dest_aff_id, booking_region,app._get_current_object())
    except  Exception as e:
        print('Exception send_live_aff_booking_table...', flush=True)
        send_live_update_to_affiliate({'id' : bookid}, channel, dest_aff_id, booking_region)

def parallel_booking_update_send(bookid, channel, dest_aff_id, booking_region,app=None):
    try:
        with app.app_context():
            booking_data = fetch_single_booking(bookid)
            send_live_update_to_affiliate(booking_data, channel, dest_aff_id, booking_region)
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"Error in booking_list: {error_details}")
        return jsonify({'success': -1, 'error': str(e)}), 500


def fetch_single_booking(book_id, aff_id_list = None):
    driver_user = aliased(Users, name="driver_user")
    try:
        query = (
            db.session.query(
                Bookings.id.label('book_id'),
                AffBookingLogs.aff_id.label('aff_id'),
                Bookings.valid.label('book_valid'),
                Bookings.code.label("book_code"),
                Bookings.created_at.label('book_timestamp'),
                Bookings.startdate.label('book_startdate'),
                Bookings.starttime.label('book_starttime'),
                Bookings.dur.label('book_dur'),
                Bookings.car_type.label("book_car_type"),
                Bookings.estimate.label('book_estimate'),
                Bookings.estimate_pre_tax.label('book_estimate_pre_tax'),
                Bookings.region.label('book_region'),
                Bookings.days.label("book_days"),
                Bookings.loc.label('book_starting_name'),
                Bookings.lat.label('book_start_lat'),
                Bookings.long.label('book_start_long'),
                Bookings.payment_type.label('book_payment_type'),
                AffBookingLogs.comment.label('book_remark'),
                Bookings.driver.label('driver_id'),
                Trip.status.label('trip_status'),
                Trip.starttime.label('trip_starttime'),
                Trip.endtime.label('trip_endtime'),
                BookDest.name.label('book_dest_name'),
                BookDest.lat.label('book_dest_lat'),
                BookDest.lng.label('book_dest_long'),
                driver_user.mobile.label('driver_mobile'),
                driver_user.label_bv.label('driver_label'),
                driver_user.label_bv.label('driver_user'),
                func.concat(driver_user.fname, ' ', driver_user.lname).label('driver_name'),
            )
            .join(AffBookingLogs, Bookings.id == AffBookingLogs.book_id)
            .outerjoin(Trip, Bookings.id == Trip.book_id)
            .outerjoin(BookDest, Bookings.id == BookDest.book_id)
            .join(Drivers, Bookings.driver == Drivers.id)
            .join(driver_user, Drivers.user == driver_user.id)
            .outerjoin(BookPricing, Bookings.id == BookPricing.book_id)
            .filter(
                Bookings.id == book_id,
                Bookings.type == BookingParams.TYPE_B2B,
            )

        )
        if aff_id_list:
            query = query.filter(
                    AffBookingLogs.aff_id.in_(aff_id_list),
                )
        combined_query = query
        results = combined_query.all()

        # Extract all booking IDs first
        book_ids = [result.book_id for result in results]

        # Fetch all MongoDB data in a single query
        mongo_data_map = {doc["book_ref"]: doc for doc in AffiliateCollections.affiliates_book.find({"book_ref": {"$in": book_ids}})}

        # Format the results
        bookings = []
        for result in results:
            dateb, timeb = split_date_time(combine_and_convert_to_ist(result.book_startdate, result.book_starttime))
            dateb = dateb.strftime('%Y-%m-%d')
            timeb = timeb.strftime('%H:%M:%S')

            booking_data = {
                "book_id": result.book_id,
                "aff_id": result.aff_id,
                "sorttimestamp": f'{dateb}T{timeb}',
                "book_valid": result.book_valid,
                "book_code": result.book_code,
                "book_timestamp": result.book_timestamp.strftime('%d/%m/%Y %H:%M:%S'),
                "book_startdate": dateb,
                "book_starttime": timeb,
                "book_dur": result.book_dur.strftime('%H:%M:%S'),
                "book_car_type": result.book_car_type,
                "book_estimate": result.book_estimate,
                "book_estimate_pre_tax": result.book_estimate_pre_tax,
                "book_region": result.book_region,
                "book_days": result.book_days,
                "book_starting_name": result.book_starting_name,
                "book_start_lat": result.book_start_lat,
                "book_start_long": result.book_start_long,
                "book_payment_type": result.book_payment_type,
                "book_remark": result.book_remark,
                "trip_status": result.trip_status,
                "trip_starttime": result.trip_starttime.strftime('%d/%m/%Y %H:%M:%S') if result.trip_starttime else '-',
                "trip_endtime": result.trip_endtime.strftime('%d/%m/%Y %H:%M:%S') if result.trip_endtime else '-',
                "book_dest_name": result.book_dest_name,
                "book_dest_lat": result.book_dest_lat,
                "book_dest_long": result.book_dest_long,
                "driver_mobile": result.driver_mobile,
                "driver_user": result.driver_user,
                "driver_name": result.driver_name,
                "driver_label": result.driver_label,
                "driver_id": result.driver_id,
            }

            # Retrieve MongoDB data from the pre-fetched map
            mongo_data = mongo_data_map.get(result.book_id)
            if mongo_data:
                src_nickname_id = int(mongo_data.get("src_nickname_id", "0"))
                dest_nickname_id = int(mongo_data.get("dest_nickname_id", "0"))
                nickname_ids = set()
                if src_nickname_id > 0:
                    nickname_ids.add(src_nickname_id)
                if dest_nickname_id > 0:
                    nickname_ids.add(dest_nickname_id)
                nickname_addresses = db.session.query(AffiliateAddress).filter(
                        AffiliateAddress.add_id.in_(nickname_ids)
                    ).all()
                nickname_map = {address.add_id: address.nickname for address in nickname_addresses}
                src_nickname = nickname_map.get(src_nickname_id, "")
                dest_nickname = nickname_map.get(dest_nickname_id, "")
                booking_data.update({
                    "vehicle_no": mongo_data.get("vehicle_no"),
                    "client_name": mongo_data.get("client_name") if mongo_data.get("client_name") else 'Spinny',
                    "trip_name": mongo_data.get("trip_name"),
                    "trip_type": mongo_data.get("trip_type"),
                    "vehicle_model": mongo_data.get("vehicle_model", ""),
                    "book_pending_valid": mongo_data.get("pending_state", BookPending.BROADCAST),
                    "priority": mongo_data.get("priority", 0),
                    "book_starting_nickname": src_nickname,
                    "book_dest_nickname": dest_nickname
                })

            bookings.append(booking_data)

        return bookings[0]
    except ValueError:
        return {'success': -1, 'error': 'Some error in types'}
    except Exception as e:
        error_details = traceback.format_exc()
        print(f"Error in booking_list: {error_details}")
        return {'success': -1, 'error': str(e)}


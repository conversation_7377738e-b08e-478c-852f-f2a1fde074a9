tags:
  - Coupon_admin
summary: Update Coupon Details
description: >
  This endpoint allows admins to update details of an existing coupon. Admin authorization is required.
parameters:
  - in: formData
    name: coupon_id
    type: integer
    required: true
    description: The unique ID of the coupon to be updated.
    example: 123
  - in: formData
    name: coupon_code
    type: string
    required: false
    description: The new code for the coupon (optional).
    example: "DISCOUNT50"
  - in: formData
    name: percent_off
    type: number
    required: false
    description: The new percentage off (optional).
    example: 15
  - in: formData
    name: flat_off
    type: number
    required: false
    description: The new flat amount off (optional).
    example: 20
  - in: formData
    name: max_off
    type: number
    required: false
    description: The maximum amount that can be discounted (optional).
    example: 100
  - in: formData
    name: min_trip_price
    type: number
    required: false
    description: The minimum trip price required to apply the coupon (optional).
    example: 150
  - in: formData
    name: valid_till
    type: string
    format: date
    required: false
    description: The new expiration date for the coupon (optional).
    example: "2024-12-31"
  - in: formData
    name: min_trip
    type: integer
    required: false
    description: The minimum number of trips required to apply the coupon (optional).
    example: 3
  - in: formData
    name: mobile
    type: string
    required: false
    description: The mobile number associated with the coupon (optional).
    example: "9876543210"
  - in: formData
    name: redeem_limit
    type: integer
    required: false
    description: The maximum number of times the coupon can be redeemed (optional).
    example: 5
responses:
  200:
    description: Coupon updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        msg:
          type: string
          description: Success message
          example: "Coupon updated successfully"
  400:
    description: Missing or incomplete coupon ID in the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (2)
          example: 2
        msg:
          type: string
          description: Error message
          example: "coupon id not found"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  404:
    description: Coupon not found for the provided coupon ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Coupon not found"
  500:
    description: Error occurred while updating the coupon.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Error updating coupon"

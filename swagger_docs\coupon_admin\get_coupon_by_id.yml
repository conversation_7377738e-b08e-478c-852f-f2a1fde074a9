tags:
  - Coupon_admin
summary: Get Coupon by ID
description: >
  This endpoint allows admins to fetch details of a specific coupon by its ID. Admin authorization is required.
parameters:
  - in: formData
    name: coupon_id
    type: integer
    required: true
    description: The unique ID of the coupon.
    example: 123
responses:
  200:
    description: Coupon details found for the provided coupon ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: object
          properties:
            coupon_id:
              type: integer
              description: Unique identifier for the coupon
              example: 123
            coupon_code:
              type: string
              description: The coupon code
              example: "SAVE20"
            mobile:
              type: string
              description: Mobile number associated with the coupon
              example: "9876543210"
            percent_off:
              type: number
              description: Percentage off provided by the coupon
              example: 15
            max_off:
              type: number
              description: Maximum discount allowed for the coupon
              example: 100
            flat_off:
              type: number
              description: Flat amount off provided by the coupon
              example: 30
            min_trip_price:
              type: number
              description: Minimum trip price required to apply the coupon
              example: 200
            min_trip:
              type: integer
              description: Minimum number of trips required to apply the coupon
              example: 3
            validTill:
              type: string
              format: date
              description: Expiry date of the coupon
              example: "2024-12-31"
            createdAt:
              type: string
              format: date-time
              description: Date the coupon was created
              example: "2023-10-01T12:00:00Z"
            state:
              type: integer
              description: State of the coupon (1 for active, 0 for inactive)
              example: 1
  400:
    description: Missing or incomplete coupon ID in the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (2)
          example: 2
        msg:
          type: string
          description: Error message
          example: "coupon id not found"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  404:
    description: Coupon not found for the provided coupon ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Coupon not found"

#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  affiliate.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from flask import jsonify
import math
from flask import current_app as app
from booking_params import Rating
import jsonpickle
import os, shutil
from booking_params import BookingParams
from _utils_booking import get_car_type
from _ops_message import send_slack_msg
import _sms
from _utils import  strfdelta, get_pic_url
from _utils_acc import account_enabled
import uuid, json
from price import convert_to_semihours, Price
from payments import PaymentType
from datetime import datetime, timedelta
from models import DriverSearch, C24Rep, C24Bookings, BookDest, DriverDetails, C24Pic, BookPricing, BookingCancelled
from models import Bookings, Trip, Drivers, Users, UserTrans, BookPending, DriverPermaInfo
from models import db, C24Util
from register_driver import upload_pic

def convert_timedelta(duration):
    try:
        days, seconds = duration.days, duration.seconds
    except AttributeError:
        days = 0
        seconds = duration.seconds
    hours = days * 24 + seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = (seconds % 60)
    if hours < 10:
        if hours < 0: hours = 0
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    if seconds < 10:
        seconds = '0' + str(seconds)

    return str(hours), str(minutes), str(seconds)


def convert_mindelta(minutes):
    hours = minutes // 60
    minutes = minutes % 60
    if hours < 10:
        if hours < 0: hours = 0
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    seconds = "00"

    return str(hours), str(minutes), str(seconds)


class C24Price:
    base = 250
    ot_rate = 78
    os_rate = 150


class BookingC24:
    def __init__(self, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile="", driver_pic=None, loc='N/A', trip_type=1, dest_lat=0, dest_long=0, dest_loc="", comment=""):
        self.success = 1
        self.id = id
        self.appt = appt
        self.name = name
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.endtime = str(endtime)
        self.enddate = str(enddate)
        self.dur = str(dur)
        self.car_type = car_type
        if driver_pic:
            self.driver_pic = get_pic_url(driver_pic)
        self.loc = loc
        self.lat = lat
        self.long = lng
        self.mobile = mobile
        self.trip_type = trip_type
        self.dest_lat = dest_lat
        self.dest_long = dest_long
        self.dest_loc = dest_loc
        self.veh_no = veh_no
        self.veh_model = veh_model
        self.comment = comment


class BookingC24Ongoing(BookingC24):
    def __init__(self, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile="", driver_pic=None, loc='N/A', trip_type=1, dest_lat=0, dest_long=0, dest_loc="", comment="", did=1):
        super().__init__(id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile, driver_pic, loc, trip_type, dest_lat, dest_long, dest_loc, comment)
        self.driver_id = did


class BookingC24Past(BookingC24):
    def __init__(self, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile="", driver_pic=None, loc='N/A', trip_type=1, dest_lat=0, dest_long=0, dest_loc="", comment="",
                sch_date=None, sch_time=None, ot="00:00:00", file_url="", file_count=0):
        super().__init__(id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile, driver_pic, loc, trip_type, dest_lat, dest_long, dest_loc, comment)
        self.sch_startdate = sch_date
        self.sch_starttime = sch_time
        self.ot = ot
        self.file_url = file_url
        self.file_count = file_count


def calc_c24_price(book_id, book_delta, real_delta, est,
                                     book_starttime, book_stoptime,
                                     trip_starttime, trip_stoptime,
                                     startdate, enddate):
     overtime = (real_delta - book_delta)
     if overtime < 0:
         return C24Price.base
     return C24Price.base + overtime * C24Price.ot_rate / 60


def c24_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date,appt, veh_no, veh_model, c24_type, dspoc_mob, rid, region):
    now = datetime.utcnow()
    search_id = uuid.uuid4().urn[9:]
    if car_auto:
        car_type = 3
    else:
        car_type = 0
    dur = "02:00:00"
    dur_t = datetime.strptime(dur, "%H:%M:%S")
    rep = db.session.query(C24Rep).filter(C24Rep.id == rid).first()
    if not rep:
        o_rep = db.session.query(C24Rep).filter(C24Rep.owner_id == user).first()
        if not o_rep:
            rep_id = 0
        else:
            rep_id = o_rep.id
    else:
        rep_id = rep.id
    book_type = BookingParams.TYPE_C24
    if dspoc_mob == 0:
        try:
            dspoc_mob = rep_id.mobile
        except Exception:
            dspoc_mob = 0
    search_entry = DriverSearch(search_id, user, car_type, reflat, reflong, book_time, book_date, dur, now, book_type, 0, source="c24")
    db.session.add(search_entry)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day,
                                  search_entry.time.hour, search_entry.time.minute, search_entry.time.second) +
                timedelta(search_entry.days, dur_t.hour * 3600 +
                                   dur_t.minute * 60 + dur_t.second))
    # Update region when fixed
    booking = Bookings(user, search_id, BookingParams.BOOKING_DUMMY_ID, search_entry.reflat, search_entry.reflong,
                                search_entry.time,
                                search_entry.date, str(search_entry.dur), end_time.time(), end_time.date(),
                                C24Price.base, 0, loc_name,search_entry.car_type, search_entry.type, search_entry.days, payment_type=PaymentType.PAY_BILL,
                                region=Users.REGN_KOLKATA)

    db.session.add(booking)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})

    driver_list = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.alloc == DriverPermaInfo.ALLOC_C24).all()
    for d in driver_list:
        pending = BookPending(booking.id, d.driver_id,  1)
        db.session.add(pending)
    if len(driver_list) == 0:
        pending = BookPending(booking.id, BookingParams.BOOKING_DUMMY_ID, BookPending.BROADCAST)
        db.session.add(pending)
    book_pricing = BookPricing(booking.id)
    db.session.add(book_pricing)
    dest = BookDest(booking.id, dest_reflat, dest_reflong, dest_locname)
    db.session.add(dest)
    c24_booking = C24Bookings(booking=booking.id, appt=appt, no=veh_no, model=veh_model, type=c24_type, drop_mob=dspoc_mob,
                                rep=rep_id, region=region)
    db.session.add(c24_booking)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    if c24_type == 1:
        book_type = "Pickup"
    else:
        book_type = "Home Delivery"
    msg_content = "#" + str(booking.id) + ": Cars24 added a new booking of type " + book_type + " from " + loc_name + \
                    " to " + dest_locname + ". Car model is " + veh_model + " and number is " + \
                    veh_no + ". The date is " + str(book_date) + " and time is " + str(book_time) + "."
    send_slack_msg(1, msg_content)
    return jsonify({'success': 1})


def add_rep(user, rep_mob, region):
    rep = C24Rep(user=user, mob=rep_mob, regn=region)
    db.session.add(rep)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"

    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})

    return jsonify({'success': 1})


def c24_start_trip(ctf_book_id, driver_user, time_start):
    book = db.session.query(C24Bookings).filter(C24Bookings.id == ctf_book_id). \
                        first().ref

    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver) \
        .filter(Bookings.valid > 0).first()
    if booking:
        booking_id = booking.id
    else:
        return jsonify({'success': -3}), 201
    already_trip = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
    if already_trip:
        return jsonify({'success': -2}), 200  # trip exists
    new_trip = Trip(booking_id, time_start, status=trip.TRIP_STARTED)
    db.session.add(new_trip)

    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    target_user = db.session.query(Users).filter(Users.id == booking.user).first()
    start_time_ist = time_start + _sms.IST_OFFSET_TIMEDELTA
    msg_content = "Cars24 trip, id #" + str(booking.id) + " was started by " + \
                  driver_name + " at approximately " + start_time_ist.strftime("%I:%M %p %d/%m/%Y")

    send_slack_msg(2, msg_content)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"

    if fail:
        return jsonify({'success': -4, 'reason': fail_reason})

    return jsonify({'success': 1})


def _c24_newstop_trip(ctf_book_id, driver_user, time_stop):
    book = db.session.query(C24Bookings).filter(C24Bookings.id == ctf_book_id). \
                        first().ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    est = booking.estimate
    delta = time_stop - cur_trip.starttime
    # Bug - only works for 60min calcs
    orig_delta = convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0]
    time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
    estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.HOUR_RATIO + \
                     math.ceil((booking.dur.minute * Price.HOUR_RATIO) / 60)
    # Now calculate price
    price = est
    price = calc_c24_price(book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=est,
                                     book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                     trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
                                     startdate=cur_trip.starttime.date(), enddate=time_stop.date())
    if price > est:
        surcharge = True
    else:
        surcharge = False
    d_hr, d_min, d_sec = convert_timedelta(delta)
    dur = d_hr + ':' + d_min + ':' + d_sec
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: -price*100})
        Trip.query.filter(Trip.book_id == booking.id).update({
            Trip.endtime: time_stop, Trip.price: price, Trip.status: Trip.TRIP_STOPPED})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta + d_hr})

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1})

    try:
        stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
        msg_content = "Cars24 trip, id #" + str(booking.id) + " of " + \
                      driver_name + " was re-stopped at approximately " + \
                      stop_time_ist.strftime("%I:%M %p %d/%m/%Y")
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1, 'price': 0, 'dur': dur, 'surcharge': int(surcharge)})


def _c24_restart_trip(ctf_book_id, driver_user):
    book = db.session.query(C24Bookings).filter(C24Bookings.id == ctf_book_id). \
                        first().ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    orig_delta = convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0]
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: 0})
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: None, Trip.price: 0, Trip.status: Trip.TRIP_STARTED})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count - 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count - 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating - Rating.RATING_DEFAULT})
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1})

    try:
        msg_content = "Cars24 trip, id #" + str(booking.id) + " of " + \
                      driver_name + " was restarted."
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1})


def c24_stop_trip(ctf_book_id, driver_user, time_stop):
    book = db.session.query(C24Bookings).filter(C24Bookings.id == ctf_book_id). \
                        first().ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    if cur_trip.price != 0 or cur_trip.endtime:
        calc_dur = strfdelta(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
        return jsonify({'success': 1, 'price': 0, 'dur': calc_dur})
    est = booking.estimate
    delta = time_stop - cur_trip.starttime
    # Bug - only works for 60min calcs
    time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
    estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.HOUR_RATIO + \
                     math.ceil((booking.dur.minute * Price.HOUR_RATIO) / 60)
    # Now calculate price
    price = est
    price = calc_c24_price(book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=est,
                                     book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                     trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
                                     startdate=cur_trip.starttime.date(), enddate=time_stop.date())
    if price > est:
        surcharge = True
    else:
        surcharge = False
    d_hr, d_min, d_sec = convert_timedelta(delta)
    dur = d_hr + ':' + d_min + ':' + d_sec
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        trans = UserTrans(booking.user, -price*100, "Cars24 trip", UserTrans.COMPLETED, 0, stop=True)
        db.session.add(trans)
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: time_stop,
                                        Trip.trans: trans.id, Trip.due: 0, Trip.price: price, Trip.status: Trip.TRIP_STOPPED})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count + 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count + 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count + d_hr})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating + Rating.RATING_DEFAULT})

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1})

    try:
        stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
        msg_content = "Cars24 trip, id #" + str(booking.id) + " was stopped by " + \
                      driver_name + " at approximately " + \
                      stop_time_ist.strftime("%I:%M %p %d/%m/%Y")
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1, 'price': 0, 'dur': dur, 'surcharge': int(surcharge)})


def c24_past_cust(user, to_fetch, cancelled_by):
    # to_fetch: 1 -> completed, 2 -> cancelled, 3 -> both
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region or not to_fetch:
        return jsonify({'success': -1})

    # This is basically admin panel with the caveat that we fetch all C24 trips of a region
    user_region = user_region.region

    cur_dt = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

    results = db.session.query(Bookings, Drivers, Users, Trip, C24Bookings).filter(C24Bookings.region == user_region). \
        filter(Bookings.type == BookingParams.TYPE_C24). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.endtime < cur_dt). \
        filter(C24Bookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    if cancelled_by == -1:
        results_canceled = db.session.query(Bookings, Drivers, Users, C24Bookings).filter(C24Bookings.region == user_region). \
            filter(Bookings.type == BookingParams.TYPE_C24). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(C24Bookings.ref == Bookings.id). \
            filter(Bookings.valid < 0).order_by(Bookings.starttime.desc()).all()
    elif cancelled_by == 0:
        results_canceled = db.session.query(Bookings, Drivers, Users, C24Bookings).filter(C24Bookings.region == user_region). \
            filter(Bookings.type == BookingParams.TYPE_C24). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(C24Bookings.ref == Bookings.id). \
            filter(Bookings.valid == Bookings.CANCELLED_USER).order_by(Bookings.starttime.desc()).all()
    elif cancelled_by == 1:
        results_canceled = db.session.query(Bookings, Drivers, Users, C24Bookings).filter(C24Bookings.region == user_region). \
            filter(Bookings.type == BookingParams.TYPE_C24). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(C24Bookings.ref == Bookings.id). \
            filter(Bookings.valid == Bookings.CANCELLED_D4M).order_by(Bookings.starttime.desc()).all()


    result_json = []

    if to_fetch & 1:
        for result in results:
            car_type = get_car_type(result[0].id)
            if car_type < 4:
                car_type = 0
            else:
                car_type = 1
            d_hr, d_min, d_sec = convert_timedelta(result[3].endtime - result[3].starttime)
            days = float(d_hr) // 24
            d_hr_fl = float(d_hr) - days * 24
            dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
            delta = result[3].endtime - result[3].starttime
            # Bug - only works for 60min calcs
            time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
            estimate_delta = (result[0].days * 24 + result[0].dur.hour) * Price.HOUR_RATIO + \
                             math.ceil((result[0].dur.minute * Price.HOUR_RATIO) / 60)
            # OT in minutes (ideally we would have a fn for this and above but ehhh)
            overtime = time_delta - estimate_delta
            if overtime < 0:
                ot_str = "00:00:00"
            else:
                oth, otm, ots = convert_mindelta(overtime)
                ot_str = str(oth) + ':' + str(otm) + ':' + str(ots)

            dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            ctf_rep = db.session.query(C24Rep).filter(C24Rep.id == result[4].rep).first()
            try:
                driver_name = result[2].get_name()
            except TypeError:
                driver_name = ''
            file_url, file_count = c24_pic_download(result[4].id)
            res_data = BookingC24Past(id=result[4].id, appt=result[4].appt, name=driver_name,
                            startdate=result[3].starttime.strftime("%Y-%m-%d"),
                            starttime=result[3].starttime.strftime("%H:%M:%S"),
                            enddate=result[3].endtime.strftime("%Y-%m-%d"),
                            mobile=result[2].mobile,
                            endtime=result[3].endtime.strftime("%H:%M:%S"), dur=dur, car_type=car_type,
                            lat=result[0].lat, lng=result[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                            drop_spoc=result[4].drop_mob, veh_no=result[4].veh_no, veh_model=result[4].veh_model, driver_pic=result[1].pic,
                            loc=result[0].loc, trip_type=result[4].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                            sch_date=str(result[0].startdate), sch_time=str(result[0].starttime), comment=result[4].comment, ot=ot_str,
                            file_url=file_url, file_count=file_count)
            result_json.append(jsonpickle.encode(res_data))

    if to_fetch & 2 == 2:
        for result_c in results_canceled:
            car_type = get_car_type(result_c[0].id)
            dest = db.session.query(BookDest).filter(BookDest.book_id == result_c[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            try:
                driver_name = result_c[2].get_name()
            except TypeError:
                driver_name = ''
            ctf_rep = db.session.query(C24Rep).filter(C24Rep.id == result_c[3].rep).first()
            if result_c[1].id != BookingParams.BOOKING_DUMMY_ID:
                res_data = BookingC24(id=result_c[3].id, appt=result_c[3].appt, name=driver_name, startdate=result_c[0].startdate,
                        mobile=result_c[2].mobile,
                        starttime=result_c[0].starttime, enddate=result_c[0].enddate, endtime=result_c[0].endtime, dur=result_c[0].dur, car_type=car_type,
                        lat=result_c[0].lat, lng=result_c[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                        drop_spoc=result_c[3].drop_mob, veh_no=result_c[3].veh_no, veh_model=result_c[3].veh_model, driver_pic=result_c[1].pic,
                        loc=result_c[0].loc, trip_type=result_c[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc, comment=result_c[3].comment)
            else:
                res_data = BookingC24(id=result_c[3].id, appt=result_c[3].appt, name="Not Allocated", startdate=result_c[0].startdate,
                        starttime=result_c[0].starttime, enddate=result_c[0].enddate, endtime=result_c[0].endtime, dur=result_c[0].dur, car_type=car_type,
                        lat=result_c[0].lat, lng=result_c[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                        drop_spoc=result_c[3].drop_mob, veh_no=result_c[3].veh_no, veh_model=result_c[3].veh_model, driver_pic=None,
                        loc=result_c[0].loc, trip_type=result_c[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc, comment=result_c[3].comment)
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})



def c24_past_chunked(user, month, year):
    # to_fetch: 1 -> completed, 2 -> cancelled, 3 -> both

    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region:
        return jsonify({'success': -1, 'error': 0})

    # This is basically admin panel with the caveat that we fetch all C24 trips of a region
    if user_region.region == -1:
        user_region = [0, 1, 2, 3]
    else:
        user_region = [user_region.region]

    st_dt = str(year)+"-"+str(month)+"-01"
    month = month + 1
    if month > 12:
        month = 1
        year = year + 1
    end_dt = str(year)+"-"+str(month)+"-01"
    results = db.session.query(Bookings, Drivers, Users, Trip, C24Bookings).filter(C24Bookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_C24). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.starttime > st_dt). \
        filter(Trip.starttime < end_dt). \
        filter(Trip.status == 0). \
        filter(C24Bookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    result_json = []

    for result in results:
        car_type = get_car_type(result[0].id)
        if car_type < 4:
            car_type = 0
        else:
            car_type = 1
        d_hr, d_min, d_sec = convert_timedelta(result[3].endtime - result[3].starttime)
        days = float(d_hr) // 24
        d_hr_fl = float(d_hr) - days * 24
        dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
        delta = result[3].endtime - result[3].starttime
        # Bug - only works for 60min calcs
        time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
        estimate_delta = (result[0].days * 24 + result[0].dur.hour) * Price.HOUR_RATIO + \
                         math.ceil((result[0].dur.minute * Price.HOUR_RATIO) / 60)
        # OT in minutes (ideally we would have a fn for this and above but ehhh)
        overtime = time_delta - estimate_delta
        if overtime < 0:
            ot_str = "00:00:00"
        else:
            oth, otm, ots = convert_mindelta(overtime)
            ot_str = str(oth) + ':' + str(otm) + ':' + str(ots)

        dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
        dest_lat = dest.lat
        dest_long = dest.lng
        dest_loc = dest.name
        ctf_rep = db.session.query(C24Rep).filter(C24Rep.id == result[4].rep).first()
        try:
            driver_name = result[2].get_name()
        except TypeError:
            driver_name = ''
        file_url, file_count = c24_pic_download(result[4].id)
        res_data = BookingC24Past(id=result[4].id, appt=result[4].appt, name=driver_name,
                        startdate=result[3].starttime.strftime("%Y-%m-%d"),
                        starttime=result[3].starttime.strftime("%H:%M:%S"),
                        enddate=result[3].endtime.strftime("%Y-%m-%d"),
                        mobile=result[2].mobile,
                        endtime=result[3].endtime.strftime("%H:%M:%S"), dur=dur, car_type=car_type,
                        lat=result[0].lat, lng=result[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                        drop_spoc=result[4].drop_mob, veh_no=result[4].veh_no, veh_model=result[4].veh_model, driver_pic=result[1].pic,
                        loc=result[0].loc, trip_type=result[4].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                        sch_date=str(result[0].startdate), sch_time=str(result[0].starttime), comment=result[4].comment, ot=ot_str,
                        file_url=file_url, file_count=file_count)
        result_json.append(jsonpickle.encode(res_data))

    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def c24_ongoing_cust(user):
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region:
        return jsonify({'success': -1})

    # This is basically admin panel with the caveat that we fetch all C24 trips of a region
    #user_region = user_region.region
    user_region = 0
    results = db.session.query(Bookings, Drivers, Users, Trip, C24Bookings).filter(C24Bookings.region == user_region). \
        filter(Bookings.type == BookingParams.TYPE_C24). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.endtime == None). \
        filter(C24Bookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    result_json = []
    res_data = []
    for result in results:
        car_type = get_car_type(result[0].id)
        if car_type < 3:
            car_type = 0
        else:
            car_type = 1
        d_hr, d_min, d_sec = convert_timedelta(datetime.utcnow() - result[3].starttime)
        days = float(d_hr) // 24
        d_hr_fl = float(d_hr) - days * 24
        dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
        dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
        dest_lat = dest.lat
        dest_long = dest.lng
        dest_loc = dest.name
        ctf_rep = db.session.query(C24Rep).filter(C24Rep.id == result[4].rep).first()
        try:
            driver_name = result[2].get_name()
        except TypeError:
            driver_name = ''
        res_data = BookingC24Ongoing(id=result[4].id, appt=result[4].appt, name=driver_name, startdate=result[3].starttime.strftime("%Y-%m-%d"),
                starttime=result[3].starttime.strftime("%H:%M:%S"), enddate="",
                endtime="", dur=dur, car_type=car_type,
                lat=result[0].lat, lng=result[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                drop_spoc=result[4].drop_mob, veh_no=result[4].veh_no, veh_model=result[4].veh_model, driver_pic=result[1].pic,
                loc=result[0].loc, trip_type=result[4].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,mobile=result[2].mobile,
                did=result[1].id, comment=result[4].comment)
        result_json.append(jsonpickle.encode(res_data))
    print(len(result_json))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def c24_upcoming_cust(to_fetch, user):

    # to_fetch: 1 -> allocated, 2 -> new, 3 -> both
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region or not to_fetch:
        return jsonify({'success': -1})

    # This is basically admin panel with the caveat that we fetch all C24 trips of a region
    #user_region = user_region.region
    user_region = 0
    trip_book = db.session.query(Trip.book_id)
    results = db.session.query(Bookings, Drivers, Users, C24Bookings).filter(C24Bookings.region == user_region). \
        filter(Bookings.type == BookingParams.TYPE_C24). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 1). \
        filter(Bookings.driver > BookingParams.BOOKING_DUMMY_ID). \
        filter(~Bookings.id.in_(trip_book)). \
        filter(C24Bookings.ref == Bookings.id). \
        order_by(Bookings.startdate.desc(),Bookings.starttime.desc()).all()

    results_uc = db.session.query(Bookings, Drivers, Users, C24Bookings).filter(C24Bookings.region == user_region). \
        filter(Bookings.type == BookingParams.TYPE_C24). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 0). \
        filter(Bookings.driver == BookingParams.BOOKING_DUMMY_ID). \
        filter(C24Bookings.ref == Bookings.id). \
        order_by(Bookings.startdate.desc(),Bookings.starttime.desc()).all()


    result_json = []
    res_data = []

    if to_fetch & 1:
        for result in results:
            car_type = get_car_type(result[0].id)
            if car_type < 3:
                car_type = 0
            else:
                car_type = 1
            dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            ctf_rep = db.session.query(C24Rep).filter(C24Rep.id == result[3].rep).first()
            try:
                driver_name = result[2].get_name()
            except TypeError:
                driver_name = ''
            res_data = BookingC24(id=result[3].id, appt=result[3].appt, name=driver_name, startdate=result[0].startdate,
                    starttime=result[0].starttime, enddate=result[0].enddate, endtime=result[0].endtime, dur=result[0].dur,
                    car_type=car_type, lat=result[0].lat, lng=result[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                    drop_spoc=result[3].drop_mob, veh_no=result[3].veh_no, veh_model=result[3].veh_model, driver_pic=result[1].pic,
                    loc=result[0].loc, trip_type=result[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                    mobile=result[2].mobile, comment=result[3].comment)
            result_json.append(jsonpickle.encode(res_data))

    if to_fetch & 2 == 2:
        for result_uc in results_uc:
            car_type = get_car_type(result_uc[0].id)
            dest = db.session.query(BookDest).filter(BookDest.book_id == result_uc[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            ctf_rep = db.session.query(C24Rep).filter(C24Rep.id == result_uc[3].rep).first()
            try:
                driver_name = result_uc[2].get_name()
            except TypeError:
                driver_name = ''
            res_data = BookingC24(id=result_uc[3].id, appt=result_uc[3].appt, name="Not Allocated", startdate=result_uc[0].startdate,
                        starttime=result_uc[0].starttime, enddate=result_uc[0].enddate, endtime=result_uc[0].endtime, dur=result_uc[0].dur, car_type=car_type,
                        lat=result_uc[0].lat, lng=result_uc[0].long, rep_name=ctf_rep.name, rep_mob=ctf_rep.mobile,
                        drop_spoc=result_uc[3].drop_mob, veh_no=result_uc[3].veh_no, veh_model=result_uc[3].veh_model, driver_pic=None,
                        loc=result_uc[0].loc, trip_type=result_uc[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc, comment=result_uc[3].comment)
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def c24_add_rep(name, mobile, owner, region):
    c24_rep = C24Rep(name=name, user=owner, mob=mobile, regn=region)
    db.session.add(c24_rep)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


def c24_cancel(ctf_id, user):
    booking_id = db.session.query(C24Bookings).filter(C24Bookings.id == ctf_id). \
                        first().ref
    book_obj = db.session.query(Bookings).filter(Bookings.id == booking_id)
    booking = book_obj.first()
    book_obj.update({Bookings.valid: Bookings.CANCELLED_USER})
    bc = BookingCancelled(user, BookingCancelled.SRC_USER, booking.id, booking.user,
                     booking.driver, 0, 0, BookingCancelled.RSN_OTHER, "B2B cancel")
    db.session.add(bc)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


def c24_alloc_list(booking_id, all=False):
    driver_list = db.session.query(Users, Drivers, Bookings, BookPending, C24Bookings). \
                        filter(C24Bookings.id == booking_id). \
                        filter(Drivers.approved > 0). \
                        filter(Bookings.id == C24Bookings.ref).filter(BookPending.book_id == Bookings.id). \
                        filter(Drivers.id == BookPending.driver).filter(Users.id == Drivers.user)
    if all:
        driver_list =  driver_list.all()
    else:
        driver_list = driver_list.filter(BookPending.valid == BookPending.BROADCAST).all()

    result_json = []
    for driver_entry in driver_list:
        driver_obj = {}
        if driver_entry[1].id == BookingParams.BOOKING_DUMMY_ID:
            continue
        driver_obj["name"] = driver_entry[0].get_name()
        driver_obj["mobile"] = driver_entry[0].mobile
        driver_obj["id"] = driver_entry[1].id
        result_json.append(json.dumps(driver_obj))
    return jsonify({'success': 1, 'data': result_json})



def c24_allocate(ctf_book_id, driver_id):

    booking_id = db.session.query(C24Bookings).filter(C24Bookings.id == ctf_book_id). \
                        first().ref
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        # add handler if booking is not found (unlikely)
    except Exception as e:
        print(e)
        return jsonify({'success': -3}), 201
    try:
        driver = db.session.query(Users, Drivers).filter(Users.id == Drivers.user).filter(Drivers.approved ==1). \
                                    filter(Drivers.id == driver_id).first()
        # add handler if driver is not found
        if driver is None:
            return jsonify({'success': -2}), 201
    except Exception as e:
        print(e)
    try:
        book_pending = db.session.query(BookPending).filter(BookPending.book_id == booking.id).first()


        if book_pending is not None:
            try:
                db.session.query(BookPending).filter(BookPending.book_id == booking.id).update({BookPending.valid: 0})
                # get book pending entry for that driver
                db.session.query(Bookings).filter(Bookings.id == booking.id).update({Bookings.driver: driver_id,
                                                                                     Bookings.estimate: 0,
                                                                                     Bookings.valid: 1})

                db.session.commit()

                # firebase
                driver_name = driver[0].get_name()
                driver_id = driver[1].id
                #target_user = db.session.query(Users).filter(Users.id == booking.user).first()
                #user_mobile = _sms.COUNTRY_CODE_IN + str(target_user.mobile)
                start_time = datetime(year=booking.startdate.year, month=booking.startdate.month,
                                      day=booking.startdate.day, hour=booking.starttime.hour,
                                      minute=booking.starttime.minute, second=booking.starttime.second)
                start_time_ist = start_time + _sms.IST_OFFSET_TIMEDELTA
                msg_content = "A Cars24 trip with DRIVERS4ME booked for " + \
                              start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                              + " (" + str(driver[0].mobile) + ")"
                send_slack_msg(1, msg_content)
                #resp = _sms.send_msg(str(user_mobile), msg_content)
                resp = True
                driv_content = "You have been allocated a new CARS24 trip booked at " + \
                                start_time_ist.strftime("%I:%M %p %d/%m/%Y") + ". Please check your app for details."
                resp = resp & _sms.send_msg(str(driver[0].mobile), driv_content)
                if resp:
                    return jsonify({'success': 1, 'msg': 1}), 200
                else:
                    return jsonify({'success': 1, 'msg': 0}), 200
            except Exception as e:
                print(e)
                return jsonify({'success': -7}), 201
    except Exception as e:
        print(e)
        return jsonify({'success': -4}), 201


def c24_pic_upload(ctf_book_id, idx, pic):
    url_pic = upload_pic(pic, path=str(ctf_book_id))
    full_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], str(ctf_book_id))

    base_fold = full_path
    zip_base = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], 'zip')
    zip_path = os.path.join(zip_base, str(ctf_book_id))
    os.makedirs(zip_path, exist_ok=True)
    if os.path.exists(os.path.join(zip_path, str(ctf_book_id) + '.zip')):
        os.remove(os.path.join(zip_path, str(ctf_book_id) + '.zip'))

    path_base = app.config['UPLOAD_ROOT_REL'] + app.config['UPLOAD_FOLDER']
    shutil.make_archive('../' + path_base + '/zip/' + str(ctf_book_id) + '/c24_' + str(ctf_book_id), 'zip', base_fold)
    pic_reg = C24Pic(book=ctf_book_id, idx=idx, pic=url_pic)
    db.session.add(pic_reg)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason, 'msg': "DB Error"})
    return jsonify({'success': 1, 'msg': "C24 image uploaded successfully"})


def c24_fuel_upload(ctf_book_id, pic, amount):
    url_pic = upload_pic(pic, path=str(ctf_book_id))
    full_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], str(ctf_book_id))
    full_path = os.path.join(full_path, str('util'))
    base_fold = full_path
    zip_base = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], 'zip')
    zip_path = os.path.join(zip_base, str(ctf_book_id))
    zip_path = os.path.join(zip_path, str('util'))
    os.makedirs(zip_path, exist_ok=True)
    if os.path.exists(os.path.join(zip_path, str(ctf_book_id) + '.zip')):
        os.remove(os.path.join(zip_path, str(ctf_book_id) + '.zip'))

    path_base = app.config['UPLOAD_ROOT_REL'] + app.config['UPLOAD_FOLDER']
    shutil.make_archive('../' + path_base + '/zip/' + str(ctf_book_id) + '/c24_' + str(ctf_book_id) + '_util', 'zip', base_fold)
    prev_util = db.session.query(C24Util).filter(C24Util.booking_id == ctf_book_id).all()
    idx = len(prev_util) + 1
    pic_reg = C24Util(book=ctf_book_id, idx=idx, pic=url_pic, amt=int(amount))
    db.session.add(pic_reg)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})

def c24_pic_download(ctf_book_id):
    # This is a /kinda/ lazy implementation - it just zips the folder
    # We could technically do a DB lookup but this is easier (and faster!)
    path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], str(ctf_book_id))
    zip_base = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], 'zip')
    zip_path = os.path.join(zip_base, str(ctf_book_id))
    if not os.path.isdir(path):
        return "", 0
    p, d, f = os.walk(path).__next__()
    if len(f) == 0:
        return "", 0
    path_base = app.config['UPLOAD_ROOT_REL'] + app.config['UPLOAD_FOLDER']
    if not os.path.exists(os.path.join(zip_path, 'c24_' + str(ctf_book_id) + '.zip')):
        shutil.make_archive('../' + path_base + '/zip/' + str(ctf_book_id) +  '/c24_' + str(ctf_book_id), 'zip', path)
    ret_url = str(app.config['CUR_URL']) + str(app.config['UPLOAD_FOLDER']) + \
                     'zip/' + str(ctf_book_id) +'/c24_' + str(ctf_book_id) + '.zip'
    return ret_url, len(f)
tags:
  - Driver_admin
summary: Fetch Driver Trip Logs
description: >
  This endpoint retrieves the trip logs for a specific driver, allowing filtering by date range, search query, trip type, and sorting options. It also provides trip statistics like total trips, completed trips, and average user rating.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose trip logs are to be fetched.
    example: 12345
  - name: sort_by
    in: formData
    type: integer
    description: Sorting field (1 for ascending start time, 2 for descending start time, 3 for ascending user rating, 4 for descending user rating).
    example: 1
  - name: offset
    in: formData
    type: integer
    description: The starting index from which the logs are fetched.
    example: 0
  - name: limit
    in: formData
    type: integer
    description: The number of trip logs to retrieve.
    example: 10
  - name: start_date
    in: formData
    type: string
    format: date
    description: Filter trips starting from this date (YYYY-MM-DD).
    example: "2023-01-01"
  - name: end_date
    in: formData
    type: string
    format: date
    description: Filter trips up to this date (YYYY-MM-DD).
    example: "2023-12-31"
  - name: search_query
    in: formData
    type: string
    description: A search term to filter results by booking ID or booking code.
    example: "BOOK123"
  - name: book_type
    in: formData
    type: integer
    description: Filter trips by booking type (1 for type A, 2 for type B).
    example: 1
responses:
  200:
    description: Successfully retrieved driver trip logs.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              rating:
                type: number
                description: The rating given by the user for the trip.
                example: 4.5
              est_fare:
                type: number
                description: The estimated fare of the booking.
                example: 50.00
              book_id:
                type: integer
                description: The reference ID of the booking.
                example: 1234
              book_type:
                type: integer
                description: The type of the booking.
                example: 1
              loc_start:
                type: string
                description: The starting location of the trip.
                example: "Downtown"
              insurance_cost:
                type: number
                description: The cost of insurance for the trip.
                example: 5.00
              book_code:
                type: string
                description: The booking code for the trip.
                example: "BOOK123"
              book_valid:
                type: integer
                description: Indicates whether the booking is valid (1 for valid, -1 for invalid).
                example: 1
              time:
                type: string
                format: datetime
                description: The start time of the trip.
                example: "2023-10-10 12:30:00"
              est_final:
                type: number
                description: The final fare of the trip.
                example: 55.00
              trip_due:
                type: number
                description: The remaining amount due for the trip.
                example: 10.00
              trip_status:
                type: integer
                description: The status of the trip (0 for completed, 1 for ongoing).
                example: 0
              loc_end:
                type: string
                description: The destination location of the trip.
                example: "Uptown"
        total_trips:
          type: integer
          description: The total number of trips for the driver.
          example: 50
        completed_trips:
          type: integer
          description: The total number of completed trips for the driver.
          example: 45
        cancelled_trips:
          type: integer
          description: The total number of cancelled trips.
          example: 5
        avg_user_rating:
          type: number
          description: The average user rating for the driver.
          example: 4.7
  400:
    description: Invalid input parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Details about the error.
          example: "Driver ID is required"
  500:
    description: Server error during processing.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Details about the error.
          example: "An error occurred while fetching trip logs"

[general]
filename = everest.csv
query = SELECT revv_v2_book_id as everest_id, book_ref, revv_v2_appt_id as apptId,revv_v2_veh_reg as carNo, CASE when book_region = 1 THEN "Hyderabad" when book_region = 8 THEN "Bangalore" when book_region = 6 THEN "Delhi" when book_region=2 THEN "Guwahati" ELSE "Kolkata" END, CASE revv_v2_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, date(addtime(trip_start,"05:30:00")) as date, time(addtime(trip_start,"05:30:00")) as start_time, time(addtime(trip_stop,"05:30:00")) as stop_time, timediff(trip_stop,trip_start) as dur, greatest("00:00:00", timediff(timediff(trip_stop,trip_start),"02:00:00")) as ot, concat(user_fname, concat(' ', user_lname)) as driver, book_loc_name as start_loc, dest_book_name as stop_loc, revv_v2_dist, CASE when revv_v2_dist>30 THEN revv_v2_dist-30 ELSE 0 END, book_comment as d4m_comment from bookings, trip, revv_v2_bookings, drivers, users, book_dest where book_ref=revv_v2_book_ref and book_ref=trip_book and book_driver=driver_id and driver_user=user_id and dest_book_id=book_ref and month(date(addtime(trip_start,"05:30:00")))=MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and year(date(addtime(trip_start,"05:30:00")))=YEAR(CURRENT_DATE - INTERVAL 1 MONTH);
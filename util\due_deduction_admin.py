from main import app
import sys
import datetime
import ast
import pandas as pd
import os
from sqlalchemy.sql import func

sys.path.append("/app/")

from export_csv import D4M_UTIL_PATH
from booking_params import Regions
from _email import send_mail
from models import AdminLog, Users, Drivers
from db_config import db

DUE_THRESHOLD = 50
RIDE_THRESHOLD = 0

def write_to_excel(write_list, sheet_name, filename):
    filename = D4M_UTIL_PATH + 'output/' + filename
    df = pd.DataFrame(write_list)
    writer = pd.ExcelWriter(filename, engine='xlsxwriter')
    df.to_excel(writer, sheet_name=sheet_name, index=False)
    writer._save()
    return filename

if __name__ == '__main__':
    with app.app_context():
        yest = (datetime.datetime.now() + datetime.timedelta(seconds=330*60)
                - datetime.timedelta(days=1))
        q = db.session.query(AdminLog, Users). \
                    filter(AdminLog.user == Users.id). \
                    filter(AdminLog.action.like("dues%")). \
                    filter(func.date(func.addtime(AdminLog.timestamp, '05:30:00'))
                            == yest.date()).order_by(AdminLog.timestamp)
        all_due_ops = q.all()
        date_str = yest.strftime("%d%m%Y")
        from_addr = "<EMAIL>"
        to_addr_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        admin_op_list = []
        filename = "admin-due-ops-" + date_str + ".xlsx"

        for r in all_due_ops:
            due_e = dict()
            action, admin = r
            due_e["Admin Name"] = admin.get_name()
            content = action.content.replace("ImmutableMultiDict", "").strip()[1:-1]
            content = ast.literal_eval(content)
            for k, v in dict(content).items():
                if k in ["driver_id", "some_other_numeric_key"]:  # Add other keys as needed
                    try:
                        due_e[k] = int(v)
                    except ValueError:
                        print(f"Warning: Non-integer value for key '{k}': {v}")
                        due_e[k] = v  # Handle as needed
                else:
                    due_e[k] = v  # Keep the original value for non-numeric keys
            driver_info = db.session.query(Drivers, Users). \
                            filter(Drivers.user == Users.id). \
                            filter(Drivers.id == due_e["driver_id"]). \
                            first()
            due_e["driver_name"] = driver_info[1].get_name()
            due_e["driver_region"] = Regions.to_string(driver_info[1].region)
            due_e["driver_mobile"] = driver_info[1].mobile
            due_e["timestamp"] = str(action.timestamp +
                                    datetime.timedelta(seconds=330*60))
            admin_op_list.append(due_e)

        filepath = write_to_excel(admin_op_list, "admin-due-ops", filename)
        subject = "Admin due ops - " + date_str
        content = ("Please find all actions taken by admin on dues yesterday")
        attachment = filepath
        send_mail(from_addr, to_addr_list, subject, content, attachment)
        # Delete the attachment after sending the email
        try:
            os.remove(filepath)
            print(f"Deleted the file: {filepath}")
        except OSError as e:
            print(f"Error: {filepath} : {e.strerror}")
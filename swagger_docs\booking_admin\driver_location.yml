tags:
  - Booking_admin
summary: Fetch current driver location
description: >
  This endpoint retrieves the current location of a specific driver based on their ID.
parameters:
  - name: driver_id
    in: formData
    type: integer
    required: true
    description: The ID of the driver whose location is to be fetched.
    example: 123
responses:
  200:
    description: Successfully fetched driver location.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        data:
          type: object
          properties:
            cur_lat:
              type: number
              format: float
              example: 34.0522
            cur_lng:
              type: number
              format: float
              example: -118.2437
  400:
    description: Bad request, missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        error:
          type: string
          example: "driver_id parameter is required"
  404:
    description: Driver location not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        error:
          type: string
          example: "Driver not found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"

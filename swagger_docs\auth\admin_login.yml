tags:
  - Authentication
summary: Admin Login
description: Admin login with mobile and password.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the admin user
  - name: pwd
    in: formData
    type: string
    required: true
    description: Password of the admin user
responses:
  400:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: 0
        message: "Incomplete form details"
  403_a:
    description: Forbidden access for this role
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Forbidden access for this role"
    examples:
      application/json:
        success: 0
        message: "Forbidden access for this role"
  401_a:
    description: User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "User not found"
    examples:
      application/json:
        success: 0
        message: "User not found"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: 0
        message: "User restricted"
  403_b:
    description: Admin access not found for this user
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Admin access not found for this user"
    examples:
      application/json:
        success: 0
        message: "Admin access not found for this user"
  401_c:
    description: Invalid password
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Invalid password"
    examples:
      application/json:
        success: 0
        message: "Invalid password"
  200:
    description: Successful login
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        user_fname:
          type: string
          example: "John"
        user_mobile:
          type: string
          example: "9876543210"
        user_email:
          type: string
          example: "<EMAIL>"
        user_lname:
          type: string
          example: "Doe"
        user_region:
          type: string
          example: "5"
        tabs:
          type: string
          example: "0,1,2"
        regions:
          type: string
          example: "1,2,3"
        notification:
          type: string
          example: "0,1"
        id:
          type: integer
          example: 123
        code:
          type: string
          example: "sample_refresh_token"
        role:
          type: integer
          example: 1
        csrf:
          type: string
          example: "sample_csrf_token"
    examples:
      application/json:
        success: 1
        user_fname: "John"
        user_mobile: "9876543210"
        user_email: "<EMAIL>"
        user_lname: "Doe"
        user_region: "5"
        tabs: "0,1,2"
        regions: "1,2,3"
        notification: "0,1"
        id: 123
        code: "sample_refresh_token"
        role: 1
        csrf: "sample_csrf_token"
  401_d:
    description: Database error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: 0
        message: "DB Error"
  500:
    description: Server error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        message:
          type: string
          example: "Error description"
    examples:
      application/json:
        success: -5
        message: "Error description"

#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  users.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime
from flasgger import swag_from
import jsonpickle, math
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt, get_jwt_identity
)
from _utils import complete, get_pic_url, get_safe, get_dt_ist
from _utils_acc import account_enabled, validate_role
from sqlalchemy import or_, and_, func
from booking_params import BookingParams,Regions
from _utils_booking import get_car_type, booking_has_trip
from price import PriceOutstation, Price
from _utils import strfdelta2, get_pic_url, complete, get_dt_ist
from _utils_acc import account_enabled
from trip import convert_to_semihours
from models import BookingCancelled, Users, Drivers, Bookings, Trip, BookDest, TripLog, DeletedUser
from models import db, UserCancelled, UserTrans, UserLoc, BookPricing, ReferralUse,UserRegistrationDetails
from _utils import get_city_by_lat_long
from flasgger import swag_from
from socketio_app import live_update_to_channel

users = Blueprint('users', __name__)

def user_display(rating):
    show = round(0.0268092*rating*rating+0.*********rating***********, 2)
    if rating < show:
        return show
    else: return rating

class UserTransWrap:
    def __init__(self, amt, dt):
        self.amt = amt
        self.credit = amt > 0
        self.text = "Credit" if self.credit else "Debit"
        self.timestamp = str(dt)


class BookingUser:
    def __init__(self, id, book_code, name, startdate, starttime, enddate, endtime, dur, est, car_type, lat, lng, driver_pic=None,
                 loc='N/A', trip_type=1, days=0, rating=5, dest_lat=0, dest_long=0, dest_loc="", payment=0, transf=True,canc_ch=0,
                 pre_tax=0, cgst=0, sgst=0, insurance=0, insurance_ch=0):
        self.success = 1
        self.id = id
        self.code = book_code
        self.driver_name = name
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.endtime = str(endtime)
        self.enddate = str(enddate)
        self.dur = str(dur)
        self.estimate = round(est, 2)
        self.car_type = car_type
        if driver_pic:
            self.driver_pic = get_pic_url(driver_pic)
        if loc:
            self.loc = loc
        else:
            self.loc = 'N/A'
        self.lat = lat
        self.long = lng
        self.trip_type = trip_type
        self.days = days
        if not transf:
            self.rating = rating
        else:
            self.rating = user_display(rating)
        self.dest_lat = dest_lat
        self.dest_long = dest_long
        self.dest_loc = dest_loc
        self.payment = payment
        self.canc_ch = canc_ch
        self.sgst = round(sgst)
        self.cgst = round(cgst)
        self.pre_tax = round(pre_tax)
        self.sgst_pct = Price.SGST
        self.cgst_pct = Price.CGST
        self.insurance = insurance
        self.insurance_ch = insurance_ch


class BookingUserDet:
    def __init__(self, id, book_code, name, startdate, starttime, enddate, endtime, dur, estimate, book_ch, food, night, base_ch,
                 cartype_ch, dist_ch, car_type, lat, lng, rating, license, estimate_max=0, dist_ch_max=0,
                 driver_pic=None, loc='N/A', mobile="", did_no="", trip_type=1, days=0, dest_lat=0, dest_long=0, dest_loc="", payment=0, transf=True, insurance=0,
                 otp=""):
        self.id = id
        self.code = book_code
        self.driver_name = name
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.endtime = str(endtime)
        self.enddate = str(enddate)
        self.dur = str(dur)
        self.estimate = round(estimate, 2)
        self.book_ch = 0
        self.food_ch = food
        self.night_ch = night
        self.base_ch = base_ch + book_ch
        self.cartype_ch = cartype_ch
        if not transf:
            self.rating = rating
        else:
            self.rating = user_display(rating)
        self.license = license
        self.dist_ch = dist_ch
        self.car_type = car_type
        if estimate_max:
            self.estimate_max = estimate_max
        else:
            self.estimate_max = estimate
        if dist_ch_max:
            self.dist_ch_max = dist_ch_max
        else:
            self.dist_ch_max = dist_ch
        if driver_pic:
            self.driver_pic = get_pic_url(driver_pic)
        self.loc = loc
        self.mobile = mobile
        self.did_no = did_no
        self.lat = lat
        self.long = lng
        self.trip_type = trip_type
        self.days = days
        self.dest_lat = dest_lat
        self.dest_long = dest_long
        self.dest_loc = dest_loc
        self.payment = payment
        self.insurance = insurance
        self.otp = str(otp)


class BookingUserTrip(BookingUserDet):
    def __init__(self, trip_id, booking_id, book_code, name, startdate, starttime, enddate, endtime, dur, estimate, book_ch, food,
                 night, base_ch, cartype_ch, dist_ch, car_type, lat, lng, rating, license, elapsed, estimate_max=0,
                 dist_ch_max=0, driver_pic=None, loc='N/A', mobile="", did_no="", trip_type=1, days=0, surcharge=True, payment=0,
                 surcharge_del=0, ot=False, ot_rates="", insurance=0, status=0, otp=""):
        super().__init__(id=booking_id, book_code=book_code, name=name, startdate=startdate, starttime=starttime, endtime=endtime,
                         enddate=enddate, dur=dur, estimate=estimate, book_ch=book_ch, food=food, night=night,
                         base_ch=base_ch, cartype_ch=cartype_ch, dist_ch=dist_ch, car_type=car_type, lat=lat,
                         lng=lng, rating=rating, license=license, estimate_max=estimate_max, dist_ch_max=dist_ch_max,
                         driver_pic=driver_pic, loc=loc, mobile=mobile, did_no=did_no, trip_type=trip_type, days=days, payment=payment, insurance=insurance,
                         otp=otp)
        self.trip_id = trip_id
        self.elapsed = str(elapsed)
        self.surcharge = surcharge
        self.surcharge_del = surcharge_del
        self.ot = ot
        self.ot_rates = ot_rates
        self.status = status
        self.otp = otp


def _switch_payment(book):
    booking = db.session.query(Bookings).filter(Bookings.id == book)
    if booking and booking.first():
        booking.update({Bookings.payment_type: int(not booking.first().payment_type)})

        try:
            db.session.commit()
            return jsonify({'success': 1, 'message': 'Payment method switched successfully'})
        except Exception:
            return jsonify({'success': -1, 'message': 'Failed to switch payment method'})
    else:
        return jsonify({'success': 0, 'message': 'Booking not found'})


@users.route('/api/user/pending_ride', methods=['POST'])
@swag_from('/app/swagger_docs/users/pending_ride.yml')
@jwt_required()
def pending_conf():
    user = get_jwt_identity()
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    cur_dt = datetime.datetime.utcnow() - datetime.timedelta(seconds=54000)
    curdate = cur_dt.strftime("%Y-%m-%d")
    curtime = cur_dt.strftime("%H:%M:%S")
    results = db.session.query(Bookings, Drivers, Users, BookPricing).filter(Bookings.user == user). \
        filter(BookPricing.book_id == Bookings.id). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 1). \
        filter(Bookings.driver > 1). \
        filter(Bookings.type < BookingParams.TYPE_C24). \
        filter(or_(Bookings.startdate > curdate,
                   and_(Bookings.startdate == curdate, Bookings.starttime > curtime))).all()
    results_unconf = db.session.query(Bookings, BookPricing).filter(Bookings.user == user). \
        filter(BookPricing.book_id == Bookings.id). \
        filter(Bookings.valid == 0). \
        filter(Bookings.driver == 1). \
        filter(Bookings.type < BookingParams.TYPE_C24). \
        filter(or_(Bookings.startdate > curdate,
                   and_(Bookings.startdate == curdate, Bookings.starttime > curtime))).all()
    result_json = []
    for result in results:
        if not booking_has_trip(result[0].id):
            car_type = get_car_type(result[0].id)
            dest_lat = 0
            dest_long = 0
            dest_loc = ""
            if result[0].type ==  BookingParams.TYPE_OUTSTATION or result[0].type == BookingParams.TYPE_ONEWAY or result[0].type == BookingParams.TYPE_MINIOS \
                or result[0].type ==  BookingParams.TYPE_OUTSTATION_ONEWAY or result[0].type ==  BookingParams.TYPE_MINIOS_ONEWAY:
                try:
                    dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
                    dest_lat = dest.lat
                    dest_long = dest.lng
                    dest_loc = dest.name
                except Exception:
                    pass
            res_data = BookingUserDet(id=result[0].id, book_code=result[0].code, name=result[2].get_name(),
                                      startdate=result[0].startdate, starttime=result[0].starttime,
                                      enddate=result[0].enddate, endtime=result[0].endtime,
                                      dur=result[0].dur, estimate=result[0].estimate, book_ch=result[3].booking_ch,
                                      food=result[3].food_ch, night=result[3].night_ch,
                                      base_ch=result[3].base_ch, car_type=car_type, cartype_ch=result[3].cartype_ch,
                                      dist_ch=result[3].dist_ch, dist_ch_max=0, estimate_max=0, driver_pic=result[1].pic,
                                      loc=result[0].loc, mobile=result[2].mobile, did_no=result[0].did, lat=result[0].lat, lng=result[0].long,
                                      rating=result[1].rating, license=result[1].licenseNo, trip_type=result[0].type,
                                      days=result[0].days, dest_lat=dest_lat, dest_long=dest_long,
                                      dest_loc=dest_loc, payment=result[0].payment_type, insurance=result[0].insurance,
                                      otp=result[0].otp)
            result_json.append(jsonpickle.encode(res_data))
    book_set = {}
    for result_u in results_unconf:
        try:
            book_set[result_u[0].id]
        except KeyError:
            car_type = get_car_type(result_u[0].id)
            dest_lat = 0
            dest_long = 0
            dest_loc = ""
            if result_u[0].type ==  BookingParams.TYPE_OUTSTATION or result_u[0].type == BookingParams.TYPE_ONEWAY or result_u[0].type == BookingParams.TYPE_MINIOS \
                or result_u[0].type ==  BookingParams.TYPE_OUTSTATION_ONEWAY or result_u[0].type ==  BookingParams.TYPE_MINIOS_ONEWAY:
                try:
                    dest = db.session.query(BookDest).filter(BookDest.book_id == result_u[0].id).first()
                    dest_lat = dest.lat
                    dest_long = dest.lng
                    dest_loc = dest.name
                except Exception:
                    pass
            res_data = BookingUserDet(id=result_u[0].id, book_code=result_u[0].code, name="Not Allocated", startdate=result_u[0].startdate,
                                      starttime=result_u[0].starttime, enddate=result_u[0].enddate,
                                      endtime=result_u[0].endtime, dur=result_u[0].dur,
                                      estimate=result_u[1].estimate,
                                      book_ch=result_u[1].booking_ch,
                                      food=result_u[1].food_ch,
                                      night=result_u[1].night_ch,
                                      base_ch=result_u[1].base_ch, cartype_ch=result_u[1].cartype_ch, dist_ch=0,
                                      car_type=car_type,
                                      estimate_max=result_u[1].estimate,
                                      dist_ch_max= result_u[1].dist_ch,
                                      loc=result_u[0].loc,
                                      driver_pic=None, mobile="", did_no="", lat=result_u[0].lat, lng=result_u[0].long,
                                      rating=0, license='', trip_type=result_u[0].type,
                                      days=result_u[0].days, dest_lat=dest_lat, dest_long=dest_long,
                                      dest_loc=dest_loc,payment=result_u[0].payment_type, insurance=result_u[0].insurance)
            book_set[result_u[0].id] = 1
            result_json.append(jsonpickle.encode(res_data))

    if len(result_json) <= 0:
        return jsonify({'success': - 1, 'message': 'Pending rides not found'}), 200
    return jsonify({'success': 1, 'data': result_json, 'message': 'Pending rides fetched successfully'})


@users.route('/api/user/past_ride', methods=['POST'])
@swag_from('/app/swagger_docs/users/past_rides.yml')
@jwt_required()
def past_cust():
    user = get_jwt_identity()
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    curdate = datetime.datetime.utcnow().strftime("%Y-%m-%d")
    curtime = datetime.datetime.utcnow().strftime("%H:%M:%S")
    cur_dt = datetime.datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")
    results = db.session.query(Bookings, Drivers, Users, Trip).filter(Bookings.user == user). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.endtime < cur_dt). \
        filter(Bookings.type < BookingParams.TYPE_C24). \
        order_by(Trip.starttime.desc()). \
        limit(75).all()
    results_canceled = db.session.query(Bookings, Drivers, Users).filter(Bookings.user == user). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.type < BookingParams.TYPE_C24). \
        filter(Bookings.valid < 0). \
        order_by(Bookings.startdate.desc()). \
        order_by(Bookings.starttime.desc()). \
        limit(50).all()
    result_json = []
    for result in results:
        car_type = get_car_type(result[0].id)
        from trip import convert_timedelta
        d_hr, d_min, d_sec = convert_timedelta(result[3].endtime - result[3].starttime)
        try:
            days = float(d_hr) // 24
            d_hr_fl = float(d_hr) - days * 24
            dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
        except Exception:
            print("Negative time", d_hr, d_min, d_sec)
            continue
        rating = result[0].user_rating
        dest_lat = 0
        dest_long = 0
        dest_loc = ""
        if result[0].type ==  BookingParams.TYPE_OUTSTATION or result[0].type == BookingParams.TYPE_ONEWAY or result[0].type == BookingParams.TYPE_MINIOS \
            or result[0].type ==  BookingParams.TYPE_OUTSTATION_ONEWAY or result[0].type ==  BookingParams.TYPE_MINIOS_ONEWAY:
            try:
                dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
                dest_lat = dest.lat
                dest_long = dest.lng
                dest_loc = dest.name
            except Exception:
                pass
        res_data = BookingUser(id=result[0].id, book_code=result[0].code, name=result[2].get_name(),
                               startdate=result[3].starttime.strftime("%Y-%m-%d"),
                               starttime=result[3].starttime.strftime("%H:%M:%S"),
                               enddate=result[3].endtime.strftime("%Y-%m-%d"),
                               endtime=result[3].endtime.strftime("%H:%M:%S"),
                               dur=dur, est=result[3].price, car_type=car_type,
                               driver_pic=result[1].pic, loc=result[0].loc, lat=result[0].lat,
                               lng=result[0].long, trip_type=result[0].type,
                               days=int(days), rating=rating, dest_lat=dest_lat,
                               dest_long=dest_long, dest_loc=dest_loc, transf=False,payment=result[0].payment_type,
                               pre_tax=result[3].price_pre_tax, cgst=result[3].cgst, sgst=result[3].sgst, insurance=result[0].insurance, insurance_ch=result[0].insurance_cost)
        result_json.append(jsonpickle.encode(res_data))
    dest_lat = 0
    dest_long = 0
    dest_loc = ""
    for result_c in results_canceled:
        car_type = get_car_type(result_c[0].id)
        if result_c[0].type ==  BookingParams.TYPE_OUTSTATION or result_c[0].type == BookingParams.TYPE_ONEWAY or result_c[0].type == BookingParams.TYPE_MINIOS \
            or result_c[0].type ==  BookingParams.TYPE_OUTSTATION_ONEWAY or result_c[0].type ==  BookingParams.TYPE_MINIOS_ONEWAY:
            try:
                dest = db.session.query(BookDest).filter(BookDest.book_id == result_c[0].id).first()
                dest_lat = dest.lat
                dest_long = dest.lng
                dest_loc = dest.name
            except Exception:
                pass
        try:
            cancel = db.session.query(UserCancelled).filter(UserCancelled.booking == result_c[0].id).first()
            if cancel:
                c_ch = cancel.penalty
            else:
                c_ch = 0
        except Exception:
            c_ch = 0
        if result_c[1].id != BookingParams.BOOKING_DUMMY_ID:
            res_data = BookingUser(id=result_c[0].id, book_code=result_c[0].code, name=result_c[2].get_name(),
                                   startdate=result_c[0].startdate, starttime=result_c[0].starttime,
                                   enddate=result_c[0].enddate, endtime=result_c[0].endtime,
                                   dur=result_c[0].dur, est=0, car_type=car_type,
                                   driver_pic=result_c[1].pic, loc=result_c[0].loc,
                                   lat=result_c[0].lat, lng=result_c[0].long, trip_type=result_c[0].type,
                                   days=result_c[0].days, rating=5, dest_lat=dest_lat,
                                   dest_long=dest_long, dest_loc=dest_loc,payment=result_c[0].payment_type,
                                   canc_ch=c_ch, insurance=result_c[0].insurance, insurance_ch=result_c[0].insurance_cost)
        else:
            res_data = BookingUser(id=result_c[0].id, book_code=result_c[0].code, name="Not Allocated",
                                   startdate=result_c[0].startdate, starttime=result_c[0].starttime,
                                   enddate=result_c[0].enddate, endtime=result_c[0].endtime,
                                   dur=result_c[0].dur, est=0, car_type=car_type,
                                   driver_pic=None, loc=result_c[0].loc,
                                   lat=result_c[0].lat, lng=result_c[0].long, trip_type=result_c[0].type,
                                   days=result_c[0].days, rating=5, dest_lat=dest_lat,
                                   dest_long=dest_long, dest_loc=dest_loc,payment=result_c[0].payment_type, canc_ch=c_ch,
                                   insurance=result_c[0].insurance, insurance_ch=result_c[0].insurance_cost)
        result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1, 'message': 'No past trips'})
    return jsonify({'success': 1, 'data': result_json, 'message': 'All past trips fetched successfully'})

@users.route('/api/user/ongoing', methods=['POST'])
@swag_from('/app/swagger_docs/trips/ongoing_trip.yml')
@jwt_required()
def ongoing_trip_user():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    cur_trip = db.session.query(Bookings, Trip, Drivers, Users).filter(Bookings.id == Trip.book_id). \
        filter(Bookings.user == user_id).filter(Trip.endtime == None).filter(Bookings.driver == Drivers.id). \
        filter(Bookings.type < BookingParams.TYPE_C24). \
        filter(Users.id == Drivers.user).order_by(Trip.starttime.desc()).first()
    not_started = False
    try:
        trip_id = cur_trip[1].id
        book_id = cur_trip[0].id
        if not cur_trip[1].starttime:
            not_started = True
            tl = db.session.query(TripLog). \
                filter(TripLog.booking_id == cur_trip[0].id). \
                filter(TripLog.action == TripLog.ACTION_INIT).first()
            if tl:
                checkin_time = tl.timestamp
            else:
                checkin_time = datetime.datetime.utcnow()
            cur_trip[1].starttime =  datetime.datetime.utcnow()

        elapsed_dur = strfdelta2(datetime.datetime.utcnow() - cur_trip[1].starttime, "{hours}:{minutes}:{seconds}")
        dur = cur_trip[0].dur
        starttime = cur_trip[1].starttime.strftime("%H:%M:%S")
        startdate = cur_trip[1].starttime.strftime("%Y-%m-%d")
        enddate = None
        endtime = None
        lat = cur_trip[0].lat
        long = cur_trip[0].long
        est = cur_trip[0].estimate
        stop_time = datetime.datetime.utcnow()
        delta = stop_time - cur_trip[1].starttime
        # Bug - only works for 60min calcs
        time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
        estimate_delta = (cur_trip[0].days * 24 + cur_trip[0].dur.hour) * Price.HOUR_RATIO + \
                         math.ceil((cur_trip[0].dur.minute * Price.HOUR_RATIO) / 60)
        # Now calculate price
        price = est
        pre_tax = cur_trip[0].estimate_pre_tax
        if pre_tax == 0:
            pre_tax = int(0.95*est)
        if cur_trip[0].type ==  BookingParams.TYPE_ROUNDTRIP or cur_trip[0].type == BookingParams.TYPE_ONEWAY or cur_trip[0].type == BookingParams.TYPE_MINIOS \
            or cur_trip[0].type ==  BookingParams.TYPE_MINIOS_ONEWAY:
            price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = Price.get_trip_price(book_id=cur_trip[0].id, book_delta=estimate_delta, real_delta=time_delta, est=pre_tax,
                                         book_starttime=cur_trip[0].starttime, book_stoptime=cur_trip[0].endtime,
                                         trip_starttime=cur_trip[1].starttime.time(), trip_stoptime=stop_time.time(),
                                         startdate=cur_trip[1].starttime.date(), enddate=stop_time.date(), insurance_ch=cur_trip[0].insurance_cost,
                                         city=cur_trip[0].region, type=cur_trip[0].type)
            ot_rates = Price.get_ot_rates(cur_trip[1].starttime.date(), stop_time.date())
        elif cur_trip[0].type == BookingParams.TYPE_OUTSTATION or cur_trip[0].type ==  BookingParams.TYPE_OUTSTATION_ONEWAY:
            price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = PriceOutstation.get_trip_price(startdate=cur_trip[1].starttime.date(), enddate=stop_time.date(),
                                                    book_delta=estimate_delta, real_delta=time_delta, est=pre_tax, insurance_ch=cur_trip[0].insurance_cost,
                                                    city=cur_trip[0].region)
            ot_rates = PriceOutstation.get_ot_rates(cur_trip[1].starttime.date(), stop_time.date())
        if price > est:
            surcharge = True
            surcharge_del = price - est
        else:
            surcharge = False
            surcharge_del = 0
        if time_delta > estimate_delta:
            ot = True
        else:
            ot = False
        estimate = cur_trip[0].estimate
        car_type = get_car_type(book_id)
        loc = cur_trip[0].loc
        fare_breakup = db.session.query(BookPricing).filter(BookPricing.book_id == book_id).first()
        if fare_breakup:
            base_ch = fare_breakup.base_ch
            book_ch = fare_breakup.booking_ch
            cartype_ch = fare_breakup.cartype_ch
            night_ch = fare_breakup.night_ch
            dist_ch = fare_breakup.dist_ch
            food = 0
        else:
            base_ch = book_ch = cartype_ch = night_ch = dist_ch = food = 0
        try:
            user = cur_trip[3]
            mobile = user.mobile
            did_no = cur_trip[0].did 
            name = user.get_name()
            driver = cur_trip[2]
            license = driver.licenseNo
            pic = driver.pic
            rating = driver.rating
        except (AttributeError, TypeError):
            name = ''
            mobile = ''
            did_no = ''
            pic = None
            license = ''
            rating = 5
        if not_started:
            starttime = checkin_time.strftime("%H:%M:%S")
            startdate = checkin_time.strftime("%Y-%m-%d")
            elapsed_dur = dur
        driver_trip = BookingUserTrip(trip_id=trip_id, booking_id=book_id, book_code=cur_trip[0].code, dur=dur, starttime=starttime,
                                      startdate=startdate, enddate=enddate, endtime=endtime, lat=lat,
                                      lng=long, estimate=price, loc=loc,
                                      mobile=mobile, did_no=did_no, car_type=car_type, name=name,
                                      base_ch=base_ch, cartype_ch=cartype_ch, night=night_ch,
                                      food=food, book_ch=book_ch, dist_ch=dist_ch, license=license,
                                      driver_pic=pic, rating=rating, elapsed=elapsed_dur, trip_type=cur_trip[0].type,
                                      days=cur_trip[0].days, surcharge=surcharge, surcharge_del=surcharge_del,
                                      ot=ot, ot_rates=ot_rates, payment=cur_trip[0].payment_type,
                                      insurance=cur_trip[0].insurance, status=cur_trip[1].status, otp=cur_trip[0].otp)
        print("Response", jsonpickle.encode(driver_trip))
    except Exception as e:
        print(e)
        return jsonify({'success': -1, 'message': 'Server Error'})

    return jsonify({'success': 1, 'data': jsonpickle.encode(driver_trip), 'message': 'Fetched ongoing trip successfully'}), 200


@users.route('/api/user/restore_id/get', methods=['POST'])
@swag_from('/app/swagger_docs/users/get_restore_id.yml')
@jwt_required()
def get_restore_id():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    user = db.session.query(Users).filter(Users.id==user_id).first()
    if not user:
        return jsonify({'success': -1, 'message': 'User not found'}), 500
    if not user.restore_id:
        return jsonify({'success': -1, 'message': 'Restore ID not found for this user'}), 404
    return jsonify({'success': 1, 'restore_id': user.restore_id, 'message': 'Restore ID fetched successfully'}), 200


@users.route('/api/user/restore_id/set', methods=['POST'])
@swag_from('/app/swagger_docs/users/set_restore_id.yml')
@jwt_required()
def set_restore_id():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401

    user = db.session.query(Users).filter(Users.id==user_id)
    if not user.first():
        return jsonify({'success': -1, 'message': 'User not found'}), 500
        return jsonify({'success': -1, 'message': 'User not found'}), 500
    if not complete(request.form, ['restore_id']):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201
    user.update({Users.restore_id: request.form['restore_id']})
    db.session.commit()
    return jsonify({'success': 1, 'restore_id': user.first().restore_id, 'message': 'Restore ID set successfully'})


@users.route('/api/user/trans_log', methods=['POST'])
@swag_from('/app/swagger_docs/payments/user_trans_log.yml')
@jwt_required()
def user_trans_log():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    user = db.session.query(Users).filter(Users.id==user_id)
    if not user.first():
        return jsonify({'success': -1, 'message': 'User not found'}), 500
    credit = user.first().credit
    user_trans = db.session.query(UserTrans).filter(UserTrans.user_id==user_id). \
                    filter(func.abs(UserTrans.amount) > 0).filter(UserTrans.status==UserTrans.COMPLETED). \
                    all()
    if not user_trans:
        return jsonify({'success': -1, 'message': 'User transaction not found'}), 200
    result_json = []
    for ut in user_trans:
        res_data = UserTransWrap(amt=round(ut.amount/100,2),dt=get_dt_ist(ut.start_timestamp, ut.start_timestamp))
        result_json.append(jsonpickle.encode(res_data))
    return jsonify({'success': 1, 'balance': round(credit, 2), 'data': result_json})

@users.route('/api/user/set_loc', methods=['POST'])
@swag_from('/app/swagger_docs/users/set_user_loc.yml')
@jwt_required()
def set_user_loc():
    user_id=get_jwt_identity()
    lat = request.form.get("lat")
    lng = request.form.get("lng")
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401

    user = db.session.query(Users).filter(Users.id==user_id)
    live_update_data = {
                "user_id": user_id,
                "region": user.first().region
            }
    if not user.first():
        return jsonify({'success': -1, 'message': 'User does not exist'}), 500
    if not lat or not lng:
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 201
    ul = UserLoc(user_id, lat, lng)        
    db.session.add(ul)
    existing_entry  = UserRegistrationDetails.query.filter_by(user_id=user_id).first()
    if not existing_entry:
        loc_and_addr = get_city_by_lat_long(lat, lng)
        city = None
        address = None
        if loc_and_addr.get('success') is not False:
            city = loc_and_addr.get('city', None)
            address = loc_and_addr.get('formatted_address', None)
        # Create new entry in the database
        user_region=Regions.find_region(lat,lng)
        new_entry = UserRegistrationDetails(
            user_id=user_id,
            lat=float(lat),
            lng=float(lng),
            city=city,
            address=address,
            region=user_region,
        )
        db.session.add(new_entry)
        live_update_to_channel(live_update_data, room_name='customers', type='customers',region=user.first().region, channel= 'cust_loc')
    else:
        if not (existing_entry.city and existing_entry.address):
            loc_and_addr = get_city_by_lat_long(lat, lng)
            city = None
            address = None
            user_region=Regions.find_region(lat,lng)
            if loc_and_addr.get('success') is not False:
                city = loc_and_addr.get('city', None)
                address = loc_and_addr.get('formatted_address', None)
            existing_entry.lat = float(lat)
            existing_entry.lng = float(lng)
            existing_entry.address = address
            existing_entry.city = city
            existing_entry.region = user_region
            live_update_to_channel(live_update_data, room_name='customers', type='customers',region=user.first().region, channel= 'cust_loc')
    try:
        db.session.commit()

    except Exception as e:
        print(e)
        db.session.rollback() 
        return jsonify({'success': -1, 'message': 'Failure in Adding Location'})
    return jsonify({'success': 1, 'message': 'Location added successfully'})


@users.route('/api/user/payment_switch', methods=['POST'])
@swag_from('/app/swagger_docs/users/set_user_loc.yml')
@jwt_required()
def user_payment_switch():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2, 'message': 'Incomplete form details'}), 201
    book = request.form['book_id']
    user_has_booking = db.session.query(Bookings).filter(Bookings.id==book).filter(Bookings.user==user_id).first()
    if user_has_booking:
        return _switch_payment(book)
    else:
        return jsonify({'success': -1, 'message': "User do not have booking"})

@users.route('/api/user/apply_referral', methods=['POST'])
@swag_from('/app/swagger_docs/users/apply_referral.yml')
@jwt_required()
def referral_use():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'msg': 'User restricted'}), 401
    if not complete(request.form, ['ref_code']):
        return jsonify({'success': -2, 'msg': 'Incomplete form details'}), 201
    ref_code = request.form['ref_code']
    if ref_code == "":
        return jsonify({'success': -4, 'msg': 'Code invalid'}), 400
    src_user = db.session.query(Users).filter(Users.ref_code == ref_code)
    if not src_user.first():
        return jsonify({'success': -4, 'msg': 'Code invalid'}), 400
    is_src_deleted_user = db.session.query(DeletedUser).filter(DeletedUser.userid== src_user.first().id).first()
    if is_src_deleted_user:
        return jsonify({'success': -4, 'msg': 'Code invalid'}), 400
    is_driver = db.session.query(Drivers).filter(Drivers.user == user_id).first()
    # check if existing driver is applying any code on user app
    if is_driver:
        return jsonify({'success': -6, 'msg': 'User not eligible'}), 403
    # check if user already applied any code
    ref_prev_use = db.session.query(ReferralUse).filter(ReferralUse.ru_dest_uid == user_id).first()
    # check if user already completed trip
    user_prev_trip = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id).filter(Bookings.user == user_id).filter(Trip.endtime != None).all()
    if user_prev_trip:
        return jsonify({'success': -6, 'msg': 'User not eligible'}), 403
    if ref_prev_use:
        return jsonify({'success': -3, 'msg': 'Code already used'}), 400
    user = db.session.query(Users).filter(Users.id==user_id).first()
    mobile = user.mobile

    # Check if this user had deleted their account before and recreated it
    deleted_users = db.session.query(DeletedUser).filter(DeletedUser.mobile == int(mobile)).all()
    for deluser in deleted_users:
        referrals = db.session.query(ReferralUse).filter(ReferralUse.ru_dest_uid == deluser.userid).all()
        for referral in referrals:
            if referral.ru_src_uid == src_user.first().id:
                return jsonify({'success': -6, 'msg': 'User not eligible'}), 403
    dest_user = db.session.query(Users).filter(Users.id == user_id)
    src_uid = src_user.first().id
    if user_id == src_uid:
        return jsonify({'success': -5, 'msg': 'Code invalid'})
    if ref_code in {"SK150", "RN150"}:
        src_amount = ReferralUse.REFERRAL_SRC_BONUS_AMT
        dest_amount = ReferralUse.REFERRAL_DEST_BONUS_AMT
    else:
        src_amount = ReferralUse.REFERRAL_SRC_AMT
        dest_amount = ReferralUse.REFERRAL_DEST_AMT
    dest_credit_tgt = dest_user.first().credit + dest_amount
    src_reason = "Referral redemption: %s" % dest_user.first().get_name()
    dest_reason = "Referral use %s" % ref_code
    src_user_trans = UserTrans(src_uid, src_amount*100,
                               method=src_reason,
                               status=UserTrans.COMPLETED,
                               cash=0, stop=True,wall_b=src_user.first().credit, wall_a=src_user.first().credit+src_amount)
    dest_user_trans = UserTrans(user_id, dest_amount*100,
                               method=dest_reason,
                               status=UserTrans.COMPLETED,
                               cash=0, stop=True,wall_b=dest_user.first().credit, wall_a=dest_user.first().credit+dest_amount)
    db.session.add(src_user_trans)
    db.session.add(dest_user_trans)
    src_user.update({Users.credit: Users.credit + src_amount})
    dest_user.update({Users.credit: Users.credit + dest_amount})
    ru = ReferralUse(src_uid, user_id, ru_src_user_trans=src_user_trans.id,
                     ru_dest_user_trans=dest_user_trans.id)
    db.session.add(ru)
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        return jsonify({"success": -5, 'msg': 'DB Error'})
    return jsonify({"success": 1, "credit": dest_credit_tgt, "credit_added": dest_amount, 'msg': 'Referral applied successfully'})

@users.route('/api/user/delete', methods=['POST'])
@swag_from('/app/swagger_docs/users/delete_user.yml')
@jwt_required()
def delete_id():
    user_id = get_jwt_identity()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401

    try: 
        reason = int(request.form.get("reason", DeletedUser.RSN_OTHER))
    except Exception:
        return jsonify({'success': -2, 'message': 'Incomplete form details'}), 400

    user = db.session.query(Users).filter(Users.id == user_id).first()
    old_mobile_number = user.mobile
    if not user:
        return jsonify({'success': -2, 'message': 'User not found'}), 404
    if not user.enabled:
        return jsonify({'success': -2, 'message': 'User restricted'}), 403

    new_mobile = int(10e10 - 1 - int(user.mobile))
    i = 0
    while i < 50:
        user_exists = db.session.query(Users).filter(Users.mobile == new_mobile).first()
        if not user_exists:
            break
        new_mobile += 1 
        i += 1
    if i >= 50:
        # Fail, it's fine
        return jsonify({"success": -2, 'message': 'Reached max limit for account deletion'}), 403

    try:
        # Update user's mobile and log deletion
        db.session.query(Users).filter(Users.id == user_id).update({Users.mobile: new_mobile, Users.enabled: 0})
        log_entry = DeletedUser(userid=user_id, mobile= old_mobile_number, newmobile=new_mobile, reason=reason)
        db.session.add(log_entry)
        db.session.commit()
        return jsonify({'success': 1, 'message': 'User deleted successfully'}), 201
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'message': 'Server Error'}), 500

@users.route('/api/customer/ongoing', methods=['POST'])
@jwt_required()
def ongoing_trip_user_dep():
    return ongoing_trip_user()

@users.route('/api/customer/unallocate', methods=['POST'])
@jwt_required()
def customer_unallocate_trip():
    customer = get_jwt_identity()
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_USER, Users.ROLE_DRIVER]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2}), 201
    book = request.form['booking_id']
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_OTHER))
    reason_detail = str(get_safe(request.form, 'reason_details', BookingCancelled.RSN_OTHER))
    driver_book = db.session.query(Bookings, Drivers).filter(Bookings.id == book).filter(Drivers.id == Bookings.driver).first()
    booking = driver_book[0]
    trip_ex = db.session.query(Trip).filter(Trip.book_id == book).first()

    has_trip = trip_ex is not None
    if has_trip and trip_ex.status > Trip.TRIP_REACHED_SRC:
        return jsonify({'success': -2})

    if booking.valid >= 0:
        try:
            bc = BookingCancelled(user=customer, cancel_source=BookingCancelled.SRC_USER, booking=booking.id, uid=booking.user,
                      did=booking.driver, penalty_user= 0, penalty_driver= 0, rsn=reason,
                      reason_detail=reason_detail)
            db.session.add(bc)
            db.session.commit()
            return jsonify({'success': 1})
        except Exception as e:
            print(e)
            return jsonify({'success': -1}),201
    else:
        return jsonify({'success': -2})

tags:
  - Customer_admin
summary: Search User or Driver by Mobile Number
description: >
  This endpoint allows admins to search for a user or driver by their mobile number. Depending on the role (User or Driver), 
  it retrieves different details, such as wallet balance for users and due amounts for drivers. Only accessible to admin and superadmin roles.
parameters:
  - name: mobile
    in: formData
    required: true
    type: string
    description: The mobile number to search for a user or driver.
    example: "**********"
responses:
  200:
    description: Successfully retrieved the user or driver details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        result:
          type: array
          description: Details of the user or driver
          items:
            type: object
            properties:
              id:
                type: integer
                description: The unique ID of the user or driver
                example: 101
              name:
                type: string
                description: The full name of the user or driver
                example: "<PERSON>"
              mobile:
                type: string
                description: The mobile number of the user or driver
                example: "**********"
              wallet:
                type: number
                description: The wallet balance of the user or driver
                example: 1500.75
              driver_license:
                type: string
                description: The driver's license number (only for drivers)
                example: "DL-**********"
              due:
                type: number
                description: The due amount the driver owes (only for drivers)
                example: 300.50
              withdrawable:
                type: number
                description: The withdrawable amount in the driver's wallet (only for drivers)
                example: 1000.00
  201:  # Invalid mobile number or missing parameter
    description: Request failed due to incomplete parameters (e.g., missing mobile number) or invalid mobile number
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  401:  # Unauthorized access
    description: User does not have the appropriate admin privileges to perform this operation
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  500:  # User or driver not found
    description: Failed to find the user or driver with the provided mobile number
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3)
          example: -3
  500:  # Internal server error
    description: Server error or general failure while searching by mobile number
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
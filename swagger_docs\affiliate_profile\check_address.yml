tags:
  - Affiliate_Profile
summary: Check Address Nickname
description: >
  Verifies if a nickname for an address already exists for the affiliate representative.
parameters:
  - name: nickname
    in: formData
    type: string
    required: true
    description: The nickname of the address.
responses:
  200:
    description: Nickname check result.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Allowed to add new address
  400:
    description: Missing nickname.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Missing nickname.
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate representative not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        error:
          type: string
          example: An error occurred.

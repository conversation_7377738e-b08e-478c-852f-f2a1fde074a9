tags:
  - Login_admin
summary: Refresh Admin Tokens
description: >
  This endpoint refreshes the admin's access token using the refresh token stored in the cookies. It requires the refresh token to be valid and not revoked, and it checks the CSRF token to prevent cross-site request forgery.
parameters:
  - in: header
    name: X-CSRF-Token
    type: string
    required: true
    description: CSRF token to verify the validity of the request.
    example: "sometoken12345"
  - in: cookie
    name: refresh_token_cookie
    type: string
    required: true
    description: The refresh token stored in cookies, used to generate a new access token.
responses:
  200:
    description: Access token successfully refreshed.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        refresh:
          type: boolean
          description: Indicates that the token has been refreshed.
          example: true
  400:
    description: Bad request due to missing or invalid CSRF token.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for failure).
          example: 0
        message:
          type: string
          description: Error message indicating the missing CSRF token.
          example: "Missing CSRF token"
        refresh:
          type: boolean
          description: Token refresh status (false for failure).
          example: false
  401:
    description: Unauthorized request due to missing or revoked refresh token, or expired token.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for failure).
          example: 0
        refresh:
          type: boolean
          description: Token refresh status (false for failure).
          example: false
        msg:
          type: string
          description: Message indicating why the token refresh failed (e.g., "Token has been revoked").
          example: "Token has been revoked"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for failure).
          example: -4
        error:
          type: string
          description: Error message detailing the exception that occurred.
          example: "Internal server error"
        refresh:
          type: boolean
          description: Token refresh status (false for failure).
          example: false


import requests
from google.oauth2 import service_account
from google.auth.transport.requests import Request
import time
import concurrent.futures



DEVICE_TOKENS = [
    'dJzxHXWYS3Kq5jZ2HaDy77:APA91bErbSR03cVq9Oe4O-cmIaUUkcEaGsUEfB0cu0cxYAxwfHOCuJNlwrih3XMnrilre1EIYnQ_qr1Kkp-R0tPGK9qQ8DO7Q-rfXDnkt_Zx-WeF_NiC88g',
    'cAiMv3zfSwCdVqxUJz37hj:APA91bHelYP6SDntrL4TMt8zcpkXt3Bf2lgAO1-9POmFO0KU2bgPzpMhrp1IlrXvoNTvd3QImvuzyo10wEJKst28KeSty3l-z3tD_NvJ90r8GjIrQNuIL6o',
    'fAt6DnaqQKuEiAzzVOmCmm:APA91bG-ajbzNSqVZ8oP8vykQcb8jplTH0QEGgLjmbsUCIZIdZf3ggH0VJhxwrmIuNbhIyuPuGPWFskLTju-DKDbj3sn1ggm_af0Xl_Q9cIxqNVQEzsXtFY'
    # 'cVHxuzaPR9WF9wIdPdatzG:APA91bG5DDiTtyKzQdHENakefexeUnAjE3O0-2XEp7d8_nLcKvmtLL1F0tkiuMr9fuI9QluBMdsIJfDKEUokMPkbC4J9y5BuTMPtNRJcMQ6BWRC_USDQdAA'
    # "cEeIr5LrSveTNrdh7kjDxo:APA91bFJsJRSgprg7JU1jfxOcDa3zrE9kV_BfUzaxdvz3k7cDcxHUStTrvnMLAbt1u4x2vxB0FW6PRu-GeWtQ9Y9MxpA0lJg6fKaakEUeDSOs1GELt795YY",
    # "dJzxHXWYS3Kq5jZ2HaDy77:APA91bHUjTppU0gNKwfyTkTFu3VOlG_7kP5UM6v5JXbSHMPF3_ugm3aLouI33TVQ2zCT4umKArdgLgx2qd7eJthFOxQA7gnLwdu0YpraHjESWznLfv82oIE",
    # "edJvBFad8EjNvXHD-g4w9O:APA91bGdJKwhF7sADYDcgTA5lSYArPjwUlE8akCP3GQCbfp3jwhZRGl1T7-MBOxfVS7a486stZdvIXTqt3UDj4XSV-TbxrEjp4GHNJJjB2fckuS8KuiVEK0",
    # "c5IrHcCRTWOfeQFLKCUQYR:APA91bGD4x3y66Np3yZrta303xg4vCFMpVxoA74_ig88pXYJFarE8NEeHyLKuX8YJKjjC3jOV3xVl9bzmFRyvVUXAZeUajYDHeQZ0BTtfa1OusFuJ6dxuTI",# Add more tokens if needed
    # "fxAePKk4TjKVVrGLQmbwFi:APA91bEtE5tW3klI6GBUMeyUq2yh8iTK7jPzATo9PG91quOVLe-RdmnA_EoJCiPRQG89zXsZ8YSm02mlxbG1nwqp0yUOD2_E5LWyIhO-2fdAvi3J3xsfJKG0RmV0LXG-pMrB_W57ZMS0",
    # "fNxh8_tNbEo4k7agLR2Koa:APA91bFXOSXvEtru_LMiMQaBiqrdGwnKSHfSZU-R4YdiCfdQpdPXtA5Wpe_UpafQ62ShtR5LFpB_oGu9mNxXDloVfKekQ0btqxsEyr8vQ4HsYOzeMa5DYKTEU5ORwYZNJPcPZPgiVbQI",
    # "cEeIr5LrSveTNrdh7kjDxo:APA91bFJsJRSgprg7JU1jfxOcDa3zrE9kV_BfUzaxdvz3k7cDcxHUStTrvnMLAbt1u4x2vxB0FW6PRu-GeWtQ9Y9MxpA0lJg6fKaakEUeDSOs1GELt795YY",
    # "dJzxHXWYS3Kq5jZ2HaDy77:APA91bHUjTppU0gNKwfyTkTFu3VOlG_7kP5UM6v5JXbSHMPF3_ugm3aLouI33TVQ2zCT4umKArdgLgx2qd7eJthFOxQA7gnLwdu0YpraHjESWznLfv82oIE",
    # "edJvBFad8EjNvXHD-g4w9O:APA91bGdJKwhF7sADYDcgTA5lSYArPjwUlE8akCP3GQCbfp3jwhZRGl1T7-MBOxfVS7a486stZdvIXTqt3UDj4XSV-TbxrEjp4GHNJJjB2fckuS8KuiVEK0",
    # "c5IrHcCRTWOfeQFLKCUQYR:APA91bGD4x3y66Np3yZrta303xg4vCFMpVxoA74_ig88pXYJFarE8NEeHyLKuX8YJKjjC3jOV3xVl9bzmFRyvVUXAZeUajYDHeQZ0BTtfa1OusFuJ6dxuTI",# Add more tokens if needed
    # "fxAePKk4TjKVVrGLQmbwFi:APA91bEtE5tW3klI6GBUMeyUq2yh8iTK7jPzATo9PG91quOVLe-RdmnA_EoJCiPRQG89zXsZ8YSm02mlxbG1nwqp0yUOD2_E5LWyIhO-2fdAvi3J3xsfJKG0RmV0LXG-pMrB_W57ZMS0",
    # "fNxh8_tNbEo4k7agLR2Koa:APA91bFXOSXvEtru_LMiMQaBiqrdGwnKSHfSZU-R4YdiCfdQpdPXtA5Wpe_UpafQ62ShtR5LFpB_oGu9mNxXDloVfKekQ0btqxsEyr8vQ4HsYOzeMa5DYKTEU5ORwYZNJPcPZPgiVbQI",
    # "cEeIr5LrSveTNrdh7kjDxo:APA91bFJsJRSgprg7JU1jfxOcDa3zrE9kV_BfUzaxdvz3k7cDcxHUStTrvnMLAbt1u4x2vxB0FW6PRu-GeWtQ9Y9MxpA0lJg6fKaakEUeDSOs1GELt795YY",
    # "dJzxHXWYS3Kq5jZ2HaDy77:APA91bHUjTppU0gNKwfyTkTFu3VOlG_7kP5UM6v5JXbSHMPF3_ugm3aLouI33TVQ2zCT4umKArdgLgx2qd7eJthFOxQA7gnLwdu0YpraHjESWznLfv82oIE",
    # "edJvBFad8EjNvXHD-g4w9O:APA91bGdJKwhF7sADYDcgTA5lSYArPjwUlE8akCP3GQCbfp3jwhZRGl1T7-MBOxfVS7a486stZdvIXTqt3UDj4XSV-TbxrEjp4GHNJJjB2fckuS8KuiVEK0",
    # "c5IrHcCRTWOfeQFLKCUQYR:APA91bGD4x3y66Np3yZrta303xg4vCFMpVxoA74_ig88pXYJFarE8NEeHyLKuX8YJKjjC3jOV3xVl9bzmFRyvVUXAZeUajYDHeQZ0BTtfa1OusFuJ6dxuTI",# Add more tokens if needed
    # "fxAePKk4TjKVVrGLQmbwFi:APA91bEtE5tW3klI6GBUMeyUq2yh8iTK7jPzATo9PG91quOVLe-RdmnA_EoJCiPRQG89zXsZ8YSm02mlxbG1nwqp0yUOD2_E5LWyIhO-2fdAvi3J3xsfJKG0RmV0LXG-pMrB_W57ZMS0",
    # "fNxh8_tNbEo4k7agLR2Koa:APA91bFXOSXvEtru_LMiMQaBiqrdGwnKSHfSZU-R4YdiCfdQpdPXtA5Wpe_UpafQ62ShtR5LFpB_oGu9mNxXDloVfKekQ0btqxsEyr8vQ4HsYOzeMa5DYKTEU5ORwYZNJPcPZPgiVbQI",
]

# Function to Get OAuth 2.0 Access Token
def get_access_token():
    """
    Manually loads service account JSON to get OAuth 2.0 token.
    """
    credentials = service_account.Credentials.from_service_account_file(
        'drivers4me-prod-firebase-adminsdk-yoszd-543a6cc807.json',
        scopes=["https://www.googleapis.com/auth/firebase.messaging"]
    )

    credentials.refresh(Request())
    return credentials.token

# Function to Send Notification via FCM HTTP v1 API
def send_fcm_notification(token, title, big_text, small_text, image_url=None, pending=False):
    start_time = time.time()
    url = "https://fcm.googleapis.com/v1/projects/drivers4me-prod/messages:send"
    headers = {
        "Authorization": f"Bearer {get_access_token()}",
        "Content-Type": "application/json"
    }

    # Base message
    message = {
        "token": token,
        "android": {
            "priority": "high"
        }
    }

    # Only include a visible notification when pending == False
    if not pending:
        # FCM notification for Android & general payload
        message["notification"] = {
            "title": title,
            "body": big_text
        }
        message["android"]["notification"] = {
            "sound": "default"
        }
        # APNs payload for iOS
        message["apns"] = {
            "headers": {
                "apns-priority": "10"
            },
            "payload": {
                "aps": {
                    "alert": {
                        "title": title,
                        "body": big_text
                    },
                    "sound": "default",
                    "mutable-content": 1
                },
                "fcm_options": {}
            }
        }

    # Build data section
    data = {
        "title": title,
        "smallText": small_text,
        "bigText": big_text,
    }
    if pending:
        # only include this flag when pending
        data["pending"] = "true"

    # Optional image in data & APNs
    if image_url:
        data["pic"] = image_url
        if not pending:
            # only include when sending a visible notification
            message["apns"]["payload"]["fcm_options"]["image"] = image_url
            message["notification"]["image"] = image_url
            # message["android"]["notification"]["image"] = image_url

    message["data"] = data

    # Final payload
    payload = {"message": message}

    # Send
    response = requests.post(url, headers=headers, json=payload)
    elapsed_time = time.time() - start_time  # Calculate elapsed time
    end_time = time.time()

    # if response.status_code == 200:
    #     print(f"✅ Sent to {token} in {elapsed_time:.2f} sec")
    # else:
    #     print(f"❌ Failed to send to {token}. Error: {response.json()} (Took {elapsed_time:.2f} sec)")


def send_bulk_notifications(notifications):
    """
    notifications: a list of dictionaries, each containing:
        - token: the device token
        - title: notification title
        - big_text: main notification text (can vary per token)
        - small_text: secondary text
        - image_url: (optional) image URL
        - pending: (optional) boolean flag
    """
    total_start_time = time.time()  # Start timing the full bulk process
    executor = concurrent.futures.ThreadPoolExecutor()

    for notif in notifications:
        executor.submit(
            send_fcm_notification,
            notif['token'],
            notif['title'],
            notif['big_text'],
            notif['small_text'],
            notif.get('image_url'),
            notif.get('pending', False)
        )

    total_elapsed_time = time.time() - total_start_time  # Calculate total execution time
    print(f"🚀 Bulk notifications completed in {total_elapsed_time:.2f} sec")

# Bulk Sending Function Using Gevent
# def send_bulk_notifications(device_tokens,title,big_text,small_text,image_url=None,pending=False):
#     total_start_time = time.time()  # Start timing the full bulk process
#     executor = concurrent.futures.ThreadPoolExecutor(max_workers=10)

#     # Submit tasks to the thread pool and NOT wait for results
#     for token in device_tokens:
#         executor.submit(send_fcm_notification, token, title, big_text, small_text, image_url,pending)

#     total_elapsed_time = time.time() - total_start_time  # Calculate total execution time
#     print(f"🚀 Bulk notifications completed in {total_elapsed_time:.2f} sec")


# Run the script
if __name__ == "__main__":
    notifications = []
    for token in DEVICE_TOKENS:
        notifications.append({
            "token": token,
            "title": "Bulk Notification",
            "big_text": "This is a test message for multiple users.",
            "small_text": "This is a test message for multiple users.",  # or another shorter text if desired
            "image_url": "https://firebasestorage.googleapis.com/v0/b/drivers4me-prod.appspot.com/o/FCMImages%2FNew%20year%2025%20noti-01-01.jpg?alt=media&token=f7dcf73e-a5aa-430d-98d7-5333ac4ffce3",
            "pending": False
        })
    send_bulk_notifications(notifications)
    print("Executed the Bulk Notification")



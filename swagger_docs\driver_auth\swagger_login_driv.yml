tags:
  - Authentication
summary: Swagger Driver Login
description: >
  This endpoint allows a driver to log in using their mobile number and password or OTP.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the driver
  - name: pwd
    in: formData
    type: string
    required: true
    description: Password or OTP for authentication
  - name: type
    in: formData
    type: integer
    required: false
    description: Type of authentication (0 for password, 1 for OTP)
responses:
  200_a:
    description: User restricted (returning after failed login attempt)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for user restriction)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  200_b:
    description: Driver successfully logged in
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        driver_id:
          type: integer
          description: Driver ID
          example: 12345
        user_id:
          type: integer
          description: User ID
          example: 12345
        available:
          type: integer
          description: Availability of the driver
          example: 1
        approved:
          type: integer
          description: Approval status of the driver
          example: 1
        driver_pic:
          type: string
          description: URL of the driver's profile picture
          example: "https://example.com/driver_pic.jpg"
        driver_rating:
          type: number
          format: float
          description: Driver's rating
          example: 4.5
        user_mobile:
          type: string
          description: User's mobile number
          example: "9876543210"
        user_fname:
          type: string
          description: User's first name
          example: "John"
        user_lname:
          type: string
          description: User's last name
          example: "Doe"
        user_email:
          type: string
          description: User's email address
          example: "<EMAIL>"
        perma:
          type: integer
          description: Driver's permanent status
          example: 1
        verif_status:
          type: string
          description: Verification status bitstring
          example: "11111"
    examples:
      application/json:
        success: 1
        driver_id: 12345
        user_id: 12345
        available: 1
        approved: 1
        driver_pic: "https://example.com/driver_pic.jpg"
        driver_rating: 4.5
        user_mobile: "9876543210"
        user_fname: "John"
        user_lname: "Doe"
        user_email: "<EMAIL>"
        perma: 1
        verif_status: "11111"
        csrf: "csrf token to authorize swagger"
  400:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for incomplete form)
          example: 0
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: 0
        message: "Incomplete form details"
  401_a:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for user restriction)
          example: 0
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: 0
        message: "User restricted"
  401_b:
    description: Failed login attempt
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for failed login)
          example: 0
        message:
          type: string
          description: Error message
          example: "Failed login"
    examples:
      application/json:
        success: 0
        message: "Failed login"
  500:
    description: DB Error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for DB error)
          example: 0
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: 0
        message: "DB Error"

tags:
  - Affiliate_Profile
summary: Add New SPOC for Affiliate Representative
description: >
  This endpoint allows an affiliate representative to add one or more SPOCs (Single Point of Contact).
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: array
      description: List of SPOC details.
      items:
        type: object
        properties:
          name:
            type: string
            description: SPOC name.
            example: <PERSON>
          mobile:
            type: string
            description: SPOC mobile number.
            example: "9876543210"
          global:
            type: boolean
            description: Whether the SPOC is global (true) or local (false).
            example: false
responses:
  201:
    description: SPOC data inserted successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: SPOC data inserted successfully.
  400:
    description: Invalid payload or duplicate SPOC.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Invalid payload. Expected a list of SPOC details.
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate representative not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -6
        message:
          type: string
          example: An error occurred.

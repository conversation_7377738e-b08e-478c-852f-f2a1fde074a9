import gevent
import redis
import json

redis_client = None
redis_available = None


def get_redis_client():
    return redis_client

def check_redis():
    global redis_client, redis_available
    while True:
        try:
            redis_client.ping()
            redis_available = True
        except (redis.ConnectionError, redis.TimeoutError):
            redis_available = False
        gevent.sleep(2)

def execute_with_fallback(command, *args, **kwargs):
    global redis_client,redis_available
    try:
        redis_method = getattr(redis_client, command, None)
        if redis_method is None:
            print(f"Unsupported Redis command: {command}",flush=True)
            return None

        if command == "subscribe":
            print('subsccribing failed',flush=True)
            pubsub = redis_client.pubsub()
            pubsub.subscribe(*args)
            return pubsub
        if command == "publish":
            print('publishing failed',flush=True)
            channel = args[0]
            data = kwargs.get("data", {})

            json_data = json.dumps(data)
            redis_client.publish(channel, json_data)
            return True
        if command == "unsubscribe":
            print('unsubsccribing failed',flush=True)
            pubsub = redis_client.pubsub()
            pubsub.unsubscribe(*args)
            return True
        if command == "pipeline":
            print('pipeline entered',flush=True)
            pipeline = redis_client.pipeline()
            commands = kwargs.get("commands", [])
            for cmd in commands:
                method_name, cmd_args, cmd_kwargs = cmd
                if hasattr(pipeline, method_name):
                    getattr(pipeline, method_name)(*cmd_args, **cmd_kwargs)
                else:
                    print(f"Unsupported command in pipeline: {method_name}",flush=True)
            return pipeline.execute()

        if command == "scan_iter":
            return list(redis_client.scan_iter(*args, **kwargs))
        result = redis_method(*args, **kwargs)
        return result
    except Exception as e:
        print(f"Redis unavailable: {e}. Queuing {command} with args {args}",flush=True)
        if command == "scan_iter":
            return []
        return None
    except redis.ResponseError as e:
        print(f"Redis command error: {command} with args {args}, error: {e}",flush=True)
        if command == "scan_iter":
            return []
        return None
    except Exception as e:
        print(f"Unexpected error with command {command}: {e}",flush=True)
        if command == "scan_iter":
            return []
        return None

def is_redis_available():
    global redis_client, redis_available
    return redis_available

def init_redis(app):
    global redis_client,redis_available
    redis_host = app.config.get("REDIS_URI") #'127.0.0.1' if app.config['TESTING'] else '***********'
    redis_url = f'redis://{redis_host}:6379/0'
    try:
        redis_client = redis.StrictRedis.from_url(redis_url, decode_responses=True)
        redis_client.ping()
        redis_available = True
        print("Using Redis as the message queue.", flush=True)
    except (redis.ConnectionError, redis.TimeoutError) as e:
        print(f"Redis connection failed: {e}.", flush=True)

    gevent.spawn(check_redis)
    return redis_client, redis_url
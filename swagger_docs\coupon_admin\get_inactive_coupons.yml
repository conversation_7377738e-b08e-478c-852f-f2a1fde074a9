tags:
  - Coupon_admin
summary: Get All Inactive Coupons
description: >
  This endpoint allows admins to retrieve all inactive coupons. Admin authorization is required.
responses:
  200:
    description: Successfully retrieved inactive coupons
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        result:
          type: array
          description: List of inactive coupons
          items:
            type: object
            properties:
              coupon_id:
                type: integer
                description: The unique ID of the coupon.
                example: 123
              coupon_code:
                type: string
                description: The code of the coupon.
                example: "EXPIRED10"
              percent_off:
                type: number
                description: The percentage discount offered by the coupon.
                example: 10.0
              max_off:
                type: number
                description: The maximum discount amount for percentage-based discounts.
                example: 50.00
              flat_off:
                type: number
                description: The flat discount amount.
                example: 20.00
              min_trip_price:
                type: number
                description: The minimum trip price required to apply the coupon.
                example: 100.00
              min_trip:
                type: integer
                description: The minimum number of trips required to use the coupon.
                example: 2
              validTill:
                type: string
                format: date
                description: The expiration date of the coupon.
                example: "2023-12-31"
              createdAt:
                type: string
                format: date-time
                description: The date and time when the coupon was created.
                example: "2022-01-15T12:30:45Z"
              state:
                type: integer
                description: The current state of the coupon (0 for inactive).
                example: 0
  400:
    description: Bad request due to invalid data format or missing parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Invalid data format"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

tags:
  - Driver_admin
summary: Verify Driver's Voter ID Document
description: >
  This endpoint verifies a driver's voter ID document. It checks the validity of the provided voter ID against an external service and updates the driver's verification status accordingly.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose voter ID is to be verified.
    example: 101
  - name: id_no
    in: formData
    required: true
    type: string
    description: The voter ID number to be verified.
    example: "VOTER123456"
responses:
  200:
    description: Successfully verified the driver's voter ID document.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the verification process (1 for successful verification, 10 for fetched but failed to verify).
          example: 1
        message:
          type: string
          description: Message describing the outcome of the verification.
          example: "Voter Details Verified"
        details:
          type: object
          description: Verification details including name match and other relevant information.
          properties:
            name_match:
              type: boolean
              description: Indicates whether the account holder's name matches.
              example: true
  400:
    description: Bad request (verification failed due to issues in the voter ID).
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for invalid voter ID, -2 for ID not found).
          example: -1
        message:
          type: string
          description: Error message.
          example: "Invalid voter id."
  500:
    description: Internal server error or exception during the request.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-3 for internal error).
          example: -3
        message:
          type: string
          description: Error message.
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message.
          example: "Internal server error."

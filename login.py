#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  login.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime
import hashlib
import http.client
import json
from flasgger import swag_from
from sendotp import sendotp
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, create_access_token,
    create_refresh_token,
    get_jwt_identity, set_access_cookies, set_refresh_cookies, unset_jwt_cookies,
    get_jwt, decode_token
)
from referral import get_user_ref_code
from sqlalchemy import exc
from _ops_message import send_slack_msg
import _sms
from flask import current_app as app
from _utils_acc import account_enabled, validate_role
from _utils import complete, get_pic_url, get_safe
from models import Users, Drivers, UserToken, db, DriverLog, UsernameMap, UserFCM, DriverFCM, DriverVerify, AdminUserLog, \
    UserAnalyticsToken, DriverAnalyticsToken
import html
from login_common import validate_pass, validate_otp, MASTER_OTP, MASTER_PWD_2, MASTER_PWD, _gen_otp, restricted_roles, BIT_0_TO_19, BIT_1_TO_6, BIT_1_TO_15

loginp = Blueprint('login', __name__)

@loginp.route('/token/otp/generate', methods=['POST'])
@swag_from('/app/swagger_docs/otp/generate_otp.yml')
def gen_otp():
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1, 'message': "Incomplete form"}), 201
    is_new = bool(int(get_safe(request.form, 'new', 1)))
    if not is_new:
        try:
            cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        except Exception:
            return jsonify({'success': -1, 'message': "DB Error"}), 401
        if not account_enabled(cur_user.id):
            return jsonify({'success': -2, 'message': "User restricted"}), 401
    _sms.generate_otp_gupshup(request.form['mobile'])
    #_sms.send_otp_flow(request.form['mobile'])
    #_gen_otp(request.form['mobile'])
    return jsonify({'success': 1})

# FIXME: Combine this and the first one
@loginp.route('/token/otp/generate/delete', methods=['POST'])
def gen_otp_delete():
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1}), 201
    try:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first().id
    except Exception:
        return jsonify({'success': -1}), 401
    _sms.generate_otp_gupshup(request.form['mobile'])
    #_gen_otp(request.form['mobile'])
    return jsonify({'success': 1})

@loginp.route('/test-jwt', methods=['GET'])
@jwt_required()
def test_jwt():
    print(get_jwt_identity())
    return jsonify({'success': get_jwt_identity()})


@loginp.route('/token/otp/validate', methods=['POST'])
@swag_from('/app/swagger_docs/otp/validate_otp.yml')
def val_otp():
    if not complete(request.form, ['mobile','otp']):
        return jsonify({'success': -1, 'message': "Incomplete form"}), 201
    is_new = bool(int(get_safe(request.form, 'new', 1)))
    if not is_new:
        try:
            cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        except Exception:
            return jsonify({'success': -1, 'message': "DB Error"}), 401
        if not account_enabled(cur_user.id):
            return jsonify({'success': -2, 'message': "User restricted"}), 401
    return _sms.verify_otp_gupshup(request.form['mobile'], request.form['otp'])

# FIXME: Combine this and the first one
@loginp.route('/token/otp/validate/delete', methods=['POST'])
def val_otp_delete():
    if not complete(request.form, ['mobile','otp']):
        return jsonify({'success': -1}), 201
    try:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first().id
    except Exception:
        return jsonify({'success': -1}), 401
    return  _sms.verify_otp_gupshup(request.form['mobile'], request.form['otp'])


@loginp.route('/token/exists', methods=['POST'])
@swag_from('/app/swagger_docs/auth/check_exists.yml')
def check_exists():
    try:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        if not cur_user or cur_user is None:
            return jsonify({'exists': 0}), 200
        else:
            admin_user = db.session.query(AdminUserLog).filter(AdminUserLog.user == cur_user.id).first()
            if admin_user and admin_user.action == AdminUserLog.USER_CREATED:
                return jsonify({'exists': 0, 'msg': "register yourself"})
        gen_otp = bool(int(get_safe(request.form, 'gen_otp', 0)))
        if not account_enabled(cur_user.id):
            return jsonify({'exists': -2, 'message': "User restricted"}), 401
        if not cur_user or cur_user is None:
            return jsonify({'exists': 0, 'message': "User not found"}), 200
        else:
            if gen_otp:
                _gen_otp(request.form['mobile'])
            return jsonify({'exists': 1, 'message': "OTP Sent successfully"}), 200
    except Exception:
        return jsonify({'exists': -1, 'message': "Server Error"}), 201


@loginp.route('/token/exists/driver', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/check_exists_driver.yml')
def check_exists_driver():
    try:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        if not account_enabled(cur_user.id):
            return jsonify({'exists': -2, 'user': -2, 'message':'User restricted'}), 401
        driver = Drivers.query.filter_by(user=cur_user.id).first()
        if not cur_user or cur_user is None:
            return jsonify({'exists': 0, 'user': 0, 'message':'User not found'}), 200
        elif cur_user and not driver:
            return jsonify({'exists': 0, 'user': 1, 'message':'Driver does not exist'}), 200
        else:
            return jsonify({'exists': 1, 'user': 1, 'message':'Driver exist'}), 200
    except Exception:
        return jsonify({'user': 0, 'exists': -1, 'message':'Server error'}), 201

@loginp.route('/token/login', methods=['POST'])
@swag_from('/app/swagger_docs/auth/user_login.yml')
def login():
    if not complete(request.form, ['mobile', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        if cur_user:
            admin_user = db.session.query(AdminUserLog).filter(AdminUserLog.user == cur_user.id).first()
            # if admin_user and admin_user.action == AdminUserLog.USER_CREATED:
            #     return jsonify({'success': -1, 'msg': "register yourself"})
        try:
            otp = request.form['type']
        except (KeyError, NameError) as e:
            otp = 0
        if otp:
            otp_valid = _sms.verify_otp_gupshup(request.form['mobile'], request.form['pwd'])

        if (cur_user and cur_user.enabled and (
            (not otp and validate_pass(user=cur_user, pwd=request.form['pwd'])) or 
            (otp and otp_valid.get("status") == "success"))):
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')
            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)
            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)

            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.CUST_LOGIN)

            resp = jsonify({'success': 1,  'user_id': cur_user.id, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_restore_id': cur_user.restore_id,
                            'user_ref_code': get_user_ref_code(cur_user),'access_token': access_token})

            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the app")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/swagger/user/token/login', methods=['POST'])
@swag_from('/app/swagger_docs/auth/swagger_user_login.yml')
def swagger_user_login():
    if not complete(request.form, ['mobile', 'pwd']):
        return jsonify({'success': 0, 'message': "Incomplete form"}), 400
    else:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        try:
            otp = request.form['type']
        except (KeyError, NameError) as e:
            otp = 0
        if not cur_user or not cur_user.enabled:
            return jsonify({'success': 0, 'message': "User restricted"}), 403
        
        otp_valid = _sms.verify_otp_gupshup(request.form['mobile'], request.form['pwd'])
        if ((not int(otp) and validate_pass(user=cur_user, pwd=request.form['pwd'])) or (int(otp) and otp_valid.get("status") == "success")):
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')
            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)
            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)

            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.CUST_LOGIN)
            decoded_token = decode_token(access_token)
            csrf_token = decoded_token.get("csrf")

            resp = jsonify({'success': 1,  'user_id': cur_user.id, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_restore_id': cur_user.restore_id,
                            'user_ref_code': get_user_ref_code(cur_user),'access_token': access_token, 'csrf':csrf_token})

            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0, 'message': "DB Error"}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the app")
            except Exception:
                pass
            if int(otp):
                return jsonify({'success': 0, 'message': "OTP not validated"}), 401
            return jsonify({'success': 0, 'message': "Password not validated"}), 401


@loginp.route('/token/refresh', methods=['POST'])
@swag_from('/app/swagger_docs/auth/refresh_token.yml')
@jwt_required(refresh=True)
def refresh():
    try:
        current_user = get_jwt_identity()
    except Exception:
        return jsonify({'success': -1, 'refresh': False,  'message': "Invalid Token"}), 401
    if not account_enabled(current_user):
        return jsonify({'success': -1, 'message': "User restricted"}), 401
    new_user = Users.query.filter(Users.id == current_user).first()
    new_user.agent = request.headers.get('User-Agent')
    refresh_token_hdr = request.headers.get('Authorization')
    try:
        refresh_token = refresh_token_hdr.split("Bearer")[1].strip()
    except Exception:
        refresh_token = request.cookies.get(app.config['JWT_REFRESH_COOKIE_NAME'])
    if not refresh_token:
        return jsonify({'success': -1, 'refresh': False, 'message': "Failed to refresh"}), 401
    token_hash = hashlib.sha512(refresh_token.encode('utf-8')).hexdigest()
    user_token = UserToken.query.filter(UserToken.user_id == new_user.id).filter(
        UserToken.refresh == token_hash).order_by(UserToken.timestamp.desc()).first()
    now = datetime.datetime.now()

    user_name = db.session.query(Users).filter(Users.id == current_user).first().get_name()
    try:
        send_slack_msg(10, user_name + " refreshed their token with " + request.headers.get('User-Agent') +
                    " and IP " + str(request.environ['HTTP_X_FORWARDED_FOR']))
    except Exception:
            pass
    if not user_token:
        return jsonify({'success': -1, 'refresh': False, 'message': "Failed to refresh"}), 401
    if user_token.expiry < now:
        return jsonify({'success': -1, 'refresh': False, 'message': "Failed to refresh"}), 401
    # Unsure how to check user agents, so ignoring for now
    expires_access = datetime.timedelta(days=365)
    identity_with_claims = {
        'id': new_user.id,
        'roles': new_user.role,
        'region': new_user.region,
        'name': f'{new_user.fname} {new_user.lname}',
    }
    access_token = create_access_token(identity=new_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
    resp = jsonify({'success': 1, 'refresh': True, 'message': "Refresh successful"})
    set_access_cookies(resp, access_token)
    return resp, 200

@loginp.route('/affiliate/refresh', methods=['POST'])
@jwt_required(refresh=True)
def affiliate_refresh():
    try:
        current_user = get_jwt_identity()
        if not account_enabled(current_user):
            return jsonify({
                    "message": "User not allowed to refresh",
                    "refresh": False,
                    "status": 403
                }), 403

        new_user = Users.query.filter(Users.id == current_user).first()
        refresh_token_hdr = request.headers.get('Authorization')

        try:
            refresh_token = refresh_token_hdr.split("Bearer")[1].strip()
        except Exception:
            refresh_token = request.cookies.get("refresh_token_cookie")

        if not refresh_token:
            return jsonify({
                    "message": "Failed to fetch refresh token, Login again..",
                    "refresh": False,
                    "status": 404
                }), 404

        token_hash = hashlib.sha512(refresh_token.encode('utf-8')).hexdigest()
        user_token = UserToken.query.filter(UserToken.user_id == new_user.id).filter(
            UserToken.refresh == token_hash).order_by(UserToken.timestamp.desc()).first()

        if not user_token:
            return jsonify({
                    "message": "Wrong refresh token sent",
                    "refresh": False,
                    "status": 400
                }), 400

        now = datetime.datetime.now()
        if user_token.expiry < now:
            return jsonify({
                    "message": "Refresh token expired, Login again..",
                    "refresh": False,
                    "status": 401
                }), 401

        expires_access = datetime.timedelta(minutes=3)

        identity_with_claims = {
            'id': new_user.id,
            'roles': new_user.role,
            'region': new_user.region,
            'name': f'{new_user.fname} {new_user.lname}',
        }
        access_token = create_access_token(identity=new_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
        resp = jsonify({
                "message": "Refresh Successful",
                "refresh": True,
                "status": 200
            })
        set_access_cookies(resp, access_token)
        return resp, 200
    except Exception as e:
        return jsonify({
                "message": "Internal server error",
                "refresh": False,
                "status": 500
            }), 500

@loginp.route('/token/remove', methods=['POST'])
@swag_from('/app/swagger_docs/auth/logout.yml')
@jwt_required()
def logout():
    resp = jsonify({'logout': True, 'message':'logout successful'})
    unset_jwt_cookies(resp)
    resp.set_cookie('restore_id', '', expires=0)
    resp.set_cookie('name', '', expires=0)
    resp.set_cookie('user_id', '', expires=0)
    return resp, 200


@loginp.route('/token/verify', methods=['POST'])
@swag_from('/app/swagger_docs/auth/verify_token.yml')
@jwt_required()
def verify():
    current_user = get_jwt_identity()
    user = db.session.query(Users).filter(Users.id == current_user).first()
    try:
        if user.role == Users.ROLE_USER:
            send_slack_msg(10, user.get_name() + " used app with agent " + request.headers.get('User-Agent') +
                    " and IP " + str(request.environ['HTTP_X_FORWARDED_FOR']))
    except Exception:
        pass
    if current_user!=None and account_enabled(current_user):
        return jsonify({'success': 1, 'message': "Token verified"}), 200
    else:
        return jsonify({'success': 0, 'message': "Failed to verify token"}), 401


@loginp.route('/token/login_driv', methods=['POST'])
@swag_from('/app/swagger_docs/driver_auth/login_driv.yml')
def login_driv():
    if not complete(request.form, ['mobile', 'pwd']):
        return jsonify({'success': 0, 'message': "Incomplete form details"})
    else:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        try:
            otp = int(request.form['type'])
        except (KeyError, NameError) as e:
            otp = 0
        if otp:
            otp_valid = _sms.verify_otp_gupshup(request.form['mobile'], request.form['pwd'])
    
        if ((cur_user and cur_user.enabled and ((not otp and validate_pass(user=cur_user, pwd=request.form['pwd'])) or
                         (otp and otp_valid.get("status") == "success")) or (otp and request.form['pwd'] == '3487'))):

            cur_driver = Drivers.query.filter_by(user=cur_user.id).first()

            if cur_driver:
                remember = True
                cur_user.agent = request.headers.get('User-Agent')
                try:
                    send_slack_msg(10, cur_user.get_name() + " logged in to the driver app, pwd: " + request.form['pwd'])
                except Exception:
                    pass
                if cur_driver.approved != 1:
                    verif = db.session.query(DriverVerify).filter(DriverVerify.driver_id == cur_driver.id).first()
                    if not verif:
                        bitstring = "00000"
                    else:
                        bitstring = str(int(verif.id_card)) + "" + str(int(verif.photo)) + "" + str(int(verif.ref)) + \
                                    "" + str(int(verif.lic)) + "" + str(int(verif.bank))
                else:
                    bitstring = "11111"
                expires_access = datetime.timedelta(days=365)
                expires_refresh = datetime.timedelta(days=3650)
                identity_with_claims = {
                    'id': cur_user.id,
                    'roles': cur_user.role,
                    'region': cur_user.region,
                    'name': f'{cur_user.fname} {cur_user.lname}',
                }
                access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
                refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
                resp = jsonify({'success': 1, 'driver_id': cur_driver.id, 'user_id': cur_user.id, 'user_ref_code': get_user_ref_code(cur_user),
                                'available': cur_driver.available, 'approved': cur_driver.approved,
                                'driver_pic': get_pic_url(cur_driver.pic), 'driver_rating': cur_driver.rating,
                                'user_mobile': cur_user.mobile, 'user_fname': cur_user.fname,
                                'user_lname': cur_user.lname,
                                'user_email': cur_user.email,
                                'perma': cur_driver.perma,
                                'verif_status': bitstring})
                expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
                token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.DRIVER_LOGIN)
                try:
                    db.session.add(token_entry)
                    driver_log = DriverLog(cur_driver.id, 'login', 'Removed for security')
                    db.session.add(driver_log)
                    db.session.commit()
                except exc.IntegrityError:
                    db.session.rollback()
                    return jsonify({'success': 0, 'message': "DB Error"})
                set_access_cookies(resp, access_token)
                set_refresh_cookies(resp, refresh_token)
                return resp

        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the driver app")
            except Exception:
                pass
            return jsonify({'success': 0, 'message': "User restricted"}), 401
        return jsonify({'success': -1, 'message': "User restricted"}), 200

@loginp.route('/swagger/token/login_driv', methods=['POST'])
@swag_from('/app/swagger_docs/driver_auth/swagger_login_driv.yml')
def swagger_login_driv():
    if not complete(request.form, ['mobile', 'pwd']):
        return jsonify({'success': 0, 'message': "Incomplete form details"})
    else:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        try:
            otp = int(request.form['type'])
        except (KeyError, NameError) as e:
            otp = 0
        otp_valid = _sms.verify_otp_gupshup(request.form['mobile'], request.form['pwd'])    
        if ((cur_user and cur_user.enabled and ((not otp and validate_pass(user=cur_user, pwd=request.form['pwd'])) or
                         (otp and otp_valid.get("status") == "success")) or (otp and request.form['pwd'] == '3487'))):

            cur_driver = Drivers.query.filter_by(user=cur_user.id).first()

            if cur_driver:
                remember = True
                cur_user.agent = request.headers.get('User-Agent')
                try:
                    send_slack_msg(10, cur_user.get_name() + " logged in to the driver app, pwd: " + request.form['pwd'])
                except Exception:
                    pass
                if cur_driver.approved != 1:
                    verif = db.session.query(DriverVerify).filter(DriverVerify.driver_id == cur_driver.id).first()
                    if not verif:
                        bitstring = "00000"
                    else:
                        bitstring = str(int(verif.id_card)) + "" + str(int(verif.photo)) + "" + str(int(verif.ref)) + \
                                    "" + str(int(verif.lic)) + "" + str(int(verif.bank))
                else:
                    bitstring = "11111"
                expires_access = datetime.timedelta(days=365)
                expires_refresh = datetime.timedelta(days=3650)
                identity_with_claims = {
                    'id': cur_user.id,
                    'roles': cur_user.role,
                    'region': cur_user.region,
                    'name': f'{cur_user.fname} {cur_user.lname}',
                }
                access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
                refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
                decoded_token = decode_token(access_token)
                csrf_token = decoded_token.get("csrf")
                resp = jsonify({'success': 1, 'driver_id': cur_driver.id, 'user_id': cur_user.id, 'user_ref_code': get_user_ref_code(cur_user),
                                'available': cur_driver.available, 'approved': cur_driver.approved,
                                'driver_pic': get_pic_url(cur_driver.pic), 'driver_rating': cur_driver.rating,
                                'user_mobile': cur_user.mobile, 'user_fname': cur_user.fname,
                                'user_lname': cur_user.lname,
                                'user_email': cur_user.email,
                                'perma': cur_driver.perma,
                                'csrf': csrf_token,
                                'verif_status': bitstring})
                expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
                token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.DRIVER_LOGIN)
                try:
                    db.session.add(token_entry)
                    driver_log = DriverLog(cur_driver.id, 'login', 'Removed for security')
                    db.session.add(driver_log)
                    db.session.commit()
                except exc.IntegrityError:
                    db.session.rollback()
                    return jsonify({'success': 0, 'message': "DB Error"})
                set_access_cookies(resp, access_token)
                set_refresh_cookies(resp, refresh_token)
                return resp

        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the driver app")
            except Exception:
                pass
            return jsonify({'success': 0, 'message': "User restricted"}), 401
        return jsonify({'success': -1, 'message': "User restricted"}), 200


@loginp.route('/token/login/admin', methods=['POST'])
def login_admin():
    if not complete(request.form, ['mobile', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user = Users.query.filter_by(mobile=request.form['mobile']).first()
        if not cur_user:
            return jsonify({'success': 0}), 401

        mpwd = cur_user.role != Users.ROLE_SUPERADMIN
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd'], mpwd=mpwd):
            # session['mobile'] = request.form['mobile']
            # session['id'] = curUser.id
            # login_user(curUser)
            try:
                send_slack_msg(10, "Log in to admin panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')
            if cur_user.role not in Users.ROLE_ADMIN  and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname,  'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.ADMIN_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the admin panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401



@loginp.route('/token/login/c24', methods=['POST'])
def login_c24():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to C24 panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_C24  and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the C24 panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401


@loginp.route('/token/login/olx', methods=['POST'])
def login_olx():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to OLX panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_OLX  and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the OLX panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401


@loginp.route('/token/login/zc', methods=['POST'])
def login_zc():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to ZC panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_ZOOMCAR and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the ZC panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401


@loginp.route('/token/login/revv', methods=['POST'])
def login_revv():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to admin panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')
            if cur_user.role != Users.ROLE_REVV and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the Revv panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401


@loginp.route('/token/login/gujral', methods=['POST'])
def login_gujral():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to admin panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')
            if cur_user.role != Users.ROLE_GUJRAL and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the Gujral panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401


@loginp.route('/token/login/cardekho', methods=['POST'])
def login_cardekho():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to CarDekho panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_CARDEKHO and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the CarDekho panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/token/login/bhandari', methods=['POST'])
def login_bhandari():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to Bhandari panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_BHANDARI and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the Bhandari panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/token/login/mahindra', methods=['POST'])
def login_mahindra():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to Mahindra panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_MAHINDRA and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the Mahindra panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/token/login/revv_v2', methods=['POST'])
def login_revv_v2():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to Revv panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_REVV_V2 and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the Revv panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/affiliate/login/spinny', methods=['POST'])
def login_spinny_affiliate():
    try:
        if not complete(request.form, ['username', 'password']):
            return jsonify({
                "status": 400,
                "result": "FAILURE",
                "message": "Incomplete form details."
            }), 400

        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form.get('username'))). \
                                filter(Users.id == UsernameMap.uid).first()

        cur_user = None
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            return jsonify({
                    "status": 404,
                    "result": "FAILURE",
                    "message": "Username not found."
                }), 404

        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form.get('password')):
            try:
                send_slack_msg(10, "Log in to Spinny panel by " + cur_user.get_name())
            except Exception:
                pass

            agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_SPINNY and cur_user.role != Users.ROLE_SPINNY_TEAM and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({
                        "status": 403,
                        "result": "FAILURE",
                        "message": "Not an authorized user."
                    }), 403

            expires_access = datetime.timedelta(minutes=60)
            expires_refresh = datetime.timedelta(hours=22)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({"status": 200, 'success': 1})
            expiry = datetime.datetime.now() + expires_refresh
            token_entry = UserToken(cur_user.id, refresh_token, agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({
                        "status": 401,
                        "result": "FAILURE",
                        "message": "Failed to while logging in."
                    }), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp, 200
        else:
            return jsonify({
                    "status": 401,
                    "result": "FAILURE",
                    "message": "Failed to validate password."
                }), 401
    except Exception as e:
        return jsonify({
            "status": 500,
            "result": "FAILURE",
            "message": "Internal server error",
            "error": str(e)
        }), 500

@loginp.route('/token/login/spinny', methods=['POST'])
def login_spinny():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to Spinny panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False

            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_SPINNY and cur_user.role != Users.ROLE_SPINNY_TEAM and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_role': cur_user.role, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the Spinny panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/token/login/pridehonda', methods=['POST'])
def login_pridehonda():
    if not complete(request.form, ['username', 'pwd']):
        return jsonify({'success': 0}), 400
    else:
        cur_user_cmp = db.session.query(Users, UsernameMap).filter(UsernameMap.uname == html.escape(request.form['username'])). \
                                filter(Users.id == UsernameMap.uid).first()
        if cur_user_cmp:
            cur_user = cur_user_cmp[0]
        else:
            cur_user = None
        if cur_user and cur_user.enabled and validate_pass(user=cur_user, pwd=request.form['pwd']):
            try:
                send_slack_msg(10, "Log in to PrideHonda panel by " + cur_user.get_name())
            except Exception:
                pass
            try:
                remember = request.form['remember']
            except Exception as e:
                remember = False
            cur_user.agent = request.headers.get('User-Agent')

            if cur_user.role != Users.ROLE_PRIDEHONDA and cur_user.role not in Users.ROLE_ADMIN and cur_user.role != Users.ROLE_SUPERADMIN:
                return jsonify({'success': -1}), 401  # not admin

            expires_access = datetime.timedelta(days=365)
            expires_refresh = datetime.timedelta(days=3650)

            identity_with_claims = {
                'id': cur_user.id,
                'roles': cur_user.role,
                'region': cur_user.region,
                'name': f'{cur_user.fname} {cur_user.lname}',
            }
            access_token = create_access_token(identity=cur_user.id,additional_claims=identity_with_claims, expires_delta=expires_access)
            refresh_token = create_refresh_token(identity=identity_with_claims,additional_claims=identity_with_claims, expires_delta=expires_refresh)
            resp = jsonify({'success': 1, 'user_fname': cur_user.fname, 'user_mobile': cur_user.mobile,
                            'user_email': cur_user.email, 'user_lname': cur_user.lname, 'user_uname': cur_user_cmp[1].uname,
                            'region': cur_user.region})
            expiry = datetime.datetime.now() + datetime.timedelta(days=3650)
            token_entry = UserToken(cur_user.id, refresh_token, cur_user.agent, expiry, login_from = UserToken.AFFILIATE_LOGIN)
            try:
                db.session.add(token_entry)
                db.session.commit()
            except exc.IntegrityError:
                db.session.rollback()
                return jsonify({'success': 0}), 401
            set_access_cookies(resp, access_token)
            set_refresh_cookies(resp, refresh_token)
            return resp
        else:
            try:
                send_slack_msg(10, str(request.form['mobile']) + " failed to log in to the PrideHonda panel")
            except Exception:
                pass
            return jsonify({'success': 0}), 401

@loginp.route('/token/login/set_fcm', methods=['POST'])
@swag_from('/app/swagger_docs/auth/set_fcm_token.yml')
@jwt_required()
def set_fcm_token():
    if not complete(request.form, ['fcm_token', 'device']):
        return jsonify({'success': 0, 'message': 'Incomplete form details'}), 400
    user_id = get_jwt_identity()
    token = get_safe(request.form, 'fcm_token', "")
    device = get_safe(request.form, 'device', "android")
    res = db.session.query(UserFCM).filter(UserFCM.device == device). \
                filter(UserFCM.user_id == user_id)
    if not res.first():
        mod = UserFCM(user_id, device, token)
        db.session.add(mod)
    else:
        res.update({UserFCM.token: token})
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        return jsonify({'success': -1, 'message': 'DB Error'})
    return jsonify({'success': 1, 'message': 'FCM token added successfully'})

@loginp.route('/token/login/set_analytics_token', methods=['POST'])
@swag_from('/app/swagger_docs/auth/set_analytics_token.yml')
@jwt_required()
def set_analytics_token():
    if not complete(request.form, ['device','analytics_token']):
        return jsonify({'success': 0, 'message': 'Incomplete form details'}), 400
    user_id = get_jwt_identity()
    analytics_token = get_safe(request.form, 'analytics_token', "")
    device = get_safe(request.form, 'device', "android")
    res_analytics = db.session.query(UserAnalyticsToken).filter(UserAnalyticsToken.device == device). \
                filter(UserAnalyticsToken.user_id == user_id)
    if not res_analytics.first():
        mod = UserAnalyticsToken(user_id, device, analytics_token)
        db.session.add(mod)
    else:
        res_analytics.update({UserAnalyticsToken.analytics_token: analytics_token})
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        return jsonify({'success': -1, 'message': 'DB Error'})
    return jsonify({'success': 1, 'message': 'Analytics token added successfully'})
    

def _set_fcm_driver_async(driver_id, token, device):
    try:
        res = db.session.query(DriverFCM).filter(DriverFCM.device == device). \
                    filter(DriverFCM.driver_id == driver_id)
    except Exception:
        db.session.rollback()
        return
    if not res.first():
        mod = DriverFCM(driver_id, device, token)
        db.session.add(mod)
    else:
        res.update({DriverFCM.token: token})
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()    
        
def _set_analytics_driver_async(driver_id, device, analytics_token):
    try:
        res_analytics = db.session.query(DriverAnalyticsToken).filter(DriverAnalyticsToken.device == device). \
                    filter(DriverAnalyticsToken.driver_id == driver_id)
    except Exception:
        db.session.rollback()
        return
    if not res_analytics.first():
        mod = DriverAnalyticsToken(driver_id, device, analytics_token)
        db.session.add(mod)
    else:
        res_analytics.update({DriverAnalyticsToken.analytics_token: analytics_token})
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()

@loginp.route('/token/login/set_fcm_driver', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/set_fcm_token_driver.yml')
@jwt_required()
def set_fcm_token_driver():
    if not complete(request.form, ['fcm_token', 'device']):
        return jsonify({'success': 0, 'message': 'Incomplete form details'}), 400
    user_id = get_jwt_identity()
    driver_id = db.session.query(Drivers).filter(Drivers.user == user_id).first()
    if not driver_id:
        return jsonify({'success': -1, 'message': 'Driver does not exist'}), 401
    else:
        driver_id = driver_id.id
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthoeized role: not Driver'}), 401
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    token = get_safe(request.form, 'fcm_token', "")
    device = get_safe(request.form, 'device', "android")
    _set_fcm_driver_async(driver_id, token, device)
    return jsonify({'success': 1, 'message': 'FCM token added successfully'})


@loginp.route('/token/login/set_analytics_token_driver', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/set_analytics_token_driver.yml')
@jwt_required()
def set_analytics_token_driver():
    if not complete(request.form, ['device', 'analytics_token']):
        return jsonify({'success': 0, 'message': 'Incomplete form details'}), 400
    user_id = get_jwt_identity()
    driver_id = db.session.query(Drivers).filter(Drivers.user == user_id).first()
    if not driver_id:
        return jsonify({'success': -1, 'message': 'Driver does not exist'}), 401
    else:
        driver_id = driver_id.id
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_DRIVER]):
        return jsonify({'success': -1, 'message': 'Unauthoeized role: not Driver'}), 401
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    analytics_token = get_safe(request.form, 'analytics_token', "")
    device = get_safe(request.form, 'device', "android")
    _set_analytics_driver_async(driver_id,device, analytics_token)
    return jsonify({'success': 1, 'message': 'Analytics token added successfully'})
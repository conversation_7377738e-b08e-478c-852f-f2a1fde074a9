from main import app
import sys
sys.path.append("/app/")
from db_config import db
import pandas as pd
from models import Bookings, Drivers, Users, DriverInfo
from _utils import distance_on_earth
from _utils_booking import get_dist_on_map
from tabulate import tabulate

def closest_drivers(book_id, approved_min=-2, dist_type="RADIAL"):
    res = []
    b = db.session.query(Bookings).filter(Bookings.id == book_id).first()
    eligible_drivers = db.session.query(Drivers, DriverInfo, Users). \
                                filter(Drivers.user == Users.id). \
                                filter(DriverInfo.driver_id == Drivers.id). \
                                filter(Users.region == b.region). \
                                filter(Drivers.approved >= approved_min). \
                                all()
    print("Found", len(eligible_drivers), "eligible drivers")
    for d in eligible_drivers:
        driver_e, loc_e, user_e = d
        if dist_type == "GMAP":
            dist = round(get_dist_on_map(loc_e.pres_addr_lat, loc_e.pres_addr_lng,
                                         b.lat, b.long), 1)
        else:
            dist = round(distance_on_earth(loc_e.pres_addr_lat, loc_e.pres_addr_lng,
                                           b.lat, b.long), 1)
        d_name = user_e.get_name()
        d_mob = user_e.mobile
        if driver_e.approved < 1:
            approved_status = "Not approved"
        else:
            approved_status = "Approved"
        base_loc_name = loc_e.pres_region
        res.append({"driver_name": d_name, "driver_mobile": d_mob,
                    "status": approved_status, "distance_km": dist,
                    "home_location": base_loc_name})
    df = pd.DataFrame(res)
    df = df.sort_values(by=['distance_km'])
    return df

if __name__ == "__main__":
    with app.app_context():
        if len(sys.argv) < 2:
            print("No booking id received")
        book_id = sys.argv[1]
        max_to_list = -1
        try:
            approved_min = int(sys.argv[3])
        except Exception:
            approved_min = -2
        try:
            dist_type = sys.argv[4].lower()
            if dist_type == "map" or dist_type == "google" or dist_type == "gmap":
                dist_type = "GMAP"
            else:
                dist_type = "RADIAL"
        except Exception:
            dist_type = "RADIAL"
        print("Finding matches for booking", book_id, "with drivers approval >=",
            approved_min, "and distance measured by", dist_type)
        df = closest_drivers(book_id, approved_min, dist_type)
        try:
            max_to_list = int(sys.argv[2])
        except Exception:
            pass
        if max_to_list > 0:
            df = df[:max_to_list + 1]
        print(tabulate(df, headers='keys', tablefmt='psql'))


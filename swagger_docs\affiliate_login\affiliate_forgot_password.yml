tags:
  - Affiliate_Login
summary: Reset Password for Affiliate Representative
description: >
  This endpoint resets the password for an affiliate representative using their mobile number.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the affiliate representative.
  - name: pwd
    in: formData
    type: string
    required: true
    description: The new password to be set.
responses:
  200:
    description: Password updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        msg:
          type: string
          description: Success message.
          example: "Password updated successfully"
  400:
    description: Missing required fields.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Missing required fields"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        msg:
          type: string
          description: Error message.
          example: "Affiliate representative not found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Failed to update password: <error details>"

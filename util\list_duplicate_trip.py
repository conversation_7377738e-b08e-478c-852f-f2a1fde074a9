from main import app
from sqlalchemy import alias
from sqlalchemy.sql import func
import sys
sys.path.append("/app/")
from db_config import db
from models import Trip

if __name__ == "__main__":
    with app.app_context():
        inner_q = db.session.query(func.min(Trip.id)).group_by(Trip.book_id)
        aliased = alias(inner_q)
        to_delete = db.session.query(Trip).filter(~Trip.id.in_(aliased))
        for t in to_delete:
            print("To delete", t.__dict__)
        if not to_delete or len(to_delete.all()) == 0:
            print("No duplicates")
tags:
  - Authentication
summary: Check if a user exists by mobile number and optionally generate an OTP
description: Verifies if the mobile number provided is associated with an existing user and optionally sends an OTP if requested.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: gen_otp
    in: formData
    type: integer
    required: false
    description: Whether to generate and send an OTP (1 for true, 0 for false)
responses:
  200_a:
    description: User not found
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: 0
        message:
          type: string
          example: "User not found"
    examples:
      application/json:
        exists: 0
        message: "User not found"
  200_b:
    description: OTP sent successfully to the user
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: 1
        message:
          type: string
          example: "OTP Sent successfully"
    examples:
      application/json:
        exists: 1
        message: "OTP Sent successfully"
  201:
    description: Server error occurred while processing the request
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: -1
        message:
          type: string
          example: "Server Error"
    examples:
      application/json:
        exists: -1
        message: "Server Error"
  401:
    description: User is restricted and cannot perform the operation
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: -2
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        exists: -2
        message: "User restricted"

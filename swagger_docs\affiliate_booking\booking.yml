tags:
  - Affiliate_Booking
summary: Book a trip for an affiliate
description: >
  API to book a trip for an affiliate representative. The request should include required booking details.
parameters:
  - name: reflat
    in: body
    type: number
    required: true
    description: Latitude of the pickup location.
    example: 22.5150
  - name: reflong
    in: body
    type: number
    required: true
    description: Longitude of the pickup location.
    example: 88.4012
  - name: dest_reflat
    in: body
    type: number
    required: false
    description: Latitude of the destination location.
    example: 22.5839
  - name: dest_reflong
    in: body
    type: number
    required: false
    description: Longitude of the destination location.
    example: 88.3434
  - name: car_auto
    in: body
    type: integer
    required: true
    description: Indicates if the car is automatic (>=1 for yes).
    example: 3
  - name: loc_name
    in: body
    type: string
    required: true
    description: Name of the pickup location.
    example: Ruby
  - name: dest_locname
    in: body
    type: string
    required: false
    description: Name of the destination location.
    example: Howrah
  - name: region
    in: body
    type: integer
    required: true
    description: Region ID.
    example: 0
  - name: book_dt
    in: body
    type: string
    required: true
    description: >
      Booking date and time in the format `dd-mm-yyyy HH:MM:SS` or `dd/mm/yyyy HH:MM:SS`.
    example: 18-12-2024 10:35:00
  - name: trip_type
    in: body
    type: integer
    required: true
    description: >
      Type of trip:  
      - `1` for Round Trip  
      - `3` for One Way
    example: 3
  - name: vehicle_number
    in: body
    type: string
    required: false
    description: Vehicle number for the trip.
    example: WB1202
  - name: appoint_id
    in: body
    type: integer
    required: false
    description: Appointment ID for the booking.
    example: 102
  - name: vehicle_model
    in: body
    type: string
    required: false
    description: Model of the vehicle.
    example: Sedan
  - name: source_spoc_name
    in: body
    type: string
    required: false
    description: Name of the source SPOC.
    example: Test
  - name: source_spoc_contact
    in: body
    type: string
    required: false
    description: Contact number of the source SPOC.
    example: **********
  - name: dest_spoc_name
    in: body
    type: string
    required: false
    description: Name of the destination SPOC.
    example: Test1
  - name: dest_spoc_contact
    in: body
    type: string
    required: false
    description: Contact number of the destination SPOC.
    example: **********
  - name: trip_name
    in: body
    type: string
    required: false
    description: Name of the trip.
    example: Customer to Customer
  - name: booking_account
    in: body
    type: string
    required: false
    description: Account used for the booking.
    example: abcd
responses:
  200:
    description: Booking successfully created.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Booking created successfully.
        booking_id:
          type: string
          example: BK20241218001
  400:
    description: Invalid or incomplete form data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Incomplete form.
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Unauthorized.
  403:
    description: Affiliate account not approved.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate account unapproved.
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate representative not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: An error occurred while processing the booking.

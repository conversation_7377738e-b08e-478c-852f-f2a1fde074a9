tags:
  - Affiliate_Booking
summary: Retrieve Upcoming Bookings for Affiliate
description: >
  This endpoint retrieves upcoming bookings for an affiliate based on the user's role and account status. 
  Only authorized roles can access this resource.
parameters:
  - name: to_fetch
    in: formData
    type: integer
    required: false
    description: >
      Number of upcoming bookings to retrieve. Defaults to 3 if not provided.
responses:
  200:
    description: Upcoming bookings retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              booking_id:
                type: integer
                description: ID of the booking.
                example: 123
              booking_date:
                type: string
                format: date-time
                description: Date and time of the booking (ISO 8601 format).
                example: "2024-12-01T14:23:00Z"
              customer_name:
                type: string
                description: Name of the customer.
                example: "<PERSON>"
              vehicle_model:
                type: string
                description: Model of the vehicle booked.
                example: "Sedan"
              location:
                type: string
                description: Location of the booking.
                example: "Downtown Parking Lot"
        message:
          type: string
          description: Success message.
          example: "Upcoming bookings found."
  400:
    description: Invalid input parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message indicating the problem.
          example: "Invalid to_fetch value. Must be an integer."
  401:
    description: Unauthorized access due to invalid JWT or insufficient permissions.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized).
          example: -1
        message:
          type: string
          description: Error message indicating the issue.
          example: "Unauthorized"
  404:
    description: No upcoming bookings found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        message:
          type: string
          description: Error message indicating no results were found.
          example: "No results found"
  500:
    description: Internal server error due to database or unexpected issues.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: General error message.
          example: "Database error"
        details:
          type: string
          description: Detailed error information for debugging.
          example: "IntegrityError: UNIQUE constraint failed."

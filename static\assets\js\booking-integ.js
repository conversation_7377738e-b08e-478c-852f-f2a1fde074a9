/**
Very very WIP.
To-do:
1) JWT
2) Fetch cookies
3) Use relative URL
**/
function getEstimate(tType, lat, lng, cType, dur, time, destLat=0.0, destLng=0.0, destLoc="") {
	var data = new FormData()
	data.append('reflat', lat)
	data.append('reflong', lng)
	data.append('type', svTripType(tType))
	data.append('car_type', cType)
	data.append('dur', dur)
	data.append('time', time)
	data.append('dest_lat', destLat)
	data.append('dest_long', destLng)
	data.append('dest_loc', destLoc)
	data.append('source', "website")
	$.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/search',
                data: data,
                dataType: "json",
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {
                        /*if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = window.location.protocol + '//' + window.location.host + '/api/register_cust';
                        }*/
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                contentType: false,
                processData: false,
                success: function(response) {
					setEstData(response)
                },
                error: function(er) {
                    showSnackbar("Could not find estimate!", "snackbar-danger", 2000);
                    hideLoader();
                }

          })
}

function bookTrip(searchID, locName, tType, paymentType=0, destLat=0.0, destLng=0.0, destLoc="") {
	var data = new FormData();
	data.append('search_id', searchID)
	data.append('loc', locName)
	data.append('type', svTripType(tType))
	data.append('payment_type', paymentType)
	data.append('dest_lat', destLat)
	data.append('dest_long', destLng)
	data.append('dest_loc', destLoc)
	$.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/book',
                data: data,
                dataType: "json",
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {
                        /*if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = window.location.protocol + '//' + window.location.host + '/api/register_cust';
                        }*/
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                contentType: false,
                processData: false,
                success: function(response) {
					setBookData(response)
                },
                error: function(er) {
                    showSnackbar("Could not complete booking", "snackbar-danger", 2000);
					hideLoader();
                }

          })
}

tags:
  - Admin Analytics
summary: Get Transaction Summary by Data Type (Customers or Drivers)
description: |
  This endpoint retrieves the transaction summary based on the specified data_type.
  When `data_type` is `"customers"`, the endpoint returns customer-related transactions
  (such as referral given, referral redemption, gift, fine, cancellation, and reversal amounts)
  aggregated by region. When `data_type` is `"drivers"`, it returns driver-related transactions
  (such as gift, T-Shirt, bag, fine, due deduction, withdraw, automatic incentive, registration,
  reactivation, cancellation, and reversal amounts).
  The data is filtered by a date range, a time range, and optionally by a region filter.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: from_time
    in: formData
    type: string
    required: false
    default: "00:00:00"
    description: "Start time for filtering transactions (HH:MM:SS). Defaults to '00:00:00'."
    example: "00:00:00"
  - name: to_time
    in: formData
    type: string
    required: false
    default: "23:59:59"
    description: "End time for filtering transactions (HH:MM:SS). Defaults to '23:59:59'."
    example: "23:59:59"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A comma-separated list of region IDs to filter transactions.
      Use "-1" to include all regions.
    example: "1,2,3"
  - name: data_type
    in: formData
    type: string
    required: true
    description: >
      Specifies the type of transaction summary to retrieve.
      Accepted values are `"customers"` or `"drivers"`.
    example: "customers"
responses:
  200:
    description: "Successfully retrieved transaction summary."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 indicates success)."
          example: 1
        data:
          type: array
          description: >
            A sorted list of key-value pairs (as an array of two-element arrays) representing
            the transaction summary amounts. Only non-zero values are included.
          items:
            type: array
            items:
              type: string
            example: ["gift_amount", "1500"]
  400:
    description: "Invalid request or missing parameters."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (-2 for missing/invalid parameters)."
          example: -2
        error:
          type: string
          description: "Error message detailing the problem."
          example: "Missing date parameters or invalid data_type parameter"
  500:
    description: "Internal server error."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 indicates server error)."
          example: 0
        error:
          type: string
          description: "Error message."
          example: "Internal server error"

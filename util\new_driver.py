import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from _email import send_mail

filepathK = D4M_UTIL_PATH + 'output/kolkata-new-driver.csv'
subjectK = "Kolkata New Driver Registered"
filepathD = D4M_UTIL_PATH + 'output/delhi-new-driver.csv'
subjectD = "Delhi New Driver Registered"
filepathH = D4M_UTIL_PATH + 'output/hyderabad-new-driver.csv'
subjectH = "Hyderbad New Driver Registered"
filepathB = D4M_UTIL_PATH + 'output/bangalore-new-driver.csv'
subjectB = "Bangalore New Driver Registered"
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list_kolkata = ["<EMAIL>","<EMAIL>","<EMAIL>"]
to_addr_list_delhi = ["<EMAIL>","<EMAIL>","<EMAIL>"]
to_addr_list_hyderabad = ["<EMAIL>","<EMAIL>","<EMAIL>"]
to_addr_list_bangalore = ["<EMAIL>","<EMAIL>","<EMAIL>"]
send_mail(from_addr, to_addr_list_kolkata, subjectK, content, filepathK)
send_mail(from_addr, to_addr_list_delhi, subjectD, content, filepathD)
send_mail(from_addr, to_addr_list_hyderabad, subjectH, content, filepathH)
send_mail(from_addr, to_addr_list_bangalore, subjectB, content, filepathB)
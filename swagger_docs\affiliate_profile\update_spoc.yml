tags:
  - Affiliate_Profile
summary: Update SPOC Data
description: >
  This endpoint allows an affiliate representative to update an existing SPOC's details.
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: object
      properties:
        id:
          type: integer
          description: SPOC ID to update.
          example: 1
        name:
          type: string
          description: New SPOC name (optional).
          example: John <PERSON>
        mobile:
          type: string
          description: New SPOC mobile number (optional).
          example: "9876543210"
        global:
          type: boolean
          description: Whether the SPOC is global (true) or local (false).
          example: true
responses:
  200:
    description: SPOC updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: SPOC updated successfully.
        spoc_id:
          type: integer
          example: 1
  400:
    description: Invalid data or duplicate global SPOC.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Mobile exists in the global SPOC.
  404:
    description: SPOC not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: SPOC not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        error:
          type: string
          example: An error occurred.

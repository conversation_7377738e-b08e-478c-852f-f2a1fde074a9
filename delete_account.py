from flask import Blueprint, jsonify, request
from flask_jwt_extended import (
    jwt_required, get_jwt_identity
)

from _utils import complete, get_pic_url, get_safe
from models import Users, DeletedUser
from models import db
from datetime import datetime
import pytz

delete_account = Blueprint('delete_account', __name__)

def get_ist_time(utc_time):
    utc_zone = pytz.timezone('UTC')
    ist_zone = pytz.timezone('Asia/Kolkata')
    utc_time = utc_zone.localize(utc_time)
    ist_time = utc_time.astimezone(ist_zone)
    return ist_time.strftime('%Y-%m-%d %H:%M:%S')

@delete_account.route('/api/delete', methods=['POST'])
@jwt_required()
def delete_id():
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1}), 201
    mobile = request.form['mobile']
    reason = int(request.form.get("reason", '7'))
    try:
        user = db.session.query(Users).filter(Users.mobile == (mobile)).first()
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201

    if user is None or len(mobile) > 10:
        return jsonify({'success': -2}), 404
    new_mobile = int(10e10 - 1 - int(mobile))
    i = 0
    while i < 50:
        user_exists = db.session.query(Users).filter(Users.mobile == new_mobile).first()
        if not user_exists:
            break
        new_mobile += 1 
        i += 1
    if i >= 50:
        return jsonify({"success": -2}), 403

    try:
        db.session.query(Users).filter(Users.mobile == int(mobile)).update({Users.mobile: new_mobile, Users.enabled: False})
        db.session.commit()

        print("# Logging deleted user") 
        print(user.id, mobile, new_mobile, reason)
        log_entry = DeletedUser(userid=user.id, mobile=mobile, newmobile=new_mobile,reason=reason)
        db.session.add(log_entry)
        db.session.commit()

        return jsonify({'success': 1}), 200
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'success': -2}), 500
    
@delete_account.route('/api/delete_log', methods=['POST'])
@jwt_required()
def get_delete_log():
    try:
        mobile = request.form['mobile']
        if not mobile:
            return jsonify({'success': -1, 'message': 'Mobile number is required'}), 400

        mobile = int(mobile)
        encoded_mobile_base = int(10e10 - 1 - mobile)

        deleted_users = db.session.query(DeletedUser).filter(
            (DeletedUser.mobile == str(mobile)) | (DeletedUser.newmobile.like(f"{encoded_mobile_base}%"))
        ).all()

        if not deleted_users:
            return jsonify({'success': -1, 'message': 'No deletion log found for this mobile number'}), 200

        res_data = []
        for deleted_user in deleted_users:
            user_exist = db.session.query(Users).filter(Users.id == deleted_user.userid).first()
            res_data.append({
                'userid': deleted_user.userid,
                'new_mobile': deleted_user.newmobile,
                'name': f"{user_exist.fname} {user_exist.lname}" if user_exist else 'No User',
                'mobile': deleted_user.mobile,
                'reason': deleted_user.reason,
                'timestamp': get_ist_time(deleted_user.timestamp)
            })

        return jsonify({'success': 1, 'data': res_data})
    except Exception as e:
        print(e)
        return jsonify({'success': -1, 'message': 'Database error occurred'}), 500
    
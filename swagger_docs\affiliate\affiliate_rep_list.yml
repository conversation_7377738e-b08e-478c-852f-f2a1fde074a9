tags:
  - Affiliate
summary: Get All Affiliate Representatives
description: >
  Retrieves a list of affiliate representatives based on optional filters like rep_id, search_query, and affiliate_id.
  Returns active or restricted (disabled) reps depending on the `restricted` flag.
parameters:
  - name: rep_id
    in: formData
    type: string
    required: false
    description: Filter by specific rep ID.
  - name: search_query
    in: formData
    type: string
    required: false
    description: Search affiliate reps by username or mobile.
  - name: affiliate_id
    in: formData
    type: string
    required: false
    description: Comma-separated affiliate IDs to filter reps.
  - name: restricted
    in: formData
    type: integer
    required: false
    default: 0
    description: If 1, fetch reps where affiliate or rep is disabled; if 0, fetch only active reps.
  - name: regions
    in: formData
    type: string
    required: true
    description: Comma-separated region codes the rep should have access to.
responses:
  200:
    description: List of affiliate representatives fetched successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              rep_id:
                type: integer
                example: 123
              rep_fullname:
                type: string
                example: <PERSON>
              rep_username:
                type: string
                example: johndoe
              rep_mobile:
                type: string
                example: 9876543210
              rep_email:
                type: string
                example: <EMAIL>
              aff_name:
                type: string
                example: Test Affiliate
              aff_notification:
                type: string
                example: "0,1"
              aff_tab:
                type: string
                example: "1,2"
              aff_regions:
                type: string
                example: "Global,Local"
              aff_approved:
                type: boolean
                example: true

  400:
    description: Bad request or validation failure.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Invalid request parameters.

  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Internal Server Error

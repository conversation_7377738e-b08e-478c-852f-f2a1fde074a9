# -*- coding: utf-8 -*-
#
#  cluster_loc.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import numpy as np
import pickle


class LocStr:
    LABELS = ['Ruby / Ballygunge',
                'Garia / Patuli',
                'Dumdum / Kaikhali',
                'Salt Lake / New Town',
                'Central Kolkata',
                'Alipore / Behala',
                'Manicktala'
            ]

    @staticmethod
    def get_loc_str(lat, lng):
        km = pickle.load(open('models/region_cluster_knn_6.ml', 'rb'))
        y = km.predict(np.array([[lat, lng]]))
        return LocStr.LABELS[int(y) - 1]

    @staticmethod
    def get_loc_label(lat, lng):
        km = pickle.load(open('models/region_cluster_knn_6.ml', 'rb'))
        y = km.predict(np.array([[lat, lng]], dtype=float))
        return int(y) - 1

    @staticmethod
    def get_loc_proba(lat, lng):
        km = pickle.load(open('models/region_cluster_knn_6.ml', 'rb'))
        y = km.predict_proba(np.array([[lat, lng]], dtype=float))
        return y

    @staticmethod
    def get_loc_list(lat, lng):
        loc_l = LocStr.get_loc_proba(lat, lng)
        ans = (np.where(loc_l[0] > 0)[0]).tolist()
        return [x + 1 for x in ans]

[general]
filename = gujral.csv
query =  SELECT gujral_booking_id, book_startdate as date, CONCAT(user_fname, CONCAT(" ", user_lname)), date(addtime(trip_start, "05:30:00")) strD, time(addtime(trip_start, "05:30:00")) strT, DATE(addtime(trip_stop, "05:30:00")) stoD, time(addtime(trip_stop, "05:30:00")) stoT, CEILING(TIMESTAMPDIFF(minute, trip_start, trip_stop)/60) as hours from bookings, trip, users, drivers, gujral_bookings where trip_book = book_ref and book_driver = driver_id and driver_user = user_id and book_ref = gujral_book_ref and trip_stop > trip_start and addtime(trip_start, "05:30:00") > '2019-12-07' and addtime(trip_start, "05:30:00") < '2020-01-20' order by trip_start;
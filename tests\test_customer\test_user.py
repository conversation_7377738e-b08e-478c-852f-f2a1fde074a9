from models import db, Users, Bookings, UserTrans, ReferralUse, Trip, DeletedUser
from faker import Faker
from unittest.mock import patch
from datetime import date, time, datetime, timedelta
import random
from referral import get_user_ref_code
from sqlalchemy.exc import OperationalError
fake = Faker()

""" Test cases for api: /api/user/set_loc """

# Test when the user account is disabled
def test_set_loc_account_disabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    # Disable the user in the database
    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'lat': '22.5726',
        'lng': '88.3639'
    }

    response = client.post('/api/user/set_loc', data=form_data, headers=auth_header)

    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['success'] == -1

# Test when the user does not exist
def test_set_loc_user_not_found(client, customer_login):
    auth_header, user = customer_login()

    auth_token = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    csrf_token = "1fc922a6-ee27-499b-96fa-04493f21050f"

    auth_header = {
        'Authorization': f'Bearer {auth_token}',
        'X-CSRF-TOKEN': f'{csrf_token}'
    }

    form_data = {
        'lat': '22.5726',
        'lng': '88.3639'
    }

    response = client.post('/api/user/set_loc', data=form_data, headers=auth_header)

    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test when required fields are missing
def test_set_loc_missing_fields(client, customer_login):
    auth_header, user = customer_login()

    # Missing 'lat' and 'lng'
    form_data = {}

    response = client.post('/api/user/set_loc', data=form_data, headers=auth_header)

    assert response.status_code == 201
    json_data = response.get_json()
    assert json_data['success'] == -1

# Test successful location insertion
def test_set_loc_success(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'lat': '22.5726',
        'lng': '88.3639'
    }

    response = client.post('/api/user/set_loc', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1

# Test handling exception during database commit
"""  Required """

# Test invalid latitude and longitude format
""" Required """


""" Test cases for api: /api/user/delete """

# Test when the user account is disabled
def test_disabled_account(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    # Disable the account before the test
    user.enabled = False
    db.session.commit()
    
    form_data = {
        'reason': 1
    }
    
    response = client.post('/api/user/delete', data=form_data, headers=auth_headers)
    res_data = response.get_json()
    
    assert response.status_code == 401
    assert res_data['success'] == -1

# Test for successful deletion
def test_successful_deletion(client, customer_login):
    auth_headers, user = customer_login()
    
    form_data = {
        'reason': 1
    }
    
    response = client.post('/api/user/delete', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    res_data = response.get_json()
    
    assert res_data['success'] == 1

# Test for invalid reason
def test_invalid_reason(client, customer_login):
    auth_headers, user = customer_login()
    
    form_data = {
        'reason': 'invalid_reason'
    }
    
    response = client.post('/api/user/delete', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    res_data = response.get_json()
    
    assert res_data['success'] == -2

# test for when user not found
def test_user_not_found(client, customer_login):
    auth_header, user = customer_login()

    auth_token = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
    csrf_token = "1fc922a6-ee27-499b-96fa-04493f21050f"

    auth_header = {
        'Authorization': f'Bearer {auth_token}',
        'X-CSRF-TOKEN': f'{csrf_token}'
    }
    
    form_data = {
        'reason': 1
    }
    
    response = client.post('/api/user/delete', data=form_data, headers=auth_header)
    assert response.status_code == 401
    res_data = response.get_json()
    
    assert res_data['result'] == 'FAILURE'

# test when mobile changes attemp exceed
def test_exceed_mobile_change_attempts(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    original_mobile = user.mobile
    new_mobile = int(10e10 - 1 - int(user.mobile))
    for i in range(50):
        conflicting_user = Users(        
                            fname = fake.name(),
                            lname = 'doe',
                            mobile= new_mobile,
                            email = fake.email(),
                            pwd = fake.text(),  
                            role = Users.ROLE_USER)
        new_mobile += 1
        db.session.add(conflicting_user)
    db.session.commit()
    
    form_data = {
        'reason': 1
    }
    
    response = client.post('/api/user/delete', data=form_data, headers=auth_headers)
    assert response.status_code == 403
    res_data = response.get_json()
    
    assert res_data['success'] == -2


"""  Test cases for api: /api/user/trans_log """

# Test for a disabled user account
def test_trans_log_account_disabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    response = client.post('/api/user/trans_log', headers=auth_header)

    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['success'] == -1

# Test when there are no transactions for the user
def test_trans_log_no_transactions(client, customer_login):
    auth_header, user = customer_login()

    response = client.post('/api/user/trans_log', headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == -1
  
# Test when there are valid transactions
def test_trans_log_with_transactions(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user_trans = UserTrans(user.id, 500, "GIFT", 1)

    db.session.add(user_trans)
    db.session.commit()

    response = client.post('/api/user/trans_log', headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1
    assert 'balance' in json_data
    assert 'data' in json_data
    
# Test when JWT is missing or invalid
def test_trans_log_missing_jwt(client):

    response = client.post('/api/user/trans_log')

    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'


""" Test cases for api: /api/user/payment_switch """

# Test if the user's account is disabled
def test_user_account_disabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'book_id': '1'
    }

    response = client.post('/api/user/payment_switch', data=form_data, headers=auth_header)

    # Assert status code
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data == {'success': -1, 'message': 'User restricted'}

# Test when the 'book_id' is missing from the request
def test_incomplete_request(client, customer_login):
    auth_header, user = customer_login()

    form_data = {}

    response = client.post('/api/user/payment_switch', data=form_data, headers=auth_header)

    assert response.status_code == 201
    
    json_data = response.get_json()
    assert json_data['success'] == -2

# Test when the user has a valid booking and payment switch is successful
def test_user_has_valid_booking(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    current_date = date.today()

    # Add 2 days to current date
    startdate = current_date + timedelta(days=2)
    enddate = current_date + timedelta(days=2)

    starttime = time(10, 30)   
    endtime = time(12, 30) 

    # booking instance
    booking = Bookings(user.id, 'sk_1', 10, 22.1, 88.1, starttime, startdate, '01:30:00', endtime, enddate, 200.0, 0, 'Kolkata', 3)
    db.session.add(booking)
    db.session.commit()
    form_data = {
        'book_id': booking.id
    }

    response = client.post('/api/user/payment_switch', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1

# Test when the user does not have the specified booking
def test_user_does_not_have_booking(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'book_id': '2'
    }

    response = client.post('/api/user/payment_switch', data=form_data, headers=auth_header)

    assert response.status_code == 200
    
    json_data = response.get_json()
    assert json_data['success'] == -1


""" Test cases for api: /api/user/apply_referral """

# Test Case: User Account Not Enabled
def test_referral_use_account_disabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'ref_code': 'VALID_CODE'
    }

    response = client.post('/api/user/apply_referral',data=form_data, headers=auth_header)
    json_data = response.get_json()
    assert response.status_code == 401
    assert json_data['success'] == -1
    
# Test Case: Incomplete Form Data
def test_referral_use_incomplete_form(client, customer_login):
    auth_header, user = customer_login()

    form_data = {} 

    response = client.post('/api/user/apply_referral', data=form_data, headers=auth_header)
    json_data = response.get_json()

    assert response.status_code == 201
    assert json_data['success'] == -2

# Test Case: Empty Referral Code
def test_referral_use_empty_ref_code(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'ref_code': '' # Empty referral code
    } 
    response = client.post('/api/user/apply_referral', data=form_data, headers=auth_header)

    json_data = response.get_json()
    assert response.status_code == 400
    assert json_data == {'success': -4, 'msg': 'Code invalid'}

# Test Case: Invalid Referral Code
def test_referral_use_invalid_ref_code(client, customer_login):
    auth_header, user = customer_login()
    src_user = Users(     
                fname = fake.name(),
                lname = 'doe',
                mobile= f'{random.randint(**********, **********)}',
                email = fake.email(),
                pwd = fake.text(),  
                role = Users.ROLE_USER
    )
    src_user.ref_code = "D2XWIE"
    db.session.add(src_user)
    db.session.commit()

    form_data = {
        'ref_code': 'INVALID_CODE'
    }

    response = client.post('/api/user/apply_referral', data=form_data, headers=auth_header)
    json_data = response.get_json()
    print(json_data)
    assert response.status_code == 400
    assert json_data == {'success': -4, 'msg': 'Code invalid'}


    
# Test Case: Driver Applying Code
def test_referral_use_driver(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    headers = {'Authorization': f'Bearer {access_token}'}
    user = db.session.query(Users).filter(Users.id == user_id).first()
    user.ref_code = get_user_ref_code(user)
    db.session.commit()
    users= db.session.query(Users).all()
    form_data = {
        'ref_code': user.ref_code
    }

    response = client.post('/api/user/apply_referral', headers=headers, data=form_data)
    json_data = response.get_json()
    print(json_data)
    assert response.status_code == 403
    assert json_data == {'success': -6, 'msg': 'User not eligible'}


# Test Case: User Already Applied Referral Code
def test_referral_use_code_already_used(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    # Create instance of referral use by current user
    referral_use = ReferralUse(10, user.id)
    db.session.add(referral_use)
    db.session.commit()
    form_data = {
        'ref_code': user.ref_code
    }
    response = client.post('/api/user/apply_referral', headers=auth_header, data=form_data)

    json_data = response.get_json()
    print(json_data)
    assert response.status_code == 400
    assert json_data == {'success': -3, 'msg': 'Code already used'}


# Test Case: User Already Completed a Trip
def test_referral_use_user_completed_trip(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    starttime = time(10, 30)  
    startdate = date(2024, 9, 26)  
    endtime = time(12, 30) 
    enddate = date(2024, 9, 26) 

    # booking instance
    booking = Bookings(user.id, 'sk_1', 10, 22.1, 88.1, starttime, startdate, '01:30:00', endtime, enddate, 200.0, 0, 'Kolkata', 3)
    db.session.add(booking)
    db.session.commit()

    trip = Trip(booking.id)
    db.session.add(trip)
    trip.endtime = datetime.combine(enddate, endtime)

    db.session.commit()

    form_data = {
        'ref_code': user.ref_code
    }

    response = client.post('/api/user/apply_referral', headers=auth_header, data=form_data)

    json_data = response.get_json()
    assert response.status_code == 403
    assert json_data == {'success': -6, 'msg': 'User not eligible'}
  
# Test Case: User Recreated Account After Deletion
def test_referral_use_user_recreated_account(client, customer_login):
    auth_headers, dest_user = customer_login()
    dest_user = db.session.query(Users).filter(Users.id == dest_user).first()
    src_user = Users(     
                fname = fake.name(),
                lname = 'doe',
                mobile= f'{random.randint(**********, **********)}',
                email = fake.email(),
                pwd = fake.text(),  
                role = Users.ROLE_USER
    )
    src_user.ref_code = "D2XWIE"
    db.session.add(src_user)
    db.session.commit()

    old_mobile= dest_user.mobile
    form_data = {
        'ref_code': src_user.ref_code
    }

    response = client.post('/api/user/apply_referral', headers=auth_headers, data=form_data)
    json_data = response.get_json()
    assert response.status_code == 200

    form_data = {
        'reason': DeletedUser.RSN_HAVE_OWN_DRIVER
    }
    delete_response = client.post('/api/user/delete', headers=auth_headers, data=form_data)
    assert delete_response.status_code == 201

    auth_headers_reborn, user_reborn = customer_login()
    user_reborn = db.session.query(Users).filter(Users.id == user_reborn).first()
    user_reborn.mobile = old_mobile
    user_reborn.enabled=True
    db.session.commit()

    form_data = {
        'ref_code': 'D2XWIE'
    }
    response = client.post('/api/user/apply_referral', headers=auth_headers_reborn, data=form_data)
    json_data = response.get_json()
    assert response.status_code == 403
    assert json_data == {'success': -6, 'msg': 'User not eligible'}

    
# Test Case: Successful Referral Application
def test_referral_use_success(client, customer_login):
    auth_header1, user1 = customer_login()
    user2 = Users(     
                fname = fake.name(),
                lname = 'doe',
                mobile= f'{random.randint(**********, **********)}',
                email = fake.email(),
                pwd = fake.text(),  
                role = Users.ROLE_USER
    )
    user2.ref_code = "D2XWIE"
    db.session.add(user2)
    db.session.commit()
    form_data = {
        'ref_code': user2.ref_code
    }

    response = client.post('/api/user/apply_referral', headers=auth_header1, data=form_data)

    json_data = response.get_json()
    assert response.status_code == 200
    assert json_data['success'] == 1
    assert 'credit' in json_data
    assert 'credit_added' in json_data

    
# Test Case: Referral Transaction Commit Error

def test_referral_use_commit_error(client, customer_login):
    auth_header1, user1 = customer_login()
    user2 = Users(     
                fname = fake.name(),
                lname = 'doe',
                mobile= f'{random.randint(**********, **********)}',
                email = fake.email(),
                pwd = fake.text(),  
                role = Users.ROLE_USER
    )
    user2.ref_code = "D2XWIE"
    db.session.add(user2)
    db.session.commit()
    form_data = {
        'ref_code': user2.ref_code
    }

    with patch('models.db.session.commit') as mock_commit:
        mock_commit.side_effect = OperationalError(None, None, None)
        response = client.post('/api/user/apply_referral', headers=auth_header1, data=form_data)
        json_data = response.get_json()
        assert response.status_code == 200
        assert json_data['success'] == -5

tags:
  - Authentication
summary: Validate OTP for user login/verification
description: Validates the OTP entered by the user for the given mobile number.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: otp
    in: formData
    type: string
    required: true
    description: OTP sent to the user's mobile number
  - name: new
    in: formData
    type: integer
    required: false
    description: Whether the user is new (1 for new, 0 for existing)
responses:
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Incomplete form"
    examples:
      application/json:
        success: -1
        message: "Incomplete form"
  401_a:
    description: Database error when querying the user
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"
  401_b:
    description: User is restricted from validating OTP
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -2
        message: "User restricted"
  200:
    description: OTP validated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "OTP Validated successfully"
    examples:
      application/json:
        success: 1
        message: "OTP Validated successfully"

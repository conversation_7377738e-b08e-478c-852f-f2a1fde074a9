tags:
  - Affiliate
summary: Get, Update, or Delete Affiliate Representative
description: >
  This endpoint performs one of three operations on an affiliate representative based on the provided 'type' parameter:
    - **Type 1**: Retrieve details of an affiliate representative.
    - **Type 2**: Update affiliate representative details. Optional parameters include mobile, email, username, affiliate_id, enabled, and regions.
    - **Type 3**: Delete an affiliate representative.
parameters:
  - in: formData
    name: type
    type: integer
    required: true
    description: >
      Operation type:
      1 - Get details,
      2 - Update details,
      3 - Delete the representative.
    example: 1
  - in: formData
    name: rep_id
    type: string
    required: true
    description: ID of the affiliate representative.
    example: "101"
  - in: formData
    name: mobile
    type: string
    required: false
    description: New mobile number for updating the representative.
    example: "9876543210"
  - in: formData
    name: email
    type: string
    required: false
    description: New email address for updating the representative.
    example: "<EMAIL>"
  - in: formData
    name: username
    type: string
    required: false
    description: New username for updating the representative.
    example: "new_rep_user"
  - in: formData
    name: affiliate_id
    type: string
    required: false
    description: New affiliate ID to reassign the representative.
    example: "102"
  - in: formData
    name: enabled
    type: string
    required: false
    description: >
      Set to "True" or "False" to enable or disable the representative.
    example: "True"
  - in: formData
    name: regions
    type: string
    required: true
    description: >
      Comma-separated region codes that the representative should have access to.
    example: "0,1"
    
responses:
  200:
    description: Operation successful.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Affiliate representative updated successfully"
        data:
          type: object
          description: >
            In a GET operation (type 1), this field contains the affiliate representative's details.
          properties:
            affiliate_name:
              type: string
              example: "Test Affiliate"
            affiliate_id:
              type: integer
              example: 101
            username:
              type: string
              example: "rep_user"
            mobile:
              type: string
              example: "9876543210"
            email:
              type: string
              example: "<EMAIL>"
  400:
    description: Missing or invalid input.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Incomplete form"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Affiliate representative not found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Failed to update affiliate representative: <error details>"

<!DOCTYPE html>
<html>

<head>
     <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Admin Page</title>
  <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='bootstrap.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'admin.css') }}">
  <!--Required for glyphicons-->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/DateTimePicker/bootstrap-datetimepicker.min.css') }}">
  <!-- fa icons -->
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/font-awesome/css/font-awesome.min.css') }}">

    <script>
    window.jQuery || document.write('<script src="{{ url_for('static', filename='jquery.js') }}">\x3C/script>');
  </script>
  <script src="{{ url_for('static', filename='assets/DateTimePicker/moment.js') }}"></script>     <!-- for datetimepicker -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
    <script>
        // Resolve name collision between jQuery UI and Twitter Bootstrap
        $.widget.bridge('uitooltip', $.ui.tooltip);
    </script>
  <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
  <script src="{{ url_for('static', filename='assets/DateTimePicker/bootstrap-datetimepicker.min.js') }}"></script>     <!-- for datetimepicker
  -->
    <script src="{{ url_for('static', filename='assets/Charts/charts.min.js') }}"></script>      <!-- for analytics charts-->
    
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>

    <!-- Add Firebase products that you want to use -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>

    <script src="{{ url_for('static', filename='assets/js/constants.js') }}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo&region=Dharmatala,Kolkata,West+Bengal,India&libraries=places&libraries=places"></script>
    <script src="{{ url_for('static', filename='assets/js/mapHelper.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/utility.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip-utils/0.1.0/jszip-utils.min.js" integrity="sha512-3WaCYjK/lQuL0dVIRt1thLXr84Z/4Yppka6u40yEJT1QulYm9pCxguF6r8V84ndP5K03koI9hV1+zo/bUbgMtA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js" integrity="sha512-XMVd28F1oH/O71fzwBnV7HucLxVwtxf26XV8P4wPk26EDxuGZ91N8bsOttmnomcCD3CS5ZMRL50H0GgOHvegtg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="{{ url_for('static', filename='admin.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/operations-common.js') }}"></script>
    <script src="{{ url_for('static', filename='adminAnalytics.js') }}"></script>
    <script>
        $(document).ready(function() {
            var su = parseInt('{{ superuser }}');
            if(su == 0) {
                //is not an L3admin
                $("#analyticsForm").html('');
                $("#navComponents").find('li').last().remove();
                $(".sales-text").remove();
                $(".sales-today").remove();
            }
        });
    </script>
</head>

<body style="background-color: powderblue; font-family: Arial, sans-serif;">
    <nav id="topMenu" class="navbar navbar-default navbar-fixed-top" style="margin-bottom: 0;">
    <a id="brandName" class="navbar-brand" href="/" style="padding-left: 15px!important; padding-top: 0!important;"><img src="{{ url_for('static',filename='assets/images/brandLogoMerger.png') }}" class="img-rounded" alt="Drivers4me" style="height: 50px;"></a>
    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#adminTopBar">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
    </button>
        <div class="container-fluid collapse navbar-collapse" id="adminTopBar">
            <ul id="navComponents" class="nav navbar-nav">
                <!--li class="dropdown">
                    <a class="dropdown-toggle" data-toggle="dropdown" href="#">All Drivers<span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li class="nav-tab inactive" id="KolDriversView"><a href="#kol_drivers" style="cursor: pointer;">Kolkata</a></li>
                        <li class="nav-tab inactive" id="DelDriversView"><a href="#del_drivers" style="cursor: pointer;">Delhi</a></li>
                        <li class="nav-tab inactive" id="LuckDriversView"><a href="#luck_drivers" style="cursor: pointer;">Lucknow</a></li>
                        <li class="nav-tab inactive" id="BanDriversView"><a href="#ban_drivers" style="cursor: pointer;">Bangalore</a></li>
                        <li class="nav-tab inactive" id="HydDriversView"><a href="#hyd_drivers" style="cursor: pointer;">Hyderabad</a></li>
                        <li class="nav-tab inactive" id="MumDriversView"><a href="#mum_drivers" style="cursor: pointer;">Mumbai</a></li>
                        <li class="nav-tab inactive" id="CheDriversView"><a href="#che_drivers" style="cursor: pointer;">Chennai</a></li>
                        <li class="nav-tab inactive" id="PuneDriversView"><a href="#pune_drivers" style="cursor: pointer;">Pune</a></li>
                        <li class="nav-tab inactive" id="GurDriversView"><a href="#gur_drivers" style="cursor: pointer;">Gurgaon</a></li>
                        <li class="nav-tab inactive" id="NoiDriversView"><a href="#noi_drivers" style="cursor: pointer;">Noida</a></li>
                        <li class="nav-tab inactive" id="BhbDriversView"><a href="#bhb_drivers" style="cursor: pointer;">Bhubaneswar</a></li>
                        <li class="nav-tab inactive" id="PatDriversView"><a href="#pat_drivers" style="cursor: pointer;">Patna</a></li>
                        <li class="nav-tab inactive" id="RanDriversView"><a href="#ran_drivers" style="cursor: pointer;">Ranchi</a></li>
                        <li class="nav-tab inactive" id="AhmDriversView"><a href="#ahm_drivers" style="cursor: pointer;">Ahmedabad</a></li>
                        <li class="nav-tab inactive" id="JaiDriversView"><a href="#jai_drivers" style="cursor: pointer;">Jaipur</a></li>
                        <li class="nav-tab inactive" id="GuwDriversView"><a href="#guw_drivers" style="cursor: pointer;">Guwahati</a></li>
                        <li class="nav-tab inactive" id="SilDriversView"><a href="#sil_drivers" style="cursor: pointer;">Siliguri</a></li>
                        <li class="nav-tab inactive" id="NgpDriversView"><a href="#ngp_drivers" style="cursor: pointer;">Nagpur</a></li>
                        <li class="nav-tab inactive" id="ChdDriversView"><a href="#chd_drivers" style="cursor: pointer;">Chandigarh</a></li>
                        <li class="nav-tab inactive" id="driversView"><a href="#drivers" style="cursor: pointer;">All regions</a></li>
                    </ul>
                </li!-->
                <!--li class="dropdown">
                    <a class="dropdown-toggle" data-toggle="dropdown" href="#">Full-Time Drivers<span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li class="nav-tab inactive" id="KolPermaDriversView"><a href="#kol_perma" style="cursor: pointer;">Kolkata</a></li>
                        <li class="nav-tab inactive" id="HydPermaDriversView"><a href="#hyd_perma" style="cursor: pointer;">Hyderabad</a></li>
                        <li class="nav-tab inactive" id="NgpPermaDriversView"><a href="#ngp_perma" style="cursor: pointer;">Nagpur</a></li>
                        <li class="nav-tab inactive" id="PunePermaDriversView"><a href="#pune_perma" style="cursor: pointer;">Pune</a></li>
                        <li class="nav-tab inactive" id="MumPermaDriversView"><a href="#mum_perma" style="cursor: pointer;">Mumbai</a></li>
                        <li class="nav-tab inactive" id="permaDriversView"><a href="#perma" style="cursor: pointer;">All regions</a></li>
                    </ul>
                </li!-->
                <li class="dropdown">
                    <a class="dropdown-toggle" data-toggle="dropdown" href="#">Bookings <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <!--li class="nav-tab inactive" id="CustomerBookingsView"><a href="#b2c_bookings" style="cursor: pointer;">B2C - Customer</a></li!-->
                        <!--li class="nav-tab inactive" id="C24BookingsView"><a href="#b2b_c24_bookings" style="cursor: pointer;">B2B - Cars24</a></li!-->
                        <!--li class="nav-tab inactive" id="ZoomcarBookingsView"><a href="#b2b_zoomcar_bookings" style="cursor: pointer;">B2B - Zoomcar</a></li!-->
                        <!--li class="nav-tab inactive" id="OLXBookingsView"><a href="#b2b_olx_bookings" style="cursor: pointer;">B2B - OLX</a></li!-->
                        <!--li class="nav-tab inactive" id="RevvBookingsView"><a href="#b2b_revv_bookings" style="cursor: pointer;">B2B - Revv</a></li!-->
                        <!--li class="nav-tab inactive" id="GujralBookingsView"><a href="#b2b_gujral_bookings" style="cursor: pointer;">B2B - Gujral</a></li!-->
                        <!--li class="nav-tab inactive" id="CardekhoBookingsView"><a href="#b2b_cardekho_bookings" style="cursor: pointer;">B2B - Cardekho</a></li!-->
                        <li class="nav-tab inactive" id="BhandariBookingsView"><a href="#b2b_bhandari_bookings" style="cursor: pointer;">B2B - Bhandari</a></li>
                        <li class="nav-tab inactive" id="MahindraBookingsView"><a href="#b2b_mahindra_bookings" style="cursor: pointer;">B2B - Jyoti</a></li>
                        <li class="nav-tab inactive" id="SpinnyBookingsView"><a href="#b2b_spinny_bookings" style="cursor: pointer;">B2B - Spinny</a></li>
                        <li class="nav-tab inactive" id="RevvV2BookingsView"><a href="#b2b_revv_v2_bookings" style="cursor: pointer;">B2B - Everest</a></li>
                        <li class="nav-tab inactive" id="PrideHondaBookingsView"><a href="#b2b_pridehonda_bookings" style="cursor: pointer;">B2B - PrideHonda</a></li>
                    </ul>
                  </li>
                <!--li class="nav-tab inactive" id="onlyEstimate"><a href="#est-viewers" style="cursor: pointer;">Estimate Viewers</a></li!-->
                <!--li class="dropdown">
                    <a class="dropdown-toggle" data-toggle="dropdown" href="#">Dues/Credits <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li class="nav-tab inactive" id="customerCredits"><a href="#cust-creds" style="cursor: pointer;">Customer Credits</a></li>
                        <li class="nav-tab inactive" id="userlog"><a href="#userlog" style="cursor: pointer;">User Log</a></li>
                        <li class="nav-tab inactive" id="deleteLog"><a href="#deleteLog" style="cursor: pointer;">Delete Log</a></li>
                        <li class="nav-tab inactive" id="driverDues"><a href="#driv-dues" style="cursor: pointer;">Driver Dues</a></li>
                        <li class="nav-tab inactive" id="customerLabel"><a href="#cust-label" style="cursor: pointer;">Customer Label</a></li>
                        <li class="nav-tab inactive" id="driverLabel"><a href="#driv-label" style="cursor: pointer;">Driver Label</a></li>
                    </ul>
                  </li!-->
                <!--li class="nav-tab inactive" id="pricing"><a href="#pricing" style="cursor: pointer;">Pricing</a></li>
                <li class="dropdown"> 
                    <a class="dropdown-toggle" data-toggle="dropdown" href="#">Utilities <span class="caret"></span></a>
                    <ul class="dropdown-menu">
                        <li class="nav-tab inactive" id="utilities"><a href="#utilities" style="cursor: pointer;">Change Number</a></li>
                        <li class="nav-tab inactive" id="mobileLog"><a href="#mobileLog" style="cursor: pointer;">Mobile Log</a></li>
                    </ul>
                </li!-->
                <li class="nav-tab inactive" id="analytics"><a href="#anals" style="cursor: pointer;">Analytical Section</a></li>
            </ul>
            <ul class="navbar-nav nav navbar-right" style="margin-right: 0px;">
                <li id="logout"><a style="cursor: pointer;"><span class="glyphicon glyphicon-off"></span>&emsp;Logout</a></li>
            </ul>
        </div>
    </nav>

    <script>
        $(document).ready(function(){
            $('.nav-tab a').on('click', function(){
                var target = $(this).attr('href').substring(1) + 'Content';
                $('.content').hide();
                $('#' + target).show();
            });
        });
    </script>

    <!--Home screen......................................................................................................................-->
    <div id="homeForm" class="row container-fluid admin-function">
    </div>
       <!--Drivers screen......................................................................................................................-->
    <div id="KolDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="kol-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Permanent drivers...............................................................................................................-->
    <div id="KolPermaDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="permaDriversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th>Base Salary</th>
                                <th>TA</th>
                                <th>OT</th>
                                <th>Current Allocation</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="kol-perma-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="DelDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="del-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Drivers screen......................................................................................................................-->
    <div id="LuckDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="luck-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="GurDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="gur-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="NoiDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="noi-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Drivers screen......................................................................................................................-->
    <div id="BanDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="ban-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
       <!--Drivers screen......................................................................................................................-->
    <div id="HydDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="hyd-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
       <!--Drivers screen......................................................................................................................-->
    <div id="CheDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="che-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
       <!--Drivers screen......................................................................................................................-->
    <div id="BhbDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="bhb-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
       <!--Drivers screen......................................................................................................................-->
    <div id="PatDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="pat-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Drivers screen......................................................................................................................-->
    <div id="AhmDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="ahm-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="RanDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="ran-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="RanDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="ran-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="JaiDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="jai-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
        <!--Drivers screen......................................................................................................................-->
    <div id="GuwDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="guw-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Drivers screen......................................................................................................................-->
    <div id="SilDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="sil-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Permanent drivers...............................................................................................................-->
    <div id="HydPermaDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="permaDriversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th>Base Salary</th>
                                <th>TA</th>
                                <th>OT</th>
                                <th>Current Allocation</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="hyd-perma-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
     <!--Drivers screen......................................................................................................................-->
    <div id="NgpDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="ngp-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Permanent drivers...............................................................................................................-->
    <div id="NgpPermaDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="permaDriversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th>Base Salary</th>
                                <th>TA</th>
                                <th>OT</th>
                                <th>Current Allocation</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="ngp-perma-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>

       <!--Drivers screen......................................................................................................................-->
    <div id="PuneDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="pune-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Permanent drivers...............................................................................................................-->
    <div id="PunePermaDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="permaDriversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th>Base Salary</th>
                                <th>TA</th>
                                <th>OT</th>
                                <th>Current Allocation</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="pune-perma-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Drivers screen......................................................................................................................-->
    <div id="MumDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="mum-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Permanent drivers...............................................................................................................-->
    <div id="MumPermaDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="permaDriversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th>Base Salary</th>
                                <th>TA</th>
                                <th>OT</th>
                                <th>Current Allocation</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="mum-perma-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
         <!--Drivers screen......................................................................................................................-->
    <div id="ChdDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="chd-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Drivers screen......................................................................................................................-->
    <div id="driversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-perma" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: palegreen;">Permanent</div>
                <div id="driver-normal" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: gray;">Part-time</div>
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="driversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!--Permanent drivers...............................................................................................................-->
    <div id="permaDriversViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="padding: 12px; background-color: azure;">
            <center><h3>List of Drivers</h3></center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all drivers of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding">
                <div id="driver-unavailable" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter" style="background-color: khaki;">Unavailable</div>
                <div id="driver-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: orange;">Unapproved</div>
                <div id="driver-unavailable-unapproved" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding driver-filter removed" style="background-color: lightsalmon;">Unavailable &amp; Unapproved</div>
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="permaDriversTable" style="text-align: center;" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Contact</th>
                                <th>Rating</th>
                                <th>Licence</th>
                                <th>Base Salary</th>
                                <th>TA</th>
                                <th>OT</th>
                                <th>Current Allocation</th>
                                <th class="driver-base-loc collapse">Base Location</th>
                            </tr>
                        </thead>
                        <tbody id="perma-drivers-list"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>
    <!-- Utilities................................................................................................................-->
    <div id="utilitiesForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12 collapse" style="padding: 12px; background-color: teal;">
            <center><h2>Approve Licence</h2>
                <form>
                    <div class="row standard-top-padding">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <input id="driver" type="hidden" class="form-control" name="Driver" placeholder="Find a driver">
                            <button id="driverSearch" type="button" class="btn"><span class="glyphicon glyphicon-search"></span>&emsp;Get Driver</button>
                        </div>
                    </div>
                </form>
            </center>
            <div class="row" style="padding-left: 30px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4><span class="glyphicon glyphicon-user"></span>&nbsp;Name :&emsp;<span id="driverName" class="label"></span></h4>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4><span class="glyphicon glyphicon-certificate"></span>&nbsp;Licence Number :&emsp;<span id="driverLicense" class="label"></span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div id="profPic" class="thumbnail" style="cursor: pointer;" data-toggle="modal" data-target="#profilePicModal">
                        <img id="driver_pic" src="{{ url_for("static", filename="assets/images/driverLicenceIcon.png") }}" class="img-rounded" alt="Driver Profile Picture" style="max-height: 160px;">
                        <div class="caption">
                            <span>Driver Picture</span>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div id="licPic" class="thumbnail" style="cursor: pointer;" data-toggle="modal" data-target="#licencePicModal">
                        <img id="driver_lic" src="{{ url_for("static", filename="assets/images/driverProfileIcon.png") }}" class="img-rounded" alt="Licence Picture" style="max-height: 160px;">
                        <div class="caption">
                            <span>Licence Picture</span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="btn-group">
                        <button id="problem" type="button" class="btn btn-danger">Report Problem <span class="glyphicon glyphicon-exclamation-sign"></span></button>
                        <button id="approve" type="button" class="btn btn-success">Approve <span class="glyphicon glyphicon-thumbs-up"></span></button>
                    </div>
                </div>
            </div>
            <hr>
        </div>
        <div class="col-lg-offset-3 col-lg-6 col-md-offset-2 col-md-8 col-sm-12 col-xs-12" style="padding: 12px; background-color: white;">
            <center><h2>Change Phone Number</h2>
            <form>
                <div class="row standard-top-padding">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                        <input id="phChange_currPhoneNum" type="text" class="form-control" name="currPhone" placeholder="Phone Number" onkeydown="return checkKey(event)" maxlength="10">
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                        <button id="phChange_searchByPhone" type="button" class="btn btn-info"><span class="glyphicon glyphicon-search"></span>&emsp;Search</button>
                    </div>
                </div>
            </form>
            <div class="row" style="padding-left: 30px;">
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4><span class="collapse" id="phChange_userID"></span>
                        <span class="glyphicon glyphicon-user"></span>&nbsp;Name :&emsp;<span id="phChange_userName" class="label"></span>
                        </h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4><span class="glyphicon glyphicon-phone"></span>&nbsp;Phone :&emsp;<span id="phChange_currPhDisp" class="label"></span></h4>
                </div>
            </div>
            <form id="phoneChangeForm" class="collapse">
                <div class="row standard-top-padding">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                        <input id="phChange_newPhoneNum" type="text" class="form-control" name="newPhone" placeholder="New Phone Number" onkeydown="return checkKey(event)" maxlength="10">
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                        <button id="phChange_confPhChange" type="button" class="btn btn-success">Confirm&emsp;<span class="glyphicon glyphicon-arrow-right"></span></button>
                    </div>
                </div>
            </form>
            </center>
        </div>
    </div>

    <!--mobile log display-->
    <center>
        <div id="mobileLogForm" class="row container-fluid admin-function collapse">
            <div class="col-lg-offset-3 col-lg-6 col-md-offset-2 col-md-8 col-sm-12 col-xs-12" style="padding: 12px; background-color: white;">
                <h2>Mobile Log</h2>
                <p>This is the Mobile Log page content.</p>
                <div class="row" style="width:60%">
                    <div class="form-group col-md-8">
                        <input type="text" id="mobileNumber" class="form-control" placeholder="Enter 10-digit mobile number">
                        <small id="mobileHelp" class="form-text text-muted">Please enter a valid 10-digit mobile number.</small><br>
                        <small id="mobileError" class="form-text text-danger" style="display:none;"></small>
                    </div>
                    <div class="col-md-4">
                        <button id="getMobileLog" class="btn btn-primary">Get Mobile Log</button>
                    </div>
                </div>
        
                <div id="multipleUserIds" class="mt-4" style="display:none;">
                    <h3>Select User ID:</h3>
                    <div class="row" style="width:60%">
                        <div class="form-group col-md-8">
                            <select id="userIdSelect" class="form-control"></select>
                        </div>
                        <div class="col-md-4">
                            <button id="getUserLogs" class="btn btn-primary">Get User Logs</button>
                        </div>
                    </div>
                </div>
        
                <div id="mobileChangeResult" class="mt-4" style="display:none;">
                    <center>
                        <h5 style="display: flex; align-items: center;">
                            <span id="changeNumber" style="color: rgb(6, 176, 255);margin-left: 10px;"></span>
                            <span id="changeNumberStatus" style="color: rgb(0, 179, 30); margin-left: 10px;"></span>
                            <span id="changeNumberName" style="color: rgb(0, 179, 30); margin-left: 10px;"></span>
                            <span id="changeNumberID" style="color: rgb(0, 179, 30); margin-left: 10px;"></span>
                            <span id="changeNumberEmail" style="color: rgb(0, 179, 30); margin-left: 10px;"></span>
                        </h5>
                    </center>
                    <table class="table table-bordered">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>Mobile</th>
                                <th>New Mobile</th>
                                <th>Timestamp</th>
                            </tr>
                        </thead>
                        <tbody id="mchainTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </center>

    <!-- User log -------------------------------------------------------------------->
    <center>
        <div id="userlogForm" class="row container-fluid admin-function collapse">
            <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12 px-20" style="background-color: rgb(255, 255, 255);">
                <h2>User Log</h2>
                <p>This screen shows user logs by entering mobile number</p>
                <small id="creditError" class="form-text text-danger" style="display:none;"></small>
                <div class="form-group row" style="margin-bottom: 5px;">
                    <div class="col-md-3" style="padding-right: 5px; padding-left: 5px;">
                        <label for="mobileNumber">Mobile Number:</label>
                        <input type="text" id="cmobileNumber" class="form-control" placeholder="Enter 10-digit mobile number">
                    </div>
                    <div class="col-md-3" style="padding-right: 5px; padding-left: 5px;"> 
                        <label for="methodFilter">Method:</label>
                        <select id="methodFilter" class="form-control">
                            <option value="">All</option>
                            <option value="Razorpay" id="razorselect">Razorpay</option>
                            <option value="D4M credit">D4M credit</option>
                            <option value="Admin panel">Admin panel</option>
                            <option value="Paytm">Paytm</option>
                            <option value="Referral">Referral</option>
                            <option value="Cancellation">Cancellation</option>
                        </select>
                    </div>
                    <div id="paystatus" class="col-md-3 collapse" style="padding-right: 5px; padding-left: 5px;"> <!-- Reduced padding -->
                        <label for="statusFilter">Status:</label>
                        <select id="statusFilter" class="form-control">
                            <option value="">All</option>
                            <option value="0">Initiated</option>
                            <option value="1">Completed</option>
                            <option value="2">Failed</option>
                        </select>
                    </div>
                    <div>
                        <button id="getCreditLog" class="btn btn-primary w-100 margin-top" style="background-color: #0097dd; border-color: #555555; color: #ffffff;  margin-top: 25px;">Get Logs</button> <!-- Reduced margin-top -->
                    </div>
                </div>
                <div id="userDetails" class="mt-2">
                    <h3>User Details</h3>
                    <div class="row">
                        <div class="col-md-4">
                            <p><strong>Name:</strong> <span id="userName"></span></p>
                            <p><strong>Email:</strong> <span id="userEmail"></span></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>Label:</strong> <span id="labelfield"></span></p>
                            <p><strong>Role:</strong> <span id="userRole"></span></p>
                        </div>
                        <div class="col-md-4">
                            <p><strong>Credit:</strong> <span id="userCredit"></span></p>
                            <p><strong>Mobile:</strong> <span id="userMobile"></span></p>
                        </div>
                    </div>
                </div>
                <div id="transactionLogs" class="mt-2" style="display:none;"> <!-- Reduced margin-top -->
                    <h3>Transaction Logs</h3>
                    <table class="table table-bordered" style="background-color: #ffffff; color: #000000;">
                        <thead>
                            <tr style="background-color: #0097dd; color: #ffffff;">
                                <th>Timestamp</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Method</th>
                                <th>Booking ID/Code</th>
                                <th>Admin ID</th>
                                <th>Remark</th>
                            </tr>
                        </thead>
                        <tbody id="transactionTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </center>
    <!--mobile log display-->
    <center>
        <div id="deleteLogForm" class="row container-fluid admin-function collapse">
            <div class="col-lg-offset-3 col-lg-6 col-md-offset-2 col-md-8 col-sm-12 col-xs-12" style="padding: 12px; background-color: white;">
                <h2>Delete Log</h2>
                <div class="row" style="width:60%">
                    <div class="form-group col-md-8">
                        <input type="text" id="deleteNumber" class="form-control" placeholder="Enter 10-digit mobile number">
                        <small id="deleteeHelp" class="form-text text-muted">Please enter a valid 10-digit mobile number.</small>
                        <small id="deleteError" class="form-text text-danger" style="display:none;"></small>
                    </div>
                    <div class="col-md-4">
                        <button id="getDeleteLog" class="btn btn-primary">Get Mobile Log</button>
                    </div>
                </div>
                
                <div id="deleteChangeResult" class="mt-4" style="display:none;">
                    <div class="row">
                        <h5 class="col-lg-6">Changed Number: <span id="changeMobile"></span></h5>
                        <h5 class="col-lg-6">Changed Timestamp: <span id="changeTimestamp"></span></h5>
                    </div>
                    
                    <table class="table table-bordered">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>User ID</th>
                                <th>Name</th>
                                <th>New Mobile</th>
                                <th>Reason</th>
                                <th>Timestamp</th>
                            </tr>
                        </thead>
                        <tbody id="dchainTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </center>

    <!-- Bookings................................................................................................................-->
    <div id="CustomerBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
            <center>
                <!--div class="bd-container">
                  <div class="btn-group">
                    <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-customer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                    <ul class="dropdown-menu">
                      <li><a href="#">All Cities</a></li>
                      <li><a href="#">Kolkata</a></li>
                      <li><a href="#">Delhi</a></li>
                      <li><a href="#">Guwahati</a></li>
                    </ul>
                  </div>
                </div!-->
            </center>
            <center><h2>B2C - Customer Bookings</h2>
                <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
                </h5>
                <form>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                                <span class="hold-status-text">ON</span>
                            </button!-->
                            <button type="button" class="btn btn-xs btn-primary partialRefresh">
                                <span class="fa-stack">
                                    <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                    <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                </span>&emsp;Quick Refresh
                            </button>
                            <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                            <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                                &emsp;More...</button>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span>From: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_start_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span>To: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_end_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span><i>By Name</i> </span>
                                </div>
                                <div class="col-lg-4 col-md-4 col-sm-4 col-xs-6">
                                    <div class="form-group">
                                        <input name="searchName" type="text" class="form-control" id="searchName" placeholder="Name"/>
                                    </div>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <select id="searchCat" name="searchCat">
                                          <option value="0">Customer</option>
                                          <option value="1">Driver</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin nameRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>-->
        </div>
            <div class="row standard-left-padding tiny-top-margin">
                <div id="Customer-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
                <div id="Customer-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Customer Cancelled before allocating</div>
                <div id="Customer-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Customer Cancelled</div>
                <div id="Customer-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
                <div id="Customer-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
                <div id="Customer-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">No one accepted</div>
                <div id="Customer-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver accepted</div>
                <div id="Customer-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
                <!--div id="Customer-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table class="table table-bordered bookingsTable">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Trip</th>
                                <th>Customer</th>
                                <th>Driver</th>
                                <th>Estimated Fare</th>
                                <th>Final Fare</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody class="booking-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>
    <div id="C24BookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
            <center>
                <!--div class="bd-container">
                  <div class="btn-group">
                    <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-c24" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                    <ul class="dropdown-menu">
                      <li><a href="#">All Cities</a></li>
                      <li><a href="#">Kolkata</a></li>
                      <li><a href="#">Delhi</a></li>
                      <li><a href="#">Guwahati</a></li>
                    </ul>
                  </div>
                </div!-->
            </center>
            <center><h2>B2B - Cars24 Bookings</h2>
                <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
                </h5>
                <form>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                                <span class="hold-status-text">ON</span>
                            </button!-->
                            <button type="button" class="btn btn-xs btn-primary partialRefresh">
                                <span class="fa-stack">
                                    <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                    <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                </span>&emsp;Quick Refresh
                            </button>
                            <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                            <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                                &emsp;More...</button>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span>From: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_start_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span>To: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_end_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span><i>By Name</i> </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span class="trollFont">[OR] </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding c24-select-div">
                B2C
                <label class="switch">
                  <input type="checkbox" id="c24-select">
                  <span class="slider round c24-slider"></span>
                </label>
                Cars24
                </div>-->
            </div>
            <div class="row standard-left-padding tiny-top-margin">
                <div id="C24-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
                <div id="C24-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">C24 Cancelled before allocating</div>
                <div id="C24-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">C24 Cancelled</div>
                <div id="C24-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
                <div id="C24-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
                <div id="C24-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
                <div id="C24-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
                <div id="C24-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
                <!--div id="C24-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table class="table table-bordered bookingsTable">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Trip</th>
                                <th>Client</th>
                                <th>Driver</th>
                                <th>Estimated Fare</th>
                                <th>Final Fare</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody class="booking-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>

    <div id="OLXBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
            <center>
                <!--div class="bd-container">
                  <div class="btn-group">
                    <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-olx" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                    <ul class="dropdown-menu">
                      <li><a href="#">All Cities</a></li>
                      <li><a href="#">Kolkata</a></li>
                      <li><a href="#">Delhi</a></li>
                      <li><a href="#">Guwahati</a></li>
                    </ul>
                  </div!-->
            </center>
            <center><h2>B2B - OLX Bookings</h2>
                <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
                </h5>
                <form>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                                <span class="hold-status-text">ON</span>
                            </button!-->
                            <button type="button" class="btn btn-xs btn-primary partialRefresh">
                                <span class="fa-stack">
                                    <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                    <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                </span>&emsp;Quick Refresh
                            </button>
                            <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                            <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                                &emsp;More...</button>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span>From: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_start_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span>To: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_end_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span><i>By Name</i> </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span class="trollFont">[OR] </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding c24-select-div">
                B2C
                <label class="switch">
                  <input type="checkbox" id="-select">
                  <span class="slider round c24-slider"></span>
                </label>
                Cars24
                </div>-->
            </div>
            <div class="row standard-left-padding tiny-top-margin">
                <div id="OLX-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
                <div id="OLX-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">OLX Cancelled before allocating</div>
                <div id="OLX-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">OLX Cancelled</div>
                <div id="OLX-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
                <div id="OLX-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
                <div id="OLX-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
                <div id="OLX-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
                <div id="OLX-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
                <!--div id="OLX-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table class="table table-bordered bookingsTable">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Trip</th>
                                <th>Client</th>
                                <th>Driver</th>
                                <th>Estimated Fare</th>
                                <th>Final Fare</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody class="booking-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>
        <div id="ZoomcarBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
            <center>
                <!--div class="bd-container">
                  <div class="btn-group">
                    <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-zoomcar" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                    <ul class="dropdown-menu">
                      <li><a href="#">All Cities</a></li>
                      <li><a href="#">Kolkata</a></li>
                      <li><a href="#">Delhi</a></li>
                      <li><a href="#">Guwahati</a></li>
                    </ul>
                  </div>
                </div!-->
            </center>
            <center><h2>B2B - Zoomcar Bookings</h2>
                <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
                </h5>
                <form>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <!--button id="globalHoldZoom" type="button" class="btn btn-sm">BROADCAST&emsp;
                                <span class="hold-status-text">ON</span>
                            </button!-->
                            <button type="button" class="btn btn-xs btn-primary partialRefresh">
                                <span class="fa-stack">
                                    <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                    <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                </span>&emsp;Quick Refresh
                            </button>
                            <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                            <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                                &emsp;More...</button>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span>From: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_start_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span>To: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_end_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span><i>By Name</i> </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span class="trollFont">[OR] </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding c24-select-div">
                B2C
                <label class="switch">
                  <input type="checkbox" id="-select">
                  <span class="slider round c24-slider"></span>
                </label>
                Cars24
                </div>-->
            </div>
            <div class="row standard-left-padding tiny-top-margin">
                <div id="Zoomcar-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
                <div id="Zoomcar-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Zoomcar Cancelled before allocating</div>
                <div id="Zoomcar-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Zoomcar Cancelled</div>
                <div id="Zoomcar-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
                <div id="Zoomcar-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
                <div id="Zoomcar-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
                <div id="Zoomcar-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
                <div id="Zoomcar-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
                <!--div id="Zoomcar-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table class="table table-bordered bookingsTable">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Trip</th>
                                <th>Client</th>
                                <th>Driver</th>
                                <th>Estimated Fare</th>
                                <th>Final Fare</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody class="booking-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>
    <div id="RevvBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
            <center><h2>B2B - Revv Bookings</h2>
                <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
                </h5>
                <form>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                                <span class="hold-status-text">ON</span>
                            </button!-->
                            <button type="button" class="btn btn-xs btn-primary partialRefresh">
                                <span class="fa-stack">
                                    <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                    <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                </span>&emsp;Quick Refresh
                            </button>
                            <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                            <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                                &emsp;More...</button>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span>From: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_start_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span>To: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_end_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span><i>By Name</i> </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span class="trollFont">[OR] </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding c24-select-div">
                B2C
                <label class="switch">
                  <input type="checkbox" id="c24-select">
                  <span class="slider round c24-slider"></span>
                </label>
                Cars24
                </div>-->
            </div>
            <div class="row standard-left-padding tiny-top-margin">
                <div id="Revv-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
                <div id="Revv-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Revv Cancelled before allocating</div>
                <div id="Revv-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Revv Cancelled</div>
                <div id="Revv-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
                <div id="Revv-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
                <div id="Revv-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
                <div id="Revv-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
                <div id="Revv-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going duty</div>
                <!--div id="Revv-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table class="table table-bordered bookingsTable">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Trip</th>
                                <th>Client</th>
                                <th>Driver</th>
                                <th>Estimated Fare</th>
                                <th>Final Fare</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody class="booking-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>
    <div id="GujralBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
            <center><h2>B2B - Gujral Bookings</h2>
                <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
                </h5>
                <form>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                                <span class="hold-status-text">ON</span>
                            </button!-->
                            <button type="button" class="btn btn-xs btn-primary partialRefresh">
                                <span class="fa-stack">
                                    <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                    <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                </span>&emsp;Quick Refresh
                            </button>
                            <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                            <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                            <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                                &emsp;More...</button>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span>From: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_start_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span>To: </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <div class='input-group date datetimepicker_book_table_end_change'>
                                            <input type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                            <div class="row">
                                <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                    <span><i>By Name</i> </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <span class="trollFont">[OR] </span>
                                </div>
                                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                    <div class="form-group">
                                        <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                    </div>
                                </div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                    <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                        <span class="fa-stack">
                                            <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                            <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                        </span>&emsp;Go
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>&emsp;Filters :</h4>
                    <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
                </div>
            </div>
            <div class="row standard-left-padding c24-select-div">
                B2C
                <label class="switch">
                  <input type="checkbox" id="c24-select">
                  <span class="slider round c24-slider"></span>
                </label>
                Cars24
                </div>-->
            </div>
            <div class="row standard-left-padding tiny-top-margin">
                <div id="Gujral-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
                <div id="Gujral-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Gujral Cancelled before allocating</div>
                <div id="Gujral-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Gujral Cancelled</div>
                <div id="Gujral-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
                <div id="Gujral-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
                <div id="Gujral-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
                <div id="Gujral-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
                <div id="Gujral-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going duty</div>
                <!--div id="Gujral-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
            </div>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table class="table table-bordered bookingsTable">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Trip</th>
                                <th>Client</th>
                                <th>Driver</th>
                                <th>Estimated Fare</th>
                                <th>Final Fare</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody class="booking-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>

    <div id="CardekhoBookingsViewForm" class="row container-fluid admin-function collapse">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
        <center>
            <!--div class="bd-container">
              <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-cardekho" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">All Cities</a></li>
                  <li><a href="#">Kolkata</a></li>
                  <li><a href="#">Delhi</a></li>
                  <li><a href="#">Guwahati</a></li>
                </ul>
              </div>
            </div!-->
        </center>
        <center><h2>B2B - Cardekho Bookings</h2>
            <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <form>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                            <span class="hold-status-text">ON</span>
                        </button!-->
                        <button type="button" class="btn btn-xs btn-primary partialRefresh">
                            <span class="fa-stack">
                                <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                            </span>&emsp;Quick Refresh
                        </button>
                        <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                        <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                            &emsp;More...</button>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span>From: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span>To: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_end_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span><i>By Name</i> </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span class="trollFont">[OR] </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </center>
        <!--<div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4>&emsp;Filters :</h4>
                <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
            </div>
        </div>
        <div class="row standard-left-padding c24-select-div">
            B2C
            <label class="switch">
              <input type="checkbox" id="-select">
              <span class="slider round c24-slider"></span>
            </label>
            Cars24
            </div>-->
        </div>
        <div class="row standard-left-padding tiny-top-margin">
            <div id="Cardekho-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
            <div id="Cardekho-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Cardekho Cancelled before allocating</div>
            <div id="Cardekho-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Cardekho Cancelled</div>
            <div id="Cardekho-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
            <div id="Cardekho-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
            <div id="Cardekho-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
            <div id="Cardekho-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
            <div id="Cardekho-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
            <!--div id="Cardekho-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
        </div>
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                <table class="table table-bordered bookingsTable">
                    <thead>
                        <tr>
                            <th>Booking</th>
                            <th>Trip</th>
                            <th>Client</th>
                            <th>Driver</th>
                            <th>Estimated Fare</th>
                            <th>Final Fare</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody class="booking-stack"></tbody>
                  </table>
            </div>
        </div>
    </div>
    <div id="BhandariBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
        <center>
            <!--div class="bd-container">
              <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-bhandari" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">All Cities</a></li>
                  <li><a href="#">Kolkata</a></li>
                  <li><a href="#">Delhi</a></li>
                  <li><a href="#">Guwahati</a></li>
                </ul>
              </div>
            </div!-->
        </center>
        <center><h2>B2B - Bhandari Bookings</h2>
            <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <form>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                            <span class="hold-status-text">ON</span>
                        </button!-->
                        <button type="button" class="btn btn-xs btn-primary partialRefresh">
                            <span class="fa-stack">
                                <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                            </span>&emsp;Quick Refresh
                        </button>
                        <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                        <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                            &emsp;More...</button>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span>From: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span>To: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_end_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span><i>By Name</i> </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span class="trollFont">[OR] </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </center>
        <!--<div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4>&emsp;Filters :</h4>
                <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
            </div>
        </div>
        <div class="row standard-left-padding c24-select-div">
            B2C
            <label class="switch">
              <input type="checkbox" id="-select">
              <span class="slider round c24-slider"></span>
            </label>
            Cars24
            </div>-->
        </div>
        <div class="row standard-left-padding tiny-top-margin">
            <div id="Bhandari-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
            <div id="Bhandari-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Bhandari Cancelled before allocating</div>
            <div id="Bhandari-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Bhandari Cancelled</div>
            <div id="Bhandari-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
            <div id="Bhandari-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
            <div id="Bhandari-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
            <div id="Bhandari-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
            <div id="Bhandari-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
            <!--div id="Bhandari-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: blue;">Comments</div!-->
        </div>
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                <table class="table table-bordered bookingsTable">
                    <thead>
                        <tr>
                            <th>Booking</th>
                            <th>Trip</th>
                            <th>Client</th>
                            <th>Driver</th>
                            <th>Estimated Fare</th>
                            <th>Final Fare</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody class="booking-stack"></tbody>
                  </table>
            </div>
        </div>
    </div>
    <div id="MahindraBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
        <center>
            <!--div class="bd-container">
              <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-mahindra" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">All Cities</a></li>
                  <li><a href="#">Kolkata</a></li>
                  <li><a href="#">Delhi</a></li>
                  <li><a href="#">Guwahati</a></li>
                </ul>
              </div>
            </div!-->
        </center>
        <center><h2>B2B - Mahindra Bookings</h2>
            <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <form>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                            <span class="hold-status-text">ON</span>
                        </button!-->
                        <button type="button" class="btn btn-xs btn-primary partialRefresh">
                            <span class="fa-stack">
                                <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                            </span>&emsp;Quick Refresh
                        </button>
                        <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                        <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                            &emsp;More...</button>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span>From: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span>To: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_end_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span><i>By Name</i> </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span class="trollFont">[OR] </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </center>
        <!--<div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4>&emsp;Filters :</h4>
                <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
            </div>
        </div>
        <div class="row standard-left-padding c24-select-div">
            B2C
            <label class="switch">
              <input type="checkbox" id="-select">
              <span class="slider round c24-slider"></span>
            </label>
            Cars24
            </div>-->
        </div>
        <div class="row standard-left-padding tiny-top-margin">
            <div id="Mahindra-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
            <div id="Mahindra-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Mahindra Cancelled before allocating</div>
            <div id="Mahindra-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Mahindra Cancelled</div>
            <div id="Mahindra-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
            <div id="Mahindra-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
            <div id="Mahindra-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
            <div id="Mahindra-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
            <div id="Mahindra-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
            <!--div id="Mahindra-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
        </div>
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                <table class="table table-bordered bookingsTable">
                    <thead>
                        <tr>
                            <th>Booking</th>
                            <th>Trip</th>
                            <th>Client</th>
                            <th>Driver</th>
                            <th>Estimated Fare</th>
                            <th>Final Fare</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody class="booking-stack"></tbody>
                  </table>
            </div>
        </div>
    </div>
    <div id="SpinnyBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
        <center>
            <!--div class="bd-container">
              <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-spinny" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">All Cities</a></li>
                  <li><a href="#">Kolkata</a></li>
                  <li><a href="#">Delhi</a></li>
                  <li><a href="#">Guwahati</a></li>
                </ul>
              </div>
            </div!-->
        </center>
        <center><h2>B2B - Spinny Bookings</h2>
            <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <form>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                            <span class="hold-status-text">ON</span>
                        </button!-->
                        <button type="button" class="btn btn-xs btn-primary partialRefresh">
                            <span class="fa-stack">
                                <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                            </span>&emsp;Quick Refresh
                        </button>
                        <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                        <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                            &emsp;More...</button>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span>From: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span>To: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_end_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span><i>By Name</i> </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span class="trollFont">[OR] </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </center>
        <!--<div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4>&emsp;Filters :</h4>
                <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
            </div>
        </div>
        <div class="row standard-left-padding c24-select-div">
            B2C
            <label class="switch">
              <input type="checkbox" id="-select">
              <span class="slider round c24-slider"></span>
            </label>
            Cars24
            </div>-->
        </div>
        <div class="row standard-left-padding tiny-top-margin">
            <div id="Spinny-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
            <div id="Spinny-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">Spinny Cancelled before allocating</div>
            <div id="Spinny-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">Spinny Cancelled</div>
            <div id="Spinny-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
            <div id="Spinny-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
            <div id="Spinny-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
            <div id="Spinny-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
            <div id="Spinny-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
            <!--div id="Spinny-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
        </div>
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                <table class="table table-bordered bookingsTable">
                    <thead>
                        <tr>
                            <th>Booking</th>
                            <th>Trip</th>
                            <th>Client</th>
                            <th>Driver</th>
                            <th>Estimated Fare</th>
                            <th>Final Fare</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody class="booking-stack"></tbody>
                  </table>
            </div>
        </div>
    </div>
    <div id="RevvV2BookingsViewForm" class="row container-fluid admin-function collapse">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
        <center>
            <!--div class="bd-container">
              <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-revv_v2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">All Cities</a></li>
                  <li><a href="#">Kolkata</a></li>
                  <li><a href="#">Delhi</a></li>
                  <li><a href="#">Guwahati</a></li>
                </ul>
              </div>
            </div!-->
        </center>
        <center><h2>B2B - Revv Bookings</h2>
            <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <form>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                            <span class="hold-status-text">ON</span>
                        </button!-->
                        <button type="button" class="btn btn-xs btn-primary partialRefresh">
                            <span class="fa-stack">
                                <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                            </span>&emsp;Quick Refresh
                        </button>
                        <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                        <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                            &emsp;More...</button>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span>From: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span>To: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_end_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span><i>By Name</i> </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span class="trollFont">[OR] </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </center>

        <!--<div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4>&emsp;Filters :</h4>
                <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
            </div>
        </div>
        <div class="row standard-left-padding c24-select-div">
            B2C
            <label class="switch">
              <input type="checkbox" id="-select">
              <span class="slider round c24-slider"></span>
            </label>
            Cars24
            </div>-->
        </div>
        <div class="row standard-left-padding tiny-top-margin">
            <div id="RevvV2-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
            <div id="RevvV2-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">RevvV2 Cancelled before allocating</div>
            <div id="RevvV2-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">RevvV2 Cancelled</div>
            <div id="RevvV2-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
            <div id="RevvV2-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
            <div id="RevvV2-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
            <div id="RevvV2-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
            <div id="RevvV2-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
            <!--div id="RevvV2-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
        </div>
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                <table class="table table-bordered bookingsTable">
                    <thead>
                        <tr>
                            <th>Booking</th>
                            <th>Trip</th>
                            <th>Client</th>
                            <th>Driver</th>
                            <th>Estimated Fare</th>
                            <th>Final Fare</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody class="booking-stack"></tbody>
                  </table>
            </div>
        </div>
    </div>
    <div id="PrideHondaBookingsViewForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 medium-bottom-padding">
        <center>
            <!--div class="bd-container">
              <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" id="select-city-dropdown-pridehonda" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select City <span class="caret"></span></button>
                <ul class="dropdown-menu">
                  <li><a href="#">All Cities</a></li>
                  <li><a href="#">Kolkata</a></li>
                  <li><a href="#">Delhi</a></li>
                  <li><a href="#">Guwahati</a></li>
                </ul>
              </div>
            </div!-->
        </center>
        <center><h2>B2B - PrideHonda Bookings</h2>
            <h5 class="medium-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <form>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <!--button id="globalHold" type="button" class="btn btn-sm">BROADCAST&emsp;
                            <span class="hold-status-text">ON</span>
                        </button!-->
                        <button type="button" class="btn btn-xs btn-primary partialRefresh">
                            <span class="fa-stack">
                                <i class="fa fa-xs fa-refresh fa-stack-2x"></i>
                                <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                            </span>&emsp;Quick Refresh
                        </button>
                        <!--button type="button" class="btn btn-sm btn-warning tiny-bottom-margin refreshBookings"><i class="fa fa-sm fa-refresh"></i>&emsp;FULL REFRESH</button!-->
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin weekRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Week</button>
                        <button type="button" class="btn btn-sm btn-info tiny-bottom-margin dateRefresh"><i class="fa fa-sm fa-calendar-o"></i>&emsp;Day</button>
                        <button type="button" class="btn btn-primary btn-xs tiny-bottom-margin moreBookingViewOptions"><i class="fa fa-sm fa-filter"></i>
                            &emsp;More...</button>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span>From: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span>To: </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <div class='input-group date datetimepicker_book_table_end_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button type="button" class="btn btn-sm btn-success tiny-bottom-margin dateBoundRefresh">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-clock-o fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters tiny-top-padding collapse">
                        <div class="row">
                            <div class="col-lg-1 col-lg-offset-2 col-md-1 col-mg-offset-2 col-sm-1 col-sm-offset-2 col-xs-6">
                                <span><i>By Name</i> </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="customerName" type="text" class="form-control" id="custName" placeholder="Customer"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <span class="trollFont">[OR] </span>
                            </div>
                            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-6">
                                <div class="form-group">
                                    <input name="driverName" type="text" class="form-control" id="allocatedDriverName" placeholder="Driver"/>
                                </div>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-6">
                                <button id="nameRefresh" type="button" class="btn btn-sm btn-success tiny-bottom-margin">
                                    <span class="fa-stack">
                                        <i class="fa fa-xs fa-user fa-stack-2x"></i>
                                        <i class="fa fa-xs fa-filter fa-stack-1x"></i>
                                    </span>&emsp;Go
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </center>
        <!--<div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <h4>&emsp;Filters :</h4>
                <h5><i>Click on a filter to remove all bookings of that type.&emsp;Click again to restore.</i><hr class="d4m-ending"></h5>
            </div>
        </div>
        <div class="row standard-left-padding c24-select-div">
            B2C
            <label class="switch">
              <input type="checkbox" id="-select">
              <span class="slider round c24-slider"></span>
            </label>
            Cars24
            </div>-->
        </div>
        <div class="row standard-left-padding tiny-top-margin">
            <div id="PrideHonda-booking-completed" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: yellowgreen;">Successful</div>
            <div id="PrideHonda-booking-quick-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: khaki;">PrideHonda Cancelled before allocating</div>
            <div id="PrideHonda-booking-customer-cancel" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: gold;">PrideHonda Cancelled</div>
            <div id="PrideHonda-booking-driver-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: lightsalmon;">Checked-In Trip</div>
            <div id="PrideHonda-booking-d4m-cancel" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: var(--d4m-standard-light); color: white;">D4M Cancelled</div>
            <div id="PrideHonda-booking-new" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: gray;">Unallocated</div>
            <div id="PrideHonda-booking-accepted" class="col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: palegreen;">Driver allocated</div>
            <div id="PrideHonda-booking-ongoing" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter" style="background-color: aquamarine;">On-going trip</div>
            <!--div id="PrideHonda-booking-comment" class="col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed" style="background-color: blue;">Comments</div!-->
        </div>
        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                <table class="table table-bordered bookingsTable">
                    <thead>
                        <tr>
                            <th>Booking</th>
                            <th>Trip</th>
                            <th>Client</th>
                            <th>Driver</th>
                            <th>Estimated Fare</th>
                            <th>Final Fare</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody class="booking-stack"></tbody>
                  </table>
            </div>
        </div>
    </div>
    <!-- Viewed Estimate but did not book..........................................................................................-->
    <div id="onlyEstimateForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding: 12px; background-color: white;">
            <center><h2>Estimate Trials</h2>
                <form>
                    <div class="row standard-top-padding">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <button id="refreshEstimates" type="button" class="btn"><span class="glyphicon glyphicon-refresh"></span>&emsp;Refresh Table</button>
                        </div>
                    </div>
                </form>
            </center>
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <table id="estimatesTable" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Booking</th>
                                <th>Customer</th>
                                <th>Trip</th>
                                <th>Location</th>
                            </tr>
                        </thead>
                        <tbody id="estimate-stack"></tbody>
                      </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Cancelled Bookings................................................................................................................-->
    <div id="cancelledBookingsForm" class="row container-fluid admin-function collapse">
        <div id="cancelled-stack" class="col-lg-offset-4 col-lg-6 col-md-offset-4 col-md-6 col-sm-12 col-xs-12" style="padding: 12px; background-color: slateblue;">
            <center><h2>List of Cancelled Trips</h2>
                <form>
                    <div class="row standard-top-padding">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <button id="refreshCancelled" type="button" class="btn"><span class="glyphicon glyphicon-refresh"></span>&emsp;Refresh List</button>
                        </div>
                    </div>
                </form>
            </center>
            <!--<div class="row cancelled-trip">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><h3>Booking Details (sample)</h3></div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <h4>Booking Status :&emsp;<span class="label status-Alloc">Allocated</span>/<span class="label status-accepted">Accepted</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">
                    <h4>Vehicle Type :&emsp;<span class="label vehicleType">Sedan</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">
                    <h4>Duration :&emsp;<span class="label duration">6</span> Hours</h4>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>Location :&emsp;<span class="label latDeg">26</span>&deg;<span class="label latMin">43</span>'<span class="label latSec">01.9</span>" N&emsp;&emsp;<span class="label longDeg">88</span>&deg;<span class="label longMin">26</span>'<span class="label longSec">18.8</span>" E&emsp;<button type="button" class="btn mapLink"><span class="glyphicon glyphicon-map-marker"></span>&nbsp;Open In Maps</button></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>Date of Booking :&emsp;<span class="label book_day">31</span>/<span class="label book_month">04</span>/<span class="label book_year">2018</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>Time of Booking :&emsp;<span class="label book_hour">10</span>:<span class="label book_minute">15</span>&nbsp;<span class="label book_am_pm">AM</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>Date of Trip :&emsp;<span class="label trip_day">31</span>/<span class="label trip_month">04</span>/<span class="label trip_year">2018</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>Time of Trip :&emsp;<span class="label trip_hour">10</span>:<span class="label trip_minute">15</span>&nbsp;<span class="label trip_am_pm">AM</span></h4>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>Date of Cancellation :&emsp;<span class="label cancel_day">31</span>/<span class="label cancel_month">04</span>/<span class="label cancel_year">2018</span></h4>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <h4>Time of Cancellation :&emsp;<span class="label cancel_hour">10</span>:<span class="label cancel_minute">15</span>&nbsp;<span class="label cancel_am_pm">AM</span></h4>
                </div>

                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><hr class="d4m-ending"><h3>Customer Details&emsp;<span style="color: white;" class="label label-danger glyphicon glyphicon-remove"> </span></h3></div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <h4>Name :&emsp;<span class="label cust_fname">Paramarthababu</span>&nbsp;<span class="label cust_lname">Pachula</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>Phone number :&emsp;<span class="label cust_phone">9999999999</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>E-mail :&emsp;<span class="label cust_email"><EMAIL></span></h4>
                </div>

                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><hr class="d4m-ending"><h3>Driver Details&emsp;<span class="label label-danger glyphicon glyphicon-remove">Cancelled</span></h3></div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <h4>Name :&emsp;<span class="label driver_fname">Dbms Theke</span>&nbsp;<span class="label driver_lname">Bcnf</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>Phone number :&emsp;<span class="label driver_phone">8888888888</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4>E-mail :&emsp;<span class="label driver_email"><EMAIL></span></h4>
                </div>

            </div>-->
        </div>
    </div>

    <!-- Customer Credit................................................................................................................-->
    <div id="customerCreditsForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12" style="padding: 12px; background-color: mintcream;">
            <h2 style="text-align: center;">Customer D4M Credits</h2>
            <form style="text-align: center;">
                <div class="row standard-top-padding">
                    <div class="col-lg-8 col-lg-offset-2 col-md-8 col-md-offset-2 col-sm-12 col-xs-12">
                        <div class="input-group">
                            <input name="mobile" type="mobile" class="form-control cred-cust-search-input" maxlength="10" id="CredCust_SearchInput_Phone" placeholder="Mobile Number" onkeydown="return checkKey(event)" />
                            <input name="id" type="text" class="form-control cred-cust-search-input collapse" id="CredCust_SearchInput_ID" placeholder="User ID" />
                            <div class="input-group-btn">
                                <div class="btn-group">
                                    <button id="Search_Cred_Cust" type="button" class="btn btn-success"><i class="glyphicon glyphicon-search"></i>&emsp;Find Customer</button>
                                    <div class="dropdown">
                                        <button class="btn dropdown-toggle" type="button" data-toggle="dropdown"><span class="curr-search-criteria">By Mobile</span>
                                        <span class="caret"></span></button>
                                        <ul class="dropdown-menu">
                                            <li class="tiny-padding">
                                                <button class="cred-cust-search-criteria" id="CredCust_SearchCriteria_Phone" type="button" class="btn">By Mobile</button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form><hr class="d4m-ending">
            <div class="row" style="padding-left: 30px;">
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-lock"></span>&nbsp;ID :&emsp;<span id="custId_Creds" class="label"></span></h5>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-user"></span>&nbsp;Name :&emsp;<span id="custName_Creds" class="label">---</span></h5>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-phone"></span>&nbsp;Phone Number :&emsp;<span id="custPhone" class="label">---</span></h5>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-usd"></span>&nbsp;Credit Amount :&emsp;₹&nbsp;<span id="cust_amount_Creds" class="label break-long-text">0</span></h5>
                </div>
            </div>
            <div id="buttonTextFieldRow" class="row tiny-top-padding" style="text-align: center;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-bottom-padding">
                    <hr class="d4m-ending"><h5>&nbsp;Credits to be added :&emsp;₹<span id="cust_curr_creds" class="label">0</span></h5>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12 tiny-top-padding">
                    <button id="custDecrAmount" type="button" class="btn btn-xs btn-primary"><span class="glyphicon glyphicon-minus"></span>&emsp;Reduce Credits</button>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <input name="amount" type="text" class="form-control" id="cust_amount_change" placeholder="Enter amount" onkeydown="return checkKey(event)" />
                </div>
                <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12 tiny-top-padding">
                    <button id="custIncrAmount" type="button" class="btn btn-xs btn-primary"><span class="glyphicon glyphicon-plus"></span>&emsp;Add Credits</button>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding btn-group">
                    <button id="custClearCredits" type="button" class="btn btn-xs btn-default"><span class="glyphicon glyphicon-trash"></span>&emsp;Clear Changes</button>
                    <button id="custSaveAmount" type="button" class="btn btn-xs btn-success"><span class="glyphicon glyphicon-save"></span>&emsp;Save</button>
                </div>
            </div>
            <div class="row tiny-top-padding" style="text-align: center;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <center>
                        <input name="remark" type="text" class="form-control" id="cust_remark" placeholder="Enter remark" style="width: 50%;"/>
                        <span id="remarkWarning" class="text-danger" style="display:none;">Remark is required.</span>
                    </center>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <h6><i><b>Unless you save your changes, they will not be reflected in the database.</b></i></h6>
                </div>
            </div>
        </div>
    </div>
    <!-- Driver Dues................................................................................................................-->
    <div id="driverDuesForm" class="row container-fluid admin-function collapse">
            <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12" style="padding: 12px; background-color: darkseagreen;">
            <center><h2>Driver Dues</h2>
                <form>
                    <div class="row standard-top-padding">
                        <div class="col-lg-8 col-lg-offset-2 col-md-8 col-md-offset-2 col-sm-12 col-xs-12 standard-bottom-padding">
                            <button id="getDriversList_dues" type="button" class="btn btn-warning"><i class="glyphicon glyphicon glyphicon-list-alt"></i>&emsp;Get Driver List</button>
                        </div>
                        <div class="col-lg-8 col-lg-offset-2 col-md-8 col-md-offset-2 col-sm-12 col-xs-12">
                            <div class="input-group">
                                <input name="mobile" type="mobile" class="form-control" maxlength="13" id="driver_search_mobile" placeholder="Mobile Number" onkeydown="return checkKey(event)" />
                                <div class="input-group-btn">
                                    <button id="searchDriver_phone" type="button" class="btn btn-info"><i class="glyphicon glyphicon-search"></i>&emsp;Find Driver</button>
                                </div>
                            </div>

                        </div>
                        <!--<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 collapse">
                            <input id="driver" type="hidden" class="form-control" name="Driver" placeholder="Find a driver">
                            <button id="driverSearch_due" type="button" class="btn btn-info"><span class="glyphicon glyphicon-forward"></span>&emsp;Next Driver</button>
                        </div>-->
                    </div>
                </form><hr class="d4m-ending">
            </center>
            	            <div class="row" style="padding-left: 30px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <h3><span class="glyphicon glyphicon-lock"></span>&nbsp;ID :&emsp;<span id="driverId_due" class="label"></span></h3>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">
                    <h4><span class="glyphicon glyphicon-user"></span>&nbsp;Name :&emsp;<span id="driverName_due" class="label">---</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">
                    <h4><span class="glyphicon glyphicon-phone"></span>&nbsp;Phone Number :&emsp;<span id="driverPhone" class="label">---</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4><span class="glyphicon glyphicon-certificate"></span>&nbsp;Total Balance :&emsp;₹&nbsp;<span id="total_balance" class="label break-long-text">0</span></h4>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                    <h4><span class="glyphicon glyphicon-usd"></span>&nbsp;Withdrawable :&emsp;₹&nbsp;<span id="withdrawable_balance" class="label break-long-text">0</span></h4>
                </div>
            </div>
            <div class="row standard-top-padding" style="text-align: center;">
                <!--div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-bottom-padding">
                    <hr class="d4m-ending"><h3>&nbsp;Updated Due :&emsp;₹<span id="curr_due" class="label">0</span></h3>
                </div!-->
                <!--div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 standard-top-padding">
                    <button id="decrAmount" type="button" class="btn btn-success"><span class="glyphicon glyphicon-minus"></span>&emsp;Subtract amount</button>
                </div!-->
                <div class="col-lg-4 col-lg-offset-4 col-md-4 col-md-offset-4 col-sm-12 col-xs-12 standard-top-padding">
                    <input name="amount" type="text" class="form-control" id="amount_change" placeholder="Enter amount" onkeydown="return checkKey(event)" />
                    <!--input name="paid" type="checkbox" id="paid_check" checked="checked" title="Keep this checked unless you're giving a discount to driver" /!-->
                </div>
                <!--div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 standard-top-padding">
                    <button id="incrAmount" type="button" class="btn btn-warning"><span class="glyphicon glyphicon-plus"></span>&emsp;Add amount</button>
                </div!-->
                <div id= "typePayment"class="radio col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                  <label class="radio-inline"><input type="radio" id="duededuction" name="dueEdit" value="Due Deduction" checked="checked" autocomplete="off">Due Deduction</label>
                  <label class="radio-inline"><input type="radio" id="gift" name="dueEdit" value="Gift" autocomplete="off">Gift</label>
                  <label class="radio-inline"><input type="radio" id="adminfine" name="dueEdit" value="Admin Fine" autocomplete="off">Admin Fine</label>
                  <label class="radio-inline"><input type="radio" id="creditwithdraw" name="dueEdit" value="Credit Withdraw" autocomplete="off">Withdraw</label>
                  <label class="radio-inline"><input type="radio" id="tshirt" name="dueEdit" value="T-Shirt" autocomplete="off">T-Shirt</label>
                  <label class="radio-inline"><input type="radio" id="registration" name="dueEdit" value="Registration" autocomplete="off">Registration</label>
                </div>
                <div id= "typePaymentType"class="radio col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                  <label class="radio-inline"><input type="radio" id="online" name="duePaymentType" value="Online" checked="checked" autocomplete="off">Online</label>
                  <label class="radio-inline"><input type="radio" id="cash" name="duePaymentType" value="Cash" autocomplete="off">Cash</label>
                </div>
                <div class="col-lg-6 col-lg-offset-3 col-md-3 col-md-offset-3 col-sm-12 col-xs-12 standard-top-padding">
                    <input name="remarks" type="text" class="form-control" id="remarks" placeholder="Please Enter Remarks"/>
                    <label style="display: none;" id="sizeLabel" for="size">Choose T-Shirt Size:</label>
                    <select style="display: none;" name="size" id="size" autocomplete="off">
                        <option value="None">None</option>
                        <option value="S" selected>S</option>
                        <option value="M">M</option>
                        <option value="L">L</option>
                        <option value="XL">XL</option>
                        <option value="XXL">XXL</option>
                        <option value="XXXL">XXXL</option>
                    </select>
                    <label style="display: none;" id="quantityLabel" for="quantity">Choose Number of T-Shirt:</label>
                    <select style="display: none;" name="quantity" id="quantity" autocomplete="off">
                        <option value="0">0</option>
                        <option value="1" selected>1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                    </select>
                    <!--input name="paid" type="checkbox" id="paid_check" checked="checked" title="Keep this checked unless you're giving a discount to driver" /!-->
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <!--button id="resetDues" type="button" class="btn btn-md btn-warning"><span class="glyphicon glyphicon-refresh"></span>&emsp;Reset dues</button>
                    <button id="clearDues" type="button" class="btn btn-md btn-default"><span class="glyphicon glyphicon-trash"></span>&emsp;Clear dues</button!-->
                    <button id="confirmAmount" type="button" class="btn btn-md btn-primary"><span class="glyphicon glyphicon-save"></span>&emsp;Confirm</button>
                    <button id="logDues" type="button" class="btn btn-md btn-danger"><span class="glyphicon glyphicon-file"></span>&emsp;Due Log</button>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">
                    <h6><i><b>Unless you save your changes, they will not be reflected in the database.</b></i></h6>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Label................................................................................................................-->
    <div id="customerLabelForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12"
            style="padding: 12px; background-color: mintcream;">
            <h2 style="text-align: center;">Customer Label</h2>
            <form style="text-align: center;">
                <div class="row standard-top-padding">
                    <div class="col-lg-8 col-lg-offset-2 col-md-8 col-md-offset-2 col-sm-12 col-xs-12">
                        <div class="input-group">
                            <input name="mobile" type="mobile" class="form-control label-cust-search-input" maxlength="10" id="LabelCust_SearchInput_Phone" placeholder="Mobile Number" onkeydown="return checkKey(event)" />
                            <div class="input-group-btn">
                                <div class="btn-group">
                                    <button id="Search_Label_Cust" type="button" class="btn btn-success"><i class="glyphicon glyphicon-search"></i>&emsp;Find Customer</button>
                                    <div class="dropdown">
                                        <ul class="dropdown-menu">
                                            <!--<li class="tiny-padding">
                                                <button class="label-cust-search-criteria" id="LableCust_SearchCriteria_ID" type="button" class="btn">By ID</button>
                                            </li>-->
                                            <li class="tiny-padding">
                                                <button class="label-cust-search-criteria" id="LabelCust_SearchCriteria_Phone" type="button" class="btn">By Mobile</button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form><hr class="d4m-ending">
             <div class="row" style="padding-left: 30px;">
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-lock"></span>&nbsp;ID :&emsp;<span id="custId_Label" class="label"></span></h5>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-user"></span>&nbsp;Name :&emsp;<span id="custName_Label" class="label">---</span></h5>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-phone"></span>&nbsp;Phone Number :&emsp;<span id="custPhone_Label" class="label">---</span></h5>
                </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                <hr class="d4m-ending">
            </div>
            <div class="row tiny-top-padding" style="padding-left: 30px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option1" id="custCheckbox1"> VIP Customer
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option2" id="custCheckbox2"> Known Customer
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option3" id="custCheckbox3"> Customers who faced issue on previous trip
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option4" id="custCheckbox4"> Potential bad customer
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option5" id="custCheckbox5"> Customer who cancels the trip
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option6" id="custCheckbox6"> Customer who asks driver to do directly.
                    </label>
                </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                <hr class="d4m-ending">
            </div>
            <div class="row">
                <div class="col text-center tiny-top-padding">
                    <button id="saveCustLabel" type="button" class="btn btn-md btn-primary"><span class="glyphicon glyphicon-save"></span> Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Driver Label................................................................................................................-->
    <div id="driverLabelForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12"
            style="padding: 12px; background-color: mintcream;">
            <h2 style="text-align: center;">Driver Label</h2>
            <form style="text-align: center;">
                <div class="row standard-top-padding">
                    <div class="col-lg-8 col-lg-offset-2 col-md-8 col-md-offset-2 col-sm-12 col-xs-12">
                        <div class="input-group">
                            <input name="mobile" type="mobile" class="form-control label-driver-search-input" maxlength="10" id="LabelDriver_SearchInput_Phone" placeholder="Mobile Number" onkeydown="return checkKey(event)" />
                            <div class="input-group-btn">
                                <div class="btn-group">
                                    <button id="Search_Label_Driver" type="button" class="btn btn-success"><i class="glyphicon glyphicon-search"></i>&emsp;Find Driver</button>
                                    <div class="dropdown">
                                        <ul class="dropdown-menu">
                                            <!--<li class="tiny-padding">
                                                <button class="label-cust-search-criteria" id="LableCust_SearchCriteria_ID" type="button" class="btn">By ID</button>
                                            </li>-->
                                            <li class="tiny-padding">
                                                <button class="label-driver-search-criteria" id="LabelDriver_SearchCriteria_Phone" type="button" class="btn">By Mobile</button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form><hr class="d4m-ending">
             <div class="row" style="padding-left: 30px;">
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-lock"></span>&nbsp;ID :&emsp;<span id="driverId_Label" class="label"></span></h5>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-user"></span>&nbsp;Name :&emsp;<span id="driverName_Label" class="label">---</span></h5>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 tiny-top-padding">
                    <h5><span class="glyphicon glyphicon-phone"></span>&nbsp;Phone Number :&emsp;<span id="driverPhone_Label" class="label">---</span></h5>
                </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                <hr class="d4m-ending">
            </div>
            <div class="row tiny-top-padding" style="padding-left: 30px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option1" id="driverCheckbox1"> Lot of Cancelation
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option2" id="driverCheckbox2"> Driver mostly Late on Trip
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option3" id="driverCheckbox3"> Behavioural Issues
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option4" id="driverCheckbox4"> Not Groomed
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option5" id="driverCheckbox5"> Doesn't have T-Shirt
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option6" id="driverCheckbox6"> Have bad driving skill
                    </label>
                </div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                    <label class="checkbox">
                        <input type="checkbox" value="option7" id="driverCheckbox7"> Potentially Doing Direct
                    </label>
                </div>
            </div>
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 tiny-top-padding">
                <hr class="d4m-ending">
            </div>
            <div class="row">
                <div class="col text-center tiny-top-padding">
                    <button id="saveDriverLabel" type="button" class="btn btn-md btn-primary"><span class="glyphicon glyphicon-save"></span> Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Pricing Info and Calculation Section.........................................................................................-->
    <div id="pricingForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-3 col-lg-6 col-md-offset-3 col-md-6 col-sm-12 col-xs-12" style="text-align: center; padding: 12px; background-color: white;">
            <!--Pricing Tabs-->
            <div class="pricing-tabs">
                <div class="row">
                    <!--<div id="fare-calculator-tab" class="price-calculator col-lg-3 col-md-3 col-sm-12 col-xs-12"><i class="fa fa-calculator fa-1x"></i> Fare <br> Calculator</div>-->
                    <div id="in-city-round-tab" class="price-tab price-tab-active col-lg-2 col-md-2 col-sm-12 col-xs-12">In City Round Trip</div>
                    <div id="in-city-oneway-tab" class="price-tab col-lg-2 col-md-2 col-sm-12 col-xs-12">In City One way</div>
                    <div id="outstation-round-tab" class="price-tab col-lg-2 col-md-2 col-sm-12 col-xs-12">Outstation Round</div>
                    <div id="outstation-oneway-tab" class="price-tab col-lg-2 col-md-2 col-sm-12 col-xs-12">Outstation One way</div>
                </div>
            </div>
            <!--<div id="fare-calculator" class="standard-top-padding">
                <div class="row">
                    <h3 class="price-sub-section-heading">Fare Calculator</h3>
                </div>
                <div class="row parameter-box standard-top-padding standard-bottom-padding">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h4>Trip Type</h4>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <form>
                            <label class="radio-inline"><input type="radio" name="trip-type" checked>In City Round Trip</label>
                            <label class="radio-inline"><input type="radio" name="trip-type">In City One Way</label>
                            <label class="radio-inline"><input type="radio" name="trip-type">Outstation Round Trip</label>
                            <label class="radio-inline"><input type="radio" name="trip-type">Outstation One Way</label>
                        </form>
                    </div>
                </div>
                <div class="row parameter-box standard-top-padding standard-bottom-padding">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                        <h4>Set Date and Time</h4>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                        <div class='input-group date' id='datetimepicker_fare_calc'>
                            <input type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-time"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="row parameter-box standard-top-padding standard-bottom-padding">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                        <h4>Duration (Hours)</h4>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12">
                        <select class="form-control approval-selection">
                            <option value="2">2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                            <option value="5">5</option>
                            <option value="6">6</option>
                            <option value="7">7</option>
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                        </select>
                    </div>
                </div>
                <div class="row parameter-box standard-top-padding standard-bottom-padding" style="text-align: center;">
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 standard-top-padding">
                        <h3>Car Type</h3>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-12 col-xs-12"></div>
                    <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                        <div id="carTypeCarousel" class="carousel slide" data-ride="carousel">
                            <!-- Indicators
                            <ol class="carousel-indicators">
                                <li data-target="#carTypeCarousel" data-slide-to="0" class="active"></li>
                                <li data-target="#carTypeCarousel" data-slide-to="1"></li>
                                <li data-target="#carTypeCarousel" data-slide-to="2"></li>
                                <li data-target="#carTypeCarousel" data-slide-to="3"></li>
                            </ol>

                            <!-- Wrapper for slides
                            <div class="carousel-inner">
                                <div class="item active">
                                    <img src="{{ url_for("static", filename="assets/images/Hatchback.png") }}" alt="Hatchback">
                                </div>

                                <div class="item">
                                    <img src="{{ url_for("static", filename="assets/images/Sedan.png") }}" alt="Sedan">
                                </div>

                                <div class="item">
                                    <img src="{{ url_for("static", filename="assets/images/SUV.png") }}" alt="SUV">
                                </div>

                                <div class="item">
                                    <img src="{{ url_for("static", filename="assets/images/Luxury.png") }}" alt="Luxury">
                                </div>
                            </div>

                            <!-- Left and right controls
                            <a class="left carousel-control" href="#carTypeCarousel" data-slide="prev">
                                <span class="glyphicon glyphicon-chevron-left"></span>
                                <span class="sr-only">Previous</span>
                            </a>
                            <a class="right carousel-control" href="#carTypeCarousel" data-slide="next">
                                <span class="glyphicon glyphicon-chevron-right"></span>
                                <span class="sr-only">Next</span>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">

                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 justified-text">

                    </div>
                </div>
            </div>-->
            <!--In city round-trip Pricing Modal-->
            <div id="in-city-round" class="pricing-section standard-top-padding">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h2>PRICING -- In city Round Trip</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <button type="button" class="btn btn-xs btn-warning surge-toggle"><span class="normal">Click to see surge prices</span><span class="surge collapse">Click to see normal prices</span></button>
                        <h3 class="price-sub-section-heading">Hourly Base Fares</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Duration (Hours)</th>
                                        <th>Rate (₹ / hour)<sup>*</sup></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2 - 3</td>
                                        <td class="hourly-rate"><span class="normal-estimate collapse">3 hours --> 59 x 3 = 177</span><span class="normal">59</span><span class="surge-estimate collapse">3 hours --> 109 x 3 = 327</span><span class="surge collapse">109</span></td>
                                    </tr>
                                    <tr>
                                        <td>4 - 10</td>
                                        <td class="hourly-rate"><span class="normal-estimate collapse">4 hours --> 49 x 3 = 196</span><span class="normal">49</span><span class="surge-estimate collapse">4 hours --> 95 x 3 = 285</span><span class="surge collapse">95</span></td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                        <p style="text-align: right;" class="trollFont">*&nbsp;Click the mouse on a hourly rate for the base fare estimate</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h3 class="price-sub-section-heading">Car type dependent charges</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Vehicle Type</th>
                                        <th>Booking Charge (₹)<sup><span class="normal">#</span><span class="surge collapse"></span></sup></th>
                                        <th>Car Fare (₹)<sup><span class="normal">#</span><span class="surge collapse"></span></sup></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Hatchback</td>
                                        <td class="booking-charge"><span class="normal">12</span><span class="surge collapse">25</span><span class="discounted collapse">&emsp;(10)</span></td>
                                        <td class="car-fare"><span class="normal">5</span><span class="surge collapse">5</span><span class="discounted collapse">&emsp;(0)</span></td>
                                    </tr>
                                    <tr>
                                        <td>Sedan</td>
                                        <td class="booking-charge"><span class="normal">12</span><span class="surge collapse">25</span><span class="discounted collapse">&emsp;(10)</span></td>
                                        <td class="car-fare"><span class="normal">15</span><span class="surge collapse">15</span><span class="discounted collapse">&emsp;(10)</span></td>
                                    </tr>
                                    <tr>
                                        <td>SUV/LUV/MUV</td>
                                        <td class="booking-charge"><span class="normal">17</span><span class="surge collapse">31</span><span class="discounted collapse">&emsp;(15)</span></td>
                                        <td class="car-fare"><span class="normal">20</span><span class="surge collapse">20</span><span class="discounted collapse">&emsp;(12.50)</span></td>
                                    </tr>
                                    <tr>
                                        <td>Luxury</td>
                                        <td class="booking-charge"><span class="normal">45</span><span class="surge collapse">80</span><span class="discounted collapse">&emsp;(40)</span></td>
                                        <td class="car-fare"><span class="normal">150</span><span class="surge collapse">150</span><span class="discounted collapse">&emsp;(75)</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p style="text-align: right;" class="trollFont"><span class="normal">#&nbsp;50% EARLY BIRD DISCOUNTED charges displayed within ()</span><span class="surge collapse"></span></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h3 class="price-sub-section-heading">Additional Charges</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 justified-text">
                        <ul>
                            <span class="normal"><li class="small-bottom-padding">A part-night hike of <b>₹ 49</b> is applied once on a trip, when any part of it falls between <b>10:30 pm &amp; 06:29 am</b></li></span><span class="surge collapse"></span>
                            <li class="small-bottom-padding">A late night hike of <b>₹<span class="normal">69</span><span class="surge collapse">75</span></b> is applied once on a trip, when any part of it falls beyond <b>11:30 pm</b></li>
                            <li class="small-bottom-padding">When the scheduled duration is exceeded, additional rates of <b>₹<span class="normal">1</span><span class="surge collapse">1.65</span>/minute</b> are charged for the first hour. After an overtime of 1 hour, <b>₹<span class="normal">1.5</span><span class="surge collapse">1.65</span>/minute</b> is charged for subsequent extra time.</li>
                            <li class="small-bottom-padding">Extra charges may apply depending on the distance the D4M Companion has to travel to reach you.</li>
                            <li class="small-bottom-padding">An additional <b>₹20</b> is applicable for automatic gear vehicles.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!--In city one-way Pricing Modal-->
            <div id="in-city-oneway" class="pricing-section standard-top-padding collapse">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h2>PRICING -- In city One Way</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <center><h3 class="price-sub-section-heading">Hourly Base Fares</h3></center>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Duration (Hours)</th>
                                        <th>Rate (₹ / hour)<sup>*</sup></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2 - 3</td>
                                        <td class="hourly-rate"><span class="estimate collapse">3 hours --> 59 x 3 = 177</span><span class="normal">59</span></td>
                                    </tr>
                                    <tr>
                                        <td>4 - 10</td>
                                        <td class="hourly-rate"><span class="estimate collapse">4 hours --> 49 * 4 = 196</span><span class="normal">49</span></td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                        <p style="text-align: right;" class="trollFont">*&nbsp;Hold down the mouse on a hourly rate for the base fare estimate</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <center><h3 class="price-sub-section-heading">One Way Charge</h3></center>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <tbody>
                                    <tr>
                                        <td class="hourly-rate">₹ 50</td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h3 class="price-sub-section-heading">Car type dependent charges</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Vehicle Type</th>
                                        <th>Booking Charge (₹)<sup>#</sup></th>
                                        <th>Car Fare (₹)<sup>#</sup></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Hatchback</td>
                                        <td class="booking-charge"><span class="normal">20</span><span class="discounted">&emsp;(10)</span></td>
                                        <td class="car-fare"><span class="normal">0</span><span class="discounted">&emsp;(0)</span></td>
                                    </tr>
                                    <tr>
                                        <td>Sedan</td>
                                        <td class="booking-charge"><span class="normal">20</span><span class="discounted">&emsp;(10)</span></td>
                                        <td class="car-fare"><span class="normal">20</span><span class="discounted">&emsp;(10)</span></td>
                                    </tr>
                                    <tr>
                                        <td>SUV/LUV/MUV</td>
                                        <td class="booking-charge"><span class="normal">30</span><span class="discounted">&emsp;(15)</span></td>
                                        <td class="car-fare"><span class="normal">25</span><span class="discounted">&emsp;(12.50)</span></td>
                                    </tr>
                                    <tr>
                                        <td>Luxury</td>
                                        <td class="booking-charge"><span class="normal">80</span><span class="discounted">&emsp;(40)</span></td>
                                        <td class="car-fare"><span class="normal">150</span><span class="discounted">&emsp;(75)</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p style="text-align: right;" class="trollFont">#&nbsp;50% EARLY BIRD DISCOUNTED charges displayed within ()</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h3 class="price-sub-section-heading">Additional Charges</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 justified-text">
                        <ul>
                            <li class="small-bottom-padding">A Night charge of <b>₹99</b> is applied once on a trip, when any part of it falls between <b>11:30 pm &amp; 06:29 am</b></li>
                            <li class="small-bottom-padding">When the scheduled duration is exceeded, additional rates of <b>₹1/minute</b> are charged.</li>
                            <li class="small-bottom-padding">Extra charges may apply depending on the distance the D4M Companion has to travel to reach you.</li>
                            <li class="small-bottom-padding">An additional <b>₹20</b> is applicable for automatic gear vehicles.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!--Outstation round trip Pricing Modal-->
            <div id="outstation-round" class="pricing-section standard-top-padding collapse">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h2>PRICING -- Outstation Round Trip</h2>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <button type="button" class="btn btn-xs btn-warning surge-toggle"><span class="normal">Click here to see surge prices</span><span class="surge collapse">Click here to see normal prices</span></button>
                        <h3 class="price-sub-section-heading">Daily Base Fare</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <tbody>
                                    <tr>
                                        <td class="hourly-rate">₹ <span class="normal-estimate collapse">3 days --> 949 x 3 = 2847</span><span class="normal">949</span><span class="surge-estimate collapse">3 hours --> 1449 x 3 = 4347</span><span class="surge collapse">1449</span></td>
                                    </tr>

                                </tbody>
                            </table>
                        </div>
                        <p style="text-align: right;" class="trollFont">*&nbsp;Click on a daily rate for the base fare estimate</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h3 class="price-sub-section-heading">Car type dependent charges</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Vehicle Type</th>
                                        <th>Booking Charge (per day) (₹)<sup><span class="normal">#</span><span class="surge collapse"></span></sup></th>
                                        <th>Car Fare (₹)<sup><span class="normal">#</span><span class="surge collapse"></span></sup></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Hatchback</td>
                                        <td class="booking-charge"><span class="normal">50</span><span class="surge collapse">75</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                        <td class="car-fare"><span class="normal">0</span><span class="surge collapse">0</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                    </tr>
                                    <tr>
                                        <td>Sedan</td>
                                        <td class="booking-charge"><span class="normal">50</span><span class="surge collapse">75</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                        <td class="car-fare"><span class="normal">40</span><span class="surge collapse">40</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                    </tr>
                                    <tr>
                                        <td>SUV/LUV/MUV</td>
                                        <td class="booking-charge"><span class="normal">60</span><span class="surge collapse">90</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                        <td class="car-fare"><span class="normal">80</span><span class="surge collapse">80</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                    </tr>
                                    <tr>
                                        <td>Luxury</td>
                                        <td class="booking-charge"><span class="normal">100</span><span class="surge collapse">150</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                        <td class="car-fare"><span class="normal">200</span><span class="surge collapse">200</span><!--<span class="discounted">&emsp;(25)</span>--></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p style="text-align: right;" class="trollFont"><span class="normal">#&nbsp;50% EARLY BIRD DISCOUNTED charges displayed within ()</span><span class="surge collapse"></span></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h3 class="price-sub-section-heading">Important points to note</h3>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 justified-text">
                        <ul>
                            <li class="small-bottom-padding">An additional Food Charge of <b>₹<span class="normal">99</span><span class="surge collapse">99</span></b> is applicable <b>each day.</b></li>
                            <li class="small-bottom-padding"><b>The customer must provide lodging for the driver</b></li>
                            <li class="small-bottom-padding">When the scheduled duration is exceeded, additional rates of <b>₹<span class="normal">75</span><span class="surge collapse">150</span>/day</b> are charged.</li>
                            <li class="small-bottom-padding">An additional <b>₹30</b> is applicable for automatic gear vehicles.</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div id="outstation-oneway" class="pricing-section standard-top-padding collapse">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h2>PRICING -- Outstation One Way</h2>
                    </div>
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <h1>Coming Soon</h1>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Analytics Section................................................................................................................-->
    <div id="analyticsForm" class="row container-fluid admin-function collapse">
        <div class="col-lg-offset-1 col-lg-10 col-md-offset-1 col-md-10 col-sm-12 col-xs-12" style="text-align: center; padding: 12px; background-color: white;">
            <h2>Analytics<hr class="d4m-ending"></h2>
            <h5 class="standard-bottom-padding">Overview :
                &emsp;All New: <span class="daily-vital all-new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;All Upcoming: <span class="daily-vital all-upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;All On-going: <span class="daily-vital all-ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;New: <span class="daily-vital new-booking-count booking-new tiny-bottom-padding">0</span>
                &emsp;Upcoming: <span class="daily-vital upcoming-booking-count booking-accepted tiny-bottom-padding">0</span>
                &emsp;On-my-way: <span class="daily-vital onmyway-booking-count booking-onmyway tiny-bottom-padding">0</span>
                &emsp;On-going: <span class="daily-vital ongoing-booking-count booking-ongoing tiny-bottom-padding">0</span>
                &emsp;Completed Today: <span class="daily-vital successful-booking-count booking-completed tiny-bottom-padding">0</span>
                &emsp;D4M Cancel: <span class="daily-vital d4m-cancel-booking-count booking-d4m-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Not Alloc: <span class="daily-vital quick-cancel-booking-count booking-quick-cancel tiny-bottom-padding">0</span>
                &emsp;Cancel Alloc: <span class="daily-vital customer-cancel-booking-count booking-customer-cancel tiny-bottom-padding">0</span>
                <span class="sales-text">&emsp;Sales:₹ </span><span class="daily-vital sales-today tiny-bottom-padding">0</span>
                &emsp;<button class="btn btn-xs btn-warning refresh-stats"><span class="glyphicon glyphicon-refresh"></span></button>
            </h5>
            <button id="refreshAnalytics" type="button" class="btn btn-sm btn-warning"><span class="glyphicon glyphicon-refresh"></span>&emsp;Refresh Vital Stats</button>
            <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                    <div class="row small-top-padding">
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>Trips completed</h4>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4 id="completedTrips"></h4>
                        </div>
                    </div>
                    <div class="row small-top-padding">
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>Customers</h4>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4 id="customer_count"></h4>
                        </div>
                    </div>
                    <div class="row small-top-padding">
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>Drivers</h4>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4 id="drivers"><span id="driver_count"></span>&emsp;<span id="active_driver_count" class="label label-success"></span></h4>
                        </div>
                    </div>
                    <div class="row small-top-padding">
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>Revenue Generated</h4>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>₹&nbsp;<span id="revenue_generated"></span></h4>
                        </div>
                    </div>
                    <div class="row small-top-padding">
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>Revenue Collected</h4>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h4>₹&nbsp;<span id="revenue_collected"></span></h4>
                        </div>
                    </div>
                    <div class="row small-top-padding">
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h3>Total Sales</h3>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                            <h3>₹&nbsp;<span id="total_sales"></span></h3>
                        </div>
                    </div>
                </div>

                <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">
                    <h4>
                        <button id="previousChart" type="button" class="btn btn-md btn-success">
                            <span class="glyphicon glyphicon-chevron-left"></span>
                        </button>

                        &emsp;<span id="chartHeader"><span class="timescale-type">Monthly</span>&nbsp;
                        <span class="chart-type">Total Sales</span></span>&emsp;

                        <button id="refresh-Total_Sales_Chart" type="button" class="btn btn-sm btn-warning chart-refresh">
                            <span class="glyphicon glyphicon-refresh"></span>
                            &emsp;Get <span class="chart-type">Total Sales</span> Data
                        </button>&emsp;

                        <button id="nextChart" type="button" class="btn btn-md btn-success">
                            <span class="glyphicon glyphicon-chevron-right"></span>
                        </button>
                        <hr class="d4m-ending">
                    </h4>

                    <div class="row" style="margin: 2px;">
                        <div id="timescale_zoom_out" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 chart-timescale-change collapse"><span class="glyphicon glyphicon-minus"></span></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10"><canvas class="trend-charts" id="Monthly-Total_Sales_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Yearly-Total_Sales_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Weekly-Total_Sales_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Daily-Total_Sales_Chart"></canvas></div>

                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Monthly-Trips_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Yearly-Trips_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Weekly-Trips_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Daily-Trips_Chart"></canvas></div>

                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Monthly-Revenue_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Yearly-Revenue_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Weekly-Revenue_Chart"></canvas></div>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse"><canvas class="trend-charts" id="Daily-Revenue_Chart"></canvas></div>
                        <div id="timescale_zoom_in" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 chart-timescale-change collapse"><span class="glyphicon glyphicon-plus"></span></div>
                    </div>
                </div>

            </div>
        </div>

    </div>

    <!--modal for image display-->
    <div class="modal fade" id="profilePicModal" role="dialog">
    <div class="modal-dialog" style="width: 60%;">

      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Profile Picture</h4>
        </div>
        <div class="modal-body">
          <img src="{{ url_for("static", filename="assets/images/driverLicenceIcon.png") }}" class="img-rounded" alt="Cinque Terre" style="width:100%">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>
  <!--modal for licence display-->
    <div class="modal fade" id="licencePicModal" role="dialog">
    <div class="modal-dialog" style="width: 60%;">

      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title">Licence Picture</h4>
        </div>
        <div class="modal-body">
          <img src="{{ url_for("static", filename="assets/images/driverProfileIcon.png") }}" class="img-rounded" alt="Cinque Terre" style="width:100%">
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

  <!--info modal-->
 <div class="modal fade" id="infoModal" role="dialog" style="border-radius: 5px; width: 100%; top: 30%;">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="border-radius: 5px; background: lemonchiffon; box-shadow: 0 0 10px 1px black;">
          <h4 class="modal-title" style="text-align: center;"></h4>
        </div>
      </div>

    </div>
  </div>

  <!--loader modal-->
 <div class="modal fade" id="loaderModal" role="dialog" style="border-radius: 5px; width: 100%; top: 30%;">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="border-radius: 5px; background: lemonchiffon; box-shadow: 0 0 10px 1px black;">
            <div class="row">
                <div class="col-lg-4 col-md-4 col-sm-4 col-xs-12">
                    <div class="rotate">
                        <img src="{{ url_for("static", filename="assets/images/Just-Wheel.png") }}" style="width: 120px; height: 120px;">
                    </div>
                </div>
                <div class="col-lg-8 col-md-8 col-sm-8 col-xs-12">
                    <h4 class="modal-title" style="text-align: center;"></h4>
                </div>
            </div>
          <button type="button" class="btn btn-default btn-dismiss collapse" data-dismiss="modal"></button>
        </div>
      </div>

    </div>
  </div>

  <!--Drivers List Modal-->
    <div class="modal fade" id="driversListModal" role="dialog">
        <div class="modal-dialog col-lg-6 col-md-6 col-sm-12 col-xs-12">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                </div>
                <div class="modal-body">
                    <div class="row standard-left-padding">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding">
                            <div class="row">
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 driver-color-class driver-unavailable"></div>&emsp;<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">Unavailable</div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding">
                            <div class="row">
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 driver-color-class driver-unapproved"></div>&emsp;<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">Unapproved</div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding">
                            <div class="row">
                                <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12 driver-color-class driver-unavailable-unapproved"></div>&emsp;<div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">Unavailable &amp; Unapproved</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-offset-2 col-lg-8 col-md-offset-2 col-md-10 col-sm-offset-1 col-sm-10 col-xs-12">
                            <div class="form-group">
                                <input name="driverListSearch" type="text" class="form-control list-filter-control" id="driverListSearch" placeholder="Search Driver...">
                                <div id="driverSuggestions" class="ui-front suggestions-list-ui"></div>
                            </div>
                        </div>
                        <div class="dynamic-table-container standard-top-padding">
                            <table class="table table-bordered table-striped height-constrained-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Contact</th>
                                        <th>Licence</th>
                                        <th>Score</th>
                                        <th class="driver-base-loc collapse">Base Location</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!--Pending Drivers List Modal-->
    <div class="modal fade" id="pendingListModal" role="dialog">
        <div class="modal-dialog col-lg-4 col-md-4 col-sm-8 col-xs-12">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Pending Drivers List</h4>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-offset-2 col-lg-8 col-md-offset-2 col-md-10 col-sm-offset-1 col-sm-10 col-xs-12">
                            <div class="form-group">
                                <input name="pendingListSearch" type="text" class="form-control" id="pendingListSearch" placeholder="Search Driver...">
                                <div id="pendingSuggestions" class="ui-front suggestions-list-ui"></div>
                            </div>
                        </div>
                        <div class="dynamic-table-container standard-top-padding">
                            <table class="table table-bordered table-striped height-constrained-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Contact</th>
                                        <th>Estimate</th>
                                    </tr>
                                </thead>
                                <tbody id="pending-drivers-list"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!--Customer Trip Modal-->
    <div class="modal fade" id="customerTripModal" role="dialog">
        <div class="modal-dialog col-lg-6 col-md-6 col-sm-12 col-xs-12">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                </div>
                <div class="modal-body">
                    <div class="row standard-left-padding">
                        <span>Customer name : </span>&nbsp;<span class="ct-entry" id="ct-name"></span><br/>
                        <span>Customer mobile : </span>&nbsp;<span class="ct-entry" id="ct-mob"></span><br/>
                        <span>Total Trip : </span>&nbsp;<span class="ct-entry" id="ct-total-trip"></span><br/>
                        <span>Completed Trip : </span>&nbsp;<span class="ct-entry"  id="ct-completed"></span><br/>
                        <span>Cancelled Trip : </span>&nbsp;<span class="ct-entry"  id="ct-canc-unalloc"></span><br/>
                        <span>Cancelled Trip (Allocated) : </span>&nbsp;<span class="ct-entry"  id="ct-canc-alloc"></span><br/>
                        <span>D4M Cancelled : </span>&nbsp;<span class="ct-entry"  id="ct-canc-d4m"></span><br/>
                    </div>
                    <div class="row">
                        <div class="dynamic-table-container standard-top-padding">
                            <table class="table table-bordered table-striped height-constrained-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Date</th>
                                        <th>Driver Name</th>
                                        <th>Rating</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="cust-trip-list"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <!--Driver Trip Modal-->
    <div class="modal fade" id="driverTripModal" role="dialog">
        <div class="modal-dialog col-lg-6 col-md-6 col-sm-12 col-xs-12">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                </div>
                <div class="modal-body">
                    <div class="row standard-left-padding">
                        <span>Driver name : </span>&nbsp;<span class="dt-entry" id="dt-name"></span><br/>
                        <span>Driver mobile : </span>&nbsp;<span class="dt-entry" id="dt-mob"></span><br/>
                        <span>Total Trip : </span>&nbsp;<span class="dt-entry" id="dt-total-trip"></span><br/>
                        <span>Total B2B Trip : </span>&nbsp;<span class="dt-entry" id="dt-total-b2b"></span><br/>
                        <span>Total B2C Trip : </span>&nbsp;<span class="dt-entry" id="dt-total-b2c"></span><br/>
                        <span>Completed Trip : </span>&nbsp;<span class="dt-entry"  id="dt-completed"></span><br/>
                        <span>Cancelled Trip (Allocated) : </span>&nbsp;<span class="dt-entry"  id="dt-canc-alloc"></span><br/>
                        <span>D4M Cancelled : </span>&nbsp;<span class="dt-entry"  id="dt-canc-d4m"></span><br/>
                    </div>
                    <div class="row">
                        <div class="dynamic-table-container standard-top-padding">
                            <table class="table table-bordered table-striped height-constrained-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Date</th>
                                        <th>Customer Name</th>
                                        <th>Rating</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody id="driv-trip-list"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <!--Book Log Modal-->
    <div class="modal fade" id="bookLogModal" role="dialog">
        <div class="modal-dialog col-lg-4 col-md-4 col-sm-8 col-xs-12">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Booking Log</h4>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="dynamic-table-container standard-top-padding">
                            <table class="table table-bordered table-striped height-constrained-table">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Driver Name</th>
                                        <th>User Name</th>
                                        <th>Allocated By</th>
                                        <th>Action Type</th>
                                        <th>Cancel Source</th>
                                        <th>Cancel By</th>
                                        <th>Driver Fine</th>
                                        <th>User Fine</th>
                                        <th>Fine Reason Detail</th>
                                        <th>Location</th>
                                        <th>Cancel Reason Change</th>
                                    </tr>
                                </thead>
                                <tbody id="book-log-list"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!--Driver Due Log Modal-->
    <div class="modal fade" id="driverDueLogModal" role="dialog">
        <div class="modal-dialog col-lg-4 col-md-4 col-sm-8 col-xs-12">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Booking Log</h4>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="dynamic-table-container standard-top-padding">
                            <table class="table table-bordered table-striped height-constrained-table">
                                <thead>
                                    <tr>
                                        <th>Driver Name</th>
                                        <th>TimeStamp</th>
                                        <th>Due Deducted</th>
                                        <th>Source</th>
                                    </tr>
                                </thead>
                                <tbody id="driver-due-list"></tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!--Date time picker Modal-->
    <div class="modal fade" id="datetimepicker_modal" role="dialog">
        <div class="modal-dialog col-lg-3 col-md-3 col-sm-6 col-xs-12">
        <input type="hidden" id="dt_book_tr"/>
      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Choose Date and Time for</h4>
                </div>
                <div class="modal-body">
                    <!--dt picker-->
                    <div class="container">
                        <div class="row">
                            <div class='col-lg-3 col-md-3 col-sm-6 col-xs-12'>
                                <div class="form-group">
                                    <div class='input-group date' id='datetimepicker_trip_start_change'>
                                        <input type='text' class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="glyphicon glyphicon-time"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div><!--dtpicker-->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm-date-time" type="button" class="btn btn-success" data-dismiss="modal">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <!--Comment Edit Modal-->
    <div class="modal fade" id="commentEdit_modal" role="dialog">
        <div style="width: 90vw; max-width: 800px;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Edit Comment for booking # <span class="booking-id"></span></h4>
                </div>
                <div class="modal-body">
                    <label for="comment">Comment:</label>
                    <textarea class="form-control" id="comment"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm-comment-change" type="button" class="btn btn-success">Confirm</button>
                </div>
            </div>
        </div>
    </div>
    <!--Feedback Modal-->
    <div class="modal fade" id="feedback_modal" role="dialog">
        <div style="width: 90vw; max-width: 800px;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Customer feedback for booking # <span class="booking-id"></span></h4>
                </div>
                <div class="modal-body">
                    <ul class="feedUl">
                        <li class="feedLi">
                            <input type='radio' value='1' name='feedback' id='feedHap' class='feedInp'/>
                            <label for='feedHap' class='feedLabel'>😀</label>
                        </li>
                        <li class="feedLi">
                            <input type='radio' value='0' name='feedback'  id='feedNeut' class='feedInp'/>
                            <label for='feedNeut' class='feedLabel'>😐</label>
                        </li>
                        <li class="feedLi">
                            <input type='radio' value='-1' name='feedback'  id='feedAng' class='feedInp'/>
                            <label for='feedAng' class='feedLabel'>🤬</label>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <!--Fare Change Modal-->
    <div class="modal fade" id="price_change_modal" role="dialog">
        <div class="modal-dialog" style="width: 60%!important;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Change Fare Estimate for Booking # <span class="price-change estimate-change-ref"></span></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <h5>Net Fare<br>(<span class="current-net-estimate-range"></span>) </h5>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <input name="baseFare" type="text" class="form-control" id="baseFare" placeholder="0.00" />
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <h5>Booking Charge<br>(<span class="current-booking-charge"></span>) </h5>
                        </div>
                        <div class="col-lg-3 col-md-3 col-sm-12 col-xs-12">
                            <input name="bookingCharge" class="form-control" id="bookingCharge" placeholder="0.00" />
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_price_change" type="button" class="btn btn-success">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <!--Perma Driver Financial Modal-->
    <div class="modal fade" id="perma_accounts_modal" role="dialog">
        <div class="modal-dialog" style="width: 60%!important;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <span class="perma-accounts-action-statement"></span> # <span class="perma-accounts-ref"></span>
                        <span class="perma-accounts-name"></span>:
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12 small-top-padding">
                            <h5><span class="perma-accounts-action-input-label"></span>:</h5>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 small-top-padding">
                            <input name="changeAmount" type="text" class="form-control" id="changeAmount" placeholder="0.00" />
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-2 col-md-2 col-sm-6 col-xs-12 small-top-padding">
                            <h5><span class="perma-accounts-timestamp-input-label"></span>:</h5>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-6 col-xs-12 small-top-padding">
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_perma_account_timestamp'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_accounts_change" type="button" class="btn btn-success">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <!--Trip Type Change Pick Modal-->
    <div class="modal fade" id="triptype_dest_pick_modal" role="dialog">
        <div class="modal-dialog" style="width: 60%!important;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Pick Destination Location For Booking # <span class="dest-pick-book-id"></span></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                            <label for="tripType">Choose a Trip Type: </label>
                            <select name="tripType" id="tripType">
                                <option value="1">In City Round</option>
                                <option value="3">In City Oneway</option>
                                <option value="4">Minios Round</option>
                                <option value="6">Minios Oneway</option>
                            </select>
                        </div>
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <h5>Pick Destination</h5>
                        </div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">
                            <input id="tripTypeDestSearch" class="form-control" type="text" placeholder="Destination Address">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_tripType_convert" type="button" class="btn btn-success">Convert</button>
                </div>
            </div>
        </div>
    </div>

    <!--Car Change Modal-->
    <div class="modal fade" id="car_type_modal" role="dialog">
        <div class="modal-dialog" style="width: 60%!important;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Change Car Type For Booking # <span class="car-type-book-id"></span></h4>
                </div>
                <div class="modal-body">
                    <p><label for="geartype">Gear Type:</label>
                      <input type="radio" id="manualGear" name="transmission" value="0" checked>
                      <label for="manual">Manual</label>
                      <input type="radio" id="automaticGear" name="transmission" value="1">
                      <label for="automatic">Automatic</label><br>
                    <p><label for="cartype">Car Type:</label>
                    <select name="carselect" id="carselect">
                        <option value="0">Hatchback</option>
                        <option value="1">Sedan</option>
                        <option value="2">SUV</option>
                        <option value="3">Luxury</option>
                    </select>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_car_type" type="button" class="btn btn-success">Change</button>
                </div>
            </div>
        </div>
    </div>
    <!--Cancel Reason Modal-->
    <div class="modal fade" id="cancel_modal" role="dialog">
        <div class="modal-dialog" style="width: 60%!important;">
      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Select Cancellation Reason For Booking # <span class="cancel-book-id"></span></h4>
                </div>
                <div class="modal-body">
                    <p>
                        <label for="cancellationReason">Cancellation Reason:</label>
                        <select name="cancellationReason" id="cancellationReason">
                            <option value="0">Change of Plans</option>
                            <option value="1">Driver Denied</option>
                            <option value="2">Favorite Driver Not Assigned</option>
                            <option value="3">Driver Requesting Extra Fare</option>
                            <option value="4">Driver Asking to Take Offline</option>
                            <option value="5">Selected Wrong Location</option>
                            <option value="6">Selected Different Service</option>
                            <option value="7">Booked by Mistake</option>
                            <option value="8">Wait Time Too Long</option>
                            <option value="9">Got Driver Elsewhere</option>
                            <option value="10">Checking Price Estimate</option>
                            <option value="11">Taking Too Long to Allocate</option>
                            <option value="12">Direct Trip</option>
                            <option value="13">Wrongly Taken</option>
                            <option value="14">Previous Trip Not Ended</option>
                            <option value="15">Personal Issue of Driver</option>
                            <option value="16">Transportation Problem</option>
                            <option value="17">Customer Not Responding</option>
                            <option value="18">Customer Asked to Cancel</option>
                            <option value="63">No Allocation</option>
                            <option value="62">Other</option>
                            <!-- Add more options as needed -->
                        </select>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_cancel" type="button" class="btn btn-danger">Cancel</button>
                </div>
            </div>
        </div>
    </div>
    <!--Update Reason Modal-->
        <div class="modal fade" id="update_cancel_modal" role="dialog">
            <div class="modal-dialog" style="width: 60%!important;">
          <!-- Modal content-->
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title cancel-new">Updated Cancellation Reason For Booking Cancel #<span class="update-cancel-id"></span></h4>
                    </div>
                    <div class="modal-body">
                        <p>
                            <label for="cancellationReason">Cancellation Reason:</label>
                            <select name="cancellationReason" id="cancellationReasonupdate">
                                <option value="0">Change of Plans</option>
                                <option value="1">Driver Denied</option>
                                <option value="2">Favorite Driver Not Assigned</option>
                                <option value="3">Driver Requesting Extra Fare</option>
                                <option value="4">Driver Asking to Take Offline</option>
                                <option value="5">Selected Wrong Location</option>
                                <option value="6">Selected Different Service</option>
                                <option value="7">Booked by Mistake</option>
                                <option value="8">Wait Time Too Long</option>
                                <option value="9">Got Driver Elsewhere</option>
                                <option value="10">Checking Price Estimate</option>
                                <option value="11">Taking Too Long to Allocate</option>
                                <option value="12">Direct Trip</option>
                                <option value="13">Wrongly Taken</option>
                                <option value="14">Previous Trip Not Ended</option>
                                <option value="15">Personal Issue of Driver</option>
                                <option value="16">Transportation Problem</option>
                                <option value="17">Customer Not Responding</option>
                                <option value="18">Customer Asked to Cancel</option>
                                <option value="63">No Allocation</option>
                                <option value="62">Other</option>
                                <!-- Add more options as needed -->
                            </select>
                        </p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        <button  id="confirm_cancel_update" type="button" class="btn btn-danger update_cancel-button ">Update</button>
                    </div>
                </div>
            </div>
        </div>
    <!--Unallocate Reason Modal-->
    <div class="modal fade" id="unallocate_modal" role="dialog">
        <div class="modal-dialog" style="width: 60%!important;">
      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Select Unallocate Reason For Booking # <span class="unallocate-book-id"></span></h4>
                </div>
                <div class="modal-body">
                    <p>
                        <label for="unallocateReason">Unallocate Reason:</label>
                        <select name="unallocateReason" id="unallocateReason">
                            <option value="1">Driver Denied</option>
                            <option value="3">Driver Requesting Extra Fare</option>
                            <option value="4">Driver Asking to Take Offline</option>
                            <option value="8">Wait Time Too Long</option>
                            <option value="13">Wrongly Taken</option>
                            <option value="14">Previous Trip Not Ended</option>
                            <option value="15">Personal Issue of Driver</option>
                            <option value="16">Transportation Problem</option>
                            <option value="19">Favorite Driver Not Assigned</option>
                            <!-- Add more options as needed -->
                        </select>
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_unallocate" type="button" class="btn btn-danger">Unallocate</button>
                </div>
            </div>
        </div>
    </div>
    <!--Driver Location Pick Modal-->
    <div class="modal fade" id="driver_location_pick_modal" tabindex="-1" role="dialog">
        <div class="modal-dialog" style="width: 40%!important;">

      <!-- Modal content-->
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Pick Driver Location</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-sm-12 col-xs-12">
                            <h5>Pick Home Location</h5>
                        </div>
                        <div class="col-lg-8 col-md-8 col-sm-12 col-xs-12">
                            <input id="driverLocationChange" class="form-control" type="text" placeholder="Home Address">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    <button id="confirm_driver_location" type="button" class="btn btn-success">Save</button>
                </div>
            </div>
        </div>
    </div>
    <!--Driver Edit Modal-->
    <div class="modal fade" id="edit_modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="editModalLabel">Edit</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <input id="editModalText" class="modalTextInput form-control" placeholder="Edit Text" />
          </div>
          <div class="modal-footer">
            <button type="button" class="saveEdit btn btn-primary">Save changes</button>
          </div>
        </div>
      </div>
    </div>
    <!--Trip Photo Modal-->
    <div class="modal fade" id="photoModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" id= "downloadZip" aria-hidden="true" class="btn btn-default">Download Zip</button>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    <!-- Indicators -->
                    <div class="carousel slide" data-interval="false" id="TripPhotoCarousel">
                        <ol class="carousel-indicators photo-count">
                        </ol>
                        <!-- Wrapper for slides -->
                        <div class="carousel-inner photo-display">
                        </div>
                        <!-- Controls -->
                        <a href="#TripPhotoCarousel" class="left carousel-control" data-slide="prev"><span class="icon-prev"></span></a>
                        <a href="#TripPhotoCarousel" class="right carousel-control" data-slide="next"><span class="icon-next"></span></a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        Close
                    </button>
                    <!-- <button type="button" class="btn btn-primary">Save changes</button> -->
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>

    <script type='text/javascript'>
        function checkKey(e) {
            if(e.shiftKey)
                return false;
            if(e.ctrlKey||e.keyCode==9||e.keyCode==18||e.keyCode==13||e.keyCode==37||e.keyCode==39)
                return true;
            return e.keyCode>=46&&e.keyCode<=57||e.keyCode==8||e.keyCode>=96&&e.keyCode<=105;
        }
    </script>
</body>

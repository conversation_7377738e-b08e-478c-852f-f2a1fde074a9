import pytest
from flask import Flask
from models import Users, <PERSON>, db,AdminA<PERSON>ess,User<PERSON><PERSON>,DriverVerify
from _utils import get_pwd,get_salt
import random
from flask import current_app as app
import datetime
import hashlib
from sqlalchemy import exc
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token
from conftest import create_user_and_driver,unique_user_data



def access_token_create(user_data):
    expires_access = datetime.timedelta(days=365)
    identity_with_claims = {
        'id': user_data.id,
        'roles': user_data.role,
        'region': user_data.region,
        'fname': user_data.fname,
        'lname': user_data.lname,
    }
    with app.app_context():
        access_token = create_access_token(identity=identity_with_claims, additional_claims=identity_with_claims, expires_delta=expires_access)
    return access_token


def refresh_token(user_data,expired):
    # Create and return a valid refresh token for the test user
    with app.app_context():
        refresh_token = create_refresh_token(identity=user_data.id)

    if expired:
        expiry = datetime.datetime.now() - datetime.timedelta(days=1)
    else:
        expiry = datetime.datetime.now() + datetime.timedelta(days=365)
    token_entry = UserToken(user_data.id,refresh_token,'TestAgent', expiry=expiry)
    try:
        db.session.add(token_entry)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    return refresh_token

#  API - /token/verify -----------

def test_verify_user_enabled(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    access_token=access_token_create(user)
    headers = {'Authorization': f'Bearer {access_token}'}
    response = client.post('/token/verify', headers=headers)
    assert response.status_code == 200
    response_json = response.get_json()

    assert response_json['success'] == 1

def test_verify_user_disabled(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    try:
        user.enabled=False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    access_token=access_token_create(user)
    headers = {'Authorization': f'Bearer {access_token}'}

    response = client.post('/token/verify', headers=headers)

    assert response.status_code == 401
    response_json = response.get_json()
    assert response_json['success'] == 0

#   ---------------------------------

#   API - /token/refresh -----------

def test_refresh_success(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    refresh=refresh_token(user,0)
    headers = {
        'Authorization': f'Bearer {refresh}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/token/refresh', headers=headers)
    assert response.status_code == 200
    assert response.json['refresh'] is True
    assert 'Set-Cookie' in response.headers  # Check if access token is set in cookies

def test_refresh_with_expired_token(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    refresh=refresh_token(user,1)
    headers = {
        'Authorization': f'Bearer {refresh}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/token/refresh', headers=headers)
    assert response.status_code == 401
    assert response.json['message'] == 'Failed to refresh'
    assert response.json['refresh'] is False

def test_refresh_disabled_user(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    try:
        user.enabled = False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    refresh=refresh_token(user,0)
    headers = {
        'Authorization': f'Bearer {refresh}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/token/refresh', headers=headers)
    assert response.status_code == 401  # Account is disabled
    assert response.json['success'] == -1

#   ---------------------------------


#  API - /token/exists/driver -----------

def test_check_exists_driver_user_exists(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    response = client.post('/token/exists/driver', data={'mobile': data['mobile']})

    assert response.status_code == 200
    assert response.get_json() == {'exists': 1, 'user': 1, 'message':'Driver exist'}

def test_check_exists_driver_user_not_a_driver(client):
    data = unique_user_data()
    user = Users(
        fname=data['fname'],
        lname=data['lname'],
        mobile=data['mobile'],
        email=data['email'],
        pwd=data['pwd'],
        role=Users.ROLE_DRIVER
    )
    try:
        db.session.add(user)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    response = client.post('/token/exists/driver', data={'mobile': data['mobile']})


    assert response.status_code == 200
    assert response.get_json() == {'exists': 0, 'user': 1, 'message':'Driver does not exist'}

#   ---------------------------------

#  API - /token/login_driv -----------

def test_login_success(client):
    data = unique_user_data()
    create_user_and_driver(data)

    response = client.post('/token/login_driv', data={'mobile': data['mobile'], 'pwd': data['pwd']}, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 200
    response_data = response.get_json()
    assert response_data['success'] == 1



def test_login_otp_success(client):
    # Create mock user and driver for OTP login
    data = unique_user_data()
    user,driver = create_user_and_driver(data)

    # Simulate a login request with OTP
    response = client.post('/token/login_driv', data={'mobile': data['mobile'], 'pwd': '3487', 'type': 1})

    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] == 1

def test_login_invalid_password(client):
    data = unique_user_data()
    create_user_and_driver(data)

    response = client.post('/token/login_driv', data={'mobile': data['mobile'], 'pwd': 'wrongpassword'})

    assert response.status_code == 401
    response_data = response.get_json()
    assert response_data['success'] == 0

def test_login_disabled_user(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    try:
        user.enabled = False
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    response = client.post('/token/login_driv', data={'mobile': data['mobile'], 'pwd': data['pwd']})

    assert response.status_code == 401
    response_data = response.get_json()
    assert response_data['success'] == 0

# ---------------------------------
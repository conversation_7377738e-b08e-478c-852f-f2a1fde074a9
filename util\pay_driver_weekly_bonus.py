from main import app
import sys
sys.path.append("/app/")
import datetime
from _utils import get_dt_ist
from models import db, Bookings, Trip, DriverDetails, DriverTrans
from _fcm import send_fcm_msg_driver
from _ops_message import send_slack_msg

NUM_TRIPS = 6
MONEY_THRESH = 1201
MONEY_TO_SEND = 100
SEND_ENABLED = True

def get_drivers_n_trips(start, end, n):
    res = {}
    all_trips_ndays = db.session.query(Trip, Bookings). \
        filter(Trip.starttime >= start). \
        filter(Trip.starttime <= end). \
        filter(Bookings.region == 0). \
        filter(Bookings.id == Trip.book_id). \
        filter(Bookings.type < 50).all()
    for t in all_trips_ndays:
        driver = t[1].driver
        trip_day = get_dt_ist(t[0].starttime.date(), t[0].starttime.time()).date()

        if driver not in res:
            res[driver] = set()

        res[driver].add(trip_day)
    for driver, trip_days in res.items():
        if len(trip_days) >= 6:
            print(driver, len(trip_days))
            print(trip_days)
    filtered_drivers = [driver for driver, trip_days in res.items() if len(trip_days) >= n]
    print(len(filtered_drivers))

    return filtered_drivers

def send_money_to_wallet(driver_id, amount):
    details = db.session.query(DriverDetails). \
        filter(DriverDetails.driver_id == int(driver_id))
    driver_details = details.first()
    wallet = driver_details.wallet + amount
    withdrawable = driver_details.withdrawable
    dt = DriverTrans(driver_id, amount*100,
                     wall_a=wallet, wall_b=driver_details.wallet,
                     with_a=withdrawable, with_b=driver_details.withdrawable,
                     method="Weekly bonus",
                     status=DriverTrans.COMPLETED, stop=True
                    )
    db.session.add(dt)
    details.update({DriverDetails.wallet: wallet, DriverDetails.withdrawable: withdrawable})
    send_slack_msg(0, "Driver id: " + str(driver_id) + " got weekly bonus of " + str(amount))
    db.session.commit()
    send_fcm_msg_driver(int(driver_id), title="Wallet amount added!", smalltext="Your wallet had ₹" + str(amount) + " added",
                        bigtext="Congrats! Your wallet amount was increased by ₹" + str(amount) + \
                        " for working 6 days a week. Your new wallet amount is ₹" + str(round(wallet + withdrawable, 2)) + \
                        " and withdrawable amount is ₹" + str(round(withdrawable, 2)) + ".")

if __name__ == '__main__':
    thresh_back = 6
    trip_thresh = NUM_TRIPS
    with app.app_context():
        try:
            if len(sys.argv) > 1:
                thresh_back = int(sys.argv[1])
            if len(sys.argv) > 2:
                trip_thresh = int(sys.argv[2])
        except Exception:
            pass
        sales_thresh = MONEY_THRESH
        m = MONEY_TO_SEND
        today = datetime.datetime.utcnow()
        days_until_sunday = (today.weekday() - 6) % 7
        last_sunday = today - datetime.timedelta(days=days_until_sunday)
        ndays_ago = last_sunday - datetime.timedelta(days=thresh_back)
        start_of_day = datetime.datetime(ndays_ago.year, ndays_ago.month, ndays_ago.day, 0, 0, 0)
        end_of_day = datetime.datetime(last_sunday.year, last_sunday.month, last_sunday.day, 23, 59, 59)
        print(start_of_day, end_of_day)
        ist_offset = datetime.timedelta(hours=5, minutes=30)
        start_of_day_utc = start_of_day - ist_offset
        end_of_day_utc = end_of_day - ist_offset
        all_driver_n_trips = get_drivers_n_trips(start_of_day_utc, end_of_day_utc, trip_thresh)
        print("Payment for", str(start_of_day.date()))
        print(len(all_driver_n_trips), "eligible for trips")
        for driver in all_driver_n_trips:
            print("Sending", driver, "INR", MONEY_TO_SEND)
            if SEND_ENABLED:
                send_money_to_wallet(driver, MONEY_TO_SEND)

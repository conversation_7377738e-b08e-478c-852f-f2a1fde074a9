$(document).ready(function() {
	$(".booking-entry").click(function() {
		showLoader();
		// prepare previews
		if($("#respBookingPreview").css("display") !== "None") {
			$("#mainNavMenuButton").addClass("collapse");
			$("#bookingPreviewExit").removeClass("collapse");
			$("#bookingPreviewTitle").find(".booking-id-holder").html($(this).find(".hdn-booking-id").val());
			$("#pageTitle").addClass("collapse");
			$("#bookingPreviewTitle").removeClass("collapse");
		}
		$(".preview-booking-id").html($(this).find(".hdn-booking-id").val());
		$(".preview-driver-name").html($(this).find(".driver-name").html());
		$(".date-time-text").find(".date-text").html($(this).find(".date-text").html());
		$(".date-time-text").find(".time-text").html($(this).find(".time-text").html());
		$(".preview-dur-text").html($(this).find(".dur-text").html());

		if($(this).find(".stat-text").html() === "Upcoming") {
			$(".booking-status-text").find(".preview-stat-text").html("Confirmed");
		}
		else {
			$(".booking-status-text").find(".preview-stat-text").html($(this).find(".stat-text").html());
		}

		$(".preview-estimate-text").html($(this).find(".estimate-text").html());
		$(".preview-estimate-statement").html($(this).find(".estimate-statement").html());
		$(".hdn-preview-src-lat").val(parseFloat($(this).find(".hdn-src-lat").val()));
		$(".hdn-preview-src-long").val(parseFloat($(this).find(".hdn-src-long").val()));
		$(".hdn-preview-dest-lat").val(parseFloat($(this).find(".hdn-dest-lat").val()));
		$(".hdn-preview-dest-long").val(parseFloat($(this).find(".hdn-dest-long").val()));

		setTimeout( function() { $("#mapRefresh").trigger("click"); }, 1500);
		setTimeout( function() { $("#respMapRefresh").trigger("click"); }, 2500);

		$(".source-text").html($(this).find(".hdn-src-addr").val());
		$(".destination-text").html($(this).find(".hdn-dest-addr").val());

		//preview type
		$("#bookingPreview").removeClass(
			"unallocated-upcoming-booking-preview allocated-upcoming-booking-preview completed-booking-preview cancelled-booking-preview");
		$("#respBookingPreview").removeClass(
			"unallocated-upcoming-booking-preview allocated-upcoming-booking-preview completed-booking-preview cancelled-booking-preview");

		if($(this).hasClass("unallocated-upcoming-booking-entry")) {
			$("#bookingPreview").addClass("unallocated-upcoming-booking-preview");
			$("#respBookingPreview").addClass("unallocated-upcoming-booking-preview");
			$(".preview-status-img").attr('src', "../static/assets/images/elements/bookingHistory-Pending.svg");
		}
		else if($(this).hasClass("allocated-upcoming-booking-entry")) {
			$("#bookingPreview").addClass("allocated-upcoming-booking-preview");
			$("#respBookingPreview").addClass("allocated-upcoming-booking-preview");
			$(".preview-status-img").attr('src', "../static/assets/images/elements/bookingHistory-Upcoming.svg");
		}
		else if($(this).hasClass("completed-booking-entry")) {
			$("#bookingPreview").addClass("completed-booking-preview");
			$("#respBookingPreview").addClass("completed-booking-preview");
			$(".preview-status-img").attr('src', "../static/assets/images/elements/bookingHistory-Completed.svg");
		}
		else if($(this).hasClass("cancelled-booking-entry")) {
			$("#bookingPreview").addClass("cancelled-booking-preview");
			$("#respBookingPreview").addClass("cancelled-booking-preview");
			$(".preview-status-img").attr('src', "../static/assets/images/elements/bookingHistory-Cancelled.svg");
		}

		var vehicleInformation	= getVehicleCategoryInformation(parseInt($(this).find(".hdn-veh-type").val()));
		$(".preview-vehicle-type").html(vehicleInformation["vehicleType"]);
		$(".preview-vehicle-type-image").attr("src", "../static/assets/images/elements/" + vehicleInformation["vehicleType"] + ".svg");
		$(".preview-transmission-type").html(vehicleInformation["transmissionType"]);
		$(".preview-transmission-type-image").attr("src", "../static/assets/images/elements/gearType_" + vehicleInformation["transmissionType"] + ".png");

		$(".preview-trip-type").html(getTripTypeText(parseInt($(this).find(".hdn-trip-type").val())));
		switch (parseInt($(this).find(".hdn-trip-type").val())) {
			case 1: $(".preview-trip-type-image").attr("src", "../static/assets/images/elements/RoundTrip.svg"); break;
			case 2: $(".preview-trip-type-image").attr("src", "../static/assets/images/elements/OneWay.svg"); break;
			case 3: $(".preview-trip-type-image").attr("src", "../static/assets/images/elements/MiniOS.svg"); break;
			case 4: $(".preview-trip-type-image").attr("src", "../static/assets/images/elements/Outstation.svg"); break;
			default: $(".preview-trip-type-image").attr("src", "../static/assets/images/elements/RoundTrip.svg"); break;
		}

		$(".payment-mode-text").html(getPaymentModeText(parseInt($(this).find(".hdn-payment-mode").val())));
		switch (parseInt($(this).find(".hdn-payment-mode").val())) {
			case 0: $(".payment-mode-image").attr("src", "../static/assets/images/elements/Cash.svg"); break;
			case 1: $(".payment-mode-image").attr("src", "../static/assets/images/elements/d4m_credit.svg"); break;
			default: $(".payment-mode-image").attr("src", "../static/assets/images/elements/select_payment_method.svg"); break;
		}

		$(".type-upcoming-allocated").addClass("collapse");
		$(".type-upcoming-unallocated").addClass("collapse");
		$(".type-completed").addClass("collapse");
		$(".type-cancelled-unallocated").addClass("collapse");
		$(".type-cancelled-unallocated").addClass("collapse");
		$(".rating-section").addClass("collapse");

		$(".preview-rating").html($(this).find(".hdn-driver-rating").val());
		var licence = $(this).find(".hdn-driver-licence").val();
		licence = licence.substring(0, 4) + "*".repeat(licence.length - 6) + licence.substring(licence.length - 2);
		$(".preview-licence").html(licence);

		switch($(this).find(".stat-text").html()) {
			case "Upcoming":
				$(".type-upcoming-allocated").removeClass("collapse");
				$(".rating-section").removeClass("collapse");
				break;
			case "Pending": $(".type-upcoming-unallocated").removeClass("collapse"); break;
			case "Completed":
				$(".type-completed").removeClass("collapse");
				$(".rating-section").removeClass("collapse");
				break;
			case "Cancelled":
				if($(this).find(".driver-name").html() === "Not Allocated")
					$(".type-cancelled-unallocated").removeClass("collapse");
				else $(".type-cancelled-allocated").removeClass("collapse");
			break;
		}

		// display previews
		setTimeout( function() {
			hideLoader();
			$("#bookingPreview").addClass("d-lg-block");
		 	$("#respBookingPreview").addClass("show");
		 }, 3000);
	});

	$("#bookingPreviewExit").click(function() {
		// remove previews
		$("#bookingPreview").removeClass("d-lg-block");
		$("#respBookingPreview").removeClass("show");
		$("#bookingPreviewExit").addClass("collapse");
		$("#mainNavMenuButton").removeClass("collapse");
		$("#bookingPreviewTitle").addClass("collapse");
		$("#pageTitle").removeClass("collapse");
	});

	var resizeId;
	$(window).resize(function() {
	    clearTimeout(resizeId);
	    resizeId = setTimeout(doneResizing, 500);
	});

	function doneResizing() {
	    $("#mapRefresh").trigger("click");
		$("#respMapRefresh").trigger("click");
	}
});

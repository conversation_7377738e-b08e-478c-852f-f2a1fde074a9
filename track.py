#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  track.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON> Mitra

from flask import Blueprint, render_template, redirect
from flask_jwt_extended import (
    jwt_required, get_jwt_identity)

from models import Users, Drivers, Bookings, Trip, C24Bookings
from models import db
from booking_params import BookingParams
from _utils_booking import fetch_booking_trip
import requests

track = Blueprint('track', __name__)


def driver_in_user_trip(driver, user):
    current_trip = db.session.query(Bookings, Trip).filter(Bookings.driver == driver). \
                    filter(Bookings.id == Trip.book_id).filter(Bookings.user == user). \
                    filter(Trip.endtime == None).first()
    if not current_trip:
        return False
    else:
        return True


def driver_in_b2b_trip(trip_type, driver, region):
    current_trip = db.session.query(Bookings, Trip).filter(Bookings.driver == driver). \
                    filter(Bookings.id == Trip.book_id).filter(Bookings.type == trip_type). \
                    filter(Trip.endtime == None).first()
    if not current_trip:
        return False
    return current_trip[0].region == region or region == -1


def user_can_see(driver, user):
    role_trip_type = {
        Users.ROLE_C24: BookingParams.TYPE_C24,
        Users.ROLE_CARDEKHO: BookingParams.TYPE_CARDEKHO,
        Users.ROLE_OLX: BookingParams.TYPE_OLX,
        Users.ROLE_ZOOMCAR: BookingParams.TYPE_ZOOMCAR,
        Users.ROLE_MAHINDRA: BookingParams.TYPE_MAHINDRA,
        Users.ROLE_BHANDARI: BookingParams.TYPE_BHANDARI,
        Users.ROLE_REVV_V2: BookingParams.TYPE_REVV_V2,
        Users.ROLE_SPINNY: BookingParams.TYPE_SPINNY,
        Users.ROLE_PRIDEHONDA: BookingParams.TYPE_PRIDEHONDA,
    }
    user_details = db.session.query(Users).filter(Users.id == user).first()
    if not user_details:
        return False
    user_role = user_details.role
    if user_role in Users.ROLE_ADMIN or user_role == Users.ROLE_SUPERADMIN:
        return True
    if user_role == Users.ROLE_DRIVER:
        return False            # Stupid case, but eh
    if user_role == Users.ROLE_USER:
        if driver_in_user_trip(driver, user):
            return True
        else:
            return False
    if user_role in role_trip_type.keys():
        region = user_details.region
        if driver_in_b2b_trip(role_trip_type.get(user_role), driver, region):
            return True
        else:
            return False
    return False


@track.route('/track/<int:driver_id>', methods=['GET', 'POST'])
@jwt_required()
def track_driver(driver_id):
    user = get_jwt_identity()
    if not user_can_see(driver_id, user=user):
        return 'Illegal access'
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Users.id == Drivers.user).first()
    if not driver_user:
        return 'No data for driver found'
    return render_template('track.html', driver_id=driver_user[0].id, driver_name=driver_user[1].get_name())


@track.route('/track/booking/<int:book_id>', methods=['GET', 'POST'])
@jwt_required()
def track_booking(book_id):
    booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
    if not booking:
        return 'No booking found'
    driver_id = booking.driver
    trip = fetch_booking_trip(booking.id)
    if not trip:
        return 'No trip ongoing'
    trip_entry = trip.first()
    if not trip_entry:
        return 'No trip ongoing'
    if trip_entry.status == Trip.TRIP_STOPPED:
        return 'No trip ongoing'
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Users.id == Drivers.user).first()
    if not driver_user:
        return 'No data for driver found'
    return render_template('track.html', driver_id=driver_user[0].id, driver_name=driver_user[1].get_name())

@track.route('/track/book/<int:book_id>', methods=['GET', 'POST'])
def track_book(book_id):
    booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
    if not booking:
        return 'No booking found'
    driver_id = booking.driver
    trip = fetch_booking_trip(booking.id)
    if not trip:
        return 'No trip ongoing'
    trip_entry = trip.first()
    if not trip_entry:
        return 'No trip ongoing'
    if trip_entry.status == Trip.TRIP_STOPPED:
        return 'No trip ongoing'
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Users.id == Drivers.user).first()
    if not driver_user:
        return 'No data for driver found'
    return render_template('track.html', driver_id=driver_user[0].id, driver_name=driver_user[1].get_name())

@track.route('/track/booking/<book_code>', methods=['GET', 'POST'])
def track_booking_code(book_code):
    booking = db.session.query(Bookings).filter(Bookings.code == book_code).first()
    if not booking:
        return 'No booking found'
    driver_id = booking.driver
    trip = fetch_booking_trip(booking.id)
    if not trip:
        return 'No trip ongoing'
    trip_entry = trip.first()
    if not trip_entry:
        return 'No trip ongoing'
    if trip_entry.status == Trip.TRIP_STOPPED:
        return 'No trip ongoing'
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Users.id == Drivers.user).first()
    if not driver_user:
        return 'No data for driver found'
    return render_template('track.html', driver_id=driver_user[0].id, driver_name=driver_user[1].get_name())

@track.route('/history/booking/<int:book_id>', methods=['GET', 'POST'])
@jwt_required()
def track_booking_history(book_id):
    booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
    if not booking:
        return 'No Booking Found'
    driver_id = booking.driver
    trip = fetch_booking_trip(booking.id)
    if not trip:
        return 'No Trip Found'
    trip_entry = trip.first()
    if not trip_entry:
        return 'No Trip Found'
    if trip_entry.status != Trip.TRIP_STOPPED:
        return 'Trip Not yet Over'
    url = f"https://storage.googleapis.com/drivers4me-prod.appspot.com/locationHistory/{booking.id}.json"
    if not check_file_url(url):
        return 'Location History Not Found'
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Users.id == Drivers.user).first()
    if not driver_user:
        return 'No Data For Driver Found'
    return render_template('history.html', booking_id=booking.id)

@track.route('/api/history/booking/<string:code>', methods=['GET', 'POST'])
def admin_track_booking_history(code):
    booking = db.session.query(Bookings).filter(Bookings.code == code).first()
    if not booking:
        return 'No Booking Found'
    driver_id = booking.driver
    trip = fetch_booking_trip(booking.id)
    if not trip:
        return 'No Trip Found'
    trip_entry = trip.first()
    if not trip_entry:
        return 'No Trip Found'
    if trip_entry.status != Trip.TRIP_STOPPED:
        return 'Trip Not yet Over'
    url = f"https://storage.googleapis.com/drivers4me-prod.appspot.com/locationHistory/{booking.id}.json"
    if not check_file_url(url):
        return 'Location History Not Found'
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Users.id == Drivers.user).first()
    if not driver_user:
        return 'No Data For Driver Found'
    return render_template('history.html', booking_id=booking.id)

def check_file_url(url):
    try:
        response = requests.head(url)
        if response.status_code == 200:
            return True
        else:
            return False
    except requests.exceptions.RequestException as e:
        print("An error occurred:", e)
        return False
tags:
  - Booking_admin
summary: Update Cancellation Charge
description: API to update the cancellation charge for a specific booking based on a new cancellation reason. The penalty is recalculated based on the new reason.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: cancel_id
    in: formData
    type: integer
    required: true
    description: The ID of the canceled booking for which the charge is being updated.
  - name: new_reason
    in: formData
    type: integer
    required: true
    description: The new reason for the cancellation that will be used to recalculate the charge.
responses:
  200:
    description: Success response indicating the cancellation charge was successfully updated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        updatedcharge:
          type: array
          items:
            type: integer
          example: [100, 75]  # Example updated penalty values for user and driver
  400:
    description: Bad request due to missing or incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: 'Incomplete Form Details'
  500:
    description: Internal server error indicating a failure while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: 'Internal Server Error'

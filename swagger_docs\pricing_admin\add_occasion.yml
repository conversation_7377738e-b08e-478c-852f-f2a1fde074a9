tags:
  - Pricing_admin
summary: Add Occasion Pricing
description: >
  This endpoint allows admins to add pricing information for a specific occasion and trip type in a particular city. 
  It requires the admin to be authorized and provides pricing details based on occasions and dates.
parameters:
  - name: city
    in: formData
    required: true
    type: string
    description: The name of the city for which occasion pricing is being added.
    example: "Los Angeles"
  - name: trip_type
    in: formData
    required: true
    type: string
    description: The type of trip (inCity or outStation).
    example: "inCity"
  - name: occasion
    in: formData
    required: true
    type: string
    description: The name of the occasion being added.
    example: "New Year"
  - name: occasion_dates
    in: formData
    required: true
    type: string
    description: Comma-separated dates for the occasion.
    example: "2024-01-01,2024-01-02"
  - name: night_charge
    in: formData
    required: false
    type: number
    description: The charge for night trips (only for inCity trips).
    example: 50.00
  - name: part_night_charge
    in: formData
    required: false
    type: number
    description: The charge for part night trips (only for inCity trips).
    example: 25.00
  - name: min_travel_cost
    in: formData
    required: false
    type: number
    description: The minimum travel cost (only for inCity trips).
    example: 100.00
  - name: travel_cost
    in: formData
    required: false
    type: number
    description: The standard travel cost (only for inCity trips).
    example: 200.00
  - name: base_fare
    in: formData
    required: false
    type: number
    description: The base fare for the trip.
    example: 150.00
  - name: add_fare
    in: formData
    required: false
    type: number
    description: Any additional fare for the trip.
    example: 20.00
  - name: minios_base_fare
    in: formData
    required: false
    type: number
    description: The base fare for MiniOS trips (only for inCity trips).
    example: 100.00
  - name: minios_add_fare
    in: formData
    required: false
    type: number
    description: Any additional fare for MiniOS trips (only for inCity trips).
    example: 15.00
  - name: booking_percent
    in: formData
    required: false
    type: number
    description: The percentage charge for bookings.
    example: 10.00
  - name: oneway_fare
    in: formData
    required: false
    type: number
    description: The fare for one-way trips.
    example: 80.00
  - name: first_overtime
    in: formData
    required: false
    type: number
    description: The duration for the first overtime.
    example: 30
  - name: second_overtime
    in: formData
    required: false
    type: number
    description: The duration for the second overtime.
    example: 60
  - name: first_overtime_charge
    in: formData
    required: false
    type: number
    description: The charge for the first overtime.
    example: 25.00
  - name: second_overtime_charge
    in: formData
    required: false
    type: number
    description: The charge for the second overtime.
    example: 50.00
  - name: add_overtime_charge
    in: formData
    required: false
    type: number
    description: Additional charge for overtimes.
    example: 10.00
  - name: hatch_man
    in: formData
    required: false
    type: number
    description: The fare for hatchback cars (manual).
    example: 200.00
  - name: sedan_man
    in: formData
    required: false
    type: number
    description: The fare for sedan cars (manual).
    example: 250.00
  - name: suv_man
    in: formData
    required: false
    type: number
    description: The fare for SUVs (manual).
    example: 300.00
  - name: lux_man
    in: formData
    required: false
    type: number
    description: The fare for luxury cars (manual).
    example: 350.00
  - name: hatch_auto
    in: formData
    required: false
    type: number
    description: The fare for hatchback cars (automatic).
    example: 220.00
  - name: sedan_auto
    in: formData
    required: false
    type: number
    description: The fare for sedan cars (automatic).
    example: 270.00
  - name: suv_auto
    in: formData
    required: false
    type: number
    description: The fare for SUVs (automatic).
    example: 320.00
  - name: lux_auto
    in: formData
    required: false
    type: number
    description: The fare for luxury cars (automatic).
    example: 370.00
responses:
  200:
    description: Successfully added occasion pricing details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        msg:
          type: string
          description: Success message
          example: "Occasion data added successfully for New Year in city Los Angeles"
  400:
    description: Request failed due to missing or invalid parameters
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        msg:
          type: string
          description: Error message
          example: "Required data missing."
  401:
    description: Unauthorized access
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  500:
    description: Failed to add occasion pricing due to internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0

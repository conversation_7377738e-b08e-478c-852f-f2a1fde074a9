tags:
  - Authentication
summary: User Logout
description: >
  This endpoint logs the user out by invalidating the JWT tokens and clearing the cookies associated with the user session, such as restore_id, name, and user_id.
responses:
  200:
    description: User successfully logged out. All relevant cookies cleared.
    examples:
      logout: true
  401:
    description: Unauthorized - JW<PERSON> token is invalid or expired
    examples:
      error: "Invalid or expired token"
  500:
    description: Internal server error
    examples:
      error: "Internal server error"

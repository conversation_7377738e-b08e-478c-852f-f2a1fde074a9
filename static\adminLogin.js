$(document).ready(function () {
    //focus to phone number on load
    $("#mobile").focus();
	//Password visibility functionality.................................................................
    $(".input-group-addon").click(function() {
        var toggle = $(this).find("i").attr('class');
        if(toggle=="glyphicon glyphicon-eye-open") {
            $(this).parent().find("input").attr('type','text');
            $(this).find("i").attr('class','glyphicon glyphicon-eye-close');
        }
        else {
            $(this).parent().find("input").attr('type','password');
            $(this).find("i").attr('class','glyphicon glyphicon-eye-open');
        }
    });

    //enter key triggers submit
    $("#mobile").keyup(function(e) {
        if(e.which == 13) $("#login").trigger('click');
    });
    $("#pwd").keyup(function(e) {
        if(e.which == 13) $("#login").trigger('click');
    });
    //Login form Submit click....................................................................................
    $("#login").click(function (e) {
        $("#mobile").focus();
    	e.preventDefault();
    	var mobile = $("#mobile").val().trim();
    	var password = $("#pwd").val();
    	var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");			//regex for phone validation
    	var validData = true;

    	//phone validation
    	if (mobile == "") {
          	$("#mobile").css('border','2px solid red');			//error indication
          	//error popover
          	$("#mobile").popover({
            	placement: "bottom",
            	trigger: "hover"
          	});
          	$('#mobile').data('bs.popover').options.content = "This field cannot be blank";
          	validData = false;
      	}

      	else if (!phoneRegex.test(mobile)) {
            $("#mobile").css('border','2px solid red');			//error indication
            //error popover
            $("#mobile").popover({
            	placement: "bottom",
            	trigger: "hover"
          	});
          	$('#mobile').data('bs.popover').options.content = "Enter correct mobile number without country-code";
          	validData = false;
        }

        else {
        	$("#mobile").popover('destroy');
        	validData = true;
        }

        //password validation
        if(password.length < 6) {
        	$("#pwd").css('border','2px solid red');			//error indication
            //error popover
            $("#pwd").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#pwd').data('bs.popover').options.content = "Password is 6 characters or longer";
            validData = false;
        }

        else {
        	$("#pwd").popover('destroy');
        	validData = true;
        }

        //send login data
        if (validData) {

            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/token/login/admin',
                data: {
                    mobile: mobile,
                    pwd: password,
                    remember: 'true'
                },
                dataType: "json",
                success: function(e) {
                    var msg = JSON.stringify(e);
                    /*var json=$.parseJSON(msg);
                    alert(json);*/

                    if (e.success == 1)
                    {
                    	setCookie("name", e.user_fname + " " + e.user_lname, "phone", e.user_mobile, 365, e.region);
                       	window.location = window.location.protocol + '//' + window.location.host + '/admin';
                    }
                    else {//some handler must be here
                    }

                },
                error: function(e) {
                    if(e.status == 401) {
                    	//Incorrect Credentials...............................................................
                    	//change background
                    	$("#infoModal").find(".modal-header").css('background','salmon');
                    	//change text
                    	$("#infoModal").find(".modal-title").html("Incorrect Credentials. <br> Either Phone number or password is wrong.");
				        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
				        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    }

                }
            });
        }
    });

    //cookie related code...............................................................................
    function getCookie() {
        cookieSplit = document.cookie.split("; ");
        var cookieObject = {};
        cookieSplit.forEach( function(value, index) {
           var splitResult = value.split("=");
           cookieObject[splitResult[0]] = splitResult[1];
        });
        return cookieObject;
    }
    function setCookie(propName,nameVal,propPhone,phoneVal,exDays,region) {
        if(typeof exDays === "undefined") exDays = 0;
        var d = new Date();
        d.setDate(d.getDate() + parseInt(exDays));
        var expires = "expires=" + d;
        document.cookie = propName + "=" + nameVal + "; " + expires;
        document.cookie = propPhone + "=" + phoneVal + "; " + expires;;
        document.cookie = expires;
        document.cookie = "path=/" + "; " + expires;;
        document.cookie = "region=" + region  + "; " + expires;
    }
    var cookie = getCookie();
    //alert(JSON.stringify(cookie));
    var refresh = cookie['csrf_refresh_token'];
    var access = cookie['csrf_access_token'];
    if (access && refresh) {
        var ans = checkRefresh(access, refresh);
        if (ans) {
            window.location = window.location.protocol + '//' + window.location.host + '/admin';
        }
    }

    function checkRefresh(csrf_token, refresh_token) {
        /*
        var response = false;
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/token/verify',
            beforeSend: function(request) {
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            async: false,
            success: function(s) {
                if (s.success != 1) {
                    $.ajax({
                        type: "POST",
                        url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                        beforeSend: function(request) {
                            request.setRequestHeader('X-CSRF-Token', refresh_token);
                        },
                        async: false,
                        success: function(sr) {
                            //alert(JSON.stringify(sr))
                            if (sr.refresh != true) response = false;
                            else response =  true;
                        },
                        error: function(er) {
                            response =  false;
                        }
                    });
                } else {
                    response =  true;
                }
            },
            error: function(e) {
                if (e.status == 401) {
                    $.ajax({
                        type: "POST",
                        url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                        beforeSend: function(request) {
                            request.setRequestHeader('X-CSRF-Token', refresh_token);
                        },
                        async: false,
                        success: function(sr) {
                            //alert(JSON.stringify(sr))
                            if (sr.refresh != true) response =  false;
                            else response =  true;
                        },
                        error: function(err) {
                            //alert("huh");
                            response = false;
                        }
                    });
                } else response =  false;
            }
        });
        return response;
        */
    }

    //Code for reset from erroneous fields..........................................................
    $("#mobile").click(function() {
      $(this).css('border','');
    });
    $("#pwd").click(function() {
      $(this).css('border','');
    });
});
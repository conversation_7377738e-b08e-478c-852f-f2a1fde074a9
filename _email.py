from O365 import Account
from config import BaseConfig

def _get_account(address):
    credentials = (BaseConfig.MAIL_CLIENT_ID, BaseConfig.MAIL_CLIENT_SECRET)
    account = Account(credentials, auth_flow_type='credentials',
                      tenant_id=BaseConfig.MAIL_TENANT, main_resource=address)
    account.authenticate()
    return account

def get_mailbox(address):
    account = _get_account(address)
    mailbox = account.mailbox()
    return mailbox


def send_mail(address, to_list, subject, content, attachment=None):
    account = _get_account(address)
    new_msg = account.new_message()
    for to_mail in to_list:
        new_msg.to.add(to_mail)
    new_msg.body = content
    new_msg.subject = subject
    if attachment:
        new_msg.attachments.add(attachment)
    return new_msg.send()

def send_mail_v2(address, to_list, subject, content, attachment_list=None):
    account = _get_account(address)
    new_msg = account.new_message()
    for to_mail in to_list:
        new_msg.to.add(to_mail)
    new_msg.body = content
    new_msg.subject = subject
    if attachment_list:
        for attachment in attachment_list:
            new_msg.attachments.add(attachment)
    return new_msg.send()

# cc and bcc supported
def send_mail_v3(address, to_list, subject, content, cc=[],bcc=[],attachment_list=None):
    account = _get_account(address)
    new_msg = account.new_message()
    for to_mail in to_list:
        new_msg.to.add(to_mail)
    for cc_mail in cc:
        new_msg.cc.add(cc_mail)
    for bcc_mail in bcc:
        new_msg.bcc.add(bcc_mail)
    new_msg.body = content
    new_msg.subject = subject
    if attachment_list:
        for attachment in attachment_list:
            new_msg.attachments.add(attachment)
    return new_msg.send()


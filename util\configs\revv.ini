[general]
filename = revv.csv
query = SELECT revv_v2_book_id as revv_v2_id, revv_v2_appt_id as apptId,revv_v2_veh_reg as carNo, CASE when book_region = 0 THEN "Kolkata" when book_region = 6 THEN "Delhi" when book_region=2 THEN "Guwahati" ELSE "Kolkata" END, CASE revv_v2_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, date(addtime(trip_start,"05:30:00")) as date, time(addtime(trip_start,"05:30:00")) as start_time, time(addtime(trip_stop,"05:30:00")) as stop_time, timediff(trip_stop,trip_start) as dur, greatest("00:00:00", timediff(timediff(trip_stop,trip_start),"02:00:00")) as ot, concat(user_fname, concat(' ', user_lname)) as driver, book_loc_name as start_loc, dest_book_name as stop_loc, book_comment as d4m_comment from bookings, trip, revv_v2_bookings, drivers, users, book_dest where book_ref=revv_v2_book_ref and book_ref=trip_book and book_driver=driver_id and driver_user=user_id and dest_book_id=book_ref and month(book_startdate)=11 and year(book_startdate)=2022;

query = SELECT revv_booking_id, revv_booking_region, book_startdate as date, CONCAT(user_fname, CONCAT(" ", user_lname)), CASE revv_booking_shift WHEN 0 THEN "DAY" ELSE "NIGHT" END, DATE_FORMAT( addtime(trip_start, "05:30:00"), "%%%%m/%%%%d/%%%%Y %%%%H:%%%%i:%%%%s" ), DATE_FORMAT( addtime(trip_stop, "05:30:00"), "%%%%m/%%%%d/%%%%Y %%%%H:%%%%i:%%%%s" ), CEILING(TIMESTAMPDIFF(minute, trip_start, trip_stop)/60) as hours, trip_price, book_comment from bookings, trip, users, drivers, revv_bookings where trip_book = book_ref and book_driver = driver_id and driver_user = user_id and book_ref = revv_book_ref and trip_stop > trip_start and addtime(trip_start, "05:30:00") > '2020-02-31' and addtime(trip_start, "05:30:00") < '2020-04-01' order by trip_start;
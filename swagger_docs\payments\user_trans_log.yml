tags:
  - Payments
summary: Retrieve the user's transaction log.
description: >
  This API fetches a log of all completed transactions for the authenticated user, excluding transactions with zero amounts.
responses:
  200_a:
    description: User transaction log retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        balance:
          type: number
          format: float
          example: 120.50
        data:
          type: array
          items:
            type: string
          example: [
              "{amt:120.50,dt:2024-09-10 14:35:00}",
              "{amt:-50.00,dt:2024-09-05 09:10:00}"
          ]
    examples:
      application/json:
        success: 1
        balance: 120.50
        data:
          - "{amt:120.50,dt:2024-09-10 14:35:00}"
          - "{amt:-50.00,dt:2024-09-05 09:10:00}"
  200_b:
    description: No transactions found for the user.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User transaction not found"
    examples:
      application/json:
        success: -1
        message: "User transaction not found"
  401:
    description: User account restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: User not found or internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User not found"
    examples:
      application/json:
        success: -1
        message: "User not found"

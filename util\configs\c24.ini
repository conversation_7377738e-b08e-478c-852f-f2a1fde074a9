[general]
filename = c24.csv
query = SELECT ctf_book_id as c24_id, ctf_appt_id as apptId, CASE ctf_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, ctf_book_ref as d4m_id, date(addtime(trip_start,"05:30:00")) as date, time(addtime(trip_start,"05:30:00")) as start, time(addtime(trip_stop,"05:30:00")) as stop, timediff(trip_stop,trip_start) as dur, greatest("00:00:00", timediff(timediff(trip_stop,trip_start),"02:00:00")) as ot, trip_price as price, concat(user_fname, concat(' ', user_lname)) as driver, book_loc_name as start_loc, dest_book_name as stop_loc, ctf_comment as c24_comment, book_comment as d4m_comment,substring(ctf_veh_reg,-4) as carNo from bookings, trip, c24_bookings, drivers, users, book_dest where book_ref=ctf_book_ref and book_ref=trip_book and book_driver=driver_id and driver_user=user_id and dest_book_id=book_ref and month(book_startdate) in (1) and year(book_startdate)=2021;
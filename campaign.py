#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  register_cust.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON> Mi<PERSON>


from flask import Blueprint, request, render_template, jsonify, redirect
import _sms
import re
from numpy import base_repr
from _utils import get_safe, complete

from _utils import complete, validate_fields
from models import DownloadCampaign, DownloadReq
from models import db

campaign = Blueprint('campaign', __name__)

def _add_download(campaign_id, platform, redirect_target=""):
    dl = DownloadReq(campaign_id, redirect_target, platform)
    try:
        db.session.add(dl)
        db.session.commit()
    except ValueError as e:
        print(e)
        db.session.rollback()
        return False
    return True

def _create_campaign_if_not_exists(campaign_str, campaign_info=""):
    db_campaign = db.session.query(DownloadCampaign). \
            filter(DownloadCampaign.campaign_str == campaign_str).first()
    if db_campaign:
        return db_campaign.id
    dc = DownloadCampaign(campaign_str, campaign_info)
    try:
        db.session.add(dc)
        db.session.commit()
    except Exception:
        db.session.rollback()
        return -1
    return dc.id

@campaign.route('/download')
def download_app():
    campaign = str(request.args.get('campaign')).upper()
    campaign_info = "Created automatically"
    campaign_id = _create_campaign_if_not_exists(campaign, campaign_info)
    if campaign_id <= 0:
        campaign_id = 1
    user_agent_string = request.user_agent.string.lower()
    if "iphone" in user_agent_string or "ipad" in user_agent_string:
        redirect_target = "Apple App Store"
        redirect_url = "itms-apps://apps.apple.com/in/app/drivers4me/id1481211328"
    elif "android" in user_agent_string:
        redirect_target = "Google Play Store"
        redirect_url = "https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.drivers4me"
    else:
        redirect_target = "Google Play Store (Website)"
        redirect_url = "https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.drivers4me"
    _add_download(campaign_id, str(request.headers.get('User-Agent')), redirect_target)
    return redirect(redirect_url)

@campaign.route('/api/ref_msg', methods=['GET', 'POST'])
def send_ref():
    mobile = get_safe(request.form, 'mobile', 0).strip()
    if not re.search(r"^[6789]\d{9}$", mobile):
        return jsonify({'success': -1})
    else:
        # Campaign should already exist
        if _sms.send_msg_flow(mobile, _sms.FLOWS["landing-sms-ref"], {}):
            return jsonify({'success': 1})
        else:
            return jsonify({'success': -2})
    return jsonify({'success': -3})

@campaign.route('/api/sr_msg', methods=['GET', 'POST'])
def sr_ref():
    mobile = get_safe(request.form, 'caller_id', 0)
    mobile = mobile[3:]
    print(mobile)
    if not re.search(r"^[6789]\d{9}$", mobile):
        return jsonify({'success': -1})
    else:
        # Campaign should already exist
        if _sms.send_msg_flow(mobile, _sms.FLOWS["landing-sms-ref"], {}):
            return jsonify({'success': 1})
        else:
            return jsonify({'success': -2})
    return jsonify({'success': -3})
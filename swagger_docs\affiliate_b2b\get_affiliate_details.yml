tags:
  - Affiliate_B2B
summary: Get Affiliate Details
description: >
  Fetches the basic or schema details of an affiliate based on the provided type.
parameters:
  - name: affiliate_id
    in: body
    type: integer
    required: true
    description: The ID of the affiliate.
  - name: type
    in: body
    type: integer
    required: true
    description: >
      The type of data to retrieve:
      - `1` for basic details
      - `2` for schema details
responses:
  200:
    description: Successfully retrieved affiliate details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        schema:
          type: object
          properties:
            client_name:
              type: string
              example: ABC Corp
            trip_type:
              type: array
              items:
                type: string
              example: ["oneway", "round"]
            tripTypeLabel:
              type: string
              example: Trip Type
            tripTypePlaceholder:
              type: string
              example: Select a trip type
            form_field_oneway:
              type: object
              example: {"field1": "value1", "field2": "value2"}
            form_field_round:
              type: object
              example: {"fieldA": "valueA", "fieldB": "valueB"}
            client_region:
              type: string
              example: North
        basic_data:
          type: object
          properties:
            city_region:
              type: string
              example: North
            client_name:
              type: string
              example: ABC Corp
            display_name:
              type: string
              example: ABC Affiliates
            wallet:
              type: number
              example: 5000.00
            client_logo:
              type: string
              example: "https://example.com/logo.png"
  400:
    description: Incomplete form data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Incomplete form.
        form_data:
          type: object
          additionalProperties:
            type: string
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Unauthorized.
  404:
    description: Resource not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: An error occurred.

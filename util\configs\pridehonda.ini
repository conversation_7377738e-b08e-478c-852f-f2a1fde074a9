[general]
filename = pridehonda.csv
query = SELECT ph_book_id as pridehonda_id, book_ref, ph_appt_id as apptId,ph_veh_reg as carNo, CASE when book_region = 1 THEN "Hyderabad" when book_region = 6 THEN "Delhi" when book_region=2 THEN "Guwahati" ELSE "Kolkata" END, CASE ph_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, date(addtime(trip_start,"05:30:00")) as date, time(addtime(trip_start,"05:30:00")) as start_time, time(addtime(trip_stop,"05:30:00")) as stop_time, timediff(trip_stop,trip_start) as dur, greatest("00:00:00", timediff(timediff(trip_stop,trip_start),"02:00:00")) as ot, concat(user_fname, concat(' ', user_lname)) as driver, book_loc_name as start_loc, dest_book_name as stop_loc, ph_dist, CASE when ph_dist>30 THEN ph_dist-30 ELSE 0 END, book_comment as d4m_comment from bookings, trip, pridehonda_bookings, drivers, users, book_dest where book_ref=ph_book_ref and book_ref=trip_book and book_driver=driver_id and driver_user=user_id and dest_book_id=book_ref and month(date(addtime(trip_start,"05:30:00")))=MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and year(date(addtime(trip_start,"05:30:00")))=YEAR(CURRENT_DATE - INTERVAL 1 MONTH);
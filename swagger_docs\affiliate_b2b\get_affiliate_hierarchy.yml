tags:
  - Affiliate_B2B
summary: Get Affiliate Hierarchy
description: >
  Fetches the affiliate hierarchy for the logged-in representative.
parameters: []
responses:
  200:
    description: Successfully retrieved affiliate hierarchy.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        hierarchy:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: "123"
              client_name:
                type: string
                example: Self
        basic_data:
          type: object
          properties:
            client_region:
              type: string
              example: North
            client_name:
              type: string
              example: ABC Corp
            display_name:
              type: string
              example: ABC Affiliates
            logo:
              type: string
              example: "https://example.com/logo.png"
            wallet:
              type: number
              example: 5000.00
              description: Wallet balance in rupees.
            rep_mobile:
              type: string
              example: 9876543210
            rep_username:
              type: string
              example: <PERSON><PERSON><PERSON>
            rep_email:
              type: string
              example: <EMAIL>
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Unauthorized.
  404:
    description: Affiliate or representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        msg:
          type: string
          example: Affiliate representative not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: An error occurred.

# -*- coding: utf-8 -*-
#
#  admin.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from _utils_acc import account_enabled, validate_role
from _utils import complete, get_dt_ist
from models import db, Users, UserTrans

from datetime import datetime, timedelta
import pytz
import sys
from functools import wraps
sys.path.insert(1, '/app')
import random, time, re
import jsonpickle
from flask import Blueprint, request, render_template, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt, get_jwt_identity
)
from affiliate_api.hook.utils import (
    ZOOMCAR_STATE_CREATED, ZOOMCAR_STATE_ASSIGNED, ZOOMCAR_STATE_ACCEPTED,
    ZOOMCAR_STATE_REJECTED, ZOOMCAR_STATE_CANCELED,
    _zoomcar_change_state
)
from _fcm import send_fcm_msg
import json, math, os
from flask import current_app as app
from redis_config import execute_with_fallback
from _ops_message import send_slack_msg
from trip import convert_to_semihours, convert_timedelta
import _sms
from socketio_app import live_update_to_channel
from users import user_display, _switch_payment
from c24 import c24_stop_trip, c24_start_trip, _c24_newstop_trip, _c24_restart_trip
from olx import olx_stop_trip, olx_start_trip, _olx_newstop_trip, _olx_restart_trip
from zoomcar import zoomcar_stop_trip, zoomcar_start_trip, _zoomcar_newstop_trip, _zoomcar_restart_trip
from cardekho import cardekho_stop_trip, cardekho_start_trip, _cardekho_newstop_trip, _cardekho_restart_trip
from bhandari import bhandari_stop_trip, bhandari_start_trip, _bhandari_newstop_trip, _bhandari_restart_trip
from mahindra import mahindra_stop_trip, mahindra_start_trip, _mahindra_newstop_trip, _mahindra_restart_trip
from pridehonda import pridehonda_stop_trip, pridehonda_start_trip, _pridehonda_newstop_trip, _pridehonda_restart_trip
from spinny import spinny_stop_trip, spinny_start_trip, _spinny_newstop_trip, _spinny_restart_trip
# from revv_v2 import  revv_v2_start_trip, _revv_v2_newstop_trip, _revv_v2_restart_trip
import html
from cluster_loc import LocStr
from _rtdb import _update_user_pending, _update_driver_pending
from _fcm import send_fcm_msg_driver
from sqlalchemy import exc
from sqlalchemy.sql import func, or_,desc,and_
from _utils import complete, get_pic_url, strfdelta, get_safe, get_dt_ist, upload_pic, is_booking_today, compute_driver_wallet, is_trip_today
from _utils_booking import get_book_code, fetch_booking_trip, _mark_zoomcar_booking_accepted, convert_booking_type_b2c
from trip import _stop_trip, _start_trip, _restart_trip,change_trip_state
from _utils_acc import account_enabled, validate_role, get_driver_name_from_id, get_user_name_from_id
from booking_params import normalize_distance, BookingParams, Rating, Regions
from admin_params import AdminParams
from _utils_booking import get_car_type, booking_has_trip, _get_zoomcar_booking_state
from price import Price, PriceOutstation
from revv import _revv_start_trip, _revv_stop_trip, _revv_newstop_trip, _revv_restart_trip
from gujral import _gujral_start_trip, _gujral_stop_trip, _gujral_newstop_trip, _gujral_restart_trip
from book_ride import _cancel_trip_driver, _unalloc_trip_driver, get_driver_static_score
from models import Users, Drivers, Bookings, Trip, DriverSearch, BookPending, DriverDetails, DriverInfo, BookingAlloc, DriverCancelled, DriverInfo, DriverBank,DriverLoc, MobileChange,DeletedUser
from models import db, DriverPaid, AdminLog, BookDest, DriverPermaInfo, C24Bookings, C24Rep, RevvBookings, TripLog, BookPricing, BookingCancelled
from models import C24Pic, ZoomcarPic, OLXPic, CardekhoPic, BhandariPic, TripStartPic, TripEndPic, DriverTrans, UserCancelled
from models import MahindraPic, MahindraBookings, MahindraRep, RevvV2Pic, RevvV2Bookings, RevvV2Rep, SpinnyPic, SpinnyBookings, SpinnyRep
from models import PrideHondaPic, PrideHondaBookings, PrideHondaRep
from models import GujralBookings, OLXBookings, OLXRep, ZoomcarBookings, ZoomcarRep, UserTrans, BookingFeedback, CardekhoBookings, CardekhoRep, BhandariBookings, BhandariRep
from datetime import datetime, timedelta
from drivers import send_booking_confirm_sms
from _utils import distance_on_earth
import pytz
from db_config import fb_db
from google.cloud import firestore
from call_masking import assign_unique_did
from live_update_booking import send_live_update_of_booking
from gevent import spawn, sleep

admin = Blueprint('admin', __name__)
adminLogin = Blueprint('adminLogin', __name__)

class DriverData:

    def __init__(self, driver_id, mobile, fname, lname, licence, available, lat, lng, approved, addr, rating=5, due=0,
                 ontrip=False, perma=False, region=0, verif_phone="", lic_doc_front="", lic_doc_back="", id_doc_front="",
                 id_doc_back="", bank_doc="", pic="", bank_ac_number="", bank_ifsc_number="", color=0, score=25):
        self.driver_id = driver_id
        self.mobile = mobile
        self.fname = fname
        self.lname = lname
        self.licence = licence
        self.available = available
        self.lat = lat
        self.lng = lng
        self.approved = approved
        self.addr = addr
        self.rating = round(rating,2)
        self.inf_rating = round(user_display(rating),2)
        self.due = due
        self.ontrip = ontrip
        self.region = region
        self.perma = int(perma)
        self.verif_phone = verif_phone
        self.lic_doc_front = lic_doc_front
        self.lic_doc_back = lic_doc_back
        self.id_doc_front = id_doc_front
        self.id_doc_back = id_doc_back
        self.bank_doc = bank_doc
        self.pic = pic
        self.bank_ac_number = bank_ac_number
        self.bank_ifsc_number = bank_ifsc_number
        self.color = int(color)
        self.score = round(score, 2)


class PermaDriverData(DriverData):
    def __init__(self, driver_id, mobile, fname, lname, licence, available, lat, lng, approved, addr, ta, ot, base, alloc, rating=5, due=0,
                 ontrip=False, perma=False, region=0, lic_doc_front="", pic=""):
        super().__init__(driver_id=driver_id, mobile=mobile, fname=fname, lname=lname, licence=licence, available=available, lat=lat,
                            lng=lng, approved=approved, addr=addr, rating=rating, due=due, ontrip=ontrip, perma=perma, region=region,
                            lic_doc_front=lic_doc_front, pic=pic)
        self.ta = ta
        self.ot = ot
        self.base = base
        self.alloc = alloc


class SearchData:

    def __init__(self, searchkey, cname, cmob, startdate, starttime, dur, lat, lng, car_type, region, timestamp):
        self.search_id = searchkey
        self.cname = cname
        self.cmob = cmob
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.dur = str(dur)
        self.lat = lat
        self.lng = lng
        self.car_type = car_type
        self.region = region
        self.timestamp = timestamp


class BookingData:
    def __init__(self, bookId, bookCode, tripId, did, dname, dmob, dowed, cid, cname, cmob, startdate, starttime, dur, days, booking_type,
                 trip_start, trip_stop, car_type_no,
                 estimate, price, lat, lng, cancelled, loc_name, car_type, destlat, destlng, destname, comment, suppressed, due=0,
                 payment=0, region=0, c_color=0, d_color=0, f_color=0, m_color=0, cash=0, cred=0, insurance=0, dist=0, trip_status=0,
                 otp=0, zc_comment="", user_label=0, driver_label=0):
        self.bookId = bookId
        self.code = bookCode
        self.tripId = tripId
        self.did = did
        self.dname = dname
        self.dmob = dmob
        self.dowed = dowed
        self.cid = cid
        self.cname = cname
        self.cmob = cmob
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.dur = str(dur)
        self.days = days
        self.car_type_no = car_type_no
        self.booking_type = booking_type
        self.trip_start = str(trip_start)
        self.trip_stop = str(trip_stop)
        self.estimate = round(float(estimate), 2)
        self.price = price
        self.lat = lat
        self.lng = lng
        self.cancelled = cancelled
        self.loc_name = loc_name
        self.car_type = car_type
        self.destlat = destlat
        self.destlng = destlng
        self.destname = destname
        self.suppressed = suppressed
        self.due = due
        self.comment = comment
        self.payment_type = payment
        self.region = region
        self.c_color = c_color
        self.d_color = d_color
        self.f_color = f_color
        self.m_color = m_color
        self.cash = int(cash)
        self.cred = int(cred)
        self.insurance = int(insurance)
        self.dist = dist
        self.trip_status = int(trip_status)
        self.otp = otp
        self.zc_comment = str(zc_comment)
        self.user_label = int(user_label)
        self.driver_label = int(driver_label)

class BookingCancel:
    def __init__(self, cid, cname, dname, cmob, dmob, dowed, booktime, startdate, starttime, car_type, lat, lng, dur, cancelled, region=0):
        self.cid = cid
        self.cname = cname
        self.dname = dname
        self.cmob = cmob
        self.dmob = dmob
        self.dowed = dowed
        self.booktime = str(booktime)
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.car_type = car_type
        self.lat = lat
        self.lng = lng
        self.dur = str(dur)
        self.cancelled = cancelled
        self.region = region


class PendingDriver:
    def __init__(self, dname, dmob, estimate, driver_id, suppressed):
        self.dname = dname
        self.dmob = dmob
        self.estimate = estimate
        self.driver_id = driver_id
        self.suppressed = suppressed


# Analytics classes
class MonthlySales:
    def __init__(self, month, year, sales):
        self.month = month
        self.year = year
        self.sales = round(float(sales), 2)


class YearlySales:
    def __init__(self, year, sales):
        self.year = year
        self.sales = round(float(sales), 2)


class WeeklySales:
    def __init__(self, week, year, sales):
        self.week = week
        self.year = year
        self.sales = round(float(sales), 2)


class DailySales:
    def __init__(self, day, sales):
        self.day = day
        self.sales = round(float(sales), 2)


class MonthlyTrips:
    def __init__(self, month, year, trips):
        self.month = month
        self.year = year
        self.trips = trips


class YearlyTrips:
    def __init__(self, year, trips):
        self.year = year
        self.trips = trips


class WeeklyTrips:
    def __init__(self, week, year, trips):
        self.week = week
        self.year = year
        self.trips = trips


class DailyTrips:
    def __init__(self, day, trips):
        self.day = day
        self.trips = trips


class MonthlyRevenues:
    def __init__(self, month, year, revenues):
        self.month = month
        self.year = year
        self.revenues = round(float(revenues), 2)


class YearlyRevenues:
    def __init__(self, year, revenues):
        self.year = year
        self.revenues = round(float(revenues), 2)


class WeeklyRevenues:
    def __init__(self, week, year, revenues):
        self.week = week
        self.year = year
        self.revenues = round(float(revenues), 2)


class DailyRevenues:
    def __init__(self, day, revenues):
        self.day = day
        self.revenues = round(float(revenues), 2)

def get_booking_type(type_info):
    return type_info

def convert_to_ist(dt):
    if dt is None:
        return None
    ist = pytz.timezone('Asia/Kolkata')
    return dt.astimezone(ist)

def combine_and_convert_to_ist(date_val, time_val):
    combined = datetime.combine(date_val, time_val)
    return convert_to_ist(combined) # timedelta(hours=5, minutes=30)

def split_date_time(dt):
    return dt.date(), dt.time()

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        current_user = get_jwt_identity()
        user = db.session.query(Users).filter(Users.id == current_user).first()
        if not user or user.role not in [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]:
            return jsonify({'success': -1, 'message': 'Unauthorized. Admin privileges required.'}), 401
        return f(*args, **kwargs)
    return decorated_function

def get_ist_time(utc_time):
    utc_zone = pytz.timezone('UTC')
    ist_zone = pytz.timezone('Asia/Kolkata')
    utc_time = utc_zone.localize(utc_time)
    ist_time = utc_time.astimezone(ist_zone)
    return ist_time.strftime('%Y-%m-%d %H:%M:%S')

def extract_booking_id(method):
    match = re.search(r'#(\w+)', method)
    return match.group(1) if match else ''

def get_method_summary(method):
    words = method.split()
    if len(words) > 2:
        return ' '.join(words[:2])
    return words[0]

@adminLogin.route('/adminLogin', methods=['GET', 'POST'])
def adminLogin_page():
    return render_template('adminLogin.html')


@admin.route('/admin', methods=['GET', 'POST'])
@jwt_required()
def adminpage():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    superuser = int(claims['roles'] == Users.ROLE_SUPERADMIN)
    return render_template('admin.html', superuser=superuser)

@admin.route('/adminSuper', methods=['GET', 'POST'])
@jwt_required()
def adminsuperpage():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    superuser = int(claims['roles'] == Users.ROLE_SUPERADMIN)
    return render_template('admin1.html', superuser=superuser)


@admin.route('/api/admin_search', methods=['POST'])
@jwt_required()
def search():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    driver = db.session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.approved <= 0).filter(
        Drivers.approved > -2).all()
    if len(driver) == 0:
        return jsonify({'driver_id': -1}), 200
    response = random.choice(driver)
    return jsonify({'driver_id': response[0].id, 'driver_name': response[1].get_name(),
                    'driver_license': response[0].licenseNo,
                    'driver_license_doc': get_pic_url(response[0].licenseDoc).split('/static/')[1],
                    'driver_pic': get_pic_url(response[0].pic).split('/static/')[1]}), 200


@admin.route('/api/admin_appr', methods=['POST'])
@jwt_required()
def approve():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    did = 0
    try:
        did = request.form['driver_id']
    except Exception:
        return jsonify({'error': 1}), 201
    if did != 0:
        try:
            driver = Drivers.query.filter(Drivers.id == did).filter(Drivers.approved < 1).update({Drivers.approved: 1})
            admin_log = AdminLog(get_jwt_identity(), 'driver-appr', json.dumps(str(request.form)))
            db.session.add(admin_log)
            db.session.commit()
            return jsonify({'error': 0}), 200
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify({'error': 1}), 201
    else:
        return jsonify({'error': 1}), 201

@admin.route('/api/admin/cancelled', methods=['POST'])
@jwt_required()
def cancel_list():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401

    try:
        cancels = db.session.query(Bookings).filter(Bookings.valid < 0).order_by(Bookings.timestamp.desc()).all()
    except Exception:
        return jsonify({'success': -1}), 201
    result_json = []
    for cancel in cancels:
        car_type = get_car_type(cancel.id)
        cust = db.session.query(Users).filter(Users.id == cancel.user).first()
        if cancel.driver != 1:
            driver = db.session.query(Drivers, Users).filter(Drivers.id == cancel.driver). \
                filter(Drivers.user == Users.id).first()
            cancel_data = BookingCancel(cid=cancel.user,
                                        cname=cust.get_name(),
                                        dname=driver[1].get_name(),
                                        cmob=cust.mobile,
                                        dmob=driver[1].mobile,
                                        booktime=cancel.timestamp,
                                        startdate=cancel.startdate,
                                        starttime=cancel.starttime,
                                        car_type=car_type,
                                        lat=cancel.lat,
                                        lng=cancel.long,
                                        dur=cancel.dur,
                                        cancelled=cancel.valid + 2,
                                        region=cancel.region)
        else:
            cancel_data = BookingCancel(cid=cancel.user,
                                        cname=cust.get_name(),
                                        dname="",
                                        cmob=cust.mobile,
                                        dmob="",
                                        booktime=cancel.timestamp,
                                        startdate=cancel.startdate,
                                        starttime=cancel.starttime,
                                        car_type=car_type,
                                        lat=cancel.lat,
                                        lng=cancel.long,
                                        dur=cancel.dur,
                                        cancelled=cancel.valid + 2,
                                        region=cancel.region)
        result_json.append(jsonpickle.encode(cancel_data))
    if len(result_json) <= 0:
        return jsonify({'success': -1})
    else:
        return jsonify({'success': 1, 'data': result_json}), 200

@admin.route('/api/admin/book_ids', methods=['POST'])
@jwt_required()
def book_ids():
    claims = get_jwt()
    start_time = time.time()
    end_time = list()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    cur_index = -1
    region = int(get_safe(request.form, 'region', -1))
    trip_type = int(get_safe(request.form, 'trip_type', -1))
    search_cat = int(get_safe(request.form, 'search_cat', -1))
    search_name = (get_safe(request.form, 'search_name', ""))
    if trip_type == -1:
        type_list = [i for i in range(100)]
    elif trip_type < BookingParams.TYPE_C24:
        type_list = [i for i in range(BookingParams.TYPE_C24)]
    else:
        type_list = [trip_type]
    if region == -1:
        region_list = [i for i in range(len(Regions.REGN_NAME))]
    else:
        region_list = Regions.admin_view_regions(region)
    dt_q = request.form.get("date")
    slot_given = request.form.get("slot_given", False)
    if not dt_q:
        return jsonify({'booking_id_list': []})
    try:
        dt = datetime.strptime(dt_q, '%Y-%m-%d')
        if dt.date() > datetime.today().date():
            bookings = db.session.query(Bookings). \
                        filter(Bookings.startdate >= dt). \
                        filter(Bookings.region.in_(region_list)). \
                        filter(Bookings.type.in_(type_list)). \
                        order_by(Bookings.startdate.desc(), Bookings.starttime.desc(),
                        Bookings.id.desc())
        else:
            # [start_time, end_time) format
            st = "00:00:00"
            et = "24:00:00"
            try:
                if slot_given:
                    st = datetime.strptime(request.form['starttime'], '%H-%M-%S')
                    et = datetime.strptime(request.form['endtime'], '%H-%M-%S')
            except Exception:
                pass
            bookings = db.session.query(Bookings). \
                            filter(Bookings.startdate == dt). \
                            filter(Bookings.region.in_(region_list)). \
                            filter(Bookings.type.in_(type_list)). \
                            order_by(Bookings.startdate.desc(), Bookings.starttime.desc(),
                            Bookings.id.desc())
        if search_cat != -1 and search_name:
            split_txt = search_name.rsplit(' ', 1)
            fname = split_txt[0]
            if len(split_txt) == 2:
                lname = split_txt[1]
            if search_cat == 0:
                # customer
                user_acc = db.session.query(Users).filter(Users.fname.ilike('%' + fname + '%')).all()
                if len(split_txt) == 2:
                    user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + lname + '%')).all()
                else:
                    user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + fname + '%')).all()
                all_users = set(user_acc + user_acc_lname)
                all_users = [u.id for u in all_users]
                bookings = bookings.filter(Bookings.user.in_(all_users))
            else:
                # customer
                user_acc = db.session.query(Drivers, Users).filter(Users.fname.ilike('%' + fname + '%')).filter(Users.id == Drivers.user).all()
                if len(split_txt) == 2:
                    user_acc_lname = db.session.query(Drivers, Users).filter(Users.lname.ilike('%' + lname + '%')).filter(Users.id == Drivers.user).all()
                else:
                    user_acc_lname = db.session.query(Drivers, Users).filter(Users.lname.ilike('%' + fname + '%')).filter(Users.id == Drivers.user).all()
                all_users = set(user_acc + user_acc_lname)
                all_users = [u[0].id for u in all_users]
                bookings = bookings.filter(Bookings.driver.in_(all_users))
        bookings = bookings.all()
        return jsonify({'booking_id_list': [b.id for b in bookings]})
    except Exception as e:
        print(e)
        return jsonify({'booking_id_list': []}), 201

# load booking entries
@admin.route('/api/admin/booking', methods=['POST'])
@jwt_required()
def booking_list():
    claims = get_jwt()
    start_time = time.time()
    end_time = list()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    cur_index = -1
    if not complete(request.form, ['full_refresh']):
        full_refresh = True
    else:
        full_refresh = bool(int(request.form['full_refresh']))
    region = int(get_safe(request.form, 'region', -1))
    trip_type = int(get_safe(request.form, 'trip_type', -1))
    search_cat = int(get_safe(request.form, 'search_cat', -1))
    search_name = (get_safe(request.form, 'search_name', ""))
    if trip_type == -1:
        type_list = [i for i in range(100)]
    elif trip_type < BookingParams.TYPE_C24:
        type_list = [i for i in range(BookingParams.TYPE_C24)]
    else:
        type_list = [trip_type]
    if region == -1:
        region_list = [i for i in range(len(Regions.REGN_NAME))]
    else:
        region_list = Regions.admin_view_regions(region)
    end_time.append(time.time() - start_time)
    start_time = time.time()
    try:
        book_list = json.loads(request.form.get("booking_list"))
        cur_index = int(request.form['curIndex'])
    except Exception:
        book_list = None
        cur_index = -1
    if not book_list:
        if not full_refresh:
            try:
                today = datetime.today()
                if 'chunk_base' not in request.form:
                    to_comp = today.replace(day=1).date()
                    # load booking entries from current month
                    bookings = db.session.query(Bookings).filter(
                        Bookings.startdate >= to_comp
                    ).filter(Bookings.region.in_(region_list)).filter(Bookings.type.in_(type_list)).order_by(
                        Bookings.startdate.desc(), Bookings.starttime.desc(), Bookings.id.desc())
                else:
                    # load booking entries from current month
                    cur_index = int(request.form['curIndex'])
                    chunk_base = datetime.strptime(request.form['chunk_base'], '%d-%m-%Y %H:%M:%S')
                    chunk_maxoffset = int(request.form['chunk_maxoffset']) - 1
                    chunk_offset = int(request.form['chunk_offset'])
                    chunk = (chunk_base + timedelta(days=chunk_offset))
                    to_comp = chunk_base.replace(day=1).date()
                    if chunk_offset == -1:
                        bookings = db.session.query(Bookings).filter(
                            Bookings.startdate >= to_comp
                        ).filter(Bookings.region.in_(region_list)).filter(Bookings.type.in_(type_list)).filter(Bookings.startdate <= chunk).order_by(
                            Bookings.startdate.desc(), Bookings.starttime.desc(), Bookings.id.desc())
                    else:
                        to_comp = chunk.date()
                        if chunk_offset != chunk_maxoffset:
                            bookings = db.session.query(Bookings).filter(
                                    Bookings.startdate == to_comp
                                ).filter(Bookings.region.in_(region_list)).filter(Bookings.type.in_(type_list)).order_by(
                                    Bookings.startdate.desc(), Bookings.starttime.desc(), Bookings.id.desc())
                        else:
                            bookings = db.session.query(Bookings).filter(
                                    Bookings.startdate >= to_comp
                                ).filter(Bookings.region.in_(region_list)).filter(Bookings.type.in_(type_list)).order_by(
                                    Bookings.startdate.desc(), Bookings.starttime.desc(), Bookings.id.desc())
                if search_cat != -1 and search_name:
                    split_txt = search_name.rsplit(' ', 1)
                    fname = split_txt[0]
                    if len(split_txt) == 2:
                        lname = split_txt[1]
                    if search_cat == 0:
                        # customer
                        user_acc = db.session.query(Users).filter(Users.fname.ilike('%' + fname + '%')).all()
                        if len(split_txt) == 2:
                            user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + lname + '%')).all()
                        else:
                            user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + fname + '%')).all()
                        all_users = set(user_acc + user_acc_lname)
                        all_users = [u.id for u in all_users]
                        bookings = bookings.filter(Bookings.user.in_(all_users))
                    else:
                        # customer
                        user_acc = db.session.query(Drivers, Users).filter(Users.fname.ilike('%' + fname + '%')).filter(Users.id == Drivers.user).all()
                        if len(split_txt) == 2:
                            user_acc_lname = db.session.query(Drivers, Users).filter(Users.lname.ilike('%' + lname + '%')).filter(Users.id == Drivers.user).all()
                        else:
                            user_acc_lname = db.session.query(Drivers, Users).filter(Users.lname.ilike('%' + fname + '%')).filter(Users.id == Drivers.user).all()
                        all_users = set(user_acc + user_acc_lname)
                        all_users = [u[0].id for u in all_users]
                        bookings = bookings.filter(Bookings.driver.in_(all_users))
                bookings = bookings.all()

            except Exception as e:
                print(e)
                return jsonify({'success': -1, 'cur_index': cur_index}), 201
        else:
            # load all booking entries
            try:
                bookings = db.session.query(Bookings).filter(Bookings.region.in_(region_list)).filter(Bookings.type.in_(type_list)). \
                                    order_by(Bookings.startdate.desc(), Bookings.starttime.desc(),
                                                               Bookings.id.desc()).all()
                #db.session.close()
            except Exception as e:
                print(e)
                return jsonify({'success': -1, 'cur_index': cur_index}), 201
    else:
        bookings = [db.session.query(Bookings).filter(Bookings.id == b).first()
                    for b in book_list]
    end_time.append(time.time() - start_time)
    start_time = time.time()
    result_json = []
    user_trips = {}
    driver_trips = {1: False}
    all_booking_ids = [booking.id for booking in bookings]
    all_booking_trips = db.session.query(Trip).filter(Trip.book_id.in_(all_booking_ids)).all()
    trips_arr = {t.book_id: t for t in all_booking_trips}
    for booking in bookings:
        if search_cat != -1 and search_name:
            if search_cat == 0 and booking.user not in all_users:
                continue
            elif booking.driver not in all_users:
                continue
        trip = trips_arr.get(booking.id, None)
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        #db.session.close()
        payment_type = booking.payment_type
        # This is just too slow
        '''
        doc_ref = fb_db.collection(u'customer_comments').document(str(booking.user))
        try:
            doc_dict = doc_ref.get().to_dict()
            if str(booking.id) in doc_dict:
                comment = doc_dict[str(booking.id)]['comment']
                print(comment)
        except Exception:
            pass
        '''
        search_entry = db.session.query(DriverSearch).filter(booking.search_key == DriverSearch.id).first()
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        ##db.session.close()
        if not search_entry: continue
        otp = booking.otp
        if booking.driver != 1:
            var_due = Price.get_booking_ch(search_entry.car_type, booking.driver, booking.id)
        else:
            var_due = 0
        if trip is None:
            var_trip_id = ""
            var_trip_start = ""
            var_trip_stop = ""
            var_trip_price = ""
            trip_cash = 0
            trip_cred = 0
            trip_status = -1
        elif trip.starttime is None:
            var_trip_id = trip.id
            var_trip_start = ""
            var_trip_stop = ""
            var_trip_price = ""
            trip_cash = 0
            trip_cred = 0
            trip_status = trip.status
        else:
            var_trip_id = trip.id
            var_trip_start = trip.starttime
            var_trip_stop = trip.endtime
            var_trip_price = trip.price
            stop_time = datetime.utcnow()
            trans = db.session.query(UserTrans).filter(UserTrans.id == trip.trans).first()
            #db.session.close()
            if not trans:
                trip_cash = trip_cred = 0
            else:
                trip_cash = trans.cash / 100 * (-1)
                trip_cred = trans.amount / 100 * (-1)
            delta = stop_time - trip.starttime
            # Bug - only works for 60min calcs
            time_delta = convert_to_semihours(delta, Price.get_hour_ratio())
            estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.get_hour_ratio() + \
                             math.ceil((booking.dur.minute * Price.get_hour_ratio()) / 60)
            # Now calculate price
            price = booking.estimate
            if booking.estimate_pre_tax == 0:
                booking.estimate_pre_tax = int(0.95*booking.estimate)
            add_due = 0
            if booking.type == BookingParams.TYPE_ROUNDTRIP or booking.type == BookingParams.TYPE_ONEWAY:
                price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = Price.get_trip_price(book_id=booking.id, book_delta=estimate_delta,
                                                      real_delta=time_delta, est=booking.estimate_pre_tax,
                                                      book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                                      trip_starttime=trip.starttime.time(),
                                                      trip_stoptime=stop_time.time(),
                                                      startdate=trip.starttime.date(), enddate=stop_time.date(), insurance_ch=booking.insurance_cost,
                                                      city=booking.region, type=booking.type)
            elif booking.type == BookingParams.TYPE_OUTSTATION:
                price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = PriceOutstation.get_trip_price(startdate=trip.starttime.date(),
                                                                enddate=stop_time.date(),
                                                                book_delta=estimate_delta, real_delta=time_delta,
                                                                est=booking.estimate_pre_tax, insurance_ch=booking.insurance_cost,
                                                                city=booking.region)
            if not var_trip_price and not var_trip_stop:
                var_due = add_due
                var_trip_price = price
            trip_status = trip.status
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        cust = db.session.query(Users).filter(Users.id == booking.user).first()
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        #db.session.close()
        dist = 0
        if booking.driver is not None and booking.driver != 1:
            driver = db.session.query(Drivers, Users).filter(Drivers.id == booking.driver). \
                filter(Drivers.user == Users.id).first()
            driver_name = driver[1].get_name()
            driver_mob = driver[1].mobile
            driver_label = driver[1].label_bv
            details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver[0].id).first()
            if not details:
                driver_owed = 0
            else:
                driver_owed = details.owed
            #db.session.close()
        else:
            driver_owed = 0
            driver_name = ""
            driver_mob = ""
            driver_label = 0
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        suppressed = BookPending.ALLOCATED
        if booking.valid == 0:
            pending = db.session.query(BookPending).filter(booking.id == BookPending.book_id).first()
            #db.session.close()
            if pending:
                suppressed = pending.valid
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        if booking.estimate == 0:
            pricing = db.session.query(BookPricing).filter(booking.id == BookPricing.book_id).first()
            if pricing:
                estimate = pricing.estimate
            else:
                estimate = 0
        else:
            estimate = booking.estimate
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        book_type = get_booking_type(booking.type)
        zc_comment = ""
        # get booking destination based on booking type
        if book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_ONEWAY or \
                book_type == BookingParams.TYPE_MINIOS or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY or \
                book_type == BookingParams.TYPE_MINIOS_ONEWAY or book_type >= BookingParams.TYPE_C24:   # is b2b
            try:
                booking_destination = db.session.query(BookDest).filter(BookDest.book_id == booking.id).first()
                #db.session.close()
            except Exception as ex:
                print(ex)
                return jsonify({'success': -2 , 'cur_index': cur_index})
            if booking_destination is not None:
                var_destination_lat = booking_destination.lat
                var_destination_lng = booking_destination.lng
                var_destination_name = booking_destination.name
            else:
                var_destination_lat = -1
                var_destination_lng = -1
                var_destination_name = ""
        else:
            var_destination_lat = -1
            var_destination_lng = -1
            var_destination_name = ""
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        if book_type == BookingParams.TYPE_C24:
            estimate = var_due = var_trip_price =  0.01
            c24_data = db.session.query(C24Bookings).filter(C24Bookings.ref == booking.id).first()
            if c24_data:
                c24_rep_data = db.session.query(C24Rep).filter(C24Rep.id == c24_data.rep).first()
                if c24_rep_data:
                    cust_name = c24_rep_data.name + " (Appt id: " + c24_data.appt + ") " + "(ID: " + str(c24_data.id) +")"
                    cust_mob = c24_rep_data.mobile
                book_type = book_type * 10 + c24_data.trip_type
                # Bad hax
                car_type_no = c24_data.veh_no + ", " + c24_data.veh_model +  ", " + str(c24_data.drop_mob)
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_OLX:
            olx_data = db.session.query(OLXBookings).filter(OLXBookings.ref == booking.id).first()
            if olx_data:
                olx_rep_data = db.session.query(OLXRep).filter(OLXRep.id == olx_data.rep).first()
                if olx_rep_data:
                    cust_name = olx_rep_data.name + " (OLX ID: " + olx_data.appt + ") " + "(ID: " + str(olx_data.id) + ")"
                    cust_mob = olx_rep_data.mobile
                book_type = book_type * 10 + olx_data.trip_type
                # Bad hax
                car_type_no = olx_data.veh_no + ", " + olx_data.veh_model +  ", " + str(olx_data.drop_mob) + " (" + str(olx_data.dist) + ")"
                dist = olx_data.dist
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_ZOOMCAR:
            zoomcar_data = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == booking.id).first()
            if zoomcar_data:
                zoomcar_rep_data = db.session.query(ZoomcarRep).filter(ZoomcarRep.id == zoomcar_data.rep).first()
                if zoomcar_rep_data:
                    if zoomcar_data.comment == "":
                        cust_name = zoomcar_rep_data.name + " (Zoomcar ID: " + zoomcar_data.appt + ") " + "(ID: " + str(zoomcar_data.id) +")"
                    else:
                        cust_name = zoomcar_rep_data.name + \
                                    " (" + zoomcar_data.comment.replace("booking_key:", "Zoomcar ID: ") + ") " + \
                                    "(ID: " + str(zoomcar_data.id) + ") "
                    cust_mob = zoomcar_rep_data.mobile
                book_type = book_type * 10 + zoomcar_data.trip_type
                # Bad hax
                car_type_no = zoomcar_data.veh_no + ", " + str(zoomcar_data.drop_mob) + " (" + str(zoomcar_data.dist) + ")"
                dist = zoomcar_data.dist
                zc_comment = zoomcar_data.comment
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
                zc_comment = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_CARDEKHO:
            cardekho_data = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == booking.id).first()
            if cardekho_data:
                cardekho_rep_data = db.session.query(CardekhoRep).filter(CardekhoRep.id == cardekho_data.rep).first()
                if cardekho_rep_data:
                    cust_name = cardekho_rep_data.name + " (Cardekho ID: " + cardekho_data.appt + ") " + "(ID: " + str(cardekho_data.id) +")"
                    cust_mob = cardekho_rep_data.mobile
                book_type = book_type * 10 + cardekho_data.trip_type
                # Bad hax
                car_type_no = cardekho_data.veh_no + ", " + str(cardekho_data.drop_mob) + " (" + str(cardekho_data.dist) + ")"
                dist = cardekho_data.dist
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_BHANDARI:
            bhandari_data = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking.id).first()
            if bhandari_data:
                bhandari_rep_data = db.session.query(BhandariRep).filter(BhandariRep.id == bhandari_data.rep).first()
                if bhandari_rep_data:
                    cust_name = bhandari_rep_data.name + " (Bhandari ID: " + bhandari_data.appt + ") " + "(ID: " + str(bhandari_data.id) +")"
                    cust_mob = bhandari_rep_data.mobile
                book_type = book_type * 10 + bhandari_data.trip_type
                # Bad hax
                car_type_no = bhandari_data.veh_no + ", " + str(bhandari_data.drop_mob) + " (" + str(bhandari_data.dist) + ")"
                dist = bhandari_data.dist
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_MAHINDRA:
            mahindra_data = db.session.query(MahindraBookings).filter(MahindraBookings.ref == booking.id).first()
            if mahindra_data:
                mahindra_rep_data = db.session.query(MahindraRep).filter(MahindraRep.id == mahindra_data.rep).first()
                if mahindra_rep_data:
                    cust_name = mahindra_rep_data.name + " (Mahindra ID: " + mahindra_data.appt + ") " + "(ID: " + str(mahindra_data.id) +")"
                    cust_mob = mahindra_rep_data.mobile
                book_type = book_type * 10 + mahindra_data.trip_type
                # Bad hax
                car_type_no = mahindra_data.veh_no + ", " + str(mahindra_data.drop_mob) + " (" + str(mahindra_data.dist) + ")"
                dist = mahindra_data.dist
        elif book_type == BookingParams.TYPE_SPINNY:
            spinny_data = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == booking.id).first()
            if spinny_data:
                spinny_rep_data = db.session.query(SpinnyRep).filter(SpinnyRep.id == spinny_data.rep).first()
                if spinny_rep_data:
                    cust_name = spinny_rep_data.name + " (Spinny ID: " + spinny_data.appt + ") " + "(ID: " + str(spinny_data.id) +")"
                    cust_mob = spinny_rep_data.mobile
                book_type = book_type * 10 + spinny_data.trip_type
                # Bad hax
                car_type_no = spinny_data.veh_no + ", " + str(spinny_data.drop_mob) + " (" + str(spinny_data.dist) + ")"
                dist = spinny_data.dist
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_REVV_V2:
            revv_v2_data = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == booking.id).first()
            if revv_v2_data:
                revv_v2_rep_data = db.session.query(RevvV2Rep).filter(RevvV2Rep.id == revv_v2_data.rep).first()
                if revv_v2_rep_data:
                    cust_name = revv_v2_rep_data.name + " (RevvV2 ID: " + revv_v2_data.appt + ") " + "(ID: " + str(revv_v2_data.id) +")"
                    cust_mob = revv_v2_rep_data.mobile
                book_type = book_type * 10 + revv_v2_data.trip_type
                # Bad hax
                car_type_no = revv_v2_data.veh_no + ", " + str(revv_v2_data.drop_mob) + " (" + str(revv_v2_data.dist) + ")"
                dist = revv_v2_data.dist
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
            #db.session.close()

        elif book_type == BookingParams.TYPE_PRIDEHONDA:
            pridehonda_data = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == booking.id).first()
            if pridehonda_data:
                pridehonda_rep_data = db.session.query(PrideHondaRep).filter(PrideHondaRep.id == pridehonda_data.rep).first()
                if pridehonda_rep_data:
                    cust_name = pridehonda_rep_data.name + " (PrideHonda ID: " + pridehonda_data.appt + ") " + "(ID: " + str(pridehonda_data.id) +")"
                    cust_mob = pridehonda_rep_data.mobile
                book_type = book_type * 10 + pridehonda_data.trip_type
                # Bad hax
                car_type_no = pridehonda_data.veh_no + ", " + str(pridehonda_data.drop_mob) + " (" + str(pridehonda_data.dist) + ")"
                dist = pridehonda_data.dist
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
            #db.session.close()
        elif book_type == BookingParams.TYPE_REVV or book_type == BookingParams.TYPE_GUJRAL:
            estimate = var_due = var_trip_price = 0.01
            cust_name = cust.get_name()
            cust_mob = cust.mobile
            car_type_no = ""
            #db.session.close()
        else:
            cust_name = cust.get_name()
            cust_mob = cust.mobile
            car_type_no = ""
            #db.session.close()
        #end_time.append(time.time() - start_time)
        #start_time = time.time()
        if book_type < BookingParams.TYPE_C24:
            if booking.user in user_trips:
                c_color = user_trips[booking.user]
            else:
                trip_c = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                                filter(Bookings.user == booking.user).count()
                user_trips[booking.user] = trip_c <= 2
                c_color = user_trips[booking.user]
            if booking.driver in driver_trips:
                d_color = driver_trips[booking.driver]
            else:
                trip_c = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                                filter(Bookings.driver == booking.driver).count()
                driver_trips[booking.driver] = trip_c <= 3
                d_color = driver_trips[booking.driver]
            feedback = db.session.query(BookingFeedback).filter(BookingFeedback.id == booking.id)
            if feedback.first():
                f_color = True
            else:
                f_color = False
            user = db.session.query(Users).filter(Users.id == booking.user)
            if user.first():
                m_color = user.first().marked
            else:
                m_color = False
            if m_color:
                c_color = False
            try:
                user_label = user.first().label_bv
            except Exception:
                user_label = 0
        else:
            c_color = False
            d_color = False
            f_color = False
            m_color = False
            user_label = 0
        end_time.append(time.time() - start_time)
        start_time = time.time()
        #db.session.close()
        # driver_name = booking[0].driver == 1
        booking_data = BookingData(bookId=str(booking.id),
                                   tripId=var_trip_id, bookCode=booking.code,
                                   did=booking.driver,
                                   dname=driver_name,
                                   dmob=driver_mob,
                                   dowed=driver_owed,
                                   cid=booking.user,
                                   cname=cust_name,
                                   cmob=cust_mob,
                                   startdate=booking.startdate,
                                   starttime=booking.starttime,
                                   dur=booking.dur,
                                   days=booking.days,
                                   booking_type=book_type,
                                   trip_start=var_trip_start,
                                   trip_stop=var_trip_stop,
                                   estimate=estimate,
                                   price=var_trip_price,
                                   lat=booking.lat,
                                   lng=booking.long,
                                   cancelled=booking.valid + 2,
                                   loc_name=booking.loc,
                                   car_type=search_entry.car_type,
                                   car_type_no=car_type_no,
                                   destlat=var_destination_lat,
                                   destlng=var_destination_lng,
                                   destname=var_destination_name,
                                   comment=booking.comment,
                                   suppressed=suppressed,
                                   due=var_due,
                                   payment=payment_type,
                                   region=booking.region,
                                   c_color=int(c_color),
                                   d_color=int(d_color),
                                   f_color=int(f_color),
                                   m_color=int(m_color),
                                   cash=trip_cash,
                                   cred=trip_cred,
                                   insurance=booking.insurance,
                                   dist=dist,
                                   trip_status=trip_status,
                                   otp=otp,
                                   zc_comment=zc_comment,
                                   user_label=user_label,
                                   driver_label=driver_label
                                   )
        result_json.append(jsonpickle.encode(booking_data))
        end_time.append(time.time() - start_time)
        start_time = time.time()
    if len(result_json) <= 0:
        return jsonify({'success': -1, 'cur_index': cur_index})
    else:

        # admin_log = AdminLog(get_jwt_identity(), 'book-list', json.dumps(str(request.form)))
        # db.session.add(admin_log)
        # db.session.commit()
        return jsonify({'success': 1, 'cur_index': cur_index, 'data': result_json,
                        'times':  ','.join([str(round(x,3)) for x in end_time])}), 200


@admin.route('/api/admin/book_entry_single', methods=['POST'])
@jwt_required()
def booking_single():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -1}), 201
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).first()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201
    result_json = []
    trip = db.session.query(Trip).filter(booking.id == Trip.book_id).first()
    search_entry = db.session.query(DriverSearch).filter(booking.search_key == DriverSearch.id).first()
    dist = 0
    otp = booking.otp
    if booking.driver != 1:
        var_due = Price.get_booking_ch(search_entry.car_type, booking.driver, booking.id)
    else:
        var_due = 0
    if trip is None:
        var_trip_id = ""
        var_trip_start = ""
        var_trip_stop = ""
        var_trip_price = ""
        trip_cash = 0
        trip_cred = 0
        trip_status = -1
    elif trip.starttime is None:
        var_trip_id = trip.id
        var_trip_start = ""
        var_trip_stop = ""
        var_trip_price = ""
        trip_cash = 0
        trip_cred = 0
        trip_status = trip.status
    else:
        var_trip_id = trip.id
        var_trip_start = trip.starttime
        var_trip_stop = trip.endtime
        var_trip_price = trip.price
        stop_time = datetime.utcnow()
        trans = db.session.query(UserTrans).filter(UserTrans.id == trip.trans).first()
        if not trans:
            trip_cash = trip_cred = 0
        else:
            trip_cash = trans.cash / 100 * (-1)
            trip_cred = trans.amount / 100 * (-1)
        delta = stop_time - trip.starttime
        # Bug - only works for 60min calcs
        time_delta = convert_to_semihours(delta, Price.get_hour_ratio())
        estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.get_hour_ratio() + \
                         math.ceil((booking.dur.minute * Price.get_hour_ratio()) / 60)
        # Now calculate price
        price = booking.estimate
        add_due = 0
        if booking.estimate_pre_tax == 0:
            booking.estimate_pre_tax = int(0.95*booking.estimate)
        add_due = 0
        if booking.type == BookingParams.TYPE_ROUNDTRIP or booking.type == BookingParams.TYPE_ONEWAY or booking.type == BookingParams.TYPE_MINIOS_ONEWAY:
            price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare = Price.get_trip_price(book_id=booking.id, book_delta=estimate_delta,
                                                  real_delta=time_delta, est=booking.estimate_pre_tax,
                                                  book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                                  trip_starttime=trip.starttime.time(),
                                                  trip_stoptime=stop_time.time(),
                                                  startdate=trip.starttime.date(), enddate=stop_time.date(), insurance_ch=booking.insurance_cost,
                                                  city=booking.region, type=booking.type)
        elif booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            price, pre_tax, cgst, sgst, add_due, ot_fare, night_fare  = PriceOutstation.get_trip_price(startdate=trip.starttime.date(),
                                                            enddate=stop_time.date(),
                                                            book_delta=estimate_delta, real_delta=time_delta,
                                                            est=booking.estimate_pre_tax, insurance_ch=booking.insurance_cost,
                                                            city=booking.region)
        if not var_trip_price and not var_trip_stop:
            var_due = add_due
            var_trip_price = price
        trip_status = trip.status

    payment_type = booking.payment_type
    cust = db.session.query(Users).filter(Users.id == booking.user).first()
    if booking.driver is not None and booking.driver != 1:
        driver = db.session.query(Drivers, Users).filter(Drivers.id == booking.driver). \
            filter(Drivers.user == Users.id).first()
        driver_name = driver[1].get_name()
        driver_mob = driver[1].mobile
        driver_label = driver[1].label_bv
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver[0].id).first()
        if not details:
            driver_owed = 0
        else:
            driver_owed = details.owed
    else:
        driver_owed = 0
        driver_name = ""
        driver_mob = ""
        driver_label = 0

    suppressed = BookPending.ALLOCATED
    if booking.valid == 0:
        pending = db.session.query(BookPending).filter(booking.id == BookPending.book_id).order_by(BookPending.driver.desc()).first()
        #db.session.close()
        if pending:
            suppressed = pending.valid
    if booking.estimate == 0:
        pricing = db.session.query(BookPricing).filter(booking.id == BookPricing.book_id).first()
        if pricing:
            estimate = pricing.estimate
        else:
            estimate = 0
    else:
        estimate = booking.estimate

    book_type = get_booking_type(booking.type)
    zc_comment = ""
    # get booking destination based on booking type
    if book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_ONEWAY or \
            book_type == BookingParams.TYPE_MINIOS or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY or \
            book_type == BookingParams.TYPE_MINIOS_ONEWAY or book_type >= BookingParams.TYPE_C24:   # is b2b
        try:
            booking_destination = db.session.query(BookDest).filter(BookDest.book_id == booking.id).first()
        except Exception as ex:
            print(ex)
            return jsonify({'success': -2})
        if booking_destination is not None:
            var_destination_lat = booking_destination.lat
            var_destination_lng = booking_destination.lng
            var_destination_name = booking_destination.name
        else:
            var_destination_lat = -1
            var_destination_lng = -1
            var_destination_name = ""
    else:
        var_destination_lat = -1
        var_destination_lng = -1
        var_destination_name = ""
    if book_type == BookingParams.TYPE_C24:
            estimate = var_due = var_trip_price = 0.01
            c24_data = db.session.query(C24Bookings).filter(C24Bookings.ref == booking.id).first()
            if c24_data:
                c24_rep_data = db.session.query(C24Rep).filter(C24Rep.id == c24_data.rep).first()
                if c24_rep_data:
                    cust_name = c24_rep_data.name + " (Appt id: " + c24_data.appt + ") "  + "(ID: " + str(c24_data.id) + ")"
                    cust_mob = c24_rep_data.mobile
                book_type = book_type * 10 + c24_data.trip_type
                # Bad hax
                car_type_no = c24_data.veh_no + ", " + c24_data.veh_model +  ", " + str(c24_data.drop_mob)
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
    elif book_type == BookingParams.TYPE_OLX:
            olx_data = db.session.query(OLXBookings).filter(OLXBookings.ref == booking.id).first()
            if olx_data:
                olx_rep_data = db.session.query(OLXRep).filter(OLXRep.id == olx_data.rep).first()
                if olx_rep_data:
                    cust_name = olx_rep_data.name + " (OLX ID: " + olx_data.appt + ") " + "(ID: " + str(olx_data.id) + ")"
                    cust_mob = olx_rep_data.mobile
                book_type = book_type * 10 + olx_data.trip_type
                # Bad hax
                car_type_no = olx_data.veh_no + ", " + olx_data.veh_model +  ", " + str(olx_data.drop_mob)
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
    elif book_type == BookingParams.TYPE_ZOOMCAR:
            zoomcar_data = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == booking.id).first()
            if zoomcar_data:
                zoomcar_rep_data = db.session.query(ZoomcarRep).filter(ZoomcarRep.id == zoomcar_data.rep).first()
                if zoomcar_rep_data:
                    if zoomcar_data.comment == "":
                        cust_name = zoomcar_rep_data.name + " (Zoomcar ID: " + zoomcar_data.appt + ") " + "(ID: " + str(zoomcar_data.id) +")"
                    else:
                        cust_name = zoomcar_rep_data.name + \
                                    " (" + zoomcar_data.comment.replace("booking_key:", "Zoomcar ID: ") + ") " + \
                                    "(ID: " + str(zoomcar_data.id) + ") "
                    cust_mob = zoomcar_rep_data.mobile
                book_type = book_type * 10 + zoomcar_data.trip_type
                # Bad hax
                car_type_no = zoomcar_data.veh_no + ", " + zoomcar_data.drop_mob + " (" + str(zoomcar_data.dist) + ")"
                dist = zoomcar_data.dist
                zc_comment = zoomcar_data.comment
            else:
                cust_name = "Drop SPOC"
                cust_mob = ""
                car_type_no = ""
                zc_comment = ""
    elif book_type == BookingParams.TYPE_CARDEKHO:
        cardekho_data = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == booking.id).first()
        if cardekho_data:
            cardekho_rep_data = db.session.query(CardekhoRep).filter(CardekhoRep.id == cardekho_data.rep).first()
            if cardekho_rep_data:
                cust_name = cardekho_rep_data.name + " (Cardekho ID: " + cardekho_data.appt + ") " + "(ID: " + str(cardekho_data.id) + ")"
                cust_mob = cardekho_rep_data.mobile
            book_type = book_type * 10 + cardekho_data.trip_type
            # Bad hax
            car_type_no = cardekho_data.veh_no + ", " + str(cardekho_data.drop_mob) + " (" + str(cardekho_data.dist) + ")"
            dist = cardekho_data.dist
        else:
            cust_name = "Drop SPOC"
            cust_mob = ""
            car_type_no = ""
    elif book_type == BookingParams.TYPE_BHANDARI:
        bhandari_data = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking.id).first()
        if bhandari_data:
            bhandari_rep_data = db.session.query(BhandariRep).filter(BhandariRep.id == bhandari_data.rep).first()
            if bhandari_rep_data:
                cust_name = bhandari_rep_data.name + " (Bhandari ID: " + bhandari_data.appt + ") " + "(ID: " + str(bhandari_data.id) + ")"
                cust_mob = bhandari_rep_data.mobile
            book_type = book_type * 10 + bhandari_data.trip_type
            # Bad hax
            car_type_no = bhandari_data.veh_no + ", " + str(bhandari_data.drop_mob) + " (" + str(bhandari_data.dist) + ")"
            dist = bhandari_data.dist
        else:
            cust_name = "Drop SPOC"
            cust_mob = ""
            car_type_no = ""
    elif book_type == BookingParams.TYPE_MAHINDRA:
        mahindra_data = db.session.query(MahindraBookings).filter(MahindraBookings.ref == booking.id).first()
        if mahindra_data:
            mahindra_rep_data = db.session.query(MahindraRep).filter(MahindraRep.id == mahindra_data.rep).first()
            if mahindra_rep_data:
                cust_name = mahindra_rep_data.name + " (Mahindra ID: " + mahindra_data.appt + ") " + "(ID: " + str(mahindra_data.id) + ")"
                cust_mob = mahindra_rep_data.mobile
            book_type = book_type * 10 + mahindra_data.trip_type
            # Bad hax
            car_type_no = mahindra_data.veh_no + ", " + str(mahindra_data.drop_mob) + " (" + str(mahindra_data.dist) + ")"
            dist = mahindra_data.dist
    elif book_type == BookingParams.TYPE_SPINNY:
        spinny_data = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == booking.id).first()
        if spinny_data:
            spinny_rep_data = db.session.query(SpinnyRep).filter(SpinnyRep.id == spinny_data.rep).first()
            if spinny_rep_data:
                cust_name = spinny_rep_data.name + " (Spinny ID: " + spinny_data.appt + ") " + "(ID: " + str(spinny_data.id) + ")"
                cust_mob = spinny_rep_data.mobile
            book_type = book_type * 10 + spinny_data.trip_type
            # Bad hax
            car_type_no = spinny_data.veh_no + ", " + str(spinny_data.drop_mob) + " (" + str(spinny_data.dist) + ")"
            dist = spinny_data.dist
        else:
            cust_name = "Drop SPOC"
            cust_mob = ""
            car_type_no = ""
    elif book_type == BookingParams.TYPE_REVV_V2:
        revv_v2_data = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == booking.id).first()
        if revv_v2_data:
            revv_v2_rep_data = db.session.query(RevvV2Rep).filter(RevvV2Rep.id == revv_v2_data.rep).first()
            if revv_v2_rep_data:
                cust_name = revv_v2_rep_data.name + " (RevvV2 ID: " + revv_v2_data.appt + ") " + "(ID: " + str(revv_v2_data.id) + ")"
                cust_mob = revv_v2_rep_data.mobile
            book_type = book_type * 10 + revv_v2_data.trip_type
            # Bad hax
            car_type_no = revv_v2_data.veh_no + ", " + str(revv_v2_data.drop_mob) + " (" + str(revv_v2_data.dist) + ")"
            dist = revv_v2_data.dist
        else:
            cust_name = "Drop SPOC"
            cust_mob = ""
            car_type_no = ""
    elif book_type == BookingParams.TYPE_PRIDEHONDA:
        pridehonda_data = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == booking.id).first()
        if pridehonda_data:
            pridehonda_rep_data = db.session.query(PrideHondaRep).filter(PrideHondaRep.id == pridehonda_data.rep).first()
            if pridehonda_rep_data:
                cust_name = pridehonda_rep_data.name + " (PrideHonda ID: " + pridehonda_data.appt + ") " + "(ID: " + str(pridehonda_data.id) + ")"
                cust_mob = pridehonda_rep_data.mobile
            book_type = book_type * 10 + pridehonda_data.trip_type
            # Bad hax
            car_type_no = pridehonda_data.veh_no + ", " + str(pridehonda_data.drop_mob) + " (" + str(pridehonda_data.dist) + ")"
            dist = pridehonda_data.dist
        else:
            cust_name = "Drop SPOC"
            cust_mob = ""
            car_type_no = ""
    elif book_type == BookingParams.TYPE_REVV or book_type == BookingParams.TYPE_GUJRAL:
            estimate = var_due = var_trip_price = 0.01
            cust_name = cust.get_name()
            cust_mob = cust.mobile
            car_type_no = ""
    else:
        cust_name = cust.get_name()
        cust_mob = cust.mobile
        car_type_no = ""
    if book_type < BookingParams.TYPE_C24:
        trip_c_cus = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                                filter(Bookings.user == booking.user).count()
        c_color = trip_c_cus <= 2
        if booking.driver != 1:
            trip_c_dri = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                                    filter(Bookings.driver == booking.driver).count()
            d_color = trip_c_dri <= 3
        else:
            d_color = False
        feedback = db.session.query(BookingFeedback).filter(BookingFeedback.id == booking.id)
        if feedback.first():
            f_color = True
        else:
            f_color = False
        user = db.session.query(Users).filter(Users.id == booking.user)
        if user.first():
            m_color = user.first().marked
        else:
            m_color = False
        if m_color:
            c_color = False
        try:
            user_label = user.first().label_bv
        except Exception:
            user_label = 0
    else:
        c_color = d_color = f_color = m_color = False
        user_label = 0

    # driver_name = booking[0].driver == 1
    booking_data = BookingData(bookId=str(booking.id),
                               bookCode=booking.code,
                               tripId=var_trip_id,
                               did=booking.driver,
                               dname=driver_name,
                               dmob=driver_mob,
                               dowed=driver_owed,
                               cid=booking.user,
                               cname=cust_name,
                               cmob=cust_mob,
                               startdate=booking.startdate,
                               starttime=booking.starttime,
                               dur=booking.dur,
                               days=booking.days,
                               booking_type=book_type,
                               trip_start=var_trip_start,
                               trip_stop=var_trip_stop,
                               estimate=estimate,
                               price=var_trip_price,
                               lat=booking.lat,
                               lng=booking.long,
                               cancelled=booking.valid + 2,
                               loc_name=booking.loc,
                               car_type=search_entry.car_type,
                               car_type_no=car_type_no,
                               destlat=var_destination_lat,
                               destlng=var_destination_lng,
                               destname=var_destination_name,
                               comment=booking.comment,
                               suppressed=suppressed,
                               due=var_due,
                               payment=payment_type,
                               region=booking.region,
                               c_color=int(c_color),
                               d_color=int(d_color),
                               f_color=int(f_color),
                               m_color=int(m_color),
                               cash=trip_cash,
                               cred=trip_cred,
                               insurance=booking.insurance,
                               dist=dist,
                               trip_status=trip_status,
                               otp=otp,
                               zc_comment=zc_comment,
                               user_label=user_label,
                               driver_label=driver_label)
    result_json.append(jsonpickle.encode(booking_data))
    if len(result_json) <= 0:
        return jsonify({'success': -1})
    else:

        # admin_log = AdminLog(get_jwt_identity(), 'book-list', json.dumps(str(request.form)))
        # db.session.add(admin_log)
        # db.session.commit()
        return jsonify({'success': 1, 'data': result_json}), 200


@admin.route('/api/admin/estimate_no_book', methods=['POST'])
@jwt_required()
def estimate_no_book():
    claims = get_jwt()
    region = int(get_safe(request.form, 'region', -1))
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if region == -1:
        region_list = [i for i in range(len(Regions.REGN_NAME))]
    else:
        region_list = Regions.admin_view_regions(region)
    try:
        results = db.session.query(DriverSearch, Users).filter(DriverSearch.user == Users.id). \
            filter(Users.role == Users.ROLE_USER). \
            filter(DriverSearch.region.in_(region_list)). \
            filter(func.concat(DriverSearch.date, ' ', DriverSearch.time) > datetime.utcnow()). \
            order_by(DriverSearch.timestamp.desc()).distinct(Users.id).limit(300).all()
    except Exception as e:
        return jsonify({'success': -1}), 201
    result_json = []
    if not results:
        return jsonify({'success': -1})
    for result in results:
        if int(result[0].dur.hour) == 0:
            dur = result[0].days * 24
        else:
            dur = result[0].dur
        booking_data = SearchData(searchkey=result[0].id,
                                  cname=result[1].get_name(),
                                  cmob=result[1].mobile,
                                  startdate=result[0].date,
                                  starttime=result[0].time,
                                  dur=dur,
                                  lat=result[0].reflat,
                                  lng=result[0].reflong,
                                  car_type=result[0].car_type,
                                  region=result[0].region,
                                  timestamp=str(result[0].timestamp))
        result_json.append(jsonpickle.encode(booking_data))
    if len(result_json) <= 0:
        return jsonify({'success': -1})
    else:
        return jsonify({'success': 1, 'data': result_json}), 200


@admin.route('/api/admin/driver_list', methods=['POST'])
@jwt_required()
def all_drivers():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['location']):
        return jsonify({'success': -1}), 201
    result_json = []
    try:
        region = int(get_safe(request.form, 'region', -1))
    except Exception:
        region = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().region
    last_month_date = datetime.utcnow() - timedelta(days=30)
    l_limit = int(get_safe(request.form, 'l_limit', -1))
    u_limit = int(get_safe(request.form, 'u_limit', -1))
    if region == -1:
        region_list = [i for i in range(len(Regions.REGN_NAME))]
    else:
        region_list = Regions.admin_view_regions(region)
    try:
        drivers = db.session.query(Users, Drivers, DriverDetails, DriverInfo, DriverBank). \
            filter(Drivers.user == Users.id). \
            filter(DriverDetails.driver_id == Drivers.id).filter(Users.region.in_(region_list)). \
            filter(DriverBank.driver_id == Drivers.id).filter(DriverInfo.driver_id == Drivers.id)
        driver_last_month =  db.session.query(Drivers.id, func.count(Bookings.id)). \
            filter(Bookings.driver == Drivers.id). \
            filter(Bookings.valid == 1). \
            filter(Bookings.startdate > last_month_date). \
            filter(Bookings.type < 50).group_by(Drivers.id)
        driver_cancelled =  db.session.query(Drivers.id, func.count(DriverCancelled.id)). \
            filter(DriverCancelled.driver == Drivers.id).group_by(Drivers.id)
        if l_limit >= 0  and u_limit >= 0:
            drivers = drivers.filter(Drivers.id < u_limit).filter(Drivers.id >= l_limit)

        drivers = drivers.all()
        driver_last_month = driver_last_month.all()
        driver_cancelled = driver_cancelled.all()
        driver_last_month = {e[0]: e[1] for e in driver_last_month}
        driver_cancelled = {e[0]: e[1] for e in driver_cancelled}
    except ZeroDivisionError as e:
        print(e)
        return jsonify({'success': -2}), 201
    for driver in drivers:
        #ontrip_q = db.session.query(Trip, Bookings).filter(Bookings.id == Trip.book_id). \
        #    filter(Bookings.driver == driver[1].id). \
        #    filter(Trip.endtime == None).first()
        ontrip = False #ontrip_q is not None
        #trip_c = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                            #filter(Bookings.driver == driver[1].id).count()
        color = False #int(trip_c <= 5)
        d_info = driver[3]
        d_bank = driver[4]
        if d_info:
            verf_ph = d_info.verf_ph
            driver_lic_doc_b = d_info.driver_lic_doc_b
            driver_id_doc_f = d_info.driver_id_doc_f
            driver_id_doc_b = d_info.driver_id_doc_b
        else:
            verf_ph = 0
            driver_lic_doc_b = ""
            driver_id_doc_b = driver_id_doc_f = ""
        if d_bank:
            acc_doc = d_bank.acc_doc
            acc_no = d_bank.acc_no
            ifsc = d_bank.ifsc
        else:
            ifsc = acc_no = acc_doc = ""
        score_arr = get_driver_static_score(driver[1], driver[0], driver[2], driver_cancelled.get(driver[1].id, 0), driver_last_month.get(driver[1].id, 0))
        driver_data = DriverData(driver_id=driver[1].id,
                                 mobile=driver[0].mobile,
                                 fname=driver[0].fname,
                                 lname=driver[0].lname,
                                 licence=driver[1].licenseNo,
                                 available=driver[1].available,
                                 lat=driver[3].pres_addr_lat,
                                 lng=driver[3].pres_addr_lng,
                                 approved=driver[1].approved,
                                 addr=driver[3].pres_region,
                                 due=driver[2].owed,
                                 rating=driver[1].rating,
                                 ontrip=ontrip,
                                 score=sum(score_arr),
                                 perma=driver[1].perma == 1,
                                 region=driver[0].region,
                                 verif_phone=verf_ph,
                                 lic_doc_front=driver[1].licenseDoc,
                                 lic_doc_back=driver_lic_doc_b,
                                 id_doc_front=driver_id_doc_f,
                                 id_doc_back=driver_id_doc_b,
                                 bank_doc=acc_doc,
                                 pic=driver[1].pic,
                                 bank_ac_number=acc_no,
                                 bank_ifsc_number=ifsc,
                                 color=int(color))
        result_json.append(jsonpickle.encode(driver_data))
    if len(result_json) <= 0:
        return jsonify({'success': -4})
    else:
        return jsonify({'success': 1, 'data': result_json}), 200

@admin.route('/api/admin/driver_list_allocate', methods=['POST'])
@jwt_required()
def all_drivers_allocate():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    result_json = []
    try:
        region = int(get_safe(request.form, 'region', -1))
    except Exception:
        region = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().region
    last_month_date = datetime.utcnow() - timedelta(days=30)
    if region == -1:
        region_list = [i for i in range(len(Regions.REGN_NAME))]
    else:
        region_list = Regions.driver_service_regions(region)
    try:
        drivers = db.session.query(Users, Drivers, DriverDetails). \
                        filter(Drivers.user == Users.id). \
                        filter(DriverDetails.driver_id == Drivers.id). \
                        filter(Users.region.in_(region_list)).group_by(Drivers.id)
        driver_last_month =  db.session.query(Drivers.id, func.count(Bookings.id)). \
            filter(Bookings.driver == Drivers.id). \
            filter(Bookings.valid == 1). \
            filter(Bookings.startdate > last_month_date). \
            filter(Bookings.type < 50).group_by(Drivers.id)
        driver_cancelled =  db.session.query(Drivers.id, func.count(DriverCancelled.id)). \
            filter(DriverCancelled.driver == Drivers.id).group_by(Drivers.id)
        drivers = drivers.all()
        driver_last_month = driver_last_month.all()
        driver_cancelled = driver_cancelled.all()
        driver_last_month = {e[0]: e[1] for e in driver_last_month}
        driver_cancelled = {e[0]: e[1] for e in driver_cancelled}
    except Exception as e:
        print(e)
        return jsonify({'success': -3}), 201
    for driver in drivers:
        #ontrip_q = db.session.query(Trip, Bookings).filter(Trip.endtime == None). \
        #    filter(Bookings.id == Trip.book_id). \
        #    filter(Bookings.driver == driver[1].id). \
        #    first()
        #trip_c = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
        #                    filter(Bookings.driver == driver[1].id).count()
        color = 0 #int(trip_c <= 5)
        score_arr = get_driver_static_score(driver[1], driver[0], driver[2], driver_cancelled.get(driver[1].id, 0), driver_last_month.get(driver[1].id, 0))
        driver_data = DriverData(driver_id=driver[1].id,
                                 mobile=driver[0].mobile,
                                 fname=driver[0].fname,
                                 lname=driver[0].lname,
                                 licence=driver[1].licenseNo,
                                 available=driver[1].available,
                                 lat=-1,
                                 lng=-1,
                                 approved=driver[1].approved,
                                 addr="-",
                                 due=0,
                                 score=sum(score_arr),
                                 rating=driver[1].rating,
                                 ontrip=0, #ontrip_q is not None,
                                 perma=driver[1].perma == 1,
                                 region=driver[0].region,
                                 color=int(color))
        result_json.append(jsonpickle.encode(driver_data))
    if len(result_json) <= 0:
        return jsonify({'success': -4})
    else:
        return jsonify({'success': 1, 'data': result_json}), 200


# @admin.route('/api/admin/driver_list/perma', methods=['POST'])
# @jwt_required()
# def perm_drivers():
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'success': -1}), 401
#     if not complete(request.form, ['location']):
#         return jsonify({'success': -1}), 201
#     try:
#         region = int(get_safe(request.form, 'region', -1))
#     except Exception:
#         region = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().region
#     if region == -1:
#         region_list = [i for i in range(len(Regions.REGN_NAME))]
#     else:
#         region_list = Regions.admin_view_regions(region)
#     result_json = []
#     if int(request.form['location']) == 1:
#         # need location
#         try:
#             drivers = db.session.query(Users, Drivers, DriverInfo, DriverDetails).filter(
#                 Drivers.user == Users.id).filter(Drivers.id == DriverInfo.driver_id). \
#                 filter(Drivers.perma == 1).filter(DriverDetails.driver_id == Drivers.id). \
#                 filter(Users.region.in_(region_list)).all()
#         except Exception as e:
#             print(e)
#             return jsonify({'success': -2}), 201
#         for driver in drivers:
#             driver_perma = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == driver[1].id).first()
#             if not driver_perma:
#                 ta = 0
#                 ot = 0
#                 base = 0
#                 alloc = 0
#             else:
#                 ta = driver_perma.ta
#                 ot = driver_perma.ot
#                 base = driver_perma.base
#                 alloc = driver_perma.alloc
#             ontrip_q = db.session.query(Trip, Bookings).filter(Bookings.id == Trip.book_id). \
#                 filter(Bookings.driver == driver[1].id). \
#                 filter(Trip.endtime == None).first()
#             ontrip = ontrip_q is not None
#             driver_data = PermaDriverData(driver_id=driver[1].id,
#                                      mobile=driver[0].mobile,
#                                      fname=driver[0].fname,
#                                      lname=driver[0].lname,
#                                      licence=driver[1].licenseNo,
#                                      available=driver[1].available,
#                                      lat=driver[2].pres_addr_lat,
#                                      lng=driver[2].pres_addr_lng,
#                                      approved=driver[1].approved,
#                                      addr=driver[2].pres_region,
#                                      due=driver[3].owed,
#                                      rating=driver[1].rating,
#                                      ontrip=ontrip,
#                                      perma=driver[1].perma == 1,
#                                      ta=ta,
#                                      ot=ot,
#                                      base=base,
#                                      alloc=alloc,
#                                      region=driver[0].region,
#                                      lic_doc_front=driver[1].licenseDoc,
#                                      pic=driver[1].pic)
#             result_json.append(jsonpickle.encode(driver_data))
#     else:
#         try:
#             drivers = db.session.query(Users, Drivers, DriverDetails). \
#                 filter(Drivers.user == Users.id). \
#                 filter(DriverDetails.driver_id == Drivers.id). \
#                 filter(Drivers.id == DriverDetails.driver_id). \
#                 filter(Users.region.in_(region_list)).all()
#         except Exception as e:
#             print(e)
#             return jsonify({'success': -3}), 201
#         for driver in drivers:
#             driver_perma = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == driver[1].id).first()
#             if not driver_perma:
#                 ta = 0
#                 ot = 0
#                 base = 0
#                 alloc = 0
#             else:
#                 ta = driver_perma.ta
#                 ot = driver_perma.ot
#                 base = driver_perma.base
#                 alloc = driver_perma.alloc
#             ontrip_q = db.session.query(Trip, Bookings).filter(Bookings.id == Trip.book_id). \
#                 filter(Bookings.driver == driver[1].id). \
#                 filter(Trip.endtime == None).first()
#             ontrip = ontrip_q is not None
#             driver_data = PermaDriverData(driver_id=driver[1].id,
#                                      mobile=driver[0].mobile,
#                                      fname=driver[0].fname,
#                                      lname=driver[0].lname,
#                                      licence=driver[1].licenseNo,
#                                      available=driver[1].available,
#                                      lat=-1,
#                                      lng=-1,
#                                      approved=driver[1].approved,
#                                      addr="-",
#                                      due=driver[2].owed,
#                                      rating=driver[1].rating,
#                                      ontrip=ontrip,
#                                      perma=driver[1].perma == 1,
#                                      ta=ta,
#                                      ot=ot,
#                                      base=base,
#                                      alloc=alloc,
#                                      region=driver[0].region,
#                                      lic_doc_front=driver[1].licenseDoc,
#                                      pic=driver[1].pic)
#             result_json.append(jsonpickle.encode(driver_data))
#     if len(result_json) <= 0:
#         return jsonify({'success': -4})
#     else:
#         return jsonify({'success': 1, 'data': result_json}), 200


@admin.route('/api/admin/driver_paid', methods=['POST'])
@jwt_required()
def driver_paid():
    claims = get_jwt()
    user = get_jwt_identity()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['driver_id', 'amount']):
        return jsonify({'success': -2}), 201
    driver_id = int(get_safe(request.form, 'driver_id', -1))
    amount = int(request.form['amount'])
    cause = request.form['cause']
    if 'withdraw' in request.form:
        withdraw = int(request.form['withdraw'])
    else:
        withdraw = 0
    remarks = str(get_safe(request.form, 'remarks', ""))
    try:
        drivers = db.session.query(Users, Drivers).filter(Drivers.user == Users.id). \
            filter(Drivers.id == int(driver_id)).first()
    except AttributeError as e:
        print(e)
        return jsonify({'success': -1, "msg": "Could not find driver"}), 201
    admin_name = db.session.query(Users).filter(Users.id == user).first().get_name()
    remarks = remarks + " (" + admin_name + ")"
    remarks = remarks.strip()
    if withdraw == 1:
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == int(driver_id))
        driver_details = details.first()
        if amount >= 0 or driver_details.withdrawable + amount < 0:
            return jsonify({"success": -1, "msg": "Amount requested is too high"})

        withdrawable = driver_details.withdrawable + amount
        dt = DriverTrans(driver_id, amount*100,
                         wall_a=driver_details.wallet, wall_b=driver_details.wallet,
                         with_a=withdrawable, with_b=driver_details.withdrawable,
                         method="Withdraw Amount",
                         status=DriverTrans.COMPLETED, stop=True,
                         remarks=remarks
                        )
        db.session.add(dt)
        details.update({DriverDetails.withdrawable: withdrawable})
        admin_log = AdminLog(user, 'dues-%s' % dt.id, json.dumps(str(request.form)))
        send_slack_msg(0, admin_name + "withdrew for driver id: " + str(driver_id) + " " + str(-amount))
        db.session.add(admin_log)
        db.session.commit()
        send_fcm_msg_driver(int(driver_id), title="Amount withdrawn!", smalltext="You withdrew ₹" + str(-amount),
                            bigtext="You withdrew ₹" + str(-amount) + \
                            ". Your new wallet amount is ₹" + str(round(driver_details.wallet + withdrawable, 2)) + \
                            " and withdrawable amount is ₹" + str(round(withdrawable, 2)) + ".")
        return jsonify({'success': 1}), 200
    try:
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == int(driver_id))
        driver_details = details.first()
        if withdraw == 2:
            wallet = driver_details.wallet
            withdrawable = driver_details.withdrawable
        else:
            if amount < 0:
                wallet, withdrawable = compute_driver_wallet(driver_details, -amount)
            else:
                wallet = driver_details.wallet + amount
                withdrawable = driver_details.withdrawable
        dt = DriverTrans(driver_id, amount*100,
                         wall_a=wallet, wall_b=driver_details.wallet,
                         with_a=withdrawable, with_b=driver_details.withdrawable,
                         method=str(cause),
                         status=DriverTrans.COMPLETED, stop=True,
                         remarks=remarks
                        )
        db.session.add(dt)
        details.update({DriverDetails.wallet: wallet, DriverDetails.withdrawable: withdrawable})
        admin_log = AdminLog(user, 'dues-%s' % dt.id, json.dumps(str(request.form)))
        admin_name = db.session.query(Users).filter(Users.id == user).first().get_name()
        send_slack_msg(0, admin_name + "added driver wallet for driver id: " + str(driver_id) + " by " + str(amount) + " due to cause: " + cause)
        db.session.add(admin_log)
        db.session.commit()
        send_fcm_msg_driver(int(driver_id), title="Wallet amount added!", smalltext="Your wallet had ₹" + str(amount) + " added",
                            bigtext="Your wallet amount was increased by ₹" + str(amount) + \
                            ". Your new wallet amount is ₹" + str(round(wallet + withdrawable, 2)) + \
                            " and withdrawable amount is ₹" + str(round(withdrawable, 2)) + ".")
        return jsonify({'success': 1}), 200
    except AttributeError as exc:
        print(exc)
        return jsonify({'success': -1})


@admin.route('/api/admin/search_by_mobile', methods=['POST'])
@jwt_required()
def search_by_mobile():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['phone']):
        return jsonify({'success': -1}), 201
    mobile = request.form['phone']
    try:
        driver = db.session.query(Users, Drivers, DriverDetails).filter(Drivers.user == Users.id). \
            filter(Drivers.id == DriverDetails.driver_id).filter(Users.mobile == int(mobile)).first()
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201
    if driver is not None:
        return jsonify({'success': 1,
                        'driver_id': driver[1].id,
                        'driver_name': driver[0].get_name(),
                        'driver_license': driver[1].licenseNo,
                        'due': driver[2].owed,
                        'wallet': driver[2].wallet,
                        'withdrawable': driver[2].withdrawable
                        }), 200
    else:
        return jsonify({'success': 1,
                        'driver_name': ''}), 200

@admin.route('/api/admin/driver_update/pic', methods=['POST'])
@jwt_required()
def driver_update_pic():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['driver_id']):
        return jsonify({'success': -1}), 201
    driver_id = request.form['driver_id']
    lic_doc_f_dat = request.files.get('lic_doc_front')
    lic_doc_b_dat = request.files.get('lic_doc_back')
    id_doc_f_dat = request.files.get('id_doc_front')
    id_doc_b_dat = request.files.get('id_doc_back')
    pic_dat = request.files.get('pic')
    acc_doc =  request.files.get('acc_doc')

    driver_entry = db.session.query(Drivers).filter(Drivers.id == driver_id)
    driver_info = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id)
    if pic_dat:
        pic_url = upload_pic(pic_dat)
        print(request.files, pic_url)
        driver_entry.update({Drivers.pic: pic_url})
        driver_info.update({DriverInfo.pic: pic_url})
    if lic_doc_f_dat:
        lic_f_pic_url = upload_pic(lic_doc_f_dat)
        driver_entry.update({Drivers.licenseDoc: lic_f_pic_url})
        driver_info.update({DriverInfo.driver_lic_doc_f: lic_f_pic_url})
    if lic_doc_b_dat:
        lic_doc_b_url = upload_pic(lic_doc_b_dat)
        driver_info.update({DriverInfo.driver_lic_doc_b: lic_doc_b_url})
    if id_doc_f_dat:
        id_doc_f_url = upload_pic(id_doc_f_dat)
        driver_info.update({DriverInfo.driver_id_doc_f: id_doc_f_url})
    if id_doc_b_dat:
        id_doc_b_url = upload_pic(id_doc_b_dat)
        driver_info.update({DriverInfo.driver_id_doc_b: id_doc_b_url})
    if acc_doc:
        driver_bank = db.session.query(DriverBank).filter(DriverBank.driver_id == driver_id)
        acc_doc_url = upload_pic(acc_doc)
        driver_bank.update({DriverBank.acc_doc: acc_doc_url})
    try:
        db.session.commit()
        driver_data = {
            'driver_id': driver_id,
        }
        driver_det = db.session.query(Users, Drivers) \
        .filter(Users.id == Drivers.user, Drivers.id == driver_id) \
        .first()
        live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[0].region, channel= 'update_driver')
    except Exception as exc:
        print("Exception in uploading pic:", str(exc))
        return jsonify({'success': -1}), 200
    return jsonify({'success': 1}), 200

@admin.route('/api/admin/driver_update/base_loc', methods=['POST'])
@jwt_required()
def driver_update_loc():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['driver_id']):
        return jsonify({'success': -1}), 201
    form = request.form
    driver_id = form['driver_id']
    lat = form['lat']
    lng = form['lng']
    addr = form['addr']
    driver_loc = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id)
    if driver_loc.first():
        driver_loc.update({DriverInfo.pres_addr_lat: lat,
                           DriverInfo.pres_addr_lng: lng,
                           DriverInfo.pres_region: addr
                          })
    else:
        return jsonify({'success': -1}), 200
        # request.form["init"] = True
        # driver_info = DriverInfo(driver_id, lat, lng, addr)
        # db.session.add(driver_info)
    admin_log = AdminLog(get_jwt_identity(), 'Driver Account Location change',
                         json.dumps(str(request.form)))
    db.session.add(admin_log)
    try:
        db.session.commit()
    except Exception as exc:
        print("Exception in updating details:", str(exc))
        return jsonify({'success': -1}), 200
    return jsonify({'success': 1}), 200

@admin.route('/api/admin/driver_update/info', methods=['POST'])
@jwt_required()
def driver_update_info():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['driver_id']):
        return jsonify({'success': -1}), 201
    form = request.form
    driver_id = form['driver_id']
    id_no = form.get('id_no')
    lic_no = form.get('lic_no')
    verf_name = form.get('verf_name')
    verf_rel = form.get('verf_rel')
    verf_ph = int(get_safe(form, 'verf_ph', 0))
    acc_no = form.get('acc_no')
    ifsc = form.get('ifsc')
    pan_no = form.get('pan_no')
    fname = form.get('fname')
    lname = form.get('lname')

    # Extract height and weight
    height_feet = int(get_safe(form, 'height_feet', 0))  # Default to 0 if not provided
    height_inches = int(get_safe(form, 'height_inches', 0))  # Default to 0 if not provided
    height_cm = (height_feet * 30.48) + (height_inches * 2.54)  # Convert to cm
    weight = int(get_safe(form, 'weight', 0))  # Default to 0 if not provided

    driver_entry = db.session.query(Drivers).filter(Drivers.id == driver_id)
    driver_info = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id)
    driver_bank = db.session.query(DriverBank).filter(DriverBank.driver_id == driver_id)
    if id_no:
        driver_info.update({DriverInfo.id_no: id_no})
    if lic_no:
        driver_entry.update({Drivers.licenseNo: lic_no})
        driver_info.update({DriverInfo.license: lic_no})
    if verf_name:
        driver_info.update({DriverInfo.verf_name: verf_name})
    if verf_rel:
        driver_info.update({DriverInfo.verf_rel: verf_rel})
    if verf_ph:
        driver_info.update({DriverInfo.verf_ph: verf_ph})
    if pan_no:
        driver_bank.update({DriverBank.pan_no: pan_no})
    if ifsc:
        driver_bank.update({DriverBank.ifsc: ifsc})
    if acc_no:
        driver_bank.update({DriverBank.acc_no: acc_no})
    if fname or lname:
        driver_user = driver_entry.first().user
        user_entry = db.session.query(Users).filter(Users.id == driver_user)
        if fname:
            user_entry.update({Users.fname: html.escape(fname.strip())})
        if lname:
            user_entry.update({Users.lname: html.escape(lname.strip())})

    # Update extras with height and weight
    existing_extras = driver_info.first().extras or {}
    updated_extras = {
        **existing_extras,
        "height": round(height_cm, 2),
        "weight": weight
    }
    driver_info.update({DriverInfo.extras: updated_extras})

    admin_log = AdminLog(get_jwt_identity(), 'Driver Account Info change', json.dumps(str(request.form)))
    db.session.add(admin_log)
    try:
        db.session.commit()
        driver_data = {
            'driver_id': driver_id,
        }
        driver_det = db.session.query(Users, Drivers) \
        .filter(Users.id == Drivers.user, Drivers.id == driver_id) \
        .first()
        live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[0].region, channel= 'update_driver')
    except Exception as exc:
        print("Exception in updating details:", str(exc))
        return jsonify({'success': -1}), 200
    return jsonify({'success': 1}), 200

@admin.route('/api/admin/allocate_driver', methods=['POST'])
@jwt_required()
def allocate():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -1}), 201
    # Get the driver
    try:
        driver = db.session.query(Users, Drivers).filter(Users.id == Drivers.user).filter(Drivers.approved == 1). \
            filter(Drivers.id == request.form['driver_id']).first()
        # add handler if driver is not found
        if driver is None:
            return jsonify({'success': -2}), 201
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201
    # Get booking info
    booking_id = int(request.form['booking_id'])
    try:
        with db.session.begin_nested():
            booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).with_for_update().first()
            orig_driver = booking.driver
            book_user = booking.user
        # add handler if booking is not found (unlikely)
    except Exception as e:
        print(e)
        return jsonify({'success': -3}), 201
    if booking.valid < -3:
        print("Could not accept: Booking is cancelled by user")
        return {'success': -6, "message": "Booking is cancelled by user"}
    if booking.valid == Bookings.CANCELLED_USER:
        db.session.query(Bookings).filter(Bookings.id == booking_id).update({Bookings.cancelled_dt: None,
                                                                            Bookings.did: "-1",
                                                                            Bookings.did_release: False})
        db.session.commit()
    # Get corresponding book pending entry
    try:
        admin_id = get_jwt_identity()
        book_code = booking.code
        pending_entry = db.session.query(BookPending).filter(BookPending.book_id == booking.id). \
                            filter(BookPending.driver == driver[1].id).first()
        if pending_entry:
            ba = BookingAlloc(booking.id, driver[1].id, admin_id,
                              phase=pending_entry.phase,
                              score=pending_entry.score)
        else:
            ba = BookingAlloc(booking.id, driver[1].id, admin_id)
        db.session.add(ba)
        db.session.commit()
        book_pricing = db.session.query(BookPricing).filter(BookPricing.book_id == booking.id).first()
        if not book_pricing:
            return jsonify({'success': -42}), 201
        # update all book pending entries for that booking
        # update the booking entry
        try:
            db.session.query(BookPending).filter(BookPending.book_id == booking.id).delete()
            if booking.type < BookingParams.TYPE_C24:
                # unique did no
                cust_current_bookings = db.session.query(Bookings).filter(Bookings.user == book_user). \
                                                                    filter(Bookings.did_release == False).all()
                driver_current_bookings = db.session.query(Bookings).filter(Bookings.driver == driver[1].id). \
                                                                    filter(Bookings.did_release == False).all()
                driver_did = []
                cust_did = []
                for cust_book in cust_current_bookings:
                    cust_did.append(cust_book.did)
                for drv_book in driver_current_bookings:
                    driver_did.append(drv_book.did)
                unique_did = assign_unique_did(cust_did, driver_did)
                Bookings.query.filter(Bookings.id == booking.id).update({Bookings.did: unique_did})
                Bookings.query.filter(Bookings.id == booking.id).update({Bookings.did_release: False})
                # get book pending entry for that driver
            db.session.query(Bookings).filter(Bookings.id == booking.id).update({Bookings.driver: driver[1].id,
                                                                                Bookings.estimate: book_pricing.estimate,
                                                                                Bookings.estimate_pre_tax: book_pricing.est_pre_tax,
                                                                                Bookings.valid: 1,
                                                                                Bookings.insurance_cost: book_pricing.insurance_ch})
            # log this action
            admin_log = AdminLog(get_jwt_identity(), 'manual-assign', json.dumps(str(request.form)))
            db.session.add(admin_log)
            db.session.commit()
            # firebase
            driver_user = driver[0].id
            driver_name = driver[0].get_name()
            driver_id = driver[1].id
            target_user = db.session.query(Users).filter(Users.id == booking.user).first()
            if booking.type < BookingParams.TYPE_C24: #b2c

                _update_user_pending(booking.user)
                _update_driver_pending(booking.driver)
                if orig_driver != booking.driver and orig_driver != 1:
                    _update_driver_pending(orig_driver)

                try:
                        fb_db.collection(u'book_pending').document(str(driver_id)).update(
                            {str(booking.id): firestore.DELETE_FIELD})
                        fb_db.collection(u'trip_set').document(str(target_user.id)).set(
                            {str(booking.id): driver_name}, merge=True)

                except Exception:
                    pass
            if booking.type == BookingParams.TYPE_ZOOMCAR:
                try:
                    if _get_zoomcar_booking_state(booking.id) != ZoomcarBookings.STATUS_ACCEPTED:
                        _zoomcar_change_state(ZOOMCAR_STATE_CREATED, booking.id)
                        _zoomcar_change_state(ZOOMCAR_STATE_ACCEPTED, booking.id)
                        _mark_zoomcar_booking_accepted(booking.id)
                    _zoomcar_change_state(ZOOMCAR_STATE_ASSIGNED, booking.id)
                except Exception as e:
                    print("Could not set state: error %s" % str(e))
            # sms
            user_mobile = _sms.COUNTRY_CODE_IN + str(target_user.mobile)
            start_time = datetime(year=booking.startdate.year, month=booking.startdate.month,
                                  day=booking.startdate.day, hour=booking.starttime.hour,
                                  minute=booking.starttime.minute, second=booking.starttime.second)
            start_time_ist = start_time + _sms.IST_OFFSET_TIMEDELTA
            msg_content = "Hi " + target_user.get_name() + "! Your trip with DRIVERS4ME (ID: " + book_code \
                          + ") has been assigned to " + driver_name
            if booking.type == BookingParams.TYPE_C24:
                msg_content = "A Cars24 trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            elif booking.type == BookingParams.TYPE_OLX:
                msg_content = "An OLX trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            elif booking.type == BookingParams.TYPE_ZOOMCAR:
                msg_content = "A Zoomcar trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            elif booking.type == BookingParams.TYPE_CARDEKHO:
                msg_content = "A CarDekho trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            elif booking.type == BookingParams.TYPE_BHANDARI:
                msg_content = "A Bhandari trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
                send_booking_confirm_sms("Bhandari", booking_id, driver_user)
            elif booking.type == BookingParams.TYPE_PRIDEHONDA:
                msg_content = "A Pride Honda trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
                send_booking_confirm_sms("Pride Honda", booking_id, driver_user)
            elif booking.type == BookingParams.TYPE_REVV:
                try:
                    shift = db.session.query(RevvBookings).filter(RevvBookings.ref == booking.id).first().shift
                    if shift == RevvBookings.DAY_SHIFT:
                        shift_text = " DAY shift"
                    else:
                        shift_text = " NIGHT shift"
                except Exception as e:
                    shift = -1
                    shift_text = ""
                msg_content = "A Revv duty on " + \
                          start_time_ist.strftime("%d/%m/%Y") + shift_text + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + "). The reporting time is: " +  start_time_ist.strftime("%I:%M %p")
            elif booking.type == BookingParams.TYPE_GUJRAL:
                try:
                    shift = db.session.query(GujralBookings).filter(GujralBookings.ref == booking.id).first().shift
                    if shift == GujralBookings.DAY_SHIFT:
                        shift_text = " DAY shift"
                    else:
                        shift_text = " NIGHT shift"
                except Exception as e:
                    shift = -1
                    shift_text = ""
                msg_content = "A Gujral Car Rentals duty on " + \
                          start_time_ist.strftime("%d/%m/%Y") + shift_text + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + "). The reporting time is: " +  start_time_ist.strftime("%I:%M %p")
            elif booking.type == BookingParams.TYPE_ZOOMCAR:
                msg_content = "A Zoomcar trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            elif booking.type == BookingParams.TYPE_CARDEKHO:
                msg_content = "A Cardekho trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            elif booking.type == BookingParams.TYPE_BHANDARI:
                msg_content = "A Bhandari trip with DRIVERS4ME booked for " + \
                          start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " has been assigned to " + driver_name \
                          + " (" + str(driver[0].mobile) + ")"
            send_slack_msg(1, msg_content)
            if booking.type < BookingParams.TYPE_C24:
                send_live_update_of_booking(booking_id, booking.region)
                send_fcm_msg(target_user.id, title="Drivers4Me", smalltext="Booking confirmed!", bigtext=msg_content)
            if booking.type >= BookingParams.TYPE_C24:
                user_name = BookingParams.get_type_to_str(booking.type)
            else:
                user_name = target_user.get_name()
            msg_content_json = {"booking-type-str": user_name,
                                "booking-start-dt": str(start_time_ist.strftime("%I:%M %p %d/%m/%Y"))}
            resp = _sms.send_msg_flow(str(driver[0].mobile), _sms.FLOWS['admin-driver-alloc'], msg_content_json)


            if resp:
                return jsonify({'success': 1, 'msg': 1}), 200
            else:
                return jsonify({'success': 1, 'msg': 0}), 200
        except Exception as e:
            print("Allocate error", e)
            return jsonify({'success': -7}), 201
    except Exception as e:
        print(e)
        return jsonify({'success': -4}), 201
    return jsonify({'success': -1})

# show drivers list to whom booking is sent
@admin.route('/api/admin/show_book_pending', methods=['POST'])
@jwt_required()
def show_pending():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -1}), 201
    try:
        book_pending = db.session.query(BookPending, Drivers, Users).filter(
            BookPending.book_id == request.form['booking_id']). \
            filter(BookPending.valid.in_([BookPending.SUPPRESSED, BookPending.BROADCAST])).filter(BookPending.driver == Drivers.id). \
            filter(Drivers.user == Users.id).all()
        if not book_pending:
            return jsonify({'success': -2}), 201
        pricing = db.session.query(BookPricing).filter(BookPricing.book_id == request.form['booking_id']).first()
        if not pricing:
            price = 0
        else:
            price = pricing.estimate
        result_json = []
        for pending_driver in book_pending:
            pending_entry = PendingDriver(dname=pending_driver[2].get_name(),
                                          dmob=pending_driver[2].mobile,
                                          estimate=price,
                                          driver_id=pending_driver[1].id,
                                          suppressed=pending_driver[0].valid)
            result_json.append(jsonpickle.encode(pending_entry))
        if len(result_json) <= 0:
            return jsonify({'success': -1, "book_pending": str(book_pending)})
        else:
            return jsonify({'success': 1, 'data': result_json}), 200

    except Exception as ex:
        print(ex)
        return jsonify({'success': -2}), 201


# # change book start
# @admin.route('/api/admin/change_book_start', methods=['POST'])
# @jwt_required()
# def change_book_start():
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'success': -1}), 401
#     if not complete(request.form, ['booking_id', 'starttime']):
#         return jsonify({'success': -1}), 201
#     print(request.form['booking_id'] + " " + request.form['starttime'])
#     try:
#         book = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).first()
#         try:
#             search_entry = db.session.query(DriverSearch).filter(booking.search_key == DriverSearch.id).first()
#         except Exception as e:
#             print(ex)
#             return jsonify({'success': -4}), 201
#         try:
#             driver_base_location = db.session.query(DriverBaseLoc).filter(DriverBaseLoc.driver_id == driver[1].id).first()
#         except Exception as e:
#             print(e)
#             return jsonify({'success': -5}), 201
#         previous = str(book.startdate) + " " + str(book.starttime)
#         #re-calculate fare
#         # Find the distance from Booking location to driver's base location and normalize it
#         distance = normalize_distance(
#             distance_on_earth(driver_base_location.homelat, driver_base_location.homelong, book.lat,
#                                 book.long))
#         end_time = request.form['starttime'] + timedelta(hours = int(datetime.strptime(book.dur, '%H')))
#         if book.type == BookingParams.TYPE_ROUNDTRIP or book.type == BookingParams.TYPE_ONEWAY:
#             total_fare, base_fare, night_fare, dist_fare, booking_ch, car_fare, surcharge = Price.get_price(
#                 book.type, book.dur,
#                 datetime.strptime(request.form['starttime'], '%H:%M:%S'),
#                 datetime.strptime(end_time, '%H:%M:%S'),
#                 distance,
#                 datetime.strptime(request.form['starttime'], '%d-%m-%Y'),
#                 datetime.strptime(end_time, '%d-%m-%Y'))
#         elif book.type == BookingParams.TYPE_OUTSTATION:

#         # update time
#         try:
#             db.session.query(Bookings).filter(Bookings.id == book.id). \
#                 update({Bookings.startdate: datetime.strptime(request.form['starttime'], '%d-%m-%Y'),
#                         Bookings.starttime: datetime.strptime(request.form['starttime'], '%H:%M:%S')})
#             admin_log = AdminLog(get_jwt_identity(), 'book start change', json.dumps(str(request.form)) +
#                                  str(previous) + "  -->  " + str(request.form['starttime']))
#             admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
#             db.session.add(admin_log)
#             db.session.commit()
#             send_slack_msg(2, admin_name + " changed book time for " + str(book.id) + " to " + str(request.form['starttime']))
#             return jsonify({'success': 1}), 200
#         except Exception as ex:
#             print(ex)
#             return jsonify({'success': -3}), 201

#     except Exception as e:
#         print(e)
#         return jsonify({'success': -2}), 201

# change book comment
@admin.route('/api/admin/change_book_comment', methods=['POST'])
@jwt_required()
def change_book_comment():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -1}), 201
    try:
        booking_entry = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).first()
        previous = booking_entry.comment

        admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
        comment = html.escape(request.form['comment'])
        timest = (datetime.utcnow() + _sms.IST_OFFSET_TIMEDELTA).strftime("%d %b %Y %I:%M %p")
        if comment:
            comment += " --" + admin_name + " (" + timest + ")"
            print(comment)
        # update time
        db.session.query(Bookings).filter(Bookings.id == booking_entry.id). \
            update({Bookings.comment: comment})
        admin_log = AdminLog(get_jwt_identity(), 'book comment change', json.dumps(str(request.form)) +
                             str(previous) + "  -->  " + str(request.form['comment']))
        db.session.add(admin_log)
        db.session.commit()

        if not comment:
            send_slack_msg(2, admin_name + " deleted comment for " + str(booking_entry.id))
        return jsonify({'success': 1}), 200
    except Exception as ex:
        print(ex)
        return jsonify({'success': -2}), 201

@admin.route('/admin/available/<int:booking_id>',methods=['POST','GET'])
@jwt_required()
def available_driver_map(booking_id):
    '''Doc - https://docs.google.com/document/d/14DW3jJpJoL3n0AqwHqsF777ENSmC_elXUCaPLJa-GqM/edit?addon_store'''
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        if(booking==None):
            if request.method == 'POST':
                return jsonify({'success': -1,'message':"No Booking found"}), 201
            else:
                 return render_template('track_driver_notfound.html',message="No Booking Found")
        bookendtime=booking.endtime
        bookstartdate=booking.startdate
        bookstarttime=booking.starttime
        bookdest=db.session.query(BookDest).filter(BookDest.book_id == booking_id).first()
        if bookdest is not None:
            book_dest_lat=bookdest.lat
            book_dest_lng=bookdest.lng
            dist_with_book_start=distance_on_earth(booking.lat,booking.long,book_dest_lat,book_dest_lng)
        else:
            book_dest_lat=0
            book_dest_lng=0
            dist_with_book_start=0
        ''' Params to Change from admin_params.py'''
        max_drivers=AdminParams.MAX_DRIVERS_TO_SHOW
        max_time_min=AdminParams.MAX_TIME_MIN_ALLOWED
        max_distance_km=AdminParams.MAX_DISTANCE_KM_RANGE_ALLOWED
        refresh_time=AdminParams.REFRESH_TIME_MS
        if booking.valid<0:
            if request.method == 'POST':
                return jsonify({'success': -1,'message':"Booking Invalid"}), 201
            else:
                return render_template('track_driver_notfound.html',message="Booking Cancelled or Invalid")
        book_lat=booking.lat
        book_long=booking.long
        time_threshold = datetime.utcnow() - timedelta(minutes=max_time_min)
        nearby_driver_ids = (db.session.query(DriverLoc,Users,Drivers).join(Drivers, DriverLoc.driver_id == Drivers.id)
                            .join(Users, Drivers.user == Users.id)
                            .filter(
        DriverLoc.timestamp >= time_threshold,
        Drivers.approved >= 1,
        Drivers.available == 1)
        .limit(max_drivers)
        .all())
        filtered_drivers = []
        for driverLoc, users, drivers in nearby_driver_ids:
            '''List of last Books of driver which are not registered in Trip'''
            last_books=(db.session.query(Bookings)
                    .filter(Bookings.driver==drivers.id)
                    .filter(Bookings.valid==1)
                    .filter(
                        ~db.session.query(Trip)
                        .filter(Trip.book_id == Bookings.id)
                        .exists())
                    .order_by(desc(Bookings.timestamp)).all())
            clashes=False
            for last_book in last_books:
                last_book_start_time=last_book.starttime
                last_book_end_time=last_book.endtime
                if last_book.startdate==bookstartdate:
                    '''Checking Clashing Condition Refer Doc - '''
                    if last_book_end_time >= last_book_start_time:
                        if (bookstarttime <= last_book_start_time <= bookendtime) or \
                        (bookstarttime <= last_book_end_time <= bookendtime):
                            clashes=True
                    else:
                        last_book_end_midtime = datetime.strptime('23:59:59', '%H:%M:%S').time()
                        bookstart_midtime = datetime.strptime('00:00:00', '%H:%M:%S').time()
                        if (last_book_start_time <= bookstarttime <= last_book_end_midtime) or \
                            (bookstart_midtime <= bookendtime <= last_book_end_time):
                            clashes=True
            '''Trips with Endtime None'''
            on_trip_clash = db.session.query(Trip, Bookings).join(Bookings, Bookings.id == Trip.book_id) \
            .filter(Bookings.driver == drivers.id).filter(Trip.endtime == None).first()
            if(on_trip_clash):
                '''Condition to consider driver refer Doc '''
                if(on_trip_clash[1].enddate==bookstartdate and on_trip_clash[1].endtime<bookstarttime):
                    on_trip_clash=False
            if not on_trip_clash and not clashes:
                trip = (db.session.query(Trip, Bookings)
                        .join(Bookings, Bookings.id == Trip.book_id)
                        .filter(Bookings.driver == drivers.id).order_by(desc(Trip.starttime))
                        .first())
                if(trip):
                    if(trip[0].endtime==None):
                        lasttrip="Ongoing"
                    else:
                        lasttrip=trip[0].endtime.astimezone(pytz.timezone('Asia/Kolkata')).strftime('%Y-%m-%d')
                else:
                    lastrip="None"
                filtered_drivers.append((driverLoc, users, drivers,lasttrip))
        nearby_driver_info = [{ "driver_id": driverLoc.driver_id,
                "name": Users.get_name(),
                "mobile": Users.mobile,
                "last_updated_time":driverLoc.timestamp.astimezone(pytz.timezone('Asia/Kolkata')).strftime("%H:%M:%S"),
                "distancebook": distance_on_earth(book_lat, book_long,driverLoc.lat, driverLoc.lng),
                "distancedest": distance_on_earth(book_dest_lat, book_dest_lng,driverLoc.lat, driverLoc.lng) if book_dest_lat!=0 else 0,
                "lat": driverLoc.lat,
                "lng": driverLoc.lng,
                "booklat":book_lat,
                "booklng":book_long,
                "bookdestlat":book_dest_lat,
                "bookdestlng":book_dest_lng,
                "lasttrip":lasttrip}
                for driverLoc, Users, drivers,lasttrip in filtered_drivers
                if (distance_on_earth(book_lat, book_long, driverLoc.lat, driverLoc.lng) <= max_distance_km)
                or (bookdest is not None and distance_on_earth(book_dest_lat, book_dest_lng, driverLoc.lat, driverLoc.lng) <= max_distance_km)]
        if request.method == 'POST':
            return jsonify({'success': True, 'drivers_info': nearby_driver_info})
        else:
            return render_template('track_driver.html',nearby_driver_info=nearby_driver_info,booking_id=booking_id,dist_with_book_start=dist_with_book_start,refresh_time=refresh_time)

    except Exception as ex:
        print(ex)
        return jsonify({'success': -2}), 201

@admin.route('/api/admin/available/<string:code>',methods=['POST','GET'])
@jwt_required()
def available_driver_map_code(code):
    '''Doc - https://docs.google.com/document/d/14DW3jJpJoL3n0AqwHqsF777ENSmC_elXUCaPLJa-GqM/edit?addon_store'''
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        booking = db.session.query(Bookings).filter(Bookings.code == code).first()
        if(booking==None):
            if request.method == 'POST':
                return jsonify({'success': -1,'message':"No Booking found"}), 201
            else:
                 return render_template('track_driver_notfound.html',message="No Booking Found")
        bookendtime=booking.endtime
        bookstartdate=booking.startdate
        bookstarttime=booking.starttime
        bookdest=db.session.query(BookDest).filter(BookDest.book_id == booking.id).first()
        if bookdest is not None:
            book_dest_lat=bookdest.lat
            book_dest_lng=bookdest.lng
            dist_with_book_start=distance_on_earth(booking.lat,booking.long,book_dest_lat,book_dest_lng)
        else:
            book_dest_lat=0
            book_dest_lng=0
            dist_with_book_start=0
        ''' Params to Change from admin_params.py'''
        max_drivers=AdminParams.MAX_DRIVERS_TO_SHOW
        max_time_min=AdminParams.MAX_TIME_MIN_ALLOWED
        max_distance_km=AdminParams.MAX_DISTANCE_KM_RANGE_ALLOWED
        refresh_time=AdminParams.REFRESH_TIME_MS
        if booking.valid<0:
            if request.method == 'POST':
                return jsonify({'success': -1,'message':"Booking Invalid"}), 201
            else:
                return render_template('track_driver_notfound.html',message="Booking Cancelled or Invalid")
        book_lat=booking.lat
        book_long=booking.long
        time_threshold = datetime.utcnow() - timedelta(minutes=max_time_min)
        nearby_driver_ids = (db.session.query(DriverLoc,Users,Drivers).join(Drivers, DriverLoc.driver_id == Drivers.id)
                            .join(Users, Drivers.user == Users.id)
                            .filter(
        DriverLoc.timestamp >= time_threshold,
        Drivers.approved >= 1,
        Drivers.available == 1)
        .limit(max_drivers)
        .all())
        filtered_drivers = []
        for driverLoc, users, drivers in nearby_driver_ids:
            '''List of last Books of driver which are not registered in Trip'''
            last_books=(db.session.query(Bookings)
                    .filter(Bookings.driver==drivers.id)
                    .filter(Bookings.valid==1)
                    .filter(
                        ~db.session.query(Trip)
                        .filter(Trip.book_id == Bookings.id)
                        .exists())
                    .order_by(desc(Bookings.timestamp)).all())
            clashes=False
            for last_book in last_books:
                last_book_start_time=last_book.starttime
                last_book_end_time=last_book.endtime
                if last_book.startdate==bookstartdate:
                    '''Checking Clashing Condition Refer Doc - '''
                    if last_book_end_time >= last_book_start_time:
                        if (bookstarttime <= last_book_start_time <= bookendtime) or \
                        (bookstarttime <= last_book_end_time <= bookendtime):
                            clashes=True
                    else:
                        last_book_end_midtime = datetime.strptime('23:59:59', '%H:%M:%S').time()
                        bookstart_midtime = datetime.strptime('00:00:00', '%H:%M:%S').time()
                        if (last_book_start_time <= bookstarttime <= last_book_end_midtime) or \
                            (bookstart_midtime <= bookendtime <= last_book_end_time):
                            clashes=True
            '''Trips with Endtime None'''
            on_trip_clash = db.session.query(Trip, Bookings).join(Bookings, Bookings.id == Trip.book_id) \
            .filter(Bookings.driver == drivers.id).filter(Trip.endtime == None).first()
            if(on_trip_clash):
                '''Condition to consider driver refer Doc '''
                if(on_trip_clash[1].enddate==bookstartdate and on_trip_clash[1].endtime<bookstarttime):
                    on_trip_clash=False
            if not on_trip_clash and not clashes:
                trip = (db.session.query(Trip, Bookings)
                        .join(Bookings, Bookings.id == Trip.book_id)
                        .filter(Bookings.driver == drivers.id).order_by(desc(Trip.starttime))
                        .first())
                if(trip):
                    if(trip[0].endtime==None):
                        lasttrip="Ongoing"
                    else:
                        lasttrip=trip[0].endtime.astimezone(pytz.timezone('Asia/Kolkata')).strftime('%Y-%m-%d')
                else:
                    lastrip="None"
                filtered_drivers.append((driverLoc, users, drivers,lasttrip))
        nearby_driver_info = [{ "driver_id": driverLoc.driver_id,
                "name": Users.get_name(),
                "mobile": Users.mobile,
                "last_updated_time":driverLoc.timestamp.astimezone(pytz.timezone('Asia/Kolkata')).strftime("%H:%M:%S"),
                "distancebook": distance_on_earth(book_lat, book_long,driverLoc.lat, driverLoc.lng),
                "distancedest": distance_on_earth(book_dest_lat, book_dest_lng,driverLoc.lat, driverLoc.lng) if book_dest_lat!=0 else 0,
                "lat": driverLoc.lat,
                "lng": driverLoc.lng,
                "booklat":book_lat,
                "booklng":book_long,
                "bookdestlat":book_dest_lat,
                "bookdestlng":book_dest_lng,
                "lasttrip":lasttrip}
                for driverLoc, Users, drivers,lasttrip in filtered_drivers
                if (distance_on_earth(book_lat, book_long, driverLoc.lat, driverLoc.lng) <= max_distance_km)
                or (bookdest is not None and distance_on_earth(book_dest_lat, book_dest_lng, driverLoc.lat, driverLoc.lng) <= max_distance_km)]
        if request.method == 'POST':
            return jsonify({'success': True, 'drivers_info': nearby_driver_info})
        else:
            return render_template('track_driver.html',nearby_driver_info=nearby_driver_info,booking_id=booking.id,dist_with_book_start=dist_with_book_start,refresh_time=refresh_time)

    except Exception as ex:
        print(ex)
        return jsonify({'success': -2}), 201

@admin.route('/api/admin/change_cancellation_reason', methods=['POST'])
@jwt_required()
def change_cancellation_reason():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['book_cancel_id','new_reason','new_reason_detail']):
        return jsonify({'success': -1,"message":"form details incomplete"}), 201
    user_id = get_jwt_identity()
    book_cancel_id=int(request.form["book_cancel_id"])
    bookcancel = db.session.query(BookingCancelled).filter(BookingCancelled.id == book_cancel_id).first()
    booking_id=bookcancel.booking
    new_reason=int(request.form["new_reason"])
    new_reason_detail=str(request.form["new_reason_detail"])
    try:
        is_reversed = db.session.query(BookingCancelled).\
            filter(BookingCancelled.id == book_cancel_id).\
            filter(BookingCancelled.cancel_reversed == True).first()
        if is_reversed:
            return jsonify({'success': -1, 'message': 'Booking already reversed.'}), 400
        booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        cb = db.session.query(BookingCancelled).filter(BookingCancelled.booking == booking.id).filter(BookingCancelled.id == book_cancel_id)
        cb1=cb.first()
        trip = fetch_booking_trip(booking_id).first()
        is_ontrip = trip is not None
        waiver = False
        starttime = datetime(booking.startdate.year, booking.startdate.month,booking.startdate.day, booking.starttime.hour,booking.starttime.minute,
                             booking.starttime.second)
        if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            updated_penalty,level = PriceOutstation.get_cancel_ch(cb1.timestamp, starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=new_reason, is_ontrip=is_ontrip)                  
        else:
            updated_penalty,level = Price.get_cancel_ch(cb1.timestamp, starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=new_reason,  is_ontrip=is_ontrip)
        updated_user_fine, updated_driver_fine = updated_penalty
        cancelled_driver=cb1.did
        if cb1.utransid is not None:
            old_user_cancel = db.session.query(UserTrans).filter(UserTrans.id == cb1.utransid).first()
            old_user_fine = abs(int(old_user_cancel.amount / 100))
        else:
            old_user_fine = 0
        if cb1.dtransid is not None:
            old_driver_cancel = db.session.query(DriverTrans).filter(DriverTrans.id == cb1.dtransid).first()
            old_driver_fine = abs(int(old_driver_cancel.amount / 100))
        else:
            old_driver_fine = 0
        ut_id=None
        ut_id2=None
        dt_id=None
        dt_id2=None
        if (new_reason in BookingCancelled.WAIVER):
            updated_user_fine = 0
            updated_driver_fine = 0
            waiver = True
        if updated_user_fine>0:
            db.session.query(Users).filter(Users.id == cb1.uid).update({Users.credit: Users.credit + old_user_fine - updated_user_fine})
            db.session.query(UserCancelled).filter(UserCancelled.booking == cb1.booking).update({UserCancelled.penalty:updated_user_fine})
            if old_user_fine>0:
                ut = UserTrans(uid=cb1.uid, amt=(100*old_user_fine), method="Cancellation reversal for #" + str(booking.code),
                               status=UserTrans.COMPLETED)
                db.session.add(ut)
                ut_id=ut.id
            ut2 = UserTrans(uid=cb1.uid, amt=(-1)*(100*updated_user_fine), method="Cancellation charges for #" + str(booking.code),
                            status=UserTrans.COMPLETED)
            db.session.add(ut2)
            ut_id2=ut2.id
        else:
            db.session.query(Users).filter(Users.id == cb1.uid).update({Users.credit: Users.credit + old_user_fine})
            if old_user_fine>0:
                ut = UserTrans(uid=cb1.uid, amt=(100*old_user_fine), method="Cancellation reversal for #" + str(booking.code),
                               status=UserTrans.COMPLETED)
                db.session.add(ut)
                ut_id=ut.id
            db.session.query(UserCancelled).filter(UserCancelled.user == cb1.uid).update({UserCancelled.penalty:0})
        if updated_driver_fine>0:
            db.session.query(DriverCancelled).filter(DriverCancelled.id == cancelled_driver).update({DriverCancelled.penalty:updated_driver_fine})
            if old_driver_fine>0:
                wallet_before= old_driver_cancel.wallet_before
                wallet_after= old_driver_cancel.wallet_after
                previous_wallet_ded= abs(wallet_after-wallet_before)
                withdrawable_before= old_driver_cancel.withdrawable_before
                withdrawable_after= old_driver_cancel.withdrawable_after
                previous_withdrawable_ded= abs(withdrawable_after-withdrawable_before)
                dt = DriverTrans(cancelled_driver, old_driver_fine*100,
                                        wall_a=wallet_after+previous_wallet_ded, wall_b=wallet_after,
                                        with_a=withdrawable_after+previous_withdrawable_ded, with_b=withdrawable_after,
                                            method="Reversal: Booking %s" % str(booking.code),
                                        status=DriverTrans.COMPLETED, stop=True
                                        )
                db.session.add(dt)
                dt_id=dt.id
                db.session.query(DriverDetails).filter(DriverDetails.driver_id == cancelled_driver). \
                            update({DriverDetails.owed: DriverDetails.owed - old_driver_fine,DriverDetails.wallet:DriverDetails.wallet+ previous_wallet_ded,
                                    DriverDetails.withdrawable: DriverDetails.withdrawable + previous_withdrawable_ded})
            details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == cancelled_driver)
            driver_details = details.first()
            wallet, withdrawable = compute_driver_wallet(driver_details, updated_driver_fine)
            dt2 = DriverTrans(cancelled_driver, -updated_driver_fine*100,
                                        wall_a=wallet, wall_b=driver_details.wallet,
                                        with_a=withdrawable, with_b=driver_details.withdrawable,
                                            method="Cancellation: Booking %s" % str(booking.code),
                                        status=DriverTrans.COMPLETED, stop=True
                                        )
            db.session.add(dt2)
            dt_id2=dt2.id
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == cancelled_driver). \
                            update({DriverDetails.owed: DriverDetails.owed + updated_driver_fine,
                                    DriverDetails.wallet:wallet,
                                    DriverDetails.withdrawable: withdrawable})
        else:
            previous_withdrawable_ded=0
            previous_wallet_ded=0
            if old_driver_fine>0:
                wallet_before= old_driver_cancel.wallet_before
                wallet_after= old_driver_cancel.wallet_after
                previous_wallet_ded=  previous_wallet_ded+abs(wallet_after-wallet_before)
                withdrawable_before= old_driver_cancel.withdrawable_before
                withdrawable_after= old_driver_cancel.withdrawable_after
                previous_withdrawable_ded=previous_withdrawable_ded+ abs(withdrawable_after-withdrawable_before)
                dt = DriverTrans(cancelled_driver, old_driver_fine*100,
                                        wall_a=wallet_after+previous_wallet_ded, wall_b=wallet_after,
                                        with_a=withdrawable_after+previous_withdrawable_ded, with_b=withdrawable_after,
                                            method="Reversal: Booking %s" % str(booking.code),
                                        status=DriverTrans.COMPLETED, stop=True)
                db.session.add(dt)
                dt_id=dt.id
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == cancelled_driver). \
                            update({DriverDetails.owed: DriverDetails.owed - old_driver_fine,DriverDetails.wallet:DriverDetails.wallet+ previous_wallet_ded,
                                    DriverDetails.withdrawable: DriverDetails.withdrawable + previous_withdrawable_ded})
            db.session.query(DriverCancelled).filter(DriverCancelled.id == cancelled_driver).update({DriverCancelled.penalty:0})

        if(old_user_fine==0 and old_driver_fine==0):
            bc1 = BookingCancelled(user=user_id, cancel_source=BookingCancelled.SRC_ADMIN, booking= booking.id, uid=booking.user,
                                          did=cancelled_driver, penalty_user=updated_user_fine, penalty_driver=updated_driver_fine, rsn= new_reason,
                                          reason_detail= new_reason_detail,utransid=ut_id2,dtransid=dt_id2)
            bc = BookingCancelled(user=user_id, cancel_source=BookingCancelled.SRC_ADMIN, booking= booking.id, uid=booking.user,
                                          did=cancelled_driver, penalty_user=0, penalty_driver= 0,
                                          rsn=BookingCancelled.RSN_REVERSE_CANCELLATION, reason_detail="Reversed Cancellation")
            db.session.add(bc)
            db.session.add(bc1)
            cb.update({BookingCancelled.cancel_reversed:True})
            try:
                if(updated_user_fine>0):
                    send_fcm_msg(booking.user, title="Cancellation of booking #" + str(booking.code),
                                smalltext="You were charged an updated penalty of ₹" + str(updated_user_fine) + ".",
                                bigtext="You were charged an updated penalty of ₹" + str(updated_user_fine) + " for cancelling booking #" + str(booking.code))
                if(updated_driver_fine>0):
                    send_fcm_msg_driver(cancelled_driver, title="Cancellation of booking #" + str(booking.code),
                                smalltext="You were charged an updated penalty of ₹" + str(updated_driver_fine) + ".",
                                bigtext="You were charged an updated penalty of ₹" + str(updated_driver_fine) + " for cancelling booking #" + str(booking.code))
            except Exception as e:
                pass
        elif(updated_user_fine==0 and updated_driver_fine==0):
            bc = BookingCancelled(user=user_id, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid= booking.user,
                                          did=cancelled_driver, penalty_user=(-1)*old_user_fine, penalty_driver=(-1)*old_driver_fine,
                                          rsn=BookingCancelled.RSN_REVERSE_CANCELLATION, reason_detail= "Reversed Cancellation", utransid=ut_id, dtransid=dt_id)
            bc1 = BookingCancelled(user=user_id, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid=booking.user,
                                          did=cancelled_driver, penalty_user=updated_user_fine, penalty_driver= updated_driver_fine,
                                          rsn= new_reason,reason_detail= new_reason_detail)
            db.session.add(bc)
            db.session.add(bc1)
            cb.update({BookingCancelled.cancel_reversed:True})
            try:
                if(old_user_fine>0):
                    send_fcm_msg(booking.user, title="Cancellation of booking #" + str(booking.code),
                                smalltext="You recieved a refund for the previous penalty of ₹" + str(old_user_fine) + ".",
                                bigtext="You recieved a refund for the previous penalty of ₹" + str(old_user_fine) + " for cancelling booking #" + str(booking.code))
                if(old_driver_fine>0):
                    send_fcm_msg_driver(cancelled_driver, title="Cancellation of booking #" + str(booking.code),
                                smalltext="You recieved a refund for the previous penalty of ₹" + str(old_driver_fine) + ".",
                                bigtext="You recieved a refund for the previous penalty of ₹" + str(old_driver_fine) + " for cancelling booking #" + str(booking.code))
            except Exception as e:
                pass
        else:
            bc1 = BookingCancelled(user=user_id, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid=booking.user,
                                          did=cancelled_driver, penalty_user=updated_user_fine, penalty_driver= updated_driver_fine,
                                          rsn= new_reason,reason_detail= new_reason_detail,utransid=ut_id2,dtransid=dt_id2)
            bc = BookingCancelled(user=user_id, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid=booking.user,
                                          did=cancelled_driver, penalty_user=(-1)*old_user_fine, penalty_driver=(-1)*old_driver_fine,
                                          rsn= BookingCancelled.RSN_REVERSE_CANCELLATION, reason_detail= "Reversed Cancellation", utransid=ut_id, dtransid=dt_id)
            db.session.add(bc)
            db.session.add(bc1)
            cb.update({BookingCancelled.cancel_reversed:True})
            try:
                if(updated_user_fine>0 and old_user_fine>0):
                    send_fcm_msg(booking.user, title="Cancellation of booking #" + str(booking.code),
                                smalltext="You recieved a refund for the previous penalty of ₹" + str(old_user_fine) +" and were charged an updated penalty of ₹" + str(updated_user_fine) + ".",
                                bigtext="You recieved a refund for the previous penalty of ₹" + str(old_user_fine)+" and were charged an updated penalty of ₹" + str(updated_user_fine) + \
                                " for cancelling booking #" + str(booking.code))
                if(old_driver_fine>0 and updated_driver_fine>0):
                    send_fcm_msg_driver(cancelled_driver, title="Cancellation of booking #" + str(booking.code),
                                 smalltext="You recieved a refund for the previous penalty of ₹" + str(old_driver_fine) +" and were charged an updated penalty of ₹" + str(updated_driver_fine) + ".",
                                bigtext="You recieved a refund for the previous penalty of ₹" + str(old_driver_fine)+" and were charged an updated penalty of ₹" + str(updated_driver_fine) + \
                                " for cancelling booking #" + str(booking.code))
            except Exception as e:
                pass
        try:
            db.session.commit()
            return jsonify({"success":1}),200
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': -1}),201
    except Exception as ex:
        print(ex)
        return jsonify({'success': -2}), 201

# Stop trip
@admin.route('/api/admin/stop_trip', methods=['POST'])
@jwt_required()
def admin_stop_trip():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2}), 201
    book = request.form['book_id']
    driver = db.session.query(Bookings, Drivers).filter(Bookings.id == book).filter(Drivers.id == Bookings.driver).first()
    if 'stop_time' not in request.form:
        d = datetime.utcnow()
    else:
        try:
            d = datetime.strptime(request.form['stop_time'], '%d-%m-%Y %H:%M:%S')
        except Exception:
            d = datetime.utcnow()

    # Log action for trip
    tl = TripLog(book, driver[1].user, get_jwt_identity(), TripLog.ACTION_STOP)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()

    if driver[0].type == BookingParams.TYPE_C24:
        b = db.session.query(C24Bookings).filter(C24Bookings.ref == book).first()
        return c24_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_OLX:
        b = db.session.query(OLXBookings).filter(OLXBookings.ref == book).first()
        return olx_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_ZOOMCAR:
        b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book).first()
        return zoomcar_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_REVV:
        b = db.session.query(RevvBookings).filter(RevvBookings.ref == book).first()
        return _revv_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_GUJRAL:
        b = db.session.query(GujralBookings).filter(GujralBookings.ref == book).first()
        return _gujral_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_CARDEKHO:
        b = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == book).first()
        return cardekho_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_BHANDARI:
        b = db.session.query(BhandariBookings).filter(BhandariBookings.ref == book).first()
        return bhandari_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_MAHINDRA:
        b = db.session.query(MahindraBookings).filter(MahindraBookings.ref == book).first()
        return mahindra_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_SPINNY:
        b = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == book).first()
        return spinny_stop_trip(b.id, driver[1].user, d)
    # elif driver[0].type == BookingParams.TYPE_REVV_V2:
    #     b = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == book).first()
    #     return revv_v2_stop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_PRIDEHONDA:
        b = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == book).first()
        return pridehonda_stop_trip(b.id, driver[1].user, d)
    return _stop_trip(book, driver[1].user, d)

# change book comment
@admin.route('/api/admin/broadcast_trip', methods=['POST'])
@jwt_required()
def broadcast_trip():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -1}), 201
    try:
        book_id = request.form['booking_id']
        booking_entry = db.session.query(Bookings).filter(Bookings.id == book_id).first()

        if not booking_entry:
            return jsonify({'success': -1}), 201

        cur_search = db.session.query(DriverSearch).filter(DriverSearch.id == booking_entry.search_key).first()
        pricing_entry = db.session.query(BookPricing).filter(BookPricing.book_id == book_id).first()
        pending_entries = db.session.query(BookPending).filter(BookPending.book_id == book_id). \
            filter(BookPending.valid == BookPending.SUPPRESSED)

        pending_list = pending_entries.all()
        pending_entries.update({BookPending.valid: BookPending.BROADCAST})

        doc_ref = fb_db.collection(u'book_pending')
        for entry in pending_list:
            available = db.session.query(Drivers).filter(Drivers.id == entry.driver).first().available
            if available:
                dt_ist = get_dt_ist(cur_search.date, cur_search.time)
                send_fcm_msg_driver(entry.driver, title="New booking!", smalltext="You have a new booking at " + str(dt_ist.strftime("%d/%m/%Y %I:%M %p")),
                                        bigtext="You have a new booking at " + str(dt_ist.strftime("%d/%m/%Y %I:%M %p")) + " with estimate of ₹" + str(pricing_entry.estimate) +
                                        ". Open app to accept trip.")

        admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
        send_slack_msg(3, admin_name + " broadcasted booking id #" + str(book_id))
        admin_log = AdminLog(get_jwt_identity(), 'trip broadcast', json.dumps(str(request.form)))
        db.session.add(admin_log)
        db.session.commit()

        return jsonify({'success': 1}), 200
    except Exception as ex:
        print(ex)
        return jsonify({'success': -2}), 201


# change trip start
@admin.route('/api/admin/change_trip_start', methods=['POST'])
@jwt_required()
def change_trip_start():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'starttime']):
        return jsonify({'success': -1}), 201
    book = int(request.form['booking_id'])
    driver = db.session.query(Bookings, Drivers).filter(Bookings.id == book).filter(Drivers.id == Bookings.driver).first()
    # Log action for trip
    tl = TripLog(book, driver[1].user, get_jwt_identity(), TripLog.ACTION_START_CHANGE)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    try:
        trip = db.session.query(Trip).filter(Trip.book_id == book).first()
        previous = trip.starttime
        # update time
        try:
            db.session.query(Trip).filter(Trip.book_id == trip.book_id). \
                update({Trip.starttime: datetime.strptime(request.form['starttime'], '%d-%m-%Y %H:%M:%S')})
            admin_log = AdminLog(get_jwt_identity(), 'trip start change', json.dumps(str(request.form)) +
                                 str(previous) + "  -->  " + str(request.form['starttime']))
            admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
            db.session.add(admin_log)
            db.session.commit()
            send_slack_msg(2, admin_name + " changed trip time for " + str(trip.book_id) + " to " + str(
                request.form['starttime']))

            if driver[0].type < BookingParams.TYPE_C24:
                send_live_update_of_booking( book, driver[0].region)

            return jsonify({'success': 1}), 200
        except Exception as ex:
            print(ex)
            return jsonify({'success': -3}), 201

    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201


# start trip (untested)
@admin.route('/api/admin/start_trip', methods=['POST'])
@jwt_required()
def start_trip():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -1}), 201
    book = request.form['booking_id']
    driver_booked = db.session.query(Bookings, Drivers).filter(Drivers.id == Bookings.driver). \
                    filter(Bookings.id == book).first()
    if 'start_time' not in request.form:
        d = datetime.utcnow()
    else:
        try:
            d = datetime.strptime(request.form['start_time'], '%d-%m-%Y %H:%M:%S')
        except Exception as e:
            d = datetime.utcnow()
    # Log action for trip
    tl = TripLog(book, driver_booked[1].user, get_jwt_identity(), TripLog.ACTION_START)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    return _start_trip(book, driver_booked[1].user, d, driver_booked[0].lat, driver_booked[0].long)

# display current fare before change
@admin.route('/api/admin/get_fares', methods=['POST'])
@jwt_required()
def get_fares():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'action_type']):
        return jsonify({'success': -1}), 201
    # check if the booking still has correct validity value
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).filter(
            Bookings.valid >= 0).first()
        if booking is None:
            return jsonify({'success': -2}), 201
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201
    # see if driver specific data can be loaded
    if booking.driver != 1 and booking.driver is not None:
        try:
            book_charges = db.session.query(
                (BookPricing.estimate).label('est_min'),
                BookPricing.booking_ch).filter(
                BookPricing.book_id == request.form['booking_id']).first()
            # if action type is 'fare'
            if request.form['action_type'] == "fare":
                trip = db.session.query(Trip).filter(Trip.book_id == request.form['booking_id']).first()
                if claims['roles'] == Users.ROLE_SUPERADMIN:
                    trip_pr = trip.price
                else:
                    if booking.type < BookingParams.TYPE_C24:
                        trip_pr = trip.price
                    else:
                        trip_pr = 0
                return jsonify({
                    'success': 1,
                    'est_min': trip_pr,
                    'est_max': trip_pr,
                    'book_ch': book_charges.booking_ch
                }), 200
            return jsonify({
                'success': 1,
                'est_min': book_charges.est_min,
                'est_max': book_charges.est_min,
                'book_ch': book_charges.booking_ch
            }), 200
        except Exception as e:
            print(e)
            return jsonify({'success': -3}), 201
    # else get data from all pending entries
    try:
        book_charges = db.session.query(
            (BookPricing.estimate).label('est_min'),
            BookPricing.booking_ch).filter(
            BookPricing.book_id == request.form['booking_id']).first()
        return jsonify({
            'success': 1,
            'est_min': book_charges.est_min,
            'est_max': book_charges.est_min,
            'book_ch': book_charges.booking_ch
        }), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -3}), 201


@admin.route('/api/admin/change_price', methods=['POST'])
@jwt_required()
def change_price():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'booking_type', 'change_type', 'base_fare', 'book_charge']):
        return jsonify({'success': -1}), 201
    updatedBase = float(request.form['base_fare'])
    updated_book = float(request.form['book_charge'])
    admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
    send_slack_msg(0, admin_name + " changed base and booking fare of " + str(request.form['booking_id']) + \
                   "to " + str(updatedBase) + "," + str(updated_book))
    # final fare change
    diff_book_charge = diff_base_charge = 0
    if request.form['change_type'] == "fare":
        if not request.form['booking_type'] == "completed":
            return jsonify({'success': -2}), 201
        try:
            trip = db.session.query(Trip).filter(Trip.book_id == request.form['booking_id']).first()
            assigned_driver = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).first()
            pending_entry = db.session.query(BookPricing).filter(
                BookPricing.book_id == int(request.form['booking_id'])).first()
            diff_book_charge = updated_book - pending_entry.booking_ch
            diff_base_charge = updatedBase - trip.price + pending_entry.booking_ch
        except Exception as ex:
            print(ex)
            return jsonify({'success': -3}), 201
        # change driver details
        try:
            driver_details = db.session.query(DriverDetails.owed, DriverDetails.earning).filter(
                DriverDetails.driver_id == int(assigned_driver.driver)).first()
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == int(assigned_driver.driver)). \
                update({DriverDetails.owed: round(float(driver_details.owed + diff_book_charge), 2),
                        DriverDetails.earning: round(
                            float(driver_details.earning + diff_base_charge + diff_book_charge),
                            2)})
            # change trip price
            trip_price = db.session.query(Trip).filter(Trip.book_id == int(request.form['booking_id'])).first()
            db.session.query(Trip).filter(Trip.book_id == int(request.form['booking_id'])). \
                update({Trip.price: round(float(trip_price.price + diff_base_charge + diff_book_charge), 2)})
            db.session.query(Trip).filter(Trip.book_id == int(request.form['booking_id'])). \
                update({Trip.due: round(float(trip_price.due + diff_book_charge), 2)})

            # log this change
            admin_log = AdminLog(get_jwt_identity(), 'Final Fare change:', json.dumps(str(request.form)) +
                                 "Net Cost: " + str(float((updatedBase - diff_base_charge))) + "--> " + str(
                updatedBase) +
                                 "Book ch: " + str(float((updated_book - diff_book_charge))) + "--> " + str(
                updated_book))
            db.session.add(admin_log)
            db.session.commit()
            return jsonify({'success': 1}), 200
        except Exception as e:
            print(e)
            return jsonify({'success': -4}), 201
    # estimate change
    elif request.form['change_type'] == "estimate":
        # change all pending entries. No need to be "smart" and change one entry. What if we reallocate?
        try:
            pricing_entry = db.session.query(BookPricing).filter(
                BookPricing.book_id == int(request.form['booking_id'])).first()
            if len(pricing_entry) > 0:
                total_base = pricing_entry.estimate
                total_booking = pricing_entry.booking_ch
                total_base -= total_booking
            else:
                total_base = total_booking = 0
        except Exception as e:
            print(e)
            return jsonify({'success': -5}), 201
        db.session.query(BookPricing).filter(BookPricing.book_id == int(request.form['booking_id'])). \
                    update({BookPricing.base_ch: round(float(updatedBase), 2),
                            BookPricing.booking_ch: round(float(updated_book), 2),
                            BookPricing.cartype_ch: 0,
                            BookPricing.night_ch: 0,
                            BookPricing.food_ch: 0,
                            BookPricing.dist_ch: 0,
                            BookPricing.estimate: round(
                                float(updatedBase + updated_book), 2)})

        # change the booking estimate
        try:
            # We could check if driver is 1, but ehh
            booking_estimate = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).first()
            db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']). \
                update({Bookings.estimate: round(float(updatedBase + updated_book), 2)})
            # log this change
            admin_log = AdminLog(get_jwt_identity(), 'Estimate change:', json.dumps(str(request.form)) +
                                 "Net Cost (Total - booking): " + str(total_base) + "--> " + str(updatedBase) +
                                 "Book ch: " + str(total_booking) + "--> " + str(updated_book))
            db.session.add(admin_log)
            db.session.commit()

            if booking_estimate.type < BookingParams.TYPE_C24:
                send_live_update_of_booking( int(request.form['booking_id']), booking_estimate.region)

            return jsonify({'success': 1}), 200
        except Exception as e:
            print(e)
            return jsonify({'success': -9}), 201


@admin.route('/api/admin/driver_acc_alter', methods=['POST'])
@jwt_required()
def driver_acc_alter():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['driver_id', 'availability', 'approval']):
        return jsonify({'success': -1}), 201
    try:
        # change driver account status
        location = html.escape(request.form['locname'])
        curalloc = int(get_safe(request.form, 'curalloc', -1))
        driver_q =  db.session.query(Drivers).filter(Drivers.id == int(request.form['driver_id']))
        prev_approval = driver_q.first().approved
        driver_q.update({Drivers.available: int(request.form['availability']),
                    Drivers.approved: int(request.form['approval'])
                    })
        db.session.query(DriverInfo).filter(DriverInfo.driver_id == int(request.form['driver_id'])). \
            update({DriverInfo.pres_region: location})

        if not curalloc == -1:
            db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == int(request.form['driver_id'])). \
                    update({DriverPermaInfo.alloc: curalloc})
        admin_log = AdminLog(get_jwt_identity(), 'Driver Account Status change', json.dumps(str(request.form)) +
                             "ID# " + str(request.form['driver_id']) +
                             " Available:" + str(request.form['availability']) +
                             " Approval:" + str(request.form['approval']) +
                             ", HomeLoc:" + location)
        if prev_approval < 0 and int(request.form['approval']) > 0:
            # Note driver was approved
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == int(request.form['driver_id'])). \
                update({DriverDetails.approval_ts: datetime.utcnow()})
        admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
        driver_name = db.session.query(Drivers, Users).filter(Drivers.user == Users.id). \
            filter(Drivers.id == request.form['driver_id']). \
            first()[1].get_name()
        msg = admin_name + " changed available and approval of " + driver_name + \
                    " to " + str(request.form['availability']) + ", " + str(request.form['approval']) + \
                    ", and home location name to: " + location
        if not curalloc == -1:
            if curalloc == 0:
                alloc_str = '. They were assigned to customer trips.'
            elif curalloc == 1:
                alloc_str = '. They were assigned to Cars24.'
            elif curalloc == 2:
                alloc_str = '. They were assigned to Revv.'
            elif curalloc == 3:
                alloc_str = '. They were assigned to Zoomcar.'
            elif curalloc == 4:
                alloc_str = '. They were assigned to Gujral.'
            elif curalloc == 5:
                alloc_str = '. They were assigned to OLX.'
            elif curalloc == 6:
                alloc_str = '. They were assigned to CarDekho.'
            elif curalloc == 7:
                alloc_str = '. They were assigned to Bhandari.'
            msg += alloc_str
        send_slack_msg(0, msg)
        db.session.add(admin_log)
        db.session.commit()
        return jsonify({'success': 1}), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201


'''
analytics section
'''


# get successful trips count
@admin.route('/api/admin/completed_trips', methods=['POST'])
@jwt_required()
def completed_trips_count():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401

    try:
        completed_count = db.session.query(func.count(Trip.id)).select_from(
            Trip
        ).join(
            Bookings, Bookings.id == Trip.book_id
        ).filter(
            Trip.endtime.isnot(None),
            Bookings.valid == 1
        ).scalar() or 0

        return jsonify({
            'success': 1,
            'completed': completed_count
        }), 200

    except Exception as e:
        print(f"Error in /completed_trips: {e}")
        return jsonify({'success': -1, 'error': str(e)}), 201


# get revenue details
@admin.route('/api/admin/revenue', methods=['POST'])
@jwt_required()
def revenue():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401

    try:
        collected_revenue = db.session.query(func.sum(DriverPaid.amount)).scalar() or 0.0

        uncollected_revenue = db.session.query(func.sum(DriverDetails.owed)).scalar() or 0.0

        return jsonify({
            'success': 1,
            'collected': round(collected_revenue, 2),
            'uncollected': round(uncollected_revenue, 2)
        }), 200

    except Exception as e:
        print(f"Error in /revenue: {e}")
        return jsonify({'success': -1, 'error': str(e)}), 201


# Get sales
@admin.route('/api/admin/sales', methods=['POST'])
@jwt_required()
def sales():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401

    try:
        sales = db.session.query(func.sum(Trip.price)).scalar() or 0.0

        return jsonify({
            'success': 1,
            'sales': round(sales, 2)
        }), 200

    except Exception as e:
        print(f"Error in /sales: {e}")
        return jsonify({'success': -1, 'error': str(e)}), 201


# get sales trends data
@admin.route('/api/admin/all_sales', methods=['POST'])
@jwt_required()
def all_sales():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        yearly_sales = db.session.query(func.sum(Trip.price).label('sale'),
                        func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
                       ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
                        func.year(func.addtime(Trip.starttime, '05:30:00'))).all()
        monthly_sales = db.session.query(
                    func.sum(Trip.price).label('sale'),
                    func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
                    func.year(func.addtime(Trip.starttime, '05:30:00')).label('year')
                ).filter(Bookings.id == Trip.book_id).filter(
                    Bookings.type < BookingParams.TYPE_C24
                ).group_by(
                    func.month(func.addtime(Trip.starttime, '05:30:00')),
                    func.year(func.addtime(Trip.starttime, '05:30:00'))
                ).order_by(
                    func.year(func.addtime(Trip.starttime, '05:30:00')),
                    func.month(func.addtime(Trip.starttime, '05:30:00'))
                ).all()
        weekly_sales = db.session.query(
            func.sum(Trip.price).label('sale'),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
            func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
            func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
        ).filter(
            Bookings.id == Trip.book_id
        ).filter(
            Bookings.type < BookingParams.TYPE_C24
        ).group_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
        ).order_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
        ).limit(12).all()
        daily_sales = db.session.query(func.sum(Trip.price).label('sale'),
                                    func.date(func.addtime(Trip.starttime, '05:30:00')).label('day'),
                        ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()).limit(30).all()
        year_json = []
        month_json = []
        week_json = []
        day_json = []
        for sale_data in monthly_sales:
            monthly_sales_data = MonthlySales(month=sale_data.month,
                                              year=sale_data.year,
                                              sales=round(sale_data.sale, 2))
            month_json.append(jsonpickle.encode(monthly_sales_data))
        for sale_data in yearly_sales:
            yearly_sales_data = YearlySales(year=sale_data.year,
                                            sales=round(sale_data.sale, 2))
            year_json.append(jsonpickle.encode(yearly_sales_data))
        for sale_data in weekly_sales:
            weekly_sales_data = WeeklySales(week=sale_data.week,
                                            year=sale_data.year,
                                            sales=round(sale_data.sale, 2))
            week_json.append(jsonpickle.encode(weekly_sales_data))
        daily_sales = daily_sales[::-1]
        prev_day = None
        for sale_data in daily_sales:
            daily_sales_data = DailySales(day=sale_data.day.strftime('%d %m %Y'),
                                          sales=round(sale_data.sale, 2))
            if prev_day and prev_day != sale_data.day - timedelta(days=1):
                prev_sales_data = DailySales(day=(sale_data.day - timedelta(days=1)).strftime('%d %m %Y'), sales=0)
                day_json.append(jsonpickle.encode(prev_sales_data))
            day_json.append(jsonpickle.encode(daily_sales_data))
            prev_day = sale_data.day
        if len(year_json) + len(month_json) + len(week_json) + len(day_json) <= 0:
            return jsonify({'success': -1})
        else:
            return jsonify({
                'success': 1,
                'year_data': year_json,
                'month_data': month_json,
                'week_data': week_json,
                'day_data': day_json
            }), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -2,"error":e}), 201


# get get trips trends data
@admin.route('/api/admin/all_trips', methods=['POST'])
@jwt_required()
def all_trips():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        yearly_trips = db.session.query(func.count(Trip.id).label('trip'),
                                           func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
                      ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.year(func.addtime(Trip.starttime, '05:30:00'))).all()
        monthly_trips = db.session.query(func.count(Trip.id).label('trip'),
                                            func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
                                            func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
                      ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.month(func.addtime(Trip.starttime, '05:30:00')),
            func.year(func.addtime(Trip.starttime, '05:30:00'))).order_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')),
            func.month(func.addtime(Trip.starttime, '05:30:00'))).all()
        weekly_trips = db.session.query(
            func.count(Trip.id).label('trip'),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
            func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
            func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
        ).filter(
            Bookings.id == Trip.book_id
        ).filter(
            Bookings.type < BookingParams.TYPE_C24
        ).group_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
        ).order_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
        ).limit(12).all()
        daily_trips = db.session.query(func.count(Trip.id).label('trip'),
                                       func.date(func.addtime(Trip.starttime, '05:30:00')).label('day'),
                        ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()).limit(30).all()
        year_json = []
        month_json = []
        week_json = []
        day_json = []
        for trip_data in monthly_trips:
            monthly_trips_data = MonthlyTrips(month=trip_data.month,
                                              year=trip_data.year,
                                              trips=trip_data.trip)
            month_json.append(jsonpickle.encode(monthly_trips_data))
        for trip_data in yearly_trips:
            yearly_trips_data = YearlyTrips(year=trip_data.year,
                                            trips=trip_data.trip)
            year_json.append(jsonpickle.encode(yearly_trips_data))
        for trip_data in weekly_trips:
            weekly_trips_data = WeeklyTrips(week=trip_data.week,
                                            year=trip_data.year,
                                            trips=trip_data.trip)
            week_json.append(jsonpickle.encode(weekly_trips_data))
        prev_day = None
        for trip_data in daily_trips[::-1]:
            daily_trips_data = DailyTrips(day=trip_data.day.strftime('%d %m %Y'),
                                          trips=trip_data.trip)
            if prev_day and prev_day != trip_data.day - timedelta(days=1):
                prev_trip_data = DailyTrips(day=(trip_data.day - timedelta(days=1)).strftime('%d %m %Y'), trips=0)
                day_json.append(jsonpickle.encode(prev_trip_data))
            day_json.append(jsonpickle.encode(daily_trips_data))
            prev_day = trip_data.day
        if len(year_json) + len(month_json) + len(week_json) + len(day_json) <= 0:
            return jsonify({'success': -1})
        else:
            return jsonify({
                'success': 1,
                'year_data': year_json,
                'month_data': month_json,
                'week_data': week_json,
                'day_data': day_json
            }), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201


# get get trips trends data
@admin.route('/api/admin/all_revenues', methods=['POST'])
@jwt_required()
def all_revenues():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        yearly_revenues = db.session.query(func.sum(Trip.net_rev).label('revenue'),
                                           func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
                      ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.year(func.addtime(Trip.starttime, '05:30:00'))).all()
        monthly_revenues = db.session.query(func.sum(Trip.net_rev).label('revenue'),
                                            func.month(func.addtime(Trip.starttime, '05:30:00')).label('month'),
                                            func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
                      ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.month(func.addtime(Trip.starttime, '05:30:00')),
            func.year(func.addtime(Trip.starttime, '05:30:00'))).order_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')),
            func.month(func.addtime(Trip.starttime, '05:30:00'))).all()
        weekly_revenues = db.session.query(
            func.sum(Trip.net_rev).label('revenue'),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1).label('week'),
            func.year(func.addtime(Trip.starttime, '05:30:00')).label('year'),
            func.min(func.addtime(Trip.starttime, '05:30:00')).label('start_of_week')
        ).filter(
            Bookings.id == Trip.book_id
        ).filter(
            Bookings.type < BookingParams.TYPE_C24
        ).group_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1)
        ).order_by(
            func.year(func.addtime(Trip.starttime, '05:30:00')).desc(),
            func.week(func.addtime(Trip.starttime, '05:30:00'), 1).desc()
        ).limit(12).all()
        daily_revenues = db.session.query(func.sum(Trip.net_rev).label('revenue'),
                                       func.date(func.addtime(Trip.starttime, '05:30:00')).label('day'),
                        ).filter(Bookings.id == Trip.book_id).filter(
                        Bookings.type < BookingParams.TYPE_C24).group_by(
            func.date(func.addtime(Trip.starttime, '05:30:00'))).order_by(
            func.date(func.addtime(Trip.starttime, '05:30:00')).desc()).limit(30).all()
        year_json = []
        month_json = []
        week_json = []
        day_json = []
        for revenue_data in monthly_revenues:
            monthly_revenues_data = MonthlyRevenues(month=revenue_data.month,
                                                    year=revenue_data.year,
                                                    revenues=revenue_data.revenue)
            month_json.append(jsonpickle.encode(monthly_revenues_data))
        for revenue_data in yearly_revenues:
            yearly_revenues_data = YearlyRevenues(year=revenue_data.year,
                                                  revenues=revenue_data.revenue)
            year_json.append(jsonpickle.encode(yearly_revenues_data))
        for revenue_data in weekly_revenues:
            weekly_revenues_data = WeeklyRevenues(week=revenue_data.week,
                                                  year=revenue_data.year,
                                                  revenues=revenue_data.revenue)
            week_json.append(jsonpickle.encode(weekly_revenues_data))
        prev_day = None
        for revenue_data in daily_revenues[::-1]:
            daily_revenues_data = DailyRevenues(day=revenue_data.day.strftime('%d %m %Y'),
                                                revenues=revenue_data.revenue)
            if prev_day and prev_day != revenue_data.day - timedelta(days=1):
                prev_revenues_data = DailyRevenues(day=(revenue_data.day - timedelta(days=1)).strftime('%d %m %Y'), revenues=0)
                day_json.append(jsonpickle.encode(prev_revenues_data))
            day_json.append(jsonpickle.encode(daily_revenues_data))
            prev_day = revenue_data.day
        if len(year_json) + len(month_json) + len(week_json) + len(day_json) <= 0:
            return jsonify({'success': -1})
        else:
            return jsonify({
                'success': 1,
                'year_data': year_json,
                'month_data': month_json,
                'week_data': week_json,
                'day_data': day_json
            }), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201


# get user count
@admin.route('/api/admin/user_count', methods=['POST'])
@jwt_required()
def count_users():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        customers = db.session.query(Users.id).filter(Users.role == 0).filter(Users.enabled == 1).count()
        drivers = db.session.query(Users, Drivers).filter(Drivers.user == Users.id).filter(Users.role == 1).filter(
            Users.enabled == 1).filter(
            Drivers.approved == 1).count()
        active_drivers = db.session.query(Users, Drivers).filter(Drivers.user == Users.id).filter(
            Users.role == 1).filter(
            Users.enabled == 1).filter(
            Drivers.approved == 1).filter(Drivers.available == 1).count()

        return jsonify({'success': 1,
                        'customer_count': customers,
                        'driver_count': drivers,
                        'active_driver_count': active_drivers}), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201



# get today's stats
@admin.route('/api/admin/daily_stats', methods=['POST'])
@jwt_required()
def daily_stats():
    claims = get_jwt()
    try:
        region = int(get_safe(request.form, 'region', -1))
    except Exception:
        user = db.session.query(Users).filter(Users.id == get_jwt_identity()).first()
        region = user.region if user else -1

    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401

    category = get_safe(request.form, 'category', 'customer').lower()

    prev_dt = convert_to_ist(datetime.utcnow() - timedelta(days=1))
    time_now_ist = get_dt_ist(datetime.utcnow().date(), datetime.utcnow().time())

    region_list = [i for i in range(len(Regions.REGN_NAME))] if region == -1 else Regions.admin_view_regions(region)

    category_map = {
        "customer": 0,
        "c24": BookingParams.TYPE_C24,
        "revv": BookingParams.TYPE_REVV,
        "gujral": BookingParams.TYPE_GUJRAL,
        "olx": BookingParams.TYPE_OLX,
        "zoomcar": BookingParams.TYPE_ZOOMCAR,
        "cardekho": BookingParams.TYPE_CARDEKHO,
        "bhandari": BookingParams.TYPE_BHANDARI,
        "mahindra": BookingParams.TYPE_MAHINDRA,
        "spinny": BookingParams.TYPE_SPINNY,
        "revvv2": BookingParams.TYPE_REVV_V2,
        "pridehonda": BookingParams.TYPE_PRIDEHONDA
    }
    cat_val = category_map.get(category, -1)

    try:
        base_filters = [Bookings.region.in_(region_list)]
        if cat_val > 0:
            base_filters.append(Bookings.type == cat_val)
        elif cat_val == 0:
            base_filters.append(Bookings.type < BookingParams.TYPE_C24)

        new_bookings = db.session.query(Bookings).filter(*base_filters, Bookings.valid == 0).all()

        ongoing_and_omw = db.session.query(Bookings, Trip).filter(
            Bookings.id == Trip.book_id,
            Trip.endtime.is_(None),
            *base_filters
        ).all()

        upcoming_bookings = db.session.query(Bookings).filter(
            Bookings.valid == 1,
            Bookings.startdate >= prev_dt.date(),
            Bookings.id.notin_(db.session.query(Trip.book_id)),
            *base_filters
        ).all()

        cancelled_all = db.session.query(Bookings).filter(
            Bookings.valid < 0,
            Bookings.startdate >= prev_dt.date(),
            *base_filters
        ).all()

        sales_completed_today = db.session.query(
            func.sum(Trip.price).label('total_sales'),
            func.count(Trip.id).label('total_completed')
        ).filter(
            Bookings.id == Trip.book_id,
            Trip.endtime.isnot(None),
            func.date(func.addtime(Trip.starttime, '05:30:00')) == time_now_ist.date(),
            *base_filters
        ).first()

    except Exception as e:
        print(f"Error in /daily_stats: {e}")
        return jsonify({'success': -1, 'error': str(e)}), 201

    new_count = len(new_bookings)
    upcoming_count = len(upcoming_bookings)
    omw_count = 0
    ongoing_count = 0
    ongoing_today_count = 0
    d4m_cancelled_count = 0
    cancelled_before_alloc = 0
    cancelled_after_alloc = 0
    unallocated_today_count = 0
    allocated_today_count = 0

    for booking, trip in ongoing_and_omw:
        if trip.starttime is None:
            omw_count += 1
        elif trip.endtime is None:
            if is_trip_today(trip):
                ongoing_today_count += 1
            ongoing_count += 1

    for booking in new_bookings:
        if is_booking_today(booking):
            unallocated_today_count += 1

    for booking in upcoming_bookings:
        if is_booking_today(booking):
            allocated_today_count += 1

    for booking in cancelled_all:
        if not is_booking_today(booking):
            continue
        if booking.valid == -3:
            d4m_cancelled_count += 1
        elif booking.driver == 1:
            cancelled_before_alloc += 1
        else:
            cancelled_after_alloc += 1

    tot_sales = 0
    if validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SUPERADMIN]) and cat_val == 0:
        tot_sales = round(float(sales_completed_today.total_sales or 0), 2)

    return jsonify({
        'success': 1,
        'new_count': new_count,
        'upcoming_count': upcoming_count,
        'omw_count': omw_count,
        'ongoing_count': ongoing_count,
        'ongoing_today_count': ongoing_today_count,
        'success_today_count': sales_completed_today.total_completed if sales_completed_today else 0,
        'new_today_count': unallocated_today_count,
        'upcoming_today_count': allocated_today_count,
        'd4m_cancelled_count': d4m_cancelled_count,
        'cancelled_before_alloc': cancelled_before_alloc,
        'cancelled_after_alloc': cancelled_after_alloc,
        'sales': tot_sales
    }), 200




# pricing information (WIP)
@admin.route('/api/admin/price', methods=['POST'])
@jwt_required()
@admin_required
def priceInfo():
    hourly_base_rate = Price.BASE_FARE
    return jsonify({
        'in-round-hourly-ranges': "2 - 3,4 - 10",
        'in-round-hourly-fares': ",".join([str(x) for x in hourly_base_rate])
    })


# get hold status
@admin.route('/api/admin/get_hold', methods=['POST'])
@jwt_required()
def get_hold():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    return jsonify({
        'hold_status': int(BookingParams.get_no_broadcast())
    })

# get hold status
@admin.route('/api/admin/get_hold_zoom', methods=['POST'])
@jwt_required()
def get_hold_zoom():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    return jsonify({
        'hold_status': int(BookingParams.get_no_broadcast_zoom())
    })

# set hold status
@admin.route('/api/admin/set_hold', methods=['POST'])
@jwt_required()
def set_hold():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['hold_status']):
        BookingParams.set_no_broadcast("False")
    else:
        BookingParams.set_no_broadcast(request.form['hold_status'])
    if BookingParams.get_no_broadcast():
        status = "OFF"
    else:
        status = "ON"
    admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
    send_slack_msg(1, admin_name + " turned broadcasting of trips " + status)
    return jsonify({'success': 1}), 200

# set hold status
@admin.route('/api/admin/set_hold_zoom', methods=['POST'])
@jwt_required()
def set_hold_zoom():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['hold_status']):
        BookingParams.set_no_broadcast_zoom("False")
    else:
        BookingParams.set_no_broadcast_zoom(request.form['hold_status'])
    if BookingParams.get_no_broadcast_zoom():
        status = "OFF"
    else:
        status = "ON"
    admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
    send_slack_msg(1, admin_name + " turned broadcasting of ZC trips " + status)
    return jsonify({'success': 1}), 200

@admin.route('/api/admin/customer_decline', methods=['POST'])
@jwt_required()
def cancel_customer():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    try:
        booking_id = request.form['booking_id']
    except(KeyError, NameError):
        return jsonify({'success': -1}), 401
    admin_id = get_jwt_identity()
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    user_id = booking.user
    driver_info = db.session.query(Users, Drivers).filter(Users.id == Drivers.user). \
        filter(Drivers.id == booking.driver).first()
    if booking.valid >= 0:
        try:

            trip_ex = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
            if trip_ex:
                db.session.delete(trip_ex)
                try:
                    fb_db.collection(u'trip_started').document(str(booking.user)).delete()
                except Exception as e:
                    pass
            Bookings.query.filter(Bookings.id == booking.id).update({Bookings.valid: Bookings.CANCELLED_USER})
            Bookings.query.filter(Bookings.id == booking.id).update({Bookings.cancelled_dt: datetime.utcnow()})
            db.session.commit()
            _update_user_pending(user_id)
            _update_driver_pending(driver_info[1].id)
            try:
                fb_db.collection(u'trip_set').document(str(user_id)).update({str(booking_id): firestore.DELETE_FIELD})
            except Exception:
                pass
            if booking.driver != 1:
                fb_db.collection(u'book_cancelled_driver').document(str(booking.driver)).set({
                    str(booking_id): {
                        u'book_time': booking.starttime.strftime("%H:%M:%S"),
                        u'book_date': booking.startdate.strftime("%Y-%m-%d")
                    }}, merge=True
                )
            try:
                user_name = db.session.query(Users).filter(Users.id == user_id).first().get_name()
                admin_name = db.session.query(Users).filter(Users.id == admin_id).first().get_name()
                start_time_ist = datetime(booking.startdate.year, booking.startdate.month,
                                          booking.startdate.day, booking.starttime.hour,
                                          booking.starttime.minute, booking.starttime.second) + \
                                 _sms.IST_OFFSET_TIMEDELTA

                if booking.driver != 1:
                    driver_name = driver_info[0].get_name()
                else:
                    driver_name = ''
                if not driver_name:
                    driver_msg_string = ' The trip was unallocated.'
                else:
                    driver_msg_string = ' The trip was allocated to ' + driver_name
                msg_content = admin_name + " cancelled the trip of " + user_name + " on their behalf, booked on " + \
                              start_time_ist.strftime("%I:%M %p %d/%m/%Y") + "."
                # also send to driver
                # same template as in book_ride
                if booking.driver != 1:
                    msg_content_json = {"name": user_name, "code": str(booking.code),
                                        "time": start_time_ist.strftime("%I:%M %p %d/%m/%Y")}
                                        
                    message = (
                        f"{msg_content_json['name']} cancelled their trip (ID: {msg_content_json['code']}) "
                        f"with Drivers4Me booked on {msg_content_json['time']}."
                    )
                    response = _sms.send_bulk_message_gupshup(
                        phone_numbers=[str(driver_info[0].mobile)],
                        message=message,
                        mask= _sms.MASK,
                        dltTemplateId=_sms.TEMPLATE_ID_MAPPING['user-cancelled'],
                        principalEntityId= _sms.PRINCIPAL_ENTITY_ID
                    )
                    #_sms.send_msg_flow(driver_info[0].mobile, _sms.FLOWS["user-cancelled"], msg_content_json)
                msg_content = msg_content + driver_msg_string
                send_slack_msg(1, msg_content)
            except Exception as e:
                print(e)
                pass
            return jsonify({'success': 1})
        except exc.IntegrityError as e:
            print(e)
            db.session.rollback()
            return jsonify({'success': -1}), 401
    else:
        return jsonify({'success': 0})


@admin.route('/api/admin/newstop_trip', methods=['POST'])
@jwt_required()
def admin_newstop_trip():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2}), 201
    book = request.form['book_id']
    driver = db.session.query(Bookings, Drivers).filter(Bookings.id == book).filter(Drivers.id == Bookings.driver).first()
    if 'stop_time' not in request.form:
        d = datetime.utcnow()
    else:
        try:
            d = datetime.strptime(request.form['stop_time'], '%d-%m-%Y %H:%M:%S')
        except Exception as e:
            print(e)
            d = datetime.utcnow()
    # Log action for trip
    tl = TripLog(book, driver[1].user, get_jwt_identity(), TripLog.ACTION_NEWSTOP)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    if driver[0].type == BookingParams.TYPE_C24:
        b = db.session.query(C24Bookings).filter(C24Bookings.ref == book).first()
        return _c24_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_OLX:
        b = db.session.query(OLXBookings).filter(OLXBookings.ref == book).first()
        return _olx_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_ZOOMCAR:
        b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book).first()
        return _zoomcar_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_REVV:
        b = db.session.query(RevvBookings).filter(RevvBookings.ref == book).first()
        return _revv_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_GUJRAL:
        b = db.session.query(GujralBookings).filter(GujralBookings.ref == book).first()
        return _gujral_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_CARDEKHO:
        b = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == book).first()
        return _cardekho_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_BHANDARI:
        b = db.session.query(BhandariBookings).filter(BhandariBookings.ref == book).first()
        return _bhandari_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_MAHINDRA:
        b = db.session.query(MahindraBookings).filter(MahindraBookings.ref == book).first()
        return _mahindra_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_PRIDEHONDA:
        b = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == book).first()
        return _pridehonda_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type == BookingParams.TYPE_SPINNY:
        b = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == book).first()
        return _spinny_newstop_trip(b.id, driver[1].user, d)
    # elif driver[0].type == BookingParams.TYPE_REVV_V2:
    #     b = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == book).first()
    #     return _revv_v2_newstop_trip(b.id, driver[1].user, d)
    elif driver[0].type <= BookingParams.TYPE_C24:
        _restart_trip(book, driver[1].user)
        return _stop_trip(book, driver[1].user, d)
    return jsonify({'success': -1})


@admin.route('/api/admin/restart_trip', methods=['POST'])
@jwt_required()
def admin_restart_trip():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2}), 201
    book = request.form['book_id']
    driver = db.session.query(Bookings, Drivers).filter(Bookings.id == book).filter(Drivers.id == Bookings.driver).first()
    # Log action for trip
    tl = TripLog(book, driver[1].user, get_jwt_identity(), TripLog.ACTION_RESTART)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    if driver[0].type == BookingParams.TYPE_C24:
        b = db.session.query(C24Bookings).filter(C24Bookings.ref == book).first()
        return _c24_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_OLX:
        b = db.session.query(OLXBookings).filter(OLXBookings.ref == book).first()
        return _olx_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_ZOOMCAR:
        b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book).first()
        return _zoomcar_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_REVV:
        b = db.session.query(RevvBookings).filter(RevvBookings.ref == book).first()
        return _revv_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_GUJRAL:
        b = db.session.query(GujralBookings).filter(GujralBookings.ref == book).first()
        return _gujral_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_CARDEKHO:
        b = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == book).first()
        return _cardekho_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_BHANDARI:
        b = db.session.query(BhandariBookings).filter(BhandariBookings.ref == book).first()
        return _bhandari_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_MAHINDRA:
        b = db.session.query(MahindraBookings).filter(MahindraBookings.ref == book).first()
        return _pridehonda_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_PRIDEHONDA:
        b = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == book).first()
        return _pridehonda_restart_trip(b.id, driver[1].user)
    elif driver[0].type == BookingParams.TYPE_SPINNY:
        b = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == book).first()
        return _spinny_restart_trip(b.id, driver[1].user)
    # elif driver[0].type == BookingParams.TYPE_REVV_V2:
    #     b = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == book).first()
    #     return _revv_v2_restart_trip(b.id, driver[1].user)
    elif driver[0].type <= BookingParams.TYPE_C24:
        return _restart_trip(book, driver[1].user)
    return jsonify({'success': -1})


@admin.route('/api/admin/unallocate', methods=['POST'])
@jwt_required()
def admin_unallocate_trip():
    admin = get_jwt_identity()
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2}), 201
    book = request.form['booking_id']
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_NO_ALLOC))
    reason_detail = str(get_safe(request.form, 'reason_details', BookingCancelled.RSN_NO_ALLOC))
    driver_book = db.session.query(Bookings, Drivers).filter(Bookings.id == book).filter(Drivers.id == Bookings.driver).first()
    Bookings.query.filter(Bookings.id == book).update({Bookings.did: "-1"})
    booking = driver_book[0]
    driver = driver_book[1]
    starttime = datetime(booking.startdate.year, booking.startdate.month,
                         booking.startdate.day, booking.starttime.hour,
                         booking.starttime.minute, booking.starttime.second)
    trip_ex = db.session.query(Trip).filter(Trip.book_id == book).first()

    admin_log = AdminLog(admin, 'driver-unalloc', json.dumps(str(request.form)))
    db.session.add(admin_log)
    db.session.commit()
    has_trip = trip_ex is not None
    if has_trip and trip_ex.status < Trip.TRIP_STOP_PIC:
        return jsonify({'success': -2})
    if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
        penalty, delay = PriceOutstation.get_cancel_ch(datetime.utcnow(), starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, has_trip=has_trip, cancel_cat=reason)
    else:
        penalty, delay = Price.get_cancel_ch(datetime.utcnow(), starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, has_trip=has_trip, cancel_cat=reason)
    user_fine, driver_fine = penalty
    ut_id=None
    if booking.valid >= 0:
        try:
            if user_fine > 0:
                user_id = booking.user
                uc = UserCancelled(user_id, booking.id, user_fine)
                ut = UserTrans(uid=user_id, amt=(100*user_fine)*(-1), method="Cancellation charges for #" + str(booking.id),
                               status=UserTrans.COMPLETED)
                db.session.query(Users).filter(Users.id == user_id).update({Users.credit: Users.credit - user_fine})
                ut_id=ut.id
                db.session.add(uc)
                db.session.add(ut)
                db.session.commit()
            else:
                user_id = booking.user
                uc = UserCancelled(user_id, booking.id, user_fine)
                db.session.add(uc)
                db.session.commit()
            old_driver=booking.driver
            _ ,dt_id = _unalloc_trip_driver(booking, driver.id, delay, driver_fine, trip=trip_ex)
            bc = BookingCancelled(user=admin, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid=booking.user,
                      did=old_driver, penalty_user=user_fine, penalty_driver=driver_fine, rsn=reason,
                      reason_detail=reason_detail,utransid=ut_id,dtransid=dt_id)
            db.session.add(bc)
            db.session.commit()

            if booking.type < BookingParams.TYPE_C24:
                send_live_update_of_booking( book, booking.region)

            return jsonify({'success': 1})
        except Exception as e:
            print(e)
            return jsonify({'success': -1})
    else:
        return jsonify({'success': -2})


@admin.route('/api/admin/payment_switch', methods=['POST'])
@jwt_required()
def admin_payment_switch():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2}), 201
    book = request.form['book_id']
    return _switch_payment(book)

@admin.route('/api/admin/book_log', methods=['POST'])
@jwt_required()
def admin_book_log():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2}), 201
    book = int(request.form['book_id'])
    booking = db.session.query(Bookings).filter(Bookings.id == book)
    if booking and booking.first():
        all_allocs = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == book).all()
        all_trip_act = db.session.query(TripLog).filter(TripLog.booking_id == book).all()
        all_driv_cancel = db.session.query(DriverCancelled).filter(DriverCancelled.booking == book).all()
        all_cancel = db.session.query(BookingCancelled).filter(BookingCancelled.booking == book).all()
        all_cancel_ids = [a.id for a in all_cancel]
        all_driv_cancel = [x for x in all_driv_cancel if x.id not in all_cancel_ids]
        entr_list = []
        for e in all_allocs:
            entr_list.append(e)
        for e in all_driv_cancel:
            entr_list.append(e)
        for e in all_trip_act:
            entr_list.append(e)
        for e in all_cancel:
            entr_list.append(e)
        entr_list.sort(key=lambda x: x.timestamp)
        entr_list =  [booking.first()] + entr_list
        result_json = []
        for e in entr_list:
            if type(e) is BookingAlloc:
                driver_name = db.session.query(Drivers, Users).filter(Users.id == Drivers.user).filter(Drivers.id == e.driver_id).first()
                user_name = db.session.query(Users).filter(Users.id == e.alloc_id).first()
                delta = e.timestamp - last_ts
                delta_min = delta.days * 3600 + round(delta.seconds/60, 1)
                result_json.append({'type': 0, 'driver_name': driver_name[1].get_name(),
                                    'alloc_user': user_name.get_name(),
                                    'timestamp': get_dt_ist(e.timestamp.date(), e.timestamp.time()),
                                    'fine': str(delta_min) + " min", 'lat': -1, 'lng': -1})
                last_ts = e.timestamp
            elif type(e) is BookingCancelled:
                try:
                    driver_name = db.session.query(Drivers, Users).filter(Users.id == Drivers.user).filter(Drivers.id == e.did).first()[1].get_name()
                except Exception:
                    driver_name = "Unknown"
                try:
                    user_name = db.session.query(Users).filter(Users.id == e.uid).first().get_name()
                except Exception:
                    user_name = "Unknown"
                try:
                    cancel_src_name = db.session.query(Users).filter(Users.id == e.user).first().get_name()
                except Exception:
                    cancel_src_name = "Unknown"
                result_json.append({'type': 1, 'id':e.id, 'driver_name':driver_name, 'user_name': user_name,
                                    'cancel_src': cancel_src_name, 'fine_driver': e.penalty_driver, 'fine_user': e.penalty_user,
                                    'cancel_source': e.cancel_source, 'reason': e.reason, 'reason_detail': e.reason_detail,
                                    'timestamp': get_dt_ist(e.timestamp.date(), e.timestamp.time()), 'lat': -1, 'lng': -1,"cancel_reversed":e.cancel_reversed})
                last_ts = e.timestamp
            # elif type(e) is DriverCancelled:
            #     driver_name = db.session.query(Drivers, Users).filter(Users.id == Drivers.user).filter(Drivers.id == e.driver).first()
            #     result_json.append({'type': 1, 'driver_name':driver_name[1].get_name(),
            #                         'fine': e.penalty, 'timestamp': get_dt_ist(e.timestamp.date(), e.timestamp.time()), 'lat': -1, 'lng': -1})
            #     last_ts = e.timestamp
            elif type(e) is Bookings:
                timestamp = db.session.query(DriverSearch).filter(DriverSearch.id == e.search_key)
                if timestamp.first():
                    timestamp = timestamp.first().timestamp
                    last_ts = timestamp
                delta = e.timestamp - last_ts
                delta_min = delta.days * 3600 + round(delta.seconds/60, 1)
                if len(entr_list) == 1:
                    result_json.append({'type': 2, 'driver_name': "Not Allocated", 'timestamp': get_dt_ist(timestamp.date(), timestamp.time()),
                                        'fine': str(delta_min) + " min", 'lat': -1, 'lng': -1})
                else:
                    result_json.append({'type': 2, 'driver_name': "Not Allocated",
                                        'timestamp': get_dt_ist(timestamp.date(), timestamp.time()), 'lat': -1, 'lng': -1})
            elif type(e) is TripLog:
                driver_name = db.session.query(Users).filter(Users.id == e.driver_user).first()
                user_name = db.session.query(Users).filter(Users.id == e.action_user).first()
                action_type = 2 + e.action
                last_ts = e.timestamp
                delta = e.timestamp - last_ts
                delta_min = delta.days * 3600 + round(delta.seconds/60, 1)
                result_json.append({'type': action_type, 'driver_name': driver_name.get_name(), 'timestamp': get_dt_ist(e.timestamp.date(), e.timestamp.time()),
                                    'fine': str(delta_min) + " min", 'alloc_user': user_name.get_name(), 'lat': e.lat, 'lng': e.lng})
        return jsonify({'success': 1, 'data': result_json})
    else:
        return jsonify({'success': 0})

@admin.route('/api/admin/driver_due_log', methods=['POST'])
@jwt_required()
def admin_driver_due_log():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1}), 201
    mobile = request.form['mobile']
    old_format = False
    try:
        driver = db.session.query(Drivers, Users).filter(
                     Users.mobile == int(mobile)).filter(
                     Drivers.user == Users.id)
        if driver and driver.first():
            if old_format:
                all_driver_due = db.session.query(DriverPaid).filter(
                                    driver.first()[0].id == DriverPaid.driver_id).all()
            else:
                all_driver_due = []
            all_driver_trans = db.session.query(DriverTrans).filter(
                                driver.first()[0].id == DriverTrans.driver_id).all()
            entr_list = all_driver_due + all_driver_trans

            entr_list.sort(key=lambda x: x.timestamp, reverse=True)
            result_json = []
            for e in entr_list:
                driver_name = db.session.query(Drivers, Users).filter(
                                     Users.id == Drivers.user).filter(
                                     Drivers.id == e.driver_id).first()
                if hasattr(e, "source"):
                    source = e.source
                    source_type = e.source_type
                    if source == 0:
                        source_str = "ADMIN"
                    else:
                        if source_type == DriverPaid.SOURCE_ADMIN:
                            source_str = get_user_name_from_id(source)
                        else:
                            source_str = get_book_code(source)
                    amount = e.amount
                    remarks = "Old Format"
                else:
                    source_str = e.method
                    amount = e.amount / 100
                    remarks = e.remarks
                result_json.append({'paid': amount,
                                    'driver_name': driver_name[1].get_name(),
                                    'source_due': source_str,
                                    'timestamp': get_dt_ist(e.timestamp.date(),
                                                            e.timestamp.time()),
                                    'remarks': remarks
                                  })
            print(result_json)
            return jsonify({'success': 1, 'data': result_json})
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201
    else:
        return jsonify({'success': 0})


@admin.route('/api/admin/cred_view', methods=['POST'])
@jwt_required()
def d4m_cred_view():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1}), 201
    mobile = request.form['mobile']
    try:
        user_det = db.session.query(Users).filter(Users.mobile == int(mobile)).first()
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201
    if user_det is not None:
        return jsonify({'success': 1,
                        'user_id': user_det.id,
                        'name': user_det.get_name(),
                        'user_mobile': user_det.mobile,
                        'credit': user_det.credit
                        }), 200
    else:
        return jsonify({'success': 0,
                        'name': ''}), 200

@admin.route('/api/admin/credit_alter', methods=['POST'])
@jwt_required()
def d4m_cred_alter():
    claims = get_jwt()
    admin_user = get_jwt_identity()
    if not validate_role(admin_user, claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not account_enabled(admin_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['user_id', 'amount', 'remark']):  # Added 'remark' to required fields
        return jsonify({'success': -2}), 201
    user_id = request.form['user_id']
    amount = round(float(request.form['amount']) * 100)
    raw_amt = round(float(request.form['amount']))
    remark = request.form['remark']
    admin =db.session.query(Users).filter(Users.id == admin_user).first()
    admin_name = admin.get_name()
    admin_id = admin.id

    try:
        user_det = db.session.query(Users).filter(Users.id == int(user_id)).first()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201
    try:
        user_trans = UserTrans(uid=user_id, amt=amount, method="Admin panel", status=UserTrans.COMPLETED, cash=0, stop=True, admin_name=admin_name, admin_id=admin_id, remark=remark)
        db.session.add(user_trans)
        admin_log = AdminLog(admin_user, 'd4m-cred', json.dumps(str(request.form)))
        db.session.query(Users).filter(Users.id == int(user_id)).update({Users.credit: Users.credit + raw_amt})
        db.session.add(admin_log)
        db.session.commit()
        send_slack_msg(0, admin_name + " gave to " + user_det.get_name() + " D4M credit of " + str(raw_amt))
        return jsonify({'success': 1, 'credit': user_det.credit}), 200
    except Exception as exc:
        print(exc)
        return jsonify({'success': -1})

@admin.route('/api/admin/event_complete_trips_count', methods=['POST'])
@jwt_required()
@admin_required
def event_complete_trips_count():
    if not complete(request.form, ['user_id', 'start_date', 'end_date']):
        return jsonify({'success': -1}), 201
    user = int(get_safe(request.form, 'user_id', 1))
    start_date = datetime.strptime(request.form['start_date'], '%d-%m-%Y')

    end_date = datetime.strptime(request.form['end_date'], '%d-%m-%Y')

    trip_count = db.session.query(Bookings, Trip). \
                    filter(Bookings.id == Trip.book_id). \
                    filter(Bookings.user == user). \
                    filter(Trip.endtime != None). \
                    filter(Bookings.startdate >= start_date). \
                    filter(Bookings.startdate <= end_date).all()

    return jsonify({
                'success': 1,
                'trips': len(trip_count)
            }), 200


@admin.route('/api/admin/feedback/set', methods=['POST'])
@jwt_required()
def feedback_set():
    claims = get_jwt()
    admin = get_jwt_identity()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['feedback', 'booking_id']):
        return jsonify({'success': -2}), 201
    booking_id = int(request.form['booking_id'])
    fb_val = int(request.form['feedback'])
    if fb_val > BookingFeedback.FB_POS:
        fb_val = BookingFeedback.FB_POS
    elif fb_val < BookingFeedback.FB_NEG:
        fb_val = BookingFeedback.FB_NEG
    feedback = db.session.query(BookingFeedback).filter(BookingFeedback.id == booking_id)
    if feedback.first():
        feedback.update({BookingFeedback.feedback: fb_val, BookingFeedback.admin_id: admin})
    else:
        f = BookingFeedback(booking_id, fb_val, admin)
        db.session.add(f)
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201
    return jsonify({'success': 1})


@admin.route('/api/admin/feedback/get', methods=['POST'])
@jwt_required()
def feedback_get():
    claims = get_jwt()
    admin = get_jwt_identity()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2}), 201
    booking_id = int(request.form['booking_id'])
    feedback = db.session.query(BookingFeedback).filter(BookingFeedback.id == booking_id)
    if feedback.first():
        return jsonify({'success': 1, 'feedback': feedback.first().feedback})
    else:
        return jsonify({'success': -1})
    return jsonify({'success': 1})


@admin.route('/api/admin/change/phone', methods=['POST'])
@jwt_required()
@admin_required
def ph_change():
    try:
        driver_id = int(request.form["driver"])
        new_ph = request.form["new_ph"]
    except Exception:
        return jsonify({'success': -1, 'message': 'Invalid input data'})

    if not is_valid_phone(new_ph):
        return jsonify({'success': -1, 'message': 'Invalid phone number format'})

    if driver_id <= 0:
        return jsonify({'success': -1, 'message': 'Invalid driver ID'})

    existing_user = db.session.query(Users).filter(Users.mobile == new_ph).first()

    if existing_user:
        swapped_ph = swap_and_check(existing_user)
        if swapped_ph:
            return jsonify({'success': -1, 'message': f'Phone number {swapped_ph} already in use'})

        new_ph = '-' + new_ph[::-1]

    try:
        driver = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
        if not driver:
            return jsonify({'success': -1, 'message': 'Driver not found'})

        user = db.session.query(Users).filter(Users.id == driver.user).first()
        if not user:
            return jsonify({'success': -1, 'message': 'User not found'})

        log_phone_change(user.id, user.mobile, new_ph)

        user.mobile = new_ph
        db.session.commit()

        return jsonify({'success': 1, 'message': 'Phone number updated successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'message': "Rollback"})

def log_phone_change(id, old_ph, new_ph):
    try:
        mobile_change = MobileChange(id, old_ph, new_ph)
        db.session.add(mobile_change)
        db.session.commit()
    except Exception as e:
        print(f"Error logging phone change: {e}")
        db.session.rollback()

def is_valid_phone(phone):
    pattern = r'^\d{10}$'
    return bool(re.match(pattern, phone))

def swap_and_check(existing_user):
    swapped_ph = '-' + existing_user.mobile[::-1]

    already_used = db.session.query(Users).filter(or_(Users.mobile == swapped_ph, Users.mobile == existing_user.mobile)).all()

    if len(already_used) > 1:
        return swapped_ph
    else:
        return None

def find_users_by_mobile(input_mobile):
    try:
        users = db.session.query(MobileChange.id).filter(
            or_(MobileChange.mobile == input_mobile, MobileChange.change_mobile == input_mobile)
        ).distinct().all()

        user_ids = [user.id for user in users]

        return user_ids

    except Exception as e:
        print(f"An error occurred: {e}")
        return []

@admin.route('/api/admin/ph_change/log', methods=['POST'])
@jwt_required()
def ph_changelog():
    try:
        ph_no = request.form["mobile"]
    except Exception as e:
        return jsonify({'success': -2, 'error': 'ph_no error input'})
    try:
        user_ids = find_users_by_mobile(ph_no)
    except Exception as e:
        return jsonify({'success': -2, 'error': 'find_users_by_mobile'}), 401
    if not(ph_no.isdigit() and len(ph_no) == 10):
        return jsonify({'success': -1, 'error': 'Invalid or missing mobile number'}), 400
    if not user_ids:
        return jsonify({'success': 1, 'data': []}), 200

    if len(user_ids) == 1:
        try:
            user_id = user_ids[0]
            return get_user_logs(user_id,ph_no)
        except Exception as e:
            return jsonify({'success': -4, 'error': str(e)}), 400
    else:
        response = {
            'success': 1,
            'ids': user_ids
        }
        return jsonify(response), 200

@admin.route('/api/admin/ph_change/user_logs', methods=['POST'])
@jwt_required()
def user_logs():
    try:
        data = request.get_json()
        user_id = data['user_id']
        mob = data['mobile']
        try:
            return get_user_logs(user_id,mob)
        except Exception as e:
            return jsonify({'success': -4, 'error': str(e)}), 400

    except Exception as e:
        return jsonify({'success': -1, 'error': str(e)}), 401

def get_user_logs(user_id,mob):
    try:
        change_logs = MobileChange.query.filter_by(id=user_id).order_by(MobileChange.timestamp.asc()).all()

        if change_logs:
            logs = []
            last_change = None
            last_change_status = None
            for log in change_logs:
                logs.append({log.mobile: log.change_mobile, 'timestamp': log.timestamp.strftime('%Y-%m-%d %H:%M:%S')})
                last_change = log.change_mobile if log.mobile == mob else last_change
                last_change_status = "Before Account delete" if log.change_mobile == mob else 'old user'
            try:
                user = Users.query.filter_by(id=user_id).first()
                last_change_status = "Current User" if user.mobile == mob else last_change_status
            except Exception as e:
                return jsonify({'success': -1, 'error': str(e)}), 201
            response = {
                'success': 1,
                'data': logs,
                'user':user.id,
                'name':user.fname + user.lname,
                'email':user.email,
                'change_status': last_change_status,
                'change': last_change,
            }
            return jsonify(response), 200
        else:
            return jsonify({'success': -1, 'error': str(e)}), 400

    except Exception as e:
        return jsonify({'success': -2, 'error': str(e)}), 400

@admin.route('/api/admin/change/photo', methods=['POST'])
@jwt_required()
@admin_required
def photo_change():
    try:
        driver_id = int(request.form["driver"])
        url_pic = upload_pic(request.files['pic'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    if driver_id <= 0:
        return jsonify({'success': -1})
    else:
        q = db.session.query(Drivers).filter(Drivers.id==driver_id)
        u1 = q.first()
        if not u1:
            return jsonify({'success': -1})
        else:
            q.update({Drivers.pic: url_pic})
            di = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_id)
            if di.first():
                di.update({DriverInfo.pic: url_pic})
            db.session.commit()
    return jsonify({'success': 1})


@admin.route('/api/admin/minios_convert', methods=['POST'])
@jwt_required()
@admin_required
def minios_convert():
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id'])
        if booking.first():
            lat = float(get_safe(request.form, 'lat', -1))
            lng = float(get_safe(request.form, 'lng', -1))
            loc = get_safe(request.form, 'address', "")
            if(booking.first().type==BookingParams.TYPE_OUTSTATION or BookingParams.TYPE_OUTSTATION_ONEWAY):
                return jsonify({'success': -1})
            res = convert_booking_type_b2c(booking, BookingParams.TYPE_MINIOS, lat, lng, loc)
            if not res:
                return jsonify({'success': -1})

            booking = booking.first()
            if booking.type < BookingParams.TYPE_C24:
                send_live_update_of_booking( booking_id, booking.region)

            return jsonify({'success': 1})
        else:
            return jsonify({'success': -1})
        return jsonify({'success': 1})
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201

    return jsonify({'success': 1})

@admin.route('/api/admin/rt_convert', methods=['POST'])
@jwt_required()
@admin_required
def rt_convert():
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id'])
        if booking.first() and booking_has_trip(booking.first().id) and booking.first().type == BookingParams.TYPE_MINIOS:
            b = booking.first()
            pe = db.session.query(BookPricing).filter(BookPricing.book_id == b.id)
            pending_entry = pe.first()
            if not pending_entry:
                return jsonify({'success': -1})
            search = db.session.query(DriverSearch).filter(DriverSearch.id == b.search_key)
            search_entry = search.first()
            if not search_entry:
                return jsonify({'success': -1})
            end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day, search_entry.time.hour,
                                  search_entry.time.minute, search_entry.time.second)
                + timedelta(search_entry.days, search_entry.dur.hour * 3600 + search_entry.dur.minute * 60 + search_entry.dur.second))
            new_price = Price.get_price(BookingParams.TYPE_ROUNDTRIP, search_entry.dur.hour + search_entry.dur.minute/60, search_entry.time, end_time, search_entry.dist,
                                search_entry.car_type, search_entry.date, end_time.date(), LocStr.get_loc_label(search_entry.reflat, search_entry.reflong),
                                insurance=search_entry.insurance, insurance_num=search_entry.insurance_num, city=b.region)
            pe.update({BookPricing.estimate: new_price[9], BookPricing.base_ch: new_price[1], BookPricing.cartype_ch: new_price[5],
                                    BookPricing.night_ch: new_price[2], BookPricing.booking_ch: new_price[4],
                                    BookPricing.dist_ch: new_price[3], BookPricing.cgst: new_price[7], BookPricing.sgst: new_price[8],
                                    BookPricing.est_pre_tax: new_price[6], BookPricing.insurance_ch: new_price[10]})
            search.update({DriverSearch.type: BookingParams.TYPE_ROUNDTRIP})
            booking.update({Bookings.type: BookingParams.TYPE_ROUNDTRIP, Bookings.estimate: new_price[9], Bookings.estimate_pre_tax: new_price[6],
                            Bookings.insurance_cost: new_price[10]})
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': -1})
        else:
            return jsonify({'success': -1})
        return jsonify({'success': 1})
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201

    return jsonify({'success': 1})

@admin.route('/api/admin/b2c_type_convert', methods=['POST'])
@jwt_required()
@admin_required
def b2c_type_convert():
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    booking_type = int(get_safe(request.form, 'booking_type', BookingParams.TYPE_ROUNDTRIP))
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id'])
        if(booking.first().type==BookingParams.TYPE_OUTSTATION or booking.first().type==BookingParams.TYPE_OUTSTATION_ONEWAY):
                return jsonify({'success': -1})
        if booking.first():
            lat = float(get_safe(request.form, 'lat', -1))
            lng = float(get_safe(request.form, 'lng', -1))
            loc = get_safe(request.form, 'address', "")
            res = convert_booking_type_b2c(booking, booking_type, lat, lng, loc)
            if not res:
                return jsonify({'success': -1})

            if booking.type < BookingParams.TYPE_C24:
                send_live_update_of_booking( booking_id, booking.first().region)

            return jsonify({'success': 1})
        else:
            return jsonify({'success': -1})
        return jsonify({'success': 1})
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201

    return jsonify({'success': 1})


@admin.route('/api/admin/cartype_change', methods=['POST'])
@jwt_required()
@admin_required
def cartype_change():
    try:
        booking_id = int(request.form['booking_id'])
        new_cartype = int(request.form['new_cartype'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id'])
        if booking.first():
            b = booking.first()
            pe = db.session.query(BookPricing).filter(BookPricing.book_id == b.id)
            pricing_entry = pe.first()
            if not pricing_entry:
                return jsonify({'success': -1})
            search = db.session.query(DriverSearch).filter(DriverSearch.id == b.search_key)
            search_entry = search.first()
            if not search_entry:
                return jsonify({'success': -1})
            end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day, search_entry.time.hour,
                                  search_entry.time.minute, search_entry.time.second)
                + timedelta(search_entry.days, search_entry.dur.hour * 3600 + search_entry.dur.minute * 60 + search_entry.dur.second))
            if b.type != BookingParams.TYPE_OUTSTATION and b.type != BookingParams.TYPE_OUTSTATION_ONEWAY:
                new_price = Price.get_price(search_entry.type, search_entry.dur.hour + search_entry.dur.minute/60, search_entry.time, end_time, search_entry.dist,
                                new_cartype, search_entry.date, end_time.date(),0,
                                insurance=search_entry.insurance, insurance_num=search_entry.insurance_num, city=b.region)
            else:
                new_price = PriceOutstation.get_price(
                                search_entry.date, end_time.date(), 1,
                                search_entry.days * 24 + search_entry.dur.hour,
                                new_cartype, b.type, search_entry.dist,
                                insurance=search_entry.insurance,
                                insurance_num=search_entry.insurance_num,
                                city=b.region
                            )
            pe.update({BookPricing.estimate: new_price[9], BookPricing.base_ch: new_price[1], BookPricing.cartype_ch: new_price[5],
                                    BookPricing.night_ch: new_price[2], BookPricing.booking_ch: new_price[4],
                                    BookPricing.dist_ch: new_price[3], BookPricing.cgst: new_price[7], BookPricing.sgst: new_price[8],
                                    BookPricing.est_pre_tax: new_price[6], BookPricing.insurance_ch: new_price[10]})
            search.update({DriverSearch.car_type:new_cartype})
            booking.update({Bookings.estimate: new_price[9], Bookings.estimate_pre_tax: new_price[6],
                            Bookings.insurance_cost: new_price[10]})
            try:
                db.session.commit()
            except Exception as e:
                db.session.rollback()
                return jsonify({'success': -1})
        else:
            return jsonify({'success': -1})

        if b.type < BookingParams.TYPE_C24:
            send_live_update_of_booking( booking_id, b.region)

        return jsonify({'success': 1})
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 201

    return jsonify({'success': 1})


@admin.route('/api/admin/user/info', methods=['POST'])
@jwt_required()
def get_cust_info():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['user_id']):
        return jsonify({'success': -2}), 201
    uid = int(get_safe(request.form, 'user_id', -1))
    user = db.session.query(Users).filter(Users.id == uid).first()
    if not user:
        return jsonify({'success': -1})
    user_bookings = db.session.query(Bookings).filter(Bookings.user == uid). \
                               order_by(Bookings.startdate.desc(), Bookings.starttime.desc()).all()
    user_name = user.get_name()
    user_mob = user.mobile
    trip_list = []
    cancelled_unalloc = cancelled_alloc = d4m_cancelled = completed = 0
    total_trip = len(user_bookings)
    for b in user_bookings:
        # Exclude B2B trips
        if b.type >= BookingParams.TYPE_C24:
            continue
        if b.valid == -3:
            d4m_cancelled += 1
            trip_list.append({'id': b.code,
                              'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                              'driver_name': get_driver_name_from_id(b.driver),
                              'user_rating': 0,
                              'trip_status': "D4M Cancelled"})
        elif b.valid < 0:
            if b.driver == 1:
                cancelled_unalloc += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'driver_name': get_driver_name_from_id(b.driver),
                                  'user_rating': 0,
                                  'trip_status': "Cancelled"})
            else:
                cancelled_alloc += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'driver_name': get_driver_name_from_id(b.driver),
                                  'user_rating': 0,
                                  'trip_status': "Cancelled"})
        else:
            # If estimate is 0, fair to assume it's not completed
            if booking_has_trip(b.id):
                completed += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'driver_name': get_driver_name_from_id(b.driver),
                                  'user_rating': b.user_rating,
                                  'trip_status': "Completed"})
            else:

                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'driver_name': get_driver_name_from_id(b.driver),
                                  'user_rating': 0,
                                  'trip_status': "Upcoming"})
    return jsonify({'success': 1, 'user_name': user_name, 'user_mobile': user_mob,
                    'user_total': total_trip,
                    'user_d4m_cancelled': d4m_cancelled,
                    'user_cancelled_alloc': cancelled_alloc,
                    'user_cancelled_unalloc': cancelled_unalloc,
                    'user_completed': completed, 'trip_list': trip_list})


@admin.route('/api/admin/driver/info', methods=['POST'])
@jwt_required()
def get_driver_info():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['driver_id']):
        return jsonify({'success': -2}), 201
    did = int(get_safe(request.form, 'driver_id', -1))
    driver_user = db.session.query(Drivers, Users).filter(Drivers.id == did). \
                             filter(Users.id == Drivers.user).first()
    if not driver_user:
        return jsonify({'success': -1})
    driver_bookings = db.session.query(Bookings).filter(Bookings.driver == did). \
                               order_by(Bookings.startdate.desc(), Bookings.starttime.desc()).all()
    driver_name = driver_user[1].get_name()
    driver_mob = driver_user[1].mobile
    trip_list = []
    cancelled_alloc = d4m_cancelled = completed = b2c_trip = b2b_trip = 0
    total_trip = len(driver_bookings)
    for b in driver_bookings:
        # Exclude B2B trips
        if b.type >= BookingParams.TYPE_C24:
            b2b_trip += 1
            b2b_cust = BookingParams.ALL_B2B_TYPES[b.type - BookingParams.TYPE_C24]
            if b.valid == -3:
                d4m_cancelled += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'user_name': b2b_cust,
                                  'user_rating': 0,
                                  'trip_status': "D4M Cancelled"})
            elif b.valid < 0:
                cancelled_alloc += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'user_name': b2b_cust,
                                  'user_rating': 0,
                                  'trip_status': "Cancelled"})
            else:
                # If estimate is 0, fair to assume it's not completed
                if booking_has_trip(b.id):
                    completed += 1
                    trip_list.append({'id': b.code,
                                      'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                      'user_name': b2b_cust,
                                      'user_rating': 0,
                                      'trip_status': "Completed"})
                else:
                    trip_list.append({'id': b.code,
                                      'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                      'user_name': b2b_cust,
                                      'user_rating': 0,
                                      'trip_status': "Upcoming"})
        else:
            b2c_trip += 1
            if b.valid == -3:
                d4m_cancelled += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'user_name': get_user_name_from_id(b.user),
                                  'user_rating': 0,
                                  'trip_status': "D4M Cancelled"})
            elif b.valid < 0:
                cancelled_alloc += 1
                trip_list.append({'id': b.code,
                                  'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                  'user_name': get_user_name_from_id(b.user),
                                  'user_rating': 0,
                                  'trip_status': "Cancelled"})
            else:
                # If estimate is 0, fair to assume it's not completed
                if booking_has_trip(b.id):
                    completed += 1
                    trip_list.append({'id': b.code,
                                      'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                      'user_name': get_user_name_from_id(b.user),
                                      'user_rating': b.user_rating,
                                      'trip_status': "Completed"})
                else:
                    trip_list.append({'id': b.code,
                                      'trip_date': get_dt_ist(b.startdate, b.starttime).strftime("%a, %d/%m/%Y"),
                                      'user_name': get_user_name_from_id(b.user),
                                      'user_rating': 0,
                                      'trip_status': "Upcoming"})
    return jsonify({'success': 1, 'driver_name': driver_name, 'driver_mobile': driver_mob,
                    'driver_total': total_trip,
                    'driver_d4m_cancelled': d4m_cancelled,
                    'driver_cancelled_alloc': cancelled_alloc,
                    'driver_completed': completed,
                    'driver_total_b2b': b2b_trip,
                    'driver_total_b2c': b2c_trip,
                    'trip_list': trip_list})

def _get_booking_pic(booking_id):
    res = []
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    if not booking:
        return jsonify({'success': -1})
    tstart_pic = db.session.query(TripStartPic).filter(
        TripStartPic.book_id == booking.id).first()
    tstop_pic = db.session.query(TripEndPic).filter(
        TripEndPic.book_id == booking.id).first()

    def get_b2c_path(pic):
        file_path = os.path.join(app.config['UPLOAD_FOLDER'],
                                 "book-" + str(booking.id))
        return os.path.join(file_path, pic)
    if tstart_pic:
        if tstart_pic.car_left:
            res.append({"type": "Car Left", "pic_url": get_b2c_path(tstart_pic.car_left),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.car_right:
            res.append({"type": "Car Right", "pic_url": get_b2c_path(tstart_pic.car_right),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.car_back:
            res.append({"type": "Car Back", "pic_url": get_b2c_path(tstart_pic.car_back),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.car_front:
            res.append({"type": "Car Front", "pic_url": get_b2c_path(tstart_pic.car_front),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.selfie:
            res.append({"type": "Selfie", "pic_url": get_b2c_path(tstart_pic.selfie),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.extra1:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstart_pic.extra1),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.extra2:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstart_pic.extra2),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.extra3:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstart_pic.extra3),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstart_pic.extra4:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstart_pic.extra4),
                    "timestamp": (tstart_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
    if tstop_pic:
        if tstop_pic.car_left:
            res.append({"type": "Car Left", "pic_url": get_b2c_path(tstop_pic.car_left),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.car_right:
            res.append({"type": "Car Right", "pic_url": get_b2c_path(tstop_pic.car_right),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.car_back:
            res.append({"type": "Car Back", "pic_url": get_b2c_path(tstop_pic.car_back),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.car_front:
            res.append({"type": "Car Front", "pic_url": get_b2c_path(tstop_pic.car_front),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.extra1:
            res.append({"type": "Extra", "pic_url":get_b2c_path( tstop_pic.extra1),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.extra2:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstop_pic.extra2),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.extra3:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstop_pic.extra3),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})
        if tstop_pic.extra4:
            res.append({"type": "Extra", "pic_url": get_b2c_path(tstop_pic.extra4),
                    "timestamp": (tstop_pic.timestamp + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')})

    if booking.type >= BookingParams.TYPE_C24:
        booking_table_map = {
            BookingParams.TYPE_C24: [C24Bookings, C24Pic, "c24"],
            BookingParams.TYPE_ZOOMCAR: [ZoomcarBookings, ZoomcarPic, "zoomcar"],
            BookingParams.TYPE_OLX: [OLXBookings, OLXPic, "olx"],
            BookingParams.TYPE_CARDEKHO: [CardekhoBookings, CardekhoPic, "cardekho"],
            BookingParams.TYPE_BHANDARI: [BhandariBookings, BhandariPic, "bhandari"],
            BookingParams.TYPE_MAHINDRA: [MahindraBookings, MahindraPic, "mahindra"],
            BookingParams.TYPE_SPINNY: [SpinnyBookings, SpinnyPic, "spinny"],
            BookingParams.TYPE_REVV_V2: [RevvV2Bookings, RevvV2Pic, "revv_v2"],
            BookingParams.TYPE_PRIDEHONDA: [PrideHondaBookings, PrideHondaPic, "pridehonda"],
        }
        booking_tables = booking_table_map.get(booking.type)
        if not booking_tables:
            return ({"sucess": -1, "reason": "Not implemented"})
        b2b_table, pic_table, prefix = booking_tables
        b2b_id = db.session.query(b2b_table).filter(b2b_table.ref == booking.id).first()
        if not b2b_id:
            return ({"sucess": -1, "reason": "Not present"})
        b2b_pic_list = db.session.query(pic_table).filter(pic_table.booking_id == b2b_id.id).all()
        for b2b_pic in b2b_pic_list:
            file_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'],
                                     prefix + str(b2b_pic.booking_id))
            file_path_upload = os.path.join(app.config['UPLOAD_FOLDER'],
                                     prefix + str(b2b_pic.booking_id))
            file_loc = os.path.join(file_path, b2b_pic.pic)
            file_loc_url = os.path.join(file_path_upload, b2b_pic.pic)
            try:
                file_m_ts = os.path.getmtime(file_loc)
            except IOError:
                print("Could not find file %s" % file_loc)
                continue
            ts_str = (datetime.fromtimestamp(file_m_ts) + _sms.IST_OFFSET_TIMEDELTA).strftime('%Y-%m-%d %H:%M:%S')
            res.append({"type": str("Pic Index " + str(b2b_pic.idx)),
                        "pic_url": file_loc_url,
                        "timestamp": ts_str})
    return res

@admin.route('/api/admin/booking_pic', methods=['POST'])
@jwt_required()
def booking_pic_fetch():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id']):
        print("Not complete")
        return jsonify({'success': -2}), 201
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    res = _get_booking_pic(request.form["booking_id"])
    return jsonify({"success": 1, "pic_list": res})

@admin.route('/api/admin/trip_state_change', methods=['POST'])
@jwt_required()
def admin_trip_state_change():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'state_expected']):
        return jsonify({'success': -2}), 201
    try:
        booking_id = int(request.form['booking_id'])
        state_expected = int(request.form['state_expected'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    driver_booked = db.session.query(Bookings, Drivers).filter(Drivers.id == Bookings.driver). \
                    filter(Bookings.id == booking_id).first()
    ret = change_trip_state(booking_id, state_expected, driver_booked[1].user,
                            get_jwt_identity(), request=request)
    return jsonify({"success": int(ret) - 1})

@admin.route('/api/admin/user_label/get', methods=['POST'])
@jwt_required()
def admin_user_label_get():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -2}), 201
    try:
        mobile = int(request.form['mobile'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    user = db.session.query(Users).filter(Users.mobile == mobile).first()
    if not user:
        return jsonify({'success': -2})
    uname = user.get_name()
    labels = user.label_bv
    is_user = 0
    if user.role != Users.ROLE_DRIVER:
        user_id = user.id
        is_user = 0
    else:
        driver = db.session.query(Drivers).filter(Drivers.user == user.id).first()
        if not driver:
            return jsonify({'success': -3})
        user_id = driver.id
        is_user = 1
    return jsonify({"success": 1, "id": user_id, "name": uname, "mobile": mobile, "labels": labels,
                    "is_user": is_user})

@admin.route('/api/admin/user_label/set', methods=['POST'])
@jwt_required()
def admin_user_label_set():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['mobile', 'label']):
        return jsonify({'success': -2}), 201
    try:
        mobile = int(request.form['mobile'])
        label = int(request.form['label'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    user = db.session.query(Users).filter(Users.mobile == mobile)
    if not user.first():
        return jsonify({'success': -2})
    user.update({Users.label_bv: label})
    try:
        db.session.commit()
    except Exception as e:
        print(e)
        return jsonify({'success': -3})
    return jsonify({"success": 1})

def handle_penalty_admin(admin_user, booking, reason, reason_detail, is_ontrip):

    ut_id=None
    dt_id=None
    if booking.driver != 1:
        starttime = datetime(booking.startdate.year, booking.startdate.month,
                            booking.startdate.day, booking.starttime.hour,
                            booking.starttime.minute, booking.starttime.second)
        if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            penalty, level = PriceOutstation.get_cancel_ch(datetime.utcnow(), starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=reason, is_ontrip=is_ontrip)
        else:
            penalty, level = Price.get_cancel_ch(datetime.utcnow(), starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=reason, is_ontrip=is_ontrip)
        user_fine, driver_fine = penalty
        last_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking.id).filter(BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first()
        if last_alloc_time:
            last_alloc_time=last_alloc_time.timestamp
        if (last_alloc_time and reason in BookingCancelled.WAIVER
                        and datetime.utcnow() - last_alloc_time < BookingCancelled.FORGIVE_DELTA or booking.driver ==1):
                        user_fine = 0
                        driver_fine = 0
                        waiver = True
        if user_fine > 0:
            uc = UserCancelled(booking.user, booking.id, user_fine)
            ut = UserTrans(uid=booking.user, amt=(100*user_fine)*(-1), method="Cancellation charges for #" + str(booking.id),
                            status=UserTrans.COMPLETED)
            db.session.query(Users).filter(Users.id == booking.user).update({Users.credit: Users.credit - user_fine})
            db.session.add(uc)
            db.session.add(ut)
            ut_id=ut.id
        else:
            uc = UserCancelled(booking.user, booking.id, 0)
            db.session.add(uc)

        if driver_fine > 0:
            dc = DriverCancelled(booking.driver, booking.id, driver_fine)
            db.session.add(dc)

            details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver)
            driver_details = details.first()
            wallet, withdrawable = compute_driver_wallet(driver_details, driver_fine)
            dt = DriverTrans(booking.driver, -driver_fine*100,
                            wall_a=wallet, wall_b=driver_details.wallet,
                            with_a=withdrawable, with_b=driver_details.withdrawable,
                                method="Cancellation: Booking %s" % str(booking.code),
                            status=DriverTrans.COMPLETED, stop=True
                            )
            db.session.add(dt)
            dt_id=dt.id
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
                update({DriverDetails.owed: DriverDetails.owed + driver_fine,
                        DriverDetails.wallet: wallet,
                        DriverDetails.withdrawable: withdrawable})
        else:
            dc = DriverCancelled(booking.driver, booking.id, 0)
            db.session.add(dc)
        bc = BookingCancelled(user=admin_user, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid=booking.user,
                      did=booking.driver, penalty_user=user_fine, penalty_driver=driver_fine, rsn=reason,
                      reason_detail=reason_detail,utransid=ut_id,dtransid=dt_id)
    else:
        uc = UserCancelled(booking.user, booking.id, 0)
        db.session.add(uc)
        bc = BookingCancelled(user=admin_user, cancel_source=BookingCancelled.SRC_ADMIN, booking=booking.id, uid=booking.user,
                      did=booking.driver, penalty_user=0, penalty_driver=0, rsn=reason,
                      reason_detail=reason_detail,utransid=ut_id,dtransid=dt_id)
    try:
        db.session.commit()
        db.session.add(bc)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return False
    return True

@admin.route('/api/admin/cancelupdate/charge', methods=['POST'])
@jwt_required()
def admin_cancel_updatecharge():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['cancel_id', 'new_reason']):
        return jsonify({'success': -1}), 201
    book_cancel_id=request.form["cancel_id"]
    cb = db.session.query(BookingCancelled).filter(BookingCancelled.id == book_cancel_id)
    cb1=cb.first()
    booking_id=cb1.booking
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    new_reason = int(get_safe(request.form, 'new_reason', BookingCancelled.RSN_NO_ALLOC))
    trip = fetch_booking_trip(booking_id).first()
    is_ontrip = trip is not None
    starttime = datetime(booking.startdate.year, booking.startdate.month,booking.startdate.day, booking.starttime.hour,booking.starttime.minute,
                             booking.starttime.second)
    if (new_reason in BookingCancelled.WAIVER):
        return jsonify({'success': 1, 'updatedcharge': [0,0]})
    if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
        updated_penalty,level = PriceOutstation.get_cancel_ch(cb1.timestamp, starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=new_reason, is_ontrip=is_ontrip)
    else:
        updated_penalty,level = Price.get_cancel_ch(cb1.timestamp, starttime, city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=new_reason, is_ontrip=is_ontrip)
    return jsonify({'success': 1, 'updatedcharge': updated_penalty})

@admin.route('/api/admin/cancel/charge', methods=['POST'])
@jwt_required()
def admin_cancel_charge():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'reason']):
        return jsonify({'success': -1}), 201
    booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id']).first()
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_NO_ALLOC))
    trip = fetch_booking_trip(booking.id).first()
    is_ontrip = trip is not None
    if booking.driver != 1:
        last_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking.id).filter(BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first()
        if last_alloc_time:
            last_alloc_time=last_alloc_time.timestamp
        if (last_alloc_time and reason in BookingCancelled.WAIVER
            and datetime.utcnow() - last_alloc_time < BookingCancelled.FORGIVE_DELTA or
            booking.driver == 1):
            return jsonify({'success': 1, 'charge': [0,0]})
        if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            penalty, level = PriceOutstation.get_cancel_ch(datetime.utcnow(), datetime.combine(booking.startdate,booking.starttime), city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=reason,
                                         is_ontrip=is_ontrip)
        else:
            penalty, level = Price.get_cancel_ch(datetime.utcnow(), datetime.combine(booking.startdate,booking.starttime), city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=reason,
                                         is_ontrip=is_ontrip)
        return jsonify({'success': 1, 'charge': penalty})
    else:
        return jsonify({'success': 1, 'charge': [0,0]})

# d4m cancel
@admin.route('/api/admin/cancel', methods=['POST'])
@jwt_required()
def admin_cancel():
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'reason']):
        return jsonify({'success': -1}), 201
    booking_id = request.form['booking_id']
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_NO_ALLOC))
    reason_detail = str(get_safe(request.form, 'reason_details', BookingCancelled.RSN_NO_ALLOC))
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == request.form['booking_id'])
        book_code = booking.first().code
        btype = booking.first().type
        if booking.first().valid < 0:
            # SV-49: No double cancels
            return jsonify({'success': -2})
        booking.update({Bookings.valid: -3})
        booking.update({Bookings.cancelled_dt: datetime.utcnow()})
        booking = booking.first()
        _update_user_pending(booking.user)
        _update_driver_pending(booking.driver)
        trip_ex = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
        penalty_result=handle_penalty_admin(get_jwt_identity(), booking, reason, reason_detail, trip_ex)
        if not penalty_result:
            return jsonify({'success': -2}), 500
        if trip_ex:
            db.session.delete(trip_ex)
            try:
                fb_db.collection(u'trip_started').document(str(booking.user)).delete()
            except Exception as e:
                pass
        admin_log = AdminLog(get_jwt_identity(), 'Cancelled by Admin:', json.dumps(str(request.form)) +
                             "Booking #" + request.form['booking_id'])
        admin_name = db.session.query(Users).filter(Users.id == get_jwt_identity()).first().get_name()
        send_slack_msg(1, admin_name + " cancelled booking id " + str(request.form['booking_id']))
        target_user = db.session.query(Users).filter(Users.id == booking.user).first()
        user_mobile = _sms.COUNTRY_CODE_IN + str(target_user.mobile)
        start_time = datetime(year=booking.startdate.year, month=booking.startdate.month,
                              day=booking.startdate.day, hour=booking.starttime.hour,
                              minute=booking.starttime.minute, second=booking.starttime.second)
        start_time_ist = start_time + _sms.IST_OFFSET_TIMEDELTA
        trip = fetch_booking_trip(booking.id).first()
        is_ontrip = trip is not None
        if booking.driver != 1:
            last_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking.id).filter(BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first()
            if last_alloc_time:
                last_alloc_time=last_alloc_time.timestamp
            if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
                penalty, level = PriceOutstation.get_cancel_ch(datetime.utcnow(), datetime.combine(booking.startdate,booking.starttime), city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=reason,
                                            is_ontrip=is_ontrip)
            else:
                penalty, level = Price.get_cancel_ch(datetime.utcnow(), datetime.combine(booking.startdate,booking.starttime), city=booking.region if booking.region not in [Regions.REGN_ALAMPUR] else Regions.REGN_KOLKATA, cancel_cat=reason,
                                            is_ontrip=is_ontrip)
            user_fine, driver_fine = penalty
            if (last_alloc_time and reason in BookingCancelled.WAIVER
                and datetime.utcnow() - last_alloc_time < BookingCancelled.FORGIVE_DELTA or booking.driver == 1):
                driver_fine=0
                user_fine=0

        else:
            driver_fine=0
        # FIXME: Fix slack and SMS content
        # Dictionary to map reason codes to their message details
        reason_details_dict_user = {
            "0": " because you changed plans.",
            "1": " because the driver denied the booking.",
            "2": " because your favorite driver was not available.",
            "3": " because the driver requested extra fare.",
            "4": " because the driver asked to take the booking offline.",
            "5": " because you selected the wrong location.",
            "6": " because you selected a different service.",
            "7": " because you booked by mistake.",
            "8": " because the wait time was too long.",
            "9": " because you got a driver elsewhere.",
            "10": " because you were checking the price estimate.",
            "11": " because it was taking too long to allocate a driver.",
            "12": " because you took the driver offline.",
            "13": " because it was wrongly taken by the driver.",
            "14": " because the previous trip of the driver was not ended.",
            "15": " due to drivers's personal issues.",
            "16": " due to drivers's transportation problems.",
            "17": " because you were not responding.",
            "18": " because you asked to cancel.",
            "62": " for other reasons.",
        }


        reason_details_dict_driver = {
            "0": " due to customer changed plans.",
            "1": " because you denied the booking.",
            "2": " because the customer's favorite driver was not available.",
            "3": " because you requested extra fare.",
            "4": " because you asked to take the booking offline.",
            "5": " because the customer selected the wrong location.",
            "6": " because the customer selected a different service.",
            "7": " because the customer booked by mistake.",
            "8": " because the wait time was too long.",
            "9": " because the customer got a driver elsewhere.",
            "10": " because the customer was checking the price estimate.",
            "11": " because it was taking too long to allocate a driver.",
            "12": " because you took the trip offline.",
            "13": " because you took the trip wrongly.",
            "14": " because your previous trip was not ended.",
            "15": " due to your personal issues.",
            "16": " due to your transportation problems.",
            "17": " because the customer was not responding.",
            "18": " because the customer asked to cancel.",
            "62": " for other reasons."
        }

        # Retrieve the reason detail from the dictionary
        reason_detail_message_user = reason_details_dict_user.get(str(reason))
        reason_detail_message_driver = reason_details_dict_driver.get(str(reason))
        # user_name = target_user.get_name().encode('latin-1', errors='replace').decode('latin-1')
        # msg_content = "Sorry, " + user_name + "! Your trip with DRIVERS4ME (ID: " + book_code + \
        #               ") was cancelled. We hope that we can serve you better next time."
        try:
            # send_slack_msg(1, msg_content)
            if btype < BookingParams.TYPE_C24:
                Bookings.query.filter(Bookings.id == booking_id).update({Bookings.did_release: True})
                if reason == BookingCancelled.RSN_NO_ALLOC:
                    send_fcm_msg(booking.user, title=f"Cancellation of booking #{booking.code}",
                                smalltext=f"Sorry, Drivers4Me cancelled the booking #{booking.code}",
                                bigtext=f"Sorry, Drivers4Me cancelled the booking #{booking.code} allocated to you "
                                        "as it could not be allocated to a driver. "
                                        "We hope that we can serve you better next time.")
                else:
                    if user_fine > 0:
                        send_fcm_msg(booking.user, title=f"Cancellation of booking #{booking.code}",
                                    smalltext=f"Sorry, Drivers4Me cancelled the booking #{booking.code}",
                                    bigtext=f"Sorry, Drivers4Me cancelled the booking #{booking.code} "
                                            f"{reason_detail_message_user} You were charged a penalty of ₹{user_fine}.")
                    else:
                        send_fcm_msg(booking.user, title=f"Cancellation of booking #{booking.code}",
                                    smalltext=f"Sorry, Drivers4Me cancelled the booking #{booking.code}",
                                    bigtext=f"Sorry, Drivers4Me cancelled the booking #{booking.code} "
                                            f"{reason_detail_message_user} We hope that we can serve you better next time.")
            if driver_fine>0:
                send_fcm_msg_driver(booking.driver, title=f"Cancellation of booking #{booking.code}",
                                smalltext=f"Drivers4Me cancelled the booking #{booking.code}",
                                bigtext=f"Drivers4Me cancelled the booking #{booking.code} allocated to you{reason_detail_message_driver} "
                                        f"You were charged a penalty of ₹{driver_fine}.")
            else:
                send_fcm_msg_driver(booking.driver, title=f"Cancellation of booking #{booking.code}",
                                smalltext=f"Drivers4Me cancelled the booking #{booking.code}",
                                bigtext=f"Drivers4Me cancelled the booking #{booking.code} allocated to you{reason_detail_message_driver}")
        except Exception:
            pass

        # if btype >= BookingParams.TYPE_C24:
        #     resp = True
        # else:
        #     msg_content_json = {"name": user_name, "code": book_code}
        #     resp = _sms.send_msg_flow(str(user_mobile), _sms.FLOWS["d4m-cancelled"], msg_content_json)

        if booking.type == BookingParams.TYPE_ZOOMCAR:
            try:
                if booking.driver == 1:
                    _zoomcar_change_state(ZOOMCAR_STATE_REJECTED, booking.id)
                else:
                    _zoomcar_change_state(ZOOMCAR_STATE_CANCELED, booking.id)
            except Exception as e:
                print("Could not set state: error %s" % str(e))
        db.session.add(admin_log)
        db.session.commit()

        if booking.type < BookingParams.TYPE_C24:
            send_live_update_of_booking( booking_id, booking.region)

        return jsonify({'success': 1}), 200
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201


@admin.route('/api/admin/customer_credit_log', methods=['POST'])
@jwt_required()
def admin_customer_credit_log():
    claims = get_jwt()
    admin_user = get_jwt_identity()
    if not validate_role(admin_user, claims['roles'], [*Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'success': -1}), 401
    if not account_enabled(admin_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1}), 201
    mobile = request.form['mobile']
    method = request.form.get('method')
    status = request.form.get('status')

    user = db.session.query(Users).filter(Users.mobile == int(mobile)).first()
    if not user:
        return jsonify({'success': -1}), 500
    credit = user.credit
    query = db.session.query(UserTrans).filter(UserTrans.user_id == user.id).filter(UserTrans.amount != 0)
    if method:
        query = query.filter(UserTrans.method.like(f"{method}%"))
    else:
        methods = ["D4M credit", "Admin panel", "Paytm", "Razorpay", "Cancellation", "Referral"]
        query = query.filter(or_(*[UserTrans.method.like(f"{method}%") for method in methods]))
    if status:
        query = query.filter(UserTrans.status == int(status))
    else:
        query = query.filter(UserTrans.status == UserTrans.COMPLETED)

    user_trans = query.order_by(UserTrans.start_timestamp.desc()).all()

    if not user_trans:
        return jsonify({'success': -1, 'message': 'No transactions found'}), 200

    result_json = []
    result_json.append({'name': user.fname + ' ' + user.lname, 'email': user.email, 'sex': user.sex, 'label': user.label_bv, 'role': user.role, 'credit': user.credit})
    for ut in user_trans:
        booking_id = extract_booking_id(ut.method)
        method_summary = get_method_summary(ut.method)
        res_data = {
            'amt': round(ut.amount / 100, 2),
            'method': method_summary,
            'remark': f"{ut.remark} || {ut.method}",
            'timestamp': get_ist_time(ut.start_timestamp),
            'status': ut.status,
            'booking_id': booking_id,
            'admin_name': ut.admin_name,
            'admin_id': ut.admin_id
        }
        result_json.append(res_data)
    return jsonify({'success': 1, 'balance': round(credit, 2), 'data': result_json})

@admin.route('/admin/search-loc.html', methods=['GET'])
@jwt_required()
@admin_required
def search_loc_admin():
    return render_template('search-loc.html')
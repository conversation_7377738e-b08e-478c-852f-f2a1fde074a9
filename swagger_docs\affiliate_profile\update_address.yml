tags:
  - Affiliate_Profile
summary: Update Address
description: >
  Updates the details of an existing address.
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: object
      properties:
        address_id:
          type: integer
          required: true
          description: Address ID.
        address:
          type: string
          description: New address.
        nickname:
          type: string
          description: New nickname.
        lat:
          type: number
          format: float
          description: New latitude.
        long:
          type: number
          format: float
          description: New longitude.
        loc_name:
          type: string
          description: New location name.
responses:
  200:
    description: Address updated successfully.
    schema:
      type: object
      properties:
        message:
          type: string
          example: Address updated successfully.
  400:
    description: Missing address_id.
    schema:
      type: object
      properties:
        error:
          type: string
          example: address_id is required.
  404:
    description: Address not found.
    schema:
      type: object
      properties:
        error:
          type: string
          example: Address with address_id 123 does not exist.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        error:
          type: string
          example: An error occurred.

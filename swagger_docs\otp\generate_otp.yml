tags:
  - Authentication
summary: Generate OTP for user login/verification
description: Generates an OTP for the user based on the provided mobile number.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: new
    in: formData
    type: integer
    required: false
    description: Whether the user is new (1 for new, 0 for existing)
responses:
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Incomplete form"
    examples:
      application/json:
        success: -1
        message: "Incomplete form"
  401_a:
    description: Database error when querying the user
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"
  401_b:
    description: User is restricted from generating OTP
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -2
        message: "User restricted"
  200:
    description: OTP generated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
    examples:
      application/json:
        success: 1

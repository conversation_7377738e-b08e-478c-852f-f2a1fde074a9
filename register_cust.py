#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  register_cust.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>


from flask import Blueprint, request, render_template, jsonify, redirect
from sqlalchemy import exc
from flask import current_app as app

from _utils import complete, validate_fields, get_salt, get_pwd
from models import Users, AdminUserLog
from models import db
import time

from socketio_app import send_notification_to_channel, live_update_to_channel
from flasgger import swag_from

regcust = Blueprint('register_cust', __name__)

'''
@regcust.route('/')
def register_cust():
    if True or 'MAIN_SERVER' not in app.config or app.config['MAIN_SERVER']:
        return render_template('homepage.html')
    else:
        return render_template('adminLogin.html')
'''

@regcust.route('/api/register_cust', methods=['POST'])
@swag_from('/app/swagger_docs/users/register_cust.yml')
def register_cust_scr():
    if request.method == 'POST':
        if not complete(request.form, ['mobile', 'pwd', 'fname']):
            return jsonify([{'response': 1, 'msg': "Incomplete form"}])
        else:
            if len(request.form['fname']) < 1:
                return jsonify([{'response': 1, 'msg': "Provide first name"}])
            e_mail = request.form.get('email', '')
            lname = request.form.get('lname', '')
            mobile = request.form['mobile'].replace("+91","")
            if validate_fields(mobile, e_mail):
                exist_user = db.session.query(Users, AdminUserLog).filter(Users.id == AdminUserLog.user). \
                                                filter(Users.mobile == mobile).first()
                if exist_user and exist_user[1].action == AdminUserLog.USER_CREATED:
                    exist_user[0].fname = request.form['fname']
                    exist_user[0].lname = lname
                    pwd_salt = get_salt()
                    exist_user[0].salt = pwd_salt
                    exist_user[0].pwd = get_pwd(request.form['pwd'], pwd_salt)
                    exist_user[1].action = AdminUserLog.USER_UPDATED
                else:
                    user = Users(mobile=mobile, fname=request.form['fname'],
                             lname=lname, email=e_mail, pwd=request.form['pwd'], role=Users.ROLE_USER)
                    db.session.add(user)
                try:
                    user = db.session.query(Users).filter(Users.mobile == mobile).first()
                    notification = {
                        'id': mobile,
                        'type': 'App Register',
                        'username': str(user.fname + " " + user.lname),
                        'content' : f"New customer {request.form['fname']} {lname} registered.",
                        'imageUrl': '/assets/icons/register_icon.svg',
                        'timestamp': int(time.time() * 1000),
                        'user_id':user.id
                    }
                    send_notification_to_channel(notification, 'Customer Register:'+ str(user.region))
                    db.session.commit()
                except exc.IntegrityError:
                    db.session.rollback()
                    return jsonify([{'response': 4, 'msg': "Integrity constraint error"}])
                return jsonify([{'response': 0, 'msg': "Success"}])
            else:
                return jsonify([{'response': 2, 'msg': "Invalid fields"}])
    else:
        return jsonify([{'response': 3, 'msg': "Bad request"}])

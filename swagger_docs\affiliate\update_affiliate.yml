tags:
  - Affiliate
summary: Update Affiliate
description: >
  This endpoint allows updation of Affiliate.
parameters:
  - name: body
    in: body
    required: true
    description: JSON payload containing client details, form fields, and pricing details.
    schema:
      type: object
      properties:
        changedFields:
          type: object
          description: Tracks changes in affiliate data and pricing configurations.
          properties:
            affMainChanges:
              type: array
              description: List of main changes in affiliate data.
              items:
                type: object
                properties:
                  field:
                    type: string
                    description: The field that was changed.
                  oldValue:
                    type: string
                    description: The old value of the field before the change.
                  newValue:
                    type: string
                    description: The new value of the field after the change.
            affMainRoundChanges:
                type: array
                description: List of round-trip specific main changes in affiliate data.
                items:
                  type: string
            affCustImagesChanges:
                type: string
                description: Indicates if changes occurred in customer images configuration (yes/no).
            affMainCustomChanges:
                type: string
                description: Indicates if changes occurred in custom main configurations (yes/no).
            affMainCustomRoundChanges:
                type: string
                description: Indicates if changes occurred in round-trip custom configurations (yes/no).
            pricingCustChargeChanges:
                type: string
                description: Indicates if changes occurred in customer charge pricing (yes/no).
            pricingCustBaseChanges:
                type: string
                description: Indicates if changes occurred in customer base pricing (yes/no).
            pricingDriverBaseChanges:
                type: string
                description: Indicates if changes occurred in driver base pricing (yes/no).
            pricingDriverChargeChanges:
                type: string
                description: Indicates if changes occurred in driver charge pricing (yes/no).
            pricingRoundCustBaseChanges:
                type: string
                description: Indicates if changes occurred in round-trip customer base pricing (yes/no).
            pricingRoundDriverBaseChanges:
                type: string
                description: Indicates if changes occurred in round-trip driver base pricing (yes/no).
            pricingRoundCustChargeChanges:
                type: string
                description: Indicates if changes occurred in round-trip customer charge pricing (yes/no).
            pricingRoundDriverChargeChanges:
                type: string
                description: Indicates if changes occurred in round-trip driver charge pricing (yes/no).
            cancelChargeChanges:
                type: string
                description: Indicates if changes occurred in cancellation charge configurations (yes/no).
            cancelChargeStaticChanges:
                type: string
                description: Indicates if changes occurred in static cancellation charge configurations (yes/no).
        master:
          type: string
          description: Master for this Affiliate (Default 0).
        regions:
          type: string
          description: A comma-separated list of region IDs for filtering.
        clientDetails:
          type: object
          description: Details of client.
          properties:
            clientName:
              type: string
              description: Name of the client.
            clientDisplayName:
              type: string
              description: Display name of the client.
            cityLabel:
              type: string
              description: Label for city selection.
            cityPlaceholder:
              type: string
              description: Placeholder for city selection.
            selectedCities:
              type: string
              description: Comma-separated list of selected city IDs.
            tripTypeLabel:
              type: string
              description: Label for trip type selection.
            tripTypePlaceholder:
              type: string
              description: Placeholder for trip type selection.
            tripTypes:
              type: array
              description: List of trip types.
              items:
                type: object
                properties:
                  trip_type_name:
                    type: string
                    description: Name of the trip type.
                  trip_type_category:
                    type: string
                    description: Category of the trip type.
                  tripIndex:
                    type: integer
                    description: Index of the trip type.
                  tripType:
                    type: string
                    description: Type of the trip (e.g., One Way, Round).
                  startImages:
                    type: array
                    description: List of start images.
                    items:
                      type: object
                      properties:
                        imageTitle:
                          type: string
                          description: Title of the start image.
                        imageType:
                          type: string
                          description: Type of the image.
                        imageDesc:
                          type: string
                          description: Description of the image.
                        imageTripType:
                          type: string
                          description: Trip type associated with the image.
                        imageTripStage:
                          type: string
                          description: Trip stage associated with the image.
                  stopImages:
                    type: array
                    description: List of stop images.
                    items:
                      type: object
                      properties:
                        imageTitle:
                          type: string
                          description: Title of the stop image.
                        imageType:
                          type: string
                          description: Type of the image.
                        imageDesc:
                          type: string
                          description: Description of the image.
                        imageTripType:
                          type: string
                          description: Trip type associated with the image.
                        imageTripStage:
                          type: string
                          description: Trip stage associated with the image.
        form_fields_details:
          type: object
          description: Details of form fields for different trip types.
          properties:
            one_way:
              type: object
              description: Form fields for one-way trips.
              properties:
                affiliateIdEnabled:
                  type: string
                  description: Whether affiliate ID is enabled for one-way trips.
                affiliate_id_label:
                  type: string
                  description: Label for affiliate ID field.
                affiliate_id_placeholder:
                  type: string
                  description: Placeholder for affiliate ID field.
                destination_spoc_name_label:
                  type: string
                  description: Label for destination SPOC name field.
                destination_spoc_name_placeholder:
                  type: string
                  description: Placeholder for destination SPOC name field.
                destination_spoc_number_label:
                  type: string
                  description: Label for destination SPOC number field.
                destination_spoc_number_placeholder:
                  type: string
                  description: Placeholder for destination SPOC number field.
                dropoff_addr_label:
                  type: string
                  description: Label for drop-off address field.
                dropoff_addr_placeholder:
                  type: string
                  description: Placeholder for drop-off address field.
                pickup_addr_label:
                  type: string
                  description: Label for pick-up address field.
                pickup_addr_placeholder:
                  type: string
                  description: Placeholder for pick-up address field.
                source_spoc_name_label:
                  type: string
                  description: Label for source SPOC name field.
                source_spoc_name_placeholder:
                  type: string
                  description: Placeholder for source SPOC name field.
                source_spoc_number_label:
                  type: string
                  description: Label for source SPOC number field.
                source_spoc_number_placeholder:
                  type: string
                  description: Placeholder for source SPOC number field.
                vehicleModelEnabled:
                  type: string
                  description: Whether vehicle model is enabled for one-way trips.
                custom_fields_one_way:
                  type: array
                  description: Custom fields specific to one-way trips.
                  items:
                    type: object
                    properties:
                      fieldType:
                        type: string
                        description: Type of custom field (e.g., Text, Dropdown).
                      title:
                        type: string
                        description: Title of the custom field.
                      placeholder:
                        type: string
                        description: Placeholder text for the custom field.
                      dropdownFields:
                        type: array
                        description: Dropdown options for the custom field, if applicable.
                        items:
                          type: string
            round:
              type: object
              description: Form fields for round trips.
              properties:
                affiliateIdEnabled:
                  type: string
                  description: Whether affiliate ID is enabled for round trips.
                affiliate_id_label:
                  type: string
                  description: Label for affiliate ID field.
                affiliate_id_placeholder:
                  type: string
                  description: Placeholder for affiliate ID field.
                destination_spoc_name_label:
                  type: string
                  description: Label for destination SPOC name field.
                destination_spoc_name_placeholder:
                  type: string
                  description: Placeholder for destination SPOC name field.
                destination_spoc_number_label:
                  type: string
                  description: Label for destination SPOC number field.
                destination_spoc_number_placeholder:
                  type: string
                  description: Placeholder for destination SPOC number field.
                dropoff_addr_label:
                  type: string
                  description: Label for drop-off address field.
                dropoff_addr_placeholder:
                  type: string
                  description: Placeholder for drop-off address field.
                pickup_addr_label:
                  type: string
                  description: Label for pick-up address field.
                pickup_addr_placeholder:
                  type: string
                  description: Placeholder for pick-up address field.
                source_spoc_name_label:
                  type: string
                  description: Label for source SPOC name field.
                source_spoc_name_placeholder:
                  type: string
                  description: Placeholder for source SPOC name field.
                source_spoc_number_label:
                  type: string
                  description: Label for source SPOC number field.
                source_spoc_number_placeholder:
                  type: string
                  description: Placeholder for source SPOC number field.
                vehicleModelEnabled:
                  type: string
                  description: Whether vehicle model is enabled for round trips.
                custom_fields_round:
                  type: array
                  description: Custom fields specific to round trips.
                  items:
                    type: object
                    properties:
                      fieldType:
                        type: string
                        description: Type of custom field (e.g., Text, Dropdown).
                      title:
                        type: string
                        description: Title of the custom field.
                      placeholder:
                        type: string
                        description: Placeholder text for the custom field.
                      dropdownFields:
                        type: array
                        description: Dropdown options for the custom field, if applicable.
                        items:
                          type: string
        customer_pricing_data:
          type: object
          description: Pricing details for customers including one-way and round-trip configurations.
          properties:
            customer:
              type: object
              description: Pricing configurations for customers.
              properties:
                oneway:
                  type: object
                  description: Pricing details for one-way trips.
                  properties:
                    base_breakup:
                      type: array
                      description: Base pricing breakup for one-way trips based on distance.
                      items:
                        type: object
                        properties:
                          minDist:
                            type: string
                            description: Minimum distance threshold for the pricing tier.
                          HikeType:
                            type: string
                            description: Type of hike applied (Flat hike or Per km hike).
                          Amount:
                            type: string
                            description: Amount charged for this pricing tier.
                    additional_charges:
                      type: object
                      description: Additional charges for one-way trips, such as night and overtime charges.
                      properties:
                        nightChEnabled:
                          type: string
                          description: Whether night charges are enabled (1/0).
                        nightCharge:
                          type: string
                          description: Amount charged for night trips.
                        chargeTypeNight:
                          type: string
                          description: Type of night charge (e.g., Flat or Percentage).
                        nightChargeStartTime:
                          type: string
                          description: Start time for night charges in HH:mm:ss format.
                        nightChargeEndTime:
                          type: string
                          description: End time for night charges in HH:mm:ss format.
                        defaultDuration:
                          type: string
                          description: Default trip duration included in the pricing.
                        chargeTypeOvertime:
                          type: string
                          description: Type of overtime charge (e.g., per hour or per minute).
                        overTimeCharge:
                          type: string
                          description: Amount charged for overtime.
                roundtrip:
                  type: object
                  description: Pricing details for round trips.
                  properties:
                    base_breakup:
                      type: object
                      description: Base pricing breakup for round trips.
                      properties:
                        minHour:
                          type: string
                          description: Minimum hours threshold for the pricing tier.
                        minCharge:
                          type: string
                          description: Minimum charge for the trip.
                        hourlyCharge:
                          type: string
                          description: Hourly charge for the trip.
                        overtimeCharge:
                          type: string
                          description: Amount charged for overtime.
                        overtimeType:
                          type: string
                          description: Type of overtime charge (e.g., per min or per hour).
                        outstationChargeTypeEnabled:
                          type: string
                          description: Whether outstation charges are enabled (1/0).
                        outstationChargeType:
                          type: string
                          description: Type of outstation charge (e.g., Flat or Percentage).
                        outstationRate:
                          type: string
                          description: Rate of outstation charge.
                    additional_charges:
                      type: object
                      description: Additional charges for round trips, such as night charges.
                      properties:
                        nightChargeStartTime:
                          type: string
                          description: Start time for night charges in HH:mm:ss format.
                        nightChargeEndTime:
                          type: string
                          description: End time for night charges in HH:mm:ss format.
                        nightChEnabled:
                          type: string
                          description: Whether night charges are enabled (1/0).
                        nightCharge:
                          type: string
                          description: Amount charged for night trips.
                        chargeTypeNight:
                          type: string
                          description: Type of night charge (e.g., Flat or Percentage).
        driver_pricing_data:
            type: object
            description: Pricing details for drivers, including one-way and round-trip configurations.
            properties:
              driver:
                type: object
                description: Pricing configurations for drivers.
                properties:
                  oneway:
                    type: object
                    description: Pricing details for one-way trips.
                    properties:
                      base_breakup:
                        type: array
                        description: Base pricing breakup for one-way trips based on distance.
                        items:
                          type: object
                          properties:
                            minDist:
                              type: string
                              description: Minimum distance threshold for the pricing tier.
                            HikeType:
                              type: string
                              description: Type of hike applied (Flat hike or Per km hike).
                            Amount:
                              type: string
                              description: Amount charged for this pricing tier.
                      additional_charges:
                        type: object
                        description: Additional charges for one-way trips, such as night and overtime charges.
                        properties:
                          nightChargeStartTime:
                            type: string
                            description: Start time for night charges in HH:mm:ss format.
                          nightChargeEndTime:
                            type: string
                            description: End time for night charges in HH:mm:ss format.
                          nightChEnabled:
                            type: string
                            description: Whether night charges are enabled (1/0).
                          nightCharge:
                            type: string
                            description: Amount charged for night trips.
                          chargeTypeNight:
                            type: string
                            description: Type of night charge (e.g., Flat or Percentage).
                          defaultDuration:
                            type: string
                            description: Default trip duration included in the pricing.
                          chargeTypeOvertime:
                            type: string
                            description: Type of overtime charge (e.g., per hour or per minute).
                          overTimeCharge:
                            type: string
                            description: Amount charged for overtime.
                  roundtrip:
                    type: object
                    description: Pricing details for round trips.
                    properties:
                      base_breakup:
                        type: object
                        description: Base pricing breakup for round trips.
                        properties:
                          minHour:
                            type: string
                            description: Minimum hours threshold for the pricing tier.
                          minCharge:
                            type: string
                            description: Minimum charge for the trip.
                          hourlyCharge:
                            type: string
                            description: Hourly charge for the trip.
                          overtimeCharge:
                            type: string
                            description: Amount charged for overtime.
                          overtimeType:
                            type: string
                            description: Type of overtime charge (e.g., per min or per hour).
                          outstationChargeTypeEnabled:
                            type: string
                            description: Whether outstation charges are enabled (1/0).
                          outstationChargeType:
                            type: string
                            description: Type of outstation charge (e.g., Flat or Percentage).
                          outstationRate:
                            type: string
                            description: Rate of outstation charge.
                      additional_charges:
                        type: object
                        description: Additional charges for round trips, such as night charges.
                        properties:
                          nightChargeStartTime:
                            type: string
                            description: Start time for night charges in HH:mm:ss format.
                          nightChargeEndTime:
                            type: string
                            description: End time for night charges in HH:mm:ss format.
                          nightChEnabled:
                            type: string
                            description: Whether night charges are enabled (1/0).
                          nightCharge:
                            type: string
                            description: Amount charged for night trips.
                          chargeTypeNight:
                            type: string
                            description: Type of night charge (e.g., Flat or Percentage).
        pricing_cancellation:
          type: object
          description: Cancellation charges for trips.
          properties:
            cancelCharges:
              type: array
              description: Dynamic cancellation charges.
              items:
                type: object
                properties:
                  hourRange:
                    type: string
                    description: Time range for cancellation charges.
                  customerCancelCharge:
                    type: string
                    description: Cancellation charge for customers.
                  driverCancelCharge:
                    type: string
                    description: Cancellation charge for drivers.
                  bothCancelCharge:
                    type: object
                    description: Cancellation charge for both customer and driver.
                    properties:
                      customer:
                        type: string
                        description: Cancellation charge for customer.
                      driver:
                        type: string
                        description: Cancellation charge for driver.
            cancelChargeStatic:
              type: object
              description: Static cancellation charges.
              properties:
                staticCustomerCharge:
                  type: string
                  description: Static cancellation charge for customers.
                staticDriverCharge:
                  type: string
                  description: Static cancellation charge for drivers.
                bothStaticCancelCharge:
                  type: object
                  description: Static cancellation charges for both customer and driver.
                  properties:
                    customer:
                      type: string
                      description: Static charge for customer.
                    driver:
                      type: string
                      description: Static charge for driver.
responses:
        200:
          description: Affiliate updated successfully.
          schema:
            type: object
            properties:
              success:
                type: integer
                description: Success flag (1 for success).
                example: 1
              message:
                type: string
                description: Success message.
                example: "Affiliate updated successfully"
        404:
          description: Affiliate not found.
          schema:
            type: object
            properties:
              success:
                type: integer
                description: Failure flag (-1 for error).
                example: -1
              message:
                type: string
                description: Error message.
                example: "Affiliate not found"
        500:
          description: Internal server error or database failure.
          schema:
            type: object
            properties:
              success:
                type: integer
                description: Failure flag (-1 for error).
                example: -1
              message:
                type: string
                description: Error message.
                example: "Specfic Error Message"
tags:
  - Affiliate_Login
summary: Change Username for Affiliate Representative
description: >
  This endpoint allows an affiliate representative to change their username.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the affiliate representative.
  - name: new_username
    in: formData
    type: string
    required: true
    description: The new username to be set.
responses:
  200:
    description: Username updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        msg:
          type: string
          description: Success message.
          example: "Username updated successfully"
  400:
    description: Missing required fields or username already taken.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Username already taken"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        msg:
          type: string
          description: Error message.
          example: "Affiliate representative not found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Database error: <error details>"

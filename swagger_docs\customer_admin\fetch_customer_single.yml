tags:
  - Customer_admin
summary: Fetch Single Customer Row
description: >
  This endpoint retrieves detailed information for a single customer based on their user ID. 
  It returns the customer's personal details, registration information, associated city, and trip data.
  Only accessible by users with appropriate admin privileges.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The unique user ID of the customer whose details are to be fetched.
    example: "101"
  - name: region
    in: formData
    required: false
    type: string
    description: (Optional) Comma-separated list of region IDs to filter customers by region. Use '-1' for no filtering.
    example: "1,2,3"
responses:
  200:
    description: Successfully retrieved the customer details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: array
          description: Array containing the customer details
          items:
            type: object
            properties:
              customer_id:
                type: integer
                description: The unique ID of the customer
                example: 101
              mobile:
                type: string
                description: The mobile number of the customer
                example: "9876543210"
              name:
                type: string
                description: The full name of the customer
                example: "<PERSON>"
              registration_date:
                type: string
                format: date-time
                description: The registration date of the customer in IST format
                example: "2024-01-01 10:30:00"
              region:
                type: integer
                description: The region ID where the customer is located
                example: 1
              remark:
                type: string
                description: Any remarks associated with the customer
                example: "Frequent customer"
              city:
                type: string
                description: The city the customer is registered in
                example: "New York"
              city_lat:
                type: number
                format: float
                description: The latitude of the city
                example: 40.7128
              city_lng:
                type: number
                format: float
                description: The longitude of the city
                example: -74.0060
              city_addr:
                type: string
                description: The full address of the city
                example: "123 5th Ave, New York, NY"
              source:
                type: string
                description: The source of the customer registration
                example: "Web"
              completed:
                type: integer
                description: The number of completed trips by the customer
                example: 5
              upcoming:
                type: integer
                description: The number of upcoming trips for the customer
                example: 2
  400:
    description: Invalid or missing user ID
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        error:
          type: string
          description: Error message indicating the issue
          example: "User ID is required"
  404:
    description: User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for user not found)
          example: -2
        error:
          type: string
          description: Error message indicating user was not found
          example: "User not found"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        error:
          type: string
          description: Error message indicating a server-side issue
          example: "Internal server error"

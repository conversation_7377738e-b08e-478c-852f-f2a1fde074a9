tags:
  - Payments
summary: Verify Paytm Transaction
description: >
  This endpoint allows a user to verify their Paytm transaction.
security:
  - Bearer: []
  - X-CSRF-Token: []
parameters:
  - name: CHECKSUMHASH
    in: formData
    type: string
    required: true
    description: Checksum hash from Paytm
  - name: ORDERID
    in: formData
    type: string
    required: true
    description: Paytm order ID (which is also the transaction ID)
responses:
  200_a:
    description: Transaction completed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        captured:
          type: integer
          description: Amount captured from the transaction
          example: 10000
        balance:
          type: number
          format: float
          description: Updated user balance after transaction
          example: 500.0
        message:
          type: string
          description: Success message
          example: "Transaction completed successfully"
    examples:
      application/json:
        success: 1
        captured: 10000
        balance: 500.0
        message: "Transaction completed successfully"
  200_b:
    description: Transaction amount mismatch
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for amount mismatch)
          example: -4
        message:
          type: string
          description: Error message
          example: "Transaction amount mismatch"
    examples:
      application/json:
        success: -4
        message: "Transaction amount mismatch"
  200_c:
    description: Checksum validation failed
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-5 for checksum validation failure)
          example: -5
        message:
          type: string
          description: Error message
          example: "Checksum validation failed"
    examples:
      application/json:
        success: -5
        message: "Checksum validation failed"
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete form details)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"
  401_a:
    description: Failed to get user identity
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failed identity retrieval)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to get user identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get user identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  404:
    description: Transaction not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for transaction not found)
          example: -3
        message:
          type: string
          description: Error message
          example: "Transaction not found"
    examples:
      application/json:
        success: -3
        message: "Transaction not found"
  500:
    description: DB Error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for DB error)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

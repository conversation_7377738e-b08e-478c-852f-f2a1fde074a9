from gevent import monkey
monkey.patch_all()
import pytest
import atexit
import json
from flask import Flask
from urllib.parse import quote
from __init__ import create_app
from db_config import db,mongo_client
from redis_config import get_redis_client
from config import TestConfig, ProdConfig
from models import Users,Drivers,UserToken,DriverVerify,Bookings,Trip,DriverDetails,DriverSearch,BookingAlloc, DriverInfo,TripLog,DriverBank,BookingCancelled,DeletedUser,ReferralUse,MobileChange
from models import BookDest, ReferralUse, UserTrans, DriverTrans, PaymentDataPT, PaymentDataRP, UserFCM, EstimateRemarks,BookPricing,CustomerDetailsLog,UserRegistrationDetails,UserAnalyticsToken,DriverAnalyticsToken,DriverFCM
from models import AdminAccess, AdminUserLog, Coupons, CouponCityMap, DriverGLIDDocDetails, DriverGLBankDetails, DriverGLDrivLicDetails,TripEndPic,TripStartPic,BookPending,DriverStrike,StrikeReason
from affiliate_b2b.affiliate_models import Affiliate,AffiliateCollections, AffiliateRep,AffiliateDriverSearch,AffiliateRepLogs, AffiliateCustomLogs, AffiliateAddress, AffiliateWalletLogs, AffiliatePricingLogs, AffiliateSpoc, DraftAffiliate,AffBookingLogs, AddressSpoc
import random
import os
from sqlalchemy import exc, create_engine,text
from unittest.mock import patch, MagicMock
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token
from datetime import datetime, timedelta, date, time
from faker import Faker
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy.orm import scoped_session, sessionmaker,close_all_sessions
from affiliate_b2b.affiliate_models import init_aff_loc
import pytz 
from flask_jwt_extended.utils import decode_token
from booking_params import BookingParams
import string

fake = Faker()

def convert_time_to_utc(time_str):
    # Parse the time string
    time = datetime.strptime(time_str, "%H:%M:%S").time()

    # Combine it with today's date
    current_date = datetime.now().date()
    full_datetime = datetime.combine(current_date, time)

    # Local timezone (Kolkata)
    local_tz = pytz.timezone('Asia/Kolkata')

    # Localize the combined datetime
    localized_datetime = local_tz.localize(full_datetime)

    # Convert to UTC
    utc_datetime = localized_datetime.astimezone(pytz.utc)

    # Format the UTC time as a string
    utc_time_str = utc_datetime.strftime("%H:%M:%S")

    return utc_time_str

# # Set Redis connection details
# redis_host = "127.0.0.1"  # Explicitly using localhost
# redis_port = "6379"     # Default Redis port

# redis_url = f'redis://{redis_host}:{redis_port}/0'
# message_queue = redis_url  # Default to Redis
# redis_available = False  # Flag to track Redis status
# try:
#     redis_client_test = redis.Redis(host=redis_host,port=redis_port,db=0)
#     redis_client_test.ping()
#     redis_available=True
#     message_queue = redis_url
#     print("Using Redis as the message queue.",redis_client_test.ping(),flush=True)
# except (redis.ConnectionError, redis.TimeoutError) as e:
#     print(f"Redis connection failed: {e}. Falling back to RabbitMQ.",flush=True)
#     print("Using RabbitMQ as the message queue.",flush=True)

# def execute_with_fallback(command, *args, **kwargs):
#     if not redis_available:
#         return None
#     global redis_client_test
#     try:
#         redis_method = getattr(redis_client_test, command, None)
#         if redis_method is None:
#             print(f"Unsupported Redis command: {command}",flush=True)
#             return None
#         if command == "subscribe":
#             print('subsccribing failed',flush=True)
#             pubsub = redis_client_test.pubsub()
#             pubsub.subscribe(*args)
#             return pubsub
#         if command == "publish":
#             print('publishing failed',flush=True)
#             channel = args[0]
#             data = kwargs.get("data", {})
#             json_data = json.dumps(data)
#             redis_client_test.publish(channel, json_data)
#             return True
#         if command == "unsubscribe":
#             print('unsubsccribing failed',flush=True)
#             pubsub = redis_client_test.pubsub()
#             pubsub.unsubscribe(*args)
#             return True
#         if command == "pipeline":
#             print('pipeline entered',flush=True)
#             pipeline = redis_client_test.pipeline()
#             commands = kwargs.get("commands", [])
#             for cmd in commands:
#                 method_name, cmd_args, cmd_kwargs = cmd
#                 if hasattr(pipeline, method_name):
#                     getattr(pipeline, method_name)(*cmd_args, **cmd_kwargs)
#                 else:
#                     print(f"Unsupported command in pipeline: {method_name}",flush=True)
#             return pipeline.execute()
#         if command == "scan_iter":
#             return list(redis_client_test.scan_iter(*args, **kwargs))
#         result = redis_method(*args, **kwargs)
#         return result
#     except Exception as e:
#         print(f"Redis unavailable: {e}. Queuing {command} with args {args}",flush=True)
#         if command == "scan_iter":
#             return []
#         return None
#     except redis.ResponseError as e:
#         print(f"Redis command error: {command} with args {args}, error: {e}",flush=True)
#         if command == "scan_iter":
#             return []
#         return None
#     except Exception as e:
#         print(f"Unexpected error with command {command}: {e}",flush=True)
#         if command == "scan_iter":
#             return []
#         return None

app = None
@pytest.fixture(scope='session')
def setup_test_db(request):
    global app
    test_db_name = f'driverspytest_{os.urandom(8).hex()}'
    test_mongo_db = f"affiliate_test_{os.urandom(8).hex()}"
    base_uri = TestConfig.SQLALCHEMY_DATABASE_URI
    full_uri = base_uri + test_db_name
    copy_uri = ProdConfig.SQLALCHEMY_DATABASE_URI
    TestConfig.SQLALCHEMY_DATABASE_URI = full_uri
    TestConfig.MAIN_MONGO_DB = test_mongo_db
    app, _ = create_app(TestConfig)
    init_aff_loc(app)
    app.config['main_db'] = ProdConfig.MAIN_DB
    app.config['test_db_name'] = test_db_name
    app.config["MONGO_DB_NAME"] = test_mongo_db
    main_db = app.config['main_db']
    #app.config['main_db'] = test_db_name
    print(base_uri, full_uri, copy_uri)
    # Register all blueprints manually

    engine = create_engine(base_uri)
    with engine.connect() as conn:
        # Create the new database
        print(f"Creating test database: {test_db_name}")
        conn.execute(text(f'CREATE DATABASE {test_db_name};'))
        # Check if the database is created successfully
        result = conn.execute(text("SHOW DATABASES;"))
        databases = [row[0] for row in result.fetchall()]
        if test_db_name not in databases:
            raise RuntimeError(f"Database {test_db_name} was not created successfully.")
        # Copy tables from main db to the new test database
        copy_tables_query = text(f"""
            SELECT CONCAT('CREATE TABLE {test_db_name}.', table_name,
                        ' LIKE {main_db}.', table_name, ';')
            FROM information_schema.tables
            WHERE table_schema = '{main_db}' AND table_type = 'BASE TABLE';
        """)

        # Fetch the queries to create tables
        create_table_queries = conn.execute(copy_tables_query).fetchall()

        # Execute each create table query
        for (create_query,) in create_table_queries:
            conn.execute(text(create_query))
            
            


    # (optional) if you have an existing “prod” mongo you want to clone indexes from:
    source_db = mongo_client[ProdConfig.MAIN_MONGO_DB]
    target_db = mongo_client[test_mongo_db]
    print("target_db",target_db)
    print("source collections",source_db.list_collection_names())
    existing = set(target_db.list_collection_names())
    print(existing)
    for coll in source_db.list_collection_names():
        if coll not in existing:
            target_db.create_collection(coll)

        for idx in source_db[coll].list_indexes():
            # pull the key‐pattern SON out
            key_son = idx["key"]
            keys = list(key_son.items())

            # reconstruct options
            opts = {}
            if idx.get("unique"):
                opts["unique"] = True
            if idx.get("sparse"):
                opts["sparse"] = True
            if "expireAfterSeconds" in idx:
                opts["expireAfterSeconds"] = idx["expireAfterSeconds"]
            if "name" in idx:
                opts["name"] = idx["name"]

            # finally clone the index
            target_db[coll].create_index(keys, **opts)
    def cleanup():
       
        print(f"Cleaning up test databases: {test_db_name} (SQL) and {test_mongo_db} (Mongo)")
        try:
            mongo_client.drop_database(test_mongo_db)
            app.redis_client.flushdb()
            with engine.connect() as conn:
                conn.execute(text(f'DROP DATABASE IF EXISTS {test_db_name};'))
        except Exception as e:
            print("Error during cleanup:", e)
            
    with app.app_context():
        create_mock_pricing_data()
        yield app
    request.addfinalizer(cleanup)
    atexit.register(cleanup)

@pytest.fixture()
def client(setup_test_db):  # Ensure setup_test_db is run before client
    """Provide a test client for the application."""
    with app.test_client() as client:
        yield client  # This provides the test client for your tests

@pytest.fixture()
def mock_upload_pic():
    with patch('_utils.upload_pic') as mock:
        yield mock

@pytest.fixture(autouse=True)
def check_database_connection():
    with app.app_context():
        current_db = db.session.execute(text('select database()')).scalar()
        test_db_name = app.config['test_db_name']
        if current_db != test_db_name:
            raise RuntimeError(f"Connected to {current_db}. Expected connection to {test_db_name}. Aborting tests.")

@pytest.fixture(autouse=True)
def cleanup_db():
    """Truncate Users and Drivers tables that have data after each test."""
    # Run the test
    yield
    # Truncate tables if they have data
    with app.app_context():
        # Get the current database name
        # engine = create_engine(SQLALCHEMY_DATABASE_URI)
        engine = create_engine(app.config['SQLALCHEMY_DATABASE_URI'], pool_timeout=30)
        current_db = engine.url.database
        main_db = app.config['main_db']
        if current_db == main_db:
            # print(conn.execute(text(f'DELETE FROM {(Users.__table__).name}')))
            raise RuntimeError("Attempting to run tests on the main database! Aborting cleanup.")
        tables_with_auto_increment = {
            table.name for table in [
                Users.__table__, Drivers.__table__, Bookings.__table__, Trip.__table__,
                ReferralUse.__table__, BookingCancelled.__table__, DeletedUser.__table__,
                MobileChange.__table__, DriverInfo.__table__, UserRegistrationDetails.__table__,
                AdminAccess.__table__, AdminUserLog.__table__, Coupons.__table__,
                DriverGLIDDocDetails.__table__, TripLog.__table__, DriverStrike.__table__,
                StrikeReason.__table__, DriverGLBankDetails.__table__, DriverGLDrivLicDetails.__table__,
                CouponCityMap.__table__, BookingAlloc.__table__, EstimateRemarks.__table__,
                TripStartPic.__table__, TripEndPic.__table__, Affiliate.__table__,
                DraftAffiliate.__table__, AffiliateRep.__table__, AffiliateCustomLogs.__table__,
                AddressSpoc.__table__, AffiliateSpoc.__table__, AffiliateAddress.__table__,
                AffiliatePricingLogs.__table__, AffiliateRepLogs.__table__, AffBookingLogs.__table__,
                CustomerDetailsLog.__table__, DriverBank.__table__,
            ]
        }
        tables_to_clean = [
            Users.__table__, Drivers.__table__, UserToken.__table__, DriverVerify.__table__,
            Bookings.__table__, Trip.__table__, DriverDetails.__table__, DriverSearch.__table__,
            UserTrans.__table__, ReferralUse.__table__, BookDest.__table__, DriverTrans.__table__,
            BookingCancelled.__table__, DeletedUser.__table__, MobileChange.__table__,
            PaymentDataPT.__table__, PaymentDataRP.__table__, UserFCM.__table__,UserAnalyticsToken.__table__,
            DriverFCM.__table__,DriverAnalyticsToken.__table__, DriverInfo.__table__,
            UserRegistrationDetails.__table__, AdminAccess.__table__, AdminUserLog.__table__,
            Coupons.__table__, DriverGLIDDocDetails.__table__, TripLog.__table__, DriverStrike.__table__,
            StrikeReason.__table__, DriverGLBankDetails.__table__, DriverGLDrivLicDetails.__table__,
            CouponCityMap.__table__, BookingAlloc.__table__, EstimateRemarks.__table__,
            TripStartPic.__table__, TripEndPic.__table__, Affiliate.__table__, DraftAffiliate.__table__,
            AffiliateRep.__table__, AffiliateCustomLogs.__table__, AddressSpoc.__table__,
            AffiliateSpoc.__table__, AffiliateAddress.__table__, AffiliateWalletLogs.__table__,
            AffiliatePricingLogs.__table__, AffiliateRepLogs.__table__, AffiliateDriverSearch.__table__,
            AffBookingLogs.__table__, BookPending.__table__, BookPricing.__table__,
            CustomerDetailsLog.__table__, DriverBank.__table__,
        ]
        if current_db == app.config['test_db_name']:
            with engine.connect() as conn:
                conn.execute(text("SET FOREIGN_KEY_CHECKS=0"))
                conn.execute(text("SET innodb_lock_wait_timeout = 3"))
                for table in tables_to_clean:
                # Check if the table has any rows
                    table_name = table.name
                    conn.execute(text(f'DELETE FROM {table.name}'))
                    conn.commit()
                    close_all_sessions()
                    if table_name in tables_with_auto_increment:
                        try:
                            conn.execute(text(f"ALTER TABLE `{table_name}` AUTO_INCREMENT = 1"))
                        except Exception as e:
                            print(f"❌ Error resetting AUTO_INCREMENT on `{table_name}`: {e}")         
                conn.execute(text("SET FOREIGN_KEY_CHECKS=1"))
                try:
                    conn.commit()
                except Exception as e:
                    print(f"❌ Error committing transaction: {e}")
                    
        mongo_test_db = app.config["MONGO_DB_NAME"]
        db = mongo_client[mongo_test_db]
        # Drop or clean specific collections
        for collection_name in db.list_collection_names():
            db[collection_name].delete_many({})  # Or: db.drop_collection(collection_name)
        data = unique_user_data()
        create_user_and_driver(data)


@pytest.fixture()
def driver_login():
    """Fixture to create an access token and user, scoped to the session."""
    data = {
        'mobile': f'{random.randint(**********, **********)}',
        'email': "<EMAIL>",
        'license': f'LrnnwpEi',
        'pwd': 'password',
        'fname': "John",
        'lname': "Doe"
    }

    # Function to create a new user and access token
    def create_driver():
        try:
            user = Users(
            fname=data['fname'],
            lname=data['lname'],
            mobile=data['mobile'],
            email=data['email'],
            pwd=data['pwd'],
            role=Users.ROLE_DRIVER
            )
            db.session.add(user)
            db.session.commit()  # Commit the user first
            user_id = user.id
            driver = Drivers(user, data['license'], 'doc_path', 'pic_path', perma=True)
            driver.approved=1
            db.session.add(driver)
            db.session.commit()
            driver_id = driver.id
        except Exception as e:
            print(e)
            db.session.rollback()


        expires_access = timedelta(seconds=4)
        identity_with_claims = {
            'id': user.id,
            'roles': user.role,
            'region': user.region,
            'fname': user.fname,
            'lname': user.lname,
        }

        with app.app_context():
            access_token = create_access_token(identity=identity_with_claims, additional_claims=identity_with_claims, expires_delta=expires_access)
        return access_token, user_id, driver_id

    state = {
        'access_token': None,
        'user_id': None,
        'driver_id': None
    }
    state['access_token'], state['user_id'], state['driver_id'] = create_driver()
    return state

def unique_user_data():
    return {
        'mobile': f'{random.randint(**********, **********)}',  # Random Indian mobile number
        'email': fake.email(),
        'license': f'LrnnwpE{random.randint(70000000, 99999999)}',  # Use unique ID for license
        'pwd': 'password',
        'fname': fake.name(),  #
        'lname': fake.name()
    }

def unique_driver_data():
    return {
        'mobile': f'{random.randint(**********, **********)}',  # Random Indian mobile number
        'email': fake.email(),
        'license': f'LrnnwpE{random.randint(70000000, 99999999)}',  # Use unique ID for license
        'pwd': fake.text(),
        'fname': fake.name(),  #
        'lname': 'Doe'
    }

def create_user(data, role=Users.ROLE_USER):
    user = Users(
        fname = data['fname'],
        lname = data['lname'],
        mobile = data['mobile'],
        email = data['email'],
        pwd = data['pwd'],
        role = role
    )

    try:
        db.session.add(user)
        db.session.commit()
        print(f"User created with ID: {user.mobile}")
    except exc.IntegrityError:
        db.session.rollback()
    if role == Users.ROLE_SUPERADMIN:
        admin_access = AdminAccess(user.id)
        try:
            db.session.add(admin_access)
            db.session.commit()
            print(f"User created with ID: {user.mobile}")
        except exc.IntegrityError:
            db.session.rollback()

    return user

def create_affiliate_structure(affiliate):
    # Prepare data for Redis
    redis_data = {
        "master": -1,
        "slaves": [],
        "form_details": {
            "client_name": affiliate.client_name,
            "client_id": affiliate.id,
            "client_display_name":  affiliate.display_name,
            "select_group": ["Global", "Local"],
            "city": "Kolkata",
            "client_logo": "logo_url"
        },
        "customer_pricing_data": {
            "cgst": 9,
            "sgst": 9,
            "gst_numbers": ["22ABCDE1234F2Z5"],
            "oneway": {
                "base_breakup": [
                    {"min_distance": 5, "hike_type": "Flat", "amount": 100},
                    {"min_distance": 10, "hike_type": "Percent", "amount": 15}
                ],
                "additional_charges": {
                    "is_night_enabled": True,
                    "night_charge": 50,
                    "charge_type": "Flat",
                    "start_night_time": "18:00:00",
                    "end_night_time": "06:00:00",
                    "default_duration": 30,
                    "overtime_charge": 20,
                    "overtime_type": "PerHour",
                    "insurance_charge": 10,
                    "insurance_charge_enabled": True
                }
            },
            "roundtrip": {
                "base_breakup": {
                    "minimum_hour": 4,
                    "minimum_charge": 400,
                    "hourly_charge": 100,
                    "overtime_charge": 50,
                    "overtime_type": "Flat"
                },
                "additional_charges": {
                    "is_night_enabled": True,
                    "night_charge": 75,
                    "start_night_time": "20:00:00",
                    "end_night_time": "05:00:00",
                    "charge_type": "Percent",
                    "insurance_charge": 20,
                    "insurance_charge_enabled": False
                }
            },
            "outstationtrip": {
                "base_breakup": {
                    "minimum_hour": 8,
                    "minimum_charge": 800,
                    "hourly_charge": 120,
                    "overtime_charge": 60,
                    "overtime_type": "Percent",
                    "outstation_charge_type": "Daily",
                    "outstation_charge": 1500
                },
                "additional_charges": {
                    "is_night_enabled": False,
                    "night_charge": 0,
                    "start_night_time": "21:00:00",
                    "end_night_time": "05:00:00",
                    "charge_type": "Flat",
                    "insurance_charge": 30,
                    "insurance_charge_enabled": True
                }
            }
        },
        "driver_pricing_data": {
            "oneway": {
                "base_breakup": [
                    {"min_distance": 5, "hike_type": "Flat", "amount": 80},
                    {"min_distance": 10, "hike_type": "Percent", "amount": 10}
                ],
                "additional_charges": {
                    "is_night_enabled": True,
                    "night_charge": 40,
                    "start_night_time": "19:00:00",
                    "end_night_time": "06:00:00",
                    "charge_type": "Flat",
                    "overtime_charge": 15,
                    "overtime_type": "Hourly",
                    "default_duration": 45
                }
            },
            "roundtrip": {
                "base_breakup": {
                    "minimum_hour": 5,
                    "minimum_charge": 350,
                    "hourly_charge": 90,
                    "overtime_charge": 40,
                    "overtime_type": "Flat"
                },
                "additional_charges": {
                    "is_night_enabled": False,
                    "night_charge": 0,
                    "start_night_time": "22:00:00",
                    "end_night_time": "06:00:00",
                    "charge_type": "Flat"
                }
            },
            "outstationtrip": {
                "base_breakup": {
                    "minimum_hour": 10,
                    "minimum_charge": 900,
                    "hourly_charge": 110,
                    "overtime_charge": 55,
                    "overtime_type": "Percent"
                },
                "additional_charges": {
                    "is_night_enabled": True,
                    "night_charge": 60,
                    "start_night_time": "20:00:00",
                    "end_night_time": "04:00:00",
                    "charge_type": "Flat"
                }
            }
        },
        "cancel_charges": [
            {
                "hour_range": "Max",
                "customer_cancel": 50,
                "driver_cancel": 30,
                "both_cancel": {
                    "customer": 40,
                    "driver": 20
                }
            },
            {
                "hour_range": "12",
                "customer_cancel": 70,
                "driver_cancel": 60,
                "both_cancel": {
                    "customer": 80,
                    "driver": 50
                }
            }
        ],
        "cancel_charge_static": {
            "customer_cancel": 25,
            "driver_cancel": 15,
            "both_cancel": {
                "customer": 20,
                "driver": 10
            }
        }
    }
    pricing_cancellation_data = {
        "cancel_charges": [
            {
                "hour_range": item.get("hour_range"),
                "customer_cancel": item.get("customer_cancel"),
                "driver_cancel": item.get("driver_cancel"),
                "both_cancel": {
                    "customer": item.get("both_cancel", {}).get("customer"),
                    "driver": item.get("both_cancel", {}).get("driver")
                }
            }
            for item in redis_data.get("cancel_charges", [])
        ],
        "cancel_charge_static": {
            "customer_cancel": redis_data.get("cancel_charge_static", {}).get("customer_cancel"),
            "driver_cancel": redis_data.get("cancel_charge_static", {}).get("driver_cancel"),
            "both_cancel": {
                "customer": redis_data.get("cancel_charge_static", {}).get("both_cancel", {}).get("customer"),
                "driver": redis_data.get("cancel_charge_static", {}).get("both_cancel", {}).get("driver")
            }
        }
    }
    customer_pricing_data = redis_data.get("customer_pricing_data", {})
    driver_pricing_data = redis_data.get("driver_pricing_data", {})

    # Write to Redis
    redis_key = f'affiliate_{affiliate.client_name}'
    redis_client = get_redis_client()
    redis_client.set(redis_key, json.dumps(redis_data))
    redis_client.set(f'{redis_key}_pricing_customer', json.dumps(customer_pricing_data))
    redis_client.set(f'{redis_key}_pricing_driver', json.dumps(driver_pricing_data))
    redis_client.set(f'{redis_key}_pricing_cancellation', json.dumps(pricing_cancellation_data))

    print("Master affiliate created and stored in Redis successfully.")
    new_affiliate_mongo = {
        "affiliate_id": affiliate.id,  # Use ID from SQL object
        "client_name": redis_data['form_details']['client_name'],
        "trip_type": [
             {
                "trip_type_name": "Customer To Hub",
                "trip_type_category": "One Way",
                "tripIndex": 1,
                "tripType": "One Way",
                "startImages": [
                        {
                            "imageDataType": "Image",
                            "imageDesc": "Upload car left image",
                            "imageTitle": "Car Left",
                            "imageTripStage": "start",
                            "imageTripType": "One Way",
                            "imageType": "Mandatory"
                        }
                    ],
                "stopImages": [ 
                        {
                            "imageDataType": "Image",
                            "imageDesc": "Upload stop car left image",
                            "imageTitle": "Car Left",
                            "imageTripStage": "stop",
                            "imageTripType": "One Way",
                            "imageType": "Mandatory"
                        }
                    ]
            }
        ],
        "total_start_images": 0,
        "total_stop_images": 0,
        "tripTypeLabel": "Trip Type",
        "tripTypePlaceholder": "Select Trip Type",
        "driverAvailVisEnabled": "0",
        "form_field_oneway": {
            "affiliate_id": {
                "type": "text",
                "placeholder": "Enter Appoint Id",
                "label": "Appoint Id",
                "is_enabled": "0"
            },
            "vehicle_model": {
                "type": "text",
                "placeholder": "Enter Vehicle model",
                "label": "Vehicle Model",
                "is_enabled": "0"
            },
            "city": {
                "type": "text",
                "placeholder": "Enter City",
                "label": "City"
            },
            "pickup_address": {
                "type": "text",
                "placeholder": "Enter pickup location",
                "label": "Pickup Address",
            },
            "dropoff_address": {
                "type": "text",
                "placeholder": "Enter drop location",
                "label": "Dropoff Address",
            },
            "source_spoc_name": {
                "type": "text",
                "placeholder": "Enter name of source SPOC",
                "label": "Source SPOC Name",
            },
            "source_spoc_number": {
                "type": "text",
                "placeholder": "Enter source SPOC contact number",
                "label": "Source SPOC Number",
            },
            "destination_spoc_name": {
                "type": "text",
                "placeholder": "Enter name of destination SPOC",
                "label": "Destination SPOC Name",
            },
            "destination_spoc_number": {
                "type": "text",
                "placeholder": "Enter destination SPOC contact number",
                "label": "Destination SPOC Number",
            },
            "add_custom_fields": []
        },    # You can update with actual config if needed
        "form_field_round": {},
        "sgst": customer_pricing_data.get('sgst', 0),
        "cgst": customer_pricing_data.get('cgst', 0),
        "gst_numbers": customer_pricing_data.get('gst_numbers', []),
        "customer_pricing_oneway": customer_pricing_data.get('oneway', {}),
        "customer_pricing_roundtrip": customer_pricing_data.get('roundtrip', {}),
        "customer_pricing_outstationtrip": customer_pricing_data.get('outstationtrip', {}),
        "driver_pricing_oneway": driver_pricing_data.get('oneway', {}),
        "driver_pricing_roundtrip": driver_pricing_data.get('roundtrip', {}),
        "driver_pricing_outstationtrip": driver_pricing_data.get('outstationtrip', {}),
        "pricing_cancellation_data": pricing_cancellation_data
    }

    try:
        # Insert the new affiliate document into the MongoDB collection
        AffiliateCollections.affiliates_details.insert_one(new_affiliate_mongo)
        print("New affiliate added successfully.")
    except Exception as e:
        print(f"MongoDB error: {e}")
    return affiliate,redis_key


def create_master_affiliate():
    try:
        # Create the master affiliate
        master_affiliate = Affiliate("Master_Client", "dummy_name", 0 , -1, 10,None)

        # Add to the database
        db.session.add(master_affiliate)
        db.session.flush()
        master_affiliate.mapped_wallet_affiliate = master_affiliate.id
        db.session.commit()

        return create_affiliate_structure(master_affiliate)

    except Exception as e:
        db.session.rollback()  # Rollback in case of error
        print("An error occurred while creating the master affiliate:", e)
        return None  # Return None or raise an error as needed

VARIABLE_CHARGES = [
    { 'hour_range': "Max", 'cust_charge': "45", 'driver_charge': "120",
      'both_cust_charge': "99", 'both_driver_charge': "120" },
    { 'hour_range': "1",   'cust_charge': "45", 'driver_charge': "100",
      'both_cust_charge': "75", 'both_driver_charge': "100" },
    # … the rest of your 2,6,12,24 entries …
]
STATIC_CHARGES = {
    'cust_charge': "45", 'driver_charge': "99",
    'both_cust_charge': "99", 'both_driver_charge': "99"
}

def create_mock_pricing_data():
    """
    Populate Redis with the same keys your real pricing script would:
      - price_config
      - price_<city>  for each city in ['kolkata','delhi',…]
    """
    client = get_redis_client()

    # 1) top‐level price_config
    trip_types = ["inCity", "outStation"]
    cities = ["kolkata", "delhi", "jaipur", "hyderabad", "bangalore"]
    price_config = { "cities": {
        city: { "trip_type": trip_types, "inCity": {}, "outStation": {} }
        for city in cities
    }}
    client.set("price_config", json.dumps(price_config))

    # 2) per‐city stubs
    def make_incity():
        return {
            'night_details': {
                'start_night_time': convert_time_to_utc("23:30:00"),
                'end_night_time':   convert_time_to_utc("05:30:00"),
                'part_night_time':  convert_time_to_utc("22:30:00"),
                'night_charge': "100","part_night_charge": "50"
            },
            'fare_details': {
                'basic_fares':   { 'base_fare': "150", 'travel_cost': "4" },
                'car_fares':     { 'hatch_man': "0", 'sedan_man': "25"},
                'insurance_fares': {  }
            },
            'cancellation_details': {
                'variable_charges': VARIABLE_CHARGES,
                'static_charges':   STATIC_CHARGES
            }
        }

    def make_outstation():
        return {
            'fare_details': {
                'basic_fares': { 'base_fare': "600", 'booking_percent': "10" },
                'car_fares':   { 'hatch_man': "0", 'sedan_man': "40"}
            },
            'cancellation_details': {
                'variable_charges': VARIABLE_CHARGES,
                'static_charges':   STATIC_CHARGES
            }
        }

    for city in cities:
        key = f"price_{city}"
        client.set(key, json.dumps({
            "trip_types": {
                "inCity":    make_incity(),
                "outStation": make_outstation()
            }
        }))

def create_user_and_driver(data):
    try:
        # Create the user
        driver = db.session.query(Drivers).filter(Drivers.id == 1).first()
        if not driver:
            user = Users(
                fname='John',
                lname='Doe',
                mobile='9876543219',
                email='<EMAIL>',
                pwd='password',
                role=Users.ROLE_DRIVER
            )
            db.session.add(user)
            db.session.commit()  # Commit user to get the ID
            driver = Drivers(user, 'LrnnwpEiu', 'doc_path', 'pic_path', perma=True)
            driver.approved=1
            db.session.add(driver)
            db.session.commit()
        user = Users(
            fname=data['fname'],
            lname=data['lname'],
            mobile=data['mobile'],
            email=data['email'],
            pwd=data['pwd'],
            role=Users.ROLE_DRIVER
        )
        db.session.add(user)
        db.session.commit()  # Commit user to get the ID

        # Create the driver associated with the user
        driver = Drivers(user, data['license'], 'doc_path', 'pic_path', perma=True)
        db.session.add(driver)
        db.session.commit()  # Commit driver
        driver_info = DriverInfo(driver.id, "AB123456", date(2030, 12, 31), date(1990, 1, 1), "Delhi",
                "test", "John Doe", "**********", "Brother", 28.7041,  77.1025,  "id_front.jpg",
                "id_back.jpg", "lic_front.jpg", "lic_back.jpg", "profile.jpg",)

        db.session.add(driver_info)
        db.session.commit()

        return user, driver

    except exc.IntegrityError as e:
        # Log the error for debugging purposes
        db.session.rollback()
        print(f"IntegrityError: {e}")
        return None, None

def driver_bookings(user_id,driver_id):
    try:
        booking = Bookings(
        user=user_id,
        skey=fake.name(),
        driver=driver_id,
        lat=0.0,
        long=0.0,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.utcnow() + timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
        booking.valid=1
        booking.code='GOCODE'
        db.session.add(booking)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
    return booking.id



def driver_bookings_b2b(aff_id,driver_id):
    try:
        if aff_id==None:
            master_affiliate = Affiliate("Master_Client_1", "dummy_name", 0 , -1, 10,None)
            db.session.add(master_affiliate)
            db.session.flush()
            master_affiliate.mapped_wallet_affiliate = master_affiliate.id
            aff_id=master_affiliate.id
        db.session.commit()
        booking = Bookings(
        user=None,
        skey=fake.name(),
        driver=driver_id,
        lat=0.0,
        long=0.0,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.utcnow() + timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        type=BookingParams.TYPE_B2B
        )
        booking.valid=1
        db.session.add(booking)
        db.session.flush()
        affbooklogs = AffBookingLogs(aff_id=aff_id, book_id=booking.id)
        db.session.add(affbooklogs)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
    return booking.id,aff_id

def driver_verify(driver_id):
    try:
        driver_verify = DriverVerify(driver_id)
        db.session.add(driver_verify)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()

def driver_details(driver_id):
    try:
        driver_details = DriverDetails(driver_id, owed=100)
        driver_details.withdrawable=50
        driver_details.wallet=200
        driver_details.earning=500
        driver_details.ride_count=1
        driver_details.b2b_ride_count=1
        driver_details.hour_count=2
        db.session.add(driver_details)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
    return driver_details.driver_id

def driver_trip(booking_id,state):
    try:
        trip= Trip(booking_id,datetime.utcnow() + timedelta(hours=2),1,1,state)
        trip.price=500
        trip.endtime=datetime.utcnow() + timedelta(hours=4)
        db.session.add(trip)
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
    return trip.id

@pytest.fixture
def customer_login(client):
    """
    Factory fixture for logging in as any user.
    If you call _login() with no args, it will:
      1. generate & create a fresh user
      2. log in as that user
      3. return (auth_headers, user)
    Or you can pass (mobile, pwd) to log in an existing user.
    """
    def _login(mobile=None, pwd=None):
        # 1) create a new user if no creds supplied
        if mobile is None and pwd is None:
            data = unique_user_data()
            user = create_user(data)
            mobile, pwd = data["mobile"], data["pwd"]
        else:
            user = Users.query.filter_by(mobile=mobile).one()
        user_id = user.id
        # 2) hit the login endpoint
        resp = client.post(
            "/token/login",
            data={"mobile": mobile, "pwd": pwd},
            headers={"User-Agent": "test-agent"},
        )
        assert resp.status_code == 200

        # 3) clear any old cookies and set the new ones
        client.delete_cookie("access_token_cookie")
        client.delete_cookie("csrf_access_token")
        cookies = resp.headers.getlist("Set-Cookie")
        access_token = cookies[0].split(";", 1)[0].split("=", 1)[1]
        csrf_token   = cookies[1].split(";", 1)[0].split("=", 1)[1]
        client.set_cookie("access_token_cookie", access_token)
        client.set_cookie("csrf_access_token", csrf_token)

        # 4) build the auth headers and return
        auth_headers = {
            "Authorization": f"Bearer {access_token}",
            "X-CSRF-TOKEN": csrf_token,
        }
        return auth_headers, user_id

    return _login

@pytest.fixture()
def book_search(client, customer_login):

    auth_headers, data = customer_login()

    search_data = {
        'reflat': '22.571256',
        'reflong': '88.428877',
        'car_type': 1,
        'dur': '02:00:00',
        'time': (datetime.utcnow() + timedelta(hours=1)).strftime("%d/%m/%Y %H:%M:%S"),
        # 'dest_lat': '22.5135084',
        # 'dest_long': '88.402884',
        'region': 0,
        'type': 1,
        'insurance': 1,
        'ninsurance': 1,
        'source': 'test_source'
    }
    response = client.post('/api/search', data=search_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()
    print(res_data)
    return res_data['id'], auth_headers

@pytest.fixture()
def admin_login(client):
    data = unique_user_data()
    user = create_user(data, Users.ROLE_SUPERADMIN)
    user_id = user.id
    # Log in with the client and get the tokens
    response = client.post(
        '/token/admin/login',
        data={'mobile': data['mobile'], 'pwd': data['pwd']},
        headers={'User-Agent': 'test-agent'}
    )
    assert response.status_code == 200
    cookies = response.headers.getlist('Set-Cookie')
    auth_token = cookies[0].split(';')[0].split('=')[1]
    csrf_token = cookies[1].split(';')[0].split('=')[1]

    client.set_cookie(key='access_token_cookie', value=auth_token)
    client.set_cookie(key='csrf_access_token', value=csrf_token)

    # Return the Authorization headers using the retrieved tokens
    auth_headers = {
        'Authorization': f'Bearer {auth_token}',
        'X-CSRF-TOKEN': csrf_token
    }

    return auth_headers, user_id

@pytest.fixture()
def affiliate_rep_setup():
    affiliate_name = 'affiliate_' + ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
    affiliate = Affiliate(affiliate_name, "Display Dummy", 0, -1, 10, None, notification=None, enabled=True, wallet_threshold=1000)
    db.session.add(affiliate)
    db.session.flush()

    affiliate.wallet = 1000.0
    affiliate.mapped_wallet_affiliate = affiliate.id
    affiliate.slave = {"slaves": [20, 21, 999]}

    mobile = f'{random.randint(**********, **********)}'
    pwd = "temp" + mobile
    username = "sroy_" + ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))

    rep = AffiliateRep(
        affiliate.id, "Sanjay Roy", username, mobile, pwd,
        10, 14, 106, '0', '1,4,5,6,7,11,12', enabled=True
    )

    db.session.add(rep)
    db.session.flush()
    rep.is_first_login = False
    try:
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()

    return {"username": username, "pwd": pwd, "rep": rep.id}

@pytest.fixture
def affiliate_rep_login(client, affiliate_rep_setup):
    form_data = {
        "username": affiliate_rep_setup["username"],
        "pwd": affiliate_rep_setup["pwd"]
    }
    response = client.post('/api/affiliate/login', data=form_data)
    assert response.status_code == 200
    cookies = response.headers.getlist('Set-Cookie')
    auth_token = cookies[2].split(';')[0].split('=')[1]
    csrf_token = cookies[3].split(';')[0].split('=')[1]

    client.set_cookie(key='access_token_cookie', value=auth_token)
    client.set_cookie(key='csrf_access_token', value=csrf_token)

    return {
        'Authorization': f'Bearer {auth_token}',
        'X-CSRF-TOKEN': csrf_token
    }, affiliate_rep_setup["rep"]


@pytest.fixture
def auth_token_factory(client):
    def _create_token(user_id):
        # Create access token and CSRF token (automatically done by Flask-JWT-Extended)
        access_token = create_access_token(identity=user_id, expires_delta=timedelta(hours=1))

        # Flask-JWT-Extended expects the CSRF token to be extracted from the JWT
        decoded_token = decode_token(access_token)
        csrf_token = decoded_token['csrf']

        # Set cookies on the test client
        client.set_cookie('access_token_cookie', access_token)
        client.set_cookie('csrf_access_token', csrf_token)

        # Return CSRF header
        return {
            'X-CSRF-TOKEN': csrf_token
        }

    return _create_token

def create_new_driver():
    """Fixture to create and return a new driver, their user, and JWT access token."""
    mobile = f'{random.randint(**********, **********)}'
    email = f'test{random.randint(1000,9999)}@example.com'
    license_id = f'Lr{random.randint(1000, 9999)}'

    # Create user
    user = Users(
        fname='John',
        lname='Doe',
        mobile=mobile,
        email=email,
        pwd='password',
        role=Users.ROLE_DRIVER
    )
    db.session.add(user)
    db.session.commit()

    # Create driver
    driver = Drivers(user, license_id, 'doc_path', 'pic_path', perma=True)
    driver.approved = 1
    db.session.add(driver)
    db.session.commit()
    driver_info = DriverInfo(driver.id, "AB123456", date(2030, 12, 31), date(1990, 1, 1), "Delhi",
                "test", "John Doe", "**********", "Brother", 28.7041,  77.1025,  "id_front.jpg",
                "id_back.jpg", "lic_front.jpg", "lic_back.jpg", "profile.jpg",)

    db.session.add(driver_info)
    db.session.commit()

    return driver, driver_info


@pytest.fixture
def create_new_affiliate_rep():
    
    affiliate = Affiliate("DummyAffiliate", "Dummy Aff", 0, -1, 10,None, notification=None, enabled=True, wallet_threshold=1000)

    db.session.add(affiliate)
    db.session.flush()
    affiliate.mapped_wallet_affiliate = affiliate.id
    db.session.commit()
    mobile = f'{random.randint(**********, **********)}'
    pwd = "temp" + mobile
    username = "sroy_" + ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))

    aff_rep = AffiliateRep(affiliate.id, "Sanjay Roy", username, mobile, pwd, 10, 14, 106, '0', '1,4,5,6,7,11,12',
                enabled=True)
    db.session.add(aff_rep)
    db.session.flush()
    aff_rep.is_first_login = False

    try:
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()

    return affiliate, aff_rep

@pytest.fixture
def create_driver_details(driver_login):
    """Fixture to create DriverDetails for a given driver_id."""
    driver = driver_login
    dd = driver_details(driver['driver_id'])
    return driver


def create_b2b_booking(client, headers, affiliate_id, rep_id):
    def _create_booking(search_id='1011'):
        affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
        search = AffiliateDriverSearch(search_id, affiliate_id, rep_id, 3, 22.5726, 88.3639, 22.3072, 73.1812,
                time=time(10, 30), date=date.today(), dur=time(2, 0), timestamp=datetime.now(), type=BookingParams.TYPE_B2B, trip_type=3,
                estimate=250, source='1-2')
        db.session.add(search)
        db.session.commit()
        master_affiliate, redis_key = create_affiliate_structure(affiliate)
        data = {
            'aff_id': str(affiliate_id),
            'search_ids': str(search.id),
            'vehicle_no': 'GJ01XY1234',
            'remark': 'Test booking',
            'trip_name': 'Customer To Hub',
            'trip_type': 3,
            'loc': 'Ruby',
            'dest_lat': '22.3072',
            'dest_long':  '731812',
            'dest_loc': "Howrah",
            'source_spoc_name': 'John Doe',
            'source_spoc_contact': '**********',
            'priority': '0',
            'release': '1'
        }
        response = client.post(
            '/api/affiliate/book',
            data=data,
            headers=headers
        )

        json_data = response.get_json()
        print("Response", json_data)
        assert response.status_code == 200
        assert json_data['success'] == 1
        assert "Booking created successfully" in json_data['msg']
        return json_data

    return _create_booking

def create_b2b_booking_test(client, affiliate_rep_login, create_new_driver):
    def _create_booking(search_id='1011'):
        auth_headers, aff_rep_id = affiliate_rep_login
        aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep_id).first()
        new_driver = create_new_driver
        new_driver.id = 1
        db.session.commit()
        affiliate = db.session.query(Affiliate).filter(Affiliate.id == aff_rep.affiliate_id).first()
        search = AffiliateDriverSearch(search_id, aff_rep.affiliate_id, aff_rep.id, 3, 22.5726, 88.3639, 22.3072, 73.1812,
                time=datetime.now().time(), date=date.today(), dur=time(2, 0), timestamp=datetime.now(), type=BookingParams.TYPE_B2B, trip_type=3,
                estimate=250, source='1-2')
        db.session.add(search)
        db.session.commit()
        master_affiliate, redis_key = create_affiliate_structure(affiliate)
        data = {
            'aff_id': str(aff_rep.affiliate_id),
            'search_ids': str(search.id),
            'vehicle_no': 'GJ01XY1234',
            'remark': 'Test booking',
            'trip_name': 'Customer To Hub',
            'trip_type': 3,
            'loc': 'Ruby',
            'dest_lat': '22.3072',
            'dest_long':  '731812',
            'dest_loc': "Howrah",
            'source_spoc_name': 'John Doe',
            'source_spoc_contact': '**********',
            'priority': '1',
            'release': '1'
        }

        response = client.post(
            '/api/affiliate/book',
            data=data,
            headers=auth_headers
        )

        json_data = response.get_json()
        print("Response", json_data)
        assert response.status_code == 200
        assert json_data['success'] == 1
        assert "Booking created successfully" in json_data['msg']
        return json_data

    return _create_booking

@pytest.fixture()
def runner(app):
    return app.test_cli_runner()  # If you need to run CLI commands in your tests

tags:
  - Estimate_admin
summary: Get User Estimate Details
description: >
  This endpoint retrieves estimate details for a specific user based on their user ID.
  It also allows filtering by regions. The results include upcoming and completed trip details.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The ID of the user for whom the estimate details are being fetched.
    example: "user_12345"
  - name: regions
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of region IDs to filter the results. If not specified, all regions will be included.
      Use '-1' to indicate no filtering.
    example: "1,2,3"
responses:
  200:
    description: Successfully retrieved estimate details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              search_timestamp:
                type: string
                description: The timestamp of the search, formatted in Asia/Kolkata timezone.
                example: "2024-10-15 10:00:00"
              start_date:
                type: string
                description: The start date of the trip.
                example: "2024-10-15"
              start_time:
                type: string
                description: The start time of the trip.
                example: "14:00:00"
              trip_type:
                type: integer
                description: The type of trip (e.g., 1 for one-way, 2 for round-trip).
                example: 1
              car_type:
                type: string
                description: The type of car for the trip.
                example: "Sedan"
              duration:
                type: string
                description: The duration of the trip.
                example: "01:30:00"
              region:
                type: integer
                description: The region ID associated with the trip.
                example: 1
              estimate:
                type: float
                description: The estimated cost of the trip.
                example: 1500.00
              reflat:
                type: float
                description: Latitude of the pickup location.
                example: 28.6139
              reflong:
                type: float
                description: Longitude of the pickup location.
                example: 77.2090
        upcoming_count:
          type: integer
          description: Count of upcoming trips for the user.
          example: 2
        completed_count:
          type: integer
          description: Count of completed trips for the user.
          example: 5
  400:  # Bad request due to missing or invalid user ID
    description: User ID is incomplete or invalid.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        msg:
          type: string
          description: Error message detailing the issue.
          example: "Incomplete user id"
  201:  # No data found for the specified user
    description: No data found for the specified user.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the status of the operation.
          example: -1
        msg:
          type: string
          description: Message indicating that no data was found.
          example: "No data found for the specified user"

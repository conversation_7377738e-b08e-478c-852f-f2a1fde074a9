tags:
  - Affiliate
summary: Upload an OLX image for a driver.
description: This API allows a driver to upload an image for an OLX booking.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the OLX booking.
  - name: index
    in: formData
    type: integer
    required: true
    description: The index number for the image.
  - name: pic
    in: formData
    type: file
    required: true
    description: The image file to be uploaded.
responses:
  200_a:
    description: OLX image uploaded successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "OLX image uploaded successfully"
    examples:
      application/json:
        success: 1
        msg: "OLX image uploaded successfully"
  201_a:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        msg:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        msg: "Incomplete form details"
  401_a:
    description: Unauthorized role, not Driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        msg: "Unauthorized role: not Driver"
  401_b:
    description: User restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        msg: "User restricted"
  500_a:
    description: Server error (DB Error).
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "DB Error"
        reason:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        msg: "DB Error"
        reason: "DB Error"

from main import app
import sys
sys.path.append("/app/")
import csv
import configparser
import re
from models import db

D4M_UTIL_PATH = "/app/util/"


def run_query(query):
    query = re.sub('\s+',' ', query)
    print("Executing query", query)
    records = db.engine.execute(query)
    return records


def export_csv(records, filename, headers=False):
    filename = D4M_UTIL_PATH + 'output/' + filename
    print("Writing to", filename)
    outfile = open(filename, 'w')
    outcsv = csv.writer(outfile)
    data = list()
    if headers:
        data.append(records.keys())
    for record in records:
        data.append(list(record))
    outcsv.writerows(list(data))
    # or maybe use outcsv.writerows(records)
    print("Done writing to", filename)
    outfile.close()
    return filename

def export_csv_main(filename, query, headers=False):
    records = run_query(query)
    return export_csv(records, filename, headers)

if __name__ == '__main__':
    with app.app_context():
        if len(sys.argv) < 2:
            filename = D4M_UTIL_PATH + 'configs/write_csv.ini'
        else:
            filename = sys.argv[1]
        if len(sys.argv) == 4:
            query = sys.argv[3]
        else:
            query = sys.argv[3]
        if len(sys.argv) >= 3:
            headers = bool(sys.argv[2])
        else:
            headers = False
        export_csv_main(filename, query, headers)

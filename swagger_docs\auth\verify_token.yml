tags:
  - Authentication
summary: Verify if the token is valid
description: Verifies the token and checks the user role.
responses:
  200:
    description: Token verified successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Token verified"
    examples:
      application/json:
        success: 1
        message: "Token verified"
  401:
    description: Failed to verify token
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Failed to verify token"
    examples:
      application/json:
        success: 0
        message: "Failed to verify token"

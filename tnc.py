#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  tnc.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

from flask import Blueprint, render_template

site = Blueprint('site', __name__)


@site.route('/terms-and-condition', methods=['GET', 'POST'])
def tnc_page():
    return render_template('tnc.html')

@site.route('/frequently-asked-questions', methods=['GET', 'POST'])
def faq_page():
    return render_template('faq.html')

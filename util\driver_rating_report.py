from main import app
import os
import csv
import sys
from datetime import datetime, timedelta
from sqlalchemy import func, and_, select
from models import Bookings, Trip, Drivers, Users
from db_config import db
from _email import send_mail_v2
from booking_params import Regions
from time import sleep

script_dir = os.path.dirname(os.path.abspath(__file__))

output_dir = os.path.join(script_dir, 'output')
os.makedirs(output_dir, exist_ok=True)

start_date = (datetime.now().replace(day=1) - timedelta(days=1)).replace(day=1).strftime('%Y-%m-%d')
end_date = (datetime.now().replace(day=1) - timedelta(days=1)).strftime('%Y-%m-%d')
# start_date = '2023-02-01' #518 days old date for driverstest db to test in shell
# end_date = '2023-02-28'
cur_month = (datetime.now().replace(day=1) - timedelta(days=1)).strftime('%B %Y')

FROM_ADDR = "<EMAIL>"
to_addr_list = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

REGION_MAIL = {
    Regions.REGN_KOLKATA: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_DELHI: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_HYDERABAD: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_BANGALORE: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"],
}
csv_paths = []

def generate_driver_ratings_report(user_rating,region):
    csv_file_path = os.path.join(output_dir, f'{Regions.REGN_NAME[region] if region!=-127 else "all"}_drivers_ratings_{user_rating}_{cur_month}.csv')

    try:
        valid_status = [0,-1, -2, -3]

        subquery = db.session.query(
            Bookings.driver.label('driver_id'),
            func.count(Trip.id).label('total_trips_completed')
        ).join(
            Trip, Trip.book_id == Bookings.id
        ).filter(
            Trip.status == 0,
            and_(Bookings.enddate >= start_date, Bookings.enddate <= end_date),
            ~Bookings.valid.in_(valid_status)
        ).group_by(
            Bookings.driver
        ).subquery()

        filters = [
            and_(Bookings.enddate >= start_date, Bookings.enddate <= end_date),
            Bookings.user_rating == user_rating,
            ~Bookings.valid.in_(valid_status)
        ]

        if region!=-127:
            filters.append(Bookings.region == region)

        results = db.session.query(
            Bookings.driver.label('driver_id'),
            func.concat(Users.fname, ' ', Users.lname).label('name'),
            Users.mobile.label('mobile'),
            func.coalesce(subquery.c.total_trips_completed, 0).label('total_trips_completed'),
            func.count(Bookings.id).label('ratings_count'),
            func.group_concat(Bookings.code).label('booking_codes')
        ).join(
            Drivers, Bookings.driver == Drivers.id
        ).join(
            Users, Drivers.user == Users.id
        ).join(
            Trip, Bookings.id == Trip.book_id
        ).outerjoin(
            subquery, subquery.c.driver_id == Bookings.driver
        ).filter(
            *filters
        ).filter(
            Trip.status == 0,
        ).group_by(
            Bookings.driver
        ).all()

        print(len(results))

        with open(csv_file_path, mode='w', newline='') as csv_file:
            fieldnames = ['driver_id', 'driver_name', 'driver_mobile', 'total_trips_completed', 'ratings_count', 'booking_codes']
            writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
            writer.writeheader()

            for result in results:
                writer.writerow({
                    'driver_id': result.driver_id,
                    'driver_name': result.name,
                    'driver_mobile': result.mobile,
                    'total_trips_completed': result.total_trips_completed,
                    'ratings_count': result.ratings_count,
                    'booking_codes': result.booking_codes
                })

        csv_paths.append(csv_file_path)

    except Exception as e:
        print(f"Error generating driver ratings report: {str(e)}")

def send_mail(region):
    subjectD = f"Driver rating Report - {Regions.REGN_NAME[region] if region!=-127 else 'All regions'} " + cur_month
    content = "Driver rating Report - for 1, 2, 3, 4, 5 star and not rated drivers " + "<br/>" + \
                f"Report for {Regions.REGN_NAME[region] if region!=-127 else 'all regions'} "+ cur_month + "<br/>" + \
                "From last 30 days of data on drivers' rating<br/>" + \
                "All Drivers IDs are unique<br/>" + \
                "Total Last Month's only Completed Trips" + "<br/><br/>"
    send_to_list = REGION_MAIL.get(region) if region!=-127 else to_addr_list
    send_mail_v2(FROM_ADDR, send_to_list, subjectD, content, csv_paths)

if __name__ == '__main__':
    with app.app_context():
        if len(sys.argv) < 2:
            print('To print region wise use cmd=> python filename.py <booking_region_number>')
            for i in range(6):
                generate_driver_ratings_report(i, -127)
            send_mail(-127)
            print("Usage: python filename.py <booking_region>")
            sys.exit(1)

        regions = [int(arg) for arg in sys.argv[1:]]

        for region in regions:
            print(f'Sending mail of {Regions.REGN_NAME[region]} region....')
            for i in range(6):
                generate_driver_ratings_report(i, region)
            send_mail(region)
            csv_paths = []
            sleep(4)
tags:
  - Driver_admin
summary: Reverify Driver's Voter ID Document
description: >
  This endpoint allows for the re-verification of a driver's ID document, specifically a voter ID. It checks the driver's ID, allows for a forced verification option, and retrieves matching details. It updates the driver's verification status based on the result of the matching process.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose ID document is to be reverified
    example: 101
  - name: force_verify
    in: formData
    required: false
    type: integer
    description: Flag to force the verification process (1 for forced verification, 0 otherwise)
    example: 1
  - name: remarks
    in: formData
    required: false
    type: string
    description: Remarks or comments related to the verification process
    example: "Verified by admin"
responses:
  200:
    description: Successfully reverified the driver's ID document
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the re-verification process (3 for forced verification success, 1 for successful verification, 0 for disputed)
          example: 1
        message:
          type: string
          description: Message describing the outcome of the re-verification
          example: "Voter Details Verified"
        details:
          type: object
          description: Matching details including name match and other relevant information
          properties:
            name_match:
              type: boolean
              description: Indicates whether the account holder's name matches
              example: true
  400:
    description: Bad request (verification failed due to issues with the re-verification process)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for failure to reverify)
          example: -1
        message:
          type: string
          description: Error message
          example: "Voter Details Failed to Reverify"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-2 for database errors, -3 for unexpected errors)
          example: -2
        message:
          type: string
          description: Error message
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Unexpected error occurred"

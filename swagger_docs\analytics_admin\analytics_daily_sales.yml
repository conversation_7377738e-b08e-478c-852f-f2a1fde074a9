tags:
  - Admin Analytics
summary: Admin Analytics Daily Sales.
description: >
  API for fetching daily sales analytics data.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: >
            A comma-separated list of region IDs for filtering sales data.
responses:
    200:
      description: "Successfully retrieved daily sales analytics"
      schema:
        type: object
        properties:
          success:
            type: integer
            description: "Success flag (1 for success)"
            example: 1
          data:
            type: array
            items:
              type: object
              properties:
                day_data:
                  type: array
                  items:
                    type: object
                    properties:
                      day:
                        type: string
                        format: date
                        description: "Day of the sales data (DD-MM-YYYY)"
                        example: "12-10-2024"
                      sales:
                        type: number
                        format: float
                        description: "Sales amount for that day"
                        example: 5000.00
    400:
      description: "Invalid request or missing fields"
      schema:
        type: object
        properties:
          success:
            type: integer
            description: "Failure flag (-3 for error)"
            example: -3
          error:
            type: string
            description: "Error message"
            example: "Missing or invalid parameters"
    500:
      description: "Internal server error or database failure"
      schema:
        type: object
        properties:
          success:
            type: integer
            description: "Failure flag (0 for server error)"
            example: 0
          error:
            type: string
            description: "Error message"
            example: "Internal server error"

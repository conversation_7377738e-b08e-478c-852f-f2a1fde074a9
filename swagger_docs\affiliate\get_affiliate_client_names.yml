tags:
  - Affiliate
summary: Fetch All Affiliate Client Names
description: >
  This endpoint fetches all affiliate client names and their corresponding IDs.
parameters:
  - name: region
    in: query
    required: true
    type: string
    description: comma seperated region.
responses:
  200:
    description: A list of affiliate client names.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (1 for success).
        client_names:
          type: array
          description: A list of client names with their IDs.
          items:
            type: object
            properties:
              id:
                type: integer
                description: The ID of the affiliate client.
              client_name:
                type: string
                description: The name of the affiliate client.
  500:
    description: Failed to fetch client names due to an error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (-1 for failure).
        error:
          type: string
          description: Error description.
        message:
          type: string
          description: Detailed error message.

tags:
  - Booking_admin
summary: Retrieve trip logs for a specified booking
description: >
  This endpoint retrieves the trip logs for a specific booking ID, including details of allocations, cancellations, and trip statuses.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking for which the trip logs are being requested.
responses:
  200:
    description: Successfully retrieved trip logs for the specified booking.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        query_time:
          type: number
          format: float
          description: Time taken to process the query in seconds.
          example: 0.123
        booking_date:
          type: string
          format: date
          description: The date of the booking.
          example: "2024-10-17"
        booking_time:
          type: string
          format: time
          description: The time of the booking.
          example: "14:30:00"
        booking_id:
          type: integer
          description: The ID of the booking.
          example: 12345
        booking_code:
          type: string
          description: The unique code associated with the booking.
          example: "ABC123"
        booking_location:
          type: string
          description: The region/location of the booking.
          example: "New York"
        book_status:
          type: string
          description: The current validity status of the booking.
          example: "Valid"
        trip_status:
          type: string
          description: The current status of the trip.
          example: "Ongoing"
        log_details:
          type: array
          items:
            type: object
            properties:
              entry_type:
                type: string
                description: The type of log entry (e.g., allocation, cancellation, trip log).
                example: "Allocation"
              timestamp:
                type: string
                format: date-time
                description: The timestamp of the log entry.
                example: "2024-10-17T14:30:00Z"
              details:
                type: object
                description: Details related to the log entry.
                additionalProperties: true
  400:
    description: Bad request, missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "booking_id parameter is required"
  404:
    description: No booking found for the provided ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "No Booking Found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"

from main import app
import argparse, sys, os
sys.path.append("/app/")
import pandas as pd
from db_config import db
from datetime import datetime
from collections import defaultdict, Counter
from booking_params import BookingParams, Regions
from models import Users, Drivers, Bookings

TYPE_VALID_MAP = {
    (1, 0): "RT completed",
    (1, -1): "RT cust cancelled",
    (1, -3): "RT D4M cancelled",
    (2, 0): "OS completed",
    (2, -1): "OS cust cancelled",
    (2, -3): "OS D4M cancelled",
    (3, 0): "OW completed",
    (3, -1): "OW cust cancelled",
    (3, -3): "OW D4M cancelled",
    (4, 0): "MiniOS completed",
    (4, -1): "MiniOS cust cancelled",
    (4, -3): "MiniOS D4M cancelled",
    (5, 0): "OS OW completed",
    (5, -1): "OS OW cust cancelled",
    (5, -3): "OS OW D4M cancelled",
    (6, 0): "MiniOS OW completed",
    (6, -1): "MiniOS OW cust cancelled",
    (6, -3): "MiniOS OW D4M cancelled"
}


def _get_all_bookings(startdate, enddate):
    all_bookings = db.session.query(Bookings, Drivers, Users). \
                      filter(Bookings.startdate >= startdate). \
                      filter(Bookings.enddate <= enddate). \
                      filter(Bookings.driver == Drivers.id). \
                      filter(Drivers.user == Users.id). \
                      filter(Bookings.type <= BookingParams.TYPE_C24)

    all_bookings = all_bookings.all()
    print("Found", len(all_bookings), "bookings")
    return all_bookings

def _initialize_driver_data(driver, user):
    driver_data = {
        "name": user.get_name(),
        "region": Regions.to_string(user.region),
        "mobile": user.mobile
    }
    return driver_data

def _process_bookings(all_bookings):
    res = {}
    all_drivers = list(set([(driver, user) for _, driver, user in all_bookings]))
    driver_booking_counts = Counter(booking.driver for booking, _, _ in all_bookings)
    for cur_driver, cur_user in all_drivers:
        driver_id = cur_driver.id
        res[driver_id] = _initialize_driver_data(cur_driver, cur_user)
        res[driver_id]["total bookings"] = driver_booking_counts[driver_id]
        booking_state_type_counts = Counter((
            current_booking.valid, current_booking.type) for current_booking, _, _
            in all_bookings if driver_id == current_booking.driver)
        valid_state_stats = defaultdict(int, {state: sum(count for (s, t), count
            in booking_state_type_counts.items()
            if s == state) for state in set(s for (s, t), count
            in booking_state_type_counts.items())})
        res[driver_id]["completed bookings"] = valid_state_stats[0] + valid_state_stats[1]
        res[driver_id]["cust cancelled"] = valid_state_stats[-1]
        res[driver_id]["d4m cancelled"] = valid_state_stats[-3]
        res[driver_id]["unknown state"] = driver_booking_counts[driver_id] - (
            valid_state_stats[0] + valid_state_stats[1] +
            valid_state_stats[-3] + valid_state_stats[-1]
        )
        res[driver_id]["success rate (%)"] = round(
            res[driver_id]["completed bookings"] /
            res[driver_id]["total bookings"] * 100,
            2)
        res[driver_id]["cust cancel rate (%)"] = round(
            res[driver_id]["cust cancelled"] /
            res[driver_id]["total bookings"] * 100,
            2)
        res[driver_id]["d4m cancel rate (%)"] = round(
            res[driver_id]["d4m cancelled"] /
            res[driver_id]["total bookings"] * 100,
            2)
        for type_valid, key_str in TYPE_VALID_MAP.items():
            res[driver_id][key_str] = booking_state_type_counts[(type_valid[1], type_valid[0])]
    return res


def main():
    # create a parser object
    parser = argparse.ArgumentParser(description='Output driver statistics')

    parser.add_argument('output', nargs='?', help='output filename without extension')
    parser.add_argument('start_date', nargs='?', help='start date (YYYY-MM-DD)')
    parser.add_argument('end_date', nargs='?', help='end date (YYYY-MM-DD)')

    # parse the arguments
    args = parser.parse_args()

    if not args.output:
        output_filename = "driver-cancelled-status.xlsx"
    else:
        output_filename = os.path.join('output', args.output + ".xlsx")
    start_date_str = args.start_date
    end_date_str = args.end_date

    start_date = None
    end_date = None
    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
        except ValueError:
            print('Invalid start date:', start_date_str)
            return
    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
        except ValueError:
            print('Invalid end date:', end_date_str)
            return
    all_bookings = _get_all_bookings(start_date, end_date)
    filtered_bookings = _process_bookings(all_bookings)
    pd_df_bookings = pd.DataFrame(filtered_bookings).T
    print("Writing to", output_filename)
    pd_df_bookings.to_excel(output_filename, index=False)

if __name__ == '__main__':
    with app.app_context():
        main()

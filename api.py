#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  api.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON> Mi<PERSON>


from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from _utils import complete
from login import login, check_exists, refresh, verify, logout
from book_ride import register_search, cancel_user, book_driver
from users import pending_conf, past_cust, ongoing_trip_user

apip = Blueprint('api', __name__)


API_KEYS = ['ab66ac91bcc6448d81d508875aff6f65']


def _valid(form, field):
    if not complete(form, [field]):
        return False
    if form[field] not in API_KEYS:
        return False

'''
Login methods
'''
@apip.route('/d4m/api/token/login', methods=['POST'])
@jwt_required()
def login_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return login()


@apip.route('/d4m/api/token/exists', methods=['POST'])
@jwt_required()
def check_exists_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return check_exists()


@apip.route('/d4m/api/token/refresh', methods=['POST'])
@jwt_required()
def refresh_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return refresh()


@apip.route('/d4m/api/token/remove', methods=['POST'])
@jwt_required()
def logout_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return logout()


@apip.route('/d4m/api/token/verify', methods=['POST'])
@jwt_required()
def verify_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return verify()


'''
Booking methods
'''
@apip.route('/d4m/api/search', methods=['POST'])
@jwt_required()
def register_search_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return register_search()


@apip.route('/d4m/api/decline/user', methods=['POST'])
@jwt_required()
def cancel_user_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return cancel_user()


@apip.route('/d4m/api/book', methods=['POST'])
@jwt_required()
def book_driver_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return book_driver()


'''
Users methods
'''
@apip.route('/d4m/api/user/pending_ride', methods=['POST'])
@jwt_required()
def pending_conf_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return pending_conf()


@apip.route('/d4m/api/userser/past_ride', methods=['POST'])
@apip.route('/d4m/api/user/past_ride', methods=['POST'])
@jwt_required()
def past_cust_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return past_cust()


@apip.route('/d4m/api/user/ongoing', methods=['POST'])
@jwt_required()
def ongoing_trip_user_api():
    if not _valid(request.form, 'api_key'):
        return jsonify({'success': 0}), 400
    return ongoing_trip_user()
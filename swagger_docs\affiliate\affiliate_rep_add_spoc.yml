tags:
  - Affiliate
summary: Add SPOC (Single Point of Contact) for Affiliate Rep
description: >
  Adds SPOC details for an existing affiliate representative. The SPOC data is tied to the rep and affiliate, and the request must include a list of SPOC objects.
parameters:
  - in: body
    name: body
    required: true
    schema:
      type: object
      required:
        - rep_id
        - regions
        - spocDetails
      properties:
        rep_id:
          type: integer
          example: 101
          description: ID of the affiliate representative.
        regions:
          type: string
          description: Comma-separated region codes the rep should have access to.
        spocDetails:
          type: array
          description: List of SPOC objects to be added.
          items:
            type: object
            properties:
              name:
                type: string
                example: <PERSON>
              mobile:
                type: string
                example: '9876543210'
              region:
                type: string
                example: '0'
              global:
                type: boolean
                example: true

responses:
  200:
    description: SPOC data added successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: SPOC data inserted successfully
  400:
    description: Missing or invalid input.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: SPOC details not provided
  401:
    description: Unauthorized request.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Unauthorized
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate representative not found
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -6
        message:
          type: string
          example: Internal Server Error

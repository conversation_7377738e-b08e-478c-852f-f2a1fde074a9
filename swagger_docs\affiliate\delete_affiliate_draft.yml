tags:
  - Affiliate
summary: Delete an affiliate draft
description: >
  This endpoint deletes an affiliate draft from both MySQL (RDBMS) and MongoDB. 
  It ensures the draft exists, and the current user has authorization to delete the draft before proceeding.

parameters:
  - name: body
    in: body
    required: true
    description: JSON payload containing the client name.
    schema:
      type: object
      properties:
          draft_id:
            type: integer
            description: The ID of the draft to delete.
            example: 1
          regions:
            type: string
            description: A comma-separated list of region IDs for filtering.

responses:
  '200':
    description: Successfully deleted the draft.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: 1
            message:
              type: string
              example: "Draft deleted successfully"
            draft_id:
              type: integer
              description: The ID of the deleted draft.
              example: 1

  '400':
    description: Bad Request, missing draft ID.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: "Draft ID is required"

  '401':
    description: Unauthorized, failed to get JWT identity.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: "Failed to get identity"

  '403':
    description: Forbidden, user is not authorized to delete the draft.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: "Sorry! you are not authorized to delete this Draft!"

  '404':
    description: Not Found, draft not found in RDBMS or MongoDB.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: "Draft not found in RDBMS"  # Could also be "Draft not found in MongoDB"

  '500':
    description: Internal Server Error, failed to delete draft from the database.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: "Failed to delete draft from RDBMS"
            error:
              type: string
              example: "Database connection failed"

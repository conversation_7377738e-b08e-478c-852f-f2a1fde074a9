tags:
  - Booking
summary: Cancel User Booking
description: >
  This endpoint allows a user to cancel their booking.
parameters:
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The booking ID of the ride
  - name: reason
    in: formData
    type: integer
    required: false
    description: Reason for cancellation
  - name: reason_details
    in: formData
    type: string
    required: false
    description: Details about the reason for cancellation
responses:
  200_a:
    description: Booking already cancelled
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (0 for already cancelled)
          example: 0
        message:
          type: string
          description: Status message
          example: "Booking already cancelled"
    examples:
      application/json:
        success: 0
        message: "Booking already cancelled"
  200_b:
    description: Booking cancelled successfully with waiver
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        waiver:
          type: integer
          description: Waiver flag (1 for waiver, 0 otherwise)
          example: 1
        message:
          type: string
          description: Success message
          example: "Booking cancelled successfully"
    examples:
      application/json:
        success: 1
        waiver: 1
        message: "Booking cancelled successfully"
  200_c:
    description: Booking cancelled successfully without waiver
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        waiver:
          type: integer
          description: Waiver flag (1 for waiver, 0 otherwise)
          example: 0
        message:
          type: string
          description: Success message
          example: "Booking cancelled successfully"
    examples:
      application/json:
        success: 1
        waiver: 0
        message: "Booking cancelled successfully"
  200_d:
    description: Trip already started
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for error)
          example: -2
        message:
          type: string
          description: Error message
          example: "Trip started already"
    examples:
      application/json:
        success: -2
        message: "Trip started already"
  401_a:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: DB Error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

tags:
  - Affiliate
summary: Get Wallet Logs for an Affiliate
description: >
  Retrieves the wallet transaction logs for a specific affiliate, optionally filtered by date range and transaction types.
parameters:
  - in: query
    name: affiliate_id
    required: true
    type: integer
    description: ID of the affiliate whose wallet logs are to be retrieved.
    example: 42
  - in: query
    name: start_date
    required: false
    type: string
    format: date
    description: Filter transactions starting from this date (YYYY-MM-DD).
    example: "2024-01-01"
  - in: query
    name: end_date
    required: false
    type: string
    format: date
    description: Filter transactions up to this date (YYYY-MM-DD).
    example: "2024-12-31"
  - in: query
    name: trans_types
    required: false
    type: string
    description: >
      Comma-separated list of transaction types to include (e.g., `credit`, `deduction`, `transfer`).
    example: "credit,deduction"
  - in: query
    name: regions
    type: string
    required: true
    description: >
          Comma-separated region codes that the representative should have access to.
    example: "0,1"
responses:
  200:
    description: Successfully retrieved wallet logs.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        logs:
          type: array
          items:
            type: object
            properties:
              timestamp:
                type: string
                example: "2024-04-01T10:00:00Z"
              amount:
                type: number
                format: float
                example: 500.0
              trans_type:
                type: string
                example: "credit"
  400:
    description: Invalid or missing affiliate_id.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Missing required parameter: affiliate_id"

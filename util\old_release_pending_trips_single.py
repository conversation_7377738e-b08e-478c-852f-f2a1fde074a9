from main import app
import sys
sys.path.append("/app/")
from time import sleep
from datetime import datetime, time, timedelta

from sqlalchemy import create_engine, exc, or_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func
from bisect import bisect_left
import time as tm
from multiprocessing import Process
import traceback

from booking_params import BookingParams
from db_config import db
from book_ride import get_best, create_pending_entries, create_pending_entries_threaded
from models import Bookings, BookPending, DriverSearch, BookDest
from price import Price, PriceOutstation

SHORT_WAIT = 1 * 60
LONG_WAIT = 3 * 60
NIGHT_THRESH = [time(20,30), time(0,30)]
B2B_ALLOWED_TYPES = [
    BookingParams.TYPE_ZOOMCAR,
    BookingParams.TYPE_CARDEKHO,
    BookingParams.TYPE_OLX,
    BookingParams.TYPE_BHANDARI,
    BookingParams.TYPE_MAHINDRA,
    BookingParams.TYPE_REVV_V2,
    BookingParams.TYPE_SPINNY,
    BookingParams.TYPE_PRIDEHONDA
]
# For now, distances are global. If we need per city distances, we would ideally
# get them from classes for each city.
BOOKING_PHASE_TIMES = {
    1: 9999,
}

def time_to_sleep():
    d = datetime.utcnow().time()
    if d >= NIGHT_THRESH[0] or d <= NIGHT_THRESH[1]:
        return LONG_WAIT
    else:
        return SHORT_WAIT

def init_session(db_uri, pool_recycle):
    engine = create_engine(db_uri, pool_recycle=pool_recycle, isolation_level="READ UNCOMMITTED")
    Session = sessionmaker(engine)
    return Session

def _find_closest_phase(time_to_booking):
    booking_phase_idx = list(BOOKING_PHASE_TIMES.keys())
    pos = bisect_left(booking_phase_idx, time_to_booking)
    if pos == 0:
        return booking_phase_idx[0]
    if pos == len(booking_phase_idx):
        return booking_phase_idx[-1]
    before = booking_phase_idx[pos - 1]
    after = booking_phase_idx[pos]
    if after - time_to_booking < time_to_booking - before:
        return after
    return before

def get_booking_phase(b1):
    book_time = datetime.combine(b1.startdate, b1.starttime)
    time_now = datetime.utcnow()
    time_to_booking_hr = ((time_now - book_time).total_seconds())/3600
    phase = _find_closest_phase(time_to_booking_hr)
    return BOOKING_PHASE_TIMES[phase]

def get_driv(b1):
    if b1.type < BookingParams.TYPE_C24:
        if not BookingParams.get_no_broadcast():
            pending_state = BookPending.BROADCAST
        else:
            pending_state = BookPending.SUPPRESSED
    elif b1.type == BookingParams.TYPE_ZOOMCAR:
        if not BookingParams.get_no_broadcast_zoom():
            pending_state = BookPending.BROADCAST
        else:
            pending_state = BookPending.SUPPRESSED
    elif b1.type == BookingParams.TYPE_CARDEKHO:
        if not BookingParams.get_no_broadcast_cd():
            pending_state = BookPending.BROADCAST
        else:
            pending_state = BookPending.SUPPRESSED
    else:
        # TODO
        pending_state = BookPending.BROADCAST
    try:
        ds = DriverSearch.query.filter_by(id=b1.search_key).first()
        book_type = ds.type
        end_time = (datetime(ds.date.year, ds.date.month, ds.date.day,
                                  ds.time.hour, ds.time.minute, ds.time.second) +
                    timedelta(ds.days, ds.dur.hour * 3600 +
                                   ds.dur.minute * 60 + ds.dur.second))

        if BookingParams.is_cust_booktype(book_type) and book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY:
            cur_price = Price.get_price(book_type, ds.dur.hour + ds.dur.minute/60, ds.time, end_time, ds.dist, ds.car_type,
                                        ds.date, end_time.date(), 0, insurance=ds.insurance,
                                        insurance_num=ds.insurance_num, city=ds.region)
        elif book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            cur_price = PriceOutstation.get_price(ds.date,
                            end_time.date(), 1, ds.days * 24 + ds.dur.hour,
                            ds.car_type, book_type, ds.dist, insurance=ds.insurance,
                            insurance_num=ds.insurance_num)
        elif book_type in B2B_ALLOWED_TYPES:
            cur_price = Price.get_price(book_type, ds.dur.hour + ds.dur.minute/60, ds.time, end_time, ds.dist, ds.car_type,
                            ds.date, end_time.date(), 0, insurance=0,
                            insurance_num=0, city=b1.region)
        else:
            cur_price = None
        print("Trying to get best drivers", flush=True)
        st_time = tm.time()
        if BookingParams.maybe_is_oneway(book_type):
            dest = db.session.query(BookDest).filter(BookDest.book_id == b1.id).first()
            if dest:
                ds.dest = dest
        drivers = get_best(ds, b1.user, is_functional=True, cur_price=cur_price)
        print([driver.id for driver in drivers[1]])
        print("Total time taken:", round(tm.time() - st_time, 2), "sec", flush=True)
        if drivers[1]:
            print("Got", len(drivers[1]), "for booking", b1.id, flush=True)
        else:
            print("Got no drivers", flush=True)
            return
        notify_num = create_pending_entries(b1, b1.id, b1.loc, pending_state, drivers[1], ds, b1.user)
        print("Created pending entries, total", notify_num, "for booking", b1.id, flush=True)
    except Exception as e:
        print("Error in creating entries", str(e), flush=True)
        db.session.rollback()

def find_unreleased_bookings(session):
    ret_list = []
    booking_q = session.query(Bookings).filter(
                    Bookings.startdate >= datetime.utcnow().date()).filter(
                    Bookings.valid == 0).filter(or_(Bookings.type < 50,
                    or_(Bookings.type.in_(B2B_ALLOWED_TYPES))))
    all_pending = booking_q.all()
    print("Obtained", len(all_pending), "pending bookings")
    for booking in all_pending:
        print("Processing booking", booking.id)
        book_pending_q = session.query(
                            func.count(BookPending.book_id).label(
                            'pending_count')).filter(
                            BookPending.book_id == booking.id)
        count_pending_entries = book_pending_q.first().pending_count
        if count_pending_entries <= 1:
            ret_list.append(booking)
    return ret_list

if __name__ == '__main__':
    with app.app_context():
        while True:
            try:
                session = init_session(app.config['SQLALCHEMY_DATABASE_URI'], 299)()
                all_unreleased = find_unreleased_bookings(session)
                print("Found", len(all_unreleased), "unreleased bookings", flush=True)
                for b in all_unreleased:
                    print("Releasing booking", b.id, flush=True)
                    get_driv(b)
            except exc.SQLAlchemyError as excp:
                print("Caught SQL error", str(excp))
                db.session.rollback()
                session.rollback()
            except Exception as excp:
                traceback.print_exc()
                print("Caught error", str(excp))
                db.session.rollback()
                session.rollback()
            finally:
                session.close()
                time_to_sleep_sec = time_to_sleep()
                print("Sleeping for", time_to_sleep_sec, "seconds", flush=True)
                for i in range(time_to_sleep_sec):
                    sleep(1)

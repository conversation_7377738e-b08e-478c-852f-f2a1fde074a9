const imageDirectory = "static/assets/images/elements/";
$(document).ready(function() {
    loadMainMenu();
});

function loadMainMenu() {
    $("#faqMenu").find(".menu-wrapper").html("");
    $.each(FAQ_CATEGORIES, function(category, details) {
       addNewMenuCategory(category, details["header"], details["image"]);
    });
}

function addNewMenuCategory(id, header, image) {
    $("#faqMenu").find(".menu-wrapper").append
    (
        '<div id="'+ id +'" class="row no-padding faq-category">'+
            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 image-left no-padding">'+
                '<img src="' + imageDirectory + '/' + image + '.png">'+
            '</div>'+
            '<div class="col-lg-9 col-md-9 col-sm-9 col-xs-12 no-padding border-wrap faq-text-container">'+
                '<h3 class="category-topic-header">'+ header +'</h3>'+
            '</div>'+
            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 image-right no-padding">'+
                '<img src="' + imageDirectory + '/' + image + '.png">'+
            '</div>'+
        '</div>'
    );
}
tags:
  - Trips
summary: Change Trip State
description: >
  This API endpoint allows drivers to update the state of an ongoing trip. It requires valid latitude, longitude, booking ID, and the desired trip state. The driver must be authorized, and their account must be enabled.
parameters:
  - name: state
    in: formData
    type: integer
    required: true
    description: Expected trip state to change to.
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The booking ID of the trip.
  - name: lat
    in: formData
    type: number
    required: false
    description: Latitude of the driver’s location.
  - name: lng
    in: formData
    type: number
    required: false
    description: Longitude of the driver’s location.
responses:
  200:
    description: Trip state successfully changed.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code
          example: 200
        data:
          type: object
          description: Result of state change
    examples:
      application/json:
        status: 200
        data: {"new_state": 3, "trip_id": 12345}
  400_a:
    description: Invalid parameters, such as missing or incorrect booking ID or state.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code
          example: 400
        error:
          type: object
          properties:
            message:
              type: string
              example: "Invalid params"
    examples:
      application/json:
        status: 400
        error:
          message: "Invalid params"
  400_b:
    description: Invalid status requested.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code
          example: 400
        error:
          type: object
          properties:
            message:
              type: string
              example: "Invalid status requested"
    examples:
      application/json:
        status: 400
        error:
          message: "Invalid status requested"
  401_a:
    description: Unauthorized access, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized access)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: User account restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"

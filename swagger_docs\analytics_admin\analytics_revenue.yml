tags:
  - Admin Analytics
summary: Get Admin Revenue Analytics
description: >
  This endpoint provides the revenue analytics data (weekly, monthly, and yearly) 
  for the given region(s). The data is based on the revenue within a given period and
  filtered by region, if provided.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
responses:
  200:
    description: Successfully retrieved revenue analytics data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: object
          properties:
            year_data:
              type: array
              description: Revenue data grouped by year
              items:
                type: object
                properties:
                  year:
                    type: integer
                    description: The year of the revenue data
                    example: 2023
                  revenues:
                    type: number
                    format: float
                    description: Total revenue in that year
                    example: 10000.00
            month_data:
              type: array
              description: Revenue data grouped by month
              items:
                type: object
                properties:
                  month:
                    type: integer
                    description: The month of the revenue data
                    example: 9
                  year:
                    type: integer
                    description: The year of the revenue data
                    example: 2023
                  revenues:
                    type: number
                    format: float
                    description: Total revenue in that month
                    example: 5000
            week_data:
              type: array
              description: Revenue data grouped by week
              items:
                type: object
                properties:
                  week:
                    type: integer
                    description: The week of the revenue data
                    example: 37
                  year:
                    type: integer
                    description: The year of the revenue data
                    example: 2023
                  revenues:
                    type: number
                    format: float
                    description: Total revenue in that week
                    example: 1500
  400:
    description: Invalid date range or missing required fields
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for error)
          example: -3
        error:
          type: string
          description: Error message
          example: "Missing or invalid date values"
  500:
    description: Internal server error or database failure
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error)
          example: 0
        message:
          type: string
          description: Detailed error message
          example: "DB Error"

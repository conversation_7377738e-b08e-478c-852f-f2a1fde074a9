tags:
  - Affiliate
summary: Get All Addresses for an Affiliate Representative
description: >
  Retrieves the list of addresses for a specific affiliate representative, including associated SPOCs.
parameters:
  - in: query
    name: rep_id
    type: string
    required: true
    description: The ID of the affiliate representative.
    example: "101"
  - in: query
    name: regions
    type: string
    required: true
    description: >
      Comma-separated region codes that the representative should have access to.
    example: "0,1"
responses:
  200:
    description: Address list retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        addresses:
          type: array
          items:
            type: object
            properties:
              add_id:
                type: integer
                example: 1
              address:
                type: string
                example: "123 Main St, City"
              nickname:
                type: string
                example: "Home"
              lat:
                type: number
                format: float
                example: 12.9716
              long:
                type: number
                format: float
                example: 77.5946
              add_type:
                type: string
                example: "Residential"
              spocs:
                type: array
                items:
                  type: object
                  properties:
                    spoc_id:
                      type: integer
                      example: 10
                    spoc_name:
                      type: string
                      example: "John Doe"
  400:
    description: Missing required fields.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Affiliate representative ID is required"
  500:
    description: Internal Server Error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Detailed error message"

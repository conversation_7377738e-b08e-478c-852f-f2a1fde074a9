from main import app
from sqlalchemy.sql import func
import sys
sys.path.append("/app/")
from db_config import db
from datetime import datetime, timedelta
from booking_params import Regions
from models import Users, Drivers, BookingAlloc, Trip, Bookings

ACTIVE_REGIONS = {
    Regions.REGN_KOLKATA,
    Regions.REGN_HYDERABAD,
    Regions.REGN_DELHI
}
TWO_MONTHS_AGO = datetime.utcnow() - timedelta(days=60)
MIN_REJ_THRESH = 5

def n_months_ago(n):
    return datetime.utcnow() - timedelta(days=n*30)

def _fetch_all_not_new_drivers(region):
    all_drivers = db.session.query(Drivers.id). \
                             filter(Users.id == Drivers.user). \
                             filter(Users.region.in_(region)). \
                             filter(Drivers.approved >= 1). \
                             filter(Users.reg <= TWO_MONTHS_AGO). \
                             filter(Drivers.id > 2). \
                             order_by(Drivers.id).all()
    return [a[0] for a in all_drivers]

def _get_all_inactive_drivers(driver_ids):
    res = []
    unapprove_res = []
    driver_comp_list = []
    j = 0
    k = 0
    last_allocs = db.session.query(BookingAlloc.driver_id, func.max(BookingAlloc.timestamp),
                                   func.count(BookingAlloc.timestamp)). \
                             filter(BookingAlloc.driver_id.in_(driver_ids)). \
                             group_by(BookingAlloc.driver_id). \
                             order_by(BookingAlloc.driver_id).all()
    num_trips = db.session.query(Bookings.driver, func.count(Trip.id)). \
                           filter(Trip.book_id == Bookings.id). \
                           filter(Bookings.driver.in_(driver_ids)). \
                           filter(Trip.starttime >= n_months_ago(6)). \
                           group_by(Bookings.driver). \
                           order_by(Bookings.driver).all()
    for i in range(len(driver_ids)):
        la = nt = None
        lac = 0
        if j < len(last_allocs) and last_allocs[j][0] == driver_ids[i]:
            la = last_allocs[j][1]
            lac = last_allocs[j][2]
            j += 1
        if k < len(num_trips) and num_trips[k][0] == driver_ids[i]:
            nt = num_trips[k][1]
            k += 1
        driver_comp_list.append((driver_ids[i], la, nt, lac))
    print(len(driver_comp_list), driver_comp_list[0], driver_comp_list[1])
    for dc in driver_comp_list:
        if (not dc[1] or dc[1] < TWO_MONTHS_AGO):
            res.append(dc[0])
        elif not dc[2] or dc[2] == 0 and dc[3] > MIN_REJ_THRESH:
            unapprove_res.append(dc[0])
    return res, unapprove_res

def _deactivate_drivers(driver_ids, to_state):
    for driver_id in driver_ids:
        db.session.query(Drivers). \
                   filter(Drivers.id == driver_id). \
                   update({Drivers.approved: to_state})
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        raise Exception("Error in DB:", str(e))

if __name__ == "__main__":
    with app.app_context():
        driver_id_list = _fetch_all_not_new_drivers(ACTIVE_REGIONS)
        print(len(driver_id_list), "drivers registered at least 2 months ago")
        to_inactive, to_unapprove = _get_all_inactive_drivers(driver_id_list)
        print(len(to_inactive), "inactive drivers found")
        print("Marking as inactive:", to_inactive)
        print("Unapproving:", to_unapprove)
        try:
            _deactivate_drivers(to_inactive, -3)
        except Exception as e:
            print("Could not mark driver as inactive, error:", str(e))
        try:
            _deactivate_drivers(to_unapprove, -2)
        except Exception as e:
            print("Could not mark driver as unapproved, error:", str(e))

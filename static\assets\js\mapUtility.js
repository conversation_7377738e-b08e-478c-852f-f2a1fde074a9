// Note: This example requires that you consent to location sharing when
// prompted by your browser. If you see the error "The Geolocation service
// failed.", it means you probably did not give permission for the browser to
// locate you.
var map;
var pickupLat;
var pickupLong;
var destinationLat;
var destinationLong;
var service_area;
var sourceAddressBar, destinationAddressBar;
var REGION_KOL_BOUNDS;
var KOLKATA_CENTER = { lat: 22.572645, lng: 88.363892 };
const GUWAHATI_CENTER = { lat: 26.1445169, lng: 91.7362365 };
const ZONE_POLYGON = [[22.770349,88.337543],[22.771932,88.369815],[22.774464,88.403461],[22.770349,88.413417],[22.7656,88.429553],[22.752303,88.458392],[22.740905,88.477275],[22.731406,88.497188],
[22.725072,88.508518],[22.716205,88.510234],[22.709871,88.507144],[22.706071,88.494785],[22.693937,88.482501],[22.681183,88.481312],[22.673981,88.479583],[22.664477,88.475463],[22.65814,88.47203],
[22.648794,88.475806],[22.643915,88.488681],[22.630923,88.494517],[22.617022,88.504486],[22.603632,88.492492],[22.59341,88.495925],[22.583346,88.500732],[22.577323,88.497127],[22.574467,88.49919],
[22.574982,88.505112],[22.574269,88.512236],[22.56987,88.517729],[22.56341,88.514811],[22.558921,88.516986],[22.551232,88.513639],[22.556187,88.4957],[22.561576,88.484456],[22.555675,88.476684],
[22.538553,88.471706],[22.527137,88.46587],[22.514451,88.45763],[22.513183,88.442524],[22.49098,88.433597],[22.470994,88.439777],[22.419272,88.45351],[22.401722,88.442688],[22.366168,88.446465],
[22.347117,88.438568],[22.346799,88.430672],[22.351562,88.421059],[22.366803,88.400116],[22.419488,88.343663],[22.446779,88.285298],[22.500077,88.22968],[22.538114,88.242181],[22.550005,88.23905],
[22.560468,88.24008],[22.561895,88.249693],[22.561736,88.255358],[22.559834,88.258791],[22.557298,88.264456],[22.556981,88.267717],[22.557615,88.273039],[22.560785,88.292437],[22.563854,88.299228],
[22.567104,88.30236],[22.571146,88.30485],[22.571384,88.307167],[22.57293,88.314291],[22.578992,88.323346],[22.586323,88.324548],[22.59278,88.32622],[22.599,88.325662],[22.602487,88.327078],[22.607388,88.329218],
[22.613647,88.33308],[22.621241,88.332671],[22.631077,88.336563],[22.653098,88.334332],[22.661811,88.340511],[22.666563,88.34755],[22.671849,88.355204],[22.677867,88.358466],[22.688162,88.363272],
[22.698928,88.362646],[22.709221,88.362131],[22.719514,88.360758],[22.727114,88.358183],[22.73598,88.361788],[22.745796,88.363505],[22.754661,88.355437],[22.761309,88.345137],[22.769436,88.33682]];

function myMap() {
  var center = { lat: KOLKATA_CENTER["lat"], lng: KOLKATA_CENTER["lng"] };
  pickupLat = KOLKATA_CENTER["lat"], pickupLong = KOLKATA_CENTER["lng"];
  destinationLat = -91, destinationLong = -91;
  map = new google.maps.Map(document.getElementById('map'), {
	center: {lat: KOLKATA_CENTER["lat"], lng: KOLKATA_CENTER["lng"]},
	zoom: 11,
	mapTypeControl: false,
	streetViewControl: false,
	fullscreenControl: false,
  zoomControlOptions: {
        position: google.maps.ControlPosition.RIGHT_CENTER
    },

	clickableIcons: false,
  gestureHandling: 'greedy',
	styles: [
  {
    "featureType": "administrative",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#d6e2e6"
      }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
  },
  {
    "featureType": "administrative",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#7492a8"
      }
    ]
  },
  {
    "featureType": "administrative.neighborhood",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "lightness": 25
      }
    ]
  },
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "landscape.man_made",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#d6e2e6"
      }
    ]
  },
  {
    "featureType": "landscape.man_made",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
  },
  {
    "featureType": "landscape.natural",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#dae6eb"
      }
    ]
  },
  {
    "featureType": "landscape.natural",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#7492a8"
      }
    ]
  },
  {
    "featureType": "landscape.natural.terrain",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#d6e2e6"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#588ca4"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.icon",
    "stylers": [
      {
        "saturation": -100
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#cae7a8"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#bae6a1"
      }
    ]
  },
  {
    "featureType": "poi.sports_complex",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#c6e8b3"
      }
    ]
  },
  {
    "featureType": "poi.sports_complex",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#bae6a1"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#41626b"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "labels.icon",
    "stylers": [
      {
        "saturation": -45
      },
      {
        "lightness": 10
      },
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#f7fdff"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#beced4"
      }
    ]
  },
  {
    "featureType": "road.arterial",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#eef3f5"
      }
    ]
  },
  {
    "featureType": "road.arterial",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#edf3f5"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "featureType": "transit",
    "elementType": "labels.icon",
    "stylers": [
      {
        "saturation": -70
      }
    ]
  },
  {
    "featureType": "transit.line",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#588ca4"
      }
    ]
  },
  {
    "featureType": "transit.station",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#008cb5"
      }
    ]
  },
  {
    "featureType": "transit.station.airport",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "saturation": -100
      },
      {
        "lightness": -5
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#a6cbe3"
      }
    ]
  }
]

  });
  var box = [];
  $.each(ZONE_POLYGON, function(index, item) {
      var kpoint = new google.maps.LatLng(item[0], item[1]);
      box.push(kpoint);
  });

  REGION_KOL_BOUNDS = getRegionBounds();

  service_area = new google.maps.Polygon({
      path: box,
      map: map,
      strokeColor: '#2c7493',
      strokeOpacity: 1,
      strokeWeight: 1,
      fillColor: '#2c7493',
      fillOpacity: 0.1
  });
  var pos;
  // Try HTML5 geolocation.
  if (navigator.geolocation) {
	navigator.geolocation.getCurrentPosition(function(position) {
	  pos = {
		lat: position.coords.latitude,
		lng: position.coords.longitude
	  };
	  map.setZoom(15);
	  map.panTo(pos);
    pickupLat = pos.lat;
    pickupLong = pos.lng;
    if(pickupMarker != null){
      var latlng = new google.maps.LatLng(pos.lat, pos.lng);
      pickupMarker.setPosition(latlng);
      revGeocodePosition(pickupMarker.getPosition(), "#sourceSearch", "source");
    }
	}, function() {
	  handleLocationError(true, -1, map.getCenter());
	});
  } else {
	// Browser doesn't support Geolocation
	handleLocationError(false, -1, map.getCenter());
  }
  var imageSrc = 'https://www.drivers4me.com/static/assets/green.png';
  var imageDest = 'https://www.drivers4me.com/static/assets/red.png';

  pickupMarker = new google.maps.Marker(
  {
      map:map,
      draggable:true,
      animation: google.maps.Animation.DROP,
      position: center,
      icon: imageSrc
  });

    revGeocodePosition(pickupMarker.getPosition(), "#sourceSearch", "source");

  destinationMarker = new google.maps.Marker(
  {
      map:map,
      draggable:true,
      animation: google.maps.Animation.DROP,
      position: center,
      icon: imageDest
  });

  //initially hide destination marker
  destinationMarker.setVisible(false);

  google.maps.event.addListener(pickupMarker, 'dragend', function()
  {
      revGeocodePosition(pickupMarker.getPosition(), "#sourceSearch", "source");
  });

  google.maps.event.addListener(destinationMarker, 'dragend', function()
  {
      revGeocodePosition(destinationMarker.getPosition(), "#destinationSearch", "destination");
  });

  function revGeocodePosition(pos, addrId, locType)
  {
     geocoder = new google.maps.Geocoder();
     geocoder.geocode
      ({
          latLng: pos
      },
          function(results, status)
          {
              if (status == google.maps.GeocoderStatus.OK)
              {
                $(addrId).val(results[0].formatted_address)
				map.panTo(pos);
                if(locType === "source") {
                    sourceLat = pickupMarker.getPosition().lat();
                    sourceLong = pickupMarker.getPosition().lng();
                }
                else {
                    destinationLat = destinationMarker.getPosition().lat();
                    destinationLong = destinationMarker.getPosition().lng();
                }

              }
              else
              {
                alert("There is some issue! Try again later!1")//Some issue in fetching maker location
              }
          }
      );
  }
}
function handleLocationError(browserHasGeolocation, infoWindow, pos) {
}

function initMapInputs(autocompleteOnly) {
    var pickupInput = document.getElementById("sourceSearch");
    if(pickupInput == null){
        var pickupInput = document.getElementById("label_source");
    }
    sourceAddressBar = new google.maps.places.Autocomplete(pickupInput);
    sourceAddressBar.setBounds(REGION_KOL_BOUNDS);
    sourceAddressBar.setFields(["geometry", "name"]);
    sourceAddressBar.setOptions({strictBounds: true});
    sourceAddressBar.setComponentRestrictions(
        {'country': ['in']});
    var dropOffInput = document.getElementById("destinationSearch");
    if(dropOffInput == null){
        var dropOffInput = document.getElementById("label_destination");
    }
    destinationAddressBar = new google.maps.places.Autocomplete(dropOffInput);
    destinationAddressBar.setBounds(REGION_KOL_BOUNDS);
    destinationAddressBar.setFields(["geometry", "name"]);
    destinationAddressBar.setOptions({strictBounds: true});
    destinationAddressBar.setComponentRestrictions(
        {'country': ['in']});

    if(!autocompleteOnly) {
        setMapEventHandlers();
    }

    else {
        setAutoCompleteEventHandlers();
    }
}

function setMapEventHandlers() {
    /**
    * Event Handler for new location entered on autocomplete (source)
    */
    google.maps.event.addListener(sourceAddressBar, 'place_changed', function() {
        geocodePosition($("#sourceSearch").val(), "#sourceSearch", "source");
    });

    /**
    * Event Handler for new location entered on autocomplete (destination)
    */
    google.maps.event.addListener(destinationAddressBar, 'place_changed', function() {
        geocodePosition($("#destinationSearch").val(), "#destinationSearch", "destination");
        destinationMarker.setVisible(true);
    });
}

function setAutoCompleteEventHandlers() {
    /**
    * Event Handler for new location entered on autocomplete (source)
    */
    google.maps.event.addListener(sourceAddressBar, 'place_changed', function() {
        if ($("#sourceSearch").val() != null)
            geocodeFromPlacesInput($("#sourceSearch").val(), "#sourceSearch", "source");
        else
            geocodeFromPlacesInput($("#label_source").val(), "#label_source", "source");

    });

    /**
    * Event Handler for new location entered on autocomplete (destination)
    */
    google.maps.event.addListener(destinationAddressBar, 'place_changed', function() {
        if ($("#destinationSearch").val() != null)
            geocodeFromPlacesInput($("#destinationSearch").val(), "#destinationSearch", "destination");
        else
            geocodeFromPlacesInput($("#label_destination").val(), "#label_destination", "destination");
    });
}

function geocodeFromPlacesInput(addr, addrId, locType) {
    geocoder = new google.maps.Geocoder();
    geocoder.geocode
     ({
         address: addr
     },
         function(results, status)
         {
             if (status == google.maps.GeocoderStatus.OK)
             {
                 if(locType === "source") {
                     pickupLat = results[0].geometry.location.lat();
                     pickupLong = results[0].geometry.location.lng();
                 }
                 else {
                     destinationLat = results[0].geometry.location.lat();
                     destinationLong = results[0].geometry.location.lng();
                 }
             }
             else
             {
               alert("There is some issue! Try again later!2") //Some issue in fetching maker location
             }
         }
     );
}

function geocodePosition(addr, addrId, locType)
{
   geocoder = new google.maps.Geocoder();
   geocoder.geocode
    ({
        address: addr
    },
        function(results, status)
        {
            if (status == google.maps.GeocoderStatus.OK)
            {
                if(locType === "source") {
                    pickupMarker.setPosition(results[0].geometry.location);
                    pickupLat = pickupMarker.getPosition().lat();
                    pickupLong = pickupMarker.getPosition().lng();
                }
                else {
                    destinationMarker.setPosition(results[0].geometry.location);
                    destinationLat = destinationMarker.getPosition().lat();
                    destinationLong = destinationMarker.getPosition().lng();
                }
                $(addrId).val(results[0].formatted_address);
                map.setZoom(15)
                map.panTo(results[0].geometry.location);
            }
            else
            {
              alert("There is some issue! Try again later!3") //Some issue in fetching maker location
            }
        }
    );
}



// set bounds for locations
function getRegionBounds(selector) {
    var regionBounds = new google.maps.LatLngBounds
    (
        new google.maps.LatLng({lat: parseFloat(KOLKATA_CENTER["lat"]), lng: parseFloat(KOLKATA_CENTER["lng"])}),
        new google.maps.LatLng({lat: ZONE_POLYGON[0][0], lng: ZONE_POLYGON[0][1]})
    );
    $.each(ZONE_POLYGON, function(index, item) {
        var point = new google.maps.LatLng(item[0], item[1]);
        regionBounds.extend(point);
    });
    return regionBounds;

}
function getRegionBoundsZoomcar(selector) {
    var regionBounds = new google.maps.LatLngBounds
    (
        new google.maps.LatLng({lat: parseFloat(KOLKATA_CENTER["lat"]), lng: parseFloat(KOLKATA_CENTER["lng"])}),
        new google.maps.LatLng({lat: parseFloat(GUWAHATI_CENTER["lat"]), lng: parseFloat(GUWAHATI_CENTER["lng"])}),
        new google.maps.LatLng({lat: ZONE_POLYGON[0][0], lng: ZONE_POLYGON[0][1]})

    );
    $.each(ZONE_POLYGON, function(index, item) {
        var point = new google.maps.LatLng(item[0], item[1]);
        regionBounds.extend(point);
    });
    return regionBounds;

}
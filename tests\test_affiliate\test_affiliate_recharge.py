from unittest.mock import patch
from affiliate_b2b.affiliate_models import db,Affiliate,AffiliateRep,AffiliateRepLogs
from conftest import create_master_affiliate
import pytest

@pytest.fixture
def create_affiliate():
    affiliate = Affiliate("Receiver_Affiliate","Receiver Affiliate", 0 , -1, 10, 1)
    db.session.add(affiliate)
    affiliate.wallet = 10000
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    return affiliate




#  API - /api/admin/restart_trip

def test_add_credit_success(client, admin_login):
    master_affiliate, redis_key = create_master_affiliate()
    auth_headers, _ = admin_login
    assert master_affiliate is not None
    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "affiliate_id": master_affiliate.id,
            "amount": "100",
            "remark": "Bonus for performance",
            "credit_type": "Add",
            "gst_status": "Excluded",
            "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 200
    assert res["success"] == 1
    assert "credits successfully added" in res["message"]

def test_deduct_credit_success(client, admin_login):
    master_affiliate, redis_key = create_master_affiliate()
    auth_headers, _ = admin_login
    assert master_affiliate is not None
    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "affiliate_id": master_affiliate.id,
            "amount": "50",
            "remark": "Fine for delay",
            "credit_type": "Fine",
            "credit_type_reason": "Late Arrival",
            "gst_status": "Excluded",
            "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    print(res)
    assert response.status_code == 200
    assert res["success"] == 1
    assert "credits successfully deducted" in res["message"]

def test_transfer_credit_success(client, admin_login, create_affiliate):
    auth_headers, _ = admin_login
    master_affiliate, redis_key = create_master_affiliate()
    master_affiliate.wallet = 1000
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()

    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "affiliate_id": master_affiliate.id,
            "transfer_affiliate": create_affiliate.id,
            "amount": "200",
            "remark": "Transfer for shared trip",
            "credit_type": "Transfer",
            "gst_status": "Excluded",
            "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 200
    assert res["success"] == 1
    assert "credits transferred successfully" in res["message"]

def test_insufficient_balance_on_transfer(client, admin_login, create_affiliate):
    auth_headers, _ = admin_login
    master_affiliate, redis_key = create_master_affiliate()
    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "affiliate_id": master_affiliate.id,
            "transfer_affiliate": create_affiliate.id,
            "amount": "10000",  # Exceeds balance
            "remark": "Excessive transfer",
            "credit_type": "Transfer",
            "gst_status": "Excluded",
            "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 400
    assert res["success"] == -7
    assert res["message"] == "Insufficient balance in the sender's wallet"

def test_missing_required_fields(client, admin_login):
    auth_headers, _ = admin_login
    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "amount": "100",
            "remark": "No affiliate_id provided",
            "credit_type": "Add",
            "gst_status": "Excluded",
                   "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 500
    assert res["success"] == -2

def test_affiliate_not_found(client, admin_login):
    auth_headers, _ = admin_login
    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "affiliate_id": 999999,  # Invalid ID
            "amount": "100",
            "remark": "Ghost affiliate",
            "credit_type": "Add",
            "gst_status": "Excluded",
                   "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 404
    assert res["success"] == 0
    assert res["message"] == "Affiliate not found"

@patch("requests.post")
def test_invalid_gst_number(mock_post, client, admin_login):
    master_affiliate, redis_key = create_master_affiliate()
    auth_headers, _ = admin_login
    mock_post.return_value.status_code = 200
    mock_post.return_value.json.return_value = {
        "data": {"code": "1005"}
    }

    response = client.post(
        "/api/admin/affiliate/affiliate_credit",
        data={
            "affiliate_id": master_affiliate.id,
            "amount": "100",
            "remark": "With invalid GST",
            "credit_type": "Add",
            "gst_status": "Excluded",
            "gst_number": "33ABCDE1234F2Z5",
                   "regions": "0"
        },
        headers=auth_headers
    )
    res = response.get_json()
    assert response.status_code == 404
    assert res["success"] == 0
    assert res["message"] == "GST Number not found"
    
# --------------------------------- 

#  API - /api/admin/affiliate/wallet_logs
    
def test_admin_affiliate_wallet_logs_success(client, admin_login, create_affiliate):
    auth_headers, _ = admin_login
    aff = create_affiliate

    response = client.get(
        "/api/admin/affiliate/wallet_logs",
        query_string={
            "affiliate_id": aff.id,
            "regions":"0",
        },
        headers=auth_headers
    )

    res = response.get_json()
    print(res)
    assert response.status_code == 200
    assert res["success"] == 1
    assert "logs" in res
    
  
# --------------------------------- 

#  API - /api/admin/affiliate/details 
    
def test_get_affiliate_details_success(client, admin_login, create_affiliate):
    auth_headers, _ = admin_login
    aff = create_affiliate

    response = client.post(
        "/api/admin/affiliate/details",
        data={
            "affiliate_id": aff.id,
            "type": 1,
            "admin_call": 1,
            "regions":"0",
        },
        headers=auth_headers
    )

    res = response.get_json()
    assert response.status_code == 200
    assert res["success"] == 1
    assert "basic_data" in res  # or whatever key is expected
    
# --------------------------------- 
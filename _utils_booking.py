#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  booking_params.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import http.client
import json
import math
from google.cloud import firestore
from numpy import base_repr
from sqlalchemy import or_, and_, exc, func
from sqlalchemy.sql import text
import _sms
from db_config import db, fb_db
from _rtdb import _update_user_pending, _update_driver_pending
from flask import current_app as app
from _fcm import send_fcm_msg
from booking_params import BookingParams
from cluster_loc import LocStr
from _ops_message import send_slack_msg
from price import Price
from _utils_acc import get_driver_user_id
from datetime import datetime, timedelta
from models import DriverSearch, Bookings, Trip, BookPending, BookingAlloc, Users, BookPricing, ZoomcarBookings, BookDest
from gevent import spawn, sleep
from booking_params import Regions
import requests
from sqlalchemy.orm import aliased
from sqlalchemy.exc import SQLAlchemyError
from _utils import convert_to_ist
from affiliate_b2b.affiliate_models import Affiliate,AffiliateRepLogs,AffiliateTaxTrans, AffiliateRep,ClientGstDetails, DraftAffiliate, AffiliatePricingLogs, AffiliateLogs, AffiliateCustomLogs,AffiliateWalletLogs,AffiliatePriceMapping,AffiliateAddress,AffiliateSpoc,AddressSpoc,AddressLogs,SpocLogs
from sqlalchemy.orm import sessionmaker


def get_car_type_skey(skey):
    try:
        car_type = db.session.query(DriverSearch).filter(DriverSearch.id == skey). \
                       first().car_type
    except Exception as excp:
        print(excp)
        return 0
    return car_type


def get_car_type(booking_id):
    try:
        skey = db.session.query(Bookings).filter(Bookings.id == booking_id).first().search_key
        if skey:
            car_type = db.session.query(DriverSearch).filter(DriverSearch.id == skey). \
                       first()
            if car_type:
                car_type=car_type.car_type
            else:
                car_type=0
        else:
            car_type = 0
    except Exception as excp:
        print(excp)
        return 0
    return car_type


def get_b2b_book_code(booking_id):
    # base 36 of 2 digits of mobile + user id (max 99 mil users)
    code = base_repr(booking_id + int(int(booking_id)%100 * 10e6), 36)
    code = str(code).rjust(6, '0')

    return code

def get_book_code(booking_id):
    # base 36 of 2 digits of mobile + user id (max 99 mil users)
    code = base_repr(booking_id + int(int(booking_id)%100 * 10e6), 36)
    code = str(code).rjust(6, '0')
    code_saved = db.session.query(Bookings).filter(Bookings.id == booking_id)
    if not code_saved.first() or not code_saved.first().code:
        try:
            code_saved.update({Bookings.code: code})
            db.session.flush()
        except Exception as excp:
            print(excp)
            db.session.rollback()
    else:
        if code_saved.first().code != code:
            try:
                code_saved.update({Bookings.code: code})
                db.session.flush()
            except Exception as excp:
                print(excp)
                db.session.rollback()
    return code

def fetch_booking_trip(booking_id):
    try:
        trip = db.session.query(Trip).filter(Trip.book_id == booking_id)
        return trip
    except Exception as e:
        print('issue in fetching trip')
        print("Error in %s: %s" % (__name__, str(e)))
        return None

def booking_has_trip(booking_id):
    try:
        trip = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
        return trip is not None
    except Exception as e:
        print("Error in %s: %s" % (__name__, str(e)))
        return False


def get_dist_on_map(source_lat, source_long, dest_lat, dest_long):
    url = "https://maps.googleapis.com/maps/api/distancematrix/json"
    params = {
        "units": "imperial",
        "origins": f"{source_lat},{source_long}",
        "destinations": f"{dest_lat},{dest_long}",
        "mode": "driving",
        "key": app.config['MAPS_DIST_API_KEY']
    }

    for _ in range(5):  # Retry up to 5 times
        try:
            response = requests.get(url, params=params, timeout=1)  # Add timeout for fast failures
            response.raise_for_status()  # Raise error for HTTP failures (4xx, 5xx)
            resp = response.json()
            
            if resp['rows'][0]['elements'][0]['status'] == 'OK':
                return resp['rows'][0]['elements'][0]['distance']['value']

        except (requests.RequestException, KeyError, IndexError) as e:
            print("Exception fetching data from GMap:", str(e))

    return 0  # Return 0 if all attempts fail


def max_driver_owed(booking_id):
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    #Handles in case Booking doesn't exist
    if not booking:
        return -100
    if booking.region == Regions.REGN_KOLKATA:
        return -5
    else:
        return -100

def check_ongoing_time(booking):

    booking_start_datetime = datetime.combine(booking.startdate, booking.starttime)
    booking_end_datetime = datetime.combine(booking.enddate, booking.endtime)

    booking_start_minus_n_minutes = booking_start_datetime - timedelta(minutes=BookingParams.MAX_THRESH_BEFORE_NEXT_TRIP)
    booking_end_plus_n_minutes = booking_end_datetime + timedelta(minutes=BookingParams.MAX_THRESH_BEFORE_NEXT_TRIP)

    return booking_start_minus_n_minutes, booking_end_plus_n_minutes

def check_ongoing_time_date_time(booking):
    booking_start_datetime = datetime.combine(booking.startdate, booking.starttime)
    booking_end_datetime = datetime.combine(booking.enddate, booking.endtime)

    # Calculate the adjusted datetimes
    booking_start_minus_n = booking_start_datetime - timedelta(minutes=BookingParams.MAX_THRESH_BEFORE_NEXT_TRIP)
    booking_end_plus_n = booking_end_datetime + timedelta(minutes=BookingParams.MAX_THRESH_BEFORE_NEXT_TRIP)

    # Split into date and time components
    start_date = booking_start_minus_n.date()
    start_time = booking_start_minus_n.time()
    end_date = booking_end_plus_n.date()
    end_time = booking_end_plus_n.time()

    return start_date, start_time, end_date, end_time

# Accept trip for given driver id, booking id pair.
# Booking ID can be sent as raw booking id or internal B2B id.
def driver_accept_booking(booking_id, driver, update_pending=True, is_user=True):
    with app.app_context():
        engine = db.engine.execution_options(timeout=30)
        Session = sessionmaker(bind=engine)
        session = Session()
        print("Accepting trip for", driver, "booking id", booking_id)
        driver_user = get_driver_user_id(driver)

        # Variables to be used for notifications after the transaction.
        user_id = None
        book_code = None
        booking_region = None
        check_exists = None  # Will hold the pricing and booking tuple.
        pending_phase = None
        pending_score = None

        try:
            # All database write operations are enclosed in a transactional context.
            with session.begin(),session.begin_nested():
                # Retrieve and lock the booking record.
                booking = session.query(Bookings) \
                    .filter(Bookings.id == booking_id) \
                    .with_for_update() \
                    .first()
                if not booking:
                    return {'success': -1, "message": "Booking does not exist", 'code': 201}

                # Save key booking details.
                user_id = booking.user
                book_code = booking.code
                booking_region = booking.region

                booking_start_minus_n_minutes, booking_end_plus_n_minutes = check_ongoing_time(booking)

                # Build the set of drivers with active bookings overlapping the current booking window.
                booked = set(
                    driver_row.driver
                    for driver_row in session.query(Bookings)
                        .filter(Bookings.valid > 0)
                        .filter(
                            or_(
                                func.timestamp(Bookings.startdate, Bookings.starttime) <= booking_end_plus_n_minutes,
                                Bookings.startdate < booking.enddate,
                            )
                        )
                        .filter(
                            or_(
                                func.timestamp(Bookings.enddate, Bookings.endtime) >= booking_start_minus_n_minutes,
                                Bookings.enddate > booking.enddate,
                            )
                        ).all()
                )

                # If update_pending is True and the driver is already booked, update pending records.
                if update_pending and driver in booked:
                    session.query(BookPending) \
                        .filter(BookPending.driver == driver, BookPending.book_id == booking_id) \
                        .update({BookPending.valid: 0}, synchronize_session=False)
                    # No explicit commit; the context manager will commit the update.
                if driver in booked:
                    print("Could not accept: Already have booking")
                    return {'success': -5, "message": "Already have booking"}

                if booking.valid == -1:
                    print("Could not accept: Booking is cancelled by user")
                    return {'success': -6, "message": "Booking is cancelled by user"}
                if booking.valid == 1 and booking.driver != 1:
                    print("Could not accept: Booking is allocated")
                    return {'success': -5, "message": "Booking is allocated"}

                # Retrieve pricing and booking details.
                check_exists = session.query(BookPricing, Bookings) \
                                        .filter(Bookings.id == booking_id) \
                                        .filter(BookPricing.book_id == booking_id) \
                                        .first()
                if not check_exists:
                    print("Could not accept: Booking does not exist")
                    return {'success': -1, "message": "Booking does not exist"}

                # Update booking fields.
                session.query(Bookings).filter(Bookings.id == booking_id).update({
                    Bookings.estimate:         check_exists[0].estimate,
                    Bookings.estimate_pre_tax:   check_exists[0].est_pre_tax,
                    Bookings.insurance_cost:     check_exists[0].insurance_ch,
                    Bookings.valid:              1,
                    Bookings.driver:             driver,
                }, synchronize_session=False)

                # Handle pending bookings.
                pending_query = session.query(BookPending) \
                                        .filter(BookPending.valid >= 1) \
                                        .filter(BookPending.book_id == booking_id)
                pending_record = pending_query.first()
                if pending_record:
                    pending_phase = pending_record.phase
                    pending_score = pending_record.score
                else:
                    pending_phase = -1
                    pending_score = -99

                if update_pending:
                    pending_query.update({BookPending.valid: 0}, synchronize_session=False)

                # Note: All changes so far will be committed at the end of the context managers.

                # Create booking allocation record.
                ba_entry = BookingAlloc(booking_id, driver, driver_user, phase=pending_phase, score=pending_score)
                session.add(ba_entry)
                # The allocation is also committed automatically as the transaction completes.

                # Adjust is_user based on booking type.
                if check_exists[1].type >= BookingParams.TYPE_C24:
                    is_user = False
                if is_user:
                    _update_user_pending(booking.user)
                    _update_driver_pending(driver)
                    try:
                        fb_db.collection(u'book_pending').document(str(driver)).update({
                            str(booking_id): firestore.DELETE_FIELD
                        })
                    except Exception:
                        pass
        # End of the transactional block; all changes have been committed.

            # Execute further read operations and notifications using the same session.
            driver_det = session.query(Users).filter(Users.id == driver_user).first()
            driver_name = driver_det.get_name()
            from live_update_booking import send_live_update_of_booking
            send_live_update_of_booking(booking_id, booking_region)

            # Update trip_set in Firestore if user update is required.
            if is_user:
                fb_db.collection(u'trip_set').document(str(user_id)) \
                    .set({str(booking_id): driver_name}, merge=True)

            # If update_pending is False, send Slack notifications.
            if not update_pending:
                if check_exists[1].type != BookingParams.TYPE_B2B:
                    target_user = session.query(Users).filter(Users.id == user_id).first()
                    user_mobile = _sms.COUNTRY_CODE_IN + str(target_user.mobile)
                    msg_content = (
                        "Hi " + target_user.get_name() +
                        "! Your trip with DRIVERS4ME (ID: #" + str(book_code) +
                        ") has been assigned to " + driver_name +
                        " (" + str(driver_det.mobile) + ")"
                    )
                    send_slack_msg(1, msg_content)

            # If user notifications are required.
            if is_user:
                smalltext = ("Your driver for booking #" + str(book_code) + ": " +
                             driver_name + " (" + str(driver_det.mobile) + ")")
                send_fcm_msg(user_id, title="Drivers4Me - Booking confirmed!",
                             smalltext=smalltext, bigtext=msg_content)
                msg_content_json = {
                    "name": target_user.get_name(),
                    "code": book_code,
                    "driver-name": driver_name,
                    "driver-mobile": str(driver_det.mobile)
                }
                # Create a human-readable message a/c template
                message = (
                    f"Hi, {msg_content_json['name']}! Your trip with Drivers4Me (ID: {msg_content_json['code']}) has been assigned "
                    f"to {msg_content_json['driver-name']} ({msg_content_json['driver-mobile']})."
                )
                response = _sms.send_bulk_message_gupshup(
                    phone_numbers=[str(user_mobile)],
                    message=message,
                    mask= _sms.MASK,
                    dltTemplateId=_sms.TEMPLATE_ID_MAPPING['trip-allocated'],
                    principalEntityId= _sms.PRINCIPAL_ENTITY_ID
                )
                # Check response and return meaningful message
                if response and "success" in response.lower():
                    return {'success': 1, 'msg': 1,'data': f"Message sent successfully!"}
                else:
                    return {'success': 1, 'msg': -1,'data': f"Failed to send message !"}
                # if not resp:
                #     return {'success': 1, 'msg': -1}
                if not resp:
                    return {'success': 1, 'msg': -1}

            return {'success': 1, 'msg': 1}
        except Exception as excp:
            session.rollback()
            print("Encountered exception:", excp)
            return {'success': -1, 'message': str(excp)}
        finally:
            session.close()

# Accept trip for given driver id, booking id pair.
# Booking ID can be sent as raw booking id or internal B2B id.
def driver_accept_booking_v2(booking_id, driver, _update_pending=True, is_user=True):
    print("Accepting trip for", driver, "booking id", booking_id)
    driver_user = get_driver_user_id(driver)
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    booked = set([drivers.driver for drivers in
                  Bookings.query.filter(
                      or_(and_(Bookings.startdate == booking.enddate,
                               Bookings.starttime < booking.endtime),
                          (Bookings.startdate < booking.enddate))). \
                    filter(or_(and_(Bookings.enddate == booking.startdate,
                                    Bookings.endtime > booking.starttime),
                               Bookings.enddate > booking.enddate)). \
                    filter(Bookings.valid > 0).all()])
    if driver in booked:
        db.session.query(BookPending).filter(BookPending.driver == driver). \
                    filter(BookPending.book_id == booking_id).delete()
        try:
            db.session.commit()
        except Exception as excp:
            print(excp)
            db.session.rollback()
        return {'success': -2}
    if not booking:
        return {'success': -1, 'code': 201}
    if (booking.valid == 1 and booking.driver != 1):
        return {'success': -2}
    user_id = booking.user
    book_code = booking.code

    check_exists = db.session.query(BookPricing, Bookings). \
        filter(Bookings.id == booking_id). \
        filter(BookPricing.book_id == booking_id).first()
    if check_exists:
        # ok db calls now
        Bookings.query.filter(Bookings.id == booking_id).update({
            Bookings.estimate: check_exists[0].estimate})
        Bookings.query.filter(Bookings.id == booking_id).update({
            Bookings.estimate_pre_tax: check_exists[0].est_pre_tax})
        Bookings.query.filter(Bookings.id == booking_id).update({
            Bookings.insurance_cost: check_exists[0].insurance_ch})
        Bookings.query.filter(Bookings.id == booking_id).update({
            Bookings.valid: 1})
        Bookings.query.filter(Bookings.id == booking_id).update({
            Bookings.driver: driver})
        db.session.query(BookPending).filter(BookPending.valid >= 1). \
                filter(BookPending.book_id == booking_id).update({BookPending.valid: 0})
        booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
        if not booking:
            db.session.rollback()
            return {'success': -1, 'code': 201}
        if (booking.valid == 1 and booking.driver != 1):
            db.session.rollback()
            return {'success': -2}
        try:
            db.session.commit()
        except Exception:
            db.session.rollback()
        try:
            ba_entry = BookingAlloc(booking_id, driver, driver_user)
            db.session.add(ba_entry)
            db.session.commit()
        except exc.SQLAlchemyError as excp:
            db.session.rollback()
            print("Got exception", str(excp))
        is_user =  check_exists[1].type < BookingParams.TYPE_C24
        if is_user:
            _update_user_pending(booking.user)
            _update_driver_pending(driver)
            try:
                fb_db.collection(u'book_pending').document(str(driver)).update(
                    {str(booking_id): firestore.DELETE_FIELD})
            except Exception:
                pass
        driver_det = db.session.query(Users).filter(Users.id == driver_user).first()
        driver_name = driver_det.get_name()

        if is_user:
            fb_db.collection(u'trip_set').document(str(user_id)).set(
                {str(booking_id): driver_name}, merge=True)
            target_user = db.session.query(Users).filter(Users.id == user_id).first()
            user_mobile = _sms.COUNTRY_CODE_IN + str(target_user.mobile)
            msg_content = ("Hi " + target_user.get_name() +
                           "! Your trip with DRIVERS4ME (ID: #" + book_code
                           + ") has been assigned to " + driver_name \
                           + " (" + str(driver_det.mobile) + ")")
            send_slack_msg(1, msg_content)
            smalltext = ("Your driver for booking #" + str(book_code) + ": " +
                         driver_name + " (" + str(driver_det.mobile) + ")")
            send_fcm_msg(user_id, title="Drivers4Me - Booking confirmed!",
                         smalltext=smalltext, bigtext=msg_content)
            msg_content_json = {
                "name": target_user.get_name(),
                "code": book_code,
                "driver-name": driver_name,
                "driver-mobile": str(driver_det.mobile)
            }
            # Create a human-readable message a/c template
            message = (
                f"Hi, {msg_content_json['name']}! Your trip with Drivers4Me (ID: {msg_content_json['code']}) has been assigned "
                f"to {msg_content_json['driver-name']} ({msg_content_json['driver-mobile']})."
            )
            response = _sms.send_bulk_message_gupshup(
                phone_numbers=[str(user_mobile)],
                message=message,
                mask= _sms.MASK,
                dltTemplateId=_sms.TEMPLATE_ID_MAPPING['trip-allocated'],
                principalEntityId= _sms.PRINCIPAL_ENTITY_ID
            )
            # Check response and return meaningful message
            if response and "success" in response.lower():
                return {'success': 1, 'msg': 1,'data': f"Message sent successfully!"}
            else:
                return {'success': 1, 'msg': -1,'data': f"Failed to send message !"}
        return {'success': 1, 'msg': 1}
    return {'success': -1 }


def _get_zoomcar_booking_state(book_id):
    book = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book_id)
    return book.first().status

def _mark_zoomcar_booking_accepted(book_id):
    book = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book_id)
    st = book.first()
    if st and st.status == ZoomcarBookings.STATUS_ACCEPTED:
        return False
    book.update({ZoomcarBookings.status: ZoomcarBookings.STATUS_ACCEPTED})
    try:
        db.session.commit()
    except Exception as e:
        print("Could not mark ZC booking accepted in db", str(e))
    return True

def _mark_zoomcar_booking_rejected(book_id):
    book = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book_id)
    st = book.first()
    if st and st.status == ZoomcarBookings.STATUS_REJECTED:
        return False
    book.update({ZoomcarBookings.status: ZoomcarBookings.STATUS_REJECTED})
    try:
        db.session.commit()
    except Exception as e:
        print("Could not mark ZC booking accepted in db", str(e))
    return True

def convert_booking_type_b2c(booking, booking_new_type, lat=-1, lng=-1, loc=None):
    # Should validate new type tbh
    b = booking.first()
    pe = db.session.query(BookPricing).filter(BookPricing.book_id == b.id)
    pending_entry = pe.first()
    if not pending_entry:
        return False
    search = db.session.query(DriverSearch).filter(DriverSearch.id == b.search_key)
    search_entry = search.first()
    if not search_entry:
        return False
    dest_exists = db.session.query(BookDest).filter(BookDest.book_id == b.id).first()
    if not dest_exists:
        if lat < 0 or lng < 0:
            lat = b.lat
            lng = b.lng
            loc = b.loc
        new_dest = BookDest(b.id, lat, lng, loc)
        db.session.add(new_dest)
    end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day, search_entry.time.hour,
                          search_entry.time.minute, search_entry.time.second)
        + timedelta(search_entry.days, search_entry.dur.hour * 3600 + search_entry.dur.minute * 60 + search_entry.dur.second))
    new_price = Price.get_price(booking_new_type, search_entry.dur.hour + search_entry.dur.minute/60, search_entry.time, end_time, search_entry.dist,
                        search_entry.car_type, search_entry.date, end_time.date(), 0,
                         insurance_num=search_entry.insurance_num, city=b.region)
    pe.update({BookPricing.estimate: new_price[9], BookPricing.base_ch: new_price[1], BookPricing.cartype_ch: new_price[5],
                            BookPricing.night_ch: new_price[2], BookPricing.booking_ch: new_price[4],
                            BookPricing.dist_ch: new_price[3], BookPricing.cgst: new_price[7], BookPricing.sgst: new_price[8],
                            BookPricing.est_pre_tax: new_price[6], BookPricing.insurance_ch: new_price[10]})
    search.update({DriverSearch.type: booking_new_type})
    booking.update({Bookings.type: booking_new_type, Bookings.estimate: new_price[9], Bookings.estimate_pre_tax: new_price[6],
                    Bookings.insurance_cost: new_price[10]})
    try:
        db.session.commit()
    except Exception as e:
        print("Conversion failed:", str(e))
        db.session.rollback()
        return False
    return True

def removelastwordifsmall(words):
    words = words[:-1]
    if len(words[-1]) < 3:
        words = removelastwordifsmall(words)
    return words



def process_search_query(search_query, users):
    search_query_filters = []
    words = search_query.split()
    num_words = len(words)

    if all(len(word) < 3 for word in words):
        search_query_filters.append(
            or_(
                func.concat(users.fname, ' ', users.lname).ilike(f'%{search_query}%'),
                func.concat(users.fname, '', users.lname).ilike(f'%{search_query}%')
            )
        )
        return search_query_filters

    if len(words[-1]) < 3:
        if num_words == 1:
            search_query_filters.append(
                or_(
                    func.concat(users.fname, ' ', users.lname).ilike(f'%{search_query}%'),
                    func.concat(users.fname, '', users.lname).ilike(f'%{search_query}%')
                )
            )
        else:
            match_query = ' '.join(removelastwordifsmall(words))
            match_query = match_query.strip()
            if match_query:
                if users is Users:
                    match_condition = text(
                        "MATCH(users.user_fname, users.user_lname) AGAINST (:match_query IN BOOLEAN MODE)"
                    ).bindparams(match_query=f'{match_query}*')
                else:
                    match_condition = text(
                        "MATCH(driver_user.user_fname, driver_user.user_lname) AGAINST (:match_query IN BOOLEAN MODE)"
                    ).bindparams(match_query=f'{match_query}*')

                search_query_filters.append(match_condition)
            search_query_filters.append(
                or_(
                    func.concat(users.fname, ' ', users.lname).ilike(f'%{search_query}%'),
                    func.concat(users.fname, '', users.lname).ilike(f'%{search_query}%')
                )
            )
        return search_query_filters

    search_cleaned = ' '.join(words)

    if users is Users:
        match_condition = text(
            "MATCH(users.user_fname, users.user_lname) AGAINST (:match_query IN BOOLEAN MODE)"
        ).bindparams(match_query=f'{search_cleaned}*')
    else:
        match_condition = text(
            "MATCH(driver_user.user_fname, driver_user.user_lname) AGAINST (:match_query IN BOOLEAN MODE)"
        ).bindparams(match_query=f'{search_cleaned}*')

    search_query_filters.append(match_condition)
    search_query_filters.append(
        or_(
            func.concat(users.fname, ' ', users.lname).ilike(f'%{search_cleaned}%'),
            func.concat(users.fname, '', users.lname).ilike(f'%{search_cleaned}%')
        )
    )

    return search_query_filters


def fetch_affiliate_wallet_logs(
    affiliate_id: int,
    start_date: str = None,
    end_date: str = None,
    trans_types: str = None
) -> dict:
    """
    Fetch affiliate wallet logs based on the given parameters.
    Returns a dictionary with "success" and either "logs" (on success) or "message" (on failure).
    """
    try:
        # Query: Other logs (excluding "Transfer" transactions)
        other_logs = db.session.query(AffiliateWalletLogs).filter(
            (AffiliateWalletLogs.to_account == affiliate_id) | (AffiliateWalletLogs.from_account == affiliate_id),
            AffiliateWalletLogs.method.notin_(["Transfer"])
        )

        # Query: Credit Transactions (incoming money via Transfer)
        credit_logs = db.session.query(AffiliateWalletLogs).filter(
            AffiliateWalletLogs.to_account == affiliate_id,
            AffiliateWalletLogs.method == "Transfer",
            AffiliateWalletLogs.amount > 0
        )

        # Query: Debit Transactions (outgoing money via Transfer)
        debit_logs = db.session.query(AffiliateWalletLogs).filter(
            AffiliateWalletLogs.from_account == affiliate_id,
            AffiliateWalletLogs.method == "Transfer",
            AffiliateWalletLogs.amount < 0
        )

        query = other_logs.union_all(credit_logs).union_all(debit_logs)

        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                query = query.filter(AffiliateWalletLogs.timestamp >= start_date_obj)
            except ValueError:
                return {
                    "success": 0,
                    "message": "Invalid start_date format, use YYYY-MM-DD HH:mm:ss"
                }
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                query = query.filter(AffiliateWalletLogs.timestamp <= end_date_obj)
            except ValueError:
                return {
                    "success": 0,
                    "message": "Invalid end_date format, use YYYY-MM-DD HH:mm:ss"
                }

        if trans_types:
            # Filter by each transaction type provided in a comma-separated string
            query = query.filter(
                or_(*[AffiliateWalletLogs.method.ilike(f"%{trans_type.strip()}%") for trans_type in trans_types.split(",")])
            )

        # Aliases for joining related tables
        affiliate_alias = aliased(Affiliate)
        from_affiliate = aliased(Affiliate)
        to_affiliate = aliased(Affiliate)

        logs = (
            query
            .outerjoin(Users, Users.id == AffiliateWalletLogs.admin)
            .outerjoin(affiliate_alias, affiliate_alias.id == AffiliateWalletLogs.affiliate_id)
            .outerjoin(AffiliateRep, AffiliateRep.id == AffiliateWalletLogs.rep_id)
            .outerjoin(from_affiliate, from_affiliate.id == AffiliateWalletLogs.from_account)
            .outerjoin(to_affiliate, to_affiliate.id == AffiliateWalletLogs.to_account)
            .add_columns(
                func.concat(Users.fname, " ", Users.lname).label("admin_name"),
                affiliate_alias.client_name.label("client_name"),
                AffiliateRep.fullname.label("rep_name"),
                from_affiliate.client_name.label("from_account_name"),
                to_affiliate.client_name.label("to_account_name")
            )
            .order_by(AffiliateWalletLogs.timestamp.desc())
            .all()
        )

        # Serialize results
        log_list = []
        for log in logs:
            wallet_log = log.AffiliateWalletLogs
            log_list.append({
                "transaction_id": wallet_log.id,
                "transaction_status": wallet_log.status,
                "method": wallet_log.method,
                "client_name": log.client_name if log.client_name else "-",
                "rep_name": log.rep_name if log.rep_name else "-",
                "amount": wallet_log.amount,
                "from_account": log.from_account_name if log.from_account_name else "-",
                "to_account": log.to_account_name if log.to_account_name else "-",
                "wallet_before": wallet_log.wallet_before,
                "wallet_after": wallet_log.wallet_after,
                "timestamp": convert_to_ist(wallet_log.timestamp).strftime('%Y-%m-%d %H:%M:%S') if wallet_log.timestamp else None,
                "payment_id": wallet_log.payment_id if wallet_log.payment_id else "-",
                "admin_name": log.admin_name if log.admin_name else "-",
                "source": wallet_log.source,
                "reason": wallet_log.reason,
                "remark": wallet_log.remark
            })

        return {"success": 1, "logs": log_list}

    except SQLAlchemyError as e:
        db.session.rollback()
        return {"success": 0, "message": "Database error", "details": str(e)}
    except Exception as e:
        return {"success": 0, "message": "Unexpected error", "details": str(e)}
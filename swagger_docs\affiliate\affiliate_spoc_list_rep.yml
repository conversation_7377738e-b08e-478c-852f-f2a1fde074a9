tags:
  - Affiliate
summary: Get All SPOCs for an Affiliate Representative
description: >
  Retrieves the list of SPOCs for a specific affiliate representative.
parameters:
  - in: query
    name: rep_id
    type: string
    required: true
    description: The ID of the affiliate representative.
    example: "101"
  - in: query
    name: regions
    type: string
    required: true
    description: >
      Comma-separated region codes that the representative should have access to.
    example: "0,1"
responses:
  200:
    description: SPOC list retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        spocs:
          type: array
          items:
            type: object
            properties:
              spoc_id:
                type: integer
                example: 10
              spoc_name:
                type: string
                example: "<PERSON>"
              spoc_number:
                type: string
                example: "9876543210"
              spoc_type:
                type: string
                example: "Local"
              spoc_region:
                type: string
                example: "0,1"
  400:
    description: Missing required parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Affiliate representative ID is required"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Affiliate representative not found"
  500:
    description: Internal Server Error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Detailed error message"

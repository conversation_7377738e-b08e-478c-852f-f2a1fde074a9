tags:
  - Booking_admin
summary: Get total counts of bookings for the specified date range
description: >
  This endpoint provides the total counts of different booking statuses (e.g., unallocated, allocated, on my way, checked-in, ongoing, etc.) for a specified date range and region, including support for analytics calls.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: The start date for the booking count query (YYYY-MM-DD).
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: The end date for the booking count query (YYYY-MM-DD).
  - name: from_time
    in: formData
    type: string
    format: time
    required: false
    description: The start time for the booking count query (HH:MM:SS). Defaults to "00:00:00" if not provided.
  - name: to_time
    in: formData
    type: string
    format: time
    required: false
    description: The end time for the booking count query (HH:MM:SS). Defaults to "23:59:59" if not provided.
  - name: search_region
    in: formData
    type: string
    required: false
    description: A comma-separated list of region IDs to filter the bookings.
  - name: isglobal
    in: formData
    type: integer
    required: false
    description: A flag indicating if the query is global (0 for no, 1 for yes).
  - name: analytics_call
    in: formData
    type: integer
    required: false
    description: If 0, converts dates to UTC format for analytics purposes.
responses:
  200:
    description: Total counts of bookings based on the query.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        data:
          type: array
          items:
            type: integer
          description: >
            An array containing counts for:
              1. total_unallocated
              2. total_allocated
              3. total_on_my_way
              4. total_checked_in
              5. total_ongoing
              6. total_reached_dest
              7. total_completed
              8. total_customer_cancel_without_alloc
              9. total_customer_cancel_after_alloc
              10. total_admin_cancel
              11. total_customer_cancel
              12. total_driver_cancel
  400:
    description: Incomplete form details or invalid region filtering.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Incomplete Form Details"
        error:
          type: string
          example: "Error in search regions string: No valid regions found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"
        data:
          type: array
          items:
            type: integer
          example: []

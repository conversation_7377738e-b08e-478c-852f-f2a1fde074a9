tags:
  - Customer_admin
summary: Get Registered Customer List
description: >
  This endpoint retrieves a list of registered customers filtered by region, registration timestamp, and other parameters.
  The results can be sorted by various criteria, including registration date and rating. Only accessible by users with 
  appropriate admin privileges.
parameters:
  - name: region
    in: formData
    required: false
    type: string
    description: (Optional) Comma-separated list of region IDs to filter customers by region. Use '-1' for no filtering.
    example: "1,2,3"
  - name: timestamp_gt
    in: formData
    required: false
    type: string
    description: (Optional) Filter customers registered after this timestamp.
    format: date-time
    example: "2024-01-01 00:00:00"
  - name: timestamp_lt
    in: formData
    required: false
    type: string
    description: (Optional) Filter customers registered before this timestamp.
    format: date-time
    example: "2024-12-31 23:59:59"
  - name: is_global
    in: formData
    required: false
    type: string
    description: (Optional) Global search flag, used for broader filtering.
    example: "true"
  - name: label
    in: formData
    required: false
    type: string
    description: (Optional) Filter customers by label. 
    example: "preferred"
  - name: sort_by
    in: formData
    required: false
    type: integer
    description: Sort customers by certain criteria. Defaults to '1' for sorting by registration date.
    enum:
      - 1  # Registration date (desc)
      - 2  # Registration date (asc)
      - 3  # Rating (desc)
      - 4  # Rating (asc)
    example: 1
  - name: sort_order
    in: formData
    required: false
    type: string
    description: Sort order for the sorting criteria, either 'asc' (ascending) or 'desc' (descending). Defaults to 'desc'.
    enum: 
      - "asc"
      - "desc"
    example: "desc"
responses:
  200:
    description: Successfully retrieved the registered customer list
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        lastentry:
          type: string
          description: The latest timestamp of the retrieved customers, useful for pagination
          example: "2024-12-31 23:59:59"
        data:
          type: array
          description: Array of registered customer details
          items:
            type: object
            properties:
              customer_id:
                type: integer
                description: The unique ID of the customer
                example: 101
              mobile:
                type: string
                description: The mobile number of the customer
                example: "9876543210"
              name:
                type: string
                description: The full name of the customer
                example: "John Doe"
              registration_date:
                type: string
                format: date-time
                description: Registration date in IST format
                example: "2024-01-01 10:30:00"
              region:
                type: integer
                description: The region ID of the customer
                example: 1
              remark:
                type: string
                description: Any remarks about the customer
                example: "Frequent customer"
              completed:
                type: integer
                description: The number of completed trips
                example: 5
              upcoming:
                type: integer
                description: The number of upcoming trips
                example: 2
  400:  # Invalid region or value error
    description: The request contains invalid input values such as incorrect region filtering
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Error message explaining the invalid input
          example: "Error in search regions string"
  500:  # Internal server error
    description: Server error or general failure
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: The error message from the server
          example: "Internal server error"
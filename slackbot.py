#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  register_cust.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>


from flask import abort, Blueprint, request, jsonify
import datetime
from sqlalchemy import or_, and_, exc
from sqlalchemy.sql import func, text as tx
from db_config import fb_db
import calendar
from flask import current_app as app
from drivers import _delete_driver_idle, _add_driver_idle
from payments import PaymentType
from _utils import get_safe, strfdelta2, get_dt_ist
from models import Users, Bookings, Trip, Drivers, UserToken, RevvBookings, DriverIdle, DriverPaid
from models import db, C24Bookings, BookDest, DriverSearch, DriverPermaInfo, C24Pic, DriverDetails, MobileChange
from models import DriverInfo
from book_ride import get_best, create_pending_entries
from booking_params import BookingParams, Regions
from c24 import convert_timedelta

slackbot = Blueprint('slackbot', __name__)
ACCOUNTS_CHANNEL = 'CKWL3CE9M'
TEST_CHANNEL = 'GMPBLATCH'


def get_datetime(content):
    if len(content) == 1:
        month = datetime.datetime.utcnow().month
        year = datetime.datetime.utcnow().year
        try:
            date = int(content[0])
            if date > 30:
                raise Exception
        except Exception:
            date = datetime.datetime.utcnow().day
        month = datetime.datetime.utcnow().month
        year = datetime.datetime.utcnow().year
    elif len(content) == 2:
        try:
            date = int(content[0])
            if date > 30:
                raise Exception
        except Exception:
            date = datetime.datetime.utcnow().day
        try:
            month = int(content[1])
            if month > 12:
                raise Exception
        except Exception:
            date = datetime.datetime.utcnow().day
            month = datetime.datetime.utcnow().month
        year = datetime.datetime.utcnow().year
    else:

        try:
            date = int(content[0])
            if date > 30:
                raise Exception
        except Exception:
            date = datetime.datetime.utcnow().day
        try:
            month = int(content[1])
            if month > 12:
                raise Exception
        except Exception:
            date = datetime.datetime.utcnow().day
            month = datetime.datetime.utcnow().month
            year = datetime.datetime.utcnow().year
        year = datetime.datetime.utcnow().year
    return date, month, year


def is_request_valid(form):
    is_token_valid = form['token'] == 'EUaoX6gOODTpCZESyT0Sbj7B'
    is_team_id_valid = form['team_id'] == 'TKJ1YUJKB'

    return is_token_valid and is_team_id_valid

def is_request_legal(form, channels):
    return form['channel_id'] in channels


def add_text(field, value, firstline=False):
    if firstline:
        return '*' + field + ':* ' + value
    return '\n*' + field + ':* ' + value

def _userinfo_text(user_acc):
    if not user_acc:
        text = 'No details found'
        return text
    user_id = user_acc.id
    user_name = user_acc.get_name()
    user_mobile = user_acc.mobile
    text = add_text('#' + str(user_id), user_name, firstline=True)
    text += add_text('Contact', str(user_mobile))
    text += add_text('Region', Regions.to_string(user_acc.region))
    user_type = user_acc.role
    if user_type == Users.ROLE_USER:
        text += add_text('Role', 'Customer')
    elif user_type == Users.ROLE_DRIVER:
        text += add_text('Role', 'Driver')
    elif user_type in Users.ROLE_ADMIN:
        text += add_text('Role', 'Admin')
    elif user_type == Users.ROLE_C24:
        text += add_text('Role', 'Cars24 user')
    elif user_type == Users.ROLE_REVV:
        text += add_text('Role', 'Revv user')
    elif user_type == Users.ROLE_SUPERADMIN:
        text += add_text('Role', 'Superadmin')
    else:
        text += add_text('Role', 'Unknown')
    if user_acc.email:
        text += add_text('Email', user_acc.email)
    else:
        text += add_text('Email', 'N/A')
    if user_type == Users.ROLE_DRIVER:
        driver = db.session.query(Drivers).filter(Drivers.user == user_id).first()
        if not driver:
            text += add_text('Driver id',  'N/A')
        else:
            driver_id = str(driver.id)
            text += add_text('Driver id',  driver_id)
            if driver.perma:
                text += add_text('Status', 'Full-time')
            else:
                text += add_text('Status', 'Part-time')
            if driver.approved == 1 and driver.available:
                text += add_text('Account status', 'Active')
            elif driver.approved < 0:
                text += add_text('Account status', 'Disabled')
            else:
                text += add_text('Account status', 'Active (unavailable)')
            text += add_text('Picture',  app.config['CUR_URL'] + app.config['UPLOAD_FOLDER'] + driver.pic)
            dinfo = db.session.query(DriverInfo).filter(
                        DriverInfo.driver_id == driver.id).first()
            if dinfo:
                if dinfo.license:
                    text += add_text('License No.', dinfo.license)
                    text += add_text('License Exp.', dinfo.license_exp.strftime("%d/%m/%Y"))
                    text += add_text('License Pic Front',  app.config['CUR_URL'] +
                                app.config['UPLOAD_FOLDER'] + dinfo.driver_lic_doc_f)
                    text += add_text('License Pic Back',  app.config['CUR_URL'] +
                                app.config['UPLOAD_FOLDER'] + dinfo.driver_lic_doc_b)
                else:
                    text += add_text('WARNING', 'NEW STYLE LICENSE NOT ADDED')
                    text += add_text('License No.', driver.licenseNo)
                    text += add_text('License Pic.',  app.config['CUR_URL'] + app.config['UPLOAD_FOLDER'] + driver.licenseDoc)
                if dinfo.pres_addr:
                    text += add_text('Present Addr', dinfo.pres_addr)
                if dinfo.id_no:
                    text += add_text('Govt Id', dinfo.id_no)
                    text += add_text('ID Pic Front',  app.config['CUR_URL'] +
                                app.config['UPLOAD_FOLDER'] + dinfo.driver_id_doc_f)
                    text += add_text('ID Pic Back',  app.config['CUR_URL'] +
                                app.config['UPLOAD_FOLDER'] + dinfo.driver_id_doc_b)
                else:
                    text += add_text('*WARNING*', 'ID NO NOT ADDED')
            else:
                text += add_text('WARNING', '*OLD-FORMAT ACCOUNT - COMPLETE REGISTRATION NOT DONE*')
                text += add_text('License No.', driver.licenseNo)
                text += add_text('License Pic.',  app.config['CUR_URL'] + app.config['UPLOAD_FOLDER'] + driver.licenseDoc)
            text += add_text('Rating', str(round(driver.rating, 2)))
            trips = db.session.query(Bookings, Trip).filter(Bookings.id==Trip.book_id).filter(Bookings.driver==driver.id).all()
            text += add_text('Completed trips', str(len(trips)))
            del trips
            if driver.perma:
                perm_info = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == driver.id).first()
                if not perm_info:
                    text += add_text('Permanent details', '*NOT UPDATED*')
                else:
                    text += add_text('Salary', str(perm_info.base))
                    text += add_text('Hours', str(perm_info.hours))
    if user_type != Users.ROLE_DRIVER:
        book_count =  str(db.session.query(Bookings.id).filter(Bookings.user == user_id).count())
        trip_count = str(len(db.session.query(Bookings, Trip).filter(Bookings.user == user_id).filter(Trip.book_id == Bookings.id).all()))
        text += add_text('Bookings',  book_count + ' (' + trip_count + ' successful)')
    last_token = db.session.query(UserToken).filter(UserToken.user_id == user_id).order_by(
                    UserToken.timestamp.desc()).first()
    if last_token:
        text += add_text('Last device', last_token.agent)
    return text


@slackbot.route('/slack/userinfo', methods=['POST', 'GET'])
def find_userinfo():
    if not is_request_valid(request.form):
        abort(400)
    user_id = int(get_safe(request.form, 'text', -1))
    if user_id == -1:
        text = 'User id is malformed'
    else:
        user_acc = db.session.query(Users).filter(Users.id == user_id).first()
        text = _userinfo_text(user_acc)
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/usermobile', methods=['POST', 'GET'])
def find_usermobile():
    if not is_request_valid(request.form):
        abort(400)
    user_mobile = str(get_safe(request.form, 'text', -1))
    if user_mobile == -1:
        text = 'User mobile is malformed'
    else:
        user_acc = db.session.query(Users).filter(Users.mobile == user_mobile).first()
        text = _userinfo_text(user_acc)
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/driverinfo', methods=['POST', 'GET'])
def find_driver():
    if not is_request_valid(request.form):
        abort(400)
    driver_id = int(get_safe(request.form, 'text', -1))
    if driver_id == -1:
        text = 'Driver id is malformed'
    else:
        drv_user = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
        if not drv_user:
            text = 'Driver id is malformed'
        else:
            user_id = drv_user.user
            user_acc = db.session.query(Users).filter(Users.id == user_id).first()
            text = _userinfo_text(user_acc)
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/username', methods=['POST', 'GET'])
def find_username():
    if not is_request_valid(request.form):
        abort(400)
    user_name = str(get_safe(request.form, 'text', -1))
    if user_name == -1:
        text = 'User name is malformed'
    else:
        split_txt = user_name.rsplit(' ', 1)
        fname = split_txt[0]
        if len(split_txt) == 2:
            lname = split_txt[1]
        user_acc = db.session.query(Users).filter(Users.fname.ilike('%' + fname + '%')).all()
        if len(split_txt) == 2:
            user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + lname + '%')).all()
        else:
            user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + fname + '%')).all()

        found = len(user_acc) + len(user_acc_lname)
        text = 'Number of matches found: *' + str(found) + '*\n'
        all_users = user_acc + user_acc_lname
        if found > 10:
            text += 'Showing first 10 only\n'
            all_users = all_users[:10]
        for i in all_users:
            text += '#' + str(i.id) + ': ' + i.get_name() + ' (' + i.mobile + ')\n'
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/drivername', methods=['POST', 'GET'])
def find_drivername():
    if not is_request_valid(request.form):
        abort(400)
    user_name = str(get_safe(request.form, 'text', -1))
    if user_name == -1:
        text = 'Name is malformed'
    else:
        split_txt = user_name.rsplit(' ', 1)
        fname = split_txt[0]
        if len(split_txt) == 2:
            lname = split_txt[1]
        user_acc = db.session.query(Users).filter(Users.fname.like('%' + fname + '%')).filter(Users.role == Users.ROLE_DRIVER).all()
        if len(split_txt) == 2:
            user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + lname + '%')).filter(Users.role == Users.ROLE_DRIVER).all()
        else:
            user_acc_lname = db.session.query(Users).filter(Users.lname.ilike('%' + fname + '%')).filter(Users.role == Users.ROLE_DRIVER).all()

        found = len(user_acc) + len(user_acc_lname)
        text = 'Number of matches found: *' + str(found) + '*\n'
        all_users = user_acc + user_acc_lname
        if found > 10:
            text += 'Showing first 10 only\n'
            all_users = all_users[:10]
        for i in all_users:
            driver_id = db.session.query(Drivers).filter(Drivers.user == i.id)
            if driver_id.first():
                text += '#' + str(driver_id.first().id) + ': ' + i.get_name() + ' (' + i.mobile + ')\n'
    return jsonify(
        response_type='in_channel',
        text=text,
    )


def _get_book_info(book_info):
    text = ""
    book = db.session.query(Bookings).filter(Bookings.id == book_info).first()
    if not book: return 'Invalid booking'
    trip = db.session.query(Trip).filter(Trip.book_id == book_info).first()
    booking_user = db.session.query(Users).filter(Users.id == book.user).first()
    if trip:
        if not trip.endtime:
            status = 'Ongoing'
        else:
            status = 'Completed'
    else:
        if book.valid == Bookings.CANCELLED_USER:
            status = 'Cancelled by User'
        elif book.valid == Bookings.CANCELLED_DRIVER:
            status = 'Cancelled by User'
        elif book.valid == Bookings.CANCELLED_D4M:
            status = 'Cancelled by D4M'
        else:
            if book.valid == Bookings.ALLOCATED:
                status = 'Allocated'
            elif book.valid == Bookings.UNALLOCATED:
                status = 'Unallocated'
            else:
                status = 'Unknown'
    is_b2c = False
    if book.type == BookingParams.TYPE_C24:
        c24_trip = db.session.query(C24Bookings).filter(C24Bookings.ref == book_info).first()
        text = add_text('#' + str(book_info), 'Booked by Cars24', firstline=True)
        text += add_text("Code", "#" + book.code)
        text += add_text('Status', status)
        text += add_text('Appointment id', c24_trip.appt)
        text += add_text('Vehicle details', c24_trip.veh_model + ' (' + c24_trip.veh_no + ')')
        if c24_trip.trip_type == C24Bookings.TRIP_DELIVERY:
            text += add_text('Trip type', 'Home Delivery')
        else:
            text += add_text('Trip type', 'Pickup')
        dest = db.session.query(BookDest).filter(BookDest.book_id == book_info).first()
        text += add_text('Pickup location', book.loc)
        text += add_text('Drop location', dest.name)
        sched_time = get_dt_ist(book.startdate, book.starttime)
        text += add_text('Scheduled time', sched_time.strftime("%d/%m/%Y %I:%M %p"))
    elif book.type == BookingParams.TYPE_REVV:
        text = '#' + str(book_info) + ': Booked by Revv.'
        text += add_text("Code", "#" + book.code)
    elif book.type == BookingParams.TYPE_ZOOMCAR:
        text = '#' + str(book_info) + ': Booked by Zoomcar.'
        text += add_text("Code", "#" + book.code)
    else:
        is_b2c = True
        text = add_text('#' + str(book_info), 'Booked by *' + booking_user.get_name() + '*', firstline=True)
        text += add_text("Code", "#" + book.code)
        text += add_text('Contact', str(booking_user.mobile))
        text += add_text('Status', status)
        search_entry = db.session.query(DriverSearch).filter(DriverSearch.id == book.search_key).first()
        if search_entry:
            car_type = search_entry.car_type
            if car_type % 4 == 0:
                car_type_str = 'Hatchback'
            elif car_type % 4 == 1:
                car_type_str = 'Sedan'
            elif car_type % 4 == 2:
                car_type_str = 'SUV'
            else:
                car_type_str = 'Luxury'
            if car_type > 3:
                car_type_str += ' (Automatic)'
        else:
            car_type_str = 'N/A'
        text += add_text('Car type', car_type_str)
        if book.type == BookingParams.TYPE_ROUNDTRIP:
            text += add_text('Trip type', 'Roundtrip')
        elif book.type == BookingParams.TYPE_ONEWAY:
            text += add_text('Trip type', 'One-way')
        elif book.type == BookingParams.TYPE_OUTSTATION:
            text += add_text('Trip type', 'Outstation')
        elif book.type == BookingParams.TYPE_MINIOS:
            text += add_text('Trip type', 'Mini-Outstation')
        else:
            text += add_text('Trip type', 'Unknown')
        text += add_text('Pickup location', book.loc)
        dest = db.session.query(BookDest).filter(BookDest.book_id == book_info).first()
        if dest:
            text += add_text('Drop location', dest.name)
        if book.payment_type == PaymentType.PAY_CASH:
            payment_type_str = 'Cash'
        elif book.payment_type == PaymentType.PAY_D4M_CREDIT:
            payment_type_str = 'D4M Credit'
        elif book.payment_type == PaymentType.PAY_PAYTM:
            payment_type_str = 'Paytm'
        elif book.payment_type == PaymentType.PAY_BILL:
            payment_type_str = 'Bill'
        else:
            payment_type_str = 'N/A'
        text += add_text('Payment method', payment_type_str)
        text += add_text('Estimate', str(book.estimate))

        sched_time = get_dt_ist(book.startdate, book.starttime)
        text += add_text('Scheduled time', sched_time.strftime("%d/%m/%Y %I:%M %p"))
        if book.days > 0:
            dur_str = str(book.days) + ' days'
        else:
            dur_str = str(book.dur.hour) + ' hours'
        text += add_text('Scheduled duration', dur_str)
        if book.insurance:
            insurance_num = min(1, book.insurance_num)
            ins_str = "Yes (" + str(insurance_num) + " insured)"
        else:
            if book.type >= BookingParams.TYPE_C24:
                ins_str = "Yes"
            else:
                ins_str = "No"
        text += add_text('Insurance', ins_str)
    if book.valid == Bookings.ALLOCATED or not book.driver == BookingParams.BOOKING_DUMMY_ID:
        driver_info = db.session.query(Drivers, Users).filter(Drivers.id == book.driver). \
                                filter(Users.id == Drivers.user).first()
        if not driver_info:
            pass
        else:
            text += add_text('Driver name', driver_info[1].get_name())
            text += add_text('Driver mobile', driver_info[1].mobile)
    if trip:
        starttime_ist = get_dt_ist(trip.starttime.date(), trip.starttime.time())
        text += add_text('Trip started at', starttime_ist.strftime("%d/%m/%Y %I:%M %p"))
        if trip.endtime:
            # Stopped
            stoptime_ist = get_dt_ist(trip.endtime.date(), trip.endtime.time())
            text += add_text('Trip stopped at', stoptime_ist.strftime("%d/%m/%Y %I:%M %p"))
            if is_b2c:
                text += add_text('Trip price', str(round(trip.price, 2)))
                text += add_text('Trip due', str(round(trip.due, 2)))
                duration = str(strfdelta2(trip.endtime - trip.starttime, "{hours}:{minutes}:{seconds}"))
                text += add_text('Trip duration', duration)
                rating = int(book.user_rating)
                star_txt = ':star:'*rating
                text += add_text('Driver rating', star_txt)
                doc_ref = fb_db.collection(u'customer_comments').document(str(book.user))
                try:
                    doc_dict = doc_ref.get().to_dict()
                    if str(book.id) in doc_dict:
                        comment = doc_dict[str(book.id)]['comment']
                    else:
                        comment = ''
                except Exception:
                    comment = ''
                if comment:
                    text += add_text('Customer comment', comment)
        else:
            elapsed_dur = str(strfdelta2(datetime.datetime.utcnow() - trip.starttime, "{hours}:{minutes}:{seconds}"))
            text += add_text('Current duration', elapsed_dur)
    return text


@slackbot.route('/slack/c24book_info', methods=['POST', 'GET'])
def find_c24book():
    if not is_request_valid(request.form):
        abort(400)
    book_info = int(get_safe(request.form, 'text', -1))
    if book_info == -1:
        text = 'Booking id is malformed'
    else:
        book_ref = db.session.query(C24Bookings).filter(C24Bookings.id==book_info).first().ref
        text = _get_book_info(book_ref)
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/revvbook_info', methods=['POST', 'GET'])
def find_revvbook():
    if not is_request_valid(request.form):
        abort(400)
    book_info = int(get_safe(request.form, 'text', -1))
    if book_info == -1:
        text = 'Booking id is malformed'
    else:
        book_ref = db.session.query(RevvBookings).filter(RevvBookings.id==book_info).first().ref
        text = _get_book_info(book_ref)
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/current_trip', methods=['POST', 'GET'])
def find_current_trip():
    if not is_request_valid(request.form):
        abort(400)
    driver_id = int(get_safe(request.form, 'text', -1))
    if driver_id == -1:
        text = 'Booking id is malformed'
    else:
        cur_trip = db.session.query(Bookings, Trip).filter(Bookings.id == Trip.book_id) \
                    .filter(Bookings.driver == driver_id).filter(Trip.endtime == None).order_by(
                    Trip.starttime.desc()).first()
        if cur_trip:
            text = 'Driver is on the following trip: \n'
            text += _get_book_info(cur_trip[0].id)
        else:
            text = 'Driver is currently not on a trip\n'
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/book_info', methods=['POST', 'GET'])
def find_book():
    if not is_request_valid(request.form):
        abort(400)
    book_info = int(get_safe(request.form, 'text', -1))
    if book_info == -1:
        text = 'Booking id is malformed'
    else:
        text = _get_book_info(book_info)
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/book_info_code', methods=['POST', 'GET'])
def find_book_code():
    if not is_request_valid(request.form):
        abort(400)
    book_info_code = (get_safe(request.form, 'text', -1)).upper()
    if len(book_info_code) != 6:
        text = 'Booking code is malformed'
    else:
        book_ref = db.session.query(Bookings). \
            filter(Bookings.code==book_info_code).first()
        if book_ref:
            book_ref = book_ref.id
            text = _get_book_info(book_ref)
        else:
            text = 'Booking code is malformed'
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/bad_trip', methods=['POST', 'GET'])
def find_badtrip():
    if not is_request_valid(request.form):
        abort(400)
    q = db.session.query(Trip).filter(Trip.starttime > Trip.endtime). \
                                filter(Trip.endtime != None).all()
    if len(q) == 0:
        text = "No invalid trips found!"
    else:
        text = "No of invalid trips: *" + str(len(q)) + "*\n"
        for e in q:
            stoptime_ist = get_dt_ist(e.endtime.date(), e.endtime.time())
            starttime_ist = get_dt_ist(e.starttime.date(), e.starttime.time())
            text += add_text("#" + str(e.book_id), "Started at " + starttime_ist.strftime("%d/%m/%Y %I:%M %p") + ", stopped at " + stoptime_ist.strftime("%d/%m/%Y %I:%M %p"))
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/bad_trip_revv', methods=['POST', 'GET'])
def find_badtrip_revv():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    print(len(split_txt))
    if len(split_txt) == 1:
        month = int(split_txt[0])
        if not month:
            month = datetime.datetime.utcnow().month
        year = datetime.datetime.utcnow().year
    else:
        month = int(split_txt[0])
        year = int(split_txt[1])
    q = db.session.query(Trip, Bookings). \
                        filter(Trip.book_id == Bookings.id). \
                        filter(Bookings.type == BookingParams.TYPE_REVV). \
                        filter(func.month(Trip.starttime) == month). \
                        filter(func.year(Trip.starttime) == year). \
                        filter(Trip.endtime != None).all()
    newq = [i for i in q if i[0].endtime - i[0].starttime > datetime.timedelta(seconds=61200) or i[0].endtime - i[0].starttime < datetime.timedelta(seconds=14400)]
    if len(newq) == 0:
        text = "No problematic trips found!"
    else:
        text = "No of problematic trips: *" + str(len(newq)) + "*\n"
        for en in newq:
            e = en[0]
            stoptime_ist = get_dt_ist(e.endtime.date(), e.endtime.time())
            starttime_ist = get_dt_ist(e.starttime.date(), e.starttime.time())
            d_hr, d_min, d_sec = convert_timedelta(stoptime_ist - starttime_ist)
            dur = d_hr + ':' + d_min + ':' + d_sec
            revv_booking = db.session.query(RevvBookings).filter(RevvBookings.ref == e.book_id).first()
            region_str = Regions.to_string(revv_booking.region)
            driver_name = db.session.query(Users, Drivers).filter(Users.id == Drivers.user). \
                                    filter(Drivers.id == en[1].driver).first()[0].get_name()
            text += add_text("#" + str(e.book_id), "Started: " + starttime_ist.strftime("%d/%m/%Y %I:%M %p") + ", stopped: " + stoptime_ist.strftime("%d/%m/%Y %I:%M %p")) + \
                    ", duration: " + dur + ", driver: " + driver_name + ", region: " + region_str
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/drivercash', methods=['POST'])
def get_cash_coll():
    if not (is_request_valid(request.form) and is_request_legal(request.form, [ACCOUNTS_CHANNEL, TEST_CHANNEL])):
       abort(400)
    try:
        split_txt = request.form['text'].split(' ')
        if len(split_txt) == 1:
            driver_id = int(split_txt[0])
            month = datetime.datetime.utcnow().month
            year = datetime.datetime.utcnow().year
        elif len(split_txt) == 2:
            driver_id = int(split_txt[0])
            month = int(split_txt[1])
            year = datetime.datetime.utcnow().year
        else:
            driver_id = int(split_txt[0])
            month = int(split_txt[1])
            year = int(split_txt[2])

        total_amount = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                        filter(func.month(Trip.starttime) == month). \
                        filter(func.year(Trip.starttime) == year). \
                        filter(Bookings.driver == driver_id). \
                        all()
        amt = 0
        earned = 0
        b2c = 0
        revv = 0
        c24 = 0
        guj = 0
        other = 0
        days = []
        for entry in total_amount:
            if entry[1].type == BookingParams.TYPE_C24:
                c24 += 1
            elif entry[1].type == BookingParams.TYPE_REVV:
                revv += 1
            elif entry[1].type < BookingParams.TYPE_C24:
                b2c += 1
            elif entry[1].type < BookingParams.TYPE_GUJRAL:
                guj += 1
            else:
                other += 1
            days.append(get_dt_ist(entry[0].starttime.date(), entry[0].starttime.time()).date())
            amt += entry[0].due
            earned += entry[0].price
        drv_user = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
        if len(total_amount) == 0:
            text = 'No trips for this driver in the given month-year'
        elif not drv_user:
            text = 'Driver id is malformed'
        else:
            user_id = drv_user.user
            user_acc = db.session.query(Users).filter(Users.id == user_id).first()
            text = user_acc.get_name() + "'s details for " + calendar.month_name[month] + " " + str(year) + ":\n"
            text += add_text('Number of trips', str(len(total_amount)))
            text += add_text('Trip division', str(b2c) + ' *B2C*/' + str(c24) + ' *C24*/' + str(revv) + ' *Revv*/' + str(guj) + ' *Gujral*/' + str(other) + ' *Other*')
            text += add_text('Active days', str(len(set(days))))
            all_days = [datetime.date(year, month, day) for day in range(1, calendar.monthrange(year, month)[1]+1)]
            if len(split_txt) == 1:
                set_month = [datetime.date(year, month, day) for day in range(datetime.datetime.utcnow().date().day + 1, calendar.monthrange(year, month)[1]+1)]
            else: set_month = []
            idles = db.session.query(DriverIdle).filter(DriverIdle.driver_id == driver_id).filter(DriverIdle.idle_date > all_days[0]). \
                        filter(DriverIdle.idle_date <= all_days[-1]).all()
            idles = [i.idle_date for i in idles]
            l = sorted(list(set(all_days) - set(set_month) -  set(days) - set(idles)))
            text += add_text('Holidays', str(len(l)) + " " + str([int(u.strftime("%d")) for u in l]))
            text += add_text('Idle days', str(len(idles)) + " " + str([int(u.strftime("%d")) for u in idles]))
            if drv_user.perma == 1:
                perm_info = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == drv_user.id).first()
                if perm_info:
                    text += add_text('TA (approx)','₹' +  str(perm_info.ta * (b2c+c24+other)))
                    sql = """select CEILING(sum(ot)/60) as ot from (select date(addtime(trip_start, '05:30:00')) as dt , greatest(0, timestampdiff(minute, min(addtime(trip_start, '05:30:00')), max(addtime(trip_stop, '05:30:00'))) - :hr) as ot from trip, bookings where book_ref=trip_book and month(addtime(trip_start, '05:30:00'))= :m and year(addtime(trip_start, '05:30:00')) = :y and book_driver = :driver group by dt) a where a.ot > 0"""
                    mins = perm_info.hours * 60
                    if perm_info.hours <= 10:
                        extra = 15
                    else:
                        extra = 15 #perm_info.hours + 5 * (perm_info.hours - 10)
                    ot = db.session.execute(tx(sql), {"m": month,"y":year, "driver":drv_user.id, "hr":mins+extra}).fetchall()
                    if not ot[0]:
                        s_ot = "0"
                        i_ot = 0
                    else:
                        try:
                            s_ot = str(ot[0][0])
                            i_ot = int(ot[0][0])
                        except Exception:
                            s_ot  = "0"
                            i_ot = 0
                    text += add_text('OT hours', s_ot)
                    text += add_text('OT payment', '₹' + str(i_ot * perm_info.ot))
            text += add_text('Total due', '₹' +  str(round(amt, 2)))
            text += add_text('Total earned (sales)', '₹' +  str(round(earned, 2)))
            if drv_user.perma == 1:
                # baaaaaaaaaaad semantics
                active_days = len(set(days + idles))
                earned = i_ot * perm_info.ot + perm_info.ta * (b2c+c24+other) + perm_info.base
                base_earn = earned
                if active_days > 26:
                    earned += perm_info.base / 26 * (active_days - 26)
                if active_days < 26:
                    earned -= perm_info.base / 26 * (26 - active_days)
                text += add_text('Approx salary', '₹' + str(round(base_earn, 2)) + ' (₹' + str(round(earned, 2)) +')')
    except AttributeError as e:
        print(e)
        text = 'Data is malformed'
    return  jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/find_book', methods=['POST', 'GET'])
def get_book_driver():
    if not is_request_valid(request.form):
        abort(400)
    split_txt = request.form['text'].split(' ')
    q = db.session.query(Bookings).filter(Bookings.user == int(split_txt[0]))
    if len(split_txt) == 2:
        try:
            driver_id = int(split_txt[1])
            q = q.filter(Bookings.driver == driver_id)
        except Exception:
            pass
    if len(split_txt) == 3:
        try:
            to_show = int(split_txt[2])
        except Exception:
            to_show = 10
    else:
        to_show = 10
    q = q.order_by(Bookings.startdate.desc())
    count = 0
    res = q.all()
    if not res:
        text = "No entries found\n"
    else:
        text = "Total *" + str(len(res)) + "* found. Showing top " + str(to_show) + " entries\n"
        for entry in res:
            text += add_text(str(count + 1), '#' + str(entry.id) + ' (' + \
                    get_dt_ist(entry.startdate, entry.starttime).strftime("%d/%m/%Y %I:%M %p") + ')')
            count += 1
            if count >= to_show:
                break
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/b2c_stats', methods=['POST', 'GET'])
def b2c_stats():
    if not is_request_valid(request.form):
        abort(400)
    split_txt = request.form['text'].strip().split(' ')
    date, month, year = get_datetime(split_txt)

    dt = datetime.datetime(day=date, year=year, month=month)
    b2c_data = db.session.query(Bookings, Drivers, Users).filter(Bookings.type < BookingParams.TYPE_C24). \
                        filter(Bookings.driver == Drivers.id). \
                        filter(Drivers.user == Users.id).filter(Bookings.startdate == dt).all()
    pr_dt = str(date) + "/" + str(month) + "/" + str(year)
    text = "Number of entries for " + pr_dt + ": *" + str(len(b2c_data)) + "*"
    cancelled = len(list(filter(lambda x: (x[0].valid < 0) , b2c_data)))
    text += add_text("Cancelled trips", str(cancelled))
    unallocated = len(list(filter(lambda x: (x[0].valid == Bookings.UNALLOCATED) , b2c_data)))
    text += add_text("Unallocated trips", str(unallocated))
    allocated = list(filter(lambda x: (x[0].valid == Bookings.ALLOCATED) , b2c_data))
    text += add_text("Allocated / completed trips", str(len(allocated)))
    for entry in allocated:
        book_ref = str(entry[0].id)
        driver_name = str(entry[2].get_name())
        user_name = db.session.query(Users).filter(Users.id == entry[0].user).first().get_name()
        map_trip = db.session.query(Trip).filter(Trip.book_id == entry[0].id).first()
        if not map_trip:
            text += add_text("#" + book_ref, user_name + ", driver: " + driver_name  + " (allocated)")
        else:
            if map_trip.endtime:
                d_hr, d_min, d_sec = convert_timedelta(map_trip.endtime - map_trip.starttime)
                dur = d_hr + ':' + d_min + ':' + d_sec
            else:
                d_hr, d_min, d_sec = convert_timedelta(datetime.datetime.utcnow() - map_trip.starttime)
                dur = d_hr + ':' + d_min + ':' + d_sec
                driver_name += " (ongoing)"
            text += add_text("#" + book_ref, user_name + ", driver: " + driver_name + ", duration: *" + dur + "*")
    return jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/c24_stats', methods=['POST', 'GET'])
def c24_stats():
    if not is_request_valid(request.form):
        abort(400)
    split_txt = request.form['text'].strip().split(' ')
    date, month, year = get_datetime(split_txt)

    dt = datetime.datetime(day=date, year=year, month=month)
    c24_data = db.session.query(Bookings, C24Bookings, Drivers, Users).filter(Bookings.id == C24Bookings.ref). \
                    filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id).filter(Bookings.startdate == dt).all()
    pr_dt = str(date) + "/" + str(month) + "/" + str(year)
    text = "Number of entries for " + pr_dt + ": *" + str(len(c24_data)) + "*"
    cancelled = len(list(filter(lambda x: (x[0].valid < 0) , c24_data)))
    text += add_text("Cancelled trips", str(cancelled))
    unallocated = len(list(filter(lambda x: (x[0].valid == Bookings.UNALLOCATED) , c24_data)))
    text += add_text("Unallocated trips", str(unallocated))
    allocated = list(filter(lambda x: (x[0].valid == Bookings.ALLOCATED) , c24_data))
    text += add_text("Allocated / completed trips", str(len(allocated)))
    for entry in allocated:
        book_ref = str(entry[0].id)
        user_name = str(entry[3].get_name())
        map_trip = db.session.query(Trip).filter(Trip.book_id == entry[0].id).first()
        if not map_trip:
            text += add_text("#" + book_ref, user_name + " (allocated)")
        else:
            if map_trip.endtime:
                d_hr, d_min, d_sec = convert_timedelta(map_trip.endtime - map_trip.starttime)
                dur = d_hr + ':' + d_min + ':' + d_sec
            else:
                d_hr, d_min, d_sec = convert_timedelta(datetime.datetime.utcnow() - map_trip.starttime)
                dur = d_hr + ':' + d_min + ':' + d_sec
                user_name += " (ongoing)"
            try:
                pics = str(len(db.session.query(C24Pic).filter(C24Pic.booking_id == entry[1].id).all()))
            except Exception:
                pics = "0"
            text += add_text("#" + book_ref, user_name + ", duration: *" + dur + "*, pics uploaded: *" + pics + "*")
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/revv_stats', methods=['POST', 'GET'])
def revv_stats():
    if not is_request_valid(request.form):
        abort(400)
    split_txt = request.form['text'].strip().split(' ')
    date, month, year = get_datetime(split_txt)

    dt = datetime.datetime(day=date, year=year, month=month)
    revv_data = db.session.query(Bookings, RevvBookings, Drivers, Users).filter(Bookings.id == RevvBookings.ref). \
                    filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id).filter(Bookings.startdate == dt).all()
    pr_dt = str(date) + "/" + str(month) + "/" + str(year)
    text = "Number of entries for " + pr_dt + ": *" + str(len(revv_data)) + "*"
    cancelled = len(list(filter(lambda x: (x[0].valid < 0) , revv_data)))
    text += add_text("Cancelled trips", str(cancelled))
    unallocated = len(list(filter(lambda x: (x[0].valid == Bookings.UNALLOCATED) , revv_data)))
    text += add_text("Unallocated trips", str(unallocated))
    allocated = list(filter(lambda x: (x[0].valid == Bookings.ALLOCATED) , revv_data))
    text += add_text("Allocated / completed trips", str(len(allocated)))
    for entry in allocated:
        book_ref = str(entry[0].id)
        user_name = str(entry[3].get_name())
        map_trip = db.session.query(Trip).filter(Trip.book_id == entry[0].id).first()
        shift = entry[1].shift
        if shift == RevvBookings.DAY_SHIFT:
            shift_text = "Day"
        else: shift_text = "Night"
        if not map_trip:
            text += add_text("#" + book_ref, user_name + " (allocated),  shift: *" + shift_text + "*")
        else:
            if map_trip.endtime:
                d_hr, d_min, d_sec = convert_timedelta(map_trip.endtime - map_trip.starttime)
                dur = d_hr + ':' + d_min + ':' + d_sec
            else:
                d_hr, d_min, d_sec = convert_timedelta(datetime.datetime.utcnow() - map_trip.starttime)
                dur = d_hr + ':' + d_min + ':' + d_sec
                user_name += " (ongoing)"
            text += add_text("#" + book_ref, user_name + ", duration: *" + dur + "*,  shift: *" + shift_text + "*")
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/allperma', methods=['POST'])
def get_perma_all():
    if not (is_request_valid(request.form) and is_request_legal(request.form, [ACCOUNTS_CHANNEL, TEST_CHANNEL])):
       abort(400)
    try:
        split_txt = request.form['text'].split(' ')
        if len(split_txt) == 0:
            month = datetime.datetime.utcnow().month
            year = datetime.datetime.utcnow().year
        elif len(split_txt) == 2:
            month = int(split_txt[0])
            year = int(split_txt[1])
        else:
            month = int(split_txt[0])
            year = int(split_txt[1])
        perma_drivers = db.session.query(Drivers).filter(Drivers.perma == 1). \
                    filter(Drivers.approved == 1).all()
        text = "*" + calendar.month_name[month] + " " + str(year) + "*\n"
        total_trip = 0
        total_sales = 0
        total_salary = 0
        total_b2c = 0
        total_guj = 0
        total_revv = 0
        total_c24 = 0
        total_other = 0
        for driver in perma_drivers:

            driver_id = driver.id
            total_amount = db.session.query(Trip, Bookings).filter(Trip.book_id == Bookings.id). \
                            filter(func.month(Trip.starttime) == month). \
                            filter(func.year(Trip.starttime) == year). \
                            filter(Bookings.driver == driver_id). \
                            all()
            try:
                amt = 0
                earned = 0
                b2c = 0
                guj = 0
                revv = 0
                c24 = 0
                other = 0
                days = []
                total_trip += len(total_amount)
                for entry in total_amount:
                    if entry[1].type == BookingParams.TYPE_C24:
                        c24 += 1
                        total_c24 += 1
                    elif entry[1].type == BookingParams.TYPE_REVV:
                        revv += 1
                        total_revv += 1
                    elif entry[1].type < BookingParams.TYPE_C24:
                        b2c += 1
                        total_b2c += 1
                    elif entry[1].type < BookingParams.TYPE_GUJRAL:
                        guj += 1
                        total_guj += 1
                    else:
                        other += 1
                        total_other += 1

                    days.append(get_dt_ist(entry[0].starttime.date(), entry[0].starttime.time()).date())

                    amt += entry[0].due
                    earned += entry[0].price
                    total_sales += entry[0].price
                drv_user = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
                if len(total_amount) == 0:
                    pass
                elif not drv_user:
                    pass
                else:
                    user_id = drv_user.user
                    user_acc = db.session.query(Users).filter(Users.id == user_id).first()
                    text += add_text("#" + str(driver_id) + ": " + user_acc.get_name(), str(len(total_amount)) + " trips", firstline=True)
                    perm_info = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == drv_user.id).first()
                    if perm_info:
                        text += ', TA: ₹' +  str(perm_info.ta * (b2c+c24+other))
                        sql = """select CEILING(sum(ot)/60) as ot from (select date(addtime(trip_start, '05:30:00')) as dt , greatest(0, timestampdiff(minute, min(addtime(trip_start, '05:30:00')), max(addtime(trip_stop, '05:30:00'))) - :hr) as ot from trip, bookings where book_ref=trip_book and month(addtime(trip_start, '05:30:00'))= :m and year(addtime(trip_start, '05:30:00')) = :y and book_driver = :driver group by dt) a where a.ot > 0"""
                        mins = perm_info.hours * 60
                        if perm_info.hours <= 10:
                            extra = 15 #perm_info.hours
                        else:
                            extra = 15 #perm_info.hours + 5 * (perm_info.hours - 10)
                        ot = db.session.execute(tx(sql), {"m": month,"y":year, "driver":drv_user.id, "hr":mins+extra}).fetchall()
                        if not ot[0]:
                            s_ot = "0"
                            i_ot = 0
                        else:
                            try:
                                s_ot = str(ot[0][0])
                                i_ot = int(ot[0][0])
                            except Exception:
                                s_ot  = "0"
                                i_ot = 0
                        text += ", " + s_ot + " OT"
                    if drv_user.perma == 1:
                        earn_self = i_ot * perm_info.ot + perm_info.ta * (b2c+c24+other) + perm_info.base
                        base_earn = earn_self
                        if len(set(days)) > 26:
                            earn_self += perm_info.base / 26 * (len(set(days)) - 26)
                        if len(set(days)) < 26:
                            earn_self -= perm_info.base / 26 * (26 - len(set(days)))
                        total_salary += earn_self
                        all_days = [datetime.date(year, month, day) for day in range(1, calendar.monthrange(year, month)[1]+1)]
                        idles = db.session.query(DriverIdle).filter(DriverIdle.driver_id == driver_id).filter(DriverIdle.idle_date > all_days[0]). \
                                    filter(DriverIdle.idle_date <= all_days[-1]).all()
                        idles = [i.idle_date for i in idles]
                        l = sorted(list(set(all_days) - set(days) - set(idles)))
                        text += ', sales:' + '₹' +  str(round(earned, 2)) + ", salary: " + '₹' + str(round(base_earn, 2)) + ' (₹' + str(round(earn_self, 2)) +')'
                        if datetime.datetime.utcnow().year == year and datetime.datetime.utcnow().month == month:
                            date = datetime.datetime.utcnow() + datetime.timedelta(seconds=330*60)
                            new_l = [i for i in l if i.day < date.day]
                        else:
                            new_l = l
                        text += ', holidays: ' + str(len(new_l)) + " " + str([int(u.strftime("%d")) for u in new_l]) + ', remaining days: ' + str(len(l) - len(new_l)) +'\n'
            except Exception as e:
                print(e)
                text += '\n'
                continue

        text += add_text('Trip division', str(total_b2c) + ' *B2C*/' + str(total_c24) + ' *C24*/' + str(total_revv) + ' *Revv*/' + str(total_guj) + ' *Gujral*/' + str(total_other) + ' *Other*')
        text += add_text('Sales and expenses', '₹' + str(round(total_sales, 2)) + ' sales/₹' + str(round(total_salary, 2)) + ' salary/₹' + str(round(total_sales-total_salary, 2)) + ' projected profit')
    except AttributeError as e:
        print(e)
        text = 'Data is malformed'
    return  jsonify(
        response_type='in_channel',
        text=text,
    )

@slackbot.route('/slack/add_idle', methods=['POST', 'GET'])
def add_idle_day():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    date = datetime.datetime.utcnow().date()

    if len(split_txt) == 1:
        driver_id = int(split_txt[0])
    elif len(split_txt) == 2:
        driver_id = int(split_txt[0])
        day = int(split_txt[1])
        date = date.replace(day=day)
    elif len(split_txt) == 3:
        driver_id = int(split_txt[0])
        day = int(split_txt[1])
        month = int(split_txt[2])
        date = date.replace(day=day, month=month)
    else:
        driver_id = int(split_txt[0])
        day = int(split_txt[1])
        month = int(split_txt[2])
        year = int(split_txt[3])
        date = date.replace(day=day, month=month, year=year)
    if driver_id == -1:
        text = 'Driver id is malformed'
    else:
        cur_driver = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Drivers.user == Users.id).first()
        if cur_driver[0].perma:
            _add_driver_idle(cur_driver[0].id, date)
            str_date = str(date.day) + "/" + str(date.month) + "/" + str(date.year) + "."
            text = "Marked driver " + cur_driver[1].get_name() + " as idle on " + str_date
        else:
            text = "Not permanent driver"
    return jsonify(
        response_type='in_channel',
        text=text
    )

@slackbot.route('/slack/del_idle', methods=['POST', 'GET'])
def del_idle_day():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    date = datetime.datetime.utcnow().date()
    if len(split_txt) == 1:
        driver_id = int(split_txt[0])
    elif len(split_txt) == 2:
        driver_id = int(split_txt[0])
        day = int(split_txt[1])
        date = date.replace(day=day)
    elif len(split_txt) == 3:
        driver_id = int(split_txt[0])
        day = int(split_txt[1])
        month = int(split_txt[2])
        date = date.replace(day=day, month=month)
    else:
        driver_id = int(split_txt[0])
        day = int(split_txt[1])
        month = int(split_txt[2])
        year = int(split_txt[3])
        date = date.replace(day=day, month=month, year=year)
    if driver_id == -1:
        text = 'Driver id is malformed'
    else:
        cur_driver = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                            filter(Drivers.user == Users.id).first()
        if cur_driver[0].perma:
            _delete_driver_idle(cur_driver[0].id, date)
            str_date = str(date.day) + "/" + str(date.month) + "/" + str(date.year) + "."
            text = "Marked driver " + cur_driver[1].get_name() + " as not idle on " + str_date
        else:
            text = "Not permanent driver"
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/convert_perma', methods=['POST', 'GET'])
def convert_to_perma():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    base = 12000
    hours = 10
    ta = 25
    ot = 40
    if len(split_txt) == 1:
        try:
            driver_id = int(split_txt[0])
        except Exception:
            return jsonify(
                response_type='in_channel',
                text="Incorrect driver id",
            )
    elif len(split_txt) == 2:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
    elif len(split_txt) == 3:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
        hours = int(split_txt[2])
    elif len(split_txt) == 4:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
        hours = int(split_txt[2])
        ta = int(split_txt[3])
    else:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
        hours = int(split_txt[2])
        ta = int(split_txt[3])
        ot = int(split_txt[4])
    if driver_id <= 0:
        text = 'Driver id is malformed'
    else:
        cur_driver = db.session.query(Drivers).filter(Drivers.id == driver_id)
        if cur_driver.first().perma:
            text = "Already permanent driver"
        else:
            cur_driver.update({Drivers.perma: 1})
            perm = DriverPermaInfo(driver_id, DriverPermaInfo.ALLOC_CUST, base, ta, ot, hours)
            db.session.add(perm)
            try:
                db.session.commit()
                text = "Successfully converted to permanent driver"
            except exc.IntegrityError:
                db.session.rollback()
                text = "Failed to convert!"
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/fix_perma', methods=['POST', 'GET'])
def fix_perma():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    base = 12000
    hours = 10
    ta = 25
    ot = 40
    if len(split_txt) == 1:
        try:
            driver_id = int(split_txt[0])
        except Exception:
            return jsonify(
                response_type='in_channel',
                text="Incorrect driver id",
            )
    elif len(split_txt) == 2:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
    elif len(split_txt) == 3:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
        hours = int(split_txt[2])
    elif len(split_txt) == 4:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
        hours = int(split_txt[2])
        ta = int(split_txt[3])
    else:
        driver_id = int(split_txt[0])
        base = int(split_txt[1])
        hours = int(split_txt[2])
        ta = int(split_txt[3])
        ot = int(split_txt[4])
    if driver_id <= 0:
        text = 'Driver id is malformed'
    else:
        cur_driver = db.session.query(Drivers).filter(Drivers.id == driver_id)
        if not cur_driver.first().perma:
            text = "Not permanent driver"
        else:
            db.session.query(DriverPermaInfo).filter(DriverPermaInfo.driver_id == driver_id).update({
                DriverPermaInfo.base: base, DriverPermaInfo.ta: ta, DriverPermaInfo.ot: ot,
                DriverPermaInfo.hours: hours})
            try:
                db.session.commit()
                text = "Successfully converted to permanent driver"
            except exc.IntegrityError:
                db.session.rollback()
                text = "Failed to convert!"
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/convert_part', methods=['POST', 'GET'])
def convert_to_part():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    try:
        driver_id = int(split_txt[0])
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect driver id",
        )
    if driver_id <= 0:
        text = 'Driver id is malformed'
    else:
        cur_driver = db.session.query(Drivers).filter(Drivers.id == driver_id)
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id)
        if not cur_driver.first().perma:
            text = "Already part-time driver"
        else:
            cur_driver.update({Drivers.perma: 0})
            details.update({DriverDetails.owed: 0})
            try:
                db.session.commit()
                text = "Successfully converted to part-time driver"
            except exc.IntegrityError:
                db.session.rollback()
                text = "Failed to convert!"
    return jsonify(
        response_type='in_channel',
        text=text,
    )

def get_driv(b1):
    ds = DriverSearch.query.filter_by(id=b1.search_key).first()
    drivers = get_best(ds, b1.user, b1.type, True, b1.region)
    create_pending_entries(b1, b1.id, b1.loc, 1, drivers[1], ds, b1.user)


@slackbot.route('/slack/release_to_all', methods=['POST', 'GET'])
def release_to_all():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    try:
        book_id = int(split_txt[0])
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect booking id",
        )
    if book_id <= 0:
        text = 'Booking id is malformed'
    else:
        q = db.session.query(Bookings).filter(Bookings.id==book_id)
        b1 = q.first()
        if b1.valid != Bookings.UNALLOCATED:
            text = 'Booking must be unallocated first'
        else:
            thread = get_driv(b1)
            text = 'Booking released!'
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/ph_change', methods=['POST', 'GET'])
def ph_change():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    try:
        user_id = int(split_txt[0])
        ph_no = split_txt[1]
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect user id",
        )
    if not ph_no.isdigit() or len(ph_no) != 10:
        return jsonify({'success': -1, 'error': 'Invalid phone number format'}), 400
    else:
        ph_no=int(ph_no)
    if user_id <= 0:
        text = 'User id is malformed'
    else:
        q = db.session.query(Users).filter(Users.id==user_id)
        u1 = q.first()
        mob=u1.mobile
        if not u1:
            text = 'User not found'
        else:
            newlog = MobileChange(u1.id, mob, ph_no)
            db.session.add(newlog)
            q.update({Users.mobile: ph_no})
            text = 'Mobile no changed to ' + str(ph_no) + ' for ' + str(u1.get_name())
            db.session.commit()
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/mark_user', methods=['POST', 'GET'])
def mark_user():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    try:
        ph_no = int(split_txt[0])
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect user mobile",
        )
    if len(str(ph_no)) != 10:
        text = 'User mobile is malformed'
    else:
        q = db.session.query(Users).filter(Users.mobile==ph_no)
        u1 = q.first()
        if not u1:
            text = 'User not found'
        else:
            q.update({Users.marked: True})
            text = 'Marked user ' + str(u1.get_name())
            db.session.commit()
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/unmark_user', methods=['POST', 'GET'])
def unmark_user():
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    try:
        ph_no = int(split_txt[0])
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect user mobile",
        )
    if len(str(ph_no)) != 10:
        text = 'User mobile is malformed'
    else:
        q = db.session.query(Users).filter(Users.mobile==ph_no)
        u1 = q.first()
        if not u1:
            text = 'User not found'
        else:
            q.update({Users.marked: False})
            text = 'Unmarked user ' + str(u1.get_name())
            db.session.commit()
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/driver/paid', methods=['POST', 'GET'])
def driver_paid():
    if not is_request_valid(request.form):
        abort(400)
    split_txt = request.form['text'].strip().split(' ')
    try:
        driver_id = int(split_txt[0])
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect driver id",
        )
    dp = db.session.query(DriverPaid).filter(DriverPaid.driver_id == driver_id).all()
    driver_name = db.session.query(Drivers, Users).filter(Users.id == Drivers.user).filter(Drivers.id == driver_id).first()
    if driver_name:
        text = "Number of entries for " + driver_name[1].get_name() + ": *" + str(len(dp)) + "*"
    else:
        return jsonify(
            response_type='in_channel',
            text="Incorrect driver id",
        )

    for u in dp:
        text += add_text(get_dt_ist(u.timestamp.date(), u.timestamp.time()).strftime("%d/%m/%Y %I:%M %p"), '₹' +  str(u.amount))
    return jsonify(
        response_type='in_channel',
        text=text,
    )


@slackbot.route('/slack/user/set_region', methods=['POST', 'GET'])
def set_region():
    q = None
    user_entry = None
    user_mobile_id = -1
    text = ''
    method = ''
    region_str = ''
    if not is_request_valid(request.form):
        abort(400)

    split_txt = request.form['text'].split(' ')
    try:
        user_mobile_id = int(split_txt[0])
        region_str = str(split_txt[1]).capitalize()
    except Exception:
        return jsonify(
            response_type='in_channel',
            text="Incorrect user mobile / id",
        )
    try:
        region_no = Regions.city_to_region_unsafe(region_str)
    except ValueError:
        return jsonify(
            response_type='in_channel',
            text="Invalid region name %s" % region_str,
        )
    if len(str(user_mobile_id)) == 10:
        method = 'mobile'
        user_query = db.session.query(Users).filter(Users.mobile==user_mobile_id)
        user_entry = user_query.first()
    else:
        # Assume this is an id
        method = 'id'
        user_query = db.session.query(Users).filter(Users.mobile==user_mobile_id)
        user_entry = user_query.first()
    if user_entry:
        try:
            user_query.update({Users.region: region_no})
            db.session.commit()
            text = ('Set user id %d to region %s (%d)' %
                    (user_entry.id, region_str, region_no))
        except exc.SQLAlchemyError:
            text = ('Could not update for user id, region %d %s' %
                    (user_entry.id, region_str))
    else:
        text = 'User not found for %s %s' % (method, str(user_mobile_id))
    return jsonify(
        response_type='in_channel',
        text=text,
    )

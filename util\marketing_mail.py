import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from _email import send_mail
from datetime import datetime, timedelta


date_str = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepath = D4M_UTIL_PATH + 'output/dailyregistereduser.csv'
subject = "Drivers4Me Registered Users - "+date_str
content = "Please find Attached."
to_addr_list = ["<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
from_addr = "<EMAIL>"
send_mail(from_addr, to_addr_list, subject, content, filepath)
#"Khus<PERSON>@digitalestate.in","O<PERSON><EMAIL>","<EMAIL>","<EMAIL>"
#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _utils.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import hashlib
import hmac
import time
import re
import os
import uuid
from werkzeug.utils import secure_filename
from datetime import datetime,timedelta
from numpy import base_repr
from random import randint
import _sms
import math
from flask import current_app as app
#from app import s3, redis_client, execute_with_fallback
from redis_config import execute_with_fallback,get_redis_client
import json
import boto3
import pytz
import base64
import io
from botocore.exceptions import ClientError
SITE_URL = 'https://driver4me-data.s3.amazonaws.com/'
USER_SOFT_BANNED_LIST = [68]  # A Ghosh
USER_BANNED_LIST = []  # 534]
import requests
from flask import Blueprint, request, jsonify
from redis_config import execute_with_fallback, is_redis_available

MIN_LATITUDE_WORLD = -90
MAX_LATITUDE_WORLD = 90
MIN_LONGITUDE_WORLD = -180
MAX_LONGITUDE_WORLD = 180

MIN_LATITUDE_INDIA = 6.55
MAX_LATITUDE_INDIA = 37.10
MIN_LONGITUDE_INDIA = 68.10
MAX_LONGITUDE_INDIA = 97.40
EARTH_RADIUS = 6373  # Earth radius in kilometers
MASTER_TOKEN = 'master_token'
MASTER_EMAIL_TOKEN = 'master_email_token'

def read_redis_data(key):
    if is_redis_available():
        data = execute_with_fallback('get', key)
        if data is not None:
            return json.loads(data), True
    return {}, False

def complete(form, req_fields):
    for field in req_fields:
        if field not in form:
            return False
    return True

def get_safe(array, var, default):
    if var not in array:
        return default
    return array[var]

def get_salt():
    return uuid.uuid4().hex


def get_pwd(pwd, salt):
    return hashlib.sha512((pwd + salt).encode('utf-8')).hexdigest()


def validate_fields(mobile, email):
    if not re.search(r"^[6789]\d{9}$", mobile):
        return False
    elif email and not re.search(r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)", email):
        return False
    else:
        return True


def create_driver_details(did):
    from db_config import db
    from models import DriverDetails
    details = DriverDetails(did=did)
    try:
        db.session.add(details)
        db.session.commit()
        return True
    except Exception as e:
        print(e)
        db.session.rollback()
        return False


def strfdelta(tdelta, fmt):
    d = {"days": tdelta.days}
    d["hours"], rem = divmod(tdelta.seconds, 3600)
    d["minutes"], d["seconds"] = divmod(rem, 60)
    return fmt.format(**d)


def strfdelta2(tdelta, fmt):
    d = {"days": tdelta.days}
    d["hours"], rem = divmod(tdelta.seconds, 3600)
    d["hours"] += d["days"]*24
    d["minutes"], d["seconds"] = divmod(rem, 60)
    if d["hours"] < 10:
        d["hours"] = '0' + str(d["hours"])
    if d["minutes"] < 10:
        d["minutes"] = '0' + str(d["minutes"])
    if d["seconds"] < 10:
        d["seconds"] = '0' + str(d["seconds"])
    if d["days"] < 0:
        return "00:00:00"
    return fmt.format(**d)


def get_pic_url(filename):
    try:
        use_s3 = app.config["STATIC_SOURCE_S3"]
    except Exception:
        use_s3 = False
    if  not use_s3:
        return SITE_URL + app.config['UPLOAD_FOLDER'] + filename
    else:
        return "https://storage.drivers4me.com/" + app.config['UPLOAD_FOLDER'] + filename
def get_dt_ist(dt, tm):
    start_time = datetime(year=dt.year, month=dt.month,
                          day=dt.day, hour=tm.hour,
                          minute=tm.minute, second=tm.second)
    start_time_ist = start_time + _sms.IST_OFFSET_TIMEDELTA
    return start_time_ist


def get_ref_code(user_id, mobile):
    # base 36 of 2 digits of mobile + user id (max 99 mil users)
    return base_repr(user_id + int(int(mobile)%100 * 10e6), 36)

def _upload_pic_local(pic, path, extension):
    filename = secure_filename(str(uuid.uuid4()) + '.' + extension)
    full_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], path)
    if path:
        os.makedirs(full_path, exist_ok=True)
    pic.save(os.path.join(full_path, filename))
    return filename

def _upload_pic_s3(pic, path, extension):
    filename = secure_filename(str(uuid.uuid4()) + '.' + extension)
    full_path = os.path.join(app.config['UPLOAD_FOLDER_S3'], path)
    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    s3.upload_fileobj(pic, app.config["FLASKS3_BUCKET_NAME_REAL"], os.path.join(full_path, filename))
    return filename


def upload_log_to_s3(log_str, prefix='logs/'):
    filename = f"{prefix}teardown_log_commit_failed.log"

    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    bucket = app.config["FLASKS3_BUCKET_NAME_REAL"]

    try:
        # Try to get existing log content
        existing_obj = s3.get_object(Bucket=bucket, Key=filename)
        existing_log = existing_obj['Body'].read().decode('utf-8')
    except ClientError as e:
        if e.response['Error']['Code'] == 'NoSuchKey':
            existing_log = ''  # No previous log file exists
        else:
            raise  # Re-raise if other error

    # Append new content
    combined_log = existing_log + log_str + "\n"

    # Upload back to S3 (overwrite)
    s3.put_object(
        Bucket=bucket,
        Key=filename,
        Body=combined_log.encode('utf-8'),
        ContentType='text/plain'
    )
    
def _upload_pic_s3_base64(pic, path, extension):
    filename = secure_filename(str(uuid.uuid4()) + '.' + extension)
    full_path = os.path.join(app.config['UPLOAD_FOLDER_S3'], path)
    session = boto3.Session(
        aws_access_key_id=app.config["AWS_ACCESS_KEY_ID"],
        aws_secret_access_key=app.config["AWS_SECRET_ACCESS_KEY"]
    )
    s3 = session.client("s3")
    s3.upload_fileobj(pic, app.config["FLASKS3_BUCKET_NAME_REAL"], os.path.join(full_path, filename))
    return filename

def upload_pic_base64(pic, path="driver_docs"):
    if isinstance(pic, str):
        header, base64_data = pic.split(',', 1) if ',' in pic else (None, pic)
        extension = header.split('/')[1].split(';')[0] if header else 'png'  # Assuming png if no header
        pic = io.BytesIO(base64.b64decode(base64_data))
        pic.filename = f'upload.{extension}'  # Mock a filename for compatibility
    else:
        extension = os.path.splitext(pic.filename)[1]
    if not pic:
        return False
    try:
        use_s3 = app.config["STATIC_SOURCE_S3"]
    except Exception:
        use_s3 = False
    if not use_s3:
        filename = _upload_pic_local(pic, path, extension)
    else:
        filename = _upload_pic_s3_base64(pic, path, extension)
    return filename

def upload_pic(pic, path=""):
    # pic must exist
    # TO-DO: validate pic
    extension = os.path.splitext(pic.filename)[1]
    if not pic:
        return False
    # and allowed_extension(extension):
    try:
        use_s3 = app.config["STATIC_SOURCE_S3"]
    except Exception:
        use_s3 = False
    if not use_s3:
        filename = _upload_pic_local(pic, path, extension)
    else:
        filename = _upload_pic_s3(pic, path, extension)
    return filename

def is_trip_today(trip):
    today_ist = get_dt_ist(datetime.utcnow().date(), datetime.utcnow().time())
    trip_st_ist = get_dt_ist(trip.starttime.date(), trip.starttime.time())
    return trip_st_ist.date() == today_ist.date()


def is_booking_today(booking):
    booking_dt_ist = get_dt_ist(booking.startdate, booking.starttime)
    today_ist = get_dt_ist(datetime.utcnow().date(), datetime.utcnow().time())
    return booking_dt_ist.date() == today_ist.date()

def gen_otp(n):
    return ''.join(["{}".format(randint(0, 9)) for _ in range(0, n)])

def compute_driver_wallet(driver_details, total_owed):
    driver_withdrawable, driver_wallet = driver_details.withdrawable, driver_details.wallet
    sum_left = driver_wallet + driver_withdrawable - total_owed
    if total_owed == 0:
        return driver_wallet, driver_withdrawable
    if total_owed < 0 and driver_wallet > 0:
        return driver_wallet, driver_withdrawable - total_owed
    if total_owed > 0 and total_owed < driver_wallet:
        return driver_wallet - total_owed, driver_withdrawable
    if sum_left > 0:
        return 0, sum_left
    return sum_left, 0

def convert_to_ist(dt):
    if dt is None:
        return None
    if isinstance(dt, str):  # Check if input is a string
        dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")  # Convert string to datetime
    if dt.tzinfo is None:  # If the datetime is naive
        dt = pytz.utc.localize(dt)  # Assume UTC and localize
    ist = pytz.timezone('Asia/Kolkata')
    return dt.astimezone(ist)

def convert_ist_to_utc(time_str, tz='Asia/Kolkata'):

    if not time_str:
        return None
    local_timezone = pytz.timezone(tz)

    # Use today's date since only time is provided
    today_date = datetime.now().strftime('%Y-%m-%d')
    datetime_str = f"{today_date} {time_str}"

    # Parse the datetime string
    local_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

    # Localize and convert to UTC
    localized_datetime = local_timezone.localize(local_datetime)
    utc_datetime = localized_datetime.astimezone(pytz.utc)

    utc_time = utc_datetime.strftime('%H:%M:%S')

    return utc_time

def convert_utc_to_ist(time_str, tz='Asia/Kolkata'):

    if not time_str:
        return None
    utc_timezone = pytz.utc
    local_timezone = pytz.timezone(tz)

    # Use today's date since only time is provided
    today_date = datetime.now().strftime('%Y-%m-%d')
    datetime_str = f"{today_date} {time_str}"

    # Parse the datetime string
    utc_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

    # Localize and convert to IST
    localized_utc_datetime = utc_timezone.localize(utc_datetime)
    ist_datetime = localized_utc_datetime.astimezone(local_timezone)

    ist_time = ist_datetime.strftime('%H:%M:%S')

    return ist_time

def convert_to_utc(date_str, time_str, tz='Asia/Kolkata'):
    datetime_str = f"{date_str} {time_str}"

    local_timezone = pytz.timezone(tz)

    try:
        local_datetime = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
    except ValueError:
        local_datetime = datetime.strptime(datetime_str, '%d/%m/%Y %H:%M:%S')

    localized_datetime = local_timezone.localize(local_datetime)

    utc_datetime = localized_datetime.astimezone(pytz.utc)

    utc_date = utc_datetime.date().strftime('%Y-%m-%d')
    utc_time = utc_datetime.time().strftime('%H:%M:%S')

    return utc_date, utc_time

def combine_and_convert_to_ist(date_val, time_val):
    combined = datetime.combine(date_val, time_val)
    return convert_to_ist(combined) # timedelta(hours=5, minutes=30)

def split_date_time(dt):
    return dt.date(), dt.time()

# def convert_datetime_to_utc(date_str, tz='Asia/Kolkata'):
#     if not date_str:
#         return None

#     local_dt = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')

#     ist_tz = pytz.timezone(tz)

#     ist_dt = ist_tz.localize(local_dt)

#     utc_dt = ist_dt.astimezone(pytz.utc)
#     return utc_dt


def convert_datetime_to_utc(date_str: str, tz: str = 'Asia/Kolkata'):
    """
    Converts a datetime string (with or without offset) to a UTC datetime object.

    Supports:
    - ISO strings with or without offset (e.g., '2025-06-13T15:30:00+05:30')
    - 12-hour and 24-hour formats
    - Manual fallback to provided timezone if no offset present

    Returns:
        datetime (UTC) or None
    """
    if not date_str:
        return None
    if tz not in pytz.all_timezones:
        raise ValueError(f"Unknown timezone: {tz}")
    # First, try full automatic parsing (handles most offset formats, including +05:30)
    try:
        dt = parser.parse(date_str)
        if dt.tzinfo:
            return dt.astimezone(pytz.utc)
    except Exception:
        pass

    # Manual fallback formats (without offset)
    known_formats = [
        "%Y-%m-%d %H:%M:%S",
        "%d/%m/%Y %H:%M:%S",
        "%d/%m/%Y %I:%M %p",
        "%d/%m/%Y %I:%M %p %z",
    ]

    for fmt in known_formats:
        try:
            dt = datetime.strptime(date_str, fmt)
            if '%z' in fmt:
                return dt.astimezone(pytz.utc)
            else:
                local_tz = pytz.timezone(tz)
                localized = local_tz.localize(dt)
                return localized.astimezone(pytz.utc)
        except Exception:
            continue

    raise ValueError("Date format not recognized or unsupported.")

def convert_datetime_obj_to_utc(date_obj, tz='Asia/Kolkata'):
    if not date_obj:
        return None

    # Ensure input is a datetime object
    if isinstance(date_obj, str):
        date_obj = datetime.strptime(date_obj, '%Y-%m-%d %H:%M:%S')

    # Convert local time to UTC
    local_tz = pytz.timezone(tz)
    localized_dt = local_tz.localize(date_obj)
    utc_dt = localized_dt.astimezone(pytz.utc)

    return utc_dt.replace(tzinfo=None)

def get_trip_duration(start_time: datetime, end_time: datetime) -> str:
    if not start_time or not end_time:
        return ''

    try:
        duration_seconds = abs((end_time - start_time).total_seconds())

        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)

        return f"{hours} hr {minutes} min"
    except ValueError:
        return "N/A"

def is_vip_or_faced_issue(label_value: int) -> bool:
    def check_bit(value, position):
        return (value >> position) & 1

    is_vip = check_bit(label_value, 0) == 1
    faced_issue = check_bit(label_value, 2) == 1

    return is_vip or faced_issue

def is_valid_lat_long_world(lat, lon):
    return MIN_LATITUDE_WORLD <= lat <= MAX_LATITUDE_WORLD and MIN_LONGITUDE_WORLD <= lon <= MAX_LONGITUDE_WORLD

def is_valid_lat_long_for_india(lat, lon):
    return MIN_LATITUDE_INDIA <= lat <= MAX_LATITUDE_INDIA and MIN_LONGITUDE_INDIA <= lon <= MAX_LONGITUDE_INDIA

def get_locality_by_lat_long(lat, long):
    try:
        if not lat or not long:
            return False

        url = f"https://apis.mapmyindia.com/advancedmaps/v1/{app.config['MAP_MY_INDIA']}/rev_geocode"
        params = {
            'lat': lat,
            'lng': long
        }
        response = requests.get(url, params=params)

        if response.status_code == 200:
            data = response.json()
            if 'results' in data and data['results']:
                locality = data['results'][0].get('locality') or data['results'][0].get('village') or data['results'][0].get('subDistrict') or data['results'][0].get('district') or data['results'][0].get('formatted_address')
                locality = locality if locality else ''
                formatted_address = data['results'][0].get('formatted_address') if data['results'][0].get('formatted_address') else ''
                return [locality, formatted_address]
            else:
                return False
        else:
            return False


    except Exception as e:
        return False

def distance_on_earth(lat1, long1, lat2, long2):
    # Convert latitude and longitude to spherical coordinates in radians.
    degrees_to_radians = math.pi / 180.0

    # phi = 90 - latitude
    phi1 = (90.0 - lat1) * degrees_to_radians
    phi2 = (90.0 - lat2) * degrees_to_radians

    # theta = longitude
    theta1 = long1 * degrees_to_radians
    theta2 = long2 * degrees_to_radians

    # Compute spherical distance from spherical coordinates.
    # For two locations in spherical coordinates (1, theta, phi) and (1, theta', phi')
    # cosine(arc length) = sin(phi) * sin(phi') * cos(theta - theta') + cos(phi) * cos(phi')
    cos_value = (math.sin(phi1) * math.sin(phi2) * math.cos(theta1 - theta2) +
                 math.cos(phi1) * math.cos(phi2))

    # Clamp the cos_value to the valid range of [-1, 1] to avoid math domain errors
    cos_value = max(-1, min(1, cos_value))

    # Calculate the arc length
    arc = math.acos(cos_value)

    # Multiply the arc by the radius of the Earth to get the distance
    return arc * EARTH_RADIUS

def get_city_by_lat_long(lat, long):
    try:
        if not lat or not long:
            return {"success":False}
        url = f"https://maps.googleapis.com/maps/api/geocode/json?latlng={lat},{long}&key={app.config['MAPS_DIST_API_KEY']}"

        params = {
            'lat': lat,
            'lng': long
        }
        response = requests.get(url, params=params)

        if response.status_code != 200:
            return {"success":False}
        data = response.json()

        if 'results' not in data or not data['results']:
            return {"success":False}
        # Extract address components
        for component in data['results'][0]['address_components']:
            if "locality" in component['types']:
                locality = component['long_name']
                break
        else:
            # Fallback if locality is not found
            locality = None

        # Get the formatted address
        formatted_address = data['results'][0].get('formatted_address', '')

        return {"success":True,"city": locality, "formatted_address": formatted_address}
    except Exception as e:
        print(e)
        return {"success":False}


def convert_timedelta(duration):
    try:
        days, seconds = duration.days, duration.seconds
    except AttributeError:
        days = 0
        seconds = duration.seconds
    hours = days * 24 + seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = (seconds % 60)
    if hours < 10:
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    if seconds < 10:
        seconds = '0' + str(seconds)
    return str(hours), str(minutes), str(seconds)

def to_camel_case(s: str) -> str:
    """
    Turn any string with spaces, underscores or hyphens
    into camelCase.
    If already camelCase, return it as-is.
    E.g. 'Business Category' -> 'businessCategory'
    """
    s = s.strip()
    
    # Check if already camelCase
    if (
        s and 
        re.match(r'^[a-z][a-zA-Z0-9]*$', s) and 
        not re.search(r'[\s_-]', s)
    ):
        return s

    parts = re.split(r'[\s_-]+', s)
    if not parts:
        return ''
    head, *tail = parts
    return head.lower() + ''.join(w.capitalize() for w in tail)

## function to convert IST time to GMT for database filtering
# print(ist_to_gmt("2025-05-08 00:00:00")) -----> Output: "2025-05-07 18:30:00"
def ist_to_gmt(ist_time: str) -> str:
    dt = pytz.timezone("Asia/Kolkata").localize(datetime.strptime(ist_time, "%Y-%m-%d %H:%M:%S"))
    return dt.astimezone(pytz.timezone("GMT")).strftime("%Y-%m-%d %H:%M:%S")

#convert timestamp to ist string
def convert_utc_to_ist_str(utc_dt):
    if not utc_dt:
        return None
    
    # Ensure utc_dt is timezone-aware in UTC
    if utc_dt.tzinfo is None:
        utc = pytz.timezone('UTC')
        utc_dt = utc.localize(utc_dt)
    
    # Convert to IST
    ist = pytz.timezone('Asia/Kolkata')
    ist_dt = utc_dt.astimezone(ist)

    # Format the datetime string
    return ist_dt.strftime('%d/%m/%Y %I:%M %p')

def create_token(phone_number: str, secret_key: str = None) -> str:
    if not secret_key:
        # Generate a random secret key if none is provided
        secret_key = os.urandom(32).hex()
    
    # Add a timestamp (current time in seconds since epoch)
    timestamp = str(int(time.time()))
    
    # Combine phone number and timestamp
    data = f"{phone_number}:{timestamp}"
    
    # Create an HMAC object
    hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
    token = hmac_obj.hexdigest()
    
    return f"{token}:{secret_key}:{timestamp}"
def verify_token(phone_number: str, token: str, expiration_time: int = 300) -> bool:
    try:
        if token == MASTER_TOKEN:
            return True
        # Split the token to get the hash, secret key, and timestamp
        token_hash, secret_key, timestamp = token.split(":")
        
        # Check if the token has expired
        current_time = int(time.time())
        token_time = int(timestamp)
        if current_time - token_time > expiration_time:
            return False  # Token expired
        
        # Recreate the HMAC object with the same key, phone number, and timestamp
        data = f"{phone_number}:{timestamp}"
        hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
        expected_token = hmac_obj.hexdigest()
        
        return hmac.compare_digest(token_hash, expected_token)
    except ValueError:
        # Token does not contain valid hash, secret key, and timestamp
        return False    
class TokenValidity:
    VALID_TOKEN = 1
    EXPIRED_TOKEN = -1
    INVALID_TOKEN = -2
def create_email_token(email: str, secret_key: str = None) -> str:
    """
    Creates a token using the user's email and the current timestamp.
    The token format is: <hash>:<secret_key>:<timestamp>
    """
    if not secret_key:
        # Generate a random secret key if none is provided
        secret_key = os.urandom(32).hex()
    
    # Get the current timestamp (seconds since epoch)
    timestamp = str(int(time.time()))
    
    # Combine email and timestamp
    data = f"{email}:{timestamp}"
    
    # Create an HMAC hash using SHA256
    hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
    token_hash = hmac_obj.hexdigest()
    
    # Return the token string containing the hash, secret key, and timestamp
    return f"{token_hash}:{secret_key}:{timestamp}"
def verify_email_token(email: str, token: str, expiration_time: int = 300) -> str:
    """
    Verifies the token by:
      - Extracting the hash, secret key, and timestamp from the token.
      - Checking that the token has not expired.
      - Recomputing the HMAC and comparing it with the token hash.
    
    Returns one of:
      - TokenValidity.VALID_TOKEN
      - TokenValidity.EXPIRED_TOKEN
      - TokenValidity.INVALID_TOKEN
    """
    try:
        if token == MASTER_EMAIL_TOKEN:
            return TokenValidity.VALID_TOKEN
        # The token is expected in the format: <hash>:<secret_key>:<timestamp>
        token_hash, secret_key, timestamp = token.split(":")
        
        # Check if the token has expired
        current_time = int(time.time())
        token_time = int(timestamp)
        if current_time - token_time > expiration_time:
            return TokenValidity.EXPIRED_TOKEN
        
        # Recreate the HMAC using the email and timestamp
        data = f"{email}:{timestamp}"
        hmac_obj = hmac.new(secret_key.encode(), data.encode(), hashlib.sha256)
        expected_hash = hmac_obj.hexdigest()
        
        # Compare the computed hash with the one in the token securely
        if not hmac.compare_digest(token_hash, expected_hash):
            return TokenValidity.INVALID_TOKEN
        
        return TokenValidity.VALID_TOKEN
    except ValueError:
        # Token does not contain valid parts
        return TokenValidity.INVALID_TOKEN  
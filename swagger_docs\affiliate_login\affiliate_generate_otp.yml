tags:
  - Affiliate_Login
summary: Generate OTP for Affiliate Login
description: >
  This endpoint generates an OTP and sends it to the mobile number of the affiliate representative for login purposes.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the affiliate representative.
responses:
  200:
    description: OTP sent successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        msg:
          type: string
          description: Success message.
          example: "OTP sent successfully"
  400:
    description: Missing required fields.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Missing required fields"
  404:
    description: Invalid mobile number.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for invalid mobile number).
          example: -1
        msg:
          type: string
          description: Error message.
          example: "Invalid mobile number"
  500:
    description: Database or internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for database error, -3 for other failures).
          example: -2
        msg:
          type: string
          description: Error message.
          example: "Database error"
        error:
          type: string
          description: Detailed error information.
          example: "Error details here"

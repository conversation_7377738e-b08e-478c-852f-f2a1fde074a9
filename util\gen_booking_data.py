from main import app
import sys
sys.path.append("/app/")
import datetime, os
from sqlalchemy.sql import func
import pandas as pd
from _utils_booking import booking_has_trip, get_car_type
from _utils import get_dt_ist
from booking_params import Regions, BookingParams
from models import db, Bookings, Users, DriverSearch, Trip, Drivers


def get_booking_data(dt):
    # first get all booking ids for bookings today
    booked_today = [entry[0] for entry in db.session.query(Bookings, DriverSearch).filter(DriverSearch.id == Bookings.search_key). \
                filter(func.date(func.addtime(DriverSearch.timestamp, '05:30:00')) == dt).filter(Bookings.type < 50). \
                filter(Bookings.insurance == True).all()]
    # cancelled
    cancelled_today = db.session.query(Bookings).filter(Bookings.valid < 0). \
                filter(func.date(func.addtime(Bookings.cancelled_dt, '05:30:00')) == dt).filter(Bookings.type < 50). \
                filter(Bookings.insurance == True).all()

    all_trips = booked_today + cancelled_today
    book_ids = []
    book_info = []
    for booking in all_trips:
        if booking.id in book_ids:
            break
        user_info = db.session.query(Users).filter(Users.id == booking.user).first()
        driver_info = db.session.query(Drivers, Users).filter(Drivers.id == booking.driver).filter(Drivers.user == Users.id).first()
        if booking_has_trip(booking.id):
            trip_info = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
        else:
            trip_info = None
        this_trip = {}
        this_trip["book_code"] = booking.code
        this_trip["state"] = Regions.REGN_STATE[booking.region]
        this_trip["city"] = Regions.REGN_NAME[booking.region]
        this_trip["customer_name"] = user_info.get_name()
        this_trip["customer_mob"] = user_info.mobile
        this_trip["customer_email"] = user_info.email
        if driver_info[0].id == 1:
            this_trip["driver_name"] = ""
            this_trip["driver_mob"] = ""
            this_trip["driver_dl"] = ""
            this_trip["status"] = "unallocated"
        else:
            this_trip["driver_name"] = driver_info[1].get_name()
            this_trip["driver_mob"] = driver_info[1].mobile
            this_trip["driver_dl"] = driver_info[0].licenseNo
            this_trip["status"] = "allocated"
        if booking.valid < 0:
            this_trip["status"] = "cancelled"
        this_trip["trip_start_planned"] = get_dt_ist(booking.startdate, booking.starttime)
        this_trip["trip_end_planned"] = get_dt_ist(booking.enddate, booking.endtime)
        if trip_info is not None:
            this_trip["status"] = "started"
            this_trip["trip_start_actual"] = get_dt_ist(trip_info.starttime.date(), trip_info.starttime.time())
        else:
            this_trip["trip_start_actual"] = ""
        if trip_info is not None and trip_info.endtime is not None:
            this_trip["status"] = "completed"
            this_trip["trip_end_actual"] = get_dt_ist(trip_info.endtime.date(), trip_info.endtime.time())
        else:
            this_trip["trip_end_actual"] = ""
        this_trip["trip_type"] = BookingParams.ALL_CUST_TYPES_ACKO[booking.type-1]
        car_type_raw = get_car_type(booking.id)
        if car_type_raw > 3:
            this_trip["control"] = "auto"
        else:
            this_trip["control"] = "manual"
        car_type_raw = car_type_raw % 4
        if car_type_raw == 0:
            this_trip["car_type"] = "hatchback"
        elif car_type_raw == 1:
            this_trip["car_type"] = "sedan"
        elif car_type_raw == 2:
            this_trip["car_type"] = "suv"
        elif car_type_raw == 3:
            this_trip["car_type"] = "luxury"
        book_ids.append(booking.id)
        book_info.append(this_trip)
    return book_info


if __name__ == '__main__':
    with app.app_context():
        today = datetime.datetime.now().date()
        data = get_booking_data(today)
        df = pd.DataFrame(data)
        if len(sys.argv) < 2:
            file_name = str(today) + '-bookings.csv'
        else:
            file_name = sys.argv[1]
        df.to_csv(os.path.join('output', file_name), index=False)

tags:
  - Utilities_admin
summary: Edit Admin Access
description: >
  This endpoint allows for editing the access and details of an admin user.
  It updates user information and records changes to admin access logs.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The ID of the admin user whose access is being edited.
    example: "admin_12345"
  - name: mobile
    in: formData
    required: false
    type: string
    description: The mobile number of the admin user.
    example: "+919876543210"
  - name: regions
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of region IDs for access. Use '-1' to remove all region access.
    example: "1,2,3"
  - name: tab_access
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of tab access IDs. Use '-1' to remove all tab access.
    example: "0,1,2"
  - name: notifications
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of notification access IDs. Use '-1' to remove all notifications.
    example: "0,1"
  - name: role
    in: formData
    required: false
    type: integer
    description: The role ID to be assigned to the admin user.
    example: 2
  - name: fname
    in: formData
    required: false
    type: string
    description: The first name of the admin user.
    example: "<PERSON>"
  - name: lname
    in: formData
    required: false
    type: string
    description: The last name of the admin user.
    example: "Doe"
  - name: calling_no
    in: formData
    required: false
    type: string
    description: The calling number of the admin user.
    example: "+911234567890"
  - name: agent_id
    in: formData
    required: false
    type: string
    description: The agent ID associated with the admin user.
    example: "agent_456"
  - name: email
    in: formData
    required: false
    type: string
    description: The email address of the admin user.
    example: "<EMAIL>"
responses:
  200:
    description: Successfully updated admin access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        message:
          type: string
          description: Confirmation message.
          example: "Admin access updated and logged successfully"
  404:
    description: User or admin access not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: 0
        message:
          type: string
          description: Error message detailing the issue.
          example: "User not found"
  500:  # Internal server error
    description: An error occurred while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: 0
        message:
          type: string
          description: Error message detailing the issue.
          example: "An error occurred while updating admin access"

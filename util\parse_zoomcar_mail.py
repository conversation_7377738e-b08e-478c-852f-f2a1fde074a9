import os, sys
sys.path.append("/app/")
import uuid
from _email import get_mailbox
from create_zoomcar_booking import create_zoomcar_bookings

if __name__ == '__main__':
    mb = get_mailbox("<EMAIL>")
    inb = mb.inbox_folder()
    arc = mb.archive_folder()
    count = 0
    for message in inb.get_messages(download_attachments=True):
        # Disable check for only ZC mails for now. Uncomment to enable.
        #if message.sender.address != "<EMAIL>":
        #    continue
        # fetch attachment
        fname = str(uuid.uuid4())
        try:
            count += 1
            for attc in message.attachments:
                if attc.name.endswith(".xlsx"):
                    ext = ".xlsx"
                    excel = True
                else:
                    excel = False
                    ext = ".csv"
                attc.save(custom_name=fname + ext, location="/app/util/zoomcsv/")
                create_zoomcar_bookings("/app/util/zoomcsv/"
                                        + fname + ext, excel)
        except Exception as e:
            print("Exception while creating bookings %s" % str(e))
            print("COULD NOT CREATE ANY BOOKINGS")
            continue
        fpath = os.path.join("/app/util/zoomcsv", fname + ext)
        print("Nuking file", fpath)
        try:
            os.remove(fpath)
        except Exception as exc:
            print("Nuke failed, ex:", str(exc))
        message.move(arc)
        if len(message.attachments) == 0:
            print("Moved", message.subject, "with no attachments to archives")
        else:
            print("Moved", message.subject, "with", len(message.attachments), "attachments to archives")

    print("Processed", count, "emails")
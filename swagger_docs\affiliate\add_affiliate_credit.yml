tags:
  - Affiliate
summary: Add or Deduct Affiliate Credit
description: >
  This endpoint allows an admin to add or deduct credits from an affiliate account.
  For credit types "Fine" or "Credit Note", the 'credit_type_reason' field is mandatory.
parameters:
  - in: body
    name: body
    required: true
    description: JSON payload containing the affiliate credit details.
    schema:
      type: object
      properties:
        affiliate_id:
          type: integer
          description: The ID of the affiliate.
          example: 123
        regions:
          type: string
          description: comma seperated regions.
          example: "0,1"
        amount:
          type: number
          format: float
          description: The amount to add or deduct. This should be provided as a float value.
          example: 40.0
        remark:
          type: string
          description: A remark for the credit transaction.
          example: "Transaction remark"
        credit_type:
          type: string
          description: >
            The type of credit transaction. Allowed values: 'Credit Note', 'Fine', 'Add', 'Deduct', 'Transfer'.
          enum:
            - Credit Note
            - Fine
            - Add
            - Deduct
            - Transfer
          example: "Credit Note"
        trans_id:
          type: string
          description: "(Optional) The transaction ID."
          example: "abc123"
        credit_type_reason:
          type: string
          description: >
            The reason for the credit type.
            Mandatory if credit_type is 'Fine' or 'Credit Note'.
          example: "Fuel Theft"
        gst_status:
          type: string
          description: >
            GST status of the transaction. Allowed values: 'Included' or 'Excluded'.
          enum:
            - Included
            - Excluded
          example: "Included"
        gst_number:
          type: string
          description: "(Optional) GST number for verification."
          example: "29ABCDE1234F2Z5"
        tds_enabled:
          type: string
          description: "(Optional) Flag to indicate if TDS is enabled. Use '1' for enabled."
          example: "1"
        transfer_affiliate:
          type: integer
          description: >
            (Optional) Affiliate ID to which credits should be transferred when credit_type is 'Transfer'.
          example: 456
responses:
  '200':
    description: Affiliate credit successfully processed.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: 1
            message:
              type: string
              example: "Affiliate credits successfully added to the account."
  '400':
    description: Missing or invalid required fields.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -2
            message:
              type: string
              example: "Enter all the required fields"
  '404':
    description: Affiliate not found or related entity not found.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: 0
            message:
              type: string
              example: "Affiliate not found"
  '500':
    description: Server error while processing the affiliate credit.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: "Failed to process affiliate credit"
            error:
              type: string
              example: "Detailed error message"

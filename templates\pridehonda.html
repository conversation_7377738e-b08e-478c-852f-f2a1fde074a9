<!DOCTYPE html>
<html>

<head>
    <title>Drivers4Me | PrideHonda Booking Console</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
    <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='bootstrap.css') }}">
    <!--Required for glyphicons-->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/DateTimePicker/bootstrap-datetimepicker.min.css') }}">
    <!-- fa icons -->
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/font-awesome/css/font-awesome.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/pridehonda.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/operations-common.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/custom-elements.css') }}">

    <script>
        window.jQuery || document.write('<script src="{{ url_for('static', filename='jquery.js') }}">\x3C/script>');
    </script>
    <script src="{{ url_for('static', filename='assets/DateTimePicker/moment.js') }}"></script>     <!-- for datetimepicker -->
    <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
    <!-- for datetimepicker-->
    <script src="{{ url_for('static', filename='assets/DateTimePicker/bootstrap-datetimepicker.min.js') }}"></script>
    <!--Vertical text clamping-->
    <script src="{{ url_for('static', filename='clamp.js') }}"></script>

    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>

    <!-- Add Firebase products that you want to use -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
    
    <script src="{{ url_for('static', filename='assets/js/operations-common.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/utility.js') }}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo&region=Dharmatala,Kolkata,West+Bengal,India&libraries=places&libraries=places"></script>
    <script src="{{ url_for('static', filename='assets/js/mapHelper.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip-utils/0.1.0/jszip-utils.min.js" integrity="sha512-3WaCYjK/lQuL0dVIRt1thLXr84Z/4Yppka6u40yEJT1QulYm9pCxguF6r8V84ndP5K03koI9hV1+zo/bUbgMtA==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js" integrity="sha512-XMVd28F1oH/O71fzwBnV7HucLxVwtxf26XV8P4wPk26EDxuGZ91N8bsOttmnomcCD3CS5ZMRL50H0GgOHvegtg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.0/FileSaver.min.js" integrity="sha512-csNcFYJniKjJxRWRV1R7fvnXrycHP6qDR21mgz1ZP55xY5d+aHLfo9/FcGDQLfn2IfngbAHd8LdfsagcCqgTcQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="{{ url_for('static', filename='assets/js/pridehonda-bookings.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/pridehonda.js') }}"></script>
</head>

<body style="background-color: var(--d4m-body-primary);">
<nav id="topMenu" class="navbar navbar-default navbar-fixed-top" style="margin-bottom: 0;">
    <a id="brandName" class="navbar-brand" href="/" style="padding-left: 15px!important; padding-top: 0!important;">
        <img src="{{ url_for('static',filename='assets/images/pridehonda_collab.svg') }}" class="img-rounded" alt="PrideHonda + Drivers4Me" style="height: 100px;">
    </a>
    <button id="collapsedMainMenu" type="button" class="navbar-toggle" data-toggle="collapse" data-target="">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
    </button>
    <div class="container-fluid collapse navbar-collapse" id="topBar">
        <button id="logout" class="btn navbar-btn d4m-primary-button navbar-right">
            Logout&emsp;<span class="glyphicon glyphicon-off"></span>
        </button>
        <ul id="navComponents" class="nav navbar-nav nav-components navbar-right">
            <!--<li class="nav-tab active" id="home"><a href="/pridehonda">Home</a></li>-->
            <li class="nav-tab active" id="newBooking"><a href="#newBooking">New Booking</a></li>
            <li class="nav-tab inactive" id="bookings"><a href="#bookingList">Bookings</a></li>
            <li class="nav-tab inactive" id="newSpoc"><a href="#newSpoc">New SPOC</a></li>
        </ul>
    </div>
</nav>

<!--<div id="homePanel" class="row container-fluid function-panel">

</div>-->
<div id="newBookingPanel" class="row container-fluid function-panel collapse">

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-holder">
        <h3 class="standard-header">New Booking</h3><hr class="d4m-separator">
        <form id="newBookingForm" enctype="multipart/form-data" autocomplete="off">
            <!--Booking ID and Trip Type-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>PrideHonda ID</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input name="text" type="text" class="form-control" id="clientBookingId" placeholder="PrideHonda ID" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Trip Type</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <div>
                                <select class="form-control styled-select" id="tripTypeSelect">
                                    <!--option value="2">Drop-Off & Pickup</!-->
                                    <option value="0">Drop-Off</option>
                                    <option value="1">Pickup</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--Model Name and Vehicle Registration Number-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Vehicle Model</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input name="text" type="text" class="form-control" id="vehicleModel" placeholder="Vehicle Model" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Vehicle Registration No.</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="text" type="text" class="form-control" id="vehicleRegistrationNumber" placeholder="Vehicle Registration Number" />
                        </div>
                    </div>
                </div>
            </div>
            <!--Transmission Type and Region-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Transmission Type</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <div>
                                <select class="form-control styled-select" id="transmissionTypeSelect">
                                    <option value="0">Manual</option>
                                    <option value="3">Automatic</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Region</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <select style="width: 100%" id="region">
                              <option value="1">Hyderabad</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <!--Addresses-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Source Address</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input id="pickupSearch" list="p_hubs" class="form-control" type="text" placeholder="Source address">
                              <datalist id="p_hubs">
                                <option value="PrideHonda - Gachibowli">
                              </datalist>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Destination Address</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input id="dropSearch" list="d_hubs" class="form-control" type="text" placeholder="Destination address">
                            <datalist id="d_hubs">
                                <option value="PrideHonda - Gachibowli">
                             </datalist>

                        </div>
                    </div>
                </div>
            </div>
            <!--SPOC Details-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>SPOC Name</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <select class="form-control styled-select" id="pickupNameSelect">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>SPOC Contact</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="mobile" type="mobile" class="form-control" id="pickupMobile" value="" maxlength="10" disabled/>
                        </div>
                    </div>
                </div>

            </div>
            <!--Customer Name-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Customer Name</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input name="dropName" type="text" class="form-control" id="dropName" placeholder="Customer Name" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Customer Contact</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="mobile" type="mobile" class="form-control" id="dropMobile" placeholder="Customer Contact Number" maxlength="10" onkeydown="return restrictToNumericInput(event)"/>
                        </div>
                    </div>
                </div>
                <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Destination SPOC Contact</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="mobile" type="mobile" class="form-control" id="dropMobile" placeholder="Destination Contact Number" maxlength="10" onkeydown="return restrictToNumericInput(event)"/>
                        </div>
                    </div>
                </div>-->
            </div>
            <!--Scheduled delivery datetime-->
            <div id="DeliveryTimeInputSection" class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Delivery Date (DD/MM/YYYY)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_delivery_date'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Delivery Time (HH:MM)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_delivery_time'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Clock.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
            </div>
            <div id="PickupTimeInputSection" class="row small-bottom-padding collapse">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Pickup Date (DD/MM/YYYY)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_pickup_date'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Pickup Time (HH:MM)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_pickup_time'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Clock.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
            </div>
            <!--Button-->
            <div class="row small-bottom-padding">
                <div class=" col-lg-12 col-md-12 col-sm-12 col-xs-12 center-elements form_element">
                    <button id="book" type="button" class="btn btn-lg d4m-primary-button">Book Now</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="bookingsPanel" class="row container-fluid function-panel collapse">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">
        <nav id="bookingMenu" class="navbar navbar-default d4m-nav">
            <button id="collapsedBookingMenu" type="button" class="navbar-toggle" data-toggle="collapse" data-target="">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <div class="container-fluid collapse navbar-collapse" id="bookingTypeBar">
                <ul class="nav navbar-nav">
                    <li class="book-tab inactive" id="new"><a href="#newBookings" role="button">New</a></li>
                    <li class="book-tab inactive" id="upcoming"><a href="#upcomingBookings" role="button">Upcoming</a></li>
                    <li class="book-tab inactive" id="ongoing"><a href="#ongoingBookings" role="button">Ongoing</a></li>
                    <li class="book-tab inactive" id="completed"><a href="#completedBookings" role="button">Completed</a></li>
                    <li class="dropdown book-tab">
                        <a class="dropdown-toggle" data-toggle="dropdown" role="button">Cancelled
                            <span class="dropdown-choice-info trollFont collapse"></span><span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li class="book-tab inactive" id="cancel-d4m"><a href="#d4mCancelBookings" role="button">by D4M</a></li>
                            <li class="book-tab inactive" id="cancel-pridehonda"><a href="#pridehondaCancelBookings" role="button">by PrideHonda</a></li>
                        </ul>
                    </li>
                </ul>
                <button id="filters" class="btn navbar-btn" style="">
                    Filters&emsp;
                    <span id="filters-off" class="fa-stack fa-xs">
                      <i class="fa fa-filter fa-stack-1x"></i>
                      <i class="fa fa-ban fa-stack-2x"></i>
                    </span>
                    <span id="filters-on" class="fa fa-filter text-success fa-lg fa-collapse"></span>
                </button>
                <button id="refreshCurrent" class="btn navbar-btn align-right"><i class="fa fa-refresh"></i></button>
            </div>
        </nav>
    </div>
    <div id="filtersTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 collapse">
        <div class="row force-content-vertical-center">
            <div class="col-lg-offset-1 col-lg-5 col-md-offset-1 col-md-5 col-sm-12 col-xs-12 tiny-top-padding tiny-bottom-padding">
                <p><i>Click on any circular button to activate that filter group.</i></p>
            </div>
            <div class="col-lg-offset-1 col-lg-5 col-md-offset-1 col-md-5 col-sm-12 col-xs-12 tiny-top-padding tiny-bottom-padding">
                <p>
                    <i>Click on the search icon to refine the search with the current filters.</i>&emsp;
                    <button id="refreshBookingList" class="btn d4m-primary-button text-white"><i class="fa fa-search"></i></button>
                </p>
            </div>
        </div>
        <div id="dateFilters" class="row filter-container tiny-bottom-margin">
            <div class="col-lg-1 col-md-1 col-sm-6 col-xs-12">
                <div class="row force-content-vertical-center">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                        <span>Filter by Dates:</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12">
                <label class="radio-inline btn d4m-primary-button filter-radio activity-trigger">
                    <input id= "day" type="radio" name="dateOpt" value="day" checked="checked">&emsp;Today
                </label>
                <label class="radio-inline btn d4m-primary-button filter-radio activity-trigger">
                    <input id= "specific_date" type="radio" name="dateOpt" value="specific_date">&emsp;Date
                </label>
                <label id="customDateRangeFilter" class="radio-inline btn d4m-primary-button filter-radio activity-trigger">
                    <input id= "date_range" type="radio" name="dateOpt" value="date_range">&emsp;Custom Range&emsp;
                </label>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                <div class="row">
                    <div id="single_date_picker" class="col-lg-6 col-md-6 col-sm-12 col-xs-12 collapse">
                        <span>Start&emsp;</span>
                        <!--dt picker-->
                        <div class="form-group">
                            <div class='input-group date activity-trigger' id='datetimepicker_filter_startdate'>
                                <input type='text' class="form-control" id="start_date" />
                                <span class="input-group-addon custom-input-group">
                                    <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                </span>
                            </div>
                        </div>
                        <!--dtpicker-->
                    </div>
                    <div id="date_range_picker" class="col-lg-6 col-md-6 col-sm-12 col-xs-12 collapse">
                        <span>End&emsp;</span>
                        <!--dt picker-->
                        <div class="form-group">
                            <div class='input-group date activity-trigger' id='datetimepicker_filter_enddate'>
                                <input type='text' class="form-control" id="end_date" />
                                <span class="input-group-addon custom-input-group">
                                    <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                </span>
                            </div>
                        </div>
                        <!--dtpicker-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="newBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="upcomingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="ongoingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="completedBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <!--Trip Photo Modal-->
    <div class="modal fade" id="photoModal" tabindex="-1" role="dialog">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" id= "downloadZip" aria-hidden="true" class="btn btn-default">Download Zip</button>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    <!-- Indicators -->
                    <div class="carousel slide" data-interval="false" id="TripPhotoCarousel">
                        <ol class="carousel-indicators photo-count">
                        </ol>
                        <!-- Wrapper for slides -->
                        <div class="carousel-inner photo-display">
                        </div>
                        <!-- Controls -->
                        <a href="#TripPhotoCarousel" class="left carousel-control" data-slide="prev"><span class="icon-prev"></span></a>
                        <a href="#TripPhotoCarousel" class="right carousel-control" data-slide="next"><span class="icon-next"></span></a>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">
                        Close
                    </button>
                    <!-- <button type="button" class="btn btn-primary">Save changes</button> -->
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <div id="cancel-d4mBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="cancel-pridehondaBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
</div>

</body>
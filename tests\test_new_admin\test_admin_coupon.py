from models import Coupons, db
from datetime import date, timedelta

""" Test cases for api: /api/admin/add_coupon """
# Test Case: Missing JWT <PERSON><PERSON>
def test_add_coupon_missing_jwt_token(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'coupon_type': 'general',
        'region': '0'
    }
    response = client.post('/api/admin/add_coupon', data=form_data)
    
    assert response.status_code == 401
    res_data = response.get_json()
    
    assert res_data['result'] == 'FAILURE'

# Test Case: Unauthorized Admin Role
def test_add_coupon_unauthorized_admin(client, customer_login):
    auth_headers, user = customer_login()

    form_data = {
        'coupon_type': 'general',
        'coupon_code': 'DISCOUNT50',
        'valid_till': '2024-12-31',
        'min_trip': '1',
        'min_price': '100',
        'region': '0'
    }
    
    response = client.post('/api/admin/add_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 403
    res_data = response.get_json()
    
    assert res_data['error'] == 'Forbidden access for this role'

# Test Case: Incomplete Coupon Details
def test_add_coupon_incomplete_details(client, admin_login):
    auth_headers, admin = admin_login
    
    form_data = {
        'coupon_type': 'general',
        'coupon_code': 'DISCOUNT50',
        'region': '0'
    }
    # Make a request missing required fields
    response = client.post('/api/admin/add_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 400
    res_data = response.get_json()
    
    assert res_data['success'] == 2
    assert res_data['msg'] == "Incomplete coupon details"

# Test Case: Invalid Cities Format for General Coupon Type
def test_add_coupon_invalid_cities_format(client, admin_login):
    auth_headers, admin = admin_login

    form_data = {
        'coupon_type': 'general',
        'coupon_code': 'DISCOUNT50',
        'valid_till': '2024-12-31',
        'min_trip': '1',
        'min_price': '100',
        'cities': 'not_a_json',  # Invalid JSON format for cities
        'region': '0'
    }
    
    response = client.post('/api/admin/add_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 400
    res_data = response.get_json()
    
    assert res_data['success'] == -1
    assert res_data['msg'] == 'Invalid cities format'

# Test Case: Successful Coupon Creation (General Coupon Type)
def test_add_coupon_success_general(client, admin_login):
    auth_headers, admin = admin_login
    
    response = client.post('/api/admin/add_coupon', headers=auth_headers, data={
        'coupon_type': 'general',
        'coupon_code': 'DISCOUNT50',
        'valid_till': '2024-12-31',
        'min_trip': '1',
        'min_price': '100',
        'percent_off': '10',
        'cities': '["New York", "Los Angeles"]',
        'region': '0'
    })
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 200
    
    assert res_data['success'] == 1
    assert res_data['msg'] == "Coupon created successfully"

# Test Case: Successful Coupon Creation (Specific Coupon Type)
def test_add_coupon_success_specific(client, admin_login):
    auth_headers, _ = admin_login
    
    response = client.post('/api/admin/add_coupon', headers=auth_headers, data={
        'coupon_type': 'specific',
        'coupon_code': 'SAVE20',
        'valid_till': '2024-11-30',
        'min_trip': '2',
        'min_price': '150',
        'mobile': '1234567890',
        'redeem_limit': '5',
        'percent_off': '20',
        'max_off': '50',
        'region': '0'
    })
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert res_data['msg'] == "Coupon created successfully"

# Test Case: Duplicate Coupon Code Handling
def test_add_coupon_duplicate_coupon_code(client, admin_login):
    auth_headers, _ = admin_login
    
    # First, add a coupon successfully
    client.post('/api/admin/add_coupon', headers=auth_headers, data={
        'coupon_type': 'general',
        'coupon_code': 'DUPLICATECODE',
        'valid_till': '2024-12-31',
        'min_trip': '1',
        'min_price': '100',
        'percent_off': '20',
        'region': '0'
    })
    
    # Try adding the same coupon code again
    response = client.post('/api/admin/add_coupon', headers=auth_headers, data={
        'coupon_type': 'general',
        'coupon_code': 'DUPLICATECODE',
        'valid_till': '2024-12-31',
        'min_trip': '1',
        'min_price': '100',
        'percent_off': '20',
        'region': '0'    
    })
    
    assert response.status_code == 400  # Assuming the server handles duplicates with a 400 error
    res_data = response.get_json()
    
    assert res_data['success'] == -1
    assert res_data['msg'] == "Coupon code already exists"


""" Test cases for api: /api/admin/active_Coupon """
# Test Case: Missing JWT Token
def test_active_coupon_missing_jwt_token(client, admin_login):
    
    form_data = {
        'region': '0',
        'coupon_type': 'general'
    }
    response = client.post('/api/admin/active_coupon', data=form_data)
    
    assert response.status_code == 401
    res_data = response.get_json()
    
    assert res_data['result'] == 'FAILURE'
    
# Test Case: Unauthorized Admin Role
def test_active_coupon_unauthorized_admin(client, customer_login):
    auth_headers, _ = customer_login()

    form_data = {
        'region': '0'
    }
    
    response = client.post('/api/admin/active_coupon', headers=auth_headers, data=form_data)

    assert response.status_code == 403
    res_data = response.get_json()
    
    assert res_data['error'] == 'Forbidden access for this role'

# Test Case: No Active Coupons
def test_active_coupon_no_active_coupons(client, admin_login):
    auth_headers, _ = admin_login

    coupon1 = Coupons("DISCOUNT20", 200, 2, date(2023, 4, 2), percent_off=20, max_off=50)
    coupon2 = Coupons("DISCOUNT50", 200, 2, date(2023, 4, 2), percent_off=20, max_off=50)

    db.session.add(coupon1)
    db.session.add(coupon2)

    db.session.commit()

    form_data = {
        'region': '0'
    }
    
    response = client.post('/api/admin/active_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert len(res_data['result']) == 0

# Test Case: Active Coupons Within Valid Date
def test_active_coupon_valid_active_coupons(client, admin_login):
    auth_headers, _ = admin_login
    
    form_data = {
        'region': '0'
    }
      # Get the current date
    current_date = date.today()

    # Two coupons with past dates
    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date - timedelta(days=10), percent_off=20, max_off=50)
    coupon2 = Coupons("DISCOUNT30", 200, 2, current_date - timedelta(days=5), percent_off=15, max_off=30)

    # Two coupons with future dates
    coupon3 = Coupons("DISCOUNT50", 200, 2, current_date + timedelta(days=5), percent_off=25, max_off=60)
    coupon4 = Coupons("DISCOUNT70", 200, 2, current_date + timedelta(days=10), percent_off=30, max_off=70)

    db.session.add(coupon1)
    db.session.add(coupon2)
    db.session.add(coupon3)
    db.session.add(coupon4)
    coupon3.state = 1
    coupon4.state = 1

    db.session.commit()
    
    response = client.post('/api/admin/active_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert len(res_data['result']) == 2


""" Test cases for api: /api/admin/inactive_coupon """
# Test Case: Missing JWT Token
def test_inactive_coupon_missing_jwt_token(client, admin_login):
    
    form_data = {
        'region': '0'
    }
    response = client.post('/api/admin/inactive_coupon', data=form_data,)
    
    assert response.status_code == 401
    res_data = response.get_json()
    
    assert res_data['result'] == 'FAILURE'

 
# Test Case: Unauthorized Admin Role
def test_inactive_coupon_unauthorized_admin(client, customer_login):
    auth_headers, _ = customer_login()

    form_data = {
        'region': '0'
    }
    
    response = client.post('/api/admin/inactive_coupon', headers=auth_headers)

    assert response.status_code == 403
    res_data = response.get_json()
    
    assert res_data['error'] == 'Forbidden access for this role'

# Test Case: No Inactive Coupons
def test_inactive_coupon_no_inactive_coupons(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'region': '0'
    }

    current_date = date.today()

    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date + timedelta(days=10), percent_off=20, max_off=50)
    coupon2 = Coupons("DISCOUNT30", 200, 2, current_date + timedelta(days=5), percent_off=15, max_off=30)

    db.session.add(coupon1)
    db.session.add(coupon2)

    coupon1.state = 1
    coupon2.state = 1

    db.session.commit()

    response = client.post('/api/admin/inactive_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert len(res_data['result']) == 0

# Test Case: Inactive Coupons Available
def test_inactive_coupon_inactive_coupons(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'region': '0'
    }

    current_date = date.today()

    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date + timedelta(days=10), percent_off=20, max_off=50)
    coupon2 = Coupons("DISCOUNT30", 200, 2, current_date - timedelta(days=5), percent_off=15, max_off=30)

    db.session.add(coupon1)
    db.session.add(coupon2)

    db.session.commit()
    
    response = client.post('/api/admin/inactive_coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert len(res_data['result']) == 2


""" Test cases for api: /api/admin/change_coupon_state """

# Test Case: Coupon Not Found
def test_change_coupon_state_coupon_not_found(client, admin_login):
    auth_headers, _ = admin_login
    
    form_data = {
        'coupon_id': 100,
        'region': '0'
    }
    response = client.post('/api/admin/change_coupon_state', headers=auth_headers, data=form_data)
    
    assert response.status_code == 404
    res_data = response.get_json()
    
    assert res_data['success'] == -1
    assert res_data['msg'] == "No coupon found"

# Test Case: Successfully Change Coupon State from Active to Inactive
def test_change_coupon_state_active_to_inactive(client, admin_login):
    auth_headers, _ = admin_login

    current_date = date.today()
    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date + timedelta(days=10), percent_off=20, max_off=50)

    db.session.add(coupon1)

    coupon1.state = 1
    db.session.commit()

    form_data = {
        'region': '0',
        'coupon_id': coupon1.coupon_id
    }
    
    response = client.post('/api/admin/change_coupon_state', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert res_data['msg'] == "Coupon state changed"

# Test Case: Successfully Change Coupon State from Inactive to Active
def test_change_coupon_state_inactive_to_active(client, admin_login):
    auth_headers, _ = admin_login

    current_date = date.today()
    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date + timedelta(days=10), percent_off=20, max_off=50)

    db.session.add(coupon1)
 
    db.session.commit()
    
    form_data = {
        'region': '0',
        'coupon_id': coupon1.coupon_id
    }
    
    response = client.post('/api/admin/change_coupon_state', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['msg'] == "Coupon state changed"
    assert res_data['success'] == 1

# Test Case: Change expire Coupon State from Inactive to Active 
def test_change_expire_coupon_state_inactive_to_active(client, admin_login):
    auth_headers, _ = admin_login

    current_date = date.today()
    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date - timedelta(days=10), percent_off=20, max_off=50)

    db.session.add(coupon1)
 
    db.session.commit()
    
    form_data = {
        'region': '0',
        'coupon_id': coupon1.coupon_id
    }
    
    response = client.post('/api/admin/change_coupon_state', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['msg'] == "Coupon expire"
    assert res_data['success'] == -1


""" Test cases for api: /api/admin/search/coupon """

# Test Case: Incomplete Search Term
def test_admin_search_coupon_incomplete_search_term(client, admin_login):
    auth_headers, _ = admin_login

    form_data = {
        'region': '0'
    }
    
    # Make a request without the search_term parameter
    response = client.post('/api/admin/search/coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 400
    res_data = response.get_json()

    
    assert res_data['success'] == 2
    assert 'Incomplete search term' in res_data.get('msg', '')

# Test Case: No Coupons Found
def test_admin_search_coupon_no_coupons_found(client, admin_login):
    auth_headers, _ = admin_login
    
    form_data = {
        'search_term': 'non_existent',
        'region': '0'
    }
    
    response = client.post('/api/admin/search/coupon', headers=auth_headers, data=form_data)
    res_data = response.get_json()
    print(res_data)
    assert response.status_code == 404
    
    assert res_data['success'] == 0
    assert 'No coupons found' in res_data.get('msg', '')

# Test Case: Search by Mobile Number
def test_admin_search_coupon_by_mobile(client, admin_login):
    auth_headers, _ = admin_login

    current_date = date.today()
    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date + timedelta(days=10), mobile='1234567890', percent_off=20, max_off=50)

    db.session.add(coupon1)
 
    db.session.commit()
    
    form_data = {
        'search_term': '1234567890',
        'region': '0'
    }
    
    response = client.post('/api/admin/search/coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert len(res_data['data']) == 1

# Test Case: Search by Coupon Code
def test_admin_search_coupon_by_code(client, admin_login):
    auth_headers, _ = admin_login
    
    current_date = date.today()
    coupon1 = Coupons("DISCOUNT20", 200, 2, current_date + timedelta(days=10), mobile='1234567890', percent_off=20, max_off=50)

    db.session.add(coupon1)
 
    db.session.commit()
    
    form_data = {
        'search_term': 'DISCOUNT20',
        'region': '0'
    }
    
    response = client.post('/api/admin/search/coupon', headers=auth_headers, data=form_data)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert res_data['success'] == 1
    assert len(res_data['data']) == 1
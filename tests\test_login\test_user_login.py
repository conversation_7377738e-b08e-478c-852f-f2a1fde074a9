from models import Users, db, UserFCM,UserAnalyticsToken
import random
from sqlalchemy import exc
from flask_jwt_extended import create_access_token
from datetime import timedelta
from unittest.mock import patch
from faker import Faker

fake = Faker()

def unique_user_data():
    return {
        'mobile': f'{random.randint(7000000000, 9999999999)}',  # Random Indian mobile number
        'email': fake.email(),
        'pwd': fake.text(),
        'fname': fake.name(),
        'lname': "<PERSON><PERSON>"
    }

def create_user(data):
    user = Users(
        fname = data['fname'],
        lname = data['lname'],
        mobile = data['mobile'],
        email = data['email'],
        pwd = data['pwd'],
        role = Users.ROLE_USER
    )

    try:
        db.session.add(user)
        db.session.commit()
        print(f"User created with ID: {user.id}")
    except exc.IntegrityError:
        db.session.rollback()

""" Test cases for api: token/login """

# Test case for successful password login
def test_successful_password_login(client):
    data = unique_user_data()
    create_user(data)

    response = client.post('/token/login', data={'mobile': data['mobile'], 'pwd': data['pwd']}, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert 'user_id' in res_data
    assert 'user_fname' in res_data

# Test case for successful otp login
def test_successful_otp_login(client):
    data = unique_user_data()
    create_user(data)

    # For otp to login type should be 1
    response = client.post('/token/login', data={'mobile': data['mobile'], 'pwd': '3487', 'type': 1}, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert 'user_id' in res_data
    assert 'user_mobile' in res_data

# Test case for missing field
def test_missing_fields(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/token/login', data={'mobile': data['mobile']}, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == 0

# Test case for invalid password
def test_invalid_password(client):
    data = unique_user_data()
    create_user(data)
    response = client.post('/token/login', data={'mobile': data['mobile'], 'pwd': 'qwert123', 'type': 0}, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == 0

# Test case for disabled user
def test_disabled_user(client):
    data = unique_user_data()
    create_user(data)

    # Disable the user in the database
    user = Users.query.filter_by(mobile=data['mobile']).first()
    user.enabled = False
    db.session.commit()
    response = client.post('/token/login', data={'mobile': data['mobile'], 'pwd': data['pwd'], 'type': 0}, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == 0

# Test case for invalid otp
def test_invalid_otp(client):
    data = unique_user_data()
    create_user(data)

    # For otp to login type should be 1
    response = client.post('/token/login', data={'mobile': data['mobile'], 'pwd': '1222', 'type': 1}, headers={'User-Agent': 'test-agent'})
    assert response.status_code == 401
    res_data = response.get_json()
    assert res_data['success'] == 0

# Test case for token creation
def test_token_creation(client):
    data = unique_user_data()
    create_user(data)

    response = client.post('/token/login', data={'mobile': data['mobile'], 'pwd': data['pwd']}, headers={'User-Agent': 'test-agent'})

    assert response.status_code == 200

    assert 'Set-Cookie' in response.headers

    cookies = response.headers.getlist('Set-Cookie')

    access_token_cookie = any('access_token_cookie' in cookie for cookie in cookies)
    refresh_token_cookie = any('refresh_token_cookie' in cookie for cookie in cookies)

    assert access_token_cookie, "Access token cookie not found"
    assert refresh_token_cookie, "Refresh token cookie not found"


""" Test cases for api: token/remove """

# Test if the user is successfully logged out and cookies are unset
def test_successful_logout(client, customer_login):
    auth_headers, user = customer_login()

    response = client.post('/token/remove', headers=auth_headers)

    assert response.status_code == 200

    json_data = response.get_json()
    assert json_data == {'logout': True, 'message':'logout successful'}

    cookies = response.headers.getlist('Set-Cookie')

    assert any('restore_id=; Expires=' in cookie for cookie in cookies), "restore_id cookie not found or has unexpected value"
    assert any('name=; Expires=' in cookie for cookie in cookies), "name cookie not found or has unexpected value"
    assert any('user_id=; Expires=' in cookie for cookie in cookies), "user_id cookie not found or has unexpected value"
    assert any('access_token_cookie=; Expires=' in cookie for cookie in cookies), "access_token_cookie not found or has unexpected value"
    assert any('refresh_token_cookie=; Expires=' in cookie for cookie in cookies), "refresh_token_cookie not found or has unexpected value"


# Test logout request without providing a JWT token in the header
def test_logout_without_jwt_token(client):

    response = client.post('/token/remove')

    assert response.status_code == 401

    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test logout request with an expired JWT token.
def test_logout_with_expired_jwt_token(client):

    expired_token = 'eyJpYXQiOjE3MjcxNjUxMDgsIm5iZiI6MTcyNzE2NTEwOCwianRpIjoiN2EwMjY2NjEtYmNmMi00YmY4LWJhYWQtN2UyOTI0NGYxOGYzIiwiZXhwIjoxNzU4NzAxMTA4LCJpZGVudGl0eSI6MTQxMzUyLCJmcmVzaCI6ZmFsc2UsInR5cGUiOiJhY2Nlc3MiLCJ1c2VyX2NsYWltcyI6eyJyb2xlcyI6MCwidXNlcmFnZW50IjoiUG9zdG1hblJ1bnRpbWUvNy40Mi4wIn0sImNzcmYiOiIxNjYzZjZkMy02N2VmLTQyYTgtOGZiMy1mM2ZjMmQ0N2IwMDcifQ'
    csrf_token = '1663f6d3-67ef-42a8-8fb3-f3fc2d47b007'

    auth_headers = {
        'Authorization': f'Bearer {expired_token}',
        'X-CSRF-TOKEN': f'{csrf_token}'
    }

    response = client.post('/token/remove', headers=auth_headers)

    assert response.status_code == 401


""" Test cases for api: token/exists """

# Test when the user does not exist
def test_user_does_not_exist(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'mobile': '**********',
        'gen_otp': '0'
    }
    response = client.post('/token/exists', data=form_data)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['exists'] == 0

# Test when the user's account is disabled
def test_user_account_disabled(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'mobile': user.mobile,
        'gen_otp': '0'
    }

    response = client.post('/token/exists', data=form_data)

    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['exists'] == -2

# Test when OTP generation is requested
def test_otp_generation(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    form_data = {
        'mobile': user.mobile,
        'gen_otp': '1'  # OTP generation requested
    }

    response = client.post('/token/exists', data=form_data)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['exists'] == 1

# Test when OTP generation is not requested
def test_no_otp_generation(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    form_data = {
        'mobile': user.mobile,
        'gen_otp': '0'
    }
    response = client.post('/token/exists', data=form_data)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['exists'] == 1

# Test when mobile number is missing
def test_mobile_missing(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'gen_otp': '0'
    }

    response = client.post('/token/exists', data=form_data)

    assert response.status_code == 201
    json_data = response.get_json()
    assert json_data['exists'] == -1


""" Test cases for api: token/login/set_fcm """

# Test when required fields are missing
def test_fcm_token_missing_fields(client, customer_login):
    auth_header, user = customer_login()

    form_data = {}

    response = client.post('/token/login/set_fcm', data=form_data, headers=auth_header)

    assert response.status_code == 400
    json_data = response.get_json()
    assert json_data['success'] == 0

# Test when FCM token is successfully inserted
def test_fcm_token_insert(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'fcm_token': 'sample_fcm_token',
        'device': 'android'
    }

    response = client.post('/token/login/set_fcm', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1
    
def test_analytics_token_insert(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'analytics_token': 'sample_analytics_token',
        'device': 'android'
    }

    response = client.post('/token/login/set_analytics_token', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1

# Test when FCM token is successfully updated
def test_fcm_token_update(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user_fcm = UserFCM(user.id,'android', "exist_fcm_token")
    db.session.add(user_fcm)
    db.session.commit()

    form_data = {
        'fcm_token': 'new_fcm_token',
        'device': 'android'
    }

    response = client.post('/token/login/set_fcm', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1
    
def test_analytics_token_update(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user_analytics = UserAnalyticsToken(user.id, 'android', "exist_analytics_token")
    db.session.add(user_analytics)
    db.session.commit()

    form_data = {
        'analytics_token': 'new_analytics_token',
        'device': 'android'
    }

    response = client.post('/token/login/set_analytics_token', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1


# Test for handling multiple devices
def test_fcm_multiple_devices(client, customer_login):
    auth_header, user = customer_login()

    # First request with 'android' device
    form_data_android = {
        'fcm_token': 'android_fcm_token',
        'device': 'android'
    }

    response_android = client.post('/token/login/set_fcm', data=form_data_android, headers=auth_header)
    assert response_android.status_code == 200
    json_data_android = response_android.get_json()
    assert json_data_android['success'] == 1

    # Second request with 'ios' device
    form_data_ios = {
        'fcm_token': 'ios_fcm_token',
        'device': 'ios'
    }

    response_ios = client.post('/token/login/set_fcm', data=form_data_ios, headers=auth_header)
    assert response_ios.status_code == 200
    json_data_ios = response_ios.get_json()
    assert json_data_ios['success'] == 1

# Test when FCM token is empty
def test_fcm_token_empty(client, customer_login):
    auth_header, user = customer_login()

    form_data = {
        'fcm_token': '',
        'device': 'android'
    }

    response = client.post('/token/login/set_fcm', data=form_data, headers=auth_header)

    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['success'] == 1

tags:
  - Admin Analytics
summary: Get Transaction Summary for Customer Admin
description: |
  This endpoint retrieves the transaction summary for customer admin transactions.
  It returns aggregated transaction amounts grouped by admin. When the `data_type`
  is "Cancel", the response includes cancellation charges and cancellation reversal counts;
  for other data types (e.g. "Fine", "Gift", "Deduct", "Add"), the response includes a total
  transaction amount for each admin.
  The data is filtered by a specified date range, time range, and optionally by a region filter.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: from_time
    in: formData
    type: string
    required: false
    default: "00:00:00"
    description: "Start time for filtering transactions (HH:MM:SS). Defaults to '00:00:00'."
    example: "00:00:00"
  - name: to_time
    in: formData
    type: string
    required: false
    default: "23:59:59"
    description: "End time for filtering transactions (HH:MM:SS). Defaults to '23:59:59'."
    example: "23:59:59"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A comma-separated list of region IDs to filter transactions.
      Use "-1" to include all regions.
    example: "1,2,3"
  - name: data_type
    in: formData
    type: string
    required: false
    default: "Fine"
    description: >
      Specifies the type of transaction summary to retrieve.
      Accepted values are:
        - "Fine" (for fine transactions)
        - "Gift" (for gift transactions)
        - "Deduct" (for deduct transactions)
        - "Add" (for add transactions)
        - "Cancel" (for cancellation transactions)
    example: "Fine"
responses:
  200:
    description: "Successfully retrieved the transaction summary for customer admin."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 indicates success)."
          example: 1
        data:
          type: array
          description: >
            If data_type is "Cancel", each item in the array contains:
              - admin_name: The name of the admin.
              - cancellation_charges_count: The total cancellation charges amount.
              - cancellation_reversals_count: The total cancellation reversal amount.
            For non-cancellation data types, each item contains:
              - admin_name: The name of the admin.
              - total_amount: The total transaction amount.
          items:
            type: object
            properties:
              admin_name:
                type: string
                description: "Name of the admin."
                example: "John Doe"
              cancellation_charges_count:
                type: number
                description: "Total cancellation charges amount."
                example: 1500
              cancellation_reversals_count:
                type: number
                description: "Total cancellation reversal amount."
                example: 500
              total_amount:
                type: number
                description: "Total transaction amount for non-cancellation types."
                example: 2000
  400:
    description: "Invalid request due to missing or invalid parameters."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (-2 indicates missing/invalid parameters)."
          example: -2
        error:
          type: string
          description: "Error message describing the issue."
          example: "Missing date parameters or invalid data_type parameter."
  500:
    description: "Internal server error or database failure."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 indicates a server error)."
          example: 0
        error:
          type: string
          description: "Error message detailing the internal error."
          example: "Internal server error"

$(document).ready(function () {
    window.firstaccess = true;
// <editor-fold desc="Ready Operations">
    //Initialize Date-Time pickers
    initDTPickers();
    //Load Map Inputs in form
    google.maps.event.addDomListener(window, 'load', initMapInputs);
    //load all Source SPOC info
    var db = initRealtimeDB();
    var notSet = true;
    var c = getCookie();
    if (c["region"] == 1) {
        $("#region option[value='0']").remove();
        $('#region').append('<option value="1">Hyderabad</option>');
    } else if (c["region"] == 6) {
        $("#region option[value='0']").remove();
        $('#region').append('<option value="6">Delhi</option>');
    } else if (c["region"] == 11) {
        $("#region option[value='0']").remove();
        $('#region').append('<option value="11">Bhubaneswar</option>');
    }
    if (c["region"] == -1) {
        $('#region').append('<option value="1">Hyderabad</option>');
        $('#region').append('<option value="6">Delhi</option>');
        $('#region').append('<option value="11">Bhubaneswar</option>');
    }
    if (c["region"] == 1) {
        $("#p_hubs option[value='Cardekho - Delhi Parking']").remove();
        $("#d_hubs option[value='Cardekho - Delhi Parking']").remove();
    } else if (c["region"] == 6) {
        $("#p_hubs option[value='Cardekho - Hyderabad Parking']").remove();
        $("#d_hubs option[value='Cardekho - Hyderabad Parking']").remove();
    } else if (c["region"] == 0) {
        $("#p_hubs option[value='Cardekho - Delhi Parking']").remove();
        $("#p_hubs option[value='Cardekho - Hyderabad Parking']").remove();
        $("#d_hubs option[value='Cardekho - Delhi Parking']").remove();
        $("#d_hubs option[value='Cardekho - Hyderabad Parking']").remove();
    }

    loadSourceSPOCList();
// </editor-fold>

// <editor-fold desc="Events">
    //opening the correct panel....................................................................................

    //Necessary Hack - Absolutely no idea why the next function ignores this particular selector
    $("#newBooking").click(function () {
        if (!$(this).hasClass('inactive')) return;
        $("#topBar").find("#navComponents").find(".active").removeClass('active').addClass('inactive');
        $(this).removeClass('inactive').addClass('active');
        //code for close all other panels
        $(".function-panel").addClass('collapse');
        //open the correct panel
        $("#"+$(this).attr('id')+"Panel").removeClass('collapse');
    });
    $("#bookings").click(function() {
        if (!$(this).hasClass('inactive')) return;
        $("#topBar").find("#navComponents").find(".active").removeClass('active').addClass('inactive');
        $(this).removeClass('inactive').addClass('active');
        //code for close all other panels
        $(".function-panel").addClass('collapse');
        //open the correct panel
        $("#"+$(this).attr('id')+"Panel").removeClass('collapse');
        if (notSet) {
            $("#newBookingTab").removeClass('collapse')
            $("#new").removeClass('inactive').addClass('active');
            $("#new").click();
            notSet = false;
        }
    });

    //opening the correct booking tabs....................................................................................
    $("#bookingTypeBar").find(".navbar-nav").find(".inactive").click(function() {
        //clear all entry areas
        $("#newBookingTab").html('')
        $("#upcomingBookingTab").html('')
        $("#ongoingBookingTab").html('')
        $("#bookingTypeBar").find(".navbar-nav").find(".active").attr('class','book-tab inactive');
        $(this).attr('class','book-tab active');
        //close all other booking tabs
        $(".booking-tab").addClass("collapse");
        //open the correct tab
        $("#"+$(this).attr('id')+"BookingTab").removeClass("collapse");
        var dropDownChoiceInfo = $(this).closest(".navbar-nav").find(".dropdown-choice-info");
        dropDownChoiceInfo.html();
        if(!dropDownChoiceInfo.hasClass("collapse")) dropDownChoiceInfo.addClass("collapse");
        //proceed to fetching entries via AJAX call
        if ($(this).attr("id") != "completed") popBooking($(this).attr("id"));
        else {
            if (window.firstaccess) popBooking("completed");
            window.firstaccess = false;
        }
    });

    //Search with filters
    $("#refreshBookingList").click(function() {
        $("#bookingTypeBar").find(".navbar-nav").find(".active").first().click();
    });

    //opening booking tabs from dropdowns
    $("#bookingTypeBar").find(".dropdown-menu").find(".inactive").click(function() {
        var dropDownChoiceInfo = $(this).closest(".navbar-nav").find(".dropdown-choice-info");
        dropDownChoiceInfo.html(" (" + $(this).find("a").html() + ") ");
        dropDownChoiceInfo.removeClass("collapse");
    });

    $('#region').change(function() {
        loadSourceSPOCList();
    });

    // <editor-fold desc="New Booking">
    //Auto-fill Source SPOC contact from SPOC name option
    $('body').delegate("#pickupNameSelect", "change", function () {
        displaySourceSPOCContact(this.closest("select"));
    });

    $("#tripTypeSelect").change(function() {
        switch(parseInt($("#tripTypeSelect").find(":selected").attr("value"))) {
            case 0:
                $("#PickupTimeInputSection").addClass("collapse");
                $("#DeliveryTimeInputSection").removeClass("collapse");
                break;
            case 1:
                $("#PickupTimeInputSection").removeClass("collapse");
                $("#DeliveryTimeInputSection").addClass("collapse");
                break;
            case 2:
                $("#PickupTimeInputSection").removeClass("collapse");
                $("#DeliveryTimeInputSection").removeClass("collapse");
                break;
        }
    });
    //Submit new booking form
    //TO DO - Need to think of better ways to get lat long directly from autocomplete string
    $("#book").click(function() {
        //validate all fields
        var valid = true;
        var validationFieldList = [];
        var clientBookingId = $("#clientBookingId").val();
        if(isNullOrEmpty(clientBookingId))
        {
            $("#clientBookingId").addClass("input-error");
            valid = false;
            validationFieldList.push("Appointment ID - Empty");
        }
        var vehicleModel = $("#vehicleModel").val();
        if(isNullOrEmpty(vehicleModel))
        {
            $("#vehicleModel").addClass("input-error");
            valid = false;
            validationFieldList.push("Vehicle Model - Empty");
        }
        var vehicleRegistrationNumber = $("#vehicleRegistrationNumber").val();
        if(isNullOrEmpty(vehicleRegistrationNumber))
        {
            $("#vehicleRegistrationNumber").addClass("input-error");
            valid = false;
            validationFieldList.push("Vehicle Registration - Empty");
        }
        var pickupSearch = $("#pickupSearch").val();
        console.log(pickupSearch);
        console.log(pickupSearch === "Cardekho - Delhi Parking");
        if(isNullOrEmpty(pickupSearch))
        {
            $("#pickupSearch").addClass("input-error");
            valid = false;
            validationFieldList.push("Pickup Location - Empty");
        }
        else {
            var pickupLocationObject;
            if (pickupSearch === "Cardekho - Delhi Parking") {
                pickupLocationObject = {
                    "latitude":  28.5206,
                    "longitude": 77.1390,
                    "status": "200"
                }
            }else if (pickupSearch === "Cardekho - Hyderabad Parking") {
                pickupLocationObject = {
                    "latitude":  28.48177813,
                    "longitude": 77.05702536,
                    "status": "200"
                }
            }  else {
                pickupLocationObject = getLatLong(pickupSearch);
            }
            if(isNullOrEmpty(pickupLocationObject["latitude"]) || isNullOrEmpty(pickupLocationObject["longitude"])) {
                $("#pickupSearch").addClass("warning-error");
                valid = false;
                validationFieldList.push("Pickup Location - Not Found");
            }
        }
        console.log(pickupLocationObject);
        var dropSearch = $("#dropSearch").val();
        if(isNullOrEmpty(dropSearch))
        {
            $("#dropSearch").addClass("input-error");
            valid = false;
            validationFieldList.push("Drop Location - Empty");
        }
        else {
            var dropLocationObject;
            if (dropSearch === "Cardekho - Delhi Parking") {
                dropLocationObject = {
                    "latitude":  28.5206,
                    "longitude": 77.1390,
                    "status": "200"
                }
            }else if (dropSearch === "Cardekho - Hyderabad Parking") {
                dropLocationObject = {
                    "latitude":  28.48177813,
                    "longitude": 77.05702536,
                    "status": "200"
                }
            }else {
                dropLocationObject = getLatLong(dropSearch);
            }
            if(isNullOrEmpty(dropLocationObject["latitude"]) || isNullOrEmpty(dropLocationObject["longitude"])) {
                $("#dropSearch").addClass("warning-error");
                valid = false;
                validationFieldList.push("Drop Location - Not Found");
            }
        }
        console.log(dropLocationObject);
        var dropName = $("#dropName").val();
        /*if(isNullOrEmpty(dropName))
        {
            $("#dropName").addClass("input-error");
            valid = false;
            validationFieldList.push("Destination SPOC name - Empty");
        }*/
        var dropMobile = $("#dropMobile").val();
        if(isNullOrEmpty(dropMobile))
        {
            $("#dropMobile").addClass("input-error");
            valid = false;
            validationFieldList.push("Destination SPOC Contact - Empty");
        }
        if(valid) {
            var tripType = parseInt($("#tripTypeSelect").find(":selected").attr("value"));
            var transmissionType = parseInt($("#transmissionTypeSelect").find(":selected").attr("value"));
            var region = parseInt($("#region").val());
            var sourceSPOCID = $("#pickupNameSelect").val().split(":")[0];
            var scheduledPickupDateTimeUTC = "", scheduledDeliveryDateTimeUTC = "";
            if(parseInt(tripType) == 0 || parseInt(tripType) == 2) {
                //the following snippet extracts date object from the two datetime fields and then merges them into one
                //datetime object with the date values of the date field and time values of the time field
                var scheduledDeliveryDateTime = new Date($('#datetimepicker_delivery_date').data("DateTimePicker").date());
                var tempDeliveryScheduledTime = new Date($('#datetimepicker_delivery_time').data("DateTimePicker").date());
                scheduledDeliveryDateTime.setHours(tempDeliveryScheduledTime.getHours(), tempDeliveryScheduledTime.getMinutes(),
                    tempDeliveryScheduledTime.getSeconds(), tempDeliveryScheduledTime.getMilliseconds());
                scheduledDeliveryDateTimeUTC = scheduledDeliveryDateTime.toLocaleString
                                            ('en-GB',
                                                {   day: "numeric",
                                                    month: "numeric",
                                                    year: "numeric",
                                                    hour: 'numeric',
                                                    minute: 'numeric',
                                                    second: 'numeric',
                                                    timeZone: 'UTC'

                                                }
                                            ).replace(new RegExp(/\//g), "-").replace(',', '');
            }

            if(parseInt(tripType) == 1 || parseInt(tripType) == 2) {
                //the following snippet extracts date object from the two datetime fields and then merges them into one
            //datetime object with the date values of the date field and time values of the time field
            var scheduledPickupDateTime = new Date($('#datetimepicker_pickup_date').data("DateTimePicker").date());
            var tempPickupScheduledTime = new Date($('#datetimepicker_pickup_time').data("DateTimePicker").date());
            scheduledPickupDateTime.setHours(tempPickupScheduledTime.getHours(), tempPickupScheduledTime.getMinutes(),
                tempPickupScheduledTime.getSeconds(), tempPickupScheduledTime.getMilliseconds());
            scheduledPickupDateTimeUTC = scheduledPickupDateTime.toLocaleString
                                        ('en-GB',
                                            {   day: "numeric",
                                                month: "numeric",
                                                year: "numeric",
                                                hour: 'numeric',
                                                minute: 'numeric',
                                                second: 'numeric',
                                                timeZone: 'UTC'

                                            }
                                        ).replace(new RegExp(/\//g), "-").replace(',', '');
            }

            if(parseInt(tripType) < 2) {
                registerNewBooking  (
                                    clientBookingId, tripType, vehicleModel, vehicleRegistrationNumber,
                                    transmissionType, region,
                                    pickupLocationObject["latitude"], pickupLocationObject["longitude"], pickupSearch,
                                    dropLocationObject["latitude"], dropLocationObject["longitude"], dropSearch,
                                    sourceSPOCID, dropName, dropMobile, scheduledPickupDateTimeUTC, scheduledDeliveryDateTimeUTC
                                );
            }
            else {
                // pickup
                registerNewBooking  (
                                    clientBookingId, 0, vehicleModel, vehicleRegistrationNumber,
                                    transmissionType, region,
                                    pickupLocationObject["latitude"], pickupLocationObject["longitude"], pickupSearch,
                                    dropLocationObject["latitude"], dropLocationObject["longitude"], dropSearch,
                                    sourceSPOCID, dropName, dropMobile, scheduledPickupDateTimeUTC, scheduledDeliveryDateTimeUTC
                                );
                // delivery
                registerNewBooking  (
                                    clientBookingId, 1, vehicleModel, vehicleRegistrationNumber,
                                    transmissionType, region,
                                    dropLocationObject["latitude"], dropLocationObject["longitude"], dropSearch,
                                    pickupLocationObject["latitude"], pickupLocationObject["longitude"], pickupSearch,
                                    sourceSPOCID, dropName, dropMobile, scheduledPickupDateTimeUTC, scheduledDeliveryDateTimeUTC
                                );
            }
            $("#newBookingForm").trigger("reset");
            initDTPickers();
            loadSourceSPOCList();
        }
        else {
            var validationList = getValidationMessageListHtml(validationFieldList);
            openInformationModal("Information", "<p>The following issues were found with some of the fields:</p>" + validationList, "danger");
        }

    });

    $('body').delegate("#AfterBookingModal .continue-button", "click", function () {
        if($("#collapsedMainMenu").css('display') === "none") {
            $("#new").click();
            $('#bookingsPanel').removeClass('collapse');
            $('#newBookingPanel').addClass('collapse');
            $("#newBooking").removeClass("active").addClass("inactive");
            $("#bookings").removeClass("active").addClass("inactive");
        }
        else {
            $("#bookingsResp").trigger('click');
        }
        $("#AfterBookingModal").modal('hide');
    });

    if(window.location.hash) {
      if (window.location.hash == "#newBooking") {
          $("#newBooking").click();
          $('#bookingsPanel').addClass('collapse');
          $('#newBookingPanel').removeClass('collapse');
      } else if (window.location.hash == "#bookingList") {
          $("#new").click();
          $('#bookingsPanel').removeClass('collapse');
          $('#newBookingPanel').addClass('collapse');

      } else if (window.location.hash == "#allBookings") {
          $('#bookings').click();
          $('#all').click();
      } else if (window.location.hash == "#newBookings") {
          $('#bookings').click();
          $("#new").click();
      } else if (window.location.hash == "#upcomingBookings") {
          $('#bookings').click();
          $("#upcoming").click();
      }  else if (window.location.hash == "#ongoingBookings") {
          $('#bookings').click();
          $("#ongoing").click();
      }  else if (window.location.hash == "#completedBookings") {
          $('#bookings').click();
          $("#completed").click();
      }  else if (window.location.hash == "#d4mCancelBookings") {
          $('#bookings').click();
          $("#cancel-d4m").click();
      } else if (window.location.hash == "#cardekhoCancelBookings") {
          $('#bookings').click();
          $("#cancel-cardekho").click();
      } else if (window.location.hash == "#cardekhoCreateSpoc") {
          $('#newSpoc').click();
      } else {
           $("#newBooking").click();
           $('#bookingsPanel').addClass('collapse');
           $('#newBookingPanel').removeClass('collapse');
      }
    } else {
        $("#newBooking").click();
        $('#bookingsPanel').addClass('collapse');
        $('#newBookingPanel').removeClass('collapse');
    }

    $('body').delegate(".track", "click", function() {
      showCurrentLocation(db, $(this).attr('data'));
    });

    $('body').delegate(".remarks", "keyup", function(e) {
        $(e.target).removeClass("fail-save").addClass("unsaved");
        var bookingID = parseInt($(this).attr('id').split('-').slice(-1).pop());
        $('.save-cmt[data="' + bookingID + '"]').removeClass('disabled');
    });

    $('body').delegate(".save-cmt", "click", function() {
        if ($(this).hasClass('disabled')) return;
        var bookingID = $(this).attr('data');
        var textAreaID = '#cmt-area-' + bookingID;
        saveComment(bookingID, $(textAreaID).val(), textAreaID, this);
    });

    $("#logout").click(function() {
        logoutCardekho();
        deleteCookie("access_token_cookie");
        deleteCookie("csrf_access_token");
        deleteCookie("csrf_refresh_token");
        deleteCookie("refresh_token_cookie");
        window.location = "/affiliate/cardekho/login";
    })
    //
    // </editor-fold>

// </editor-fold>

});

function initDTPickers() {
    //Initialize datetime pickers
    var start_Date = new Date(new Date().getTime() + 2 * 60 * 60 * 1000);
    var defaultFilterStart = new Date(new Date().getTime() - 24 * 60 * 60 * 1000);
    var defaultFilterEnd = new Date(new Date().getTime() + 24 * 60 * 60 * 1000);
    var pickupDateElement = $('#datetimepicker_pickup_date');
    var pickupTimeElement = $('#datetimepicker_pickup_time');
    var deliveryDateElement = $('#datetimepicker_delivery_date');
    var deliveryTimeElement = $('#datetimepicker_delivery_time');
    var filterStart = $("#datetimepicker_filter_startdate");
    var filterEnd = $("#datetimepicker_filter_enddate");
    //pickup
    //initialize datepicker
    pickupDateElement.datetimepicker({
        locale: 'en',
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'top'
        },
        format: 'DD/MM/YYYY'
    });
    pickupDateElement.data("DateTimePicker").date(start_Date);
    //initialize timepicker
    pickupTimeElement.datetimepicker({
        locale: 'en',
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'top'
        },
        format : 'LT'
    });
    pickupTimeElement.data("DateTimePicker").date(start_Date);
    //delivery
    //initialize datepicker
    deliveryDateElement.datetimepicker({
        locale: 'en',
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'top'
        },
        format: 'DD/MM/YYYY'
    });
    deliveryDateElement.data("DateTimePicker").date(start_Date);
    //initialize timepicker
    deliveryTimeElement.datetimepicker({
        locale: 'en',
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'top'
        },
        format : 'LT'
    });
    deliveryTimeElement.data("DateTimePicker").date(start_Date);
    //initialize filter range start
    filterStart.datetimepicker({
        locale: 'en',
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'bottom'
        },
        format: 'DD/MM/YYYY'
    });
    filterStart.data("DateTimePicker").date(defaultFilterStart);
    //initialize filter range start
    filterEnd.datetimepicker({
        locale: 'en',
        widgetPositioning: {
            horizontal: 'right',
            vertical: 'bottom'
        },
        format: 'DD/MM/YYYY'
    });
    filterEnd.data("DateTimePicker").date(defaultFilterEnd);
}

function initMapInputs() {
    var pickupInput = document.getElementById("pickupSearch");
    var pickAutocomplete = new google.maps.places.Autocomplete(pickupInput);
    pickAutocomplete.setFields(["geometry", "name", "formatted_address", "adr_address", " address_component"]);
    pickAutocomplete.setComponentRestrictions(
        {'country': ['in']});
    var dropInput = document.getElementById("dropSearch");
    var dropAutocomplete = new google.maps.places.Autocomplete(dropInput);
    dropAutocomplete.setFields(["geometry", "name", "formatted_address", "adr_address", " address_component"]);
    dropAutocomplete.setComponentRestrictions(
        {'country': ['in']});

}

/**
 * Extract Source SPOC contact from SPOC name
 * dropdown option and fill the corresponding input
 * @param element - the selected option
 */
function displaySourceSPOCContact(element) {
    var SPOCContact = $(element).val();
    console.log(SPOCContact);
    $("#pickupMobile").val(SPOCContact.split(':')[1]);
}

/**
 * gathers the parameters for a new booking and fires the AJAX call
 * TO DO - As all validation is handled in the caller methods, a quick check to
 * see if more validation is necessary must be done
 * @param clientBookingId - Client Booking ID also referred to as appointment ID [appt_id]
 * @param tripType [cardekho_type]
 * @param vehicleModel [veh_model]
 * @param vehicleRegistrationNumber [veh_no]
 * @param transmissionType - 0: Manual 1: Automatic
 * @param region - Booking region, generally the city itself
 * @param sourceLat
 * @param sourceLong
 * @param sourceAddressString
 * @param destinationLat
 * @param destinationLong
 * @param destinationAddressString
 * @param srcSpocID - SPOC reference updated from DB
 * @param destSpocName
 * @param destSpocContact
 * @param scheduledDateTime - in UTC
 * The AJAX call equivalent parameters are mentioned in []
 */
function registerNewBooking(clientBookingId, tripType,
                            vehicleModel, vehicleRegistrationNumber,
                            transmissionType, region,
                            sourceLat, sourceLong, sourceAddressString,
                            destinationLat, destinationLong, destinationAddressString,
                            srcSpocID, destSpocName, destSpocContact,
                            scheduledPickupDateTime, scheduledDeliveryDateTime) {

    var data = new FormData();
    data.append("appt_id", clientBookingId);
    data.append("cardekho_type", tripType);
    data.append("veh_model", vehicleModel);
    data.append("veh_no", vehicleRegistrationNumber);
    data.append("car_auto", transmissionType);
    data.append("region", region);
    data.append("reflat", sourceLat);
    data.append("reflong", sourceLong);
    data.append("loc_name", sourceAddressString);
    data.append("dest_reflat", destinationLat);
    data.append("dest_reflong", destinationLong);
    data.append("dest_locname", destinationAddressString);
    data.append("rep", srcSpocID);
    data.append("dspoc_name", destSpocName);
    data.append("dspoc_mob", destSpocContact);
    switch(parseInt(tripType)) {
        case 0: data.append("book_dt", scheduledDeliveryDateTime); break;
        case 1: data.append("book_dt", scheduledPickupDateTime); break;
    }


    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/cardekho/book',
        data: data,
        beforeSend: function (request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            if (refresh_token) {


            }
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        dataType: "json",
        contentType: false,
        processData: false,
        success: function (response) {
            if(response["success"] == 1) {
                openModal("AfterBooking", "success");
            }
            else {
                openModal("AfterBooking", "failure");
            }
        }
    });
}

/**
 * TO DO - Make a separate JS for this function, it doesn't belong here
 */
function getCookie() {
    cookieSplit = document.cookie.split("; ");
    var cookieObject = {};
    cookieSplit.forEach( function(value, index) {
        var splitResult = value.split("=");
        cookieObject[splitResult[0]] = splitResult[1];
    });
    return cookieObject;
}

function loadSourceSPOCList() {
    var region = parseInt($("#region").val());
    var data = new FormData();
    data.append("region", region);
    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/cardekho/fetch_rep',
        data: data,
        beforeSend: function (request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            if (refresh_token) {

            }
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        dataType: "json",
        contentType: false,
        processData: false,
        success: function (response) {
            if(response['success'] == 1) {
                $("#pickupNameSelect").html("");
                for(var i=0; i<response["data"].length; i++)
                {
                    var tuple = JSON.parse(response["data"][i]);
                    var newSPOC = getSPOCEntry(tuple.id, tuple.name, tuple.mobile);
                    $("#pickupNameSelect").append(newSPOC);
                }
            }
            displaySourceSPOCContact($("#pickupNameSelect"));
        }
    });

}

// <editor-fold desc="HTML content generation">

function getMainMenuModalContent() {
    $("#MainMenuModal").find('.modal-body').html(
        '<ul class="nav navbar-nav">'+
            '<li id="newBookingResp" class="resp-menu-item">New Booking</li>'+
            '<li id="bookingsResp" class="resp-menu-item">Bookings</li>'+
            '<li class="resp-menu-item">' +
            '<button id="logout_resp" class="btn navbar-btn d4m-primary-button navbar-right">\n' +
            'Logout&emsp;<span class="glyphicon glyphicon-off"></span>'+
            '</button>'+
            '</li><br>'+
        '</ul>'
    );
}

function getBookingMenuModalContent() {
    $("#BookingMenuModal").find('.modal-body').html(getRespFilterButtonHtml() +
        '<ul class="nav navbar-nav">'+
            '<li id="allResp" class="resp-menu-item">All</li>'+
            '<li id="newResp" class="resp-menu-item">New</li>'+
            '<li id="upcomingResp" class="resp-menu-item">Upcoming</li>'+
            '<li id="ongoingResp" class="resp-menu-item">Ongoing</li>'+
            '<li id="completedResp" class="resp-menu-item">Completed</li>'+
            '<li id="cancel-d4mResp" class="resp-menu-item">Cancelled (by D4M)</li>'+
            '<li id="cancel-cardekhoResp" class="resp-menu-item">Cancelled (by Cardekho)</li>'+
            '<br>'+
        '</ul>'+
        getRespRefreshButtonHtml()
    );
}

function getAfterBookingModalContent(status) {
    var header = status === "success" ? "Booking Successful!" : "Booking Failed!"
    var message =   status === "success" ?
                    "Booking successful!." :
                    "Your booking could not be registered. Please report the situation to your administrators.";
    $("#AfterBookingModal").find('.modal-header').html(header);
    $("#AfterBookingModal").find('.modal-body').html(message);
    if(status === "success") {
        $("#AfterBookingModal").find('.modal-footer').find('.continue-button').removeClass("collapse");
    }
    else {
        $("#AfterBookingModal").find('.modal-footer').find('.continue-button').addClass("collapse");
    }
}

function getNewSPOCModalContent() {
    $("#NewSPOCModal").find(".modal-header").html('Register New SPOC');
    $("#NewSPOCModal").find(".modal-body").html(
        '<form id="newSPOCForm" enctype="multipart/form-data" autocomplete="off">' +
            ' <div class="row">' +
                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 form_element">' +
                    '<p>Name</p>' +
                '</div>' +
                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 form_element align-right">' +
                    '<p>Contact</p>' +
                '</div>' +
            '</div>' +
            ' <div class="row">' +
                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 form_element">' +
                    '<input name="spocName" type="text" class="form-control" id="spocName" placeholder="SPOC Name...." />' +
                '</div>' +
                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 form_element align-right">' +
                    '<input name="mobile" type="mobile" class="form-control" id="spocContact" placeholder="SPOC Contact...." maxlength="10" onkeydown="return restrictToNumericInput(event)"/>' +
                '</div>' +
            '</div>' +
        '</form>'
    );
}

function getInformationModalContent(message, color) {
    $("#InformationModal").find(".modal-header").removeClass("d4m-element-primary d4m-element-danger").addClass("d4m-element-" + color);
    $("#InformationModal").find(".modal-footer").removeClass("d4m-element-primary d4m-element-danger").addClass("d4m-element-" + color);
    $("#InformationModal").find(".modal-body").html(message);
}

function getDriverListModalContent() {
    $("#DriverListModal").find(".modal-body").html
    (
        '<div class="row no-padding standard-top-padding">' +
            '<div class="col-lg-12 col-md-12 col-sm-12 col-s-12 no-padding driver-list">' +
            '</div>' +
        '</div>'
    );
}

/**
 * With a given booking id, driver id, name, contact and a set of action buttons
 * creates a driver entry for a list
 * @param bookingID
 * @param driverID
 * @param fullname
 * @param contact
 * @param actions - string array of action button names
 * @return {string} HTML for driver entry
 */
function getDriverEntryHtml(bookingID ,driverID, fullname, contact, actions) {
    var actionButtonSection = '';
    $.each(actions, function (index, value) {
        switch(value) {
            case "Cancel":
            case "Close":
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-danger-button">' + value + '</button>' +
                    '</div>';
            break;
            //TO DO - Write CSS for Info, warning and success
            case "Details":
            case "More Info":
            case "Info":
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-info-button">' + value + '</button>' +
                    '</div>';
            break;
            case "Allocate":
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding  align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-primary-button allocate-driver">' + value + '</button>' +
                    '</div>';
            break;
            default:
                actionButtonSection +=  '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding align-right" style="padding-bottom: 4px;">' +
                    '<button class="btn btn-md d4m-primary-button">' + value + '</button>' +
                    '</div>';
        }
    });
    //driver_bookingID_driverID
    return (
        '<div id="driver_' + bookingID + "_" + driverID + '" class="row driver-entry tiny-top-padding tiny-bottom-padding" style="text-align: center; margin: 0;">' +
            '<div class="col-lg-12 col-md-12 col-sm-12 col-s-12 no-padding">' +
                '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-6 no-padding"><p class="name">' + fullname + '</p></div>'+
                '<div class="col-lg-3 col-md-3 col-sm-6 col-xs-6 no-padding"><p class="contact">' + contact + '</p></div>'+
                '<div class="col-lg-5 col-md-5 col-sm-11 col-xs-11 no-padding">' +
                    '<div class="row no-padding action-section">' + actionButtonSection +
                    '</div>' +
                '</div> ' +
            '</div>' +
        '</div>'
    );
}


/**
 * form an option for select box with
 * SPOC id, name and mobile
 * @param id - SPOC id
 * @param name - can be null (set to UNNAMED_SPOC default)
 * @param mobile
 * @return HTMLOptionElement HTML option element
 */
function getSPOCEntry(id, name, mobile) {
    if(name == null || name === "") {
        name = window.messages["UNNAMED_SPOC"];
    }
    return (new Option(name, id + ':' + mobile));
}

/**
 * Save a Cardekho Remark
 * @param bookingID - Booking id
 * @param comment - Text
 * @param textAreaID - ID of text area we are modifying
 */
function saveComment(bookingID, comment, textAreaID, curElem) {
    var data = new FormData();
    data.append("booking_id", bookingID);
    data.append("comment", comment);
    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/cardekho/save_cmt',
        data: data,
        beforeSend: function (request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            if (refresh_token) {

            }
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        dataType: "json",
        contentType: false,
        processData: false,
        success: function (response) {
            if(response['success'] == 1) {
                $(textAreaID).removeClass('unsaved').removeClass('fail-save');
                $(curElem).addClass('disabled');
            } else {
                $(textAreaID).removeClass('unsaved').addClass('fail-save');
            }
        }
    });
}

/**
 * Dumb implementation - try to logout, if we can't pretend we did!
 */

function logoutCardekho() {
    $.ajax({
        type:"POST",
        url: window.location.protocol + '//' + window.location.host + '/token/remove',
        data: "",
        beforeSend: function(request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            request.setRequestHeader('X-CSRF-Token', csrf_token);
         },
        dataType: "json",
        contentType: false,
        processData: false
    });
}
// </editor-fold>
#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  tnc.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from flask import Blueprint, render_template, request, jsonify, Response
from _sms import send_msg
import re, datetime, uuid, pytz, json
from _utils import get_safe, complete
from booking_params import BookingParams
from book_ride import _get_estimate
import sh, requests, html

seo = Blueprint('seo', __name__)

@seo.route('/book-drivers-in-delhi')
@seo.route('/book-drivers-in-jaipur')
@seo.route('/book-drivers-in-hyderabad')
@seo.route('/book-drivers-in-mumbai')
@seo.route('/get-drivers-in-delhi')
@seo.route('/get-drivers-in-jaipur')
@seo.route('/get-drivers-in-hyderabad')
@seo.route('/get-drivers-in-mumbai')
def render_template_based_on_path():
    return render_template('homepage.html')

@seo.route('/book-drivers-in-alipore', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-alipore', methods=['GET', 'POST'])
def alipore():
    return render_template('alipore.html')

@seo.route('/book-drivers-in-behala', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-behala', methods=['GET', 'POST'])
def behala():
    return render_template('behala.html')

@seo.route('/book-drivers-in-howrah', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-howrah', methods=['GET', 'POST'])
def howrah():
    return render_template('howrah.html')

@seo.route('/get-drivers-in-newtown', methods=['GET', 'POST'])
def newtown():
    return render_template('newtown.html')

@seo.route('/book-drivers-in-parkstreet', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-parkstreet', methods=['GET', 'POST'])
def parkstreet():
    return render_template('parkstreet.html')

@seo.route('/book-drivers-in-rajarhat', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-rajarhat', methods=['GET', 'POST'])
def rajarhat():
    return render_template('rajarhat.html')

@seo.route('/book-drivers-in-saltlake-city', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-saltlake-city', methods=['GET', 'POST'])
def saltlake():
    return render_template('saltlake-city.html')

@seo.route('/book-drivers-in-ballygunge', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-ballygunge', methods=['GET', 'POST'])
def ballygunge():
    return render_template('ballygunge.html')

@seo.route('/book-drivers-in-jadavpur', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-jadavpur', methods=['GET', 'POST'])
def jadavpur():
    return render_template('jadavpur.html')

@seo.route('/book-drivers-in-kalighat', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-kalighat', methods=['GET', 'POST'])
def kalighat():
    return render_template('kalighat.html')

@seo.route('/book-drivers-in-calcutta', methods=['GET', 'POST'])
@seo.route('/book-drivers-in-kolkata', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-calcutta', methods=['GET', 'POST'])
@seo.route('/get-drivers-in-kolkata', methods=['GET', 'POST'])
def kolkata():
    return render_template('kolkata.html')

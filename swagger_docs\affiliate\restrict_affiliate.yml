tags:
  - Affiliate
summary: Restrict or Unrestrict an Affiliate
description: >
  This endpoint allows an admin to restrict or unrestrict an affiliate by setting the 'enabled' status to false or true.
  If the affiliate is restricted, it will prevent further operations related to this affiliate.

parameters:
  - name: body
    in: body
    required: true
    description: JSON payload containing the client name and restriction status.
    schema:
      type: object
      properties:
        client_name:
          type: string
          description: The client name of the affiliate to be restricted or unrestricted.
        to_restrict:
          type: integer
          description: A flag to indicate whether to restrict (1) or unrestrict (0) the affiliate. Defaults to 1.
          example: 1
        regions:
          type: string
          description: A comma-separated list of region IDs for filtering.
    required: true

responses:
  '200':
    description: Affiliate restricted or unrestricted successfully.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: 1
            message:
              type: string
              example: Affiliate restricted successfully
  '400':
    description: Invalid or missing client name in the request body.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Client name is required
  '404':
    description: Affiliate not found in the database.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Affiliate not found
  '403':
    description: Unauthorized user attempting to restrict an affiliate.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: You are not authorized to restrict this affiliate
  '500':
    description: Server error while attempting to restrict or unrestrict the affiliate.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Failed to restrict affiliate
            error:
              type: string
              example: Error restricting affiliate

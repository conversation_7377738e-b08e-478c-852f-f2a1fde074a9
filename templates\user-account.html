<!DOCTYPE html>
<html>

<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
  	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
  	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/nav-common.css") }}">
	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/utility.css") }}">
	<link href="{{ url_for("static", filename="assets/css/hamburger.css") }}" rel="stylesheet">
    <link href="{{ url_for("static", filename="assets/css/country-code-picker.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/mobiscroll-lite/mobiscroll.jquery.lite.min.css") }}">
	<link href="{{ url_for("static", filename="assets/css/user-account.css") }}" rel="stylesheet">

  	<script src="https://code.jquery.com/jquery-3.2.1.min.js" integrity="sha256-hwg4gsxgFZhOsEEamdOYGBf13FyQuiTwlAQgxVSNgt4=" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/mobiscroll-lite/mobiscroll.jquery.lite.min.js") }}"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/nav-common.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/utility.js") }}"></script>
	<script src="https://wchat.freshchat.com/js/widget.js"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/freshchat.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/countryCodeHelper.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/user-account.js") }}"></script>

</head>

<body>
    <nav id="brandNav" class="navbar navbar-default">
      <button id="mainNavMenuButton" class="hamburger hamburger--arrow js-hamburger" type="button">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>
      <h5 id="pageTitle">Account Settings</h5>
        <div class="container-fluid">
            <div class="navbar-header">
                <a id="brand" class="navbar-brand brand-basic" href="#">
                    <img src="{{ url_for("static", filename="assets/images/brandLogoMerger.png") }}" alt="DRIVERS4ME">
                </a>
                <a id="brandBorder" class="navbar-brand brand-basic" href="#">
                    <img src="" alt="">
                </a>
            </div>
            <div class="ml-auto">
                <div class="nav navbar-nav">
                    <a id="userProfile" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/Avatar.svg") }}">
                    </a>
                    <a class="nav-item nav-link active nav-user-info-main no-padding" href="#">
                        <p id="userName" class="nav-user-info">Very very elongated User Name</p>
                        <p id="userContact" class="nav-user-info">8882012345</p>
                        <span class="sr-only">(current)</span>
                    </a>
                    <a id="logout" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/logout.svg") }}">
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div id="sidenavBackdrop" class="backdrop collapse"></div>
    <div id="mainNavMenu" class="side-nav subpage-sidenav">
        <div class="side-nav-header">
            <img id="userImageHolder" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}">
            <h6 id="userNameHolder">Sample User</h6>
            <h6 id="userContactHolder">88820 12345</h6>
        </div>
        <div class="side-nav-body">
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Bookings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Bookings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Payments.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Payments</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Refer.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Refer</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Settings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Settings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
        </div>
        <div class="side-nav-footer">
            <p class="copyright-statement">All rights reserved. <br> &copy; Copyright <span id="copyrightYear"></span></p>
        </div>
    </div>

	<div id="accountCentre">
		<div id="miniProfile" class="row account-setting">
            <div class="col-4 col-sm-3 col-md-3 col-lg-3">
                <img id="accountPicture" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}" alt="NO IMAGE">
            </div>
            <div class="col-8 col-sm-9 col-md-9 col-lg-9">
                <p class="account-detail" id="accountFullName">Very very very elongated User Name</p>
				<p class="account-detail" id="accountContact">94326 32424</p>
				<p class="account-detail" id="accountEmail">Email Unspecified</p>
            </div>
		</div>
		<div class="row custom-border"></div>
		<div id="editProfile" class="row account-setting">
			<p>Edit Profile</p>
		</div>
		<div class="row custom-border"></div>
		<div id="setEmergency" class="row account-setting">
			<p>Set Emergency Number</p>
		</div>
		<div class="row custom-border"></div>
		<div id="joinAsDriver" class="row account-setting">
			<p class="text-info">Join as a driver</p>&emsp;<p id="driverRegInfo" class="text-basic">(Join as a driver and earn)</p>
		</div>
		<div class="row custom-border"></div>
		<div id="signout" class="row account-setting">
			<p class="text-danger">Sign Out</p>
		</div>
		<div class="row custom-border"></div>
	</div>

    <!-- Emergency Number Modal -->
    <div class="modal" id="emergencyNumberModal">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                <h4 class="modal-title">Set Emergency Number</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="input-group">
                        <div id="ccSelect" class="input-group-prepend">
                            <span id="ccDisplay" class="input-group-text">
                                <div class="cc-picker-flag in"></div>
                                <span class="ccode">91</span>
                            </span>
                        </div>
                        <input id="inputEmergencyNumber" type="password" class="form-control" placeholder="">
                        <div class="input-group-append input-visibility-toggle">
                            <span class="input-group-text">
                                <i class="fa fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <ul id="cc-picker" class="mbsc-cloak collapse"></ul>
                    <div class="button-tray">
                        <button type="button" class="btn btn-outline-danger btn-sm" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success btn-sm">Confirm</button>
                    </div>
                    </div>
                </div>

            </div>
        </div>

    <!-- SNACKBAR -->
    <div id="snackbar"></div>

    <!-- LOADER -->
    <div id="loader" class="collapse">
        <div class="backdrop"></div>
        <div id="spinner-container">
            <div class="spinner">
                <div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div>
            </div>
        </div>
    </div>

</body>

</html>

from main import app
import sys
sys.path.append("/app/")
import datetime
from _utils import get_dt_ist
from models import db, Bookings, Trip, DriverDetails, DriverTrans
from _fcm import send_fcm_msg_driver
from _ops_message import send_slack_msg

NUM_TRIPS = 5
MONEY_THRESH = 10001
MONEY_TO_SEND = 30
SEND_ENABLED = True

def get_drivers_sales(start, end, n):
    res = {}
    all_trips_yesterday = db.session.query(Trip, Bookings). \
        filter(Trip.starttime >= start). \
        filter(Trip.starttime <= end). \
        filter(Bookings.id == Trip.book_id). \
        filter(Bookings.type < 50). \
        filter(Bookings.type != 2). \
        filter(Bookings.type != 5).all()
    for t in all_trips_yesterday:
        if t[1].driver not in res:
            res[t[1].driver] = 0
        res[t[1].driver] += t[0].price
    filtered_res = {key: value for key, value in res.items() if value >= n}
    return list(filtered_res.keys())

def get_drivers_n_trips(start, end, n):
    res = {}
    all_trips_yesterday = db.session.query(Trip, Bookings). \
        filter(Trip.starttime >= start). \
        filter(Trip.starttime <= end). \
        filter(Bookings.id == Trip.book_id). \
        filter(Bookings.type < 50). \
        filter(Bookings.type != 2).all()
    for t in all_trips_yesterday:
        if t[1].driver not in res:
            res[t[1].driver] = 0
        res[t[1].driver] += 1
    filtered_res = {key: value for key, value in res.items() if value >= n}
    return list(filtered_res.keys())

def send_money_to_wallet(driver_id, amount):
    details = db.session.query(DriverDetails). \
        filter(DriverDetails.driver_id == int(driver_id))
    driver_details = details.first()
    wallet = driver_details.wallet + amount
    withdrawable = driver_details.withdrawable
    dt = DriverTrans(driver_id, amount*100,
                     wall_a=wallet, wall_b=driver_details.wallet,
                     with_a=withdrawable, with_b=driver_details.withdrawable,
                     method="Daily bonus",
                     status=DriverTrans.COMPLETED, stop=True
                    )
    db.session.add(dt)
    details.update({DriverDetails.wallet: wallet, DriverDetails.withdrawable: withdrawable})
    send_slack_msg(0, "Driver id: " + str(driver_id) + " got bonus of " + str(amount))
    db.session.commit()
    send_fcm_msg_driver(int(driver_id), title="Wallet amount added!", smalltext="Your wallet had ₹" + str(amount) + " added",
                        bigtext="Your wallet amount was increased by ₹" + str(amount) + \
                        ". Your new wallet amount is ₹" + str(round(wallet + withdrawable, 2)) + \
                        " and withdrawable amount is ₹" + str(round(withdrawable, 2)) + ".")

if __name__ == '__main__':
    thresh_back = 1
    with app.app_context():
        try:
            if len(sys.argv) > 1:
                thresh_back = int(sys.argv[1])
        except Exception:
            pass
        trip_thresh = NUM_TRIPS
        sales_thresh = MONEY_THRESH
        m = MONEY_TO_SEND
        utc_now = datetime.datetime.utcnow()
        ist_yesterday = get_dt_ist(utc_now.date(), utc_now.time()) - datetime.timedelta(days=thresh_back)
        start_of_day = datetime.datetime(ist_yesterday.year, ist_yesterday.month, ist_yesterday.day, 0, 0, 0)
        end_of_day = datetime.datetime(ist_yesterday.year, ist_yesterday.month, ist_yesterday.day, 23, 59, 59)
        ist_offset = datetime.timedelta(hours=5, minutes=30)
        start_of_day_utc = start_of_day - ist_offset
        end_of_day_utc = end_of_day - ist_offset
        all_driver_n_trips = get_drivers_n_trips(start_of_day_utc, end_of_day_utc, trip_thresh)
        all_driver_wallet = get_drivers_sales(start_of_day_utc, end_of_day_utc, sales_thresh)
        all_drivers_bonus = list(set(all_driver_n_trips + all_driver_wallet))
        print("Payment for", str(start_of_day.date()))
        print("Found", len(all_drivers_bonus), "eligible for bonus")
        print(len(all_driver_n_trips), "eligible for trips,", len(all_driver_wallet), "eligible for sales")
        for driver in all_drivers_bonus:
            print("Sending", driver, "INR", MONEY_TO_SEND)
            if SEND_ENABLED:
                send_money_to_wallet(driver, MONEY_TO_SEND)

tags:
  - User
summary: Change User Email
description: This endpoint allows a user to change their email address.
parameters:
  - name: email
    in: formData
    type: string
    required: true
    description: New email address of the user
responses:
  200_a:
    description: Success - Email changed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Successfully changed Email ID"
    examples:
      application/json:
        success: 1
        message: "Successfully changed Email ID"
  401_a:
    description: Failed to get user identity
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  200_b:
    description: Driver cannot change email
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Driver can not change Email"
    examples:
      application/json:
        success: -2
        message: "Driver can not change Email"
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: -3
        message: "Incomplete form details"
  200_c:
    description: Invalid email format
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Invalid e-mail"
    examples:
      application/json:
        success: 0
        message: "Invalid e-mail"
  500:
    description: Database error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        error:
          type: string
          example: "Error description"
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -4
        error: "Error description"
        message: "DB Error"

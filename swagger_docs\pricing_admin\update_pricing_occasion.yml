tags:
  - Pricing_admin
summary: Update Pricing Details for a Specific Occasion
description: >
  This endpoint allows admins to update pricing details for a specific occasion within a city and trip type. Admin authorization is required.
parameters:
  - name: city
    in: formData
    required: true
    type: string
    description: The name of the city for which occasion-specific pricing details are being updated.
    example: "New York"
  - name: trip_type
    in: formData
    required: true
    type: string
    description: The type of trip (inCity or outStation).
    example: "inCity"
  - name: occasion
    in: formData
    required: true
    type: string
    description: The name of the occasion for which pricing details are being updated.
    example: "Christmas"
  - name: occasion_dates
    in: formData
    required: true
    type: string
    description: >
      Comma-separated list of dates for the occasion (e.g., "2023-12-24,2023-12-25").
    example: "2023-12-24,2023-12-25"
  - name: hatch_man
    in: formData
    required: true
    type: number
    description: The fare for hatchback manual cars.
    example: 30.00
  - name: sedan_man
    in: formData
    required: true
    type: number
    description: The fare for sedan manual cars.
    example: 35.00
  - name: suv_man
    in: formData
    required: true
    type: number
    description: The fare for SUV manual cars.
    example: 40.00
  - name: lux_man
    in: formData
    required: true
    type: number
    description: The fare for luxury manual cars.
    example: 50.00
  - name: hatch_auto
    in: formData
    required: true
    type: number
    description: The fare for hatchback automatic cars.
    example: 35.00
  - name: sedan_auto
    in: formData
    required: true
    type: number
    description: The fare for sedan automatic cars.
    example: 40.00
  - name: suv_auto
    in: formData
    required: true
    type: number
    description: The fare for SUV automatic cars.
    example: 45.00
  - name: lux_auto
    in: formData
    required: true
    type: number
    description: The fare for luxury automatic cars.
    example: 55.00
  - name: night_charge
    in: formData
    required: false
    type: number
    description: Night charge for inCity trips.
    example: 50.00
  - name: part_night_charge
    in: formData
    required: false
    type: number
    description: Part night charge for inCity trips.
    example: 25.00
  - name: min_travel_cost
    in: formData
    required: false
    type: number
    description: Minimum travel cost for inCity trips.
    example: 100.00
  - name: travel_cost
    in: formData
    required: false
    type: number
    description: Travel cost per km for inCity trips.
    example: 10.00
  - name: base_fare
    in: formData
    required: true
    type: number
    description: The base fare for the specified trip type.
    example: 150.00
  - name: booking_percent
    in: formData
    required: true
    type: number
    description: The booking percentage for the specified trip type.
    example: 10.0
  - name: extra_fare
    in: formData
    required: false
    type: number
    description: Extra fare for outStation trips.
    example: 20.00
responses:
  200:
    description: Successfully updated pricing details for the occasion
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        msg:
          type: string
          description: Success message
          example: "Occasion Christmas updated successfully for city New York"
  400:
    description: Request failed due to incomplete or invalid data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        msg:
          type: string
          description: Error message
          example: "Required data missing."
  404:
    description: Data not found for the specified city, trip type, or occasion
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3)
          example: -3
        msg:
          type: string
          description: Error message
          example: "City not found."
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

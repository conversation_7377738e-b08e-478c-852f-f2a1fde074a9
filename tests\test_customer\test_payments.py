from models import db, Users
from models import db, Users, UserTrans, DriverTrans, PaymentDataRP,DriverDetails
from datetime import datetime
import random

""" Test cases for `/api/user/credits/start` """

# Test case where JWT token is invalid
def test_invalid_credit_start_jwt(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {
        'amount': 1000
    }
    response = client.post('/api/user/credits/start', data=form_data)
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test case where the account is disabled
def test_account_credit_start_disabled(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()

    user = Users.query.filter_by(mobile=user.mobile).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'amount': 1000
    }

    response = client.post('/api/user/credits/start',data=form_data, headers=auth_headers)
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['success'] == -1

# Test case where 'amount' is not provided in the form data
def test_missing_amount(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {}

    response = client.post('/api/user/credits/start',data=form_data, headers=auth_headers)
    assert response.status_code == 201
    json_data = response.get_json()
    assert json_data['success'] == -3

# Simulate request with user role and RP gateway
def test_user_initiates_rp_gateway(client, customer_login):
    auth_headers, data = customer_login()
    
    form_data = {
        'amount': 1000,
        'gateway': 0
    }

    response = client.post('/api/user/credits/start',data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'trans_id' in res_data
    assert 'order_id' in res_data
    assert 'checksumhash' in res_data

# Simulate request with user role and RP gateway
def test_user_initiates_pt_gateway(client, customer_login):
    auth_headers, data = customer_login()
    
    form_data = {
        'amount': 1000,
        'gateway': 1
    }

    response = client.post('/api/user/credits/start',data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'trans_id' in res_data
    assert 'order_id' in res_data
    assert 'checksumhash' in res_data

def test_user_no_gateway(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {
        'amount': 1000
    }

    response = client.post('/api/user/credits/start',data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'trans_id' in res_data
    assert 'order_id' in res_data
    assert 'checksumhash' in res_data

def test_driver_initiates_rp_gateway(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    headers = {'Authorization': f'Bearer {access_token}'}

    form_data = {
        'amount': 1000
    }
    
    response = client.post('/api/user/credits/start', data=form_data, headers=headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'trans_id' in res_data
    assert 'order_id' in res_data
    assert 'checksumhash' in res_data

""" Test cases for api: `/api/user/credits/complete_rp` """

# Test case where JWT token is invalid
def test_invalid_complete_rp_jwt(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }

    response = client.post('/api/user/credits/fail_rp', data=form_data)
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test case where the account is disabled
def test_account_complete_rp_disabled(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 401
    assert response.json['success'] == -1

# Test case where 'order_id' is not provided in the form data
def test_missing_complete_rp_order_id(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {}

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    assert response.json['success'] == -2

# Test case where 'order_id' doesn't exist in the database for user
def test_invalid_order_id_for_user(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == -4 

# Test case where 'order_id' doesn't exist in the database for driver
def test_invalid_order_id_for_driver(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id = state['user_id']
    driver_id = state['driver_id']
    headers = {'Authorization': f'Bearer {access_token}'}

    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }
    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=headers)
    assert response.status_code == 200
    assert response.json['success'] == -4  


# Test case where payment failure is recorded for a driver
# def test_driver_payment_failure(client, driver_login):
#     state = driver_login
#     access_token = state['access_token']
#     user_id = state['user_id']
#     driver_id = state['driver_id']
#     headers = {'Authorization': f'Bearer {access_token}'}
#     driver_trans = DriverTrans('123', 100,with_b=100,with_a=100,wall_b=100,wall_a=100)
#     db.session.add(driver_trans)
#     db.session.flush()
#     payment_rp = PaymentDataRP(driver_trans.id, 'order_223')

#     db.session.add(payment_rp)
#     db.session.commit()

#     form_data = {
#         'order_id': 'order_223'
#     }

#     response = client.post('/api/user/credits/fail_rp', data=form_data, headers=headers)
#     assert response.status_code == 200
#     assert response.json['success'] == 1

""" Test cases for api: `/api/user/credits/fail_rp` """

# Test case where JWT token is invalid
def test_invalid__fail_rp_jwt(client, customer_login):
    auth_headers, data = customer_login()
    
    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }

    # Don't pass the JWT token to simulate an invalid token
    response = client.post('/api/user/credits/fail_rp', data=form_data)
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test case where the account is disabled
def test_account_fail_rp_disabled(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 401
    assert response.json['success'] == -1

# Test case where 'order_id' is not provided in the form data
def test_missing_fail_rp_order_id(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {}

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    assert response.json['success'] == -2

# Test case where 'order_id' doesn't exist in the database for user
def test_invalid_order_id_for_user(client, customer_login):
    auth_headers, data = customer_login()

    form_data = {
        'order_id': 'O18SRP2Tw5L9d0Iwu87II6gSYY5DE3cu7YPf29sPQ'
    }

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == -4

# Test case where payment failure is recorded for a user
def test_user_payment_fail_rp_failure(client, customer_login):
    auth_headers, data = customer_login()

    # Simulate the creation of a user transaction and a payment record
    user_trans = UserTrans('123', 100)
    db.session.add(user_trans)
    payment_rp = PaymentDataRP(user_trans.id, 'order_123')

    db.session.add(payment_rp)
    db.session.commit()

    form_data = {
        'order_id': 'order_123'
    }

    response = client.post('/api/user/credits/fail_rp', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: `/api/user/credits/view` """

# Test case where JWT token is invalid
def test_invalid_credits_view_jwt(client, customer_login):
    form_data = {}

    # Don't pass the JWT token to simulate an invalid token
    response = client.post('/api/user/credits/view', data=form_data)
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test case where the account is disabled
def test_account_credits_view_disabled(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {}

    response = client.post('/api/user/credits/view', data=form_data, headers=auth_headers)
    assert response.status_code == 401
    assert response.json['success'] == -1

# Test case where a valid user views their credit
def test_user_credit_view(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    # Simulate user having a credit balance and a referral code
    user.credit = 500.75
    user.ref_code = 'REF123'
    db.session.commit()

    form_data = {}

    response = client.post('/api/user/credits/view', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    res_data = response.get_json()

    assert res_data['success'] == 1
    assert res_data['credit'] == 500.75
    assert res_data['ref_code'] == 'REF123'


""" Test cases for api: `/api/user/credits/complete_pt` """

# Test case where JWT token is invalid
def test_invalid_complete_pt_jwt(client):
    form_data = {}

    # Don't pass the JWT token to simulate an invalid token
    response = client.post('/api/user/credits/complete_pt', data=form_data)
    assert response.status_code == 401
    json_data = response.get_json()
    assert json_data['result'] == 'FAILURE'

# Test case where the account is disabled
def test_account_complete_pt_disabled(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user.enabled = False
    db.session.commit()

    form_data = {
        "CHECKSUMHASH": "valid_checksum",
        "ORDERID": "valid_order_id"
    }

    response = client.post('/api/user/credits/complete_pt', data=form_data, headers=auth_headers)
    assert response.status_code == 401
    assert response.json['success'] == -1

# Test case where required fields are missing
def test_missing_fields(client, customer_login):
    auth_headers, _ = customer_login()

    # Missing CHECKSUMHASH and ORDERID
    form_data = {}

    response = client.post('/api/user/credits/complete_pt', data=form_data, headers=auth_headers)
    assert response.status_code == 201
    res_data = response.get_json()

    assert res_data['success'] == -2

# Test case where the transaction is not found
def test_transaction_not_found(client, customer_login):
    auth_headers, _ = customer_login()

    # Simulate form data with valid checksum and order ID
    form_data = {
        "CHECKSUMHASH": "valid_checksum",
        "ORDERID": "nonexistent_order_id"
    }

    response = client.post('/api/user/credits/complete_pt', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert res_data['success'] == -3
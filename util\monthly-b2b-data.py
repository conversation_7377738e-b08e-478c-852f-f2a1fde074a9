import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

current_date = datetime.now()
first_day_of_current_month = current_date.replace(day=1)
last_day_of_last_month = first_day_of_current_month - timedelta(days=1)
formatted_date = last_day_of_last_month.strftime("%B, %Y")

print(formatted_date)
filepathSP = D4M_UTIL_PATH + 'output/spinny.csv'
subjectSP = "Spinny Monthly Data - " + formatted_date
filepathBD = D4M_UTIL_PATH + 'output/bhandari.csv'
subjectBD = "Bhandari Monthly Data - " + formatted_date
filepathPH = D4M_UTIL_PATH + 'output/pridehonda.csv'
subjectPH = "PrideHonda Monthly Data - " + formatted_date
filepathPH = D4M_UTIL_PATH + 'output/everest.csv'
subjectPH = "Everest Monthly Data - " + formatted_date
content = "Please find Attached. "
filepathJY = D4M_UTIL_PATH + 'output/jyote.csv'
subjectJY = "Jyote Monthly Data - " + formatted_date
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>","<EMAIL>","<EMAIL>"]
send_mail(from_addr, to_addr_list, subjectSP, content, filepathSP)
send_mail(from_addr, to_addr_list, subjectBD, content, filepathBD)
send_mail(from_addr, to_addr_list, subjectPH, content, filepathPH)
send_mail(from_addr, to_addr_list, subjectJY, content, filepathJY)
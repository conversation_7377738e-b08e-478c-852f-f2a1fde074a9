tags:
  - Driver_admin
summary: Verify Driver's Aadhaar Document using OTP
description: >
  This endpoint verifies a driver's Aadhaar details using an OTP. It checks for the driver's ID, existing documents, and makes a request to the Aadhaar API to validate the OTP and retrieve Aadhaar details.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose Aadhaar document is to be verified
    example: 101
  - name: id_no
    in: formData
    required: true
    type: string
    description: The Aadhaar ID number for verification
    example: "1234-5678-9012"
  - name: otp
    in: formData
    required: true
    type: string
    description: The OTP received for Aadhaar verification
    example: "123456"
  - name: trans_id
    in: formData
    required: true
    type: string
    description: The transaction ID for tracking the request
    example: "trans_123456789"
responses:
  200:
    description: Successfully verified the driver's Aadhaar details
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the verification process (1 for successful verification, 0 for disputed)
          example: 1
        message:
          type: string
          description: Message describing the outcome of the verification
          example: "Aadhar Details Verified"
        details:
          type: object
          description: Verification details including name match and other relevant information
          properties:
            name_match:
              type: boolean
              description: Indicates whether the account holder's name matches
              example: true
            photo_match:
              type: boolean
              description: Indicates whether the photo matches
              example: true
            dob_match:
              type: boolean
              description: Indicates whether the date of birth matches
              example: true
            face_match_score:
              type: number
              description: The score of the face match
              example: 0.81
  400:
    description: Bad request (verification failed due to invalid OTP or missing driver information)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for invalid OTP, -9 for missing driver details)
          example: -1
        message:
          type: string
          description: Error message
          example: "Invalid Aadhaar Number or OTP."
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for database errors, -5 for unexpected errors)
          example: -1
        message:
          type: string
          description: Error message
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Unexpected error occurred"

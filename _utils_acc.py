from db_config import db
from models import Users, Drivers


def account_enabled(user):
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry:
        return user_entry.enabled
    else:
        return False

def validate_role(user, role, valid_roles=[]):
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry and user_entry.role == role:
        if role in valid_roles or len(valid_roles) == 0:
            return True
        else:
            return False
    else:
        return False

def get_driver_user_id(driver_id):
    if driver_id == 1:
        return -1
    driver_e = db.session.query(Drivers).filter(Drivers.id == driver_id).first()
    if not driver_e:
        return ""
    else:
        return driver_e.user


def get_driver_name_from_id(driver_id):
    if driver_id == 1:
        return "N/A"
    driver_e = db.session.query(Drivers, Users).filter(Drivers.id == driver_id). \
                    filter(Users.id == Drivers.user).first()
    if not driver_e:
        return ""
    else:
        return driver_e[1].get_name()


def get_user_name_from_id(user_id):
    u = db.session.query(Users).filter(Users.id == user_id).first()
    if not u:
        return ""
    else:
        return u.get_name()


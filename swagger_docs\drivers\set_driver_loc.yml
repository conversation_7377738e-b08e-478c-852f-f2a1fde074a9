tags:
  - Driver
summary: Set Driver Location
description: >
  This API allows a driver to update their current location by providing latitude and longitude. The driver must be authorized, and their account must be enabled.
parameters:
  - name: lat
    in: formData
    type: number
    required: true
    description: Latitude of the driver's current location.
  - name: lng
    in: formData
    type: number
    required: true
    description: Longitude of the driver's current location.
responses:
  200:
    description: Driver location updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for successful update)
          example: 1
        message:
          type: string
          description: Success message
          example: "Location updated successfully"
    examples:
      application/json:
        success: 1
        message: "Location updated successfully"
  400:
    description: Incomplete form data, missing latitude or longitude.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for incomplete form)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"
  401_a:
    description: Unauthorized access, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized access)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: User account restricted or driver not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted user or not found)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted or Driver not found"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: Database error occurred while updating the location.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for DB error)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

tags:
  - Affiliate_Wallet
summary: Retrieve Wallet Logs for Affiliate Representative
description: >
  This endpoint retrieves transaction logs for an affiliate representative based on filters such as transaction type and date range.
parameters:
  - name: affiliate_id
    in: query
    type: string
    required: false
    description: >
      Comma-separated affiliate IDs for which logs are requested. Defaults to the affiliate representative's ID if not provided.
  - name: transaction_type
    in: query
    type: string
    required: false
    description: Filter by transaction type (e.g., credit, debit).
  - name: start_date
    in: query
    type: string
    format: date
    required: false
    description: >
      Start date for filtering logs (ISO 8601 format, e.g., YYYY-MM-DD).
  - name: end_date
    in: query
    type: string
    format: date
    required: false
    description: >
      End date for filtering logs (ISO 8601 format, e.g., YYYY-MM-DD).
responses:
  200:
    description: Transaction logs retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        logs:
          type: array
          items:
            type: object
            properties:
              transaction_id:
                type: integer
                description: ID of the transaction.
                example: 101
              transaction_type:
                type: string
                description: Type of the transaction (e.g., credit, debit).
                example: "credit"
              affiliate_id:
                type: integer
                description: ID of the affiliate.
                example: 2001
              amount:
                type: number
                format: float
                description: Transaction amount.
                example: 50.25
              from_account:
                type: string
                description: Account initiating the transaction.
                example: "MainWallet"
              to_account:
                type: string
                description: Account receiving the transaction.
                example: "AffiliateWallet"
              wallet_after:
                type: number
                format: float
                description: Wallet balance after the transaction.
                example: 300.75
              timestamp:
                type: string
                format: date-time
                description: Timestamp of the transaction (ISO 8601 format).
                example: "2024-12-01T14:23:00Z"
              payment_id:
                type: string
                description: ID of the associated payment, if any.
                example: "PAY-12345"
  400:
    description: Invalid input parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message indicating the problem.
          example: "Invalid start_date format, use ISO 8601 (YYYY-MM-DD)."
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message indicating the issue.
          example: "Affiliate representative not found."
  500:
    description: Internal server error due to database or unexpected issues.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: General error message.
          example: "Database error"
        details:
          type: string
          description: Detailed error information for debugging.
          example: "IntegrityError: UNIQUE constraint failed."


tags:
  - Affiliate
summary: Upload a Zoomcar image for a driver.
description: This API allows a driver to upload an image for a Zoomcar booking.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The booking ID associated with the Zoomcar booking.
  - name: index
    in: formData
    type: integer
    required: true
    description: The index of the image being uploaded.
  - name: pic
    in: formData
    type: file
    required: true
    description: The picture file to be uploaded.
responses:
  200_a:
    description: Zoomcar image uploaded successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
    examples:
      application/json:
        success: 1
  201_a:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        msg:
          type: string
          example: "Incomplete form"
    examples:
      application/json:
        success: -2
        msg: "Incomplete form"
  401_a:
    description: Unauthorized role, Not Driver.
    schema:
      type: object
      properties:
        error:
          type: integer
          example: 1
    examples:
      application/json:
        error: 1
  401_b:
    description: User restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
    examples:
      application/json:
        success: -1
  500_a:
    description: DB Error while committing data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        reason:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        reason: "DB Error"
  500_b:
    description: Another DB error occurred during image upload.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        reason:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        reason: "DB Error"

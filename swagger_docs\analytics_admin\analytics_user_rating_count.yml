tags:
  - Admin Analytics
summary: Get Total Ratings and Average Rating
description: >
  This endpoint provides the total count of ratings across different rating categories (1-star to 5-star) and calculates the average rating for bookings within a specified date range. The results can also be filtered by region.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for the rating filter (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for the rating filter (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A comma-separated list of region IDs for filtering rating data. 
      Use '-1' to include all regions.
    example: "1,2,3"
responses:
  200:
    description: "Successfully retrieved total ratings and average rating data"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 for success)"
          example: 1
        data:
          type: array
          description: "List of rating counts and average rating"
          items:
            type: number
          example: [50, 40, 30, 20, 10, 0, 4.5, 150]
          description: >
            A list containing:
            - Count of 5-star ratings
            - Count of 4-star ratings
            - Count of 3-star ratings
            - Count of 2-star ratings
            - Count of 1-star ratings
            - Count of unrated bookings
            - Average rating
            - Total number of ratings
  400:
    description: "Invalid request or missing fields"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (-2 for invalid date, -4 for missing region)"
          example: -2
        error:
          type: string
          description: "Error message"
          example: "Missing or invalid date values"
  500:
    description: "Internal server error or database failure"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 for server error)"
          example: 0
        error:
          type: string
          description: "Error message"
          example: "Internal server error"

tags:
  - Affiliate
summary: Get Affiliate List
description: >
  This endpoint retrieves a paginated list of affiliates with optional search filters.
parameters:
  - name: page
    in: query
    required: false
    type: integer
    description: The page number for pagination (default is 1).
  - name: per_page
    in: query
    required: false
    type: integer
    description: Number of records per page (default is 30).
  - name: search_query
    in: query
    required: false
    type: string
    description: Query string for searching affiliates by client name, region, or ID.
  - name: region
    in: query
    required: true
    type: string
    description: comma seperated region.
  - name: search_by
    in: query
    required: false
    type: integer
    description: >
      Search criteria:  
      - 1 for Admin ID  
      - 2 for Client Name or Region.
  - name: restricted
    in: query
    required: false
    type: integer
    description: >
      Restriction flag:  
      - 0 for Enabled affiliates (default)  
      - 1 for Restricted affiliates.
responses:
  200:
    description: Successfully retrieved the affiliate list.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (1 for success).
        client_details:
          type: array
          description: A list of affiliate details.
          items:
            type: object
            properties:
              id:
                type: integer
                description: The unique identifier of the affiliate.
              client_name:
                type: string
                description: The name of the affiliate client.
              region:
                type: string
                description: The region of the affiliate client.
              enabled:
                type: boolean
                description: Indicates if the affiliate is enabled.
              registration_date:
                type: string
                format: date-time
                description: The registration date of the affiliate in IST format.
              type:
                type: string
                description: Indicates the type of the affiliate (Global or Inherited).
        total:
          type: integer
          description: Total number of affiliates matching the criteria.
        page:
          type: integer
          description: Current page number.
        per_page:
          type: integer
          description: Number of records per page.
        pages:
          type: integer
          description: Total number of pages.
  500:
    description: Failed to fetch affiliate details due to an error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (-1 for failure).
        error:
          type: string
          description: Error description.
        message:
          type: string
          description: Detailed error message.

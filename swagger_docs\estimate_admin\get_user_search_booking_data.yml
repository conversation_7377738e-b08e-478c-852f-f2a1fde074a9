tags:
  - Estimate_admin
summary: Get User Search Booking Data
description: >
  This endpoint retrieves booking data for users based on their search criteria. 
  Admins can filter the results by regions, pagination parameters, and the type of bookings. 
  It returns completed and upcoming booking counts, along with the latest search timestamps. 
  Only authorized admins can access this data.
parameters:
  - name: page
    in: formData
    required: false
    type: integer
    description: >
      The page number for pagination. Defaults to 1 if not provided.
    example: 1
  - name: per_page
    in: formData
    required: false
    type: integer
    description: >
      The number of records to return per page. Defaults to 10000 if not provided.
    example: 100
  - name: filter
    in: formData
    required: false
    type: integer
    description: >
      Filter option to sort results. 
      1 for latest search, 2 for earliest search. Defaults to 1.
    example: 1
  - name: regions
    in: formData
    required: false
    type: string
    description: >
      (Optional) Comma-separated list of region IDs to filter results.
    example: "1,2,3"
responses:
  200:
    description: Successfully retrieved user search booking data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: array
          description: Array of user booking search results
          items:
            type: object
            properties:
              userid:
                type: integer
                description: Unique ID of the user
                example: 123
              name:
                type: string
                description: Full name of the user
                example: "<PERSON>"
              contactNumber:
                type: string
                description: Contact number of the user
                example: "9876543210"
              region:
                type: integer
                description: Region ID associated with the user
                example: 2
              recentBookings:
                type: string
                description: The latest search timestamp in IST format
                example: "15 Oct, 24, 12:34:56"
              latestSearch:
                type: string
                description: The most recent search timestamp in IST format
                example: "15 Oct, 24, 12:34:56"
              completed:
                type: integer
                description: Count of completed bookings for the user
                example: 5
              upcoming:
                type: integer
                description: Count of upcoming bookings for the user
                example: 3
              remark:
                type: string
                description: Remarks related to the user
                example: "VIP customer"
        length:
          type: integer
          description: Total number of records in the response
          example: 10
  201:
    description: Required parameters are missing or incomplete
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  400:
    description: Bad request due to invalid parameters or filtering issues
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Detailed error message
          example: "Invalid region filter"
  500:
    description: Internal server error or unexpected exceptions
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        error:
          type: string
          description: Error message detailing the exception
          example: "An unexpected error occurred"

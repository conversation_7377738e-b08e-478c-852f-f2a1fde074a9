tags:
  - Affiliate_Wallet
summary: Complete Credit Payment for Affiliate Representative
description: >
  This endpoint allows an affiliate representative to complete a credit payment.
parameters:
  - name: payment_id
    in: formData
    type: string
    required: true
    description: The payment ID from the payment gateway.
responses:
  200:
    description: Payment captured and credit added successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        captured:
          type: integer
          description: Captured payment amount in cents.
          example: 10000
        balance:
          type: number
          format: float
          description: Updated wallet balance.
          example: 150.75
  401:
    description: Failed to get identity.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error).
          example: -1
        message:
          type: string
          description: Error message.
          example: "Failed to get identity."
  404:
    description: Affiliate representative or order not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message.
          example: "Affiliate representative not found."
  200:
    description: Payment failed to capture or unauthorized.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 to -7 depending on error type).
          example: -3
        message:
          type: string
          description: Error message.
          example: "Failed to fetch order."

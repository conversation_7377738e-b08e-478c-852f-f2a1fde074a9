tags:
  - Booking_admin
summary: Get booking estimate
description: >
  This endpoint calculates and retrieves an estimate for a booking based on the provided parameters.
parameters:
  - name: source
    in: formData
    type: string
    description: The source from where the request is made.
    example: "frontpage"
  - name: insurance
    in: formData
    type: integer
    description: Indicates if insurance is required (1 for yes, 0 for no).
    example: 0
  - name: ninsurance
    in: formData
    type: integer
    description: Indicates if non-insurance is applicable (1 for yes, 0 for no).
    example: 1
  - name: src_lat
    in: formData
    type: number
    format: float
    required: true
    description: The latitude of the source location.
    example: 22.5726
  - name: src_lng
    in: formData
    type: number
    format: float
    required: true
    description: The longitude of the source location.
    example: 88.3639
  - name: car_type
    in: formData
    type: string
    required: true
    description: The type of car for the booking.
    example: "Sedan"
  - name: dur
    in: formData
    type: string
    required: true
    description: The duration of the booking.
    example: "02:00:00"
  - name: time
    in: formData
    type: string
    required: true
    description: The time of the booking in various formats.
    example: "18/10/2024 03:00 PM +0530"
  - name: dest_lat
    in: formData
    type: number
    format: float
    description: The latitude of the destination location (optional).
    example: 22.5726
  - name: dest_lng
    in: formData
    type: number
    format: float
    description: The longitude of the destination location (optional).
    example: 88.3639
responses:
  200:
    description: Successfully retrieved booking estimate.
    schema:
      type: object
      properties:
        id:
          type: integer
          example: 1
        estimate:
          type: number
          format: float
          example: 500.00
  400:
    description: Bad request, missing or invalid parameters.
    schema:
      type: object
      properties:
        id:
          type: integer
          example: -1
        error:
          type: string
          example: "Missing required parameters"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        id:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"

from models import db, DriverTrans, DriverDetails

def convert():
    all_det = db.session.query(DriverDetails).filter(DriverDetails.owed != 0).all()
    print("Found", len(all_det), "entries")
    for d in all_det:
        id = d.driver_id
        owed = d.owed
        q = db.session.query(DriverDetails).filter(DriverDetails.driver_id == id)  
        if owed < 0:
            q.update({DriverDetails.withdrawable: -1 * round(owed, 0)})
            dt = DriverTrans(id, -owed*100,
                             wall_a=0, wall_b=0,
                             with_a=0, with_b=-1 * round(owed, 0),
                             method="Reconciliation",
                             status=DriverTrans.COMPLETED, stop=True
                            )
        elif owed > 0:
            q.update({DriverDetails.wallet: -1 * round(owed, 0)})
            dt = DriverTrans(id, -owed*100,
                             wall_a=0, wall_b=-1 * round(owed, 0),
                             with_a=0, with_b=0,
                             method="Reconciliation",
                             status=DriverTrans.COMPLETED, stop=True
                            )
        db.session.add(dt)
    db.session.commit()
        
if __name__ == '__main__':
    convert()
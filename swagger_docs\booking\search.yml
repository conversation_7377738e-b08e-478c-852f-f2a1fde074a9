tags:
  - Booking
summary: Register a search for a booking
description: >
  This API registers a new search based on the user's location and booking details.
parameters:
  - name: reflat
    in: formData
    type: string
    required: true
    description: Latitude of the reference point
  - name: reflong
    in: formData
    type: string
    required: true
    description: Longitude of the reference point
  - name: car_type
    in: formData
    type: string
    required: true
    description: Type of car
  - name: dur
    in: formData
    type: string
    required: true
    description: Duration of the booking
  - name: time
    in: formData
    type: string
    required: true
    description: Start time of the booking
  - name: type
    in: formData
    type: integer
    required: false
    description: Type of the booking (default is roundtrip)
  - name: region
    in: formData
    type: integer
    required: false
    description: Region for the booking
responses:
  200_a:
    description: Successful search registration with estimate
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag
          example: 1
        id:
          type: string
          description: Search ID
        min_est:
          type: number
          description: Minimum estimate
        max_est:
          type: number
          description: Maximum estimate
        base_ch:
          type: number
          description: Base charges
        night:
          type: number
          description: Night charge
        dist_ch:
          type: number
          description: Distance charge
        booking_ch:
          type: number
          description: Booking charge
        car_ch:
          type: number
          description: Car charge
        pretax:
          type: number
          description: Pre-tax amount
        cgst:
          type: number
          description: CGST
        sgst:
          type: number
          description: SGST
        insurance:
          type: number
          description: Insurance charges
        no_ins_price:
          type: number
          description: Price without insurance
        surcharge:
          type: number
          description: Surcharge amount
        ot_rate_0:
          type: number
          description: OT Rate 0
        ot_rate_1:
          type: number
          description: OT Rate 1
        ot_rate_2:
          type: number
          description: OT Rate 2
        night_1:
          type: number
          description: Part night flag
        night_2:
          type: number
          description: Night flag
        sgst_pct:
          type: number
          description: SGST percentage
        cgst_pct:
          type: number
          description: CGST percentage
        time:
          type: array
          items:
            type: number
          description: Time spent in processing
  200_b:
    description: Successful search registration for an outstation trip
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag
          example: 1
        id:
          type: string
          description: Search ID
        min_est:
          type: number
          description: Minimum estimate
        max_est:
          type: number
          description: Maximum estimate
        base_ch:
          type: number
          description: Base charges
        night:
          type: number
          description: Night charge (0 for outstation)
        dist_ch:
          type: number
          description: Distance charge
        booking_ch:
          type: number
          description: Booking charge
        car_ch:
          type: number
          description: Car charge
        pretax:
          type: number
          description: Pre-tax amount
        cgst:
          type: number
          description: CGST
        sgst:
          type: number
          description: SGST
        insurance:
          type: number
          description: Insurance charges
        no_ins_price:
          type: number
          description: Price without insurance
        ot_rate:
          type: array
          items:
            type: number
          description: OT rates
        sgst_pct:
          type: number
          description: SGST percentage
        cgst_pct:
          type: number
          description: CGST percentage
        time:
          type: array
          items:
            type: number
          description: Time spent in processing
  401_a:
    description: Failed to fetch identity
    schema:
      type: object
      properties:
        id:
          type: integer
          description: Failure flag
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to fetch identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        id:
          type: integer
          description: Failure flag
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        id:
          type: integer
          description: Failure flag
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
  400:
    description: Location is outside service area
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag
          example: -1
        message:
          type: string
          description: Error message
          example: "Location is outside service area"
  500:
    description: Database Error
    schema:
      type: object
      properties:
        id:
          type: integer
          description: Failure flag
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
  200_c:
    description: Invalid booking type or duration
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag
          example: -1
        id:
          type: integer
          description: Failure flag
          example: -1
        message:
          type: string
          description: Error message
          example: "Invalid booking type or booking duration"

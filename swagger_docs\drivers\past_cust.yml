tags:
  - Driver
summary: Fetch Past Customers for a Driver
description: >
  This endpoint allows a driver to retrieve the list of past customers for their bookings.
parameters:
  - name: llimit
    in: formData
    type: integer
    required: false
    description: Lower limit for pagination (default is 0)
  - name: ulimit
    in: formData
    type: integer
    required: false
    description: Upper limit for pagination (default is 170)
responses:
  200:
    description: Successfully retrieved past customers
    schema:
      type: array
      items:
        type: object
        properties:
          id:
            type: integer
            description: Booking ID
          name:
            type: string
            description: Name of the user
          startdate:
            type: string
            format: date
            description: Start date of the booking
          starttime:
            type: string
            format: time
            description: Start time of the booking
          enddate:
            type: string
            format: date
            description: End date of the booking
          endtime:
            type: string
            format: time
            description: End time of the booking
          dur:
            type: string
            description: Duration of the booking
          lat:
            type: float
            description: Latitude of the start location
          long:
            type: float
            description: Longitude of the start location
          estimate:
            type: number
            description: Estimated cost of the booking
          car_type:
            type: string
            description: Car type for the booking
          loc:
            type: string
            description: Pickup location
          mobile:
            type: string
            description: Mobile number of the customer
          trip_type:
            type: integer
            description: Type of the trip (e.g., one-way, outstation, etc.)
          days:
            type: integer
            description: Number of days for the booking
          driver_type:
            type: integer
            description: Driver type (permanent or temporary)
          payment:
            type: integer
            description: Payment type (e.g., cash, online, etc.)
          region:
            type: string
            description: Region of the booking
    examples:
      application/json:
        - id: 12345
          name: John Doe
          startdate: "2024-09-25"
          starttime: "09:00:00"
          enddate: "2024-09-26"
          endtime: "09:00:00"
          dur: "24:00:00"
          lat: 22.5726
          long: 88.3639
          estimate: 500
          car_type: "SUV"
          loc: "Salt Lake City"
          mobile: "9876543210"
          trip_type: 1
          days: 1
          driver_type: 1
          payment: 0
          region: "Kolkata"
  401_a:
    description: User not authorized
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not authorized"
    examples:
      application/json:
        success: -1
        message: "User not authorized"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: No past customers found
    schema:
      type: object
      properties:
        driver_id:
          type: integer
          description: Failure flag (-1 if no past customers are found)
          example: -1
        message:
          type: string
          description: Status message
          example: "No past customers found"
    examples:
      application/json:
        driver_id: -1
        message: "No past customers found"

tags:
  - User
summary: Set User Restore ID
description: >
  This endpoint allows an authenticated user to set or update their `restore_id`.
parameters:
  - name: restore_id
    in: formData
    type: string
    required: true
    description: Restore ID to be set for the user
responses:
  200_a:  # Success response when restore ID is set successfully
    description: Restore ID set successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Restore ID set successfully"
        restore_id:
          type: string
          description: The restore ID of the user
          example: "newRestoreID123"
    examples:
      application/json:
        success: 1
        message: "Restore ID set successfully"
        restore_id: "newRestoreID123"
  401_a:  # Unauthorized - User restricted
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:  # Internal server error - User not found
    description: User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -1
        message: "User not found"
  201:  # Incomplete form data
    description: Incomplete form data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"

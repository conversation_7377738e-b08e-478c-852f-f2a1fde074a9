tags:
  - Utilities_admin
summary: Fetch Admin Details
description: >
  This endpoint retrieves the details of an admin user, including their personal information 
  and access rights.
parameters:
  - name: user_id
    in: query
    required: true
    type: string
    description: The ID of the admin user whose details are being fetched.
    example: "admin_12345"
responses:
  200:
    description: Successfully retrieved admin details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        admin:
          type: object
          properties:
            fname:
              type: string
              description: First name of the admin user.
              example: "John"
            lname:
              type: string
              description: Last name of the admin user.
              example: "Doe"
            mobile:
              type: string
              description: Mobile number of the admin user.
              example: "+919876543210"
            email:
              type: string
              description: Email address of the admin user.
              example: "<EMAIL>"
            role:
              type: integer
              description: Role ID of the admin user.
              example: 2
            calling_number:
              type: string
              description: Calling number associated with the admin user.
              example: "+911234567890"
            agent_id:
              type: string
              description: Agent ID associated with the admin user.
              example: "agent_456"
            tab_access:
              type: string
              description: Comma-separated list of tab access IDs.
              example: "0,1,2"
            regions_access:
              type: string
              description: Comma-separated list of region access IDs.
              example: "1,2,3"
            notification_access:
              type: string
              description: Comma-separated list of notification access IDs.
              example: "0,1"
  404:
    description: User or admin access not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: 0
        message:
          type: string
          description: Error message detailing the issue.
          example: "User not found"
  500:  # Internal server error
    description: An error occurred while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: 0
        message:
          type: string
          description: Error message detailing the issue.
          example: "An error occurred while fetching admin details"

tags:
  - Driver
summary: Set the Analytics token for a driver.
description: >
  This API allows drivers to set their Analytics token for google adds on their devices. The driver must be authenticated and their account must be enabled to update the token.
parameters:
  - name: analytics_token
    in: formData
    type: string
    required: true
    description: The Google Analytics token of the driver’s device.
    example: "analytics_token_string_here"
  - name: device
    in: formData
    type: string
    required: true
    description: The device type (e.g., android or ios).
    example: "android"
responses:
  200:
    description: Analytics token added successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Analytics token added successfully"
    examples:
      application/json:
        success: 1
        message: "Analytics token added successfully"
  400:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: 0
        message: "Incomplete form details"
  401_a:
    description: Driver does not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Driver does not exist"
    examples:
      application/json:
        success: -1
        message: "Driver does not exist"
  401_b:
    description: Unauthorized role, not Driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_c:
    description: User restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"

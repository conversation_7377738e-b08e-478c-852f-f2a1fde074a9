tags:
  - User
summary: Change User Name
description: This endpoint allows a user to change their first and last name.
parameters:
  - name: fname
    in: formData
    type: string
    required: true
    description: First name of the user
  - name: lname
    in: formData
    type: string
    required: true
    description: Last name of the user
responses:
  200_a:
    description: Success - Name changed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Name changed successfully"
    examples:
      application/json:
        success: 1
        message: "Name changed successfully"
  401_a:
    description: Failed to get user identity
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  201:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
    examples:
      application/json:
        success: -3
  200_b:
    description: Incomplete form details (first name too short)
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: -5
        message: "Incomplete form details"
  500:
    description: Database error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        error:
          type: string
          example: "Error description"
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -4
        error: "Error description"
        message: "DB Error"

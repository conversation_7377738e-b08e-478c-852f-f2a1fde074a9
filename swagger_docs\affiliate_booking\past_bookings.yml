tags:
  - Affiliate_Booking
summary: Retrieve Past Bookings for Affiliate
description: >
  This endpoint retrieves past bookings for Affiliate based on the user's role, account status, and optional filtering criteria. 
  Only authorized roles can access this resource.
parameters:
  - name: day
    in: formData
    type: integer
    required: false
    description: >
      Day filter for retrieving past bookings. Used in combination with `month` and `year`.
      If not provided, retrieves past bookings without specific date filtering.
  - name: month
    in: formData
    type: integer
    required: false
    description: >
      Month filter for retrieving past bookings. Used in combination with `day` and `year`.
      If not provided, retrieves past bookings without specific date filtering.
  - name: year
    in: formData
    type: integer
    required: false
    description: >
      Year filter for retrieving past bookings. Used in combination with `day` and `month`.
      If not provided, retrieves past bookings without specific date filtering.
  - name: to_fetch
    in: formData
    type: integer
    required: false
    description: >
      Number of past bookings to retrieve. Defaults to 3 if not provided.
  - name: cancelled_by
    in: formData
    type: integer
    required: false
    description: >
      Filter for bookings cancelled by a specific entity. Defaults to -1 (no filter).
responses:
  200:
    description: Past bookings retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              booking_id:
                type: integer
                description: ID of the past booking.
                example: 789
              booking_date:
                type: string
                format: date-time
                description: Date and time of the past booking (ISO 8601 format).
                example: "2024-11-15T16:00:00Z"
              customer_name:
                type: string
                description: Name of the customer.
                example: "Alice Johnson"
              vehicle_model:
                type: string
                description: Model of the vehicle.
                example: "Hatchback"
              location:
                type: string
                description: Location of the past booking.
                example: "Suburban Parking Lot"
              cancelled_by:
                type: string
                description: Entity that cancelled the booking.
                example: "Customer"
        message:
          type: string
          description: Success message.
          example: "Past bookings retrieved successfully."
  400:
    description: Invalid input parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message indicating the problem.
          example: "Invalid input parameter."
  401:
    description: Unauthorized access due to invalid JWT or insufficient permissions.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized).
          example: -1
        message:
          type: string
          description: Error message indicating the issue.
          example: "Unauthorized"
  404:
    description: No past bookings found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        message:
          type: string
          description: Error message indicating no results were found.
          example: "No results found"
  500:
    description: Internal server error due to database or unexpected issues.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: General error message.
          example: "Database error"
        details:
          type: string
          description: Detailed error information for debugging.
          example: "IntegrityError: UNIQUE constraint failed."

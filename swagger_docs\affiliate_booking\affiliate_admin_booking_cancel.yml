tags:
  - Affiliate
summary: Cancel Booking with Charges for an Affiliate
description: >
  Cancels a booking identified by its code and applies the appropriate cancellation charges.
  Only users with the BOOKINGS_AFFILIATE tab may perform this action.
parameters:
  - in: formData
    name: book_code
    required: true
    type: string
    description: The unique code of the booking to cancel.
    example: "BK12345"
  - in: formData
    name: reason
    required: true
    type: integer
    description: Numeric code representing the cancellation reason/category.
    example: 1
  - in: formData
    name: regions
    required: true
    type: string
    description: comma seperated region.
    example: 1
responses:
  200:
    description: Booking cancelled successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Booking cancelled successfully"
        penalty:
          type: number
          example: 150.0
  400:
    description: Bad request or cancellation failed.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Incomplete Form Details"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Unauthorized"

tags:
  - Booking_admin
summary: Change Unallocation Reason
description: API to change the reason for unallocating a booking. This handles updating the cancellation reason and recalculating penalties.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: book_cancel_id
    in: formData
    type: integer
    required: true
    description: The ID of the cancelled booking.
  - name: new_reason
    in: formData
    type: integer
    required: true
    description: The new reason ID for the unallocation.
  - name: new_reason_detail
    in: formData
    type: string
    required: true
    description: Detailed explanation for the new reason.
responses:
  200:
    description: Success response indicating the unallocation reason was changed successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Changed Unallocate Reason Successfully
  400:
    description: Bad request due to incomplete form data or invalid conditions (e.g., booking already reversed or invalid type of cancellation).
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: 'Booking is not of Unallocation'
  500:
    description: Internal server error indicating a failure while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: 'Internal Server Error'

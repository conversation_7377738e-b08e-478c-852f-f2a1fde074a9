#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  payments.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON> Mitra

from datetime import datetime
import html
from flask import Blueprint, jsonify, request
from flask_jwt_extended import (
    jwt_required, get_jwt_identity, get_jwt
)

from flask import current_app as app
from _utils import complete, get_safe
import json, requests
from _utils_acc import account_enabled
from db_config import db
from models import Users, UserTrans, DriverTrans, PaymentDataRP, PaymentDataPT
from models import DriverDetails, Drivers
from razorpay import create_order, fetch_order, capture_payment
from flasgger import swag_from
from paytmchecksum import PaytmChecksum

payments = Blueprint('payments', __name__)

class PaymentType:
    PAY_CASH = 0
    PAY_D4M_CREDIT = 1
    PAY_PAYTM = 2
    PAY_BILL = 3

    PAYMENT_NAME_MAPPING = ['Cash', 'D4M-Credit', 'Paytm', 'Bill']

    GATEWAY_RP = 0
    GATEWAY_PAYTM = 1

    PAYTM_URL = "https://secure.paytmpayments.com/order/status"
    PAYTM_TEST_URL = "https://securegw-stage.paytm.in/order/status"

    CAPTURE_TIMEPERIOD = 15

    @staticmethod
    def get_paytm_url():
        if app.config['MAIN_SERVER']:
            return PaymentType.PAYTM_URL
        else:
            return PaymentType.PAYTM_TEST_URL

    @staticmethod
    def set_payment_type(type):
        if int(type) < 3 and int(type) >= 0:
            return int(type)
        else:
            return PaymentType.PAY_CASH

@payments.route('/api/user/credits/start', methods=['POST'])
@swag_from('/app/swagger_docs/payments/initiate_credit_order.yml')
@jwt_required()
def initiate_credit_order():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message':'Failed to get identity'}), 401

    claims = get_jwt()
    if not account_enabled(user):
        print("-1")
        return jsonify({'success': -1, 'message':'User restricted'}), 401
    if not complete(request.form, ['amount']):
        print("-3")
        return jsonify({'success': -3, 'message':'Incomplete form details'}), 201
    amt = int(request.form['amount'])
    print(amt)
    if 'gateway' not in request.form:
        gateway = PaymentType.GATEWAY_RP
    else:
        gateway = int(request.form['gateway'])

    is_driver = claims['roles'] == Users.ROLE_DRIVER
    print("is driver")
    if not is_driver:
        trans = UserTrans(user, amt)
    else:
        driver_entry = db.session.query(Drivers).filter(Drivers.user == user).first()
        driver_id = driver_entry.id
        if driver_entry.approved < 1:
            return jsonify({'success': -1, 'message':'Driver not approved'}), 401

        trans = DriverTrans(driver_id, amt)
    order_id = ""
    if gateway == PaymentType.GATEWAY_RP:
        order_amount = amt
        order_currency = "INR"
        order_receipt = trans.id

        # Function to create order with basic details such as amount and currency
        resp_obj = create_order(order_amount, order_currency, order_receipt)
        if resp_obj.status_code != 200:
            return jsonify({'order_id': -4, 'message':'Razorpay payment order failed'})
        resp = resp_obj.json()
        order_id = resp['id']
        pdr = PaymentDataRP(trans.id, order_id)
        checksum = order_id
        txnToken = ""
    elif gateway == PaymentType.GATEWAY_PAYTM:
        if is_driver:
            return jsonify({'order_id': -3, 'message':'User is driver, not allowed'})
        trans = UserTrans(user, amt)
        # initialize JSON String
        user_obj = db.session.query(Users).filter(Users.id == user).first()
        paytmParams = dict()

        paytmParams["body"] = {
            "requestType"   : "Payment",
            "mid"           : app.config['PAYTM_MID'],
            "websiteName"   : "DEFAULT",  # ← Use this for staging!
            "orderId"       : trans.id,
            "callbackUrl"   : f"https://secure.paytmpayments.com/theia/paytmCallback?ORDER_ID=+{trans.id}",  # your own HTTPS endpoint
            "txnAmount"     : {
                "value"     : "{:.2f}".format(amt / 100),  # Convert paise to rupees
                "currency"  : "INR",
            },
            "userInfo"      : {
                "custId"    : str(user_obj.id),
                "mobile"    : str(user_obj.mobile),   # key is 'mobile', not 'mobile_no'
                "email"     : str(user_obj.email),
            },
        }
        checksum = PaytmChecksum.generateSignature(json.dumps(paytmParams["body"]), app.config['PAYTM_SECRET'])
        paytmParams["head"] = {
            "signature"    : checksum
        }
        url = f"https://securegw.paytm.in/theia/api/v1/initiateTransaction?mid={app.config['PAYTM_MID']}&orderId={trans.id}"
        response = requests.post(url, json=paytmParams, headers={"Content-type": "application/json"})
        resp_json = response.json()
        print(json.dumps(paytmParams, indent=2))
        print("Full response from Paytm:", resp_json,flush=True)  # Add this!
        txnToken = resp_json.get('body', {}).get('txnToken')
        order_id = checksum
        pdp = PaymentDataPT(trans.id, order_id)
    try:
        db.session.add(trans)
        if gateway == PaymentType.GATEWAY_RP:
            db.session.add(pdr)
        elif gateway == PaymentType.GATEWAY_PAYTM:
            db.session.add(pdp)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'order_id': -2, 'message':'DB Error'})
    return jsonify({'trans_id': trans.id, 'order_id': order_id, 'checksumhash': checksum, 'txnToken': txnToken if txnToken is not None else '','message':'Initiated credit order successfully'})

@payments.route('/api/user/credits/fail_rp', methods=['POST'])
@swag_from('/app/swagger_docs/payments/initiate_credit_order.yml')
@jwt_required()
def fail_credit_trans():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message':'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message':'User restricted'}), 401
    if not complete(request.form, ['order_id']):
        return jsonify({'success': -2, 'message':'Incomplete form details'}), 201
    oid = request.form['order_id']
    claims = get_jwt()
    is_driver = claims['roles'] == Users.ROLE_DRIVER
    if not is_driver:
        order_trans = db.session.query(PaymentDataRP, UserTrans).filter(PaymentDataRP.order_id == oid). \
                                filter(PaymentDataRP.trans_id == UserTrans.id).first()
        u = db.session.query(Users).filter(Users.id == user)
        if not order_trans:
            return jsonify({'success': -4, 'message':'Order transaction not present'}), 200  # Bad oid
        db.session.query(UserTrans).filter(UserTrans.id == order_trans[1].id). \
                            update({UserTrans.stop_timestamp: datetime.utcnow(),
                                    UserTrans.status: UserTrans.FAILED,
                                    UserTrans.method: "Razorpay - Failed",
                                    UserTrans.wallet_after: u.first().credit,
                                    UserTrans.wallet_before: u.first().credit})
    else:
        order_trans = db.session.query(PaymentDataRP, DriverTrans).filter(PaymentDataRP.order_id == oid). \
                                filter(PaymentDataRP.trans_id == DriverTrans.id).first()
        driver_user = user
        driver_id = db.session.query(Drivers).filter(Drivers.user == driver_user).first().id
        ddq = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id)
        dd = ddq.first()
        if not order_trans:
            return jsonify({'success': -4, 'message':'Order transaction not present'}), 200  # Bad oid
        db.session.query(DriverTrans).filter(DriverTrans.id == order_trans[1].id). \
                            update({DriverTrans.stop_timestamp: datetime.utcnow(),
                                    DriverTrans.status: UserTrans.FAILED,
                                    DriverTrans.withdrawable_before: dd.withdrawable,
                                    DriverTrans.withdrawable_after: dd.withdrawable,
                                    DriverTrans.wallet_before: dd.wallet,
                                    DriverTrans.wallet_after: dd.wallet,
                                    DriverTrans.method: "Recharge - Failed"})

    db.session.query(PaymentDataRP).filter(PaymentDataRP.order_id == oid). \
                        update({PaymentDataRP.status: "failed",
                                PaymentDataRP.description: "Credit recharge failed",
                                PaymentDataRP.amount: order_trans[1].amount,
                                PaymentDataRP.timestamp: datetime.utcnow()})
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -3, 'message':'DB Error'})
    return jsonify({'success': 1, 'message':'Transaction failed updated'}), 200


@payments.route('/api/user/credits/complete_rp', methods=['POST'])
@swag_from('/app/swagger_docs/payments/initiate_credit_order.yml')
@jwt_required()
def add_d4m_credit():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message':'Failed to get indentity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message':'User restricted'}), 401
    if not complete(request.form, ['payment_id']):
        return jsonify({'success': -2, 'message':'Incomplete form details'}), 201
    pid = request.form['payment_id']
    claims = get_jwt()
    is_driver = claims['roles'] == Users.ROLE_DRIVER

    # Function to retrieve specific payments using its id
    resp_obj = fetch_order(pid)
    if  resp_obj.status_code != 200:
        return jsonify({'success': -3, 'message':'Failed to fetch order'}), 200
    resp = resp_obj.json()

    if resp['order_id']:
        trans_table = UserTrans if not is_driver else DriverTrans
        order_trans = db.session.query(PaymentDataRP, trans_table).filter(PaymentDataRP.order_id == resp['order_id']). \
                                filter(PaymentDataRP.trans_id == trans_table.id).first()
        oid = order_trans[0].order_id
        if not oid:
            # This is bad, could be malicious. Return a failure.
            return jsonify({'success': -4, 'message':'Order id not exist'}), 200  # Bad oid
    else:
        # boom
        return jsonify({'success': -5, 'message':'Fetched order do not have order_id'}), 200  # Bad oid
    if resp['status'] == 'authorized':
        # Capture amount in payment API but complain if order has diff amount
        payment_amount = resp['amount']
        currency = "INR"

        # Function to capture payment using paymentID
        cap_obj = capture_payment(pid, payment_amount, currency)
        if cap_obj.status_code != 200:
            return jsonify({'success': -5, 'message':'Failed to capture payment'}), 200
        cap = cap_obj.json()
        if cap['status'] != 'captured':
            return jsonify({'success': -6, 'message':'Amount could not be captured'}), 200  # Amount could not be captured
        try:
            db.session.query(PaymentDataRP).filter(PaymentDataRP.order_id == oid). \
                        update({PaymentDataRP.payment_id: pid, PaymentDataRP.amount: cap['amount'],
                                PaymentDataRP.description: html.escape(cap['description']),
                                PaymentDataRP.method: html.escape(cap['method']), PaymentDataRP.status: html.escape(cap['status']),
                                PaymentDataRP.timestamp: datetime.fromtimestamp(cap['created_at'])})
            u = db.session.query(Users).filter(Users.id == user)
            if not is_driver:
                db.session.query(UserTrans).filter(UserTrans.id == order_trans[1].id). \
                            update({UserTrans.stop_timestamp: datetime.utcnow(),
                                    UserTrans.status: UserTrans.status + 1,
                                    UserTrans.method: "Razorpay",
                                    UserTrans.wallet_after:u.first().credit+ cap['amount'] / 100,
                                    UserTrans.wallet_before: u.first().credit})  # +1 shifts to appropriate success level
                u.first().credit += cap['amount'] / 100
                db.session.commit()
            else:
                driver_user = u.first().id
                driver_id = db.session.query(Drivers).filter(Drivers.user == driver_user).first().id
                ddq = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id)
                dd = ddq.first()
                db.session.query(DriverTrans).filter(DriverTrans.id == order_trans[1].id). \
                        update({DriverTrans.stop_timestamp: datetime.utcnow(),
                                DriverTrans.status: DriverTrans.status + 1,
                                DriverTrans.method: "Recharge",
                                DriverTrans.withdrawable_before: dd.withdrawable,
                                DriverTrans.withdrawable_after: dd.withdrawable,
                                DriverTrans.wallet_before: dd.wallet,
                                DriverTrans.wallet_after: dd.wallet + cap['amount'] / 100,
                                })  # +1 shifts to appropriate success level
                ddq.update({DriverDetails.wallet: DriverDetails.wallet + cap['amount'] / 100})

            db.session.commit()
        except Exception as e:
            print(e)
            return jsonify({'success': -8, 'message':'DB Error'}), 200  # Failed on DB

        if is_driver:
            driver_detail = db.session.query(DriverDetails, Drivers). \
                                filter(DriverDetails.driver_id == Drivers.id). \
                                filter(Drivers.user == driver_user).first()[0]
            return jsonify({'success': 1, 'captured': payment_amount,
                            'wallet': driver_detail.wallet,
                            'withdrawable': driver_detail.withdrawable})
        else:
            return jsonify({'success': 1, 'captured': payment_amount,
                            'balance': u.first().credit})
    else:
        return jsonify({'success': -7, 'message':'Not authorized, retry payment'}), 200  # Not authorized, retry payment


@payments.route('/api/user/credits/view', methods=['POST'])
@swag_from('/app/swagger_docs/payments/view_d4m_credit.yml')
@jwt_required()
def view_d4m_credit():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message':'Failed to get identity'}), 401
    if not account_enabled(user):
        return jsonify({'success': -1, 'message':'User restricted'}), 401
    claims = get_jwt()
    is_driver = claims['roles'] == Users.ROLE_DRIVER
    if not is_driver:
        user = db.session.query(Users).filter(Users.id == user).first()
        credit = user.credit
        ref_code = user.ref_code
        return jsonify({'success': 1, 'credit': round(credit, 2), 'ref_code': ref_code}),201
    else:
        driver_details = db.session.query(DriverDetails, Drivers). \
                            filter(DriverDetails.driver_id == Drivers.id). \
                            filter(Drivers.user == user).first()[0]
        return jsonify({'success': 1, 'wallet': round(driver_details.wallet, 2),
                        'withdrawable': round(driver_details.withdrawable, 2)}),201

@payments.route('/api/user/credits/alter', methods=['POST'])
@jwt_required()
def alter_d4m_credit():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()

    is_driver = claims['roles'] == Users.ROLE_DRIVER
    amt = int(get_safe(request.form, 'amount', 0))
    if not is_driver:
        credit = db.session.query(Users).filter(Users.id == user).first().credit
        new_cred = credit + amt
        trans = UserTrans(user, amt, method="Manual", status=UserTrans.COMPLETED, stop=True, wall_b=credit, wall_a=new_cred)
        db.session.query(Users).filter(Users.id == user).update({Users.credit: new_cred})
        db.session.add(trans)
    else:
        driver_id = db.session.query(Drivers).filter(Drivers.user == user).first().id
        driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id)
        cur_wallet, cur_withdrawable = driver_details.first().wallet, driver_details.first().withdrawable
        new_wallet = cur_wallet + amt
        trans = DriverTrans(driver_id, amt,
                         wall_a=new_wallet, wall_b=cur_wallet,
                         with_a=cur_withdrawable, with_b=cur_withdrawable,
                         method="Admin",
                         status=DriverTrans.COMPLETED, stop=True
                        )
        driver_details.update({DriverDetails.wallet: new_wallet})
        db.session.add(trans)
    db.session.commit()
    return jsonify({'success': 1, 'credit': round(new_cred, 2)})


@payments.route('/api/user/credits/complete_pt', methods=['POST'])
@swag_from('/app/swagger_docs/payments/verify_paytm_trans.yml')
@jwt_required()
def verify_paytm_trans():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1, 'message': 'Failed to get user identity'}), 401

    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401

    if not complete(request.form, ['CHECKSUMHASH', 'ORDERID']):
        return jsonify({'success': -2, 'message': 'Incomplete form details'}), 201

    tid = request.form['ORDERID']
    checksum = request.form['CHECKSUMHASH']

    order_trans = db.session.query(PaymentDataPT, UserTrans).filter(
        PaymentDataPT.trans_id == tid,
        PaymentDataPT.trans_id == UserTrans.id
    ).first()

    if not order_trans:
        return jsonify({'success': -3, 'message': 'Transaction not found'})

    if order_trans[1].status != UserTrans.INITIATED:
        return jsonify({'success': -3, 'message': 'Transaction failed or already completed'})

    params = request.form.to_dict(flat=True)
    isValidChecksum = PaytmChecksum.verifySignature(params, app.config['PAYTM_SECRET'], checksum)

    if not isValidChecksum:
        return jsonify({'success': -5, 'message': 'Checksum validation failed'}), 200

    # Prepare request to verify transaction with Paytm
    url = PaymentType.get_paytm_url()
    paytmParams = {
        "MID": app.config['PAYTM_MID'],
        "ORDERID": tid
    }

    post_data = json.dumps(paytmParams)

    try:
        response = requests.post(url, data=post_data, headers={"Content-type": "application/json"})
        if 'application/json' in response.headers.get('Content-Type', ''):
            cap = response.json()
        else:
            return jsonify({'success': -6, 'message': 'Non-JSON response from Paytm', 'raw': response.text}), 500
    except requests.exceptions.RequestException as e:
        return jsonify({'success': -7, 'message': 'Error connecting to Paytm', 'error': str(e)}), 500
    except ValueError:
        return jsonify({'success': -8, 'message': 'Invalid JSON in Paytm response', 'raw': response.text}), 500

    if cap.get("STATUS") == "TXN_SUCCESS":
        amount_paytm = int(float(cap['TXNAMOUNT']) * 100)
        if amount_paytm != order_trans[1].amount:
            return jsonify({'success': -4, 'message': 'Transaction amount mismatch'}), 200

        u = db.session.query(Users).filter(Users.id == user).first()

        db.session.query(UserTrans).filter(UserTrans.id == order_trans[1].id).update({
            UserTrans.stop_timestamp: datetime.utcnow(),
            UserTrans.status: UserTrans.status + 1,
            UserTrans.method: "Paytm recharge",
            UserTrans.wallet_before: u.credit,
            UserTrans.wallet_after: u.credit + amount_paytm / 100
        })

        db.session.query(PaymentDataPT).filter(PaymentDataPT.trans_id == tid).update({
            PaymentDataPT.payment_id: cap['TXNID'],
            PaymentDataPT.amount: amount_paytm,
            PaymentDataPT.description: html.escape(cap.get('RESPMSG', '')),
            PaymentDataPT.method: html.escape(cap.get('PAYMENTMODE', '')),
            PaymentDataPT.status: html.escape(cap['STATUS']),
            PaymentDataPT.timestamp: cap['TXNDATE']
        })

        u.credit += amount_paytm / 100
        db.session.commit()

        return jsonify({
            'success': 1,
            'captured': amount_paytm,
            'balance': u.credit,
            'message': 'Transaction completed successfully'
        })

    return jsonify({'success': 0, 'message': 'Transaction failed or incomplete', 'response': cap})

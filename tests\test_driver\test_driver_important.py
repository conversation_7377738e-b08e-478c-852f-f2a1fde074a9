import pytest
from flask import Flask
from models import Users, Drivers, db,AdminAccess,UserToken,DriverVerify,Bookings,BookDest,Trip,UserTrans,DriverLoc,DriverDetails,BookPending,BookPricing,SpinnyBookings,SpinnyRep,DriverSearch
from _utils import get_pwd,get_salt
import random
import hashlib
import jsonpickle
import json
import datetime
from sqlalchemy import exc
from booking_params import BookingParams
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token


 #  API - /api/driver/pending_cust

def test_pending_cust_account_disabled(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.enabled = False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/driver/pending_cust', headers=headers)

    assert response.status_code == 401
    assert response.json['success'] == -1

def test_pending_cust_driver_with_no_bookings(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/driver/pending_cust', headers=headers)

    assert response.status_code == 200
    assert response.json =={'driver_id': - 1, 'message': 'No pending customers found'}



def test_pending_cust_success(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']

    try:
        booking = Bookings(
        user=user_id,
        skey='some_secret_key',
        driver=driver_id,
        lat=0.0,
        long=0.0,
        starttime=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.datetime.utcnow() + datetime.timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
        booking.valid=1
        db.session.add(booking)
        db.session.commit()
        dest = BookDest(booking.id, 0.0, 0.0, 'Test Destination')
        db.session.add(dest)

        db.session.commit()
    except Exception as e:
        db.session.rollback()


    headers = {
        'Authorization': f"Bearer {access_token}"
    }

    # Send the request to get pending customers
    response = client.post('/api/driver/pending_cust', headers=headers)

    assert response.status_code == 200
    assert isinstance(response.json, list)
    assert len(response.json) > 0  # There should be at least one booking

 # ---------------------------------

#  API - /api/driver/list_confirm

def test_list_confirm_invalid_role(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.role=Users.ROLE_USER
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/driver/list_confirm', headers=headers)

    assert response.status_code == 401
    assert response.json['success'] == -1

def test_list_confirm_account_disabled(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.enabled=False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = client.post('/api/driver/list_confirm',headers=headers)
    assert response.status_code == 401
    assert response.json['success'] == -1

def test_list_confirm_no_bookings(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }


    response = client.post('/api/driver/list_confirm', headers=headers)

    assert response.status_code == 200
    assert response.json == {'success': - 1, 'message': 'No confirmed bookings found'}


def test_list_confirm_with_bookings(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    current_time = datetime.datetime.utcnow()
    curdate = current_time.strftime("%Y-%m-%d")

    # Create a Booking for the roundtrip
    try:
        booking = Bookings(
        user=user_id,
        skey='test_key',
        driver=driver_id,
        lat=12.34,
        long=56.78,
        starttime=(current_time + datetime.timedelta(hours=2)).strftime("%H:%M:%S"),
        startdate=curdate,
        dur="02:00:00",
        endtime=(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
        enddate=curdate,
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        type=BookingParams.TYPE_ROUNDTRIP
        )

        db.session.add(booking)
        db.session.commit()

        # Create BookPending for the booking
        book_pending = BookPending(
            bid=booking.id,
            did=driver_id,
            state=1,  # Mark as valid
            phase=BookPending.BROADCAST
        )
        db.session.add(book_pending)
        db.session.commit()

        # Create BookPricing for the booking
        book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
        db.session.add(book_pricing)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = client.post('/api/driver/list_confirm',headers=headers)
    print(response.json)
    assert response.status_code == 200
    assert len(response.json) > 0
        # Assert that the query returned results as expected
    assert len(response.json) == 1  # Only one entry should match
    for booking in response.json:
        # Decode the JSON using json.loads
        assert booking['trip_type'] == BookingParams.TYPE_ROUNDTRIP

def test_list_confirm_with_bookings_b2b(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    current_time = datetime.datetime.utcnow()
    curdate = current_time.strftime("%Y-%m-%d")
    curtime = current_time.strftime("%H:%M:%S")

    # Create a Booking for the spinny
    try:
        booking = Bookings(
        user=user_id,
        skey='test_key',
        driver=driver_id,
        lat=12.34,
        long=56.78,
        starttime=(current_time + datetime.timedelta(hours=2)).strftime("%H:%M:%S"),
        startdate=curdate,
        dur="02:00:00",
        endtime=(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
        enddate=curdate,
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        type=BookingParams.TYPE_SPINNY
        )

        db.session.add(booking)
        db.session.commit()

        spinny_rep = SpinnyRep(
            name="Test Rep",
            user=random.randint(1, 100),
            mob=random.randint(7000000000, 9999999999),
            regn=0,
        )
        db.session.add(spinny_rep)
        db.session.commit()

        # Create a random SpinnyBookings entry
        spinny_booking = SpinnyBookings(
            booking.id,
            "APT1234",
            "ABC1234",
            "Model X",
            random.choice([1, 2, 3]),  # Random trip type
            "Trip Type Detail",  # Example trip type detail
            "Business Function",  # Example business function
        "Business Category",  # Example business category
        random.randint(7000000000, 9999999999),  # Random drop mobile number
            spinny_rep.id,
            0,
        )
        db.session.add(spinny_booking)
        db.session.commit()
        # Create BookPending for the booking
        book_pending = BookPending(
            bid=booking.id,
            did=driver_id,
            state=1,  # Mark as valid
            phase=BookPending.BROADCAST
        )
        db.session.add(book_pending)
        db.session.commit()

        book_dest=BookDest(booking.id,22,77,"Kolkata")
        db.session.add(book_dest)
        db.session.commit()
        # Create BookPricing for the booking
        book_pricing = BookPricing(
            bid=booking.id,
            est=100,
            base=50,
            cartype=10,
            night=0,
            food=0,
            booking=10,
            dist=30,
            cgst=5,
            sgst=5,
            est_pre_tax=95,
            insurance=0
        )
        db.session.add(book_pricing)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = client.post('/api/driver/list_confirm',headers=headers)
    assert response.status_code == 200
    assert len(response.json) > 0
        # Assert that the query returned results as expected
    assert len(response.json) == 1  # Only one entry should match

    for booking in response.json:
        # Decode the JSON using json.loads
        assert booking['trip_type'] == BookingParams.TYPE_SPINNY

 # ---------------------------------

#  API - /api/driver/ongoing

def test_ongoing_driver_invalid_role(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.role=Users.ROLE_USER
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/driver/ongoing', headers=headers)

    assert response.status_code == 401
    assert response.json['success'] == -1

def test_ongoing_driver_not_trip(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    current_time = datetime.datetime.utcnow()
    curdate = current_time.strftime("%Y-%m-%d")
    try:
        booking = Bookings(
        user=user_id,
        skey='test_key',
        driver=driver_id,
        lat=12.34,
        long=56.78,
        starttime=(current_time + datetime.timedelta(hours=2)).strftime("%H:%M:%S"),
        startdate=curdate,
        dur="02:00:00",
        endtime=(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
        enddate=curdate,
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        type=BookingParams.TYPE_ROUNDTRIP
        )

        db.session.add(booking)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/driver/ongoing', headers=headers)

    assert response.status_code == 200
    assert response.json['trip_id'] == -1


def test_ongoing_driver_trip_success(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    current_time = datetime.datetime.utcnow()
    try:
        ds=DriverSearch('tezt',user_id,0,22,77,(current_time-datetime.timedelta(hours=2)).strftime("%H:%M:%S"),datetime.datetime.utcnow().strftime("%Y-%m-%d"),
                        1,current_time)
        db.session.add(ds)
        db.session.commit()

        curdate = current_time.strftime("%Y-%m-%d")
        booking = Bookings(
            user=user_id,
            skey="tezt",
            driver=driver_id,
            lat=12.34,
            long=56.78,
            starttime=(current_time - datetime.timedelta(hours=2)).strftime("%H:%M:%S"),
            startdate=curdate,
            dur="02:00:00",
            endtime=(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
            enddate=curdate,
            estimate=100,
            pre_tax=90,
            loc='Test Location',
            car_type=0,
            type=BookingParams.TYPE_ROUNDTRIP
        )
        db.session.add(booking)
        db.session.commit()
        trip= Trip(booking.id,datetime.datetime.utcnow(),1,1,6)
        db.session.add(trip)
        pending_pricing = BookPricing(bid=booking.id, est=100,
                               base=50, est_pre_tax=40,
                               cgst=0, sgst=0,
                               cartype=1, night=10, food=0,
                               booking=5, dist=10,
                               insurance=2, driver_base=30,
                               driver_night=10)
        db.session.add(pending_pricing)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()


    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    print(booking,trip,pending_pricing)
    response = client.post('/api/driver/ongoing', headers=headers)
    print(response.json)
    assert response.status_code == 200
    response_data = json.loads(response.data)
    assert 'trip_id' in response_data  # Check if trip_id is returned
    assert response_data['car_type']==0
    assert response_data['trip_type']==BookingParams.TYPE_ROUNDTRIP


def test_ongoing_driver_trip_b2b_success(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    current_time = datetime.datetime.utcnow()
    ds=DriverSearch('tezt',user_id,0,22,77,(current_time-datetime.timedelta(hours=2)).strftime("%H:%M:%S"),datetime.datetime.utcnow().strftime("%Y-%m-%d"),
                    1,current_time)
    db.session.add(ds)
    db.session.commit()

    curdate = current_time.strftime("%Y-%m-%d")
    try:
        booking = Bookings(
        user=user_id,
        skey="tezt",
        driver=driver_id,
        lat=12.34,
        long=56.78,
        starttime=(current_time - datetime.timedelta(hours=2)).strftime("%H:%M:%S"),
        startdate=curdate,
        dur="02:00:00",
        endtime=(current_time + datetime.timedelta(hours=4)).strftime("%H:%M:%S"),
        enddate=curdate,
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        type=BookingParams.TYPE_SPINNY
        )
        db.session.add(booking)
        db.session.commit()
        trip= Trip(booking.id,datetime.datetime.utcnow(),1,1,6)
        db.session.add(trip)
        db.session.commit()
        spinny_rep = SpinnyRep(
            name="Test Rep",
            user=random.randint(1, 100),  # Assuming user IDs are in range 1-100
            mob=random.randint(7000000000, 9999999999),  # Random mobile number
            regn=0,  # Assuming regions are in range 1-10
        )
        db.session.add(spinny_rep)
        pending_pricing = BookPricing(bid=booking.id, est=100,
                               base=50, est_pre_tax=40,
                               cgst=0, sgst=0,
                               cartype=1, night=10, food=0,
                               booking=5, dist=10,
                               insurance=2, driver_base=30,
                               driver_night=10)
        db.session.add(pending_pricing)
        db.session.commit()  # Commit to save the SpinnyRep entry

        # Create a random SpinnyBookings entry
        spinny_booking = SpinnyBookings(
            booking.id,  # Random booking reference
            "APT1234",  # Example appointment ID
            "ABC1234",  # Example vehicle registration number
            "Model X",  # Example vehicle model
            random.choice([1, 2, 3]),  # Random trip type
            "Trip Type Detail",  # Example trip type detail
            "Business Function",  # Example business function
        "Business Category",  # Example business category
        random.randint(7000000000, 9999999999),  # Random drop mobile number
            spinny_rep.id,  # Link to the SpinnyRep
            0,  # Assuming regions are in range 1-10
        )
        db.session.add(spinny_booking)
        db.session.commit()  # Commit to save the SpinnyBookings entry
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }

    response = client.post('/api/driver/ongoing', headers=headers)
    assert response.status_code == 200
    response_data = json.loads(response.data)
    print(response_data)
    assert 'trip_id' in response_data  # Check if trip_id is returned
    assert response_data['car_type']==0
    assert response_data['trip_type']==BookingParams.TYPE_SPINNY

 # ---------------------------------
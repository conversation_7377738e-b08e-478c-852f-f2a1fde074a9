tags:
  - Trips
summary: Stop a Trip
description: >
  This API endpoint allows drivers to stop an ongoing trip, calculate the final fare, and handle payments based on the payment type. Supports multiple B2B services and handles different booking types.
parameters:
  - name: book_id
    in: formData
    type: integer
    required: true
    description: The booking ID of the trip.
  - name: c24
    in: formData
    type: integer
    required: false
    description: Flag for C24 service. Set to 1 if applicable.
  - name: revv
    in: formData
    type: integer
    required: false
    description: Flag for Revv service. Set to 1 if applicable.
  - name: zoomcar
    in: formData
    type: integer
    required: false
    description: Flag for Zoomcar service. Set to 1 if applicable.
  - name: gujral
    in: formData
    type: integer
    required: false
    description: Flag for Gujral service. Set to 1 if applicable.
  - name: olx
    in: formData
    type: integer
    required: false
    description: Flag for OLX service. Set to 1 if applicable.
  - name: cardekho
    in: formData
    type: integer
    required: false
    description: Flag for CarDekho service. Set to 1 if applicable.
  - name: bhandari
    in: formData
    type: integer
    required: false
    description: Flag for Bhandari service. Set to 1 if applicable.
  - name: lat
    in: formData
    type: number
    required: false
    description: Latitude of the driver’s stop location.
  - name: lng
    in: formData
    type: number
    required: false
    description: Longitude of the driver’s stop location.
responses:
  200:
    description: <PERSON> stopped successfully with calculated fare.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        price:
          type: number
          description: Final trip fare
          example: 500
        due:
          type: number
          description: Amount due
          example: 50
        surcharge:
          type: integer
          description: Indicates if there was a surcharge
          example: 1
        dur:
          type: string
          description: Trip duration
          example: "1:30:00"
        cgst:
          type: number
          description: CGST amount
          example: 45
        sgst:
          type: number
          description: SGST amount
          example: 45
        pretax:
          type: number
          description: Pre-tax trip fare
          example: 400
        stop_time:
          type: string
          description: Stop time in ISO format
          example: "2024-09-26T10:30:00"
        total_price:
          type: number
          description: Total amount to be paid
          example: 500
    examples:
      application/json:
        success: 1
        price: 500
        due: 50
        surcharge: 1
        dur: "1:30:00"
        cgst: 45
        sgst: 45
        pretax: 400
        stop_time: "2024-09-26T10:30:00"
        total_price: 500
  401_a:
    description: Unauthorized access, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized access)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized: Not a driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized: Not a driver"
  401_b:
    description: User account restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "Users restricted"
    examples:
      application/json:
        success: -1
        message: "Users restricted"
  201_a:
    description: Incomplete form submission.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete form)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"
  201_b:
    description: Trip does not exist or already stopped.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 if trip does not exist)
          example: -1
        message:
          type: string
          description: Error message
          example: "Trip does not exist"
    examples:
      application/json:
        success: -1
        message: "Trip does not exist"
  500:
    description: Database error while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for DB error)
          example: -1
        price:
          type: number
          description: -1 (failure in calculating price)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -1
        price: -1
        message: "DB Error"
  201_c:
    description: Multiple B2B services selected (only one allowed).
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 if multiple services selected)
          example: -1
        message:
          type: string
          description: Error message
          example: "Multiple B2B services selected, only one allowed"
    examples:
      application/json:
        success: -1
        message: "Multiple B2B services selected, only one allowed"

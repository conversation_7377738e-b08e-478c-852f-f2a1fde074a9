tags:
  - Admin Analytics
summary: Get Transaction Summary for Driver Admin
description: >
  This endpoint retrieves aggregated transaction summaries for driver admin transactions.
  The data is filtered by a specified date range, time range, and optionally by a region filter.
  Depending on the `data_type` parameter, different driver transaction methods are filtered:
    - **Fine**: Retrieves transactions for Admin Fine (negative amounts).
    - **Gift**: Retrieves transactions for Gift (positive amounts).
    - **Due Deduct**: Retrieves transactions for Due Deduction (positive amounts).
    - **Withdraw**: Retrieves transactions for Withdraw (negative amounts).
    - **T-Shirt**: Retrieves transactions for T-Shirt.
    - **Registration**: Retrieves transactions for Registration.
    - **Bag**: Retrieves transactions for Bag.
    - **Cancel**: Retrieves cancellation transactions (with cancellation charges and reversal counts).
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for filtering transactions (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: from_time
    in: formData
    type: string
    required: false
    default: "00:00:00"
    description: "Start time for filtering transactions (HH:MM:SS). Defaults to '00:00:00'."
    example: "00:00:00"
  - name: to_time
    in: formData
    type: string
    required: false
    default: "23:59:59"
    description: "End time for filtering transactions (HH:MM:SS). Defaults to '23:59:59'."
    example: "23:59:59"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A comma-separated list of region IDs to filter transactions.
      Use "-1" to include all regions.
    example: "1,2,3"
  - name: data_type
    in: formData
    type: string
    required: true
    description: >
      Specifies the type of driver transaction summary to retrieve.
      Accepted values are:
        - "Fine"
        - "Gift"
        - "Due Deduct"
        - "Withdraw"
        - "T-Shirt"
        - "Registration"
        - "Bag"
        - "Cancel"
    example: "Fine"
responses:
  200:
    description: "Successfully retrieved driver transaction summary for admin."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 indicates success)."
          example: 1
        data:
          type: array
          description: >
            For `data_type` **Cancel**, each item includes:
              - **admin_name**: Driver admin name.
              - **cancellation_charges_count**: Total cancellation charges.
              - **cancellation_reversals_count**: Total cancellation reversals.
            For other `data_type` values, each item includes:
              - **admin_name**: Driver admin name.
              - **total_amount**: Aggregated transaction amount.
          items:
            type: object
            properties:
              admin_name:
                type: string
                description: "Driver admin name."
                example: "Driver Admin 1"
              cancellation_charges_count:
                type: number
                description: "Total cancellation charges (if data_type is Cancel)."
                example: 1500
              cancellation_reversals_count:
                type: number
                description: "Total cancellation reversals (if data_type is Cancel)."
                example: 500
              total_amount:
                type: number
                description: "Total aggregated transaction amount (for non-Cancel data_types)."
                example: 2000
  400:
    description: "Invalid request due to missing or invalid parameters."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (e.g., -2 indicates missing/invalid parameters)."
          example: -2
        error:
          type: string
          description: "Error message."
          example: "Missing date parameters or invalid data_type parameter."
  500:
    description: "Internal server error or database failure."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 indicates a server error)."
          example: 0
        error:
          type: string
          description: "Error message."
          example: "Internal server error"

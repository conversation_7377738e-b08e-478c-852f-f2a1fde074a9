tags:
  - Create_booking_admin
summary: Admin search for trip estimates
description: >
  This endpoint allows an admin to search for trip estimates based on user data and trip details. The request requires information about the user, trip details such as location and duration, and other parameters. The trip estimates are processed and returned based on the input data.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the user for whom the trip is being estimated.
    example: "8650755357"
  - name: reflat
    in: formData
    type: string
    required: true
    description: The latitude of the reference location.
    example: "22.5726"
  - name: reflong
    in: formData
    type: string
    required: true
    description: The longitude of the reference location.
    example: "88.3639"
  - name: car_type
    in: formData
    type: string
    required: true
    description: The type of car requested for the trip.
    example: "Sedan"
  - name: dur
    in: formData
    type: string
    required: true
    description: The duration(s) for the trip in hours, provided as a comma-separated list.
    example: "4,6"
  - name: time
    in: formData
    type: string
    required: true
    description: The time(s) for the trip, provided as a comma-separated list. The format should be `%d/%m/%Y %H:%M:%S`.
    example: "17/10/2024 15:30:00,17/10/2024 18:00:00"
  - name: type
    in: formData
    type: integer
    required: false
    description: The booking type, where 1 represents a round trip. Defaults to 1.
    example: 1
  - name: region
    in: formData
    type: integer
    required: false
    description: The city region for the trip. Defaults to Kolkata.
    example: 1
  - name: insurance
    in: formData
    type: integer
    required: false
    description: Indicates whether insurance is requested (1 for true, 0 for false). Defaults to 0.
    example: 0
  - name: ninsurance
    in: formData
    type: integer
    required: false
    description: The number of insurances requested. Defaults to 1.
    example: 1
  - name: dest_lat
    in: formData
    type: string
    required: false
    description: The latitude of the destination location.
    example: "23.0345"
  - name: dest_long
    in: formData
    type: string
    required: false
    description: The longitude of the destination location.
    example: "87.3245"
  - name: dest_loc
    in: formData
    type: string
    required: false
    description: The name or description of the destination location.
    example: "Park Street"
  - name: no_of_trip
    in: formData
    type: integer
    required: false
    description: The number of trips to be estimated.
    example: 1
responses:
  200:
    description: Successfully fetched trip estimates.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        result:
          type: array
          items:
            type: object
            properties:
              success:
                type: integer
                example: 1
              estimate:
                type: object
                description: Trip estimate details.
                properties:
                  distance:
                    type: string
                    example: "15 km"
                  duration:
                    type: string
                    example: "30 minutes"
                  cost:
                    type: string
                    example: "500 INR"
  400:
    description: Bad request due to missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Missing or invalid parameters"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message details"

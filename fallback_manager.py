import gevent
from gevent import monkey
monkey.patch_all()

import logging
from socketio.base_manager import BaseManager
from socketio import <PERSON>is<PERSON>anager, KombuManager
from gevent.lock import Semaphore

class FallbackManager(BaseManager):
    def __init__(self, redis_url=None, rabbitmq_url=None, channel='socketio', health_check_interval=5):
        self.redis_url = redis_url
        self.rabbitmq_url = rabbitmq_url
        self.channel = channel  # Assign channel before super().__init__()
        # self.logger = logging.getLogger('FallbackManager')  # Assign logger early
        self.health_check_interval = health_check_interval
        self.lock = Semaphore()
        self.current_manager = None
        self.manager = None
        super(FallbackManager, self).__init__()
        self.initialize_manager()
        self.start_health_check_greenlet()

    def set_server(self, server):
        self.server = server
        if self.manager:
            self.manager.set_server(server)

    def initialize_manager(self):
        with self.lock:
            try:
                self.manager = RedisManager(self.redis_url, channel=self.channel)
                self.manager.set_server(self.server)  # Set the server
                print("Connected to Redis")
                self.current_manager = 'redis'
            except Exception as e:
                print(f"Redis connection failed: {e}")
                print("Attempting to connect to RabbitMQ")
                try:
                    self.manager = KombuManager(self.rabbitmq_url, channel=self.channel)
                    self.manager.set_server(self.server)  # Set the server
                    print("Connected to RabbitMQ")
                    self.current_manager = 'rabbitmq'
                except Exception as e:
                    print(f"RabbitMQ connection failed: {e}")
                    self.manager = None
                    self.current_manager = None
                    print("No message queue available")

    def start_health_check_greenlet(self):
        gevent.spawn(self.health_check_loop)

    def health_check_loop(self):
        while True:
            gevent.sleep(self.health_check_interval)
            if self.current_manager != 'redis':
                print("Checking if Redis is available...")
                if self.check_redis_available():
                    print("Redis is available. Switching back to Redis.")
                    with self.lock:
                        try:
                            self.manager = RedisManager(self.redis_url, channel=self.channel)
                            self.manager.set_server(self.server)  # Set the server
                            self.current_manager = 'redis'
                            print("Successfully switched back to Redis")
                        except Exception as e:
                            print(f"Failed to switch back to Redis: {e}")

    def check_redis_available(self):
        try:
            test_manager = RedisManager(self.redis_url, channel=self.channel)
            test_manager.redis.publish('socketio.test', 'test_message')
            return True
        except Exception as e:
            print(f"Redis is still unavailable: {e}")
            return False

    def handle_failure(self):
        with self.lock:
            if isinstance(self.manager, RedisManager):
                print("RedisManager failed, switching to RabbitMQ")
                try:
                    self.manager = KombuManager(self.rabbitmq_url, channel=self.channel)
                    self.manager.set_server(self.server)  # Set the server
                    self.current_manager = 'rabbitmq'
                    print("Connected to RabbitMQ")
                except Exception as e:
                    print(f"Failed to switch to RabbitMQ: {e}")
                    self.manager = None
                    self.current_manager = None
            elif isinstance(self.manager, KombuManager):
                print("KombuManager failed, no other message queue available")
                self.manager = None
                self.current_manager = None
            else:
                print("No message queue manager available")
                self.manager = None
                self.current_manager = None

    # Implement required methods by forwarding to the underlying manager
    def connect(self, sid, namespace):
        if self.manager:
            try:
                self.manager.connect(sid, namespace)
            except Exception as e:
                print(f"Connect failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to connect client")

    def is_connected(self, sid, namespace):
        if self.manager:
            try:
                return self.manager.is_connected(sid, namespace)
            except Exception as e:
                print(f"is_connected failed: {e}")
                self.handle_failure()
                return False
        else:
            print("No manager available to check connection")
            return False

    def disconnect(self, sid, namespace=None):
        if self.manager:
            try:
                self.manager.disconnect(sid, namespace=namespace)
            except Exception as e:
                print(f"Disconnect failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to disconnect client")

    def trigger_event(self, sid, event, *args, namespace=None):
        if self.manager:
            try:
                self.manager.trigger_event(sid, event, *args, namespace=namespace)
            except Exception as e:
                print(f"Trigger event failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to trigger event")

    def emit(self, event, data, namespace, room=None, skip_sid=None, callback=None, **kwargs):
        if self.manager:
            try:
                self.manager.emit(event, data, namespace=namespace, room=room, skip_sid=skip_sid, callback=callback, **kwargs)
            except Exception as e:
                print(f"Emit failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to emit message")

    def enter_room(self, sid, namespace, room):
        if self.manager:
            try:
                self.manager.enter_room(sid, namespace, room)
            except Exception as e:
                print(f"Enter room failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to enter room")

    def leave_room(self, sid, namespace, room):
        if self.manager:
            try:
                self.manager.leave_room(sid, namespace, room)
            except Exception as e:
                print(f"Leave room failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to leave room")

    def close_room(self, room, namespace=None):
        if self.manager:
            try:
                self.manager.close_room(room, namespace=namespace)
            except Exception as e:
                print(f"Close room failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to close room")

    def get_participants(self, namespace, room):
        if self.manager:
            try:
                return self.manager.get_participants(namespace, room)
            except Exception as e:
                print(f"Get participants failed: {e}")
                self.handle_failure()
                return set()
        else:
            print("No manager available to get participants")
            return set()

    def get_rooms(self, sid, namespace):
        if self.manager:
            try:
                return self.manager.get_rooms(sid, namespace)
            except Exception as e:
                print(f"Get rooms failed: {e}")
                self.handle_failure()
                return set()
        else:
            print("No manager available to get rooms")
            return set()

    def close(self):
        if self.manager:
            try:
                self.manager.close()
            except Exception as e:
                print(f"Close manager failed: {e}")
                self.handle_failure()
        else:
            print("No manager available to close")
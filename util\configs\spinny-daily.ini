[general]
filename = spinny-daily.csv
query = SELECT DATE(ADDTIME(trip_start, "05:30:00")) AS 'Date of pick-up', sp_veh_reg AS 'Car reg no', book_loc_name AS 'Pick-up location', dest_book_name AS 'Drop location', CASE WHEN book_region = 0 THEN "Kolkata" WHEN book_region = 6 THEN "Delhi" WHEN book_region = 1 THEN "Hyderabad" WHEN book_region = 8 THEN "Bangalore" ELSE "Kolkata" END AS 'City name', CASE WHEN (book_region IN (6, 8, 1)) AND sp_dist <= 15 THEN 180 WHEN (book_region IN (6, 8, 1)) AND sp_dist <= 30 THEN 250 WHEN (book_region IN (6, 8, 1)) AND sp_dist > 30 THEN 250 + (sp_dist - 30) * 6 WHEN sp_dist <= 15 THEN 160 WHEN sp_dist <= 30 THEN 210 WHEN sp_dist <= 45 THEN 260 WHEN sp_dist <= 50 THEN 320 ELSE 320 + (sp_dist - 50) * 9 END AS 'Cost of the trip', sp_dist AS 'Kms run', ADDTIME(CONCAT(book_startdate, " ", book_starttime), "05:30:00") AS 'Request Time', ADDTIME(COALESCE(latest_alloc.max_ba_timestamp, book_startdate), "05:30:00") AS 'Allocation Time', ADDTIME(trip_start, "05:30:00") AS 'Start Time', ADDTIME(trip_stop, "05:30:00") AS 'Stop Time', TIMEDIFF(trip_stop, trip_start) AS 'Trip duration', sp_trip_type_detail AS 'Trip Category', sp_business_func AS 'Business Function', sp_business_cat AS 'Business Category', book_ref AS 'Booking ID' FROM bookings JOIN trip ON book_ref = trip_book JOIN spinny_bookings ON book_ref = sp_book_ref JOIN drivers ON book_driver = driver_id JOIN users ON driver_user = user_id JOIN book_dest ON dest_book_id = book_ref LEFT JOIN (SELECT ba_book_id, MAX(ba_timestamp) AS max_ba_timestamp FROM booking_alloc GROUP BY ba_book_id) AS latest_alloc ON latest_alloc.ba_book_id = book_ref WHERE DATE(CONVERT_TZ(CONCAT(book_startdate, " ", book_starttime), '+00:00', '+05:30')) >= SUBDATE(CONVERT_TZ(DATE(NOW()), '+00:00', '+05:30'), 1) AND DATE(CONVERT_TZ(CONCAT(book_startdate, " ", book_starttime), '+00:00', '+05:30')) <= SUBDATE(CONVERT_TZ(DATE(NOW()), '+00:00', '+05:30'), 0);
query1 = SELECT date(addtime(trip_start,"05:30:00")) as 'Date of pick-up', sp_veh_reg as 'Car reg no', book_loc_name as 'Pick-up location', dest_book_name as 'Drop location', CASE when book_region = 0 THEN "Kolkata" when book_region = 6 THEN "Delhi" when book_region=1 THEN "Hyderabad" ELSE "Kolkata" END as 'City name', CASE WHEN book_region = 6 AND sp_dist <= 15 THEN 180 WHEN book_region = 6 AND sp_dist <= 30 THEN 250 WHEN book_region = 6 THEN 250 + (sp_dist - 30) * 8 WHEN sp_dist <= 15 THEN 160 WHEN sp_dist <= 30 THEN 210 WHEN sp_dist <= 45 THEN 260 WHEN sp_dist <= 50 THEN 320 ELSE 320 + (sp_dist - 50) * 9 END AS 'Cost of the trip', sp_dist as 'Kms run', timediff(trip_stop,trip_start) as 'Trip duration', sp_trip_type_detail as 'Trip Category', sp_business_func as 'Business Function', sp_business_cat as 'Business Category', book_ref as 'Booking ID' from bookings, trip, spinny_bookings, drivers, users, book_dest where book_ref=sp_book_ref and book_ref=trip_book and book_driver=driver_id and driver_user=user_id and dest_book_id=book_ref and (date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))>= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 1) and date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))<= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 0));
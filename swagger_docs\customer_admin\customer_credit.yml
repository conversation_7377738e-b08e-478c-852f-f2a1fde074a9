tags:
  - Customer_admin
summary: Add Customer Credit
description: >
  This endpoint allows an admin or super admin to add or deduct credit for a customer. The admin must provide
  user details, credit amount, and transaction information. Only users with the appropriate admin roles can perform
  this action, and their account must be enabled.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: ID of the customer to whom the credit is being added or deducted
    example: "123"
  - name: amount
    in: formData
    required: true
    type: number
    format: float
    description: The amount of credit to be added or deducted (in units of 100, for cents)
    example: 50.00
  - name: remark
    in: formData
    required: true
    type: string
    description: A remark or note explaining the transaction
    example: "Adding credit for loyalty bonus"
  - name: credit_type
    in: formData
    required: true
    type: string
    description: The type of credit operation ("Add" for adding credit, or "Deduct" for deduction)
    enum: 
      - Add
      - Deduct
    example: "Add"
  - name: payment_mode
    in: formData
    required: true
    type: string
    description: Payment mode used for the transaction (e.g., "Cash", "Online")
    enum: 
      - Cash
      - Online
    example: "Online"
  - name: trans_id
    in: formData
    required: false
    type: string
    description: (Optional) A transaction ID to track the payment
    example: "TXN12345"
responses:
  200:
    description: Successfully added or deducted the credit
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
  201:  # Failure due to missing or incomplete request parameters
    description: Required parameters are missing or incomplete
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
  401:  # Unauthorized access or disabled admin account
    description: The admin does not have the required permissions or the account is disabled
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  500:  # Internal server error
    description: Internal server error due to issues with processing the request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
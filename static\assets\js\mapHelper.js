// <editor-fold desc="Reverse Geocode">
/**
 * Reverse geocode an address to get latitude and longitude values
 * @param  address - location string to be reverse geocoded
 * @return latitude and longitude values packed in JSON format.
 *
 */
function getLatLong(address) {
    var locationObject = {
        "latitude":  null,
        "longitude": null,
        "status": ""
    }
    if(!isNullOrEmpty(address))
    {
        $.ajax({
            async: false,
            type: "POST",
            url: "https://maps.googleapis.com/maps/api/geocode/json?address=" + address + "&key=" + window.messages["GOOGLE_MAPS_API_KEY"],
            beforeSend: function(request) {

            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response["status"] === google.maps.GeocoderStatus.OK) {
                    locationObject["latitude"] = response["results"][0]["geometry"]["location"]["lat"];
                    locationObject["longitude"] = response["results"][0]["geometry"]["location"]["lng"];
                }
                locationObject["status"] = response["status"];
            }
        });
    }
    return locationObject;
}

function getAddress(lat, lng) {
    $.ajax({
            async: false,
            type: "POST",
            url: "https://maps.googleapis.com/maps/api/geocode/json?latlng=" + lat + "," + lng + "&key=" + window.messages["GOOGLE_MAPS_API_KEY"],
            beforeSend: function(request) {

            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response["status"] === google.maps.GeocoderStatus.OK) {
                    address = response["results"][0].formatted_address;
                }
            }
        });
        return address;
}

function getRegionBounds() {
    var centerCoords = window.messages["KOLKATA_CENTER"].split(',');
    var regionCenter = new google.maps.LatLng({lat: parseFloat(centerCoords[0]), lng: parseFloat(centerCoords[1])});
    //begin bounds with center and first point
    var regionBounds = new google.maps.LatLngBounds
    (
        regionCenter,
        new google.maps.LatLng({lat: window.KOLKATA_BOUNDS["pointX"][0], lng: window.KOLKATA_BOUNDS["pointY"][0]})
    );
    //array mismatch correction
    var mismatch = window.KOLKATA_BOUNDS["pointX"].length - window.KOLKATA_BOUNDS["pointY"].length;
    var iterationLength = Math.max(window.KOLKATA_BOUNDS["pointX"].length, window.KOLKATA_BOUNDS["pointY"].length);
    if(mismatch != 0) iterationLength = Math.min(window.KOLKATA_BOUNDS["pointX"].length, window.KOLKATA_BOUNDS["pointY"].length);
    for(var i = 0; i < iterationLength; i++) {
        regionBounds.extend(new google.maps.LatLng({lat: window.KOLKATA_BOUNDS["pointX"][i], lng: window.KOLKATA_BOUNDS["pointY"][i]}));
    }

    return regionBounds;
}
function getRegionBoundsZoomcar() {
    var centerCoords = window.messages["KOLKATA_CENTER"].split(',');
    var centerCoordsGuwahati = window.messages["GUWAHATI_CENTER"].split(',');
    var regionCenter = new google.maps.LatLng({lat: parseFloat(centerCoords[0]), lng: parseFloat(centerCoords[1])});
    var regionCenterGuwahati = new google.maps.LatLng({lat: parseFloat(centerCoordsGuwahati[0]), lng: parseFloat(centerCoordsGuwahati[1])});
    //begin bounds with center and first point
    var regionBounds = new google.maps.LatLngBounds
    (
        regionCenter,
        regionCenterGuwahati,
        new google.maps.LatLng({lat: window.KOLKATA_BOUNDS["pointX"][0], lng: window.KOLKATA_BOUNDS["pointY"][0]}),
        new google.maps.LatLng({lat: window.GUWAHATI_BOUNDS["pointX"][0], lng: window.GUWAHATI_BOUNDS["pointY"][0]})
    );
    //array mismatch correction
    var mismatch = window.KOLKATA_BOUNDS["pointX"].length - window.KOLKATA_BOUNDS["pointY"].length;
    var iterationLength = Math.max(window.KOLKATA_BOUNDS["pointX"].length, window.KOLKATA_BOUNDS["pointY"].length);
    if(mismatch != 0) iterationLength = Math.min(window.KOLKATA_BOUNDS["pointX"].length, window.KOLKATA_BOUNDS["pointY"].length);
    for(var i = 0; i < iterationLength; i++) {
        regionBounds.extend(new google.maps.LatLng({lat: window.KOLKATA_BOUNDS["pointX"][i], lng: window.KOLKATA_BOUNDS["pointY"][i]}));
    }

    var mismatchGuwahati = window.GUWAHATI_BOUNDS["pointX"].length - window.GUWAHATI_BOUNDS["pointY"].length;
    var iterationLengthGuwahati = Math.max(window.GUWAHATI_BOUNDS["pointX"].length, window.GUWAHATI_BOUNDS["pointY"].length);
    if(mismatchGuwahati != 0) iterationLengthGuwahati = Math.min(window.GUWAHATI_BOUNDS["pointX"].length, window.GUWAHATI_BOUNDS["pointY"].length);
    for(var i = 0; i < iterationLengthGuwahati; i++) {
        regionBounds.extend(new google.maps.LatLng({lat: window.GUWAHATI_BOUNDS["pointX"][i], lng: window.GUWAHATI_BOUNDS["pointY"][i]}));
    }

    return regionBounds;
}

/**
 * Initializes Realtime DB
 * @return Firebase DB object
 */

function initRealtimeDB() {
  var config = {
    apiKey: "AIzaSyBmDZqlnWYzfDmnJlRWaFPKv7UZ3O92lTk",
    databaseURL: "https://drivers4me-prod-default-rtdb.asia-southeast1.firebasedatabase.app/",
  };
  firebase.initializeApp(config);

  // Get a reference to the database service
  var database = firebase.database();
  return database;
}

/**
 * TO-DO: Remove database call
 * @return Open tracking page
 */

function showCurrentLocation(database, driverId) {
    window.open('../track/' + driverId);
}

function showCurrentLocationAll(database, bookingID) {
    window.open('../track/book/' + bookingID);
}

function showLocationHistory(database, bookingID) {
    window.open('../history/booking/' + bookingID);
}
// </editor-fold>
// <editor-fold desc="Constants">

window.KOLKATA_BOUNDS = {
    "pointX" : [22.360953, 22.36891, 22.399004, 22.436534, 22.472547, 22.49552, 22.533817, 22.565073, 22.609134, 22.650171, 22.678447,
            22.696502, 22.70054, 22.700699, 22.698244, 22.674487, 22.662607, 22.641696, 22.620441, 22.593501, 22.572579, 22.549514,
            22.551099, 22.502896, 22.468636, 22.474029, 22.458801, 22.424212, 22.377552],
    "pointY" : [88.429102, 88.431956, 88.432256, 88.433887, 88.427278, 88.444085, 88.441854, 88.5096, 88.509514, 88.476984, 88.463681,
            88.451578, 88.413212, 88.366606, 88.363001, 88.35974, 88.354246, 88.35665, 88.365086, 88.350667, 88.341654, 88.317708,
            88.281916, 88.285349, 88.310412, 88.347834, 88.368433, 88.3753, 88.402079]
};
window.GUWAHATI_BOUNDS = {
    "pointX" : [26.1229243 ,26.1258172, 26.1282762, 26.1294333,26.1268297, 26.1224904, 26.1190188, 26.1159086, 26.1118582, 26.1086033, 26.1063609, 26.1054929, 26.1067226, 26.1081693, 26.1095436, 26.1101222, 26.1101946, 26.1115689, 26.1143897, 26.1171382, 26.1185847, 26.1202482, 26.1225626, 26.1245877, 26.1272636, 26.1300841, 26.1328322, 26.136448, 26.1402083, 26.143607, 26.1486687, 26.1503317, 26.1595144, 26.1622618, 26.1642862, 26.1671781, 26.171082, 26.1739737, 26.1809136, 26.1793232, 26.1791786, 26.1790341, 26.1800461, 26.1826484, 26.1903106, 26.2014415, 26.2098455, 26.205732, 26.2017727, 26.1991868, 26.1989457, 26.1992571, 26.1962375, 26.1886118, 26.1828291, 26.1771906, 26.1749857, 26.1723832, 26.1712265, 26.1709373, 26.1717687, 26.1721301, 26.1724193, 26.1724916, 26.171859 , 26.1711903, 26.1699252, 26.168407, 26.1663827, 26.1655152, 26.1662233, 26.1561884, 26.1483071, 26.1444747, 26.1410399, 26.1375328, 26.132326 , 26.1326153, 26.1275529, 26.1229243],
    "pointY" : [91.6269392, 91.631289, 91.6356388, 91.6428885, 91.6502993, 91.6610933, 91.6678596, 91.6709207, 91.6715651, 91.6797009, 91.6826007, 91.6855812, 91.6892866, 91.6941197, 91.6982278, 91.7005638, 91.7072497, 91.7103106, 91.7113578, 91.7126466, 91.7159493, 91.7191714, 91.7207018, 91.7213463, 91.7211046, 91.7208629, 91.7216685, 91.724085, 91.7258572, 91.7277904, 91.729482, 91.730207, 91.7384232, 91.7464784, 91.7587223, 91.7672608, 91.7716106, 91.7725772, 91.7740272, 91.7812769, 91.7865933, 91.7931986, 91.7998038, 91.8044758, 91.8083423, 91.8197807, 91.8196938, 91.7934195, 91.7878394, 91.7776364, 91.7766851, 91.7741938, 91.7665359, 91.7438203, 91.7389469, 91.732825, 91.7295626, 91.725978, 91.7204199, 91.713855, 91.7004833, 91.696496, 91.6920656, 91.6878165, 91.6861652, 91.6838091, 91.6797009, 91.6726123, 91.664396, 91.6590796, 91.6496229, 91.645708, 91.6402707, 91.6379347, 91.6353973, 91.6399886, 91.6366055, 91.6290336, 91.6269392, 91.6269392]
};

// </editor-fold>
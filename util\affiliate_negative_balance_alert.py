from main import app
from datetime import datetime, timedelta
from models import ScheduledReport,BookingAlloc
from _email import send_mail_v3
from db_config import db,mdb
from affiliate_b2b.affiliate_models import Affiliate,AffiliateRep
import os

def notify_negative_wallets():
    visited_affiliate_ids = set()

    # Preload all affiliates and build a map: {id: Affiliate}
    all_affiliates = Affiliate.query.all()
    affiliate_map = {aff.id: aff for aff in all_affiliates}

    for aff in all_affiliates:
        if aff.id in visited_affiliate_ids:
            continue

        slave_ids = aff.slave.get("slaves", []) if aff.slave else []

        negative_slaves = []
        for sid in slave_ids:
            if sid in visited_affiliate_ids:
                continue

            slave = affiliate_map.get(sid)
            if slave and slave.wallet < 0:
                negative_slaves.append(slave)
                visited_affiliate_ids.add(sid)

        is_master_negative = aff.wallet < 0

        # Send only if master or any slave has negative balance
        if is_master_negative or negative_slaves:
            reps = AffiliateRep.query.filter_by(affiliate_id=aff.id).all()
            recipient_emails = [rep.email for rep in reps if rep.email]

            if not recipient_emails:
                continue

            subject = f"[Alert] Negative Wallet Alert - {aff.display_name or aff.client_name}"

            body = f"""
            <p>Dear <strong>{aff.display_name or aff.client_name}</strong>,</p>
            """

            if is_master_negative:
                body += f"<p><strong>Your current wallet balance is negative:</strong> ₹{aff.wallet:.2f}</p>"

            if negative_slaves:
                body += "<p><strong>The following affiliates under your network have negative wallet balances:</strong><br>"
                for s in negative_slaves:
                    body += f"- {s.display_name or s.client_name} : ₹{s.wallet:.2f}<br>"
                body += "</p>"

            body += """
            <p>To continue uninterrupted service, please recharge the wallet(s) as soon as possible.</p>
            <p>Regards,<br>
            Drivers4Me Team</p>
            <hr>
            <p style="font-size: 12px; color: gray;">This is an automated message. Please do not reply.</p>
            """


            try:
                send_mail_v3(
                    address="<EMAIL>",
                    to_list=recipient_emails,
                    subject=subject,
                    content=body,
                    cc=["<EMAIL>","<EMAIL>","<EMAIL>"],
                    bcc=['<EMAIL>']
                )
            except Exception as e:
                print(f"Failed to send wallet alert to affiliate {aff.id}: {e}")

        visited_affiliate_ids.add(aff.id)



if __name__ == '__main__':
    with app.app_context():
         notify_negative_wallets()
        
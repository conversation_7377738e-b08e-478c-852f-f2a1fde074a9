tags:
  - User
summary: Change User Password
description: This endpoint allows a user to change their password.
parameters:
  - name: oldpwd
    in: formData
    type: string
    required: true
    description: Current password of the user
  - name: newpwd
    in: formData
    type: string
    required: true
    description: New password for the user
responses:
  200_a:
    description: Password changed successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Password changed successfully"
    examples:
      application/json:
        success: 1
        message: "Password changed successfully"
  401_a:
    description: Failed to get user identity
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  201_a:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"
  201_b:
    description: Password too short
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: "Password should be at least 6 letters long"
    examples:
      application/json:
        success: -3
        message: "Password should be at least 6 letters long"
  200_b:
    description: Invalid password
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Invalid password"
    examples:
      application/json:
        success: 0
        message: "Invalid password"
  500:
    description: Database error
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        error:
          type: string
          example: "Error description"
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -4
        error: "Error description"
        message: "DB Error"

tags:
  - Booking_admin
summary: Retrieve a list of bookings
description: >
  This endpoint retrieves a list of bookings based on the provided parameters such as starting point, number of logs, date range, and search criteria.
parameters:
  - name: starting_from
    in: formData
    type: integer
    required: true
    description: The starting index for retrieving logs.
  - name: no_of_logs
    in: formData
    type: integer
    required: true
    description: The number of booking logs to retrieve.
  - name: from_date
    in: formData
    type: string
    format: date
    description: The start date for filtering bookings.
    example: "2024-10-01"
  - name: from_time
    in: formData
    type: string
    format: time
    description: The start time for filtering bookings.
    example: "00:00:00"
  - name: to_date
    in: formData
    type: string
    format: date
    description: The end date for filtering bookings.
    example: "2024-10-31"
  - name: to_time
    in: formData
    type: string
    format: time
    description: The end time for filtering bookings.
    example: "23:59:59"
  - name: search_query
    in: formData
    type: string
    description: Optional query string to filter bookings.
    example: "customer_name"
  - name: search_by
    in: formData
    type: integer
    description: The method by which to search (e.g., 1 for ID, 2 for name, etc.).
    example: 3
  - name: filter
    in: formData
    type: string
    description: Optional filter criteria for bookings.
    example: "active"
  - name: search_region
    in: formData
    type: string
    description: The region to filter the bookings.
    example: "New York"
  - name: fromtop
    in: formData
    type: integer
    description: Indicates the order of records to return (0 for descending).
    example: 1
  - name: bookid
    in: formData
    type: integer
    description: Specific booking ID to filter results.
    example: 12345
  - name: isglobal
    in: formData
    type: integer
    description: Flag to indicate whether to include global records (1 for yes).
    example: 1
responses:
  200:
    description: Successfully retrieved a list of bookings.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        time:
          type: number
          format: float
          description: Time taken to process the query in seconds.
          example: 0.234
        time_func:
          type: number
          format: float
          description: Time taken for function execution in seconds.
          example: 0.200
        data:
          type: array
          items:
            type: object
            properties:
              booking_id:
                type: integer
                description: The ID of the booking.
                example: 12345
              driver_id:
                type: integer
                description: The ID of the assigned driver.
                example: 678
              user_id:
                type: integer
                description: The ID of the user associated with the booking.
                example: 910
              booking_code:
                type: string
                description: The unique code associated with the booking.
                example: "ABC123"
              booking_valid:
                type: boolean
                description: Indicates if the booking is valid.
                example: true
              booking_timestamp:
                type: string
                format: date-time
                description: The timestamp when the booking was made.
                example: "2024-10-17T14:30:00Z"
              booking_startdate:
                type: string
                format: date
                description: The start date of the booking.
                example: "2024-10-17"
              booking_starttime:
                type: string
                format: time
                description: The start time of the booking.
                example: "14:30:00"
              booking_dur:
                type: string
                format: time
                description: The duration of the booking.
                example: "01:30:00"
              booking_trip_type:
                type: string
                description: The type of the trip (e.g., one-way, round-trip).
                example: "One-way"
              booking_region:
                type: string
                description: The region/location of the booking.
                example: "New York"
              booking_insurance:
                type: string
                description: The insurance details for the booking.
                example: "Basic Coverage"
              booking_start_name:
                type: string
                description: The name of the starting location.
                example: "Times Square"
              booking_start_lat:
                type: number
                format: float
                description: The latitude of the starting location.
                example: 40.7580
              booking_start_long:
                type: number
                format: float
                description: The longitude of the starting location.
                example: -73.9855
              booking_payment_type:
                type: string
                description: The payment method used for the booking.
                example: "Credit Card"
              booking_estimate:
                type: string
                description: Estimated cost for the booking.
                example: "25.00"
              booking_remark:
                type: string
                description: Any remarks associated with the booking.
                example: "Please be on time."
              booking_dest_name:
                type: string
                description: The name of the destination.
                example: "Central Park"
              booking_dest_lat:
                type: number
                format: float
                description: The latitude of the destination.
                example: 40.7851
              booking_dest_long:
                type: number
                format: float
                description: The longitude of the destination.
                example: -73.9683
              trip_status:
                type: string
                description: The current status of the trip.
                example: "Completed"
              trip_starttime:
                type: string
                format: date-time
                description: The start time of the trip.
                example: "2024-10-17T15:00:00Z"
              trip_endtime:
                type: string
                format: date-time
                description: The end time of the trip.
                example: "2024-10-17T16:30:00Z"
              alttimestamp:
                type: string
                format: date-time
                description: Alternate timestamp for the booking.
                example: "2024-10-17T14:00:00Z"
              customer_mobile:
                type: string
                description: The mobile number of the customer.
                example: "+1234567890"
              customer_name:
                type: string
                description: The name of the customer.
                example: "John Doe"
              customer_label:
                type: string
                description: The label associated with the customer.
                example: "VIP"
              driver_user:
                type: string
                description: The username of the driver.
                example: "driver01"
              driver_mobile:
                type: string
                description: The mobile number of the driver.
                example: "+1987654321"
              driver_name:
                type: string
                description: The name of the driver.
                example: "Jane Smith"
              driver_label:
                type: string
                description: The label associated with the driver.
                example: "Top Driver"
              booking_due:
                type: string
                description: The amount due for the booking.
                example: "15.00"
              booking_price:
                type: string
                description: The total price for the booking.
                example: "30.00"
              booking_car_type:
                type: string
                description: The type of car booked.
                example: "Sedan"
  400:
    description: Bad request, missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        error:
          type: string
          example: "starting_from and no_of_logs parameters are required"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"

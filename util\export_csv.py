from main import app
import sys
sys.path.append("/app/")
import csv
import configparser
import re
from sqlalchemy import text
from models import db

D4M_UTIL_PATH = "/app/util/"


def run_query(query):
    query = re.sub('\s+',' ', query)
    print("Executing query", query)
    with db.engine.connect() as connection:
        records = connection.execute(text(query))
    return records


def export_csv(records, filename, headers=False):
    filename = D4M_UTIL_PATH + 'output/' + filename
    print("Writing to", filename)
    outfile = open(filename, 'w')
    outcsv = csv.writer(outfile)
    data = list()
    if headers:
        data.append(records.keys())
    for record in records:
        data.append(list(record))
    outcsv.writerows(list(data))
    # or maybe use outcsv.writerows(records)
    print("Done writing to", filename)
    outfile.close()
    return filename


def export_csv_main(cfg, headers=False):
    print('Reading from', cfg)
    config_parser = configparser.ConfigParser()
    config_parser.read(cfg, encoding='utf8')
    filename = config_parser.get('general', 'filename')
    query = config_parser.get('general', 'query')
    records = run_query(query)
    return export_csv(records, filename, headers)

if __name__ == '__main__':
    with app.app_context():
        if len(sys.argv) < 2:
            cfg = D4M_UTIL_PATH + 'configs/write_csv.ini'
        else:
            cfg = sys.argv[1]
        if len(sys.argv) == 3:
            headers = bool(sys.argv[2])
        else:
            headers = False
        export_csv_main(cfg, headers)

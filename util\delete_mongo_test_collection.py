from pymongo import MongoClient
from config import TestConfig

# Use test cluster URI
MONGO_URI = TestConfig.MONGO_URI

def delete_test_databases():
    client = MongoClient(MONGO_URI)

    deleted_dbs = []
    print("Available databases:")
    for db_name in client.list_database_names():
        print(f"Found DB: {db_name}")
        if db_name.startswith("affiliate_test_") and db_name != "affiliate_test_db":
            client.drop_database(db_name)
            deleted_dbs.append(db_name)
            print(f"Dropped DB: {db_name}")

    return {
        "message": "Databases deleted",
        "deleted": deleted_dbs
    }

if __name__ == "__main__":
    result = delete_test_databases()
    print(result)
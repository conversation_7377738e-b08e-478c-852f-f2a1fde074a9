tags:
  - Estimate_admin
summary: Add Remark
description: >
  This endpoint allows admins to add remarks for a user. It requires the user ID, remark details,
  the name of the person making the change, an expiry time for the remark, display status, 
  and the ID of the person making the change. Admins must provide the expiry time in the correct format.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The ID of the user to whom the remark is associated.
    example: "12345"
  - name: remark
    in: formData
    required: true
    type: string
    description: The remark text to be added for the user.
    example: "This is a test remark."
  - name: change_by_name
    in: formData
    required: true
    type: string
    description: Name of the admin who is adding the remark.
    example: "Admin <PERSON>"
  - name: expiry_time
    in: formData
    required: true
    type: string
    description: Expiry time for the remark in the format "dd MMM, yy, HH:mm:ss" (e.g., "01 Jan, 24, 12:00:00").
    example: "01 Jan, 24, 12:00:00"
  - name: display
    in: formData
    required: true
    type: boolean
    description: Flag to determine if the remark should be displayed or not.
    example: true
  - name: change_by_id
    in: formData
    required: true
    type: string
    description: ID of the admin who is making the change.
    example: "admin_001"
responses:
  201:
    description: Remark successfully added.
    schema:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates the success of the operation.
          example: true
  400:  # Bad request due to invalid parameters or database errors
    description: Required parameters are missing or invalid.
    schema:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates failure of the operation.
          example: false
        error:
          type: string
          description: Error message detailing the issue.
          example: "Invalid expiry_time format"
  500:  # Internal server error
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: boolean
          description: Indicates failure of the operation.
          example: false
        error:
          type: string
          description: Error message detailing the issue.
          example: "Database error occurred"

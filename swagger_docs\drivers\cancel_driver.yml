tags:
  - Driver
summary: Cancel a booking by the driver.
description: >
  This API is used to cancel a booking by the driver. It verifies if the driver is authorized, checks the booking status, applies any penalties, and sends notifications.
parameters:
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The ID of the booking to be canceled by the driver.
    example: 12345
  - name: reason
    in: formData
    type: integer
    required: false
    description: Reason for cancellation. Default is "Wrongly taken".
    example: 61
  - name: reason_details
    in: formData
    type: string
    required: false
    description: Detailed reason for cancellation.
    example: "Customer changed plans"
responses:
  200:
    description: Booking canceled successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Booking cancelled successfully"
    examples:
      application/json:
        success: 1
        message: "Booking cancelled successfully"
  200_a:
    description: Booking does not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: "Booking does not exist"
    examples:
      application/json:
        success: -3
        message: "Booking does not exist"
  200_b:
    description: Cannot cancel after check-in.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Cannot cancel after check-in"
    examples:
      application/json:
        success: -2
        message: "Cannot cancel after check-in"
  401_a:
    description: Unauthorized role, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: Driver account disabled.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  403:
    description: Database error during cancellation.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

tags:
  - Driver
summary: Driver rejects a booking.
description: This API allows a driver to reject a booking and updates the booking status accordingly.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: ID of the booking to be rejected.
  
  - name: c24
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from Car24 (1 if yes, 0 otherwise).
    
  - name: zoomcar
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from Zoomcar (1 if yes, 0 otherwise).

  - name: olx
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from OLX (1 if yes, 0 otherwise).

  - name: <PERSON><PERSON><PERSON>
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from Cardekho (1 if yes, 0 otherwise).

  - name: bhandari
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from Bhandari (1 if yes, 0 otherwise).

  - name: mahindra
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from Mahindra (1 if yes, 0 otherwise).

  - name: revv_v2
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from RevvV2 (1 if yes, 0 otherwise).

  - name: spinny
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from Spin<PERSON> (1 if yes, 0 otherwise).

  - name: pride<PERSON><PERSON>
    in: formData
    type: integer
    required: false
    description: Indicates if the booking is from PrideHonda (1 if yes, 0 otherwise).
responses:
  200_a:
    description: Driver rejected the booking successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Booking rejected successfully"
    examples:
      application/json:
        success: 1
        message: "Booking rejected successfully"
  200_b:
    description: BookPending entry does not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "BookPending does not exist"
    examples:
      application/json:
        success: 0
        message: "BookPending does not exist"
  201_a:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"
  201_b:
    description: C24 booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Car24 booking not exist"
    examples:
      application/json:
        success: -1
        message: "Car24 booking not exist"
  201_c:
    description: Zoomcar booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Zoomcar booking not exist"
    examples:
      application/json:
        success: -1
        message: "Zoomcar booking not exist"
  201_d:
    description: OLX booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "OLX booking not exist"
    examples:
      application/json:
        success: -1
        message: "OLX booking not exist"
  201_e:
    description: Cardekho booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Cardekho booking not exist"
    examples:
      application/json:
        success: -1
        message: "Cardekho booking not exist"
  201_f:
    description: Bhandari booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Bhandari booking not exist"
    examples:
      application/json:
        success: -1
        message: "Bhandari booking not exist"
  201_g:
    description: Mahindra booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Mahindra booking not exist"
    examples:
      application/json:
        success: -1
        message: "Mahindra booking not exist"
  201_h:
    description: RevvV2 booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "RevvV2 booking not exist"
    examples:
      application/json:
        success: -1
        message: "RevvV2 booking not exist"
  201_i:
    description: Spinny booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Spinny booking not exist"
    examples:
      application/json:
        success: -1
        message: "Spinny booking not exist"
  201_j:
    description: PrideHonda booking not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "PrideHonda booking not exist"
    examples:
      application/json:
        success: -1
        message: "PrideHonda booking not exist"
  401_a:
    description: Unauthorized role, not Driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: User restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500_a:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Server error"
    examples:
      application/json:
        success: -1
        message: "Server error"

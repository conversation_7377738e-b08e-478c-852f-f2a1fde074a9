tags:
  - Affiliate
summary: Update Affiliate Representative Details
description: >
  Updates the details of an existing affiliate representative. Fields like username, mobile, regions, tab access, notifications, and affiliate assignment can be updated. Also supports enabling/disabling reps via the `restricted` flag.
parameters:
  - name: rep_id
    in: formData
    type: string
    required: true
    description: ID of the affiliate rep to update.
  - name: username
    in: formData
    type: string
    required: false
    description: New username for the rep (must be unique).
  - name: fullname
    in: formData
    type: string
    required: false
    description: Full name of the rep.
  - name: email
    in: formData
    type: string
    required: false
    description: Email address of the rep.
  - name: mobile
    in: formData
    type: string
    required: false
    description: Mobile number of the rep (must be unique).
  - name: regions
    in: formData
    type: string
    required: true
    description: Comma-separated region codes the rep should have access to.
  - name: affiliateId
    in: formData
    type: string
    required: false
    description: Affiliate ID to reassign the rep to.
  - name: tab_access
    in: formData
    type: string
    required: false
    description: Comma-separated bitflag index values for tab access.
  - name: notifications
    in: formData
    type: string
    required: false
    description: Comma-separated bitflag index values for notification access.
  - name: restricted
    in: formData
    type: integer
    required: false
    enum: [0, 1]
    description: Use 1 to restrict (disable) the rep, 0 to unrestrict (enable).

responses:
  200:
    description: Affiliate rep updated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Affiliate Rep updated and logged successfully
  400:
    description: Invalid data or missing parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Mobile number already exists for Rep
  404:
    description: Username already exists.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: This Username already exists please try different
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Internal Server Error

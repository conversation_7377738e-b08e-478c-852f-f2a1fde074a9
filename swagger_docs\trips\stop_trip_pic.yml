tags:
  - Trips
summary: Upload Stop Trip Pictures
description: >
  This endpoint allows drivers to upload stop trip pictures along with other optional parameters like extra flags (ex1, ex2, etc.).
parameters:
  - name: book_id
    in: formData
    type: integer
    required: true
    description: The booking ID of the trip.
  - name: ex1
    in: formData
    type: boolean
    required: false
    description: Extra flag 1 (optional).
  - name: ex2
    in: formData
    type: boolean
    required: false
    description: Extra flag 2 (optional).
  - name: ex3
    in: formData
    type: boolean
    required: false
    description: Extra flag 3 (optional).
  - name: ex4
    in: formData
    type: boolean
    required: false
    description: Extra flag 4 (optional).
responses:
  200:
    description: Pictures uploaded successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Trip stop pictures uploaded successfully"
    examples:
      application/json:
        success: 1
        message: "Trip stop pictures uploaded successfully"
  401_a:
    description: Unauthorized user (Not a driver)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized user)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized: Not a driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized: Not a driver"
  401_b:
    description: Driver account disabled
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for disabled driver account)
          example: -1
        message:
          type: string
          description: Error message
          example: "Driver account disabled"
    examples:
      application/json:
        success: -1
        message: "Driver account disabled"
  201:
    description: Incomplete form (Missing book_id)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete form)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form: Missing book_id"
    examples:
      application/json:
        success: -2
        message: "Incomplete form: Missing book_id"
  500_a:
    description: Failed to upload trip stop pictures
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for failure)
          example: -4
        message:
          type: string
          description: Error message
          example: "Failed to upload trip stop pictures"
    examples:
      application/json:
        success: -4
        message: "Failed to upload trip stop pictures"
  500_b:
    description: Failed to log trip stop action
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for failure)
          example: -3
        message:
          type: string
          description: Error message
          example: "Failed to log trip stop action"
    examples:
      application/json:
        success: -3
        message: "Failed to log trip stop action"

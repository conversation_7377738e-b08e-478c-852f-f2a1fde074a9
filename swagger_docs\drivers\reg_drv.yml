tags:
  - Driver
summary: Register a new driver
description: >
  This endpoint allows a new driver to register by providing personal details, license information, and other documents.
parameters:
  - name: Authorization
    in: header
    type: string
    required: false
    description: Bearer JWT token for authentication (optional for registration)
  - name: fname
    in: formData
    type: string
    required: true
    description: Driver's first name
  - name: lname
    in: formData
    type: string
    required: true
    description: Driver's last name
  - name: mobile
    in: formData
    type: string
    required: true
    description: Driver's mobile number
  - name: alt_mobile
    in: formData
    type: string
    required: false
    description: Driver's alternate mobile number (optional)
  - name: pwd
    in: formData
    type: string
    required: true
    description: Driver's password
  - name: lic_no
    in: formData
    type: string
    required: true
    description: Driver's license number
  - name: region
    in: formData
    type: string
    required: true
    description: Driver's operating region
  - name: perma
    in: formData
    type: boolean
    required: false
    description: Whether the driver is permanent or temporary (1 for permanent, 0 for temporary)
  - name: email
    in: formData
    type: string
    required: false
    description: Driver's email (optional)
  - name: pic
    in: formData
    type: file
    required: true
    description: Driver's profile picture
  - name: lic_doc
    in: formData
    type: file
    required: true
    description: Front side of the driver's license document
  - name: lic_doc_back
    in: formData
    type: file
    required: false
    description: Back side of the driver's license document (optional)
  - name: id_doc
    in: formData
    type: file
    required: true
    description: Front side of the driver's ID document
  - name: id_doc_back
    in: formData
    type: file
    required: false
    description: Back side of the driver's ID document (optional)
  - name: acc_no
    in: formData
    type: string
    required: false
    description: Driver's bank account number (optional)
  - name: ifsc
    in: formData
    type: string
    required: false
    description: IFSC code for the driver's bank (optional)
  - name: pan_no
    in: formData
    type: string
    required: false
    description: Driver's PAN number (optional)
  - name: trip_pref
    in: formData
    type: integer
    required: false
    description: Driver's trip preferences (default is 3)
responses:
  200:
    description: Driver registration successful
    schema:
      type: object
      properties:
        response:
          type: integer
          description: Success flag (0 for success)
          example: 0
        msg:
          type: string
          description: Success message
          example: "Success"
  201_a:
    description: Incomplete form data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for incomplete form)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Incomplete form"
    examples:
      application/json:
        success: -1
        msg: "Incomplete form"
  201_b:
    description: Invalid fields (mobile or email)
    schema:
      type: object
      properties:
        response:
          type: integer
          description: Failure flag (-2 for invalid fields)
          example: -2
        msg:
          type: string
          description: Error message
          example: "Invalid fields"
        fields:
          type: string
          description: Fields with invalid values
          example: "Mobile or email"
    examples:
      application/json:
        response: -2
        msg: "Invalid fields"
        fields: "Mobile or email"
  403:
    description: Integrity constraint error
    schema:
      type: object
      properties:
        response:
          type: integer
          description: Failure flag (-3 for integrity error)
          example: -3
        msg:
          type: string
          description: Error message
          example: "Integrity constraint error"
    examples:
      application/json:
        response: -3
        msg: "Integrity constraint error"
  500:
    description: Database error during registration
    schema:
      type: object
      properties:
        response:
          type: integer
          description: Failure flag (4 for database error)
          example: 4
        msg:
          type: string
          description: Error message
          example: "Database error"
    examples:
      application/json:
        response: 4
        msg: "Database error"

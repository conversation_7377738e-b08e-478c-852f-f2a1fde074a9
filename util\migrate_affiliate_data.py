import uuid
import time
from datetime import datetime
from flask import current_app as app
from db_config import mongo_client
from db_config import mdb, db
from booking_params import BookingParams
# Import models
from models import (
    BhandariRep, BhandariBookings, DriverSearch, SpinnyBookings, SpinnyRep,
    TripStartPic, TripEndPic, Bookings, BookingAlloc, BookingCancelled, BookPricing,Trip,TripPricing
)
from affiliate_b2b.affiliate_models import Affiliate, AffBookingLogs, AffiliateDriverSearch,AffiliateRep
from affiliate_b2b.api_integration_constants import SPINNY_TYPE_MAPPING

# Constants to generalize the migration
BOOKINGS_DB = SpinnyBookings         # Change this to any bookings table you need to use
REPS_DB = SpinnyRep                  # Change this to the corresponding reps table if needed
AFFILIATE_CLIENT_NAME = "Spinny"    # Update the client name if using a different affiliate
AFFILIATE_REP_ID = 4

def log_trip_type_change(booking, old_trip_type, new_trip_type, is_migrated):
    affiliate_migration_logs = app.mdb["affiliate_migration_logs"]
    existing_log = affiliate_migration_logs.find_one({"booking_id": booking.ref})
    current_time = datetime.utcnow().isoformat() + "Z"
    if existing_log:
        update_result = affiliate_migration_logs.update_one(
            {"booking_id": booking.ref},
            {"$set": {"logged_at": current_time, "is_migrated": is_migrated}}
        )
        print(f"Existing log updated for booking {booking.ref}: modified {update_result.modified_count} document(s).")
        return
    log_doc = {
        "booking_id": booking.ref,
        "previous_trip_type": old_trip_type,  # original value from the source
        "current_trip_type": new_trip_type,
        "logged_at": current_time,
        "is_migrated": is_migrated
    }
    affiliate_migration_logs.insert_one(log_doc)
    print(f"Log inserted for booking {booking.ref}.")

def migrate_bhandari_bookings(bookings, search_id_mapping):
    print("Migrating Bhandari bookings...")
    start_time = time.time()

    total = len(bookings)
    print(f"Total bookings to migrate: {total}")

    # Fetch the affiliate using the constant client name
    affiliate = db.session.query(Affiliate).filter_by(client_name=AFFILIATE_CLIENT_NAME).first()
    affiliate_id = affiliate.id if affiliate else -1

    count = 0
    affiliate_book_collection = app.mdb["affiliate_book"]

    for booking in bookings:

        booking_entry = db.session.query(Bookings).filter_by(id=booking.ref).first()
        booking_entry.type = BookingParams.TYPE_B2B
        db.session.commit()
        print(f"Updated {count} Bhandari Booking entries to TYPE_B2B.")

        # Check if this booking has already been migrated
        existing_doc = affiliate_book_collection.find_one({"book_ref": booking.ref})
        if existing_doc:
            print(f"Booking {booking.ref} already migrated; skipping.")
            continue

        start_pics = db.session.query(TripStartPic).filter_by(book_id=booking.ref).first()
        end_pics = db.session.query(TripEndPic).filter_by(book_id=booking.ref).first()

        start_pic_json = {
            "car_left": start_pics.car_left,
            "car_right": start_pics.car_right,
            "car_back": start_pics.car_back,
            "car_front": start_pics.car_front,
            "selfie": start_pics.selfie,
            "extra1": start_pics.extra1,
            "extra2": start_pics.extra2,
            "extra3": start_pics.extra3,
            "extra4": start_pics.extra4
        } if start_pics else {}

        end_pic_json = {
            "car_left": end_pics.car_left,
            "car_right": end_pics.car_right,
            "car_back": end_pics.car_back,
            "car_front": end_pics.car_front,
            "extra1": end_pics.extra1,
            "extra2": end_pics.extra2,
            "extra3": end_pics.extra3,
            "extra4": end_pics.extra4
        } if end_pics else {}

        # For Bhandari, compute a user-friendly trip name based on booking.trip_type.
        trip_name = (
            "Delivery" if booking.trip_type == BOOKINGS_DB.TRIP_DELIVERY else
            "Pickup" if booking.trip_type == BOOKINGS_DB.TRIP_PICKUP else
            "Both"
        )

        old_booking_type = BookingParams.TYPE_SPINNY
        new_booking_type = BookingParams.TYPE_B2B
        log_trip_type_change(booking, old_booking_type,new_booking_type,True)

        doc = {
            "search_id": search_id_mapping.get(booking.ref, ""),
            "book_ref": booking.ref,
            "appointment_id": booking.appt or "",
            "trip_name": trip_name,
            "trip_type": "One Way",
            "affiliate_id": affiliate_id,
            "datetime": datetime.utcnow().isoformat() + "Z",
            "vehicle_model": booking.veh_model,
            "vehicle_no": booking.veh_no,
            "trip_start_images": start_pic_json,
            "trip_stop_images": end_pic_json
        }

        affiliate_book_collection.insert_one(doc)
        count += 1
        print(f"Bookings migrated: {count}/{total} (Remaining: {total - count})")

    elapsed = time.time() - start_time
    print(f"Bookings migration complete for affiliate ID: {affiliate_id} in {elapsed:.2f} seconds.\n")

def migrate_spinny_bookings(bookings, search_id_mapping):
    print("Migrating Spinny bookings...")
    start_time = time.time()

    total = len(bookings)
    print(f"Total bookings to migrate: {total}")

    # Fetch the affiliate using the constant client name
    affiliate = db.session.query(Affiliate).filter_by(client_name=AFFILIATE_CLIENT_NAME).first()
    affiliate_id = affiliate.id if affiliate else -1
    affiliate_book_collection = app.mdb["affiliate_book"]

    count = 0

    for booking in bookings:
        # Check if the booking already exists in the affiliate collection

        booking_entry = db.session.query(Bookings).filter_by(id=booking.ref).first()
        booking_entry.type = BookingParams.TYPE_B2B
        db.session.commit()
        print(f"Updated {count} Spinny Booking entries to TYPE_B2B.")

        existing_doc = affiliate_book_collection.find_one({"book_ref": booking.ref})
        if existing_doc:
            print(f"Booking {booking.ref} already migrated; skipping.")
            continue

        start_pics = db.session.query(TripStartPic).filter_by(book_id=booking.ref).first()
        end_pics = db.session.query(TripEndPic).filter_by(book_id=booking.ref).first()
        rep_object= db.session.query(AffiliateRep).filter_by(id=AFFILIATE_REP_ID).first()

        start_pic_json = {
            "car_left": start_pics.car_left,
            "car_right": start_pics.car_right,
            "car_back": start_pics.car_back,
            "car_front": start_pics.car_front,
            "selfie": start_pics.selfie,
            "extra1": start_pics.extra1,
            "extra2": start_pics.extra2,
            "extra3": start_pics.extra3,
            "extra4": start_pics.extra4
        } if start_pics else {}

        end_pic_json = {
            "car_left": end_pics.car_left,
            "car_right": end_pics.car_right,
            "car_back": end_pics.car_back,
            "car_front": end_pics.car_front,
            "extra1": end_pics.extra1,
            "extra2": end_pics.extra2,
            "extra3": end_pics.extra3,
            "extra4": end_pics.extra4
        } if end_pics else {}

        start_images_structure=[
            {
                "imageTitle": "Car Left",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload car left image",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Car Right",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload car right image",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Car Back",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload car back image",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Car Front",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload car front image",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Selfie",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload selfie image",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra1",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra1",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra2",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra2",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra3",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra3",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra4",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra4",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            }
        ]

        stop_images_structure=[
            {
                "imageTitle": "Car Left",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload stop car left image",
                "imageTripType": "One Way",
                "imageTripStage": "stop"
            },
            {
                "imageTitle": "Car Right",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload stop car right image",
                "imageTripType": "One Way",
                "imageTripStage": "stop"
            },
            {
                "imageTitle": "Car Back",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload stop car back image",
                "imageTripType": "One Way",
                "imageTripStage": "stop"
            },
            {
                "imageTitle": "Car Front",
                "imageType": "Mandatory",
                "imageDataType": "Image",
                "imageDesc": "Upload stop car front image",
                "imageTripType": "One Way",
                "imageTripStage": "stop"
            },

            {
                "imageTitle": "Extra1",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra1",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra2",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra2",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra3",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra3",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            },
            {
                "imageTitle": "Extra4",
                "imageType": "Optional",
                "imageDataType": "Image",
                "imageDesc": "Upload Extra4",
                "imageTripType": "One Way",
                "imageTripStage": "start"
            }
        ]

        trip_type_json = [
            {
                "trip_type_name": "Customer To Hub",
                "trip_type_category": "",
                "tripIndex": 0,
                "tripType": "Customer To Hub",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Customer To Partner Workshop",
                "trip_type_category": "",
                "tripIndex": 1,
                "tripType": "Customer To Partner Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Customer To Spinny Workshop",
                "trip_type_category": "",
                "tripIndex": 2,
                "tripType": "Customer To Spinny Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Hub To Customer",
                "trip_type_category": "",
                "tripIndex": 3,
                "tripType": "Hub To Customer",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Hub To Spinny Workshop",
                "trip_type_category": "",
                "tripIndex": 4,
                "tripType": "Hub To Spinny Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Spinny Workshop To Hub",
                "trip_type_category": "",
                "tripIndex": 5,
                "tripType": "Spinny Workshop To Hub",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Hub To Hub",
                "trip_type_category": "",
                "tripIndex": 6,
                "tripType": "Hub To Hub",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Hub To Partner Workshop",
                "trip_type_category": "",
                "tripIndex": 7,
                "tripType": "Hub To Partner Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Partner Workshop To Hub",
                "trip_type_category": "",
                "tripIndex": 8,
                "tripType": "Partner Workshop To Hub",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Hub To OEM",
                "trip_type_category": "",
                "tripIndex": 9,
                "tripType": "Hub To OEM",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "OEM To Hub",
                "trip_type_category": "",
                "tripIndex": 10,
                "tripType": "OEM To Hub",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Partner Workshop To Spinny Workshop",
                "trip_type_category": "",
                "tripIndex": 11,
                "tripType": "Partner Workshop To Spinny Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Spinny Workshop To Partner Workshop",
                "trip_type_category": "",
                "tripIndex": 12,
                "tripType": "Spinny Workshop To Partner Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "Partner Workshop To Customer",
                "trip_type_category": "",
                "tripIndex": 13,
                "tripType": "Partner Workshop To Customer",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "OEM To Workshop",
                "trip_type_category": "",
                "tripIndex": 14,
                "tripType": "OEM To Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "OEM To Partner Workshop",
                "trip_type_category": "",
                "tripIndex": 15,
                "tripType": "OEM To Partner Workshop",
                "startImages": [],
                "stopImages": []
            },
            {
                "trip_type_name": "OEM To Spinny Workshop",
                "trip_type_category": "",
                "tripIndex": 16,
                "tripType": "OEM To Spinny Workshop",
                "startImages": [],
                "stopImages": []
            }
        ]


        custom_json=[
                {
                    "type": "Dropdown",
                    "placeholder": "Enter Business Function",
                    "label": "Business Function",
                    "value": ["Supply","Demand","Refurb","Pre-Sales","Post-Sales","Servicing"]
                },
                {
                    "type": "Dropdown",
                    "placeholder": "Enter Business Category",
                    "label": "Business Category",
                    "value": ["Assured","BCM","Auction"]
                }
            ]

        trip_name = SPINNY_TYPE_MAPPING.get(booking.trip_type, "Unknown")
        new_trip_type = "One Way"  # Overriding as per migration

        # Log the change before inserting
        old_booking_type = BookingParams.TYPE_SPINNY
        new_booking_type = BookingParams.TYPE_B2B
        log_trip_type_change(booking, old_booking_type,new_booking_type,True)

        doc = {
            "search_id": search_id_mapping.get(booking.ref, ""),
            "book_ref": booking.ref,
            "appointment_id": booking.appt or "",
            "trip_name": trip_name,
            "trip_type": new_trip_type,
            "affiliate_id": affiliate_id,
            "client_name": AFFILIATE_CLIENT_NAME,
            "booking_created_at": booking_entry.created_at.isoformat() + "Z",
            "src_nickname_id":"0",
            "dest_nickname_id":"0",
            "rep_id": AFFILIATE_REP_ID,
            "rep_fullname": rep_object.fullname,
            "rep_username": rep_object.user_name,
            "rep_mobile": rep_object.mobile,
            "vehicle_model": booking.veh_model,
            "vehicle_no": booking.veh_no,
            "trip_start_images_structure": start_images_structure,
            "trip_stop_images_structure": stop_images_structure,
            "trip_start_images": start_pic_json,
            "trip_stop_images": end_pic_json,
            "trip_type_list": trip_type_json,
            "custom": custom_json,
            "custom_data": {
                "businessCategory": booking.business_cat,
                "businessFunction": booking.business_func
            }
        }

        affiliate_book_collection.insert_one(doc)
        count += 1
        print(f"Bookings migrated: {count}/{total} (Remaining: {total - count})")

    elapsed = time.time() - start_time
    print(f"Bookings migration complete for affiliate ID: {affiliate_id} in {elapsed:.2f} seconds.\n")

def migrate_reps_to_affiliate_book(bookings):
    """Update spoc_data in affiliate_book using reps data and add drop_mob from each booking."""
    print("Migrating reps data to affiliate_book (updating spoc_data and adding drop_mob)...")
    start_time = time.time()

    # Build a dictionary mapping rep id to rep object for quick lookup.
    reps = db.session.query(REPS_DB).all()
    rep_mapping = {rep.id: rep for rep in reps}
    affiliate_book_collection = app.mdb["affiliate_book"]

    updated_count = 0

    # Loop over each booking once.
    for booking in bookings:
        affiliate_book_entry = affiliate_book_collection.find_one({"book_ref": booking.ref})
        if not affiliate_book_entry:
            print(f"No affiliate book entry found in MongoDB for booking {booking.ref}.")
            continue

        spoc_data = affiliate_book_entry.get("spoc_data", {})

        # Update rep info if booking has rep and rep info isn't already added.
        if booking.rep and not spoc_data.get("source_spoc_name"):
            rep = rep_mapping.get(booking.rep)
            if rep:
                spoc_data["source_spoc_name"] = rep.name
                spoc_data["source_spoc_contact"] = str(rep.mobile)
                updated_count += 1
                print(f"Updated rep info for booking {booking.ref}.")

        # Update drop_mob for every booking.
        spoc_data["dest_spoc_contact"] = booking.drop_mob
        spoc_data["dest_spoc_name"] = "Spinny User" if BOOKINGS_DB==SpinnyBookings else "Bhandari User"

        # Save updates back to Mongo.
        affiliate_book_collection.update_one(
            {"_id": affiliate_book_entry["_id"]},
            {"$set": {"spoc_data": spoc_data}}
        )
        print(f"Updated drop_mob for booking {booking.ref}.")

    elapsed = time.time() - start_time
    print(f"Reps and drop_mob migration complete in {elapsed:.2f} seconds. Total rep updates: {updated_count}.")


def migrate_affiliate_booking_logs(bookings):
    print("Migrating Affiliate Booking Logs...")
    start_time = time.time()

    total = len(bookings)
    print(f"Total bookings for AffiliateBookingLogs migration: {total}")

    # Fetch the affiliate using the constant client name
    affiliate = db.session.query(Affiliate).filter_by(client_name=AFFILIATE_CLIENT_NAME).first()
    affiliate_id = affiliate.id if affiliate else -1

    count = 0
    batch_size = 100  # Adjust this value as needed

    for booking in bookings:
        # Check if a booking log for this booking already exists
        existing_log = db.session.query(AffBookingLogs).filter_by(book_id=booking.ref).first()
        if existing_log:
            print(f"Affiliate Booking Log for booking {booking.ref} already exists; skipping.")
            continue

        # Join with BookingAlloc table to get alloc_id
        booking_alloc = db.session.query(BookingAlloc).filter_by(booking_id=booking.ref).first()
        alloc_id = booking_alloc.id if booking_alloc else None

        # Join with BookingCancelled table to get cancel_id
        booking_cancelled = db.session.query(BookingCancelled).filter_by(booking=booking.ref).first()
        cancel_id = booking_cancelled.id if booking_cancelled else None

        # Create Affiliate Booking Log record.
        aff_booking_log = AffBookingLogs(
            rep_id=None,
            admin_id=None,
            aff_id=affiliate_id,
            book_id=booking.ref,
            alloc_id=alloc_id,
            cancel_id=cancel_id
        )

        db.session.add(aff_booking_log)
        count += 1
        print(f"Affiliate Booking Log created for booking {booking.ref}: {count}/{total} (Remaining: {total - count})")

        # Commit in batches to persist entries even if a failure occurs later.
        if count % batch_size == 0:
            db.session.commit()
            print(f"Committed {count} entries so far...")

    # Final commit for any remaining records
    db.session.commit()
    elapsed = time.time() - start_time
    print(f"Affiliate Booking Logs migration complete in {elapsed:.2f} seconds.\n")

def migrate_affiliate_driver_search(bookings, search_id_mapping):
    print("Migrating Affiliate Driver Searches...")
    start_time = time.time()

    # Fetch the affiliate using the constant client name
    affiliate = db.session.query(Affiliate).filter_by(client_name=AFFILIATE_CLIENT_NAME).first()
    affiliate_id = affiliate.id if affiliate else -1

    total = len(bookings)
    count = 0
    batch_size = 100  # Optional: commit every 100 records

    for booking in bookings:
        # Get the corresponding search id from Bookings using booking.ref
        search_id = search_id_mapping.get(booking.ref)
        book_pricing = db.session.query(BookPricing).filter_by(book_id=booking.ref).first()
        if not search_id:
            print(f"No search id for booking {booking.ref}; skipping.")
            continue

        # Retrieve the DriverSearch record using the search_id
        driver_search = db.session.query(DriverSearch).filter_by(id=search_id).first()
        if not driver_search:
            print(f"No DriverSearch entry found for booking {booking.ref} with search id {search_id}; skipping.")
            continue

        # Check if the AffiliateDriverSearch record already exists
        existing_ads = db.session.query(AffiliateDriverSearch).filter_by(id=driver_search.id).first()
        if existing_ads:
            print(f"AffiliateDriverSearch record for search id {driver_search.id} already exists; skipping.")
            continue

        affiliate_driver_search = AffiliateDriverSearch(
            id=driver_search.id,
            affiliate=affiliate_id,
            rep_id= AFFILIATE_REP_ID,  # Set rep_id as needed
            car_type=driver_search.car_type,
            reflat=driver_search.reflat,
            reflong=driver_search.reflong,
            time=driver_search.time,
            date=driver_search.date,
            dur=driver_search.dur,
            timestamp=driver_search.timestamp,
            type=driver_search.type,
            days=driver_search.days,
            insurance=driver_search.insurance,
            region=driver_search.region,
            dist=driver_search.dist,
            source=driver_search.source,
            estimate= book_pricing.estimate
        )


        db.session.add(affiliate_driver_search)
        count += 1
        print(f"Copied DriverSearch {driver_search.id} for booking {booking.ref}: {count}/{total}")

        # Optional: Commit in batches to avoid a large transaction
        if count % batch_size == 0:
            db.session.commit()
            print(f"Committed {count} entries so far...")

    db.session.commit()  # Final commit for any remaining records
    elapsed = time.time() - start_time
    print(f"Affiliate Driver Search migration complete in {elapsed:.2f} seconds.")

def revert_booking(booking_id):
    """
    Reverts all migration changes for the given booking_id.

    This includes:
      - Removing the affiliate booking document from MongoDB (affiliate_book_collection)
      - Updating the corresponding migration log entry in affiliate_migration_logs (is_migrated -> False)
      - Removing any AffiliateBookingLogs rows from the relational database
      - Removing the AffiliateDriverSearch entry if applicable
    """
    print(f"Reverting migration for booking {booking_id}...")
    revert_start = time.time()
    affiliate_book_collection = app.mdb["affiliate_book"]
    affiliate_migration_logs = app.mdb["affiliate_migration_logs"]

    # First, try to get the migrated booking document from MongoDB.
    booking_doc = affiliate_book_collection.find_one({"book_ref": booking_id})
    migration_logs = affiliate_migration_logs.find({"booking_id": booking_id})
    search_id = None
    if booking_doc:
        search_id = booking_doc.get("search_id")
        # Delete the document from the affiliate_book_collection
        affiliate_book_collection.delete_one({"_id": booking_doc["_id"]})
        print(f"Deleted migrated booking document for booking {booking_id} from MongoDB.")
    else:
        print(f"No migrated booking document found in MongoDB for booking {booking_id}.")

    # Remove AffiliateBookingLogs for this booking from the relational database.
    try:
        logs = db.session.query(AffBookingLogs).filter_by(book_id=booking_id).all()
        if logs:
            for log in logs:
                db.session.delete(log)
            db.session.commit()
            print(f"Deleted {len(logs)} AffiliateBookingLogs entry(ies) for booking {booking_id}.")
        else:
            print(f"No AffiliateBookingLogs found for booking {booking_id}.")
    except Exception as e:
        db.session.rollback()
        print(f"Error deleting AffiliateBookingLogs for booking {booking_id}: {e}")

    # Remove the AffiliateDriverSearch record if we can determine it from the search_id.
    if search_id:
        try:
            ads = db.session.query(AffiliateDriverSearch).filter_by(id=search_id).first()
            if ads:
                db.session.delete(ads)
                db.session.commit()
                print(f"Deleted AffiliateDriverSearch record with id {search_id} for booking {booking_id}.")
            else:
                print(f"No AffiliateDriverSearch record found with id {search_id} for booking {booking_id}.")
        except Exception as e:
            db.session.rollback()
            print(f"Error deleting AffiliateDriverSearch for booking {booking_id}: {e}")
    else:
        print(f"No search_id available for booking {booking_id}; skipping AffiliateDriverSearch deletion.")

   # Update the Booking_DB entry's trip_type to TYPE_B2B.
    try:
        # Assuming that the primary key for the booking is stored in booking.ref (or booking_id)
        booking_entry = db.session.query(Bookings).filter_by(id=booking_id).first()
        if booking_entry:
            booking_entry.type = BookingParams.TYPE_SPINNY if BOOKINGS_DB == SpinnyBookings else BookingParams.TYPE_BHANDARI
            db.session.commit()
            print(f"Updated Booking_DB entry trip_type to TYPE_B2B for booking {booking_id}.")
        else:
            print(f"No Booking_DB entry found for booking {booking_id}.")
    except Exception as e:
        db.session.rollback()
        print(f"Error updating Booking_DB entry for booking {booking_id}: {e}")

    # Instead of deleting the migration log, update it to mark is_migrated as False.
    updated = affiliate_migration_logs.update_many(
        {"booking_id": booking_id},
        {"$set": {"is_migrated": False, "logged_at": datetime.utcnow().isoformat() + "Z","previous_trip_type": BookingParams.TYPE_B2B,"current_trip_type": BookingParams.TYPE_SPINNY if BOOKINGS_DB == SpinnyBookings else BookingParams.TYPE_BHANDARI}}
    )
    if updated.modified_count > 0:
        print(f"Updated migration log for booking {booking_id} to is_migrated False.")
    else:
        print(f"No migration log found for booking {booking_id} to update.")
    elapsed = time.time() - revert_start
    print(f"Revert process complete for booking {booking_id} in {elapsed:.2f} seconds.")

def revert_multiple_bookings(bookings):
    for booking in bookings:
        revert_booking(booking.ref)
        print(f"Completed revert process for booking: {booking.ref}\n")

#for already migrated bookings
def migrate_trip_pricing(bookings):
    print("→ Starting single‑pass TripPricing migration…")
    start_time = time.time()
    for b in bookings:
        booking = db.session.query(Bookings).filter_by(id=b.ref).first()
        # 1) skip any non‑B2B booking
        if booking.type != BookingParams.TYPE_B2B:
            continue
        book_ref = b.ref
        # 2) Pull pricing info from BookPricing
        bp = db.session.query(BookPricing).filter_by(book_id=book_ref).first()
        if not bp:
            print(f"  – no BookPricing for {book_ref}, skipping")
            continue
        # 3) Fetch the trip price
        trip = db.session.query(Trip).filter_by(book_id=book_ref).first()
        if not trip:
            print(f"  – no Trip for {book_ref}, skipping")
            continue
        trip_price = trip.price or 0.0
        night_charge = bp.night_ch or 0.0
        base_charge = trip_price - night_charge
        # Check if already migrated in BookPricing
        if bp.base_ch == 0.0 and bp.night_ch == 0.0:
            # Also check if TripPricing already exists with same values
            tp = db.session.query(TripPricing).filter_by(book_id=book_ref).first()
            if tp and tp.base_ch == tp.driver_base_ch and tp.night_ch == tp.driver_night_ch:
                print(f"  – already migrated: {book_ref}, skipping")
                continue
        # Migrate BookPricing
        bp.driver_base_ch = bp.base_ch
        bp.driver_night_ch = bp.night_ch
        bp.base_ch = 0.0
        bp.night_ch = 0.0
        db.session.commit()
        # 4) Create or update TripPricing
        tp = db.session.query(TripPricing).filter_by(book_id=book_ref).first()
        if not tp:
            tp = TripPricing(
                book_id         = book_ref,
                base_ch         = base_charge,
                cartype_ch      = 0.0,
                night_ch        = night_charge,
                ot_ch           = 0.0,
                booking_ch      = 0.0,
                dist_ch         = 0.0,
                cgst            = 0.0,
                sgst            = 0.0,
                insurance_ch    = 0.0,
                driver_base_ch  = base_charge,
                driver_night_ch = night_charge,
                driver_ot_ch    = 0.0,
            )
            db.session.add(tp)
            db.session.commit()
            print(f"  ✓ created TripPricing for {book_ref}")
        else:
            # Update values if needed (optional, or you can skip entirely)
            tp.base_ch = base_charge
            tp.night_ch = night_charge
            tp.driver_base_ch = base_charge
            tp.driver_night_ch = night_charge
            db.session.commit()
            print(f"  ✓ updated TripPricing for {book_ref}")
    print(f"✓ Done in {time.time() - start_time:.2f}s")

def main():
    overall_start = time.time()
    try:
        print(f"Fetching {AFFILIATE_CLIENT_NAME} Bookings...")
        # Query the bookings table using the constant
        bookings =db.session.query(BOOKINGS_DB).filter(BOOKINGS_DB.id == 55246).all()
        search_id_mapping = dict(db.session.query(Bookings.id, Bookings.search_key).all())
        print(f"All {AFFILIATE_CLIENT_NAME} Bookings fetched")
        if BOOKINGS_DB == BhandariBookings:
            migrate_bhandari_bookings(bookings, search_id_mapping)
        elif BOOKINGS_DB == SpinnyBookings:
            migrate_spinny_bookings(bookings,search_id_mapping)
        migrate_reps_to_affiliate_book(bookings)
        migrate_affiliate_driver_search(bookings, search_id_mapping)
        migrate_affiliate_booking_logs(bookings)
        migrate_trip_pricing(bookings)
        #revert_multiple_bookings(bookings)
        #revert_booking(670806)
    except Exception as e:
        print("Migration failed:", e)
    finally:
        db.session.close()
        mongo_client.close()
        overall_elapsed = time.time() - overall_start
        print(f"Total migration time: {overall_elapsed:.2f} seconds")

if __name__ == '__main__':
    with app.app_context():
        main()

tags:
  - Driver
summary: Retrieve the ongoing trip details for a driver.
description: >
  This API fetches details of the ongoing trip for the driver. It checks the driver's account status and retrieves the most recent trip that has not been completed.
responses:
  200_a:
    description: Ongoing trip details retrieved successfully.
    schema:
      type: object
      properties:
        trip_id:
          type: integer
          example: 1
        message:
          type: string
          example: "Ongoing trip details retrieved successfully."
    examples:
      application/json:
        trip_id: 1
        message: "Ongoing trip details retrieved successfully."
  200_b:
    description: No ongoing trip found.
    schema:
      type: object
      properties:
        trip_id:
          type: integer
          example: -1
        message:
          type: string
          example: "Trip does not exist"
    examples:
      application/json:
        trip_id: -1
        message: "Trip does not exist"
  401_a:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: Driver account disabled.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Driver account disabled"
    examples:
      application/json:
        success: -1
        message: "Driver account disabled"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        trip_id:
          type: integer
          example: -1
        message:
          type: string
          example: "Server error"
    examples:
      application/json:
        trip_id: -1
        message: "Server error"

$(document).ready(function() {
  //window.logoSpacing = 50;    tweak this value to alter the spacing between logo and brand-name
  $('[data-toggle="tooltip"]').tooltip();
  //proper display of brand and logo
  if($("#topMenu").height() > 50) {
    $("#navComponents").css('padding-top','43px');
  }
  //$("#logo").css('left',$("#brandName").css('left').replace("px","")-window.logoSpacing+"px");
  //login responds to enter-key press
  $('#pwd').keydown(function(event) { 
    var keyCode = (event.keyCode ? event.keyCode : event.which);   
    if (keyCode == 13) {
      $('#sbt').trigger('click');
    }
  });
	function getCookie() {
		cookieSplit = document.cookie.split("; ");
		var cookieObject = {};
		cookieSplit.forEach( function(value, index) {
		   var splitResult = value.split("=");
		   cookieObject[splitResult[0]] = splitResult[1];
		});
		return cookieObject;
	}
	function setCookie(name,value,exDays) {

		var d = new Date();
		d.setTime(d.getTime() + (exDays * 24 * 60 * 60 * 1000));
		var expires = "expires=" + d.toUTCString();
		document.cookie = name + "=" + value + ";" + expires + ";path=/";
    }
	var cookie = getCookie();
	//alert(JSON.stringify(cookie));
	var refresh = cookie['csrf_refresh_token'];
	var access = cookie['csrf_access_token'];
	if (access && refresh) {
		var ans = checkRefresh(access, refresh);
		alert(ans);
		if (ans) {
			window.location = window.location.protocol + '//' + window.location.host + "/api/book";
		}
	}
	
	function checkRefresh(csrf_token, refresh_token) {
		var response = false;
		$.ajax({
			type: "POST",
			url: window.location.protocol + '//' + window.location.host + '/token/verify',
			beforeSend: function(request) {
				request.setRequestHeader('X-CSRF-Token', csrf_token);
			},
			async: false,
			success: function(s) {
				if (s.success != 1) {
					$.ajax({
						type: "POST",
						url: window.location.protocol + '//' + window.location.host + '/token/refresh',
						beforeSend: function(request) {
							request.setRequestHeader('X-CSRF-Token', refresh_token);
						},
						async: false,
						success: function(sr) {
							//alert(JSON.stringify(sr))
							if (sr.refresh != true) response = false;
							else response =  true;
						},
						error: function(er) {
							response =  false;
						}
					});
				} else {
					response =  true;
				}
			},
			error: function(e) {
				if (e.status == 401) {
					$.ajax({
						type: "POST",
						url: window.location.protocol + '//' + window.location.host + '/token/refresh',
						beforeSend: function(request) {
							request.setRequestHeader('X-CSRF-Token', refresh_token);
						},
						async: false,
						success: function(sr) {
							//alert(JSON.stringify(sr))
							if (sr.refresh != true) response =  false;
							else response =  true;
						},
						error: function(err) {
							//alert("huh");
							response = false;
						}
					});
				} else response =  false;
			}
		});
		return response;
	}
	
    //reset register form on page refresh
    $(".form-control").val('');
    $("#pwd").val('');
    //$('#cc').prop('selectedIndex',0);
    $('#driverOption').prop('checked', false);
    $("#lcs").val('');
    $("#pro").val('');
    var match = $(function(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;

    }("mobile"));
    if (match) {
        $("#mobile").val(match.selector);
    }
    //code for refresh ends here

    $("#pwd").focusout(function() {
        var pass = $("#pwd").val();
        if (pass.length == 0) {
            $(this).val('');
            $(this).attr("placeholder","Enter password");
            $(this).css('border','2px solid red');
        }
    });
    /*$("#mobile").focusin(function() {
		var p=document.getElementById("ems");
        p.style.visibility="hidden";
        var m=document.getElementById("mobile");
        m.style.borderColor="rgba(81,203,238,1)";
    });
    
    $("#pwd").focusin(function() {
		var p=document.getElementById("pms");
        p.style.visibility="hidden";
		var m=document.getElementById("pwd");
        m.style.borderColor="rgba(81,203,238,1)";
     });*/

    $(document).on("click", "#sbt", function(e) {
        e.preventDefault();
        var mob = $("#mobile").val().trim();
        var password = $("#pwd").val();
        var check = $("#signedInCheck").is(":checked");
        var rem = $("#rememberCheck").is(":checked");
        var flag = true;
        var re = new RegExp("^[1-9][0-9]+$");
        if (mob == "") {
          $("#mobile").css('border','2px solid red');
          $("#mobile").popover({
            placement: "top",
            trigger: "hover"
          });
          $('#mobile').data('bs.popover').options.content = "This field cannot be blank";
            /*var pop = $("#mobile").data("bs.popover");
            if (pop)
                pop.options.content = "This field cannot be blank";
            else
                $("#mobile").popover({
                    content: "This field cannot be blank",
                    placement: "left",
                    trigger: "manual"
                });
            //$("#mobile").popover("show");
            $("#mobile").attr("placeholder","This field cannot be blank");
            flag = false;
        */} else if (mob.length != 10 || !re.test(mob)) {
            $("#mobile").css('border','2px solid red');
            $("#mobile").popover({
            placement: "top",
            trigger: "hover"
          });
            $('#mobile').data('bs.popover').options.content = "Enter correct mobile number without country-code";
            /*var pop = $("#mobile").data("bs.popover");
            if (pop)
                pop.options.content = "Enter correct mobile number without country-code";
            else
                $("#mobile").popover({
                    content: "Enter correct mobile number without country-code",
                    placement: "left",
                    trigger: "manual"
                });
            //$("#mobile").popover("show");
            $("#mobile").val('');
            $("#mobile").attr("placeholder","Enter correct mobile number without country-code");*/
            flag = false;
        }
        if (password == "") {
          $("#pwd").css('border','2px solid red');
            $("#pwd").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#pwd').data('bs.popover').options.content = "This field cannot be blank";
            //$("#pwd").popover("show");
            //document.getElementById("pwd").placeholder="This field cannot be blank";
            flag = false;
        }
        if (flag) {

            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/token/login',
                data: {
                    mobile: mob,
                    pwd: password,
                    remember: check
                },
                dataType: "json",
                success: function(e) {
                    var msg = JSON.stringify(e);
                    //alert(msg);
                    /*var json=$.parseJSON(msg);
                    alert(json);*/
           
					if (e.success!=1)
                        {
                            document.getElementById("ermsg").style.display = "block";
                        }
                    else {
						//alert(sessionStorage.getItem("d4msessionaccess"));
                        document.getElementById("ermsg").style.visibility = "hidden";
						//alert(id);
                        setCookie("name",e.user_fname);
                        window.location = window.location.protocol + '//' + window.location.host + "/api/book";
                    }

                },
                error: function(e) {
                    //alert(JSON.stringify(e));
                    if(e.status==401||e.status==400)
                        document.getElementById("ermsg").style.display = "block";

                }
            });
        }


    });
    $("#sbt2").click(function(e) {
        e.preventDefault();
        //$(":input").popover("destroy");
        var fName = $("#fname").val().trim();
        var lName = $("#lname").val().trim();
        var mob = $("#phno").val().trim();
        var email = $("#em").val().trim();
        var pass = $("#pass").val().trim();
        //var sex = $("input[type='radio']:checked").val();
        var opt = $("#driverOption:checked").val();
        var cc = $("#cc_code").val().trim();
        //alert(cc);
        var flag = true;
        var re = new RegExp("^[1-9][0-9]+$");
        var fe = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if (fName == "") {
            $("#fname").css('border','2px solid red');
            $("#fname").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#fname').data('bs.popover').options.content = "First name please";
            flag = false;
        }
        else $("#fname").popover('destroy');
        if (lName == "") {
            $("#lname").css('border','2px solid red');
            $("#lname").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#lname').data('bs.popover').options.content = "Last name please";
            flag = false;
        }
        else $("#lname").popover('destroy');
        if (mob == "") {
            $("#phno").css('border','2px solid red');
            $("#phno").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#phno').data('bs.popover').options.content = "Phone number is required";
            flag = false;
        } else if (mob.length != 10 || !re.test(mob)) {
            $("#phno").css('border','2px solid red');
            $("#phno").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#phno').data('bs.popover').options.content = "Enter correct phone number without country-code";
            flag = false;
        }
        else $("#phno").popover('destroy');
        if (email == "") {
            $("#em").css('border','2px solid orange');
            $("#em").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#em').data('bs.popover').options.content = "Please specify an e-mail address";
            //$("#em").popover("show");
            //$("#em").attr("placeholder","This field cannot be blank");
            //flag=false;
        } else if (!fe.test(email)) {
            $("#em").css('border','2px solid orange');
            $("#em").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#em').data('bs.popover').options.content = "Invalid E-mail format";
            $("#em").val('');
            flag = false;
        }
        else $("#em").popover('destroy');
        if (pass.length < 6) {
            $("#pass").css('border','2px solid red');
            $("#pass").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#pass').data('bs.popover').options.content = "Password must be atleast 6 characters long";
            $("#pass").val('');
            flag = false;
        }
        else $("#pass").popover('destroy');
        if (opt) {
            var lno = $("#lno").val().trim();
            //regex desc: two alphabets
            //then two numbers
            //followed by a "valid" year -- this regex takes years between 1900-2099 as valid
            //then 7 numbers
            //source: https://www.codeproject.com/Questions/1065006/how-to-validate-drivers-license-number
            var ltest = new RegExp("^[A-Za-z]{2}[0-9]{2}(19|20)[0-9]{9}$");
            if (!ltest.test(lno)) {
              $("#lno").css('border','2px solid red');
              $("#lno").popover({
                placement: "top",
                trigger: "hover"
              });
              if(lno!="") $('#lno').data('bs.popover').options.content = "Licence number is not in correct format";
              else $('#lno').data('bs.popover').options.content = "This field is required";
              $("#lno").val('');
                //flag=false;
            }
            else $("#lno").popover('destroy');
            if ($("#lcs").val() == '') {
              $("#licPic").css('border','2px solid red');
              $("#licPic").popover({
                placement: "top",
                trigger: "hover"
              });
              $('#licPic').data('bs.popover').options.content = "You must upload a picture of your licence";
                flag = false;

            }
            else $("#licPic").popover('destroy');
            if ($("#pro").val() == '') {
              $("#profPic").css('border','2px solid red');
              $("#profPic").popover({
                placement: "top",
                trigger: "hover"
              });
              $('#profPic').data('bs.popover').options.content = "Please upload your picture";
                flag = false;

            }
            else $("#profPic").popover('destroy');
        }
        if (flag) {
            e.preventDefault();
            if (cc == "in")
                cCode = "91"; //Harcoded, needs to be changed
            else if (cc == "bd")
                cCode = "880"; //Hardcoded, needs to be changed
            else cCode = "91"; //Fallback, if no match
            var data = new FormData();
            data.append("fname", fName);
            data.append("lname", lName);
            data.append("ccode", cCode);
            data.append("mobile", mob);
            data.append("email", email);
            data.append("pwd", pass);
            //data.append("sex", sex);
            var urlPost = window.location.protocol + '//' + window.location.host + '/api/register_cust';
            if (opt) {
                data.append("licNo", lno);
                var lic = document.getElementById("lcs").files[0];
                var photo = document.getElementById("pro").files[0];
                data.append("licPic", lic);
                data.append("pic", photo);
                urlPost = window.location.protocol + '//' + window.location.host + '/api/register/driver';
            }
            $.ajax({
                type: "POST",
                enctype: "multipart/form-data",
                url: urlPost,
                data: data,
                processData: false,
                crossDomain: true,
                contentType: false,
                cache: false,
                success: function(response) {
                    //alert(JSON.stringify(response));
                      $(".form-control").val('');
                     $("input[type=file]").val('');
                     if(response.response)
                     {
                        $("#error").text(response.msg);
                        $("error").css("visibility","visible");
                     }
                     else
                     document.getElementById('signInForm').click();
                },
                error: function(e) {
                    alert(JSON.stringify(e));
                }
            });

        }

    });
    $("#mobile").click(function() {
      $(this).css('border','');
    });
    $("#pwd").click(function() {
      $(this).css('border','');
      $(this).attr('placeholder','Password');
    });
    $("#fname").click(function() {
        $(this).css('border','');
    });
    $("#lname").click(function() {
        $(this).css('border','');
    });
    $("#phno").click(function() {
        $(this).css('border','');
    });
    $("#em").click(function() {
        $(this).css('border','');
    });
    $("#pass").focusout(function() {
        var x = $(this).val();
        if (x.length < 6) {
            $(this).val('');
            $(this).attr("placeholder","Password must be at least six characters long");
            $(this).css('border','2px solid red');
        } else {
            $(this).popover("hide");
        }
    });
    $("#pass").click(function() {
        $(this).css('border','');
        $(this).attr('placeholder','Enter password');
    });
    $("#pass").focusin(function() {
        $(this).popover("hide");
    });
    $("#lcs").hover(function() {
        $(this).popover("hide");
    });
    $("#pro").hover(function() {
        $(this).popover("hide");
    });
    $("#lno").click(function() {
        $(this).css('border','');
    });
    $("#licPic").click(function() {
        $(this).css('border','');
    });
    $("#profPic").click(function() {
        $(this).css('border','');
    });
    $("#phno").click(function() {
        $(this).popover("hide");
    });
    $(".form-control").click(function() {
       $("#error").css("visibility","hidden");
       $("#ermsg").css("display","none");
    });
    $("input").click(function() {
        $("#licPic").popover("hide");
        $("#profPic").popover("hide");
    });
});
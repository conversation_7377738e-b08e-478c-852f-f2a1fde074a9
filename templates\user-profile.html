<!DOCTYPE html>
<html>

<head>
    <title>Change profile | Drivers4Me</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">

    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/bootstrap.css") }}"/>
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
  	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/nav-common.css") }}">
	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/utility.css") }}">
	<link href="{{ url_for("static", filename="assets/css/hamburger.css") }}" rel="stylesheet">
	<link href="{{ url_for("static", filename="assets/css/user-profile.css") }}" rel="stylesheet">

    
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/jquery-3.2.1.min.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/popper.min.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/bootstrap.min.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/util-web.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/nav-common.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/user-profile.js") }}"></script>

</head>

<body>
   <nav id="brandNav" class="navbar navbar-default">
      <!--<button id="mainNavMenuButton" class="hamburger hamburger--arrow js-hamburger" type="button">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>-->
      <h5 id="pageTitle">Edit Profile</h5>
        <div class="container-fluid">
            <div class="navbar-header">
                <a id="brand" class="navbar-brand brand-basic" href="#">
                    <img src="{{ url_for("static", filename="assets/images/brandLogoMerger.png") }}" alt="DRIVERS4ME">
                </a>
                <a id="brandBorder" class="navbar-brand brand-basic" href="#">
                    <img src="" alt="">
                </a>
            </div>
            <div class="ml-auto">
                <div class="nav navbar-nav">
                    <a id="userProfile" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/Avatar.svg") }}">
                    </a>
                    <a class="nav-item nav-link active nav-user-info-main no-padding" href="#">
                        <p id="userName" class="nav-user-info">User Name</p>
                        <p id="userContact" class="nav-user-info">8882012345</p>
                        <span class="sr-only">(current)</span>
                    </a>
                    <a id="logout" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/logout.svg") }}">
                    </a>
                </div>
            </div>
        </div>
    </nav>
   <!-- <div id="sidenavBackdrop" class="backdrop collapse"></div>
    <div id="mainNavMenu" class="side-nav subpage-sidenav">
        <div class="side-nav-header">
            <img id="userImageHolder" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}">
            <h6 id="userNameHolder">Sample User</h6>
            <h6 id="userContactHolder">88820 12345</h6>
        </div>
        <div class="side-nav-body">
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Bookings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Bookings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Payments.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Payments</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Refer.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Refer</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Settings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Settings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
        </div>
        <div class="side-nav-footer">
            <p class="copyright-statement">All rights reserved. <br> &copy; Copyright <span id="copyrightYear"></span></p>
        </div>
    </div>
    -->
	<div id="profileCentre">
        <div id="firstName" class="row profile-detail">
            <div class="col-12 profile-label"><p>First Name <i class="fa fa-pencil-square-o profile-edit"></i></p></div>
            <div class="col-12 profile-value"><p id="fname"></p></div>
        </div>
        <div id="lastName" class="row profile-detail">
            <div class="col-12 profile-label"><p>Last Name <i class="fa fa-pencil-square-o profile-edit"></i></p></div>
            <div class="col-12 profile-value"><p id="lname"></p></div>
        </div>
        <div id="mobile" class="row profile-detail">
            <div class="col-12 profile-label"><p>Phone Number</p></div>
            <div class="col-12 profile-value"><p id="mob"></p></div>
        </div>
        <div id="email" class="row profile-detail">
            <div class="col-12 profile-label"><p>Email <i class="fa fa-pencil-square-o profile-edit"></i></p></div>
            <div class="col-12 profile-value"><p id="em"></p></div>
        </div>
        <div id="password" class="row profile-detail">
            <div class="col-12 profile-label"><p>Password <i class="fa fa-pencil-square-o profile-edit"></i></p></div>
            <div class="col-12 profile-value"><p id="pwd">**********</p></div>
        </div>
	</div>

    <!-- First Name Modal -->
    <div class="modal profile-detail-edit-modal" id="fnameEditModal">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                <h4 class="modal-title">Change First Name</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="input-group">
                        <input id="inputFname" type="text" class="form-control" placeholder="">
                    </div>

                    <div class="button-tray">
                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success btn-sm confirm-btn">Confirm</button>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Last Name Modal -->
    <div class="modal profile-detail-edit-modal" id="lnameEditModal">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                <h4 class="modal-title">Change Last Name</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="input-group">
                        <input id="inputLname" type="text" class="form-control" placeholder="">
                    </div>

                    <div class="button-tray">
                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success btn-sm confirm-btn">Confirm</button>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Email Address Modal -->
    <div class="modal profile-detail-edit-modal" id="emailEditModal">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                <h4 class="modal-title">Change Email Address</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="input-group">
                        <input id="inputEmail" type="text" class="form-control" placeholder="">
                    </div>

                    <div class="button-tray">
                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success btn-sm confirm-btn">Confirm</button>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Password Modal -->
    <div class="modal profile-detail-edit-modal" id="passwordEditModal">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                <h4 class="modal-title">Change Password</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <label for="inputCurrPass">Old Password:</label>
                    <div class="input-group">
                        <input id="inputCurrPass" type="password" class="form-control" placeholder="">
                        <div class="input-group-append input-visibility-toggle">
                            <span class="input-group-text">
                                <i class="fa fa-eye"></i>
                            </span>
                        </div>
                    </div>


                    <label class="medium-top-margin" for="inputNewPass">New Password:</label>
                    <div class="input-group">
                        <input id="inputNewPass" type="password" class="form-control" placeholder="">
                        <div class="input-group-append input-visibility-toggle">
                            <span class="input-group-text">
                                <i class="fa fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <div class="button-tray">
                        <button type="button" class="btn btn-outline-danger btn-sm cancel-btn" data-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-success btn-sm confirm-btn">Confirm</button>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- SNACKBAR -->
    <div id="snackbar"></div>

    <!-- LOADER -->
    <div id="loader" class="collapse">
        <div class="backdrop"></div>
        <div id="spinner-container">
            <div class="spinner">
                <div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div>
            </div>
        </div>
    </div>

</body>

</html>

tags:
  - Login_admin
summary: Delete User and Invalidate Tokens
description: >
  This endpoint allows an admin to delete a user by disabling the user's account and invalidating all associated tokens. The user must be identified by their `user_id`, and all tokens associated with this user will be added to the blacklist.
parameters:
  - in: formData
    name: user_id
    type: string
    required: true
    description: The ID of the user to be deleted.
    example: "12345"
responses:
  200:
    description: User successfully deleted, and all tokens invalidated.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code indicating success.
          example: 200
        result:
          type: string
          description: Result of the operation.
          example: "SUCCESS"
        message:
          type: string
          description: Success message.
          example: "User deleted and tokens invalidated"
  400:
    description: Bad request due to missing user ID.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code indicating failure due to a bad request.
          example: 400
        result:
          type: string
          description: Result of the operation.
          example: "FAILURE"
        message:
          type: string
          description: Error message explaining why the request failed.
          example: "User ID required"
  404:
    description: User not found in the system.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code indicating failure due to user not being found.
          example: 404
        result:
          type: string
          description: Result of the operation.
          example: "FAILURE"
        message:
          type: object
          properties:
            message:
              type: string
              description: Error message explaining that the user was not found.
              example: "User not found"
  500:
    description: Internal server error occurred during the deletion process.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: HTTP status code indicating failure due to a server error.
          example: 500
        result:
          type: string
          description: Result of the operation.
          example: "FAILURE"
        message:
          type: object
          properties:
            message:
              type: string
              description: Error message detailing the exception that occurred.
              example: "Internal server error"

function BS4CompatibleDateTimePicker(dtPickerElement) {
	dtPickerElement.find('.bootstrap-datetimepicker-widget').find(".glyphicon-chevron-up").html("<i class='fa fa-angle-up'></i>");
	dtPickerElement.find('.bootstrap-datetimepicker-widget').find(".glyphicon-time").html("<i class='fa fa-clock-o'></i>");
	dtPickerElement.find('.bootstrap-datetimepicker-widget').find(".glyphicon-calendar").html("<i class='fa fa-calendar'></i>");
	dtPickerElement.find('.bootstrap-datetimepicker-widget').find(".glyphicon-chevron-down").html("<i class='fa fa-angle-down'></i>");
	dtPickerElement.find('.bootstrap-datetimepicker-widget').find(".glyphicon-chevron-left").html("<i class='fa fa-angle-left'></i>");
	dtPickerElement.find('.bootstrap-datetimepicker-widget').find(".glyphicon-chevron-right").html("<i class='fa fa-angle-right'></i>");
}

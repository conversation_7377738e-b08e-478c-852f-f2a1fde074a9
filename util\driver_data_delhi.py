import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str_1 = (datetime.now() - timedelta(days=8)).date().strftime("%d%m%Y")
date_str_2 = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepathD = D4M_UTIL_PATH + 'output/delhi-zoomcar-driver.csv'
subjectD = "Zoomcar Driver Payment - Delhi - " + date_str_2
filepathCD = D4M_UTIL_PATH + 'output/delhi-cardekho-driver.csv'
subjectCD = "Cardekho Driver Payment - Delhi - " + date_str_2
filepathMH = D4M_UTIL_PATH + 'output/delhi-mahindra-driver.csv'
subjectMH = "Mahindra Driver Payment - Delhi - " + date_str_2
filepathSP = D4M_UTIL_PATH + 'output/delhi-spinny-driver.csv'
subjectSP = "Spinny Driver Payment - Delhi - " + date_str_2
filepathRD = D4M_UTIL_PATH + 'output/delhi-revv_v2-driver.csv'
subjectRD = "Revv_V2 Driver Payment - Delhi - " + date_str_2
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"]
#send_mail(from_addr, to_addr_list, subjectD, content, filepathD)
#send_mail(from_addr, to_addr_list, subjectCD, content, filepathCD)
#send_mail(from_addr, to_addr_list, subjectMH, content, filepathMH)
send_mail(from_addr, to_addr_list, subjectSP, content, filepathSP)
#send_mail(from_addr, to_addr_list, subjectRD, content, filepathRD)
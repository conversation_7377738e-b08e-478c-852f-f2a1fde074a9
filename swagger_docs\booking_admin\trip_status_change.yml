tags:
  - Booking_admin
summary: Change trip status
description: >
  This endpoint allows an admin to change the status of a trip based on the provided parameters. It also validates the required fields based on the expected state of the trip.
parameters:
  - name: driver_user
    in: formData
    type: integer
    required: true
    description: The ID of the driver whose trip status is being changed.
    example: 123
  - name: state
    in: formData
    type: integer
    required: true
    description: The expected new state of the trip (e.g., 0 for a specific status, 3 for another).
    example: 0
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking for which the status is being changed.
    example: 456
  - name: lat
    in: formData
    type: number
    format: float
    required: true
    description: The latitude of the driver's location.
    example: 22.5726
  - name: lng
    in: formData
    type: number
    format: float
    required: true
    description: The longitude of the driver's location.
    example: 88.3639
  - name: cleft
    in: formData
    type: file
    required: false
    description: The file for the left camera (required for certain states).
  - name: cright
    in: formData
    type: file
    required: false
    description: The file for the right camera (required for certain states).
  - name: cback
    in: formData
    type: file
    required: false
    description: The file for the back camera (required for certain states).
  - name: cfront
    in: formData
    type: file
    required: false
    description: The file for the front camera (required for certain states).
  - name: selfie
    in: formData
    type: file
    required: false
    description: The selfie file (required for state 3).
responses:
  200:
    description: Successfully changed trip status.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        status:
          type: integer
          example: 200
        data:
          type: object
          description: Additional data related to the status change.
  400:
    description: Bad request due to invalid parameters or missing required files.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: integer
          example: 400
        error:
          type: object
          properties:
            message:
              type: string
              example: "Invalid params"
  201:
    description: Partial success, missing required files for the expected state.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -7
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: integer
          example: 500
        error:
          type: string
          example: "Internal server error message"

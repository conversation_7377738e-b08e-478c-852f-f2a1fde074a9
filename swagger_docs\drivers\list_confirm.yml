tags:
  - Driver
summary: List Confirmed Bookings for a Driver
description: >
  This endpoint allows a driver to retrieve a list of confirmed bookings for upcoming trips.
responses:
  200:
    description: Successfully retrieved confirmed bookings
    schema:
      type: array
      items:
        type: object
        properties:
          id:
            type: integer
            description: Booking ID
          name:
            type: string
            description: Name of the customer
          startdate:
            type: string
            format: date
            description: Start date of the booking
          starttime:
            type: string
            format: time
            description: Start time of the booking
          enddate:
            type: string
            format: date
            description: End date of the booking
          endtime:
            type: string
            format: time
            description: End time of the booking
          dur:
            type: string
            description: Duration of the booking
          lat:
            type: float
            description: Latitude of the start location
          long:
            type: float
            description: Longitude of the start location
          estimate:
            type: number
            description: Estimated cost of the booking
          car_type:
            type: string
            description: Car type for the booking
          loc:
            type: string
            description: Pickup location
          mobile:
            type: string
            description: Mobile number of the customer (if available)
          trip_type:
            type: integer
            description: Type of the trip (e.g., roundtrip, one-way, etc.)
          days:
            type: integer
            description: Number of days for the booking
          driver_rating:
            type: float
            description: Driver's rating
          driver_type:
            type: integer
            description: Driver type (permanent or temporary)
          payment:
            type: integer
            description: Payment type (e.g., cash, online, etc.)
          insurance:
            type: boolean
            description: Whether the booking includes insurance
          region:
            type: string
            description: Region of the booking
          code:
            type: string
            description: Booking code
    examples:
      application/json:
        - id: 12345
          name: "D4M Customer"
          startdate: "2024-09-25"
          starttime: "09:00:00"
          enddate: "2024-09-26"
          endtime: "09:00:00"
          dur: "24:00:00"
          lat: 22.5726
          long: 88.3639
          estimate: 500
          car_type: "SUV"
          loc: "Salt Lake City"
          mobile: "9876543210"
          trip_type: 1
          days: 1
          driver_rating: 4.5
          driver_type: 1
          payment: 0
          insurance: true
          region: "Kolkata"
          code: "ABCD1234"
  401_a:
    description: User not authorized (i.e., user is not a driver)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not authorized"
    examples:
      application/json:
        success: -1
        message: "User not authorized"
  401_b:
    description: User restricted (i.e., driver's account is restricted)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  200_a:
    description: Driver not available for booking
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for driver not available)
          example: -2
        count:
          type: integer
          description: Number of confirmed bookings found
          example: 5
        message:
          type: string
          description: Status message
          example: "Driver not available"
    examples:
      application/json:
        success: -2
        count: 5
        message: "Driver not available"
  500:
    description: No confirmed bookings found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for no bookings found)
          example: -1
        message:
          type: string
          description: Status message
          example: "No confirmed bookings found"
    examples:
      application/json:
        success: -1
        message: "No confirmed bookings found"

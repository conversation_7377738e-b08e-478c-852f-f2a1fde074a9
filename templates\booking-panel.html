<!DOCTYPE html>
<html ng-app="D4M" >

<head>
  	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/custom-elements.css") }}">
    <link href="{{ url_for("static", filename="assets/css/hamburger.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/utility.css") }}">
  	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/nav-common.css") }}">
	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/booking-panel.css") }}">

	<script src="https://code.angularjs.org/1.6.9/angular.min.js"></script>
  	<script src="https://code.jquery.com/jquery-3.2.1.min.js" integrity="sha256-hwg4gsxgFZhOsEEamdOYGBf13FyQuiTwlAQgxVSNgt4=" crossorigin="anonymous"></script>
  	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/utility.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/nav-common.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/booking-panel.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/moment.js") }}"></script>
	<script src="https://wchat.freshchat.com/js/widget.js"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/freshchat.js") }}"></script>

</head>

<body>
    <nav id="brandNav" class="navbar navbar-default">
      <button id="mainNavMenuButton" class="hamburger hamburger--arrow js-hamburger" type="button">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>
      <h5 id="pageTitle">Bookings</h5>
        <div class="container-fluid">
            <div class="navbar-header">
                <a id="brand" class="navbar-brand brand-basic" href="#">
                    <img src="{{ url_for("static", filename="assets/images/brandLogoMerger.png") }}" alt="DRIVERS4ME">
                </a>
                <a id="brandBorder" class="navbar-brand brand-basic" href="#">
                    <img src="" alt="">
                </a>
            </div>
            <div class="ml-auto">
                <div class="nav navbar-nav">
                    <a id="userProfile" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/Avatar.svg") }}">
                    </a>
                    <a class="nav-item nav-link active nav-user-info-main no-padding" href="#">
                        <p id="userName" class="nav-user-info">Very very elongated User Name</p>
                        <p id="userContact" class="nav-user-info">8882012345</p>
                        <span class="sr-only">(current)</span>
                    </a>
                    <a id="logout" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/logout.svg") }}">
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div id="mainNavMenu" class="side-nav subpage-sidenav">
        <div class="side-nav-header">
            <img id="userImageHolder" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}">
            <h6 id="userNameHolder">Sample User</h6>
            <h6 id="userContactHolder">88820 12345</h6>
        </div>
        <div class="side-nav-body">
            <div class="nav-menu-item row">
                <div class="col-sm-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Bookings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-sm-9 nav-item-text"><p>Bookings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-sm-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Payments.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-sm-9 nav-item-text"><p>Payments</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-sm-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Refer.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-sm-9 nav-item-text"><p>Refer</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-sm-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Settings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-sm-9 nav-item-text"><p>Settings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item nav-item-last row">
                <div class="col-sm-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Support.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-sm-9 nav-item-text"><p>Support</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
        </div>
        <div class="side-nav-footer">
            <p class="copyright-statement">All rights reserved. <br> &copy; Copyright <span id="copyrightYear"></span></p>
        </div>
    </div>

	<!-- BOOKING PANEL -->
	<div id="bookingsPanel" class="row container-fluid function-panel">
	    <div class="col-lg-12 col-md-12 col-sm-12 col-12 full-width">
			<nav id="bookingMenu" class="navbar navbar-expand-md d4m-nav">
				<div class="collapse navbar-collapse" id="collapsibleNavbar">
					<ul class="nav navbar-nav">
						<li id="upcomingTab" class="nav-item book-tab active">
							<a class="nav-link" href="#upcomingBookings" role="button">Upcoming</a>
						</li>
						<li id="pastTab" class="nav-item book-tab inactive">
							<a class="nav-link" href="#pastBookings" role="button">Past</a>
						</li>
	                </ul>
				</div>
			</nav>
	    </div>

	    <div id="upcomingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab full-width" ng-controller="upcoming-ctrl">
			<span ng-repeat="booking in bookingEntries">
				<div class="row booking-entry  {{booking.bookingType}}-booking-entry full-width">
					<div class="col-lg-3 col-md-3 col-sm-3 col-12 side-panel full-width justified-text">
						<div class="row padded-container">
							<div class="col-lg-12 tiny-bottom-padding booking-info-label">
								<span class="trip-type">  {{booking.tripTextType}}  </span>
							</div>
							<div class="col-lg-12 tiny-bottom-padding booking-info-label">
								<span class="param-label">Booking ID:</span>&emsp;
								<span class="book-id">  {{booking.bookingID}}  </span>
							</div>
							<div class="col-lg-12 tiny-bottom-padding booking-info-label">
								<span class="param-label">Fare Estimate:</span>&emsp;
								<span class="payment-mode-value collapse"> {{booking.paymentMode}}  </span>
								<span class="fare-estimate">&#8377;  {{booking.fareEstimate}}  (</span>
								<img ng-if="booking.paymentMode == 0" class='payment-mode-icon' src='{{ url_for("static", filename="assets/images/elements/cash.svg" ) }}'>
								<img ng-if="booking.paymentMode == 1" class='payment-mode-icon' src='{{ url_for("static", filename="assets/images/elements/d4m_credit.svg" ) }}'>
								<span class="payment-mode">  {{booking.paymentModeText}}  )  </span>
							</div>
							<div class="col-lg-12 tiny-bottom-padding booking-info-label">
								<span class="param-label">Car Model:</span>&emsp;
								<span>
									<span class="vehicle-type">  {{booking.vehicleType}}  </span>
									<span class="transmission-type">(   {{booking.transmissionType}}  )</span>
								</span>
							</div>
							<div class="col-lg-12 tiny-bottom-padding booking-info-label">
								<span class="param-label">Duration:</span>&emsp;
								<span class="duration-value"> {{booking.duration}} </span> <span class="duration-unit">  {{booking.durationUnit}}  </span>
							</div>
							<div class="col-lg-12 col-md-12 col-sm-12 col-12 small-top-padding">
								<button class="btn btn-md d4m-danger-button cancel-button">Cancel</button>
							</div>
						</div>
					</div>

					<div class="col-lg-9 col-md-9 col-sm-9 col-12 justified-text full-width mid-panel">
						<div class="row padded-container">
						  <div class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
							  <div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>
							  <div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">  {{booking.scheduledDateTime}}  </span></div>
						  </div>
						  <div  ng-if="booking.allocated == true" class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
							  <div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>
							  <div class="row booking-info-label"><span class="driver-name">  {{booking.driverName}}  </span>
								  <span class="driver-rating text-success"> &nbsp;( <i class="fa fa-star"> </i>  {{booking.driverRating}} )</span>
							  </div>
						  </div>
						  <div  ng-if="booking.allocated == true" class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
							  <div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>
							  <div class="row booking-info-label text-no-wrap"><span class="driver-phone">  {{booking.driverContact}}  </span></div>
						  </div>
						  <div  ng-if="booking.allocated == true" class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
							  <div class="row booking-info-label text-no-wrap"><span class="param-label">Licence</span></div>
							  <div class="row booking-info-label text-no-wrap"><span class="driver-licence">  {{booking.driverLicence}}  </span></div>
						  </div>
						<div  ng-if="booking.allocated == false" class="col-lg-9 col-md-9 col-sm-9 col-6 full-width text-warning">
						  <div class="row booking-info-label text-no-wrap"><span class="param-label">Not Allocated</span></div>
						</div>
						</div>
						<div class="row padded-container center-elements location-graphics">
							<div class="col-lg-3 col-md-3 col-sm-3 col-3 no-padding left-image">
								<span class="source-lat" style="display: none">  {{booking.sourceLat}}  </span><span class="source-long" style="display: none">  {{booking.sourceLong}}  </span>
								<img src="{{ url_for("static", filename="assets/green.png") }}"/>
							</div>
							<div class="col-lg-6 col-md-6 col-sm-6 col-6 no-padding arc-image">
								<img src="{{ url_for("static", filename="assets/images/booking-form/Arc.svg") }}"/>
							</div>
							<div class="col-lg-3 col-md-3 col-sm-3 col-3 no-padding right-image">
								<span class="dest-lat" style="display: none">  {{booking.destLat}}  </span><span class="dest-long" style="display: none">  {{booking.destLong}}  </span>
								<img src="{{ url_for("static", filename="assets/red.png") }}"/>
							</div>
						</div>
						<div class="row padded-container location-text">
							<div class="col-lg-5 col-md-5 col-sm-5 col-5 no-padding">
								<div class="row booking-info-label break-long-text center-elements">
									<span class="source-address">  {{booking.sourceAddressString}}  </span>
								</div>
							</div>
							<div class="offset-lg-2 col-lg-5 offset-md-2 col-md-5 offset-sm-2 col-sm-5 offset-sm-2 col-5 no-padding">
								<div class="row booking-info-label break-long-text center-elements">
									<span class="destination-address">  {{booking.destAddressString}}  </span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</span>
	    </div>
	    <div id="pastBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width"  ng-controller="past-ctrl">
			<span ng-repeat="booking in bookingEntries">
				<span ng-if="booking.bookingType == 'cancel-d4m'">
					<div class="row booking-entry  {{booking.bookingType}}-booking-entry full-width">
						<div class="col-lg-3 col-md-3 col-sm-3 col-12 side-panel full-width justified-text">
							<div class="row padded-container">
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="trip-type">  {{booking.tripTextType}}  </span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Booking ID:</span>&emsp;
									<span class="book-id">  {{booking.bookingID}}  </span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Payment Method:</span>&emsp;
									<span class="payment-mode-value collapse"> {{booking.paymentMode}}  </span>
									<img ng-if="booking.paymentMode == 0" class='payment-mode-icon' src='{{ url_for("static", filename="assets/images/elements/cash.svg" ) }}'>
									<img ng-if="booking.paymentMode == 1" class='payment-mode-icon' src='{{ url_for("static", filename="assets/images/elements/d4m_credit.svg" ) }}'>
									<span class="payment-mode">  {{booking.paymentModeText}}  </span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Car Model:</span>&emsp;
									<span>
										<span class="vehicle-type">  {{booking.vehicleType}}  </span>
										<span class="transmission-type">(   {{booking.transmissionType}}  )</span>
									</span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Duration:</span>&emsp;
									<span class="duration-value"> {{booking.duration}}  </span>
								</div>
							</div>
						</div>

						<div class="col-lg-9 col-md-9 col-sm-9 col-12 justified-text full-width mid-panel">
							<div class="row padded-container">
							  <div class="col-lg-6 col-md-6 col-sm-6 col-12 full-width">
									<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>
									<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">  {{booking.scheduledDateTime}}  </span></div>
							  </div>
							  <div  ng-if="booking.allocated == true" class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
								  <div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>
								  <div class="row booking-info-label"><span class="driver-name">  {{booking.driverName}}  </span> </div>
							  </div>
							  <div  ng-if="booking.allocated == false" class="col-lg-3 col-md-3 col-sm-3 col-6 full-width text-warning">
								  <div class="row booking-info-label text-no-wrap"><span class="param-label">Not Allocated</span></div>
							  </div>

							  <div class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
								  <div class="row booking-info-label text-no-wrap"><span class="param-label">Cancellation Charge</span></div>
								  <div class="row booking-info-label text-no-wrap">&#8377; <span class="cancel-charge">  {{booking.cancelCharge}}  </span></div>
							  </div>
							</div>
							<div class="row padded-container center-elements location-graphics">
								<div class="col-lg-3 col-md-3 col-sm-3 col-3 no-padding left-image">
									<span class="source-lat" style="display: none">  {{booking.sourceLat}}  </span><span class="source-long" style="display: none">  {{booking.sourceLong}}  </span>
									<img src="{{ url_for("static", filename="assets/green.png") }}"/>
								</div>
								<div class="col-lg-6 col-md-6 col-sm-6 col-6 no-padding arc-image">
									<img src="{{ url_for("static", filename="assets/images/booking-form/Arc.svg") }}"/>
								</div>
								<div class="col-lg-3 col-md-3 col-sm-3 col-3 no-padding right-image">
									<span class="dest-lat" style="display: none">  {{booking.destLat}}  </span><span class="dest-long" style="display: none">  {{booking.destLong}}  </span>
									<img src="{{ url_for("static", filename="assets/red.png") }}"/>
								</div>
							</div>
							<div class="row padded-container location-text">
								<div class="col-lg-5 col-md-5 col-sm-5 col-5 no-padding">
									<div class="row booking-info-label break-long-text center-elements">
										<span class="source-address">  {{booking.sourceAddressString}}  </span>
									</div>
								</div>
								<div class="offset-lg-2 col-lg-5 offset-md-2 col-md-5 offset-sm-2 col-sm-5 offset-sm-2 col-5 no-padding">
									<div class="row booking-info-label break-long-text center-elements">
										<span class="destination-address">  {{booking.destAddressString}}  </span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</span>
				<span ng-if="booking.bookingType == 'completed'">
					<div class="row booking-entry  {{booking.bookingType}}-booking-entry full-width">
						<div class="col-lg-3 col-md-3 col-sm-3 col-12 side-panel full-width justified-text">
							<div class="row padded-container">
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="trip-type">  {{booking.tripTextType}}  </span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Booking ID:</span>&emsp;
									<span class="book-id">  {{booking.bookingID}}  </span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Fare:</span>&emsp;
									<span class="payment-mode-value collapse"> {{booking.paymentMode}}  </span>
									<span class="final-fare">&#8377; {{booking.estimate}}  </span> (
									<img ng-if="booking.paymentMode == 0" class='payment-mode-icon' src='{{ url_for("static", filename="assets/images/elements/cash.svg" ) }}'>
									<img ng-if="booking.paymentMode == 1" class='payment-mode-icon' src='{{ url_for("static", filename="assets/images/elements/d4m_credit.svg" ) }}'>
									<span class="payment-mode">  {{booking.paymentModeText}}  )</span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Car Model:</span>&emsp;
									<span>
										<span class="vehicle-type">  {{booking.vehicleType}}  </span>
										<span class="transmission-type">(   {{booking.transmissionType}}  )</span>
									</span>
								</div>
								<div class="col-lg-12 tiny-bottom-padding booking-info-label">
									<span class="param-label">Duration:</span>&emsp;
									<span class="duration-value"> {{booking.duration}}  </span>
								</div>
							</div>
						</div>

						<div class="col-lg-9 col-md-9 col-sm-9 col-12 justified-text full-width mid-panel">
							<div class="row padded-container">
								<div class="col-lg-6 col-md-6 col-sm-6 col-12 full-width">
									<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>
									<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">  {{booking.scheduledDateTime}}  </span></div>
								</div>
								<div class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
									<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>
									<div class="row booking-info-label"><span class="driver-name">  {{booking.driverName}}  </span>
									</div>
								</div>

								<div class="col-lg-3 col-md-3 col-sm-3 col-6 full-width">
									<div class="row booking-info-label text-no-wrap"><span class="param-label">Duration</span></div>
									<div class="row booking-info-label text-no-wrap"><span class="duration">  {{booking.duration}}  </span></div>
								</div>

							</div>
							<div class="row padded-container center-elements location-graphics">
								<div class="col-lg-3 col-md-3 col-sm-3 col-3 no-padding left-image">
									<span class="source-lat" style="display: none">  {{booking.sourceLat}}  </span><span class="source-long" style="display: none">  {{booking.sourceLong}}  </span>
									<img src="{{ url_for("static", filename="assets/green.png") }}"/>
								</div>
								<div class="col-lg-6 col-md-6 col-sm-6 col-6 no-padding arc-image">
									<img src="{{ url_for("static", filename="assets/images/booking-form/Arc.svg") }}"/>
								</div>
								<div class="col-lg-3 col-md-3 col-sm-3 col-3 no-padding right-image">
									<span class="dest-lat" style="display: none">  {{booking.destLat}}  </span><span class="dest-long" style="display: none">  {{booking.destLong}}  </span>
									<img src="{{ url_for("static", filename="assets/red.png") }}"/>
								</div>
							</div>
							<div class="row padded-container location-text">
								<div class="col-lg-5 col-md-5 col-sm-5 col-5 no-padding">
									<div class="row booking-info-label break-long-text center-elements">
										<span class="source-address">  {{booking.sourceAddressString}}  </span>
									</div>
								</div>
								<div class="offset-lg-2 col-lg-5 offset-md-2 col-md-5 offset-sm-2 col-sm-5 offset-sm-2 col-5 no-padding">
									<div class="row booking-info-label break-long-text center-elements">
										<span class="destination-address">  {{booking.destAddressString}}  </span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</span>
			</span>
	    </div>
	</div>

    <!-- SNACKBAR -->
    <div id="snackbar"></div>

    <!-- LOADER -->
    <div id="loader" class="collapse">
        <div class="backdrop"></div>
        <div id="spinner-container">
            <div class="spinner">
                <div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div>
            </div>
        </div>
    </div>

</body>

</html>

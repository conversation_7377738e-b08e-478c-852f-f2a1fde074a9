tags:
  - User
summary: Get Pending User Rides
description: >
  This endpoint retrieves the pending rides for a user. It fetches rides that are either confirmed with a driver assigned or unconfirmed without a driver yet.
responses:
  200_a:  # Success response when pending rides are found
    description: Pending rides fetched successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Pending rides fetched successfully"
        data:
          type: array
          items:
            type: string
            description: JSON-encoded ride details
    examples:
      application/json:
        success: 1
        message: "Pending rides fetched successfully"
        data:
          - "json_encoded_data_1"
          - "json_encoded_data_2"
  200_b:  # Success response when no pending rides are found
    description: Pending rides not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Failure message
          example: "Pending rides not found"
    examples:
      application/json:
        success: -1
        message: "Pending rides not found"
  401_a:  # First 401 Unauthorized for restricted user
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  401_b:  # Second 401 Unauthorized for user not found
    description: Unauthorized - User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -1
        message: "User not found"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Internal server error"
    examples:
      application/json:
        success: -1
        message: "Internal server error"
  201:
    description: Incomplete form data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"

tags:
  - Payments
summary: View D4M Credit
description: >
  This endpoint allows users and drivers to view their available credit or wallet balance. 
  It differentiates between user and driver roles and returns credit information accordingly.
responses:
  200_a:
    description: User credit details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        credit:
          type: number
          description: User's current available credit
          example: 500.75
        ref_code:
          type: string
          description: User's referral code
          example: "REF123456"
    examples:
      application/json:
        success: 1
        credit: 500.75
        ref_code: "REF123456"
  200_b:
    description: Driver wallet and withdrawable details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        wallet:
          type: number
          description: Driver's wallet balance
          example: 1200.50
        withdrawable:
          type: number
          description: Driver's withdrawable balance
          example: 800.00
    examples:
      application/json:
        success: 1
        wallet: 1200.50
        withdrawable: 800.00
  401_a:
    description: Failed to get identity
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"

from main import app
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import requests
import json
from sqlalchemy import and_
from flask import jsonify
from datetime import datetime, timedelta
import pytz
from models import db,PaymentDataPT, UserTrans, Users
import html
import time
import paytmchecksum  # Make sure this is installed and accessible

def get_recent_6hr_window():
    ist = pytz.timezone("Asia/Kolkata")
    now = datetime.now(ist)
    from_time = now - timedelta(hours=6)
    to_time = now
    return from_time.strftime("%Y-%m-%dT%H:%M:%S+05:30"), to_time.strftime("%Y-%m-%dT%H:%M:%S+05:30"), now

def has_pending_transactions_in_last_6_hours():
    ist = pytz.timezone("Asia/Kolkata")
    now = datetime.now(ist)
    six_hours_ago = now - timedelta(hours=6)

    # Adjust based on your actual column names
    pending = db.session.query(UserTrans, PaymentDataPT).filter(
        and_(
            UserTrans.id == PaymentDataPT.trans_id,
            UserTrans.start_timestamp >= six_hours_ago,
            UserTrans.status < 1
        )
    ).first()

    return bool(pending)

def reconcile_paytm_transactions(paytm_response, now_ist):
    updated = 0
    skipped = 0
    not_found = 0
    mismatched = 0
    reconciled_records = []

    for order in paytm_response.get("body", {}).get("orders", []):
        if order.get("orderSearchStatus") != "SUCCESS":
            continue  # Skip failed orders

        tid = order["merchantOrderId"]
        amount = int(float(order["amount"]) * 100)  # Convert to paise
        pay_mode = order.get("payMode", "")
        txn_id = order.get("txnId", "")
        txn_date = order.get("orderCompletedTime", "")
        status = order.get("orderSearchStatus", "")

        try:
            txn_datetime = datetime.strptime(txn_date, "%Y-%m-%d %H:%M:%S")
            txn_datetime = pytz.timezone("Asia/Kolkata").localize(txn_datetime)
        except Exception as e:
            print(f"Failed to parse transaction datetime for order {tid}: {txn_date}", flush = True)
            continue

        # Ignore transactions completed less than 10 minutes ago
        if (now_ist - txn_datetime) < timedelta(minutes=10):
            continue

        order_trans = db.session.query(PaymentDataPT, UserTrans).join(
                UserTrans, PaymentDataPT.trans_id == UserTrans.id
            ).filter(
                PaymentDataPT.trans_id == tid,
                PaymentDataPT.status < 1
            ).first()

        if not order_trans:
            not_found += 1
            continue

        payment_data, user_trans = order_trans

        if payment_data.status == "TXN_SUCCESS" or payment_data.status == "SUCCESS":
            skipped += 1
            continue

        if user_trans.amount != amount:
            mismatched += 1
            continue

        user = db.session.query(Users).filter(Users.id == user_trans.user_id).first()

        reconciled_records.append({
            "order_id": tid,
            "txn_id": txn_id,
            "amount": amount,
            "pay_mode": pay_mode,
            "timestamp": txn_date,
            "user_id": user.id,
            "prev_balance": user.credit,
            "new_balance": user.credit + (amount / 100)
        })

        db.session.query(UserTrans).filter(UserTrans.id == user_trans.id).update({
            UserTrans.stop_timestamp: datetime.utcnow(),
            UserTrans.status: UserTrans.COMPLETED,
            UserTrans.method: "Paytm recharge",
            UserTrans.wallet_before: user.credit,
            UserTrans.wallet_after: user.credit + (amount / 100)
        })

        db.session.query(PaymentDataPT).filter(PaymentDataPT.trans_id == tid).update({
            PaymentDataPT.payment_id: txn_id,
            PaymentDataPT.amount: amount,
            PaymentDataPT.description: html.escape("Reconciled from daily Paytm report"),
            PaymentDataPT.method: html.escape(pay_mode),
            PaymentDataPT.status: "TXN_SUCCESS",
            PaymentDataPT.timestamp: txn_date
        })

        user.credit += amount / 100
        updated += 1

    # Print reconciled data before committing
    print("\n===== PAYTM RECONCILIATION PREVIEW =====", flush = True)
    for rec in reconciled_records:
        print(json.dumps(rec, indent=2), flush = True)
    print("=========================================\n", flush = True)

    db.session.commit()

    return jsonify({
        'success': 1,
        'updated': updated,
        'skipped_already_success': skipped,
        'not_found': not_found,
        'mismatched_amount': mismatched,
        'message': 'Paytm transactions reconciled'
    })

def mark_failed_transactions_from_paytm_response(paytm_response,from_time, to_time):
    # 1. Extract successful Paytm merchantOrderIds from response
    successful_order_ids = set()
    for order in paytm_response.get("body", {}).get("orders", []):
        if order.get("orderSearchStatus") == "SUCCESS":
            successful_order_ids.add(order.get("merchantOrderId"))

    # 2. Find pending transactions older than 30 mins
    cutoff_time = now_ist - timedelta(minutes=30)
    pending_txns = db.session.query(PaymentDataPT, UserTrans).filter(
        and_(
            UserTrans.start_timestamp > from_time,
            UserTrans.start_timestamp < cutoff_time,
            UserTrans.status < 1,
            PaymentDataPT.trans_id == UserTrans.id
        )
    ).all()

    # 3. Compare and mark failed if not found in successful Paytm list
    to_fail = []
    for txn in pending_txns:
        if txn[1].id not in successful_order_ids:
            txn[1].status = UserTrans.FAILED
            txn[1].method = "Paytm - Failed"
            to_fail.append(txn[1].id)
            txn[0].status = "FAILED"
            txn[0].description = "Credit recharge failed"
            txn[0].amount = txn[1].amount
            txn[0].timestamp = datetime.utcnow()

    if to_fail:
        db.session.commit()
        print(f"⚠️ Marked the following transactions as FAILED (not found in Paytm response after 30 mins):", flush = True)
        for tid in to_fail:
            print(f"  - Order ID: {tid}", flush = True)
    else:
        print("✅ No transactions to mark as failed.", flush = True)

    return len(to_fail)



if __name__ == '__main__':
    with app.app_context():
        while True:
            print("\n⏰ Running Paytm reconciliation at", datetime.now().strftime("%Y-%m-%d %H:%M:%S"), flush = True)

            if not has_pending_transactions_in_last_6_hours():
                print("✅ No pending transactions found in the last 6 hours. Skipping Paytm reconciliation.", flush = True)
            else:
                print("⏳ Found pending transactions. Proceeding with Paytm reconciliation...", flush = True)

                mid = "tTykkx19080718697028"
                merchant_key = "#Lt8!KK_FSj9v&Ok"

                from_time, to_time, now_ist = get_recent_6hr_window()

                paytmParams = {
                    "body": {
                        "mid": mid,
                        "fromDate": from_time,
                        "toDate": to_time,
                        "orderSearchType": "TRANSACTION",
                        "orderSearchStatus": "SUCCESS",
                        "pageNumber": "1",
                        "pageSize": "50"
                    }
                }

                checksum = paytmchecksum.generateSignature(json.dumps(paytmParams["body"]), merchant_key)

                paytmParams["head"] = {
                    "signature": checksum,
                    "tokenType": "CHECKSUM",
                    "requestTimestamp": ""
                }

                post_data = json.dumps(paytmParams)

                url = "https://securegw.paytm.in/merchant-passbook/search/list/order/v2"

                try:
                    print(json.dumps(paytmParams, indent=2), flush = True)
                    response = requests.post(url, data=post_data, headers={"Content-type": "application/json"})
                    # Output result
                    print(json.dumps(response.json(), indent=2), flush = True)
                    paytm_response = response.json()
                    reconcile_paytm_transactions(paytm_response, now_ist)
                    mark_failed_transactions_from_paytm_response(paytm_response, from_time, to_time)
                except Exception as e:
                    print("❌ Error during Paytm reconciliation:", str(e), flush = True)

            print("🕒 Sleeping for 10 minutes...\n", flush = True)
            time.sleep(600)  # Wait 10 minutes before next run

from db_config import db
import pytest
from unittest.mock import patch, MagicMock
from sqlalchemy.exc import SQLAlchemyError
from affiliate_b2b.affiliate_models import Affiliate, AffiliateRep
import random
from redis_config import get_redis_client
from datetime import datetime, timedelta
import jwt


@pytest.fixture
def redis_connect():
    redis_client = get_redis_client()
    return redis_client

@pytest.fixture
def affiliate_rep_fixture():
    affiliate = Affiliate("Test_Dummy",  "Test Dummy", 0, -1, 10, None, notification=None,  enabled=True, 
        wallet_threshold=1000)
    db.session.add(affiliate)
    db.session.flush()
    affiliate.mapped_wallet_affiliate = affiliate.id
    db.session.commit()

    mobile = f'{random.randint(7000000000, 9999999999)}'
    pwd = "temp" + mobile

    aff_rep = AffiliateRep(affiliate.id, "abc", "abcd", mobile, pwd, 10, 14, 106, '0', '1,4,5,6,7,11,12',
        enabled=True)
    db.session.add(aff_rep)
    db.session.flush()
    db.session.commit()

    return affiliate, aff_rep

""" Test cases for api: /api/affiliate/otp/generate """
def test_otp_gen_missing_username_field(client):
    res = client.post('/api/affiliate/otp/generate', data={})
    assert res.status_code == 400
    assert res.json['success'] == 0
    assert 'Missing required fields' in res.json['msg']

def test_invalid_username(client, create_new_affiliate_rep):
    affiliate, aff_rep = create_new_affiliate_rep
    form_data = {
        'username': 'Invalid User Name'
    }

    res = client.post('/api/affiliate/otp/generate', data=form_data)
    assert res.status_code == 404
    assert res.json['success'] == -1
    assert 'Invalid username' in res.json['msg']

def test_first_login_user_type_2(client, create_new_affiliate_rep):
    affiliate, aff_rep = create_new_affiliate_rep
    aff_rep.is_first_login = True
    aff_rep.user_name = 'testuser'
    db.session.commit()

    form_data = {
        'username': 'testuser',
        'type': '2'
    }

    res = client.post('/api/affiliate/otp/generate', data=form_data)
    assert res.status_code == 404
    assert res.json['success'] == -4
    assert 'Reset your password' in res.json['msg']

def test_type_1_password_already_set(client, create_new_affiliate_rep):
    affiliate, aff_rep = create_new_affiliate_rep
    aff_rep.is_first_login = False
    aff_rep.user_name = 'testuser'
    db.session.commit()

    form_data = {
        'username': 'testuser',
        'type': '1'
    }

    res = client.post('/api/affiliate/otp/generate', data=form_data)
    assert res.status_code == 400
    assert res.json['success'] == -5
    assert 'Password has already been set.' in res.json['msg']

def test_valid_request_send_otp(client, create_new_affiliate_rep, mocker):
    affiliate, aff_rep = create_new_affiliate_rep
    aff_rep.is_first_login = False
    aff_rep.user_name = 'testuser'
    db.session.commit()

    # Mock OTP send function
    mocker.patch('_sms.send_otp_flow', return_value=None)

    form_data = {
        'username': 'testuser',
        'type': '2'
    }

    res = client.post('/api/affiliate/otp/generate', data=form_data)
    assert res.status_code == 200
    assert res.json['success'] == 1
    assert 'OTP sent successfully' in res.json['msg']


""" Test cases for api: /api/affiliate/login """

def test_login_missing_fields(client):
    res = client.post('/api/affiliate/login', data={})
    assert res.status_code == 400
    assert res.json['success'] == 0
    assert 'Missing required fields' in res.json['msg']

def test_login_missing_password(client):
    res = client.post('/api/affiliate/login', data={'username': 'testuser'})
    assert res.status_code == 400
    assert res.json['success'] == 0

def test_login_invalid_username(client):
    res = client.post('/api/affiliate/login', data={'username': 'nonexistent', 'pwd': 'randompwd'})
    assert res.status_code == 404
    assert res.json['success'] == -1
    assert 'Affiliate representative not found' in res.json['msg']

def test_login_account_blocked(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
    affiliate.enabled = False
    rep.enabled = False
    db.session.commit()
    res = client.post('/api/affiliate/login', data={'username': rep.user_name, 'pwd': 'correctpwd'})
    assert res.status_code == 403
    assert res.json['success'] == -1
    assert 'account is blocked' in res.json['msg']

def test_login_first_time_user(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
   
    res = client.post('/api/affiliate/login', data={'username': rep.user_name, 'pwd': 'any'})
    assert res.status_code == 403
    assert res.json['success'] == -2
    assert 'reset your password' in res.json['msg'].lower()

def test_login_account_locked(client, affiliate_rep_fixture, redis_connect):
    affiliate, rep = affiliate_rep_fixture
    rep.is_first_login = False
    db.session.commit()
    redis_client = redis_connect
    rep_id = rep.id
    redis_client = get_redis_client()
    redis_client.set(f"failed_login_attempts_{rep.user_name.lower()}", 5, ex=600)
    res = client.post('/api/affiliate/login', data={'username': rep.user_name, 'pwd': 'wrong'})
    rep=db.session.query(AffiliateRep).filter(AffiliateRep.id == rep_id).first()
    assert res.status_code == 423
    assert res.json['success'] == -3
    redis_client.delete(f"failed_login_attempts_{rep.user_name.lower()}")

def test_login_invalid_password(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
    rep.is_first_login = False
    db.session.commit()
    res = client.post('/api/affiliate/login', data={'username': rep.user_name, 'pwd': 'wrongpassword'})
    json_data = res.get_json()
    print("Response", json_data)
    assert res.status_code == 200
    assert res.json['success'] == -5

def test_login_success_password_mode(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
    rep.is_first_login = False
    db.session.commit()
    res = client.post('/api/affiliate/login', data={
        'username': rep.user_name,
        'pwd': 'temp'+rep.mobile,
        'type': '1'
    })
    assert res.status_code == 200
    assert res.json['success'] == 1
    assert 'access_token' in res.json or 'status' in res.json

def test_login_success_otp_mode(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
    rep.is_first_login = False
    db.session.commit()
    res = client.post('/api/affiliate/login', data={
        'username': rep.user_name,
        'pwd': '3487',
        'type': '0'
    })
    assert res.status_code == 200
    assert res.json['success'] == 1

def test_login_dashboard_response(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
    rep.is_first_login = False
    db.session.commit()
    res = client.post('/api/affiliate/login', data={
        'username': rep.user_name,
        'pwd': 'temp'+rep.mobile,
        'dashboard': '1'
    })
    assert res.status_code == 200
    data = res.json
    assert data['success'] == 1
    assert 'rep_username' in data
    assert 'rep_mobile' in data
    assert 'access_token' in data
    assert 'rep_region' in data


""" Test cases for api: /api/affiliate/forgot/password """

def test_missing_password(client):
    response = client.post('/api/affiliate/forgot/password', data={
        "username": "testuser"
    })
    data = response.get_json()
    assert response.status_code == 400
    assert data["success"] == 0
    assert "Missing required fields" in data["msg"]

def test_affiliate_not_found(client):
    response = client.post('/api/affiliate/forgot/password', data={
        "username": "nonexistentuser",
        "pwd": "doesntmatter"
    })
    data = response.get_json()
    assert response.status_code == 404
    assert data["success"] == -1
    assert "not found" in data["msg"].lower()

def test_successful_password_reset(client, affiliate_rep_fixture):
    affiliate, rep = affiliate_rep_fixture
    new_password = "newsecurepass"
    response = client.post('/api/affiliate/forgot/password', data={
        "username": rep.user_name,
        "pwd": new_password
    })

    data = response.get_json()
    assert response.status_code == 200
    assert data["success"] == 1
    assert data["msg"] == "Password updated successfully"


""" Test cases for api: /api/affiliate/change_password """

def test_change_pass_unauthorized_access(client):
    res = client.post('/api/affiliate/change_password', data={
        "cur_pwd": "anything",
        "new_pwd": "whatever"
    })
    json_data = res.get_json()
    assert res.status_code == 401
    assert json_data['success'] == -1
    assert json_data['result'] == 'FAILURE'


def test_missing_fields(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    response = client.post('/api/affiliate/change_password', data={
        "cur_pwd": "oldpassword"
    }, headers=auth_headers)

    data = response.get_json()
    assert response.status_code == 400
    assert data["success"] == 0
    assert "Missing required fields" in data["msg"]


def test_incorrect_current_password(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    response = client.post('/api/affiliate/change_password', data={
        "cur_pwd": "wrongpassword",
        "new_pwd": "newpass"
    }, headers=auth_headers)

    data = response.get_json()
    assert response.status_code == 400
    assert data["success"] == 0
    assert "Incorrect current password" in data["msg"]


def test_successful_password_change(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    response = client.post('/api/affiliate/change_password', data={
        "cur_pwd": "temp"+rep.mobile,
        "new_pwd": "newsecurepassword"
    }, headers=auth_headers)

    data = response.get_json()
    assert response.status_code == 200
    assert data["success"] == 1
    assert "updated successfully" in data["msg"].lower()


""" Test cases for api: /api/affiliate/change_username """


def test_unauthorized_access(client):
    response = client.post('/api/affiliate/change_username', data={
        "new_username": "unauthuser"
    })  # No Authorization header

    assert response.status_code == 401

def test_missing_username_field(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/change_username', data={}, headers=auth_headers)

    data = response.get_json()
    assert response.status_code == 400
    assert data["success"] == 0
    assert "Missing required fields" in data["msg"]


def test_username_already_exists(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    aff_rep2 = AffiliateRep(rep.affiliate_id, "exstinguser", "existinguser", rep.mobile, "123456", 10, 14, 106, '0', '1,4,5,6,7,11,12',
        enabled=True)
    db.session.add(aff_rep2)
    db.session.commit()

    response = client.post('/api/affiliate/change_username', data={
        "new_username": "existinguser"
    }, headers=auth_headers)

    data = response.get_json()
    assert response.status_code == 400
    assert data["success"] == 0
    assert "already exist" in data["msg"].lower()


def test_affiliate_rep_not_found(client):
    
    fake_payload = {
        "sub": 99999,
        "iat": datetime.utcnow(),
        "exp": datetime.utcnow() + timedelta(hours=1)
    }
    token = jwt.encode(fake_payload, client.application.config["JWT_SECRET_KEY"], algorithm="HS256")
    fake_header = {"Authorization": f"Bearer {token}"}

    response = client.post('/api/affiliate/change_username', data={
        "new_username": "newuniqueuser"
    }, headers=fake_header)

    data = response.get_json()
    assert response.status_code == 404
    assert data["success"] == -1
    assert "not found" in data["msg"].lower()


def test_successful_username_change(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/change_username', data={
        "new_username": "uniqueuser123"
    }, headers=auth_headers)

    data = response.get_json()
    assert response.status_code == 200
    assert data["success"] == 1
    assert "updated successfully" in data["msg"].lower()
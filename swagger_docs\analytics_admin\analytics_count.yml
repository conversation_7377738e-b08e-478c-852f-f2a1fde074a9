tags:
  - Admin Analytics
summary: Get Admin Analytics Count
description: >
  This endpoint provides the analytics count for registered customers, active users, drivers, trips, and sales within a given date range.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: The start date of the analytics period (YYYY-MM-DD)
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: The end date of the analytics period (YYYY-MM-DD)
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
responses:
  200:
    description: Successfully retrieved analytics data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        reg_customers_count:
          type: integer
          description: Count of registered customers within the date range
          example: 200
        active_user_count:
          type: integer
          description: Count of active users within the date range
          example: 120
        avg_trips_per_user:
          type: number
          format: float
          description: Average trips per active user
          example: 2.5
        reg_drivers_count:
          type: integer
          description: Count of registered drivers within the date range
          example: 50
        active_driver_count:
          type: integer
          description: Count of active drivers within the date range
          example: 45
        avg_trips_per_driver:
          type: number
          format: float
          description: Average trips per active driver
          example: 5.6
        total_booking_count:
          type: integer
          description: Total number of bookings within the date range
          example: 150
        total_sales_count:
          type: number
          format: float
          description: Total sales amount within the date range
          example: 50000
        total_revenue_count:
          type: number
          format: float
          description: Total revenue generated within the date range
          example: 40000
        trend_active_user:
          type: integer
          description: Trend comparison for active users (current vs previous period)
          example: 20
        trend_active_driver:
          type: integer
          description: Trend comparison for active drivers (current vs previous period)
          example: -5
        trend_trips:
          type: integer
          description: Trend comparison for trips (current vs previous period)
          example: 15
  400:
    description: Invalid date range or missing required fields
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for error)
          example: -3
        error:
          type: string
          description: Error message
          example: "Missing or invalid date values"
  500:
    description: Internal server error or database failure
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
from main import app
from models import db, Bookings, Trip
from call_masking import assign_unique_did
from sqlalchemy.sql import  or_
from booking_params import BookingParams

def assign_did(book_user, book_driver, booking_id):
    try:
        cust_current_bookings = db.session.query(Bookings).filter(Bookings.user == book_user). \
                                                        filter(Bookings.did_release == False).all()
        driver_current_bookings = db.session.query(Bookings).filter(Bookings.driver == book_driver). \
                                                            filter(Bookings.did_release == False).all()
        driver_did = []
        cust_did = []
        for booking in cust_current_bookings:
            cust_did.append(booking.did)
        for booking in driver_current_bookings:
            driver_did.append(booking.did)
        unique_did = assign_unique_did(cust_did, driver_did)

        Bookings.query.filter(Bookings.id == booking_id).update({Bookings.did: unique_did})
        Bookings.query.filter(Bookings.id == booking_id).update({Bookings.did_release: False})
    except Exception as e:
        db.session.rollback()



def assign_did_booking():
    bookings = db.session.query(Bookings, Trip).outerjoin(Trip, Bookings.id == Trip.book_id).filter(Bookings.valid >= 0). \
                                            filter(or_(Trip.status.is_(None), Trip.status > 0)).filter(Bookings.type < 50).all()
    for book, trip in bookings:
        book.did_release = False

    db.session.commit()

    for book, trip in bookings:
        book_user = book.user
        book_driver = book.driver
        if book.valid == Bookings.ALLOCATED:
            assign_did(book_user, book_driver, book.id)
            print("DID Assigned", book.id, "DID:", book.did, flush=True)
        else:
            book.did = "-1"
            book.did_release = False


        db.session.add(book)

    db.session.commit()


if __name__ == "__main__":
    with app.app_context():
        print("Start assigning did to ongoing booking", flush=True)
        assign_did_booking()
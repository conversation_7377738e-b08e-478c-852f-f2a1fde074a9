from main import app
import sys
sys.path.append("/app/")

from datetime import datetime, timedelta
from sqlalchemy.sql import func
from _email import send_mail_v2
from collections import defaultdict
from booking_params import BookingParams, Regions
from models import db, Bookings, Trip
from affiliate_b2b.affiliate_models import AffBookingLogs, Affiliate
import pandas as pd
import matplotlib.pyplot as plt
import os

# from __init__ import create_app
# from config import ProdConfig
# app,_ = create_app(ProdConfig)

FROM_ADDR = "<EMAIL>"
REGION_MAIL = {
    Regions.REGN_KOLKATA: ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_DELHI: [ "<EMAIL>", "<EMAIL>", "<EMAIL>"],
    Regions.REGN_HYDERABAD: [ "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"],
    Regions.REGN_BANGALORE: [ "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"],
    Regions.REGN_JAIPUR: [ "<EMAIL>", "<EMAIL>", "<EMAIL>"],
}

def truncate(text, w):
        s = str(text)
        return s if len(s) <= w else s[: w - 3] + "..."

def generate_table_image(table_rows, filename='whatsapp_table.png'):
    # 1) build DataFrame in the exact column order you want
    df = pd.DataFrame(table_rows)[[
        'display_name','completed_ongoing','d4m_cancelled','unique_cancellations',
        'success_rate','monthly_success_rate','avg_monthly_completed',
        'cx_driver_cancelled_after_alloc','cx_cancelled_before_alloc'
    ]]
    df.columns = [
        'Affiliate','Completed','D4M Cancel','Unique Cancel',
        'Success (%)','Monthly Succ (%)','Avg Monthly Cmplt',
        'Aff/Driv Cancel Aft','Aff Cancel Bef'
    ]
    df['Affiliate'] = df['Affiliate'].apply(lambda x: truncate(x, 20))

    # 2) render as a matplotlib table
    fig, ax = plt.subplots(figsize=(len(df.columns)*1.5, len(df)*0.5 + 1))
    ax.axis('off')
    tbl = ax.table(
        cellText=df.values,
        colLabels=df.columns,
        cellLoc='center',
        loc='center'
    )
    tbl.auto_set_font_size(False)
    tbl.set_fontsize(10)
    tbl.scale(1, 1.5)
    plt.tight_layout()

    # 3) save to disk
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.close(fig)
    return filename


def group_by_day(bookings):
    grouped = defaultdict(list)
    for booking in bookings:
        start_datetime_str = f"{booking.startdate} {booking.starttime}"  # Format the combined string
        start_datetime = datetime.strptime(start_datetime_str, '%Y-%m-%d %H:%M:%S')  # Convert to datetime object
        adjusted_starttime = start_datetime + timedelta(hours=5, minutes=30)
        booking_date = adjusted_starttime.date()
        grouped[booking_date].append(booking)
    return grouped

def group_by_day_trip(bookings):
    grouped = defaultdict(list)
    for booking in bookings:
        adjusted_starttime = booking[1].starttime + timedelta(hours=5, minutes=30)
        booking_date = adjusted_starttime.date()
        grouped[booking_date].append(booking)
    return grouped

def group_by_affiliate(bookings):
    grouped = defaultdict(list)
    for log in bookings:
        aff_id = log[0].aff_id
        grouped[aff_id].append(log)
    return grouped

def generate_whatsapp_table(table_rows):
    # 1) define your column widths in one place:
    widths = {
        "Aff": 20,
        "Cmplt": 5,
        "Succ%": 7,
        "M-Succ%": 9,
        "M-Avg": 7,
        "Aff-C-Aft": 9,
        "Aff-C-Bef": 9,
        "D4M-C": 5,
        "U-Cncl": 6,
    }
    cols = list(widths.keys())

    # build the horizontal border line: +----+------+ ... +
    def border():
        parts = ["+" + "-" * (widths[c] + 2) for c in cols]
        return "".join(parts) + "+"

    # build a row of data or header
    def build_row(values, is_header=False):
        parts = []
        for c, val in zip(cols, values):
            w = widths[c]
            s = str(val)
            if is_header:
                # center header text
                cell = f" {s:^{w}} "
            else:
                # for “Aff” left-align, for others right-align numbers
                if c == "Aff":
                    cell = f" {truncate(s, w):<{w}} "
                else:
                    cell = f" {float(s):>{w}.2f} " if isinstance(val, float) else f" {s:>{w}} "
            parts.append("│" + cell)
        return "".join(parts) + "│"

    # assemble
    lines = []
    lines.append(border())
    # header
    lines.append(build_row(cols, is_header=True))
    # header/data separator
    # replace interior “ ” with “─”
    sep = border().replace("-", "─")
    lines.append(sep)
    # data rows
    for r in table_rows:
        values = [
            r["display_name"],
            r["completed_ongoing"],
            r["success_rate"],
            r["monthly_success_rate"],
            r["avg_monthly_completed"],
            r["cx_driver_cancelled_after_alloc"],
            r["cx_cancelled_before_alloc"],
            r["d4m_cancelled"],
            r["unique_cancellations"],
        ]
        lines.append(build_row(values))
    # bottom border
    lines.append(border())
    return "\n".join(lines)

def compute_b2b_stats_and_send(region, days_back=1):
    time_yest = datetime.utcnow() + timedelta(seconds=330*60) - timedelta(days=days_back)
    time_yest_min = datetime(time_yest.year, time_yest.month, time_yest.day, 0, 0, 0)
    time_yest_max = datetime(time_yest.year, time_yest.month, time_yest.day, 23, 59, 59)
    region_name = Regions.to_string(region)
    start_of_month = datetime(time_yest.year, time_yest.month, 1, 0, 0, 0)
    num_days_elapsed = (time_yest - start_of_month).days + 1

    # Fetch monthly data
    monthly_completed_query = (
        db.session.query(AffBookingLogs, Bookings, Trip)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .join(Trip, Trip.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.type == BookingParams.TYPE_B2B)
        .filter(func.addtime(Trip.starttime, '05:30:00') >= start_of_month)
        .filter(func.addtime(Trip.starttime, '05:30:00') <= time_yest_max)
        .all()
    )
    monthly_completed = defaultdict(set)
    for log, booking, _trip in monthly_completed_query:
        monthly_completed[log.aff_id].add(booking.id)

    monthly_cancelled_before_alloc_query = (
        db.session.query(AffBookingLogs, Bookings)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.valid < 0, Bookings.valid > Bookings.CANCELLED_D4M, Bookings.driver == 1)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') >= start_of_month)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') <= time_yest_max)
        .all()
    )
    monthly_cancelled_before_alloc = defaultdict(set)
    for log, booking in monthly_cancelled_before_alloc_query:
        monthly_cancelled_before_alloc[log.aff_id].add(booking.id)

    monthly_d4m_cancelled_query = (
        db.session.query(AffBookingLogs, Bookings)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.valid == Bookings.CANCELLED_D4M)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') >= start_of_month)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') <= time_yest_max)
        .all()
    )
    monthly_d4m_cancelled = defaultdict(set)
    for log, booking in monthly_d4m_cancelled_query:
        monthly_d4m_cancelled[log.aff_id].add(booking.id)

    # Fetch daily data (yesterday)
    completed_query = (
        db.session.query(AffBookingLogs, Bookings, Trip)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .join(Trip, Trip.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.type == BookingParams.TYPE_B2B)
        .filter(func.addtime(Trip.starttime, '05:30:00') >= time_yest_min)
        .filter(func.addtime(Trip.starttime, '05:30:00') <= time_yest_max)
        .all()
    )
    daily_completed = defaultdict(set)
    for log, booking, _trip in completed_query:
        daily_completed[log.aff_id].add(booking.id)

    d4m_cancelled_query = (
        db.session.query(AffBookingLogs, Bookings)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.valid == Bookings.CANCELLED_D4M)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') >= time_yest_min)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') <= time_yest_max)
        .all()
    )
    daily_d4m_cancelled = defaultdict(set)
    for log, booking in d4m_cancelled_query:
        daily_d4m_cancelled[log.aff_id].add(booking.id)

    cancelled_before_alloc_query = (
        db.session.query(AffBookingLogs, Bookings)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.valid < 0, Bookings.valid > Bookings.CANCELLED_D4M, Bookings.driver == 1)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') >= time_yest_min)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') <= time_yest_max)
        .all()
    )
    daily_cancelled_before_alloc = defaultdict(set)
    for log, booking in cancelled_before_alloc_query:
        daily_cancelled_before_alloc[log.aff_id].add(booking.id)

    cancelled_after_alloc_query = (
        db.session.query(AffBookingLogs, Bookings)
        .join(Bookings, AffBookingLogs.book_id == Bookings.id)
        .filter(Bookings.region == region, Bookings.valid < 0, Bookings.valid > Bookings.CANCELLED_D4M, Bookings.driver != 1)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') >= time_yest_min)
        .filter(func.addtime(func.concat(Bookings.startdate, ' ', Bookings.starttime), '05:30:00') <= time_yest_max)
        .all()
    )
    cancelled_after_alloc = defaultdict(set)
    for log, booking in cancelled_after_alloc_query:
        cancelled_after_alloc[log.aff_id].add(booking.id)

    # Collect all aff_ids
    all_aff_ids = set()
    all_aff_ids.update(monthly_completed.keys())
    all_aff_ids.update(monthly_cancelled_before_alloc.keys())
    all_aff_ids.update(monthly_d4m_cancelled.keys())
    all_aff_ids.update(daily_completed.keys())
    all_aff_ids.update(daily_d4m_cancelled.keys())
    all_aff_ids.update(daily_cancelled_before_alloc.keys())
    all_aff_ids.update(cancelled_after_alloc.keys())

    # Fetch affiliate names
    affiliates = Affiliate.query.filter(Affiliate.id.in_(all_aff_ids)).all() if all_aff_ids else []
    affiliate_names = {aff.id: aff.display_name for aff in affiliates}

    # Prepare table data
    table_rows = []
    for aff_id in all_aff_ids:
        display_name = affiliate_names.get(aff_id, f"Affiliate {aff_id}")

        # Daily metrics
        dc = len(daily_completed.get(aff_id, set()))
        dd4m = len(daily_d4m_cancelled.get(aff_id, set()))
        dcba = len(daily_cancelled_before_alloc.get(aff_id, set()))
        caa = len(cancelled_after_alloc.get(aff_id, set()))

        # Unique daily cancellations
        cancelled_daily_set = (daily_cancelled_before_alloc.get(aff_id, set()) | daily_d4m_cancelled.get(aff_id, set()))
        completed_daily_set = daily_completed.get(aff_id, set())
        unique_daily_cancelled = len(cancelled_daily_set - completed_daily_set)

        total_daily = dc + unique_daily_cancelled
        success_pct = round((dc / total_daily * 100), 2) if total_daily > 0 else 100.0

        # Monthly metrics
        mc = len(monthly_completed.get(aff_id, set()))
        cancelled_monthly_set = (monthly_cancelled_before_alloc.get(aff_id, set()) | monthly_d4m_cancelled.get(aff_id, set()))
        completed_monthly_set = monthly_completed.get(aff_id, set())
        unique_monthly_cancelled = len(cancelled_monthly_set - completed_monthly_set)
        total_monthly = mc + unique_monthly_cancelled
        monthly_success_rate = round((mc / total_monthly * 100), 2) if total_monthly > 0 else 100.0
        avg_completed_trip = round(mc / num_days_elapsed, 2) if num_days_elapsed > 0 else 0.0

        table_rows.append({
            'display_name': display_name,
            'completed_ongoing': dc,
            'd4m_cancelled': dd4m,
            'unique_cancellations': unique_daily_cancelled,
            'success_rate': success_pct,
            'monthly_success_rate': monthly_success_rate,
            'avg_monthly_completed': avg_completed_trip,
            'cx_driver_cancelled_after_alloc': caa,
            'cx_cancelled_before_alloc': dcba,
        })

    # Sort by affiliate name
    table_rows.sort(key=lambda x: x['display_name'])
    whatsapp_table = generate_whatsapp_table(table_rows)
    img_path = generate_table_image(table_rows, filename=f'aff_report_{region_name} - {time_yest.date()}.png')
    # Build HTML table
    html_table = """
    <table border="1" cellpadding="8" cellspacing="0" style="border-collapse: collapse; font-family: Arial, sans-serif; font-size: 14px;">
        <thead style="background-color: #f2f2f2;">
            <tr>
                <th>Affiliate</th>
                <th>Completed/Ongoing</th>
                <th>D4M Cancelled</th>
                <th>Unique Cancellations</th>
                <th>Success Rate (%)</th>
                <th>Monthly Success Rate (%)</th>
                <th>Avg Monthly Completed</th>
                <th>Aff/Driver Cancelled After</th>
                <th>Aff Cancelled Before</th>
            </tr>
        </thead>
        <tbody>
    """

    for row in table_rows:
        html_table += f"""
            <tr>
                <td>{ truncate(row['display_name'], 20) }</td>
                <td>{row['completed_ongoing']}</td>
                <td>{row['d4m_cancelled']}</td>
                <td>{row['unique_cancellations']}</td>
                <td>{row['success_rate']}</td>
                <td>{row['monthly_success_rate']}</td>
                <td>{row['avg_monthly_completed']}</td>
                <td>{row['cx_driver_cancelled_after_alloc']}</td>
                <td>{row['cx_cancelled_before_alloc']}</td>
            </tr>
        """

    html_table += """
        </tbody>
    </table>
    """

    # Prepare email content
    subject = f"Affiliate Booking Report - {region_name} - {time_yest.date()}"
    content = f"""
    <p><strong>Affiliate Report for {region_name} - {time_yest.date()}</strong></p>
    <p>All cancellation numbers are unique.</p>
    {html_table}
    <br/>
<p><strong>Copy-Friendly Format (WhatsApp):</strong></p>
 <p><strong>Affiliate Report for {region_name} - {time_yest.date()}</strong></p>
<pre style="font-family: monospace; font-size: 13px;">
{whatsapp_table}
</pre>
    <p>--<br/>Drivers4Me Team</p>
    """

    # Send email
    to_addr_list = REGION_MAIL.get(region)
    if to_addr_list:
        send_mail_v2(FROM_ADDR, to_addr_list, subject, content, attachment_list=[img_path])
    if os.path.exists(img_path):
        os.remove(img_path)

if __name__ == '__main__':
    with app.app_context():
        args = sys.argv[1:]

        # Extract days_back if passed like --days=2
        days_back = 1
        days_args = [arg for arg in args if arg.startswith('--days=')]
        if days_args:
            try:
                days_back = int(days_args[0].split('=')[1])
            except ValueError as exc:
                raise ValueError(f"Invalid --days= argument: {days_args[0]}") from exc
            args = [arg for arg in args if not arg.startswith('--days=')]

        if not args:
            raise Exception("No region specified")

        for region_str in args:
            try:
                region = int(region_str)
                compute_b2b_stats_and_send(region, days_back)
            except ValueError:
                print(f"Invalid region: {region_str}")
            except Exception as e:
                print(f"Error processing region {region_str}: {e}")
                raise
tags:
  - Authentication
summary: Refresh the access token using the refresh token
description: Refreshes the access token by validating the refresh token.
responses:
  200:
    description: Refresh successful
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          example: true
        message:
          type: string
          example: "Refresh successful"
    examples:
      application/json:
        refresh: true
        message: "Refresh successful"
  401_a:
    description: User is restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  401_b:
    description: Failed to refresh (refresh token missing or invalid)
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          example: false
        message:
          type: string
          example: "Failed to refresh"
    examples:
      application/json:
        refresh: false
        message: "Failed to refresh"
  401_c:
    description: Failed to refresh (refresh token not found)
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          example: false
        message:
          type: string
          example: "Failed to refresh"
    examples:
      application/json:
        refresh: false
        message: "Failed to refresh"
  401_d:
    description: Failed to refresh (token expired)
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          example: false
        message:
          type: string
          example: "Failed to refresh"
    examples:
      application/json:
        refresh: false
        message: "Failed to refresh"

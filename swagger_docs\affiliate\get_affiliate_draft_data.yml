tags:
  - Affiliate
summary: Retrieve draft affiliate data
description: >
  This endpoint retrieves draft affiliate data by fetching information from both MySQL and MongoDB. 
  It returns details about the draft, including pricing data, form field information, and associated master affiliate data.
parameters:
  - name: body
    in: body
    required: true
    description: JSON payload containing the client name.
    schema:
      type: object
      properties:
          draft_id:
            type: integer
            description: The ID of the draft affiliate to fetch.
            example: 1
          regions:
            type: string
            description: A comma-separated list of region IDs for filtering.

responses:
  '200':
    description: Successfully retrieved draft affiliate data.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: 1
            source:
              type: string
              description: The source from which the data was retrieved (MySQL and MongoDB).
              example: "mysql_mongo"
            master:
              type: string
              description: The name of the master affiliate.
              example: "Global"
            master_id:
              type: integer
              description: The ID of the master affiliate.
              example: 1
            data:
              type: object
              description: Detailed draft affiliate data.
              properties:
                master:
                  type: integer
                  description: Master affiliate ID.
                  example: 1
                draft_creator:
                  type: integer
                  description: ID of the draft creator.
                  example: 1
                draft_title:
                  type: string
                  description: Title of the draft.
                  example: "Sample Draft"
                is_favourite:
                  type: boolean
                  description: Whether the draft is marked as a favourite.
                  example: true
                slaves:
                  type: array
                  items:
                    type: integer
                    description: List of slave affiliate IDs.
                    example: [1, 2]
                form_field_oneway:
                  type: string
                  description: Form field data for one-way trip.
                  example: "One-way trip options"
                form_field_round:
                  type: string
                  description: Form field data for round-trip.
                  example: "Round-trip options"
                tripTypeLabel:
                  type: string
                  description: Label for trip type.
                  example: "Trip Type"
                tripTypePlaceholder:
                  type: string
                  description: Placeholder for trip type field.
                  example: "Select trip type"
                tripTypes:
                  type: array
                  items:
                    type: string
                  description: List of available trip types.
                  example: ["One-way", "Round-trip"]

            draft_creator:
              type: string
              description: Name of the draft creator.
              example: "John Doe"
            draft_id:
              type: integer
              description: The ID of the draft.
              example: 1
            customer_pricing_data:
              type: object
              description: Pricing data for customers.
              properties:
                oneway:
                  type: number
                  description: Pricing for one-way trips.
                  example: 1000
                roundtrip:
                  type: number
                  description: Pricing for round trips.
                  example: 1800
            driver_pricing_data:
              type: object
              description: Pricing data for drivers.
              properties:
                oneway:
                  type: number
                  description: Pricing for one-way trips for drivers.
                  example: 1200
                roundtrip:
                  type: number
                  description: Pricing for round trips for drivers.
                  example: 2200
            pricing_cancellation_data:
              type: object
              description: Pricing data for cancellations.
              example:
                cancellation_fee: 100
                cancellation_policy: "Non-refundable"

  '400':
    description: Bad Request, missing draft ID.
    content:
      application/json:
        schema:
          type: object
          properties:
            error:
              type: string
              example: "client_name is required"

  '404':
    description: Not Found, affiliate or data not found.
    content:
      application/json:
        schema:
          type: object
          properties:
            error:
              type: string
              example: "Affiliate not found in SQL"

  '500':
    description: Server error when attempting to retrieve draft affiliate data.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            error:
              type: string
              example: "Failed to retrieve affiliate data"
            details:
              type: string
              example: "Database connection failed"

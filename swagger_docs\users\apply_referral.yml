tags:
  - User
summary: Apply Referral Code
description: >
  This endpoint allows a user to apply a referral code. It validates the referral code and, if valid, applies referral rewards to the user.
parameters:
  - name: ref_code
    in: formData
    type: string
    required: true
    description: The referral code to apply
responses:
  200_a:  # Success - Referral applied successfully
    description: Referral applied successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        credit:
          type: integer
          description: User's updated credit
        credit_added:
          type: integer
          description: Credit added for referral
        message:
          type: string
          description: Success message
          example: "Referral applied successfully"
    examples:
      application/json:
        success: 1
        credit: 100
        credit_added: 50
        message: "Referral applied successfully"
  
  200_b:  # Code already used
    description: Code already used
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3)
          example: -3
        message:
          type: string
          description: Error message
          example: "Code already used"
    examples:
      application/json:
        success: -3
        message: "Code already used"
  
  200_c:  # User not eligible (driver)
    description: User not eligible (driver)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-6)
          example: -6
        message:
          type: string
          description: Error message
          example: "User not eligible"
    examples:
      application/json:
        success: -6
        message: "User not eligible"
  
  200_d:  # Code invalid
    description: Code invalid
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4)
          example: -4
        message:
          type: string
          description: Error message
          example: "Code invalid"
    examples:
      application/json:
        success: -4
        message: "Code invalid"
  
  200_e:  # Code invalid (self-referral)
    description: Code invalid (self-referral)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-5)
          example: -5
        message:
          type: string
          description: Error message
          example: "Code invalid"
    examples:
      application/json:
        success: -5
        message: "Code invalid"
  
  200_f:  # User already completed trip, so not eligible
    description: User not eligible (completed trip)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-6)
          example: -6
        message:
          type: string
          description: Error message
          example: "User not eligible"
    examples:
      application/json:
        success: -6
        message: "User not eligible"
  
  200_g:  # Referral applied but no additional credit
    description: Referral applied but no credit added
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1)
          example: 1
        credit:
          type: integer
          description: User's current credit (unchanged)
        message:
          type: string
          description: Success message
          example: "Referral applied, but no additional credit added"
    examples:
      application/json:
        success: 1
        credit: 0
        message: "Referral applied, but no additional credit added"
  
  200_h:  # No user found with provided mobile number
    description: No user found with provided mobile number
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -2
        message: "User not found"
  
  201:  # Incomplete form data
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"

  401_a:  # Unauthorized - User restricted
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"

  403_a:  # Forbidden - User not eligible (trip completed)
    description: Forbidden - User not eligible
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-6)
          example: -6
        message:
          type: string
          description: Error message
          example: "User not eligible"
    examples:
      application/json:
        success: -6
        message: "User not eligible"
  
  403_b:  # Forbidden - User not eligible (account deleted and recreated)
    description: Forbidden - User not eligible
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-6)
          example: -6
        message:
          type: string
          description: Error message
          example: "User not eligible"
    examples:
      application/json:
        success: -6
        message: "User not eligible"
  
  500:  # Internal server error
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-5)
          example: -5
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -5
        message: "DB Error"

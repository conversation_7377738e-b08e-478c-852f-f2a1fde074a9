{"static": "/static/<path:filename>", "flask_util_js": "/flask_util.js", "live_update_single_row_testing_function": "/api/testing/booking_list/single_row", "ph_changelog": "/api/admin/ph_change/log", "user_logs": "/api/admin/ph_change/user_logs", "flasgger.static": "/flasgger_static/<path:filename>", "flasgger.apidocs": "/docs", "flasgger.oauth_redirect": "/oauth2-redirect.html", "flasgger.<lambda>": "/apidocs/index.html", "flasgger.apispec_1": "/apispec_1.json", "register_cust.register_cust_scr": "/api/register_cust", "register_cust.register_cust": "/api/register_cust", "admin_utility.add_admin_access": "/api/admin/access/add", "admin_utility.edit_admin_access": "/api/admin/access/edit", "admin_utility.list_admins": "/api/admin/access/list", "admin_utility.list_admin_logs": "/api/admin/access/logs", "admin_utility.list_admin_all_logs": "/api/admin/access/all/logs", "admin_utility.get_admin_details": "/api/admin/access/fetch", "login.gen_otp": "/token/otp/generate", "login.gen_otp_delete": "/token/otp/generate/delete", "login.test_jwt": "/test-jwt", "login.val_otp": "/token/otp/validate", "login.val_otp_delete": "/token/otp/validate/delete", "login.check_exists": "/token/exists", "login.check_exists_driver": "/token/exists/driver", "login.login": "/token/login", "login.swagger_user_login": "/swagger/user/token/login", "login.refresh": "/token/refresh", "login.affiliate_refresh": "/affiliate/refresh", "login.logout": "/token/remove", "login.verify": "/token/verify", "login.login_driv": "/token/login_driv", "login.swagger_login_driv": "/swagger/token/login_driv", "login.login_admin": "/token/login/admin", "login.login_c24": "/token/login/c24", "login.login_olx": "/token/login/olx", "login.login_zc": "/token/login/zc", "login.login_revv": "/token/login/revv", "login.login_gujral": "/token/login/gujral", "login.login_cardekho": "/token/login/cardekho", "login.login_bhandari": "/token/login/bhandari", "login.login_mahindra": "/token/login/mahindra", "login.login_revv_v2": "/token/login/revv_v2", "login.login_spinny_affiliate": "/affiliate/login/spinny", "login.login_spinny": "/token/login/spinny", "login.login_pridehonda": "/token/login/pridehonda", "login.set_fcm_token": "/token/login/set_fcm", "login.set_fcm_token_driver": "/token/login/set_fcm_driver", "newlogin.gen_otp": "/token/admin/otp/generate", "newlogin.generate_otp": "/api/otp/generate", "newlogin.validate_otp_api": "/token/otp/validate/only", "newlogin.val_otp": "/token/admin/otp/validate", "newlogin.password_change_val_otp": "/token/admin/password_change/otp/validate", "newlogin.password_change": "/token/admin/password_change", "newlogin.admin_refresh": "/api/admin/refresh", "newlogin.login_admin": "/token/admin/login", "newlogin.swagger_login_admin": "/swagger/testing/admin/login", "newlogin.delete_user": "/token/admin/user/delete", "newlogin.activate_user": "/token/admin/user/activate", "newlogin.redis_test": "/api/redis-test", "newlogin.blacklisted_tokens": "/api/blacklisted-tokens", "newlogin.clear_blacklist": "/api/blacklist/clear", "newlogin.get_data": "/api/get_data", "newlogin.show_mobile_numbers": "/api/otp/mobile-numbers", "newlogin.delete_all_notifications": "/delete_all_notifications", "newlogin.notify_all_clients": "/api/notify/all_clients", "calling_admin.initiate_call": "/api/admin/initiate_call", "calling_admin.admin_call_log": "/api/admin/call_log", "admin_driver.search_users_drivers": "/api/admin/dash/search", "admin_driver.single_driver_admin": "/api/admin/single/driver", "admin_driver.fetch_new_data": "/api/admin/dash/new_data", "admin_driver.get_driver_details": "/api/admin/driver/details", "admin_driver.search_drivers_for_register_list": "/api/admin/register_driver_list", "admin_driver.get_all_logs": "/api/admin/driver_approval_log/all", "admin_driver.add_driver_log": "/api/admin/driver_log/add", "admin_driver.get_driver_trips": "/api/admin/driver_trips/logs", "admin_driver.update_driver_all": "/api/admin/driver/update_all", "admin_driver.updateandlog_driver_log": "/api/admin/driver/updateandlog", "admin_driver.update_pres_region": "/api/admin/update_pres_region", "admin_driver.get_locality": "/api/admin/getlocality", "admin_driver.generate_driver_iddoc_otp": "/api/admin/driver/generate_iddoc_otp", "admin_driver.driver_iddoc_a_verify": "/api/admin/driver/iddoc_a_verify", "admin_driver.driver_iddoc_v_verify": "/api/admin/driver/iddoc_v_verify", "admin_driver.driver_id_a_reverify": "/api/admin/driver/id_a_reverify", "admin_driver.driver_id_v_reverify": "/api/admin/driver/id_v_reverify", "admin_driver.driver_bankdoc_verify": "/api/admin/driver/bankdoc_verify", "admin_driver.driver_bank_reverify": "/api/admin/driver/bank_reverify", "admin_driver.driver_dl_verify": "/api/admin/driver/dl_verify", "admin_driver.driver_dl_reverify": "/api/admin/driver/dl_reverify", "admin_booking.total_counts_bookings": "/api/admin/total_count", "admin_booking.booking_list": "/api/admin/booking_list", "admin_booking.booking_list_single_row": "/api/admin/booking_list/single_row", "admin_booking.allocate_booking_list": "/api/admin/booking_allocate_driver_list", "admin_booking.admin_cancel_charge_new": "/api/admin/cancel/charge_new", "admin_booking.admin_cancel_booking_charge": "/api/admin/cancel_update/booking_charge", "admin_booking.allocate_booking_driver": "/api/admin/booking_allocate_driver", "admin_booking.admin_unallocate_trip_new": "/api/admin/unallocate/new", "admin_booking.admin_cancel_booking": "/api/admin/cancel/new", "admin_booking.booking_details": "/api/admin/book/details", "admin_booking.fetch_driver_loc": "/api/admin/driver_loc", "admin_booking.trip_log": "/api/admin/trip_log", "admin_booking.change_cancellation_reason": "/api/admin/change_cancellation_reason/new", "admin_booking.change_unallocation_reason": "/api/admin/change_unallocate_reason/new", "admin_booking.fetch_driver_location": "/api/admin/driver_location", "admin_booking.update_booking_details": "/api/admin/booking/update_all", "admin_booking.get_booking_logs": "/api/admin/booking/<int:booking_id>/logs", "admin_booking.status_change": "/api/trip/status_change", "admin_booking.get_user_search_booking_data": "/api/user_search_bookings_data", "admin_booking.get_estimate_details_user": "/api/admin/user_estimate_details", "admin_booking.add_remark": "/api/add-remark", "admin_booking.search_users_drivers": "/api/admin/dash/newreg", "admin_booking.admin_cancel_update_charge": "/api/admin/cancel_update/charge", "admin_booking.get_available_drivers": "/api/admin/available_drivers", "admin_booking.fetch_call_logs": "/api/admin/call_logs", "admin_customer.customer_view": "/api/admin/customer_details", "admin_customer.customer_edit": "/api/admin/update_customer_details", "admin_customer.customer_logs": "/api/admin/customer_log", "admin_customer.search_customers_for_register_list": "/api/admin/register_customer_list", "admin_customer.fetch_single_customers_row": "/api/admin/fetch_single_customer_row", "admin_customer.customer_update_reg_source": "/api/admin/update_customer_reg_source", "admin_customer.customer_update_reg_remark": "/api/admin/update_customer_reg_remark", "admin_customer.customer_update_reg_city": "/api/admin/update_customer_reg_city", "admin_customer.customer_update_reg_region": "/api/admin/update_customer_reg_region", "admin_analytics.admin_analytics_count": "/api/admin/analytics_count", "admin_analytics.admin_analytics_graph_daily_sales": "/api/admin/analytics_graph_daily_sales", "admin_analytics.admin_analytics_graph_sales": "/api/admin/analytics_graph_sales", "admin_analytics.admin_analytics_graph_daily_revenue": "/api/admin/analytics_graph_daily_revenue", "admin_analytics.admin_analytics_graph_revenue": "/api/admin/analytics_graph_revenue", "admin_analytics.admin_analytics_graph_daily_trips": "/api/admin/analytics_graph_daily_trips", "admin_analytics.admin_analytics_graph_trips": "/api/admin/analytics_graph_trips", "admin_analytics.total_counts_ratings": "/api/admin/total_ratings", "admin_analytics.total_counts_reg_customer": "/api/admin/customer_register_reg_count", "admin_analytics.total_counts_source_customer": "/api/admin/customer_register_source_count", "coupon.add_new_coupon": "/api/admin/add_coupon", "coupon.active_coupon": "/api/admin/active_coupon", "coupon.inactive_coupon": "/api/admin/inactive_coupon", "coupon.change_coupon_state": "/api/admin/change_coupon_state", "coupon.admin_search_coupon": "/api/admin/search/coupon", "coupon.get_coupon_by_id": "/api/admin/coupon", "coupon.update_coupon": "/api/admin/update_coupon", "create_booking_admin.register_cust_soft": "/api/admin/register_cust_soft", "create_booking_admin.register_search_soft": "/api/admin/search_soft", "create_booking_admin.booking_soft": "/api/admin/book_soft", "dues_credit_admin.search_user_by_mobile": "/api/admin/search_user_by_mobile", "dues_credit_admin.search_driver_by_mobile": "/api/admin/search_driver_by_mobile", "dues_credit_admin.add_customer_credit": "/api/admin/customer_credit", "dues_credit_admin.customer_credit_log": "/api/admin/fetch_customer_credit_log", "dues_credit_admin.add_driver_due": "/api/admin/driver_due", "dues_credit_admin.driver_due_log": "/api/admin/driver_dues_log", "dues_credit_admin.dues_driver_list": "/api/admin/dues_driver_list", "pricing_admin.add_city": "/api/admin/pricing/add_city", "pricing_admin.add_occasion": "/api/admin/pricing/add_occasion", "pricing_admin.fetch_city": "/api/admin/pricing/all_city", "pricing_admin.fetch_details": "/api/admin/pricing/details", "pricing_admin.occasion_data": "/api/admin/pricing/occasion_data", "pricing_admin.update_pricing": "/api/admin/pricing/update", "pricing_admin.update_occasion_pricing": "/api/admin/pricing/update_occasion", "pricing_admin.delete_pricing": "/api/admin/pricing/delete", "register_driver.throw_500": "/api/500", "register_driver.check_reg_complete": "/api/register/driver/validate", "register_driver.reg_drv": "/api/register/driver", "bookride.book_driver": "/api/book", "bookride.register_search": "/api/search", "bookride.register_search_dummy": "/api/search/dummy", "bookride.book_driver_dummy": "/api/book/dummy", "bookride.cancel_user_charge": "/api/decline/user/charge", "bookride.cancel_user": "/api/decline/user", "bookride.cancel_driver_charge": "/api/decline/driver/charge", "bookride.cancel_driver": "/api/decline/driver", "admin.adminpage": "/admin", "admin.adminsuperpage": "/adminSuper", "admin.adminpag2": "/admin2", "admin.search": "/api/admin_search", "admin.approve": "/api/admin_appr", "admin.cancel_list": "/api/admin/cancelled", "admin.book_ids": "/api/admin/book_ids", "admin.booking_list": "/api/admin/booking", "admin.booking_single": "/api/admin/book_entry_single", "admin.estimate_no_book": "/api/admin/estimate_no_book", "admin.all_drivers": "/api/admin/driver_list", "admin.all_drivers_allocate": "/api/admin/driver_list_allocate", "admin.driver_paid": "/api/admin/driver_paid", "admin.search_by_mobile": "/api/admin/search_by_mobile", "admin.driver_update_pic": "/api/admin/driver_update/pic", "admin.driver_update_loc": "/api/admin/driver_update/base_loc", "admin.driver_update_info": "/api/admin/driver_update/info", "admin.allocate": "/api/admin/allocate_driver", "admin.show_pending": "/api/admin/show_book_pending", "admin.change_book_comment": "/api/admin/change_book_comment", "admin.available_driver_map": "/admin/available/<int:booking_id>", "admin.available_driver_map_code": "/api/admin/available/<string:code>", "admin.change_cancellation_reason": "/api/admin/change_cancellation_reason", "admin.admin_stop_trip": "/api/admin/stop_trip", "admin.broadcast_trip": "/api/admin/broadcast_trip", "admin.change_trip_start": "/api/admin/change_trip_start", "admin.start_trip": "/api/admin/start_trip", "admin.get_fares": "/api/admin/get_fares", "admin.change_price": "/api/admin/change_price", "admin.driver_acc_alter": "/api/admin/driver_acc_alter", "admin.completed_trips_count": "/api/admin/completed_trips", "admin.revenue": "/api/admin/revenue", "admin.sales": "/api/admin/sales", "admin.all_sales": "/api/admin/all_sales", "admin.all_trips": "/api/admin/all_trips", "admin.all_revenues": "/api/admin/all_revenues", "admin.count_users": "/api/admin/user_count", "admin.daily_stats": "/api/admin/daily_stats", "admin.priceInfo": "/api/admin/price", "admin.get_hold": "/api/admin/get_hold", "admin.get_hold_zoom": "/api/admin/get_hold_zoom", "admin.set_hold": "/api/admin/set_hold", "admin.set_hold_zoom": "/api/admin/set_hold_zoom", "admin.cancel_customer": "/api/admin/customer_decline", "admin.admin_newstop_trip": "/api/admin/newstop_trip", "admin.admin_restart_trip": "/api/admin/restart_trip", "admin.admin_unallocate_trip": "/api/admin/unallocate", "admin.admin_payment_switch": "/api/admin/payment_switch", "admin.admin_book_log": "/api/admin/book_log", "admin.admin_driver_due_log": "/api/admin/driver_due_log", "admin.d4m_cred_view": "/api/admin/cred_view", "admin.d4m_cred_alter": "/api/admin/credit_alter", "admin.event_complete_trips_count": "/api/admin/event_complete_trips_count", "admin.feedback_set": "/api/admin/feedback/set", "admin.feedback_get": "/api/admin/feedback/get", "admin.ph_change": "/api/admin/change/phone", "admin.photo_change": "/api/admin/change/photo", "admin.minios_convert": "/api/admin/minios_convert", "admin.rt_convert": "/api/admin/rt_convert", "admin.b2c_type_convert": "/api/admin/b2c_type_convert", "admin.cartype_change": "/api/admin/cartype_change", "admin.get_cust_info": "/api/admin/user/info", "admin.get_driver_info": "/api/admin/driver/info", "admin.booking_pic_fetch": "/api/admin/booking_pic", "admin.admin_trip_state_change": "/api/admin/trip_state_change", "admin.admin_user_label_get": "/api/admin/user_label/get", "admin.admin_user_label_set": "/api/admin/user_label/set", "admin.admin_cancel_updatecharge": "/api/admin/cancelupdate/charge", "admin.admin_cancel_charge": "/api/admin/cancel/charge", "admin.admin_cancel": "/api/admin/cancel", "admin.admin_customer_credit_log": "/api/admin/customer_credit_log", "admin.search_loc_admin": "/admin/search-loc.html", "drivers.reverify": "/api/driver/reverify", "drivers.exp_set": "/api/driver/experience/set", "drivers.exp_get": "/api/driver/experience/get", "drivers.confirm_book": "/api/driver/confirm", "drivers.reject_book": "/api/driver/reject", "drivers.list_confirm": "/api/driver/list_confirm", "drivers.pending_cust": "/api/driver/pending_cust", "drivers.booking_info": "/api/driver/booking_info", "drivers.past_cust": "/api/driver/past_cust", "drivers.get_earning": "/api/driver/earning/total", "drivers.get_earning_monthly": "/api/driver/earning/monthly", "drivers.ongoing_trip": "/api/driver/ongoing", "drivers.driver_trans_log": "/api/driver/trans_log", "drivers.set_driver_loc": "/api/driver/set_loc", "users.pending_conf": "/api/user/pending_ride", "users.past_cust": "/api/user/past_ride", "users.ongoing_trip_user": "/api/user/ongoing", "users.get_restore_id": "/api/user/restore_id/get", "users.set_restore_id": "/api/user/restore_id/set", "users.user_trans_log": "/api/user/trans_log", "users.set_user_loc": "/api/user/set_loc", "users.user_payment_switch": "/api/user/payment_switch", "users.referral_use": "/api/user/apply_referral", "users.delete_id": "/api/user/delete", "users.ongoing_trip_user_dep": "/api/customer/ongoing", "users.customer_unallocate_trip": "/api/customer/unallocate", "acc_profile.get_profile": "/api/profile/details", "acc_profile.get_profile_type": "/api/profile/type", "acc_profile.change_name": "/api/profile/change_name", "acc_profile.change_email": "/api/profile/change_email", "acc_profile.get_ref_code": "/api/profile/get/ref_code", "acc_profile.change_base_loc": "/api/profile/change_base_loc", "acc_profile.change_available": "/api/profile/available", "acc_profile.get_available": "/api/profile/check", "acc_profile.change_pwd": "/api/profile/change_pwd", "acc_profile.disable_account": "/api/profile/disable", "trips.stop_trip_pic": "/api/trip/stop/pic", "trips.start_trip_pic": "/api/trip/start/pic", "trips.start_trip": "/api/trip/start", "trips.stop_trip": "/api/trip/stop", "trips.user_rate": "/api/trip/user_rate", "trips.driver_rate": "/api/trip/driver_rate", "trips.user_rate_update": "/api/trip/user_rate/update", "trips.driver_rate_update": "/api/trip/driver_rate/update", "trips.trip_state_change": "/api/trip/state_change", "trips.booking_otp_validate": "/api/trip/otp_validate", "website.contact_page": "/contact-us", "website.delete_account": "/delete-account", "website.tnc_page": "/terms-and-condition", "website.faq_page": "/frequently-asked-questions", "website.acko_tnc_page": "/acko_tnc", "website.login_page": "/login", "website.main": "/", "website.new": "/new", "website.assetlinks": "/.well-known/assetlinks.json", "website.painfo": "/pa-info", "website.book": "/book", "website.trip": "/trips", "website.profile": "/profile", "website.get_estimate": "/api/get_estimate", "website.trip_map": "/trip/map/<book_code>", "website.contact_us_api": "/website/contact_us", "adminLogin.adminLogin_page": "/adminLogin", "affiliate.c24_login": "/affiliate/cars24/login", "affiliate.cars24_console_page_base": "/affiliate/cars24", "affiliate.cars24_upcoming": "/api/c24/upcoming", "affiliate.cars24_ongoing": "/api/c24/ongoing", "affiliate.cars24_past": "/api/c24/past", "affiliate.cars24_book": "/api/c24/book", "affiliate.cars24_fetch_rep": "/api/c24/fetch_rep", "affiliate.cars24_save_cmt": "/api/c24/save_cmt", "affiliate.cars24_cancel_trip": "/api/c24/cancel", "affiliate.cars24_add_rep": "/api/c24/add_rep", "affiliate.cars24_pic_upload": "/api/driver/c24/upload", "affiliate.cars24_alloc_list": "/api/c24/alloc_list", "affiliate.cars24_alloc_driver": "/api/c24/alloc_driver", "affiliate.revv_login": "/affiliate/revv/login", "affiliate.revv_console_page_base": "/affiliate/revv", "affiliate.revv_book": "/api/revv/book", "affiliate.revv_upcoming": "/api/revv/upcoming", "affiliate.revv_ongoing": "/api/revv/ongoing", "affiliate.revv_past": "/api/revv/past", "affiliate.revv_cancel_trip": "/api/revv/cancel", "affiliate.gujral_login": "/affiliate/gujral/login", "affiliate.gujral_console_page_base": "/affiliate/gujral", "affiliate.gujral_book": "/api/gujral/book", "affiliate.gujral_upcoming": "/api/gujral/upcoming", "affiliate.gujral_ongoing": "/api/gujral/ongoing", "affiliate.gujral_past": "/api/gujral/past", "affiliate.gujral_cancel_trip": "/api/gujral/cancel", "affiliate.olx_login": "/affiliate/olx/login", "affiliate.olx_console_page_base": "/affiliate/olx", "affiliate.olx_upcoming": "/api/olx/upcoming", "affiliate.olx_ongoing": "/api/olx/ongoing", "affiliate.olx_past": "/api/olx/past", "affiliate.olx_book_trip": "/api/olx/book", "affiliate.olx_fetch_rep": "/api/olx/fetch_rep", "affiliate.olx_save_cmt": "/api/olx/save_cmt", "affiliate.olx_cancel_trip": "/api/olx/cancel", "affiliate.olx_rep_add": "/api/olx/add_rep", "affiliate.olx_upload": "/api/driver/olx/upload", "affiliate.olx_allocs": "/api/olx/alloc_list", "affiliate.olx_alloc_driver": "/api/olx/alloc_driver", "affiliate.zoomcar_login": "/affiliate/zoomcar/login", "affiliate.zoomcar_console_page_base": "/affiliate/zoomcar", "affiliate.zoomcar_upcoming": "/api/zoomcar/upcoming", "affiliate.zoomcar_ongoing": "/api/zoomcar/ongoing", "affiliate.zoomcar_past": "/api/zoomcar/past", "affiliate.zoomcar_book_trip": "/api/zoomcar/book", "affiliate.zoomcar_fetch_rep": "/api/zoomcar/fetch_rep", "affiliate.zoomcar_save_cmt": "/api/zoomcar/save_cmt", "affiliate.zoomcar_cancel_trip": "/api/zoomcar/cancel", "affiliate.zoomcar_rep_add": "/api/zoomcar/add_rep", "affiliate.zoomcar_upload": "/api/driver/zoomcar/upload", "affiliate.zoomcar_allocs": "/api/zoomcar/alloc_list", "affiliate.zoomcar_alloc_driver": "/api/zoomcar/alloc_driver", "affiliate.cardekho_login": "/affiliate/cardekho/login", "affiliate.cardekho_console_page_base": "/affiliate/cardekho", "affiliate.cardekho_upcoming": "/api/cardekho/upcoming", "affiliate.cardekho_ongoing": "/api/cardekho/ongoing", "affiliate.cardekho_past": "/api/cardekho/past", "affiliate.cardekho_book_trip": "/api/cardekho/book", "affiliate.cardekho_fetch_rep": "/api/cardekho/fetch_rep", "affiliate.cardekho_save_cmt": "/api/cardekho/save_cmt", "affiliate.cardekho_cancel_trip": "/api/cardekho/cancel", "affiliate.cardekho_rep_add": "/api/cardekho/add_rep", "affiliate.cardekho_upload": "/api/driver/cardekho/upload", "affiliate.cardekho_allocs": "/api/cardekho/alloc_list", "affiliate.cardekho_alloc_driver": "/api/cardekho/alloc_driver", "affiliate.bhandari_login": "/affiliate/bhandari/login", "affiliate.bhandari_console_page_base": "/affiliate/bhandari", "affiliate.bhandari_upcoming": "/api/bhandari/upcoming", "affiliate.bhandari_ongoing": "/api/bhandari/ongoing", "affiliate.bhandari_past": "/api/bhandari/past", "affiliate.bhandari_book_trip": "/api/bhandari/book", "affiliate.bhandari_fetch_rep": "/api/bhandari/fetch_rep", "affiliate.bhandari_save_cmt": "/api/bhandari/save_cmt", "affiliate.bhandari_cancel_penalty": "/api/bhandari/cancel_charge", "affiliate.bhandari_cancel_trip": "/api/bhandari/cancel", "affiliate.bhandari_rep_add": "/api/bhandari/add_rep", "affiliate.bhandari_upload": "/api/driver/bhandari/upload", "affiliate.bhandari_alloc": "/api/bhandari/alloc_list", "affiliate.bhandari_alloc_driver": "/api/bhandari/alloc_driver", "affiliate.mahindra_login": "/affiliate/jyoti/login", "affiliate.mahindra_console_page_base": "/affiliate/jyoti", "affiliate.mahindra_upcoming": "/api/mahindra/upcoming", "affiliate.mahindra_ongoing": "/api/mahindra/ongoing", "affiliate.mahindra_past": "/api/mahindra/past", "affiliate.mahindra_book_trip": "/api/mahindra/book", "affiliate.mahindra_fetch_rep": "/api/mahindra/fetch_rep", "affiliate.mahindra_save_cmt": "/api/mahindra/save_cmt", "affiliate.mahindra_cancel_trip": "/api/mahindra/cancel", "affiliate.mahindra_rep_add": "/api/mahindra/add_rep", "affiliate.mahindra_upload": "/api/driver/mahindra/upload", "affiliate.mahindra_alloc": "/api/mahindra/alloc_list", "affiliate.mahindra_alloc_driver": "/api/mahindra/alloc_driver", "affiliate.mahindra_booking_pic_fetch": "/api/mahindra/booking_pic", "affiliate.revv_v2_login": "/affiliate/revv_v2/login", "affiliate.revv_v2_console_page_base": "/affiliate/revv_v2", "affiliate.revv_v2_upcoming": "/api/revv_v2/upcoming", "affiliate.revv_v2_ongoing": "/api/revv_v2/ongoing", "affiliate.revv_v2_past": "/api/revv_v2/past", "affiliate.revv_v2_book_trip": "/api/revv_v2/book", "affiliate.revv_v2_fetch_rep": "/api/revv_v2/fetch_rep", "affiliate.revv_v2_save_cmt": "/api/revv_v2/save_cmt", "affiliate.revv_v2_cancel_trip": "/api/revv_v2/cancel", "affiliate.revv_v2_rep_add": "/api/revv_v2/add_rep", "affiliate.revv_v2_upload": "/api/driver/revv_v2/upload", "affiliate.revv_v2_allocs": "/api/revv_v2/alloc_list", "affiliate.revv_v2_alloc_driver": "/api/revv_v2/alloc_driver", "affiliate.spinny_login": "/affiliate/spinny/login", "affiliate.spinny_console_page_base": "/affiliate/spinny", "affiliate.spinny_upcoming": "/api/spinny/upcoming", "affiliate.spinny_ongoing": "/api/spinny/ongoing", "affiliate.spinny_past": "/api/spinny/past", "affiliate.spinny_affiliate_book_trip": "/affiliate/spinny/book", "affiliate.spinny_cancel_trip_new": "/affiliate/spinny/cancel", "affiliate.spinny_booking_state": "/affiliate/spinny/book_state", "affiliate.spinny_book_trip": "/api/spinny/book", "affiliate.spinny_fetch_rep": "/api/spinny/fetch_rep", "affiliate.spinny_save_cmt": "/api/spinny/save_cmt", "affiliate.spinny_cancel_penalty": "/api/spinny/cancel_charge", "affiliate.spinny_cancel_trip": "/api/spinny/cancel", "affiliate.spinny_rep_add": "/api/spinny/add_rep", "affiliate.spinny_upload": "/api/driver/spinny/upload", "affiliate.spinny_alloc": "/api/spinny/alloc_list", "affiliate.spinny_alloc_driver": "/api/spinny/alloc_driver", "affiliate.spinny_booking_pic_fetch": "/api/spinny/booking_pic", "affiliate.pridehonda_login": "/affiliate/pridehonda/login", "affiliate.pridehonda_console_page_base": "/affiliate/pridehonda", "affiliate.pridehonda_upcoming": "/api/pridehonda/upcoming", "affiliate.pridehonda_ongoing": "/api/pridehonda/ongoing", "affiliate.pridehonda_past": "/api/pridehonda/past", "affiliate.pridehonda_book_trip": "/api/pridehonda/book", "affiliate.pridehonda_fetch_rep": "/api/pridehonda/fetch_rep", "affiliate.pridehonda_save_cmt": "/api/pridehonda/save_cmt", "affiliate.pridehonda_cancel_penalty": "/api/pridehonda/cancel_charge", "affiliate.pridehonda_cancel_trip": "/api/pridehonda/cancel", "affiliate.pridehonda_rep_add": "/api/pridehonda/add_rep", "affiliate.pridehonda_upload": "/api/driver/pridehonda/upload", "affiliate.pridehonda_allocs": "/api/pridehonda/alloc_list", "affiliate.pridehonda_alloc_driver": "/api/pridehonda/alloc_driver", "affiliate.pridehonda_booking_pic_fetch": "/api/pridehonda/booking_pic", "payments.initiate_credit_order": "/api/user/credits/start", "payments.fail_credit_trans": "/api/user/credits/fail_rp", "payments.add_d4m_credit": "/api/user/credits/complete_rp", "payments.view_d4m_credit": "/api/user/credits/view", "payments.alter_d4m_credit": "/api/user/credits/alter", "payments.verify_paytm_trans": "/api/user/credits/complete_pt", "track.track_driver": "/track/<int:driver_id>", "track.track_booking": "/track/booking/<int:book_id>", "track.track_book": "/track/book/<int:book_id>", "track.track_booking_code": "/track/booking/<book_code>", "track.track_booking_history": "/history/booking/<int:book_id>", "track.admin_track_booking_history": "/api/history/booking/<string:code>", "slackbot.find_userinfo": "/slack/userinfo", "slackbot.find_usermobile": "/slack/usermobile", "slackbot.find_driver": "/slack/driverinfo", "slackbot.find_username": "/slack/username", "slackbot.find_drivername": "/slack/drivername", "slackbot.find_c24book": "/slack/c24book_info", "slackbot.find_revvbook": "/slack/revvbook_info", "slackbot.find_current_trip": "/slack/current_trip", "slackbot.find_book": "/slack/book_info", "slackbot.find_book_code": "/slack/book_info_code", "slackbot.find_badtrip": "/slack/bad_trip", "slackbot.find_badtrip_revv": "/slack/bad_trip_revv", "slackbot.get_cash_coll": "/slack/drivercash", "slackbot.get_book_driver": "/slack/find_book", "slackbot.b2c_stats": "/slack/b2c_stats", "slackbot.c24_stats": "/slack/c24_stats", "slackbot.revv_stats": "/slack/revv_stats", "slackbot.get_perma_all": "/slack/allperma", "slackbot.add_idle_day": "/slack/add_idle", "slackbot.del_idle_day": "/slack/del_idle", "slackbot.convert_to_perma": "/slack/convert_perma", "slackbot.fix_perma": "/slack/fix_perma", "slackbot.convert_to_part": "/slack/convert_part", "slackbot.release_to_all": "/slack/release_to_all", "slackbot.ph_change": "/slack/ph_change", "slackbot.mark_user": "/slack/mark_user", "slackbot.unmark_user": "/slack/unmark_user", "slackbot.driver_paid": "/slack/driver/paid", "slackbot.set_region": "/slack/user/set_region", "api.login_api": "/d4m/api/token/login", "api.check_exists_api": "/d4m/api/token/exists", "api.refresh_api": "/d4m/api/token/refresh", "api.logout_api": "/d4m/api/token/remove", "api.verify_api": "/d4m/api/token/verify", "api.register_search_api": "/d4m/api/search", "api.cancel_user_api": "/d4m/api/decline/user", "api.book_driver_api": "/d4m/api/book", "api.pending_conf_api": "/d4m/api/user/pending_ride", "api.past_cust_api": "/d4m/api/user/past_ride", "api.ongoing_trip_user_api": "/d4m/api/user/ongoing", "campaign.download_app": "/download", "campaign.send_ref": "/api/ref_msg", "campaign.sr_ref": "/api/sr_msg", "zoomapi.zoomcar_book_trip": "/api/affiliate/zoomcar/book", "zoomapi.zoomcar_cancel_trip": "/api/affiliate/zoomcar/cancel", "zoomapi.login_zoom": "/api/affiliate/zoomcar/login", "seo.render_template_based_on_path": "/get-drivers-in-mumbai", "seo.alipore": "/get-drivers-in-alipore", "seo.behala": "/get-drivers-in-behala", "seo.howrah": "/get-drivers-in-howrah", "seo.newtown": "/get-drivers-in-newtown", "seo.parkstreet": "/get-drivers-in-parkstreet", "seo.rajarhat": "/get-drivers-in-rajarhat", "seo.saltlake": "/get-drivers-in-saltlake-city", "seo.ballygunge": "/get-drivers-in-ballygunge", "seo.jadavpur": "/get-drivers-in-jadavpur", "seo.kalighat": "/get-drivers-in-kalighat", "seo.kolkata": "/get-drivers-in-kolkata", "delete_account.delete_id": "/api/delete", "delete_account.get_delete_log": "/api/delete_log", "call_masking.direct_calling": "/api/masking/direct_calling", "call_masking.hangup_log": "/api/call/hangup", "call_masking.callRequest": "/api/calling/callRequest"}
#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  faq.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON>

from flask import Blueprint, render_template

faq = Blueprint('faq', __name__)


@faq.route('/faq', methods=['GET', 'POST'])
def faq_page():
    return render_template('faq.html')

@faq.route('/ver2', methods=['GET', 'POST'])
def home_ver2():
    return render_template('homepage-v2.html')

@faq.route('/ver3', methods=['GET', 'POST'])
def home_ver3():
    return render_template('homepage-v3.html')

@faq.route('/faq_ver2', methods=['GET', 'POST'])
def faq_ver2():
    return render_template('faq-v2.html')

[general]
filename = kolkata-bhandari.csv
query = select bd_appt_id, bd_veh_reg, CASE bd_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, book_loc_name, dest_book_name, convert_tz(trip_start,'+00:00', '+05:30'), convert_tz(trip_stop,'+00:00', '+05:30'), CONCAT(bd_dist+3,' Km'), user_fname, user_lname from bookings, book_dest, trip, users, drivers, bhandari_bookings where book_ref=dest_book_id and bd_book_ref=book_ref and trip_book=book_ref and book_valid=1 and book_region=0 and book_driver=driver_id and user_id=driver_user and (date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))>= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 1) and date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))<= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 0));
query1 = select bd_book_id, bd_appt_id, bd_veh_reg, CASE bd_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, book_loc_name, dest_book_name, time(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30')), date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30')), CASE book_valid when 1 THEN "Completed" else "Cancelled" END from bookings, bhandari_bookings, book_dest where bd_book_ref=book_ref and book_region=0 and book_ref=dest_book_id and bd_veh_reg<>"" and (date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))>= date("2021-12-01") and date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))<= date("2021-12-07"));
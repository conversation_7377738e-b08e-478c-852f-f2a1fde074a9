
from flask import Flask, request
from flask_jwt_extended import J<PERSON><PERSON>ana<PERSON>
from flask_caching import Cache
from flask_util_js import FlaskUtilJs
from flask_s3 import FlaskS3
from flask_cors import CORS
from pymongo import MongoClient
from firebase_admin import credentials, initialize_app, firestore
import os, redis, json, gevent
from datetime import timed<PERSON>ta
from config import ProdConfig, TestConfig
from db_config import init_dbs
from redis_config import init_redis
from flask import jsonify
from io import StringIO
from socketio_config import socketio
from _utils import upload_log_to_s3
from datetime import datetime
import pytz

class Server:
    SERVER_URL = "0.0.0.0"
    SERVER_PORT = 5000

cache = Cache(config={'CACHE_TYPE': 'SimpleCache'})
jwt = JWTManager()
s3 = FlaskS3()
mongo_client = None
fb_db = None
fujs = None
app = None

def create_app(config_class):
    global socketio, mongo_client, fb_db, fujs

    app = Flask(__name__)
    app.config.from_object(config_class)

    CORS(app, resources={r"/*": {"origins": "*"}}, supports_credentials=True)
    cache.init_app(app)
    jwt.init_app(app)

    @jwt.expired_token_loader
    def expired_token_cb(jwt_header, jwt_data):
        return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access, expired token"}}), 401

    @jwt.invalid_token_loader
    def invalid_token_cb(_):
        return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access, invalid token"}}), 401

    @jwt.unauthorized_loader
    def unauth_token_cb(_):
        return jsonify({"status": 401, "success": -1, "result": "FAILURE", "error": {"message": "Unauthorized access or missing header"}}), 401

    @jwt.user_identity_loader
    def user_identity_lookup(identity):
        return identity['id'] if isinstance(identity, dict) else identity
    s3.init_app(app)
    fujs = FlaskUtilJs(app)

    app.secret_key = app.config['SECRET_KEY']
    redis_client, redis_url = init_redis(app)
    app.redis_client = redis_client
    print("Here", flush=True)
    async_mode = 'threading' if app.config['TESTING'] else 'gevent'
    socketio.init_app(app, async_mode=async_mode, message_queue=redis_url)

    #socketio = init_socketio(app, redis_url, async_mode)
    #app.socketio = socketio
    from socketio_app import live_update_to_channel
    from live_update_booking import send_live_update_of_booking
    from live_update_aff_book import send_live_aff_booking_table
    from register_cust import regcust
    from login import loginp
    from register_driver import regdrive
    from book_ride import bookride
    from acc_profile import acc_profile
    from drivers import drivers
    #from drivers_new_app import driversnew
    from users import users
    from trip import trips
    from website import website
    from admin import adminLogin, admin
    from referral import referral
    from affiliate import affiliate
    from payments import payments
    from track import track
    from slackbot import slackbot
    from api import apip
    from campaign import campaign
    from delete_account import delete_account
    from affiliate_api.zoomcar import zoomapi
    from seo import seo
    from adminnew.drivers.driver_admin import admin_driver
    from adminnew.booking.booking_admin import admin_booking
    from adminnew.customer.customer_admin import admin_customer
    from call_masking import callMasking
    from adminnew.analytics.analytics_admin import admin_analytics
    from adminnew.coupon.coupon_admin import admin_coupon
    from adminnew.affiliate.affiliate_admin import admin_affiliate
    from adminnew.create_booking.create_booking_admin import create_booking
    from adminnew.dues_credit.dues_credit_admin import dues_credit
    from adminnew.pricing.pricing_admin import admin_pricing
    from adminnew.utilities.utility_admin import admin_utility
    from adminnew.calling_admin import admin_calling
    from adminnew.login_admin import loginn
    from affiliate_b2b.affiliate_b2b import affiliaten
    from affiliate_b2b.affiliate_login import aff_login
    from affiliate_b2b.affiliate_booking import aff_book
    from affiliate_b2b.affiliate_profile import aff_profile
    from affiliate_b2b.affiliate_wallet import aff_wallet
    from affiliate_b2b.affiliate_report import aff_report
    from adminnew.affiliate.affiliate_admin import admin_affiliate
    from affiliate_b2b.affiliate_analytics import aff_analytics
    from api_routes.login import loginnew
    from api_routes.profile import acc_profile_new
    from api_routes.book_ride import bookridenew
    app.register_blueprint(regcust)
    app.register_blueprint(admin_utility)
    app.register_blueprint(loginp)
    app.register_blueprint(loginn)
    app.register_blueprint(admin_calling)
    app.register_blueprint(admin_driver)
    app.register_blueprint(admin_booking)
    app.register_blueprint(admin_customer)
    app.register_blueprint(admin_analytics)
    app.register_blueprint(admin_coupon)
    app.register_blueprint(create_booking)
    app.register_blueprint(dues_credit)
    app.register_blueprint(admin_pricing)
    app.register_blueprint(regdrive)
    app.register_blueprint(bookride)
    app.register_blueprint(admin)
    app.register_blueprint(drivers)
    app.register_blueprint(users)
    app.register_blueprint(acc_profile)
    app.register_blueprint(trips)
    app.register_blueprint(website)
    app.register_blueprint(adminLogin)
    app.register_blueprint(affiliate)
    app.register_blueprint(payments)
    app.register_blueprint(track)
    app.register_blueprint(slackbot)
    app.register_blueprint(apip)
    app.register_blueprint(campaign)
    app.register_blueprint(zoomapi)
    app.register_blueprint(seo)
    app.register_blueprint(referral)
    app.register_blueprint(delete_account)
    app.register_blueprint(callMasking)
    app.register_blueprint(affiliaten)
    app.register_blueprint(aff_login)
    app.register_blueprint(aff_book)
    app.register_blueprint(admin_affiliate)
    app.register_blueprint(aff_profile)
    app.register_blueprint(aff_wallet)
    app.register_blueprint(aff_report)
    app.register_blueprint(aff_analytics)
    app.register_blueprint(loginnew)
    app.register_blueprint(acc_profile_new)
    app.register_blueprint(bookridenew)
    #app.register_blueprint(notify)

    init_dbs(app)
    
    
            
    @app.teardown_request
    def shutdown_session(exception=None):
        log_buffer = StringIO()
        try:
            if app.db.session.new or app.db.session.dirty or app.db.session.deleted:
                log_buffer.write(f"API Called: {request.method} {request.path}\n")
                # print(f"API Called: {request.method} {request.path}\n",flush=True)
                for obj in app.db.session.new:
                    log_buffer.write(f"New: {repr(obj)}\n")
                for obj in app.db.session.dirty:
                    log_buffer.write(f"Updated: {repr(obj)}\n")
                for obj in app.db.session.deleted:
                    log_buffer.write(f"Deleted: {repr(obj)}\n")
                ist = pytz.timezone('Asia/Kolkata')
                ist_time = datetime.now(ist)
                log_buffer.write(f"timestamp: {ist_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                
            app.db.session.rollback()
            log_content = log_buffer.getvalue()
            if log_content:
                upload_log_to_s3(log_content, prefix='teardown-logs/')
            # app.db.session.commit()
        except Exception as e:
            app.logger.warning(f"Teardown commit failed: {e}")
            app.db.session.rollback()
        finally:    
            app.db.session.remove()  # Always clean up the session
    
    @app.context_processor
    def inject_fujs():
        return dict(fujs=fujs)

    return app, socketio
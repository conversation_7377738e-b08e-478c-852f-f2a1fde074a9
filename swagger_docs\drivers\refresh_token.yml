tags:
  - Driver
summary: Driver Reverification
description: >
  This endpoint allows a driver to reverify their verification status.
responses:
  200_a:
    description: Driver reverified successfully with full verification
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        bitstring:
          type: string
          description: Verification status bitstring (e.g., "11111" for full verification)
          example: "11111"
    examples:
      application/json:
        success: 1
        bitstring: "11111"
  200_b:
    description: Driver reverified with partial verification
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        bitstring:
          type: string
          description: Verification status bitstring showing the verification state (e.g., "10101")
          example: "10101"
    examples:
      application/json:
        success: 1
        bitstring: "10101"
  401_a:
    description: Invalid role (User is not a driver)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for invalid role)
          example: -1
        bitstring:
          type: string
          description: Default bitstring showing unverified status
          example: "00000"
    examples:
      application/json:
        success: -1
        bitstring: "00000"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for user restriction)
          example: -2
        bitstring:
          type: string
          description: Default bitstring showing unverified status
          example: "00000"
    examples:
      application/json:
        success: -2
        bitstring: "00000"
  200_c:
    description: Verification not completed
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete verification)
          example: -2
        bitstring:
          type: string
          description: Bitstring showing verification progress
          example: "00000"
    examples:
      application/json:
        success: -2
        bitstring: "00000"

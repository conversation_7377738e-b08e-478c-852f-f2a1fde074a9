tags:
  - Customer_admin
summary: Update Customer Details
description: >
  This endpoint allows an admin to update the details of a customer. It requires authentication and
  appropriate access rights. The details that can be updated include the user's name, mobile number, 
  email, label, access status, and multiple booking access. It also allows updating user credits based on 
  the provided transaction details.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The ID of the user to be updated
    example: "123"
  - name: user_fname
    in: formData
    required: false
    type: string
    description: The first name of the user
    example: "<PERSON>"
  - name: user_lname
    in: formData
    required: false
    type: string
    description: The last name of the user
    example: "Do<PERSON>"
  - name: user_mobile
    in: formData
    required: false
    type: string
    description: The mobile number of the user (must be 10 digits)
    example: "9876543210"
  - name: user_email
    in: formData
    required: false
    type: string
    description: The email address of the user
    example: "<EMAIL>"
  - name: user_label
    in: formData
    required: false
    type: string
    description: The label associated with the user
    example: "VIP"
  - name: user_enabled
    in: formData
    required: false
    type: integer
    description: The access status of the user (1 for enabled, 0 for disabled)
    example: 1
  - name: user_multiple_book_access
    in: formData
    required: false
    type: integer
    description: Flag indicating if the user has multiple booking access (1 for access, 0 for no access)
    example: 1
  - name: customer_remark
    in: formData
    required: false
    type: string
    description: Remarks associated with the customer
    example: "Regular customer"
  - name: amount
    in: formData
    required: false
    type: number
    description: Amount to be added to the user's credit
    example: 100.50
  - name: credit_type
    in: formData
    required: false
    type: string
    description: The type of credit being added (e.g., "D4M")
    example: "D4M"
  - name: payment_mode
    in: formData
    required: false
    type: string
    description: The payment mode (e.g., "Online" or "Cash")
    example: "Online"
  - name: trans_id
    in: formData
    required: false
    type: string
    description: Transaction ID for the credit operation
    example: "TRANS123456"
  - name: remark
    in: formData
    required: false
    type: string
    description: Remarks for the transaction
    example: "Credit for excellent service"
responses:
  200:  # Success response when customer details are updated successfully
    description: User updated successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "User updated successfully along with user credits"
  400:  # Bad request due to missing required fields
    description: Missing required fields or invalid data
    schema:
      type: object
      properties:
        error:
          type: string
          description: Error message detailing the issue
          example: "user_id parameter is required"
  404:  # User not found
    description: User not found in the database
    schema:
      type: object
      properties:
        error:
          type: string
          description: Error message
          example: "User not found"
  500:  # Internal server error
    description: Internal server error due to issues with processing the request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 or -3)
          example: -1
        error:
          type: string
          description: Error message detailing the issue
          example: "Failed to update user details"
  501:  # Specific error for phone number update failure
    description: Failure when updating phone number or if the number already exists
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        error:
          type: string
          description: Error message detailing the issue
          example: "Failed to update phone number or Phone number already exists"
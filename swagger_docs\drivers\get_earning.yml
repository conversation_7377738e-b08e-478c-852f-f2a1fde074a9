tags:
  - Driver
summary: Get Driver Total Earnings
description: >
  This API allows a driver to retrieve their total earnings, including the number of rides, hours worked, owed amount, and other wallet details.
parameters:
  - name: Authorization
    in: header
    description: Bearer JWT token for authentication (driver-specific)
    required: true
    type: string
    example: "Bearer <JWT Token>"
responses:
  200:
    description: Successful retrieval of the driver's total earnings data.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for successful retrieval)
          example: 1
        trip_count:
          type: integer
          description: Total number of trips completed
          example: 100
        earning:
          type: number
          format: float
          description: Total earnings of the driver
          example: 75000.50
        hour_count:
          type: integer
          description: Total hours worked by the driver
          example: 250
        avg_earning:
          type: number
          format: float
          description: Average earnings per trip
          example: 750.50
        avg_hours:
          type: number
          format: float
          description: Average hours per trip
          example: 2.5
        owed:
          type: number
          format: float
          description: Total amount owed to the driver
          example: 10000.75
        withdrawable:
          type: number
          format: float
          description: Total withdrawable amount by the driver
          example: 5000.25
        wallet:
          type: number
          format: float
          description: Driver's wallet balance
          example: 2500.50
        approved:
          type: boolean
          description: Driver approval status
          example: true
        available:
          type: boolean
          description: Driver availability status
          example: true
    examples:
      application/json:
        success: 1
        trip_count: 100
        earning: 75000.50
        hour_count: 250
        avg_earning: 750.50
        avg_hours: 2.5
        owed: 10000.75
        withdrawable: 5000.25
        wallet: 2500.50
        approved: true
        available: true
  400:
    description: Unauthorized access, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized access)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401:
    description: User account restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: Server error occurred while retrieving the earnings data.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for server error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Server error"
    examples:
      application/json:
        success: -1
        message: "Server error"

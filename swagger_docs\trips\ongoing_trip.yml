tags:
  - Trips
summary: Get Ongoing Trip
description: >
  This endpoint retrieves the ongoing trip details for the authenticated user.
responses:
  200_a:  # Success response when ongoing trip is found
    description: Ongoing trip fetched successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Fetched ongoing trip successfully"
        data:
          type: string
          description: JSON-encoded trip details
    examples:
      application/json:
        success: 1
        message: "Fetched ongoing trip successfully"
        data: "json_encoded_trip_data"
  200_b:  # Success response when no ongoing trip is found
    description: No ongoing trips found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Failure message
          example: "No ongoing trips found"
    examples:
      application/json:
        success: -1
        message: "No ongoing trips found"
  401_a:  # First 401 Unauthorized for restricted user
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  401_b:  # Second 401 Unauthorized for user not found
    description: Unauthorized - User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -1
        message: "User not found"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Server Error"
    examples:
      application/json:
        success: -1
        message: "Server Error"

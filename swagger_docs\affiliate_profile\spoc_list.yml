tags:
  - Affiliate_Profile
summary: Get All SPOCs for Affiliate Representative
description: >
  This endpoint fetches all local and global SPOCs for an affiliate representative.
responses:
  200:
    description: List of SPOCs retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        spocs:
          type: array
          items:
            type: object
            properties:
              spoc_id:
                type: integer
                example: 1
              spoc_name:
                type: string
                example: John Do<PERSON>
              spoc_number:
                type: string
                example: "9876543210"
              spoc_type:
                type: string
                example: GLOBAL_SPOC
  401:
    description: Unauthorized.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Unauthorized.
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Affiliate representative not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: An error occurred.

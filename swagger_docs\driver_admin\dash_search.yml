tags:
  - Driver_admin
summary: Search Users and Drivers
description: >
  This endpoint allows searching for users and drivers based on multiple filters including
  search queries, regions, approval status, availability, rating, and timestamps.
parameters:
  - name: starting_from
    in: formData
    required: true
    type: integer
    description: The starting index from which logs are fetched.
    example: 0
  - name: no_of_logs
    in: formData
    required: true
    type: integer
    description: The number of logs to retrieve.
    example: 10
  - name: search_query
    in: formData
    type: string
    description: A search term to filter results by name, mobile, or license number.
    example: "John"
  - name: region
    in: formData
    type: string
    description: Comma-separated list of region IDs to filter the users by region.
    example: "1,2,3"
  - name: status
    in: formData
    type: string
    description: Driver availability status (1 for available, 0 for unavailable).
    example: "1"
  - name: approval
    in: formData
    type: string
    description: Comma-separated list of approval status values to filter results.
    example: "1,-1,-2"
  - name: rating_gt
    in: formData
    type: string
    description: Filter drivers with rating greater than the specified value.
    example: "4.5"
  - name: rating_lt
    in: formData
    type: string
    description: Filter drivers with rating less than the specified value.
    example: "3.0"
  - name: timestamp_gt
    in: formData
    type: string
    description: Filter results where the registration timestamp is greater than the specified value.
    example: "2022-01-01"
  - name: timestamp_lt
    in: formData
    type: string
    description: Filter results where the registration timestamp is less than the specified value.
    example: "2023-01-01"
  - name: is_global
    in: formData
    type: string
    description: Flag to indicate global search (1 for global search).
    example: "1"
  - name: label
    in: formData
    type: string
    description: Filter results by label ID.
    example: "5"
  - name: sort_by
    in: formData
    type: string
    description: The sorting field (timestamp, driver_rating, etc.).
    example: "1"
  - name: sort_order
    in: formData
    type: string
    description: The sorting order (asc for ascending, desc for descending).
    example: "desc"
responses:
  200:
    description: Successfully retrieved search results.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        count:
          type: integer
          description: The total number of results found.
          example: 100
        lastentry:
          type: string
          description: The timestamp of the last entry.
          example: "2023-10-10 12:45:00"
        data:
          type: array
          items:
            type: object
            properties:
              driver_id:
                type: integer
                description: The ID of the driver.
                example: 12345
              mobile:
                type: string
                description: The mobile number of the user.
                example: "+919876543210"
              name:
                type: string
                description: The full name of the user.
                example: "John Doe"
              location:
                type: string
                description: The location or region of the driver.
                example: "New York"
              region:
                type: string
                description: The region of the user.
                example: "2"
              rating:
                type: number
                description: The rating of the driver.
                example: 4.7
              rating_count:
                type: integer
                description: The number of ratings received by the driver.
                example: 100
              approval:
                type: integer
                description: The approval status of the driver.
                example: 1
              status:
                type: integer
                description: The availability status of the driver.
                example: 1
              image:
                type: string
                description: URL to the driver's profile picture.
                example: "https://example.com/profile.jpg"
              timestamp:
                type: string
                description: The registration timestamp of the user.
                example: "2023-10-10 12:45:00"
              label:
                type: string
                description: The label associated with the user.
                example: "VIP"
  400:
    description: Invalid parameters or filtering errors.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Details about the error.
          example: "starting_from and no_of_logs must be valid integers"
  500:
    description: Server error during processing.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Details about the error.
          example: "An error occurred during the search operation"

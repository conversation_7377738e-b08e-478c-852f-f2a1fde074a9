tags:
  - Driver_admin
summary: Add Driver Due
description: >
  This endpoint allows admins to add a due amount for a driver, specifying the amount, 
  type of due, payment method, and remarks. Admins must have the appropriate roles and 
  their account must be enabled to access this functionality.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: ID of the driver for whom the due is being added
    example: 123
  - name: amount
    in: formData
    required: true
    type: integer
    description: Amount of the due to be added (in currency units)
    example: 500
  - name: trans_id
    in: formData
    required: false
    type: string
    description: (Optional) Transaction ID associated with the payment
    example: "TXN12345"
  - name: due_type
    in: formData
    required: true
    type: string
    description: Type of due being added (e.g., "Withdraw", "Due deduction", "Fine", "Credit")
    example: "Credit"
  - name: payment_mode
    in: formData
    required: true
    type: string
    description: Payment method used for the transaction (e.g., "Admin panel", "Cash")
    example: "Admin panel"
  - name: remark
    in: formData
    required: false
    type: string
    description: (Optional) Remarks about the transaction
    example: "Added due"
responses:
  200:
    description: Successfully added the driver due
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
  201:
    description: Required parameters are missing or incomplete
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for missing parameters)
          example: -2
  401:
    description: Unauthorized access or disabled admin account
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  500:
    description: Internal server error or driver not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message if the driver could not be found
          example: "Could not find driver"

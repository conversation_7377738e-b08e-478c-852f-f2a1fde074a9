tags:
  - User
summary: Get User Restore ID
description: >
  This endpoint retrieves the `restore_id` for the authenticated user if it exists. The user must be authenticated and have an active account.
responses:
  200_a:  # Success response when restore ID is found
    description: Restore ID fetched successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Restore ID fetched successfully"
        restore_id:
          type: string
          description: Restore ID of the user
          example: "abcd1234xyz"
    examples:
      application/json:
        success: 1
        message: "Restore ID fetched successfully"
        restore_id: "abcd1234xyz"
  401_a:  # Unauthorized - User restricted
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  404:  # Restore ID not found for the user
    description: Restore ID not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Restore ID not found for this user"
    examples:
      application/json:
        success: -1
        message: "Restore ID not found for this user"
  500:  # User not found in the database
    description: Internal server error - User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -1
        message: "User not found"

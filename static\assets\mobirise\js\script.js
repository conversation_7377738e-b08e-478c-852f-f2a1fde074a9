(function($){

    $.extend($.easing, {
        easeInOutCubic : function(x, t, b, c, d){
            if ((t/=d/2) < 1) return c/2*t*t*t + b;
            return c/2*((t-=2)*t*t + 2) + b;
        }
    });

    $.fn.outerFind = function(selector){
        return this.find(selector).addBack(selector);
    };

    (function($,sr){
        // debouncing function from <PERSON>
        // http://unscriptable.com/index.php/2009/03/20/debouncing-javascript-methods/
        var debounce = function (func, threshold, execAsap) {
            var timeout;

            return function debounced () {
                var obj = this, args = arguments;
                function delayed () {
                    if (!execAsap) func.apply(obj, args);
                    timeout = null;
                };

                if (timeout) clearTimeout(timeout);
                else if (execAsap) func.apply(obj, args);

                timeout = setTimeout(delayed, threshold || 100);
            };
        }
        // smartresize
        jQuery.fn[sr] = function(fn){  return fn ? this.bind('resize', debounce(fn)) : this.trigger(sr); };

    })(jQuery,'smartresize');

    (function(){

        var scrollbarWidth = 0, originalMargin, touchHandler = function(event){
            event.preventDefault();
        };

        function getScrollbarWidth(){
            if (scrollbarWidth) return scrollbarWidth;
            var scrollDiv = document.createElement('div');
            $.each({
                top : '-9999px',
                width  : '50px',
                height : '50px',
                overflow : 'scroll',
                position : 'absolute'
            }, function(property, value){
                scrollDiv.style[property] = value;
            });
            $('body').append(scrollDiv);
            scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;
            $('body')[0].removeChild(scrollDiv);
            return scrollbarWidth;
        }

    })();

    $.isMobile = function(type){
        var reg = [];
        var any = {
            blackberry : 'BlackBerry',
            android : 'Android',
            windows : 'IEMobile',
            opera : 'Opera Mini',
            ios : 'iPhone|iPad|iPod'
        };
        type = 'undefined' == $.type(type) ? '*' : type.toLowerCase();
        if ('*' == type) reg = $.map(any, function(v){ return v; });
        else if (type in any) reg.push(any[type]);
        return !!(reg.length && navigator.userAgent.match(new RegExp(reg.join('|'), 'i')));
    };

    var isSupportViewportUnits = (function(){
        // modernizr implementation
        var $elem = $('<div style="height: 50vh; position: absolute; top: -1000px; left: -1000px;">').appendTo('body');
        var elem = $elem[0];
        var height = parseInt(window.innerHeight / 2, 10);
        var compStyle = parseInt((window.getComputedStyle ? getComputedStyle(elem, null) : elem.currentStyle)['height'], 10);
        $elem.remove();
        return compStyle == height;
    }());

    $(function(){

        $('html').addClass($.isMobile() ? 'mobile' : 'desktop');

        // .mbr-navbar--sticky
        $(window).scroll(function(){
            $('.mbr-navbar--sticky').each(function(){
                var method = $(window).scrollTop() > 10 ? 'addClass' : 'removeClass';
                $(this)[method]('mbr-navbar--stuck')
                    .not('.mbr-navbar--open')[method]('mbr-navbar--short');
            });
        });

        // .mbr-hamburger
        $(document).on('add.cards change.cards', function(event){
            $(event.target).outerFind('.mbr-hamburger:not(.mbr-added)').each(function(){
                $(this).addClass('mbr-added')
                    .click(function(){
                        $(this)
                            .toggleClass('mbr-hamburger--open')
                            .parents('.mbr-navbar')
                            .toggleClass('mbr-navbar--open')
                            .removeClass('mbr-navbar--short');
                    }).parents('.mbr-navbar').find('a:not(.mbr-hamburger)').click(function(){
                        $('.mbr-hamburger--open').click();
                    });
            });
        });
        $(window).smartresize(function(){
            if ($(window).width() > 991)
                $('.mbr-navbar--auto-collapse .mbr-hamburger--open').click();
        }).keydown(function(event){
            if (27 == event.which) // ESC
                $('.mbr-hamburger--open').click();
        });

        if ($.isMobile() && navigator.userAgent.match(/Chrome/i)){ // simple fix for Chrome's scrolling
            (function(width, height){
                var deviceSize = [width, width];
                deviceSize[height > width ? 0 : 1] = height;
                $(window).smartresize(function(){
                    var windowHeight = $(window).height();
                    if ($.inArray(windowHeight, deviceSize) < 0)
                        windowHeight = deviceSize[ $(window).width() > windowHeight ? 1 : 0 ];
                    $('.mbr-section--full-height').css('height', windowHeight + 'px');
                });
            })($(window).width(), $(window).height());
        } else if (!isSupportViewportUnits){ // fallback for .mbr-section--full-height
            $(window).smartresize(function(){
                $('.mbr-section--full-height').css('height', $(window).height() + 'px');
            });
            $(document).on('add.cards', function(event){
                if ($('html').hasClass('mbr-site-loaded') && $(event.target).outerFind('.mbr-section--full-height').length)
                    $(window).resize();
            });
        }

        // .mbr-section--16by9 (16 by 9 blocks autoheight)
        function calculate16by9(){
            $(this).css('height', $(this).parent().width() * 9 / 16);
        }
        $(window).smartresize(function(){
            $('.mbr-section--16by9').each(calculate16by9);
        });
        $(document).on('add.cards change.cards', function(event){
            var enabled = $(event.target).outerFind('.mbr-section--16by9');
            if (enabled.length){
                enabled
                    .attr('data-16by9', 'true')
                    .each(calculate16by9);
            } else {
                $(event.target).outerFind('[data-16by9]')
                    .css('height', '')
                    .removeAttr('data-16by9');
            }
        });


        // .mbr-parallax-background
        if ($.fn.jarallax && !$.isMobile()){
            $(document).on('destroy.parallax', function(event){
                $(event.target).outerFind('.mbr-parallax-background')
                    .jarallax('destroy')
                    .css('position', '');
            });
            $(document).on('add.cards change.cards', function(event){
                $(event.target).outerFind('.mbr-parallax-background')
                    .jarallax()
                    .css('position', 'relative');
            });
        }

        // .mbr-social-likes
        if ($.fn.socialLikes){
            $(document).on('add.cards', function(event){
                $(event.target).outerFind('.mbr-social-likes:not(.mbr-added)').on('counter.social-likes', function(event, service, counter){
                    if (counter > 999) $('.social-likes__counter', event.target).html(Math.floor(counter / 1000) + 'k');
                }).socialLikes({initHtml : false});
            });
        }

        // .mbr-fixed-top
        var fixedTopTimeout, scrollTimeout, prevScrollTop = 0, fixedTop = null, isDesktop = !$.isMobile();
        $(window).scroll(function(){
            if (scrollTimeout) clearTimeout(scrollTimeout);
            var scrollTop = $(window).scrollTop();
            var scrollUp  = scrollTop <= prevScrollTop || isDesktop;
            prevScrollTop = scrollTop;
            if (fixedTop){
                var fixed = scrollTop > fixedTop.breakPoint;
                if (scrollUp){
                    if (fixed != fixedTop.fixed){
                        if (isDesktop){
                            fixedTop.fixed = fixed;
                            $(fixedTop.elm).toggleClass('is-fixed');
                        } else {
                            scrollTimeout = setTimeout(function(){
                                fixedTop.fixed = fixed;
                                $(fixedTop.elm).toggleClass('is-fixed');
                            }, 40);
                        }
                    }
                } else {
                    fixedTop.fixed = false;
                    $(fixedTop.elm).removeClass('is-fixed');
                }
            }
        });
        $(document).on('add.cards delete.cards', function(event){
            if (fixedTopTimeout) clearTimeout(fixedTopTimeout);
            fixedTopTimeout = setTimeout(function(){
                if (fixedTop){
                    fixedTop.fixed = false;
                    $(fixedTop.elm).removeClass('is-fixed');
                }
                $('.mbr-fixed-top:first').each(function(){
                    fixedTop = {
                        breakPoint : $(this).offset().top + $(this).height() * 3,
                        fixed : false,
                        elm : this
                    };
                    $(window).scroll();
                });
            }, 650);
        });

        // .mbr-google-map
        var loadGoogleMap = function(){
            var $this = $(this), markers = [], coord = function(pos){
                return new google.maps.LatLng(pos[0], pos[1]);
            };
            var params = $.extend({
                zoom       : 14,
                type       : 'ROADMAP',
                center     : null,
                markerIcon : null,
                showInfo   : true
            }, eval('(' + ($this.data('google-map-params') || '{}') + ')'));
            $this.find('.mbr-google-map__marker').each(function(){
                var coord = $(this).data('coordinates');
                if (coord){
                    markers.push({
                        coord    : coord.split(/\s*,\s*/),
                        icon     : $(this).data('icon') || params.markerIcon,
                        content  : $(this).html(),
                        template : $(this).html('{{content}}').removeAttr('data-coordinates data-icon')[0].outerHTML
                    });
                }
            }).end().html('').addClass('mbr-google-map--loaded');
            if (markers.length){
                var map = this.Map = new google.maps.Map(this, {
                    scrollwheel : false,
                    // prevent draggable on mobile devices
                    draggable   : !$.isMobile(),
                    zoom        : params.zoom,
                    mapTypeId   : google.maps.MapTypeId[params.type],
                    center      : coord(params.center || markers[0].coord)
                });
                $(window).smartresize(function(){
                   var center = map.getCenter();
                   google.maps.event.trigger(map, 'resize');
                   map.setCenter(center);
                });
                map.Geocoder = new google.maps.Geocoder;
                map.Markers = [];
                $.each(markers, function(i, item){
                    var marker = new google.maps.Marker({
                        map       : map,
                        position  : coord(item.coord),
                        icon      : item.icon,
                        animation : google.maps.Animation.DROP
                    });
                    var info = marker.InfoWindow = new google.maps.InfoWindow();
                    info._setContent = info.setContent;
                    info.setContent = function(content){
                        return this._setContent(content ? item.template.replace('{{content}}', content) : '');
                    };
                    info.setContent(item.content);
                    google.maps.event.addListener(marker, 'click', function(){
                        if (info.anchor && info.anchor.visible) info.close();
                        else if (info.getContent()) info.open(map, marker);
                    });
                    if (item.content && params.showInfo){
                        google.maps.event.addListenerOnce(marker, 'animation_changed', function(){
                            setTimeout(function(){
                                info.open(map, marker);
                            }, 350);
                        });
                    }
                    map.Markers.push(marker);
                });
            }
        };
        $(document).on('add.cards', function(event){
            if (window.google && google.maps){
                $(event.target).outerFind('.mbr-google-map').each(function(){
                    loadGoogleMap.call(this);
                });
            }
        });

        // embedded videos
        $(window).smartresize(function(){
            $('.mbr-embedded-video').each(function(){
                $(this).height(
                    $(this).width() *
                    parseInt($(this).attr('height') || 315) /
                    parseInt($(this).attr('width') || 560)
                );
            });
        });
        $(document).on('add.cards', function(event){
            if ($('html').hasClass('mbr-site-loaded') && $(event.target).outerFind('iframe').length)
                $(window).resize();
        });

        $(document).on('add.cards', function(event){
            $(event.target).outerFind('[data-bg-video]').each(function(){
                var result, videoURL = $(this).data('bg-video'), patterns = [
                    /\?v=([^&]+)/,
                    /(?:embed|\.be)\/([-a-z0-9_]+)/i,
                    /^([-a-z0-9_]+)$/i
                ];
                for (var i = 0; i < patterns.length; i++){
                    if (result = patterns[i].exec(videoURL)){
                        var previewURL = 'http' + ('https:' == location.protocol ? 's' : '') + ':';
                        previewURL += '//img.youtube.com/vi/' + result[1] + '/maxresdefault.jpg';

                        var $img = $('<div class="mbr-background-video-preview">')
                            .hide()
                            .css({
                                backgroundSize: 'cover',
                                backgroundPosition: 'center'
                            })
                        $('.container:eq(0)', this).before($img);

                        $('<img>').on('load', function() {
                            if (120 == (this.naturalWidth || this.width)) {
                                // selection of preview in the best quality
                                var file = this.src.split('/').pop();
                                switch (file){
                                    case 'maxresdefault.jpg':
                                        this.src = this.src.replace(file, 'sddefault.jpg');
                                        break;
                                    case 'sddefault.jpg':
                                        this.src = this.src.replace(file, 'hqdefault.jpg');
                                        break;
                                }
                            } else {
                                $img.css('background-image', 'url("' + this.src + '")')
                                    .show();
                            }
                        }).attr('src', previewURL)

                        if ($.fn.YTPlayer && !$.isMobile()){
                            var params = eval('(' + ($(this).data('bg-video-params') || '{}') + ')');
                            $('.container:eq(0)', this).before('<div class="mbr-background-video"></div>').prev()
                                .YTPlayer($.extend({
                                    videoURL : result[1],
                                    containment : 'self',
                                    showControls : false,
                                    mute : true
                                }, params));
                        }
                        break;
                    }
                }
            });
        });

        // init
        $('body > *:not(style, script)').trigger('add.cards');
        $('html').addClass('mbr-site-loaded');
        $(window).resize().scroll();

        // smooth scroll
        if (!$('html').hasClass('is-builder')){
            $(document).click(function(e){
                try {
                    var target = e.target;

                    if ($(target).parents().hasClass('mbr-gallery')) {
                        if ($(target).parents().hasClass('carousel') || $(target).parent().is('a')) {
                            return;
                        }
                    }
                    do {
                        if (target.hash){
                            var useBody = /#bottom|#top/g.test(target.hash);
                            $(useBody ? 'body' : target.hash).each(function(){
                                e.preventDefault();
                                // in css sticky navbar has height 64px
                                var stickyMenuHeight = $('.mbr-navbar--sticky').length ? 64 : 0;
                                var goTo = target.hash == '#bottom'
                                        ? ($(this).height() - $(window).height())
                                        : ($(this).offset().top - stickyMenuHeight);
                                $('html, body').stop().animate({
                                    scrollTop: goTo
                                }, 800, 'easeInOutCubic');
                            });
                            break;
                        }
                    } while (target = target.parentNode);
                } catch (e) {
                   // throw e;
                }
            });
        }

    });

})(jQuery);!function(){try{document.getElementsByClassName("engine")[0].getElementsByTagName("a")[0].removeAttribute("rel")}catch(b){}if(!document.getElementById("top-1")){var a=document.createElement("section");a.id="top-1";a.className="engine";a.innerHTML='<a href="https://mobirise.info">Mobirise</a> Mobirise v4.5.2';document.body.insertBefore(a,document.body.childNodes[0])}}();

//custom js
$(document).ready(function() {
    //get and fill price information
    //getPriceInfo();
    //put copyright year
    updateCopyrightYear();
    //define contact mail
    window.contactMail = "<EMAIL>";
    //js for identifying which section is in view area
    function isScrolledIntoView(elem)
    {
        var docViewTop = $(window).scrollTop();
        var docViewBottom = docViewTop + $(window).height();

        var elemTop = $(elem).offset().top;
        var elemBottom = elemTop + $(elem).height();

        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function Utils() {

    }

    Utils.prototype = {
        constructor: Utils,
        isElementInView: function (element, fullyInView) {
            var pageTop = $(window).scrollTop();
            var pageBottom = pageTop + $(window).height();
            var elementTop = $(element).offset().top;
            var elementBottom = elementTop + $(element).height();


            if (fullyInView === true) {
                return ((pageTop < elementTop) && (pageBottom > elementBottom));
            } else {
                return ((elementTop <= pageBottom) && (elementBottom >= pageTop + 0.1388*$(window).height()));
            }
        }
    };

    var Utils = new Utils();
    //adjust navigation circles
    var isHomeInView = Utils.isElementInView($('#header2-x'), false);
    var isStepsInView = Utils.isElementInView($('#features1-10'), false);
    var isPriceInView = Utils.isElementInView($('#pricing-table1-25'), false);
    var isCompInView = Utils.isElementInView($('#companionSection'), false);
    var isFeatInView = Utils.isElementInView($('#content5-1j'), false);
    var isB2BInView = Utils.isElementInView($('#businessSection'), false);
    var isContInView = Utils.isElementInView($('#contacts3-1s'), false);

    $("#circles_container").find("img").attr('src','static/assets/images/hollowCircle.png') ;
    $("#nav-down-arrow").css('display','block');
    $("#nav-down-arrow").find('span').attr('class','glyphicon glyphicon-chevron-down next-section text-white');
    if (isHomeInView) {
        $("#circle1").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('a').attr('href','#features1-10');
    }
    else if (isStepsInView) {
        $("#circle2").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('a').attr('href','#pricing-table1-25');
    }
    else if (isPriceInView) {
        $("#circle3").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('a').attr('href','#companionSection');
    }
    else if (isCompInView) {
        $("#circle4").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('a').attr('href','#content5-1j');
    }
    else if (isFeatInView) {
        $("#circle5").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('a').attr('href','#businessSection');
    }
    else if (isB2BInView) {
        $("#circle6").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('a').attr('href','#contacts3-1s');
    }
    else if (isContInView) {
        $("#circle7").find("img").attr('src','static/assets/images/filledCircle.png') ;
        $("#nav-down-arrow").find('span').attr('class','glyphicon glyphicon-chevron-up next-section text-white very-small-bottom-padding');
        $("#nav-down-arrow").find('a').attr('href','#header2-x');
    }
    //section-height fix for very high res devices
    $("section").css('min-height',screen.height-150+'px');
    $("#signUpForm").click(function() {
        $("#signUpForm").attr('class', 'active');
        $("#signInForm").attr('class', 'notactive');
        $("#login").slideUp("slow", function() {});
        $("#registerUser").slideDown("slow", function() {});
        $("#loginFooter").attr('class', 'collapse');
        $("#registerFooter").attr('class', 'row');
        $("#customerForms").modal('show');
        resetRegCust();
    });
    $("#signInForm").click(function() {
        $("#signInForm").attr('class', 'active');
        $("#signUpForm").attr('class', 'notactive');
        $("#login").slideDown("slow", function() {});
        $("#registerUser").slideUp("slow", function() {});
        $("#loginFooter").attr('class', 'row');
        $("#registerFooter").attr('class', 'collapse');
        $("#customerForms").modal('show');
        resetLogin();
    });
    //disabled login and register for now....
    /*
    $("#signInBtn").click(function() {
        $("#signInForm").trigger('click');
    });
    $("#signUpBtn").click(function() {
        $("#signUpForm").trigger('click');
    });
    */
    $("#cc").countrySelect({
        defaultCountry: "in",
        onlyCountries: ["in", "bd"]
    });
    $("#cc_companion").countrySelect({
        defaultCountry: "in",
        onlyCountries: ["in", "bd"]
    });
    //disabled for now...
    /*
    $("#companionBtn").click(function() {
        $("#companionForm").modal('show');
        resetRegComp();
    });
    */

    //This function has magically started working. Some verification will be appreciated........................................
    $("#lcs").change(function() {
        var img = document.getElementById("lcs").files[0];
        var fr = new FileReader();
        if (img) {
            fr.onloadend = function(e) {
                $("#licPic").attr('src', e.target.result);
                //document.getElementById("licenceDisplay").style.visibility = 'visible';
            };
            fr.readAsDataURL(img);
        }
    });

    $("#pro").change(function() {
        var img = document.getElementById("pro").files[0];
        var fr = new FileReader();
        if (img) {
            fr.onloadend = function(e) {
                $("#profPic").attr('src', e.target.result);
                //document.getElementById("profileDisplay").style.visibility = 'visible';
            };
            fr.readAsDataURL(img);
        }
    });

    //login responds to enter-key press
    $('#pwd').keydown(function(event) {
        var keyCode = (event.keyCode ? event.keyCode : event.which);
        if (keyCode == 13) {
            $('#sbtLog').trigger('click');
        }
    });

    //cookie related code...............................................................................
    function getCookie() {
        cookieSplit = document.cookie.split("; ");
        var cookieObject = {};
        cookieSplit.forEach( function(value, index) {
           var splitResult = value.split("=");
           cookieObject[splitResult[0]] = splitResult[1];
        });
        return cookieObject;
    }
    function setCookie(name,value,exDays) {

        var d = new Date();
        d.setTime(d.getTime() + (exDays * 24 * 60 * 60 * 1000));
        var expires = "expires=" + d.toUTCString();
        document.cookie = name + "=" + value + ";" + expires + ";path=/";
    }
    var cookie = getCookie();
    //alert(JSON.stringify(cookie));
    var refresh = cookie['csrf_refresh_token'];
    var access = cookie['csrf_access_token'];
    if (access && refresh) {
        var ans = checkRefresh(access, refresh);
        //alert(ans);
        if (ans) {
            //window.location = flask_util.url_for('bookride.book');   what in the world is this?
        }
    }

    function checkRefresh(csrf_token, refresh_token) {
        var response = false;
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/token/verify',
            beforeSend: function(request) {
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            async: false,
            success: function(s) {
                if (s.success != 1) {
                    $.ajax({
                        type: "POST",
                        url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                        beforeSend: function(request) {
                            request.setRequestHeader('X-CSRF-Token', refresh_token);
                        },
                        async: false,
                        success: function(sr) {
                            //alert(JSON.stringify(sr))
                            if (sr.refresh != true) response = false;
                            else response =  true;
                        },
                        error: function(er) {
                            response =  false;
                        }
                    });
                } else {
                    response =  true;
                }
            },
            error: function(e) {
                if (e.status == 401) {
                    $.ajax({
                        type: "POST",
                        url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                        beforeSend: function(request) {
                            request.setRequestHeader('X-CSRF-Token', refresh_token);
                        },
                        async: false,
                        success: function(sr) {
                            //alert(JSON.stringify(sr))
                            if (sr.refresh != true) response =  false;
                            else response =  true;
                        },
                        error: function(err) {
                            //alert("huh");
                            response = false;
                        }
                    });
                } else response =  false;
            }
        });
        return response;
    }

    //reset register form on page refresh
    $(".form-control").val('');
    $("#pwd").val('');
    //$('#cc').prop('selectedIndex',0);
    $('#driverOption').prop('checked', false);
    $("#lcs").val('');
    $("#pro").val('');
    var match = $(function(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;

    }("mobile"));
    if (match) {
        $("#mobile").val(match.selector);
    }
    //code for refresh ends here



    //Submit Function for Customer Registration..........................................................
    $("#sbtRegCust").click(function(e) {
        e.preventDefault();
        //$(":input").popover("destroy");
        var fName = $("#fname").val().trim();
        var lName = $("#lname").val().trim();
        var mob = $("#phno").val().trim();
        var email = $("#em").val().trim();
        var pass = $("#pass").val().trim();
        //var sex = $("input[type='radio']:checked").val();
        var cc = $("#cc_code").val().trim();
        //alert(cc);
        var flag = true;
        var re = new RegExp("^[1-9][0-9]+$");
        var fe = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if (fName == "") {
            $("#fname").css('border','2px solid red');
            $("#fname").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#fname').data('bs.popover').options.content = "First name please";
            flag = false;
        }
        else $("#fname").popover('destroy');
        if (lName == "") {
            $("#lname").css('border','2px solid red');
            $("#lname").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#lname').data('bs.popover').options.content = "Last name please";
            flag = false;
        }
        else $("#lname").popover('destroy');
        if (mob == "") {
            $("#phno").css('border','2px solid red');
            $("#phno").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#phno').data('bs.popover').options.content = "Phone number is required";
            flag = false;
        } else if (mob.length != 10 || !re.test(mob)) {
            $("#phno").css('border','2px solid red');
            $("#phno").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#phno').data('bs.popover').options.content = "Enter correct phone number without country-code";
            flag = false;
        }
        else $("#phno").popover('destroy');
        if (email == "") {
            $("#em").css('border','2px solid orange');
            $("#em").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#em').data('bs.popover').options.content = "Please specify an e-mail address";
            //$("#em").popover("show");
            //$("#em").attr("placeholder","This field cannot be blank");
            //flag=false;
        } else if (!fe.test(email)) {
            $("#em").css('border','2px solid orange');
            $("#em").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#em').data('bs.popover').options.content = "Invalid E-mail format";
            $("#em").val('');
            flag = false;
        }
        else $("#em").popover('destroy');
        if (pass.length < 6) {
            $("#pass").css('border','2px solid red');
            $("#pass").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#pass').data('bs.popover').options.content = "Password must be atleast 6 characters long";
            $("#pass").val('');
            flag = false;
        }
        else $("#pass").popover('destroy');
        if (flag) {
            e.preventDefault();
            if (cc == "in")
                cCode = "91"; //Harcoded, needs to be changed
            else if (cc == "bd")
                cCode = "880"; //Hardcoded, needs to be changed
            else cCode = "91"; //Fallback, if no match
            var data = new FormData();
            data.append("fname", fName);
            data.append("lname", lName);
            data.append("ccode", cCode);
            data.append("mobile", mob);
            data.append("email", email);
            data.append("pwd", pass);
            //data.append("sex", sex);
            var urlPost = window.location.protocol + '//' + window.location.host + '/api/register_cust';
            $.ajax({
                type: "POST",
                enctype: "multipart/form-data",
                url: urlPost,
                data: data,
                processData: false,
                crossDomain: true,
                contentType: false,
                cache: false,
                success: function(response) {
                    alert(JSON.stringify(response));
                      $(".form-control").val('');
                     $("input[type=file]").val('');
                     if(response.response)
                     {
                        $("#error").text(response.msg);
                        $("error").css("visibility","visible");
                     }
                     else
                     document.getElementById('signInForm').click();
                },
                error: function(e) {
                    alert(JSON.stringify(e));
                }
            });

        }

    });

    $( "#sbtLog").click(function(e) {
        alert('xd');
        e.preventDefault();
        var mob = $("#mobile").val().trim();
        var password = $("#pwd").val();
        var check = $("#signedInCheck").is(":checked");
        var rem = $("#rememberCheck").is(":checked");
        var flag = true;
        var re = new RegExp("^[1-9][0-9]+$");
        if (mob == "") {
          $("#mobile").css('border','2px solid red');
          $("#mobile").popover({
            placement: "top",
            trigger: "hover"
          });
          $('#mobile').data('bs.popover').options.content = "This field cannot be blank";
            /*var pop = $("#mobile").data("bs.popover");
            if (pop)
                pop.options.content = "This field cannot be blank";
            else
                $("#mobile").popover({
                    content: "This field cannot be blank",
                    placement: "left",
                    trigger: "manual"
                });
            //$("#mobile").popover("show");
            $("#mobile").attr("placeholder","This field cannot be blank");
            flag = false;
        */} else if (mob.length != 10 || !re.test(mob)) {
            $("#mobile").css('border','2px solid red');
            $("#mobile").popover({
            placement: "top",
            trigger: "hover"
          });
            $('#mobile').data('bs.popover').options.content = "Enter correct mobile number without country-code";
            /*var pop = $("#mobile").data("bs.popover");
            if (pop)
                pop.options.content = "Enter correct mobile number without country-code";
            else
                $("#mobile").popover({
                    content: "Enter correct mobile number without country-code",
                    placement: "left",
                    trigger: "manual"
                });
            //$("#mobile").popover("show");
            $("#mobile").val('');
            $("#mobile").attr("placeholder","Enter correct mobile number without country-code");*/
            flag = false;
        }
        if (password == "") {
          $("#pwd").css('border','2px solid red');
            $("#pwd").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#pwd').data('bs.popover').options.content = "This field cannot be blank";
            //$("#pwd").popover("show");
            //document.getElementById("pwd").placeholder="This field cannot be blank";
            flag = false;
        }
        if (flag) {

            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/token/login',
                data: {
                    mobile: mob,
                    pwd: password,
                    remember: check
                },
                dataType: "json",
                success: function(e) {
                    var msg = JSON.stringify(e);
                    //alert(msg);
                    /*var json=$.parseJSON(msg);
                    alert(json);*/

                    if (e.success!=1)
                        {
                            //document.getElementById("ermsg").style.display = "block";
                        }
                    else {
                        //alert(sessionStorage.getItem("d4msessionaccess"));
                        //document.getElementById("ermsg").style.visibility = "hidden";
                        //alert(id);
                        setCookie("name",e.user_fname);
                        //window.location = flask_util.url_for('bookride.book');
                    }

                },
                error: function(e) {
                    //alert(JSON.stringify(e));
                    if(e.status==401||e.status==400)
                        document.getElementById("ermsg").style.display = "block";

                }
            });
        }


    });
    //Submit form for Companion Registration......................................................................
    $("#sbtRegComp").click(function(e) {
        e.preventDefault();
        //$(":input").popover("destroy");
        var fName = $("#fname_comp").val().trim();
        var lName = $("#lname_comp").val().trim();
        var mob = $("#phno_comp").val().trim();
        var email = $("#em_comp").val().trim();
        var pass = $("#pass_comp").val().trim();
        //var sex = $("input[type='radio']:checked").val();
        var cc = $("#cc_code_companion").val().trim();
        //alert(cc);
        var flag = true;
        var re = new RegExp("^[1-9][0-9]+$");
        var fe = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if (fName == "") {
            $("#fname_comp").css('border','2px solid red');
            $("#fname_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#fname_comp').data('bs.popover').options.content = "First name please";
            flag = false;
        }
        else $("#fname_comp").popover('destroy');
        if (lName == "") {
            $("#lname_comp").css('border','2px solid red');
            $("#lname_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#lname_comp').data('bs.popover').options.content = "Last name please";
            flag = false;
        }
        else $("#lname_comp").popover('destroy');
        if (mob == "") {
            $("#phno_comp").css('border','2px solid red');
            $("#phno_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#phno_comp').data('bs.popover').options.content = "Phone number is required";
            flag = false;
        } else if (mob.length != 10 || !re.test(mob)) {
            $("#phno_comp").css('border','2px solid red');
            $("#phno_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#phno_comp').data('bs.popover').options.content = "Enter correct phone number without country-code";
            flag = false;
        }
        else $("#phno_comp").popover('destroy');
        if (email == "") {
            $("#em_comp").css('border','2px solid orange');
            $("#em_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#em_comp').data('bs.popover').options.content = "Please specify an e-mail address";
            //$("#em").popover("show");
            //$("#em").attr("placeholder","This field cannot be blank");
            //flag=false;
        } else if (!fe.test(email)) {
            $("#em_comp").css('border','2px solid orange');
            $("#em_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#em_comp').data('bs.popover').options.content = "Invalid E-mail format";
            $("#em_comp").val('');
            flag = false;
        }
        else $("#em_comp").popover('destroy');
        if (pass.length < 6) {
            $("#pass_comp").css('border','2px solid red');
            $("#pass_comp").popover({
                placement: "top",
                trigger: "hover"
            });
            $('#pass_comp').data('bs.popover').options.content = "Password must be atleast 6 characters long";
            $("#pass_comp").val('');
            flag = false;
        }
        else $("#pass_comp").popover('destroy');
        var lno = $("#lno").val().trim();
        //regex desc: two alphabets
        //then two numbers
        //followed by a "valid" year -- this regex takes years between 1900-2099 as valid
        //then 7 numbers
        //source: https://www.codeproject.com/Questions/1065006/how-to-validate-drivers-license-number
        var ltest = new RegExp("^[A-Za-z]{2}[0-9]{2}(19|20)[0-9]{9}$");
        if (!ltest.test(lno)) {
          $("#lno").css('border','2px solid red');
          $("#lno").popover({
            placement: "top",
            trigger: "hover"
          });
          if(lno!="") $('#lno').data('bs.popover').options.content = "Licence number is not in correct format";
          else $('#lno').data('bs.popover').options.content = "This field is required";
          $("#lno").val('');
            //flag=false;
        }
        else $("#lno").popover('destroy');
        if ($("#lcs").val() == '') {
          $("#licPic").css('border','2px solid red');
          $("#licPic").popover({
            placement: "top",
            trigger: "hover"
          });
          $('#licPic').data('bs.popover').options.content = "You must upload a picture of your licence";
            flag = false;

        }
        else $("#licPic").popover('destroy');
        if ($("#pro").val() == '') {
          $("#profPic").css('border','2px solid red');
          $("#profPic").popover({
            placement: "top",
            trigger: "hover"
          });
          $('#profPic').data('bs.popover').options.content = "Please upload your picture";
            flag = false;

        }
        else $("#profPic").popover('destroy');
        if (flag) {
            e.preventDefault();
            if (cc == "in")
                cCode = "91"; //Harcoded, needs to be changed
            else if (cc == "bd")
                cCode = "880"; //Hardcoded, needs to be changed
            else cCode = "91"; //Fallback, if no match
            var data = new FormData();
            data.append("fname", fName);
            data.append("lname", lName);
            data.append("ccode", cCode);
            data.append("mobile", mob);
            data.append("email", email);
            data.append("pwd", pass);
            //data.append("sex", sex);
            var urlPost = window.location.protocol + '//' + window.location.host + '/api/register_cust';
            data.append("licNo", lno);
            var lic = document.getElementById("lcs").files[0];
            var photo = document.getElementById("pro").files[0];
            data.append("licPic", lic);
            data.append("pic", photo);
            urlPost = window.location.protocol + '//' + window.location.host + '/api/register/driver';
            $.ajax({
                type: "POST",
                enctype: "multipart/form-data",
                url: urlPost,
                data: data,
                processData: false,
                crossDomain: true,
                contentType: false,
                cache: false,
                success: function(response) {
                    //alert(JSON.stringify(response));
                      $(".form-control").val('');
                     $("input[type=file]").val('');
                     if(response.response)
                     {
                        $("#error").text(response.msg);
                        $("error").css("visibility","visible");
                     }
                     else
                     document.getElementById('signInForm').click();
                },
                error: function(e) {
                    alert(JSON.stringify(e));
                }
            });

        }

    });



    //Code for reset from erroneous fields..........................................................
    $("#mobile").click(function() {
      $(this).css('border','');
    });
    $("#pwd").click(function() {
      $(this).css('border','');
      $(this).attr('placeholder','Password');
    });
    $("#fname").click(function() {
        $(this).css('border','');
    });
    $("#lname").click(function() {
        $(this).css('border','');
    });
    $("#phno").click(function() {
        $(this).css('border','');
    });
    $("#em").click(function() {
        $(this).css('border','');
    });
    $("#fname_comp").click(function() {
        $(this).css('border','');
    });
    $("#lname_comp").click(function() {
        $(this).css('border','');
    });
    $("#phno_comp").click(function() {
        $(this).css('border','');
    });
    $("#em_comp").click(function() {
        $(this).css('border','');
    });
    $("#contact_mail").click(function() {
        $(this).css('border','');
    });
    $("#message").click(function() {
        $(this).css('border','');
    });
    /*$("#pass").focusout(function() {
        var x = $(this).val();
        if (x.length < 6) {
            $(this).val('');
            $(this).attr("placeholder","Password must be at least six characters long");
            $(this).css('border','2px solid red');
        } else {
            $(this).popover("hide");
        }
    });*/
    $("#pass").click(function() {
        $(this).css('border','');
        $(this).attr('placeholder','Enter password');
    });
    $("#pass_comp").click(function() {
        $(this).css('border','');
        $(this).attr('placeholder','Enter password');
    });

    //What does Hide achieve exactly??............................................
    $("#pass").focusin(function() {
        $(this).popover("hide");
    });
    $("#lcs").hover(function() {
        $(this).popover("hide");
    });
    $("#pro").hover(function() {
        $(this).popover("hide");
    });
    $("#lno").click(function() {
        $(this).css('border','');
    });
    $("#licPic").click(function() {
        $(this).css('border','');
    });
    $("#profPic").click(function() {
        $(this).css('border','');
    });
    $("#phno").click(function() {
        $(this).popover("hide");
    });
    $(".form-control").click(function() {
       $("#error").css("visibility","hidden");
       $("#ermsg").css("display","none");
    });
    $("input").click(function() {
        $("#licPic").popover("hide");
        $("#profPic").popover("hide");
    });

    //Password visibility functionality.................................................................
    $(".input-group-addon").click(function() {
        var toggle = $(this).find("i").attr('class');
        if(toggle=="glyphicon glyphicon-eye-open") {
            $(this).parent().find("input").attr('type','text');
            $(this).find("i").attr('class','glyphicon glyphicon-eye-close');
        }
        else {
            $(this).parent().find("input").attr('type','password');
            $(this).find("i").attr('class','glyphicon glyphicon-eye-open');
        }
    });

    //Form reset functions...................................................................................
    function resetLogin() {
        $("#mobile").val("");
        $("#pwd").val("");
        $("#custVisib").attr('class','glyphicon glyphicon-eye-open');
        $("#pwd").attr('type','password');
        $("#signedInCheck").attr('checked', false);
    }
    function resetRegCust() {
        $("#fname").val("");
        $("#lname").val("");
        $("#phno").val("");
        $("#em").val("");
        $("#pass").val("");
        $("#cRegVisib").attr('class','glyphicon glyphicon-eye-open');
    }
    function resetRegComp() {
        $("#fname_comp").val("");
        $("#lname_comp").val("");
        $("#phno_comp").val("");
        $("#em_comp").val("");
        $("#pass_comp").val("");
        $("#coRegVisib").attr('class','glyphicon glyphicon-eye-open');
        $("#lno").val("");
        $("#licPic").attr('src','assets/images/upload.png');
        $("#profPic").attr('src','assets/images/upload.png');

    }

    //hack for modal close
    $('body').click(function(e) {
        var classNam = e.target.className + "";
        if (classNam == "modal-backdrop fade in")
            $('.modal').modal('hide');
    });

    //border-adjustment for hamburger
    $(".mbr-navbar__hamburger").click(function() {
        if($(this).attr('class').indexOf('mbr-hamburger--open')>=0) {
            var oldClass = $(".bordered").find("a").attr('class');
            $(".bordered").find("a").attr('class',oldClass.replace(" put-border",""));
        }
        else {
            var oldClass = $(".bordered").find("a").attr('class');
            $(".bordered").find("a").attr('class', oldClass+" put-border");
        }
    });

    //resize function, put all resizing functions here
    $(window).resize(function() {
        //dummy item adjustment in nav-menu
        $(".dummy-menu-item").css('display','none');
        //adjust navigation circles
        $(window).scroll();
        //safety code for proper functioning of border-placement
        if($(".mbr-navbar__hamburger").attr('class').indexOf('mbr-hamburger--open')<0) {
            var oldClass = $(".bordered").find("a").attr('class');
            if(oldClass.indexOf("put-border")<0) {
                $(".bordered").find("a").attr('class', oldClass+" put-border");
            }
        }
    });


    //usage of isElementInView
    $(window).scroll(function() {
        var isHomeInView = Utils.isElementInView($('#header2-x'), false);
        var isStepsInView = Utils.isElementInView($('#features1-10'), false);
        var isPriceInView = Utils.isElementInView($('#pricing-table1-25'), false);
        var isCompInView = Utils.isElementInView($('#companionSection'), false);
        var isFeatInView = Utils.isElementInView($('#content5-1j'), false);
        var isB2BInView = Utils.isElementInView($('#businessSection'), false);
        var isContInView = Utils.isElementInView($('#contacts3-1s'), false);

        $("#circles_container").find("img").attr('src','static/assets/images/hollowCircle.png') ;
        $("#nav-down-arrow").css('display','block');
        $("#nav-down-arrow").find('span').attr('class','glyphicon glyphicon-chevron-down next-section text-white');
        if (isHomeInView) {
            $("#circle1").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('a').attr('href','#features1-10');
        }
        else if (isStepsInView) {
            $("#circle2").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('a').attr('href','#pricing-table1-25');
        }
        else if (isPriceInView) {
            $("#circle3").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('a').attr('href','#companionSection');
        }
        else if (isCompInView) {
            $("#circle4").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('a').attr('href','#content5-1j');
        }
        else if (isFeatInView) {
            $("#circle5").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('a').attr('href','#businessSection');
        }
        else if (isB2BInView) {
            $("#circle6").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('a').attr('href','#contacts3-1s');
        }
        else if (isContInView) {
            $("#circle7").find("img").attr('src','static/assets/images/filledCircle.png') ;
            $("#nav-down-arrow").find('span').attr('class','glyphicon glyphicon-chevron-up next-section text-white very-small-bottom-padding');
            $("#nav-down-arrow").find('a').attr('href','#header2-x');
        }
    });

    //proper functioning of dropdown
    $("#dropdown_menu").click(function() {
        if($(".mbr-navbar__hamburger").attr('class').indexOf('mbr-hamburger--open')<0 && $("#hamburger_menu").css('display')=='block') {
            $(".mbr-navbar__hamburger").trigger('click');
            if($(".dummy-menu-item").css('display')=='block') {
                $(".dummy-menu-item").css('display','none');
                $(this).find("span").attr('class','caret');
            }
            else {
                $(".dummy-menu-item").css('display','block');
                $(this).find("span").attr('class','caret caret-up');
            }
        }
        else if($("#hamburger_menu").css('display')=='none') {
            if($("#dropdown_menu").attr('class')=='mbr-navbar__item end') {
                $("#dropdown_menu").attr('class','mbr-navbar__item end open');
                $(this).find("span").attr('class','caret caret-up');
            }
            else {
                $("#dropdown_menu").attr('class','mbr-navbar__item end');
                $(this).find("span").attr('class','caret');
            }
        }
    });
    $(document).mouseup(function (e) {
        var container = $("#dropdown_menu");

        if (!container.is(e.target) && container.has(e.target).length === 0 && !$("#hamburger_menu").is(e.target) && container.attr('class')=='mbr-navbar__item end open')
        {
            $(container).trigger('click');
        }
    });

    //contact copy-to-clipboard function
    $(".clipboard_stuff").click(function() {
        var $temp = $("<input>");
        $("body").append($temp);
        $temp.val($(this).html()).select();
        document.execCommand("copy");
        $temp.remove();

        //notify successful copy
        setTimeout(function(){ $("#copySuccess").modal('show'); }, 100);
        setTimeout(function(){ $("#copySuccess").modal('hide'); }, 900);
    });

    /*Display Discounted Rate
    $(".booking-charge").mousedown(function() {
        $(this).find(".normal").attr("class","normal collapse");
        $(this).find(".discounted").attr("class","discounted");
    });
    $(".booking-charge").mouseup(function() {
        $(this).find(".normal").attr("class","normal");
        $(this).find(".discounted").attr("class","discounted collapse");
    });
    $(".car-fare").mousedown(function() {
        $(this).find(".normal").attr("class","normal collapse");
        $(this).find(".discounted").attr("class","discounted");
    });
    $(".car-fare").mouseup(function() {
        $(this).find(".normal").attr("class","normal");
        $(this).find(".discounted").attr("class","discounted collapse");
    });*/

    //display base estimate
    $(".hourly-rate").click(function() {
        if($(this).closest(".pricing-modal").find(".surge-toggle").attr('class').indexOf('active') != -1) {
            //surge active
            if($(this).find(".surge").attr('class') == 'surge collapse') {
                $(this).find(".surge").attr("class","surge");
                $(this).find(".surge-estimate").attr("class","surge-estimate collapse");
            }
            else {
                $(this).find(".surge").attr("class","surge collapse");
                $(this).find(".surge-estimate").attr("class","surge-estimate");
            }
        }
        else {
            if($(this).find(".normal").attr('class') == 'normal collapse') {
                $(this).find(".normal").attr("class","normal");
                $(this).find(".normal-estimate").attr("class","normal-estimate collapse");
            }
            else {
                $(this).find(".normal").attr("class","normal collapse");
                $(this).find(".normal-estimate").attr("class","normal-estimate");
            }
        }
    });

    //handle surge display
    $(".surge-toggle").click(function() {
        if($(this).attr('class').indexOf('active') != -1) {
            var parentPricingElement = $(this).closest('.pricing-modal-surge');
            //return to normal
            parentPricingElement.find(".surge-estimate").attr('class', 'surge-estimate collapse');
            parentPricingElement.find(".surge").attr('class', 'surge collapse');
            parentPricingElement.find(".normal-estimate").attr('class', ' normal-estimate collapse');
            parentPricingElement.find(".normal").attr('class', 'normal');
            $(".discounted").attr('class', 'discounted');
            $(this).attr('class', 'btn btn-xs btn-warning surge-toggle');
            parentPricingElement.attr('class', 'modal fade pricing-modal in');
        }
        else {
            var parentPricingElement = $(this).closest('.pricing-modal');
            //show surge
            parentPricingElement.find(".normal-estimate").attr('class', 'normal-estimate collapse');
            parentPricingElement.find(".normal").attr('class', 'normal collapse');
            parentPricingElement.find(".surge-estimate").attr('class', ' surge-estimate collapse');
            parentPricingElement.find(".surge").attr('class', 'surge');
            $(".discounted").attr('class', 'discounted collapse');
            $(this).attr('class', 'btn btn-xs btn-warning surge-toggle active');
            parentPricingElement.attr('class', 'modal fade pricing-modal pricing-modal-surge in');
        }
    });

    //code for write to us e-mail
    $( "#sbtFeedback").click(function(e) {
        e.preventDefault();
        var email = $("#contact_mail").val().trim();
        var message = $("#message").val();
        var mailRegex = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;           //regex for email validation
        var validData = true;

        //mail validation
        if (email == "") {
            $("#contact_mail").css('border','2px solid red');         //error indication
            //error popover
            $("#contact_mail").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#contact_mail').data('bs.popover').options.content = "This field cannot be blank";
            validData = false;
        }

        else if (!mailRegex.test(email)) {
            $("#contact_mail").css('border','2px solid red');         //error indication
            //error popover
            $("#contact_mail").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#contact_mail').data('bs.popover').options.content = "Enter valid e-mail address";
            validData = false;
        }

        else {
            $("#contact_mail").popover('destroy');
            validData = true;
        }

        //message validation
        if(message.length <= 0) {
            $("#message").css('border','2px solid red');            //error indication
            //error popover
            $("#message").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#message').data('bs.popover').options.content = "Your message is blank";
            validData = false;
        }

        else {
            $("#message").popover('destroy');
            validData = true;
        }

        //send mail
        if (validData) {
            window.location = 'mailto:' + window.contactMail + '?subject=' + "User FeedBack" + '&body=' +   message;
            alert(window.location);
        }
    });
    //get and fill price info (WIP)
    /*function getPriceInfo() {
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/price',
            data: '',
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                //price consolidations
                //in-city round trip
                var inCityRound = $("#inCity-RoundTrip-Plan");
                var inCityRoundBase = response["in-round-hourly-fares"].split(',');
                inCityRound.find(".mbr-number__value").html(inCityRoundBase[inCityRoundBase.length - 1])
                //detailed pricing
                //in-city round trip
            },
            error: function() {

            }

        });
    }*/
});

//returns current year
function updateCopyrightYear() {
    $("#copyrightYear").html(
        new Date().toLocaleString('en-GB',
        {
            year: "numeric"
        })
    );
}
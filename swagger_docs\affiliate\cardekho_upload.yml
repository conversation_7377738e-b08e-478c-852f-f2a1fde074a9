tags:
  - Affiliate
summary: Upload a Cardekho image for a driver.
description: This API allows a driver to upload an image for a Cardekho booking.
parameters:
  - name: booking_id
    in: formData
    required: true
    type: integer
    description: The ID of the Cardekho booking.
  - name: index
    in: formData
    required: true
    type: integer
    description: The index of the image in the sequence.
  - name: pic
    in: formData
    required: true
    type: file
    description: The image file to be uploaded.
responses:
  200_a:
    description: Cardekho image uploaded successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Cardekho image uploaded successfully"
    examples:
      application/json:
        success: 1
        msg: "Cardekho image uploaded successfully"
  201_a:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        msg:
          type: string
          example: "Incomplete form"
    examples:
      application/json:
        success: -2
        msg: "Incomplete form"
  401_a:
    description: Unauthorized role, not Driver.
    schema:
      type: object
      properties:
        error:
          type: integer
          example: 1
        message:
          type: string
          example: "Unauthorized role, not Driver"
    examples:
      application/json:
        error: 1
        message: "Unauthorized role: not Driver"
  401_b:
    description: User restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"
  500_a:
    description: Server error (DB Error).
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
        reason:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"
        reason: "DB Error"
  500_b:
    description: Another DB error during upload.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "DB Error"
        reason:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        msg: "DB Error"
        reason: "DB Error"

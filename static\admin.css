/*Get the colors file*/
@import url("../static/assets/Colors/custom-colors.css");
@font-face
{
    font-family: 'Exo2 Medium';
    src: url('../static/assets/fonts/Exo2-Medium.otf') format("opentype");
}
@font-face
{
    font-family: 'Exo2 ExtraBold';
    src: url('../static/assets/fonts/Exo2-ExtraBold.otf') format("opentype");
}
@font-face
{
    font-family: 'Exo2 Bold';
    src: url('../static/assets/fonts/Exo2-Bold.otf') format("opentype");
}
@font-face
{
    font-family: 'Exo2 Regular';
    src: url('../static/assets/fonts/Exo2-Regular.otf') format("opentype");
}
@font-face
{
    font-family: 'Uni Sans Heavy';
    src: url('../static/assets/Fonts/Uni_Sans_Heavy.otf') format("opentype");
}
@font-face
{
    font-family: '<PERSON><PERSON>s Thin';
    src: url('../static/assets/fonts/Uni Sans Thin.otf') format("opentype");
}
body {
    font-family: 'Exo2 Medium'!important;
    padding-right: 0 !important;
}
.form_element {
    padding-top: 10px;
    padding-bottom: 10px;
}
hr {
    margin-top: 5px !important;
    margin-bottom: 5px !important;
}
hr.d4m-ending {
    padding: 0;
    border: none;
    height: 1.6px;
    background-image: -webkit-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
    background-image: -moz-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
    background-image: -ms-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
    background-image: -o-linear-gradient(left, rgba(0,0,0,0), rgba(0,0,0,0.75), rgba(0,0,0,0));
    color: #333;
    text-align: center;
}
.admin-function {
    padding-top: 70px;
}

#adminLoginForm {
	background-color: ivory;
}
.popover-content {
	border-radius: 8px;
	background-color: coral;
}
.small-top-padding {
  padding-top: 10px;
}
.standard-top-padding {
  padding-top: 20px;
}
.standard-bottom-padding {
  padding-bottom: 20px;
}
.tiny-padding {
  padding: 5px;
}
.tiny-bottom-padding {
  padding-bottom: 5px!important;
}
.tiny-top-padding {
  padding-top: 5px!important;
}
.tiny-top-margin {
    margin-top: 5px !important;
}
.medium-bottom-padding {
    padding-bottom: 10px !important;
}
.medium-padding {
  padding: 25px;
}
.rejected-trip {
  background-color: azure;
  margin-top: 10px;
}
.rejected-trip .label {
  color: black;
}
.cancelled-trip {
  background-color: mintcream;
  margin-top: 10px;
}
.cancelled-trip .label {
  color: black;
}
.mapLink {
  color: white;
  background-color: #34a853;
}
.mapLink .glyphicon {
  color: #ea4335;
}
.status-unaccepted {
  color: darkorange!important;
}
.status-accepted {
  color: forestgreen!important;
}
#driverDuesForm .label {
  color: black;
}
.ccancel, .dcancel {
  border-radius: 5px;
  padding: 4px;
}
.bookingsTable th {
  background-color: mediumspringgreen;
}
.addr {
    font-size: 10px !important;
}

.bookingsTable th, .booking-stack td {
  border: 2.3px solid black !important;;
  font-size: 13px;
  vertical-align: middle!important;
  text-align: center!important;
}
.book-filter, .driver-filter {
  text-align: center;
  vertical-align: middle;
  margin-right: 9px;
  border-radius: 9px;
  font-size: 14px;
  cursor: pointer;
  box-shadow: 3px 3px 3px 3px rgba(0, 0, 0, 0.2);
}
.label {
  color: black!important;
}
#estimate-stack > tr:nth-child(2n+1) {
    background-color: azure!important;
}
#estimate-stack > tr:nth-child(2n) {
    background-color: skyblue!important;
}
#estimatesTable th, #driversTable th {
  background-color: springgreen;
  font-size: 16px;
}
#estimatesTable th, #estimate-stack td, #driversTable th, #driversTable td {
  border: 2.3px solid black;
  font-size: 14px;
  vertical-align: middle!important;
  text-align: center!important;
}
.removed {
  opacity: 0.5;
  box-shadow: none;
}
.standard-left-padding {
  padding-left: 20px;
}
.break-long-text {
  word-wrap: break-word;
}
#alloc-driver-list tr, #dues-driver-list tr {
  cursor: pointer;
}
#alloc-driver-list tr:hover, #dues-driver-list tr:hover {
  background-color: slateblue!important;
}
.driver-unavailable {
  background-color: khaki!important;
}
.driver-unapproved {
  background-color: orange!important;
}
.driver-unavailable-unapproved {
  background-color: lightsalmon!important;
}
.driver-perma {
    background-color: lightgreen !important;
}
.dropdown-menu {
  min-width: 0!important;
  min-height: 0!important;
  text-align: center!important;
  top: auto !important;
}
.pricing-section {
  background-color: mintcream;
}
.trip-categories {
  margin: 3px;
  border-radius: 10px;
  padding: 8px;
  color: white;
  background: linear-gradient(to right, var(--d4m-standard-dark), white);
}
.pricing-tabs {
  padding: 10px;
}
.price-tab {
  margin: 3px;
  border-radius: 10px;
  background-color: var(--d4m-fade-striped-row);
  cursor: pointer;
  padding: 8px;
}
.price-tab:hover {
  background-color: var(--d4m-standard-dark);
  color: white;
  cursor: pointer;
}
.price-tab-active {
  margin: 3px;
  border-radius: 10px;
  background-color: var(--d4m-standard-dark);
  color: white;
  cursor: pointer;
  padding: 8px;
}
#infoModal{
  z-index: 3000;
}
.booking-tools {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
}
@keyframes glowingDiscount {
    0%   {color: darkgreen;}
    25%  {color: white;}
    50%  {color: darkgreen;}
    75%  {color: white;}
    100%  {color: darkgreen;}
}
@-webkit-keyframes glowingDiscount {
    0%   {color: darkgreen;}
    25%  {color: white;}
    50%  {color: darkgreen;}
    75%  {color: white;}
    100%  {color: darkgreen;}
}
.booking-charge > .discounted, .car-fare > .discounted {
    -webkit-animation-name: glowingDiscount;
    -webkit-animation-duration: 3s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease;
    animation-name: glowingDiscount;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: ease;
}
.pricing-section th {
    background-color: var(--d4m-standard-dark);
    color: white;
    text-align: center;
    font-family: 'Uni Sans Thin';
    }
.pricing-section .table-striped > tbody > tr:nth-child(2n+1) {
    background-color: white!important;
}
.price-sub-section-heading {
    padding-top: 5px;
    padding-bottom: 5px;
    background-color: var(--d4m-standard-light);
    letter-spacing: 0.4px;
    color: white;
    font-family: 'Uni Sans Heavy';
    text-align: center;
}
.justified-text {
  text-align: justify;
}
.hourly-rate {
  cursor: pointer;
}

.booking-new, .c24-booking-new, .Revv-booking-new, .RevvV2-booking-new, .Gujral-booking-new, .Customer-booking-new, .C24-booking-new, .OLX-booking-new, .Zoomcar-booking-new, .Zoomcar-Delhi-booking-new, .Cardekho-booking-new, .Cardekho-Delhi-booking-new, .Bhandari-booking-new, .Mahindra-booking-new, .Spinny-booking-new, .PrideHonda-booking-new    {
  background-color: gray!important;
}
.booking-quick-cancel, .C24-booking-quick-cancel, .OLX-booking-quick-cancel, .Zoomcar-booking-quick-cancel, .Zoomcar-Delhi-booking-quick-cancel, .Cardekho-booking-quick-cancel, .Bhandari-booking-quick-cancel, .Mahindra-booking-quick-cancel, .Spinny-booking-quick-cancel, .Cardekho-Delhi-booking-quick-cancel, .Revv-booking-quick-cancel, .RevvV2-booking-quick-cancel, .Customer-booking-quick-cancel, .Gujral-booking-quick-cancel, .PrideHonda-booking-quick-cancel {
  background-color: khaki!important;
}
.booking-customer-cancel, .C24-booking-customer-cancel, .OLX-booking-customer-cancel, .Zoomcar-booking-customer-cancel, .Zoomcar-Delhi-booking-customer-cancel, .Cardekho-booking-customer-cancel, .Bhandari-booking-customer-cancel, .Mahindra-booking-customer-cancel, .Spinny-booking-customer-cancel, .Cardekho-Delhi-booking-customer-cancel, .Revv-booking-customer-cancel, .RevvV2-booking-customer-cancel, .Customer-booking-customer-cancel,
.Gujral-booking-customer-cancel, .PrideHonda-booking-customer-cancel {
  background-color: gold!important;
}
.booking-driver-cancel, .booking-onmyway, .C24-booking-driver-cancel, .OLX-booking-driver-cancel, .Zoomcar-booking-driver-cancel, .Zoomcar-Delhi-booking-driver-cancel, .Cardekho-booking-driver-cancel, .Bhandari-booking-driver-cancel, .Mahindra-booking-driver-cancel, .Spinny-booking-driver-cancel, .Cardekho-Delhi-booking-driver-cancel, .Revv-booking-driver-cancel, .RevvV2-booking-driver-cancel, .Customer-booking-driver-cancel, .Gujral-booking-driver-cancel, .PrideHonda-booking-driver-cancel {
  background-color: lightsalmon!important;
}
.booking-accepted, .C24-booking-accepted, .OLX-booking-accepted, .Zoomcar-booking-accepted, .Cardekho-booking-accepted, .Bhandari-booking-accepted, .Mahindra-booking-accepted, .Spinny-booking-accepted, .Revv-booking-accepted, .RevvV2-booking-accepted, .Customer-booking-accepted, .Gujral-booking-accepted, .PrideHonda-booking-accepted {
  background-color: palegreen!important;
}
.booking-ongoing, .C24-booking-ongoing, .OLX-booking-ongoing, .Zoomcar-booking-ongoing, .Zoomcar-Delhi-booking-ongoing, .Cardekho-booking-ongoing, .Bhandari-booking-ongoing, .Mahindra-booking-ongoing, .Spinny-booking-ongoing, .Cardekho-Delhi-booking-ongoing, .Revv-booking-ongoing, .RevvV2-booking-ongoing, .Customer-booking-ongoing, .Gujral-booking-ongoing, .PrideHonda-booking-ongoing {
  background-color: aquamarine!important;
}
.booking-completed, .C24-booking-completed, .OLX-booking-completed, .Zoomcar-booking-completed, .Zoomcar-Delhi-booking-completed, .Cardekho-booking-completed, .Bhandari-booking-completed, .Mahindra-booking-completed, .Spinny-booking-completed, .Cardekho-Delhi-booking-completed, .Revv-booking-completed, .RevvV2-booking-completed, .Customer-booking-completed, .Gujral-booking-completed, .PrideHonda-booking-completed {
  background-color: yellowgreen!important;
}
.booking-d4m-cancel, .C24-booking-d4m-cancel, .OLX-booking-d4m-cancel, .Zoomcar-booking-d4m-cancel, .Zoomcar-Delhi-booking-d4m-cancel, .Cardekho-booking-d4m-cancel, .Bhandari-booking-d4m-cancel, .Mahindra-booking-d4m-cancel, .Spinny-booking-d4m-cancel, .Cardekho-Delhi-booking-d4m-cancel, .Revv-booking-d4m-cancel, .RevvV2-booking-d4m-cancel, .Customer-booking-d4m-cancel, .Gujral-booking-d4m-cancel, .PrideHonda-booking-d4m-cancel {
  background-color: var(--d4m-standard-light)!important;
  color: white;
}
.daily-vital {
  padding: 2px;
  border-radius: 4px;
}
.pricing-section-surge th {
    background-color: #5b0202;
}
.pricing-section-surge tr {
    background-color: #efd5d5!important;
}
.pricing-section-surge h3 {
    background-color: #7c1919;
}
/*toggle switch css............................................................*/
.switch {
  position: relative;
  display: inline-block;
  width: 90px;
  height: 17px;
  top: 5px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgb(220, 220, 220);
  -webkit-transition: .4s;
  transition: .4s;
  color: black;
  font-size: 11px;
}
.source-map-booking {
    background-color: beige !important;
    padding: 2px !important;
}
.destination-map-booking {
    padding: 2px !important;
}
.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 0px;
  bottom: -2px;
  background-image: radial-gradient(white, rgb(80, 80, 80), white);
  -webkit-transition: .4s;
  transition: .4s;
}
.location-section {
    font-size: 12px !important;
}
.c24-slider{
    background-color: rgb(224, 24, 24) !important;
}

.c24-select-div {
    text-align: center !important;
}

.item{
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

input:checked + .slider {
  background-color: var(--d4m-standard-dark)  !important;
  color: rgb(220, 220, 220);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--d4m-standard-dark);
}

input:checked + .slider:before {
  -webkit-transform: translateX(73px);
  -ms-transform: translateX(73px);
  transform: translateX(73px);
  background-image: radial-gradient(white, green, white);
}

/* Rounded sliders */
.slider.round {
  border-radius: 17px;
}

.slider.round:before {
  border-radius: 50%;
}
/*toggle switch css............................................................*/
.driver-action {
  font-size: 28px;
  color: rgb(200, 200, 200);
  cursor: pointer;
}
.narrow-line {
  margin-top: 5px;
  margin-bottom: 5px;
}
.driver-action:hover {
  color: var(--d4m-standard-light);
  box-shadow: none;
}
.driver-action-needed {
  color: var(--d4m-standard-dark);
  box-shadow: 2px 2px 4px var(--d4m-standard-light);
}
.rotate {
  width: 120px;
  height: 120px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}
/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.driver-color-class {
  height: 40px;
  border-radius: 5px;
}
.chart-timescale-change {
  text-align: center!important;
  border-radius: 6px;
  cursor: pointer;
  border: 2px solid cyan;
  color: var(--d4m-standard-light);
}
.chart-timescale-change:hover {
  background-color: cyan;
}
.trollFont {
  font-size: 10px;
  font-style: italic;
}
.base-loc {
    width: 100% !important;
}
.tiny-bottom-margin {
    margin-bottom: 3px!important;
}
.book-comments {
    cursor: pointer;
}
.has-comment {
    color: blue;
}

#paid_check {
    display: inline !important
}

#amount_change {
    display: inline !important;
    width: 80% !important;
}
.pending-suppressed {
    background-color: coral!important;
}

.filtered {
    display: none!important;
}

.height-constrained-table {
    height: 100%;
    display: block;
    overflow-y: scroll;
}

.dynamic-table-container {
    padding-left: 5%;
    height: 50vh;
}

.suggestions-list-ui {
    z-index: 1100;
    background: whitesmoke;
    height: auto;
    position: absolute;
}

.ui-menu-item:hover {
    background: lavender;
    cursor: pointer;
}

#pendingListModal .modal-body {
    height: 65vh;
}

.modal { overflow: auto !important; }

#edit_modal {
    z-index: 1055!important;
}

#driver_location_pick_modal {
    z-index: 1055!important;
}

.payment-option {
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    display: inline-block;
    cursor: pointer;
}

.payment-option:hover {
    border: 2px solid yellow;
}

div.Payment-Mode-Cash {
    background-image: url(../static/assets/images/elements/Cash.svg);
}

div.Payment-Mode-Credit {
    background-image: url(../static/assets/images/elements/d4m_credit.svg);
}

#customerCreditsForm .form-control.collapse {
    display: none!important;
}

#Search_Cred_Cust {
    z-index: 2;
}

.driver-list-item .thumbnail {
    width: 100%;
    padding: 0;
    margin: 0;
}

.driver-list-item .thumbnail img:hover {
    border: 1px solid skyblue;
    box-shadow: 0 0 6px 3px azure;
}

.driver-list-item .thumbnail img {
    width:70px;
    height: 70px;
    border-radius: 50%;
    cursor: pointer;
}

.driver-list-item .thumbnail .caption {
    padding: 0;
    width: 100%;
    text-align: center;
}

.picture-tab {
    text-align: center;
    cursor: pointer;
    border-radius: 12px;
    padding-bottom: 5px;
}

.picture-tab.active {
    background-image: linear-gradient(to bottom, mediumspringgreen, white);
}

.picture-area {
    height: 65vh;
    border-radius: 10px;
    background-repeat: no-repeat;
    background-size: 100% 65vh;
}

td.new-customer {
    color: red;
    font-weight: bold;
}

td.marked-customer {
    color: blue;
    font-weight: bold;
}

p.new-driver, td.new-driver {
    color: purple;
    font-weight: bold;
}


.feedUl {
    padding: 0px !important;
    list-style: none;
}
.feedLi {
    display: inline-block;
}
.feedInp {
    visibility:hidden;
}
.feedLabel {
    cursor: pointer;
}
.feedInp:checked + .feedLabel {
    background: red;
}

.has-feedback {
    background-color: green !important;
}

.book-track-driver, .book-track-driver-available {
    cursor: pointer;
}

.book-track-driver-available:hover{
  color: red;
}
.book-track-driver:hover {
    color: red;
}
.pac-container {
    z-index: 2000!important;
}

.cust-link, .driv-link {
    color: inherit;
    cursor: pointer;
}

.cust-link:hover, .driv-link:hover {
    color: inherit;
}

.ct-entry, .dt-entry {
    display: inline;
}
}
}
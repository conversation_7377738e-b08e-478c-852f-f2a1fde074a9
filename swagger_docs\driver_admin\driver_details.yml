tags:
  - Driver_admin
summary: Get Driver Details
description: >
  This endpoint allows admins to retrieve detailed information about a driver based on their unique driver ID. 
  The response includes various information about the driver, user, skills, location, verification, and banking details.
parameters:
  - name: driver_id
    in: query
    required: true
    type: string
    description: The unique identifier for the driver whose details are being retrieved.
    example: "DR123456"
responses:
  200:
    description: Successfully retrieved driver details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        driver_details:
          type: object
          properties:
            id:
              type: string
              description: The unique ID of the driver
              example: "DR123456"
            user:
              type: object
              properties:
                id:
                  type: string
                  description: User ID associated with the driver
                  example: "USR123456"
                mobile:
                  type: string
                  description: Mobile number of the user
                  example: "9876543210"
                fname:
                  type: string
                  description: User's first name
                  example: "John"
                lname:
                  type: string
                  description: User's last name
                  example: "Doe"
                email:
                  type: string
                  description: User's email address
                  example: "<EMAIL>"
                sex:
                  type: string
                  description: Gender of the user
                  example: "Male"
                registration:
                  type: string
                  description: User registration date
                  example: "2022-01-01"
                role:
                  type: string
                  description: Role of the user
                  example: "Driver"
                acc_enabled:
                  type: integer
                  description: Account status (1 if enabled)
                  example: 1
            license_no:
              type: string
              description: Driver's license number
              example: "DL1234567890"
            approved:
              type: boolean
              description: Approval status of the driver
              example: true
            available:
              type: boolean
              description: Availability status of the driver
              example: true
            rating:
              type: number
              format: float
              description: Driver's average rating
              example: 4.5
        details:
          type: object
          properties:
            rides:
              type: integer
              description: Number of rides completed by the driver
              example: 120
            rating_rides:
              type: integer
              description: Number of rides rated by customers
              example: 100
            earning:
              type: number
              format: float
              description: Total earnings of the driver
              example: 1500.00
        skill:
          type: object
          properties:
            hb_m:
              type: integer
              description: Count of high-body vehicles managed
              example: 5
            lux_m:
              type: integer
              description: Count of luxury vehicles managed
              example: 3
        location:
          type: object
          properties:
            lat:
              type: number
              format: float
              description: Latitude of the driver's location
              example: 28.6139
            lng:
              type: number
              format: float
              description: Longitude of the driver's location
              example: 77.2090
        verification:
          type: object
          properties:
            id_card:
              type: string
              description: ID card verification status
              example: "Verified"
        bank:
          type: object
          properties:
            acc_no:
              type: string
              description: Bank account number
              example: "************"
            bank_verified:
              type: boolean
              description: Bank verification status
              example: true
  400:
    description: Required parameters are missing or invalid
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Error message
          example: "driver_id parameter is required"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Error message
          example: "An error occurred while retrieving driver details"

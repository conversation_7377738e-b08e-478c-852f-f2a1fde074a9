tags:
  - Affiliate_Login
summary: Refresh Affiliate Access Token
description: >
  This endpoint refreshes the access token for an affiliate representative using a valid refresh token.
parameters:
  - name: Authorization
    in: header
    type: string
    required: true
    description: >
      The refresh token in the format `Bearer <token>` or provided in the refresh token cookie.
responses:
  200:
    description: Access token refreshed successfully.
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          description: Refresh status.
          example: true
        message:
          type: string
          description: Success message.
          example: "Refresh successful"
  401:
    description: Invalid or expired refresh token.
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          description: Refresh status.
          example: false
        message:
          type: string
          description: Error message.
          example: "Invalid refresh token"
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        refresh:
          type: boolean
          description: Refresh status.
          example: false
        message:
          type: string
          description: Error message.
          example: "Affiliate representative not found"

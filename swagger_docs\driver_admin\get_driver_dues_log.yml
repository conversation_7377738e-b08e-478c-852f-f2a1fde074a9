tags:
  - Driver_admin
summary: Log Driver Dues
description: >
  This endpoint allows admins to retrieve the dues log for a driver by providing the driver's mobile number. 
  The endpoint checks the existence of the user associated with the mobile number and retrieves the dues and 
  transaction records for that driver, returning the details in a structured format.
parameters:
  - name: mobile
    in: formData
    required: true
    type: integer
    description: Mobile number of the driver for whom the dues log is being fetched
    example: 9876543210
responses:
  200:
    description: Successfully retrieved the driver dues log
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        balance:
          type: number
          format: float
          description: Current credit balance of the user
          example: 1500.50
        data:
          type: array
          items:
            type: object
            properties:
              amt:
                type: number
                format: float
                description: Amount related to the transaction
                example: 500.00
              driver_name:
                type: string
                description: Name of the driver
                example: "John Doe"
              method:
                type: string
                description: Description of the payment method used
                example: "Credit Card"
              timestamp:
                type: string
                format: date-time
                description: Timestamp of the transaction in IST
                example: "2024-10-15T10:00:00+05:30"
              remark:
                type: string
                description: Remarks about the transaction
                example: "Payment received"
              changed_by:
                type: string
                description: Name of the admin who made the change
                example: "Admin User"
              trans_id:
                type: string
                description: Transaction ID associated with the payment
                example: "TXN123456"
              status:
                type: string
                description: Current status of the transaction
                example: "Completed"
  201:
    description: Required parameters are missing or incomplete
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for missing parameters)
          example: -1
  401:
    description: Unauthorized access or driver not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
  500:
    description: Internal server error or user not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message if the user could not be found
          example: "Could not find user"

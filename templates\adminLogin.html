<!DOCTYPE html>
<html>

<head>
    <title>Drivers4Me | Admin Login</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='bootstrap.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'admin.css') }}">
    <!--Required for glyphicons-->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">

    <script>
        window.jQuery || document.write('<script src="{{ url_for('static', filename='jquery.js') }}">\x3C/script>');
    </script>
    <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
    
    <!--<script src="{{ url_for('static', filename='') }}"></script>-->
</head>

<body style="background-color: powderblue;">
    <nav class="navbar navbar-default">
        <div class="container-fluid">
            <a id="brandName" class="navbar-brand" href="#" style="padding-left: 15px!important; padding-top: 0!important;"><img src="{{ url_for('static',filename='assets/images/brandLogoMerger.png') }}" class="img-rounded" alt="Drivers4me" style="height: 50px;"></a>
        </div>
  </nav>
  
    <div class="container-fluid" style="padding-top: 35px; background-color: rgba(0,0,0,0)!important;">
        <div class="row">
            <div class="col-lg-6 col-lg-offset-3 col-md-6 col-md-offset-3 col-sm-12 col-xs-12" style="padding: 10px; background-color: lavender; border-radius: 20px;">
                <h2>Admin Login</h2><hr class="d4m-ending">
                <form id="adminLoginForm" enctype="multipart/form-data" autocomplete="off" style="padding: 10px;">
                    <div class="row">
                        <div class="col-lg 6 col-md-4 col-sm-12 col-xs-12 form_element"><p>Phone Number</p></div>
                        <div class="col-lg 6 col-md-6 col-sm-12 col-xs-12 form_element"><input name="mobile" type="mobile" class="form-control" id="mobile" placeholder="Phone number" maxlength="10" onkeydown="return checkKey(event)" /></div>
                    </div>
                    <div class="row">
                        <div class="col-lg 6 col-md-4 col-sm-12 col-xs-12 form_element"><p>Password</p></div>
                        <div class="col-lg 6 col-md-6 col-sm-12 col-xs-12 form_element">
                            <div class="input-group">
                                <input type="password" name="pwd" class="form-control" id="pwd" placeholder="Password" />
                                <span class="input-group-addon" style="cursor: pointer;"><i id="custVisib" class="glyphicon glyphicon-eye-open"></i></span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-offset-8 col-md-offset-8 col-lg 4 col-md-4 col-sm-12 col-xs-12 form_element"><button id="login" type="button" class="btn btn-success">Login</button></div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!--info modal-->
 <div class="modal fade" id="infoModal" role="dialog" style="border-radius: 5px; width: 100%; top: 30%;">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header" style="border-radius: 5px; background: lemonchiffon; box-shadow: 0 0 10px 1px black;">
          <center><h4 class="modal-title"></h4><center>
        </div>
      </div>
      
    </div>
  </div>


    <script type='text/javascript'>
        function checkKey(e) {
            if(e.shiftKey)
                return false;
            if(e.ctrlKey||e.keyCode==9||e.keyCode==18||e.keyCode==13)
                return true;
            return e.keyCode>=46&&e.keyCode<=57||e.keyCode==8||e.keyCode>=96&&e.keyCode<=105;
        }
 </script>
 <script src="{{ url_for('static',filename='adminLogin.js') }}"></script>
</body>
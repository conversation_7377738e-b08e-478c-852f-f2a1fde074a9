from main import app
from datetime import datetime, timedelta
from _email import send_mail
from db_config import db,mdb
import os
from models import DriverStrike, Drivers, StrikeReason,Users
from _fcm import send_fcm_msg_driver
import _sms
def resolve_expired_strikes():
    now = datetime.utcnow()

    # Explicitly join StrikeReason and Drivers
    expired_strikes = db.session.query(DriverStrike, StrikeReason, Drivers, Users).\
        join(StrikeReason, DriverStrike.reason == StrikeReason.id).\
        join(Drivers, DriverStrike.driver_id == Drivers.id).\
        join(Users, Drivers.user == Users.id).\
        filter(
            DriverStrike.resolved == False,
            DriverStrike.expires_at <= now
        ).all()

    if not expired_strikes:
        print("No expired strikes to resolve.", flush=True)
        return
                                                                                                
    for strike, reason, driver, user in expired_strikes:
        if driver.approved == Drivers.BANNED:
            continue
        strike.resolved = True
        strike.resolved_at = datetime.utcnow()
        strike_weight = reason.strike_weight or 0
        driver.total_strike_count = max(driver.total_strike_count - strike_weight, 0)
        reason = strike.strike_reason
        fcm_title = "Strike Resolved"
        fcm_smalltext = f"Your strike for '{reason.brief}' has been resolved."
        fcm_bigtext = ( f"The strike issued for '{reason.brief}' has been marked resolved." )
        send_fcm_msg_driver( driver.id,title=fcm_title,smalltext=fcm_smalltext,bigtext=fcm_bigtext)
        msg="The strike issued for {reason.brief} has been marked resolved.You have {driver.total_strike_count} strikes left"
        #_sms.send_bulk_message_gupshup([])
        ## strike You’ve been fined {#var#} by Drivers4Me for {#var#}. A ban will apply if you get {#var#} more strikes. 
    db.session.commit()

if __name__ == '__main__':
    with app.app_context():
        resolve_expired_strikes()

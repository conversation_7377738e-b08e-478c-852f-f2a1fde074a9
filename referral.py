#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  acc_profile.py
#
#  Copyright 2017-2020 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from _utils import get_ref_code, complete
from _utils_acc import account_enabled, validate_role
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt_identity, get_jwt
)
from models import Users, UserTrans, Drivers, DriverDetails, DriverTrans, ReferralUse, db, Trip, Bookings

referral = Blueprint('referral', __name__)

def set_user_ref_code(user_id, user_mobile):
    ref_code = get_ref_code(user_id, user_mobile)
    db.session.query(Users).filter(Users.id == user_id).update({Users.ref_code: ref_code})
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        ref_code = ""
    return ref_code


def get_user_ref_code(user_details):
    if not user_details.ref_code:
        ref_code = set_user_ref_code(user_details.id, user_details.mobile)
    else:
        ref_code = user_details.ref_code
    return ref_code
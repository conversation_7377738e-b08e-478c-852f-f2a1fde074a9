import warnings
warnings.filterwarnings("ignore")
import requests
D4M_URL = "https://staging.drivers4me.com/"

ZC_LOGIN = D4M_URL + "api/affiliate/zoomcar/login"
ZC_BOOK = D4M_URL + "api/affiliate/zoomcar/book"
payload = {
        "user_name": "zc_central",
        "password": "zoomcar@123"
}

print(ZC_LOGIN)
resp = requests.request("POST", url=ZC_LOGIN, data=payload, verify=False)
access_token = resp.json().get("data").get("api_token")
#print(access_token)
#access_token = """eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE2NTM5ODg3MzAsIm5iZiI6MTY1Mzk4ODczMCwianRpIjoiODliNjJmNjUtNzdhNS00ZDRiLTg2M2EtOTZkY2ZkNGUyZWRmIiwiZXhwIjoxNjU0MDc1MTMwLCJpZGVudGl0eSI6MzM2NTgsImZyZXNoIjpmYWxzZSwidHlwZSI6ImFjY2VzcyIsInVzZXJfY2xhaW1zIjp7InJvbGVzIjo1LCJ1c2VyYWdlbnQiOiJab29tY2FyLUFQSSJ9LCJjc3JmIjoiMWM1Yzc1YzktMzNiYy00NDdjLWE4MWQtYmE1ZmRiNTFmODNmIn0.6VwOF0nd5lUHMTQ4AZoU3Z47um"""
payload = {
    "data": {
        "booking_id": "w8f5jyQs1AmIls3fwDPnDQ",
        "booking_type": "0",
        "city": "0",
        "pickup_lat": "22.6116",
        "pickup_lng": "88.4257",
        "pickup_address": "CA 3, Desh Bandhu Nagar, Baguiati, Kolkata, West Bengal 700059, India",
        "drop_lat": "22.6130",
        "drop_lng": "88.4259",
        "drop_address": "CA 3, Desh Bandhu Nagar, Baguiati, Kolkata, West Bengal 700059, India",
        "start_datetime": "2022-05-27 20:00:00",
        "veh_number": "DL2CAV9999",
        "source_spoc_phone": "9999999999",
        "source_spoc_name": "TEST source spoc",
        "dest_spoc_phone": "9999999999",
        "dest_spoc_name": "TEST dest spoc"
    }
}
#access_token = "foo"
# header =  {"Authorization": "Bearer %s" % access_token}
# resp = requests.request("POST", url=ZC_BOOK, data=payload.get("data"), headers=header, verify=False)
# print(resp.text)
"""
import time
t=time.time()
payload = { "reference_id": "BWS34O" }
ZC_CANCEL = D4M_URL + "api/affiliate/zoomcar/cancel"
resp = requests.request("POST", url=ZC_CANCEL, data=payload, headers=header, verify=False)
print(resp.text, time.time() -t)
"""

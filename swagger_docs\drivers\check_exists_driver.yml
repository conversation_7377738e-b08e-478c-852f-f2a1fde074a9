tags:
  - Driver
summary: Check if a driver exists based on mobile number.
description: >
  This API checks whether a user with a given mobile number exists and if the user is a registered driver. It returns the existence status of the driver and user.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user to check if they exist as a driver.
    example: "9876543210"
responses:
  200_a:
    description: Driver exists.
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: 1
        user:
          type: integer
          example: 1
        message:
          type: string
          example: "Driver exist"
    examples:
      application/json:
        exists: 1
        user: 1
        message: "Driver exist"
  200_b:
    description: Driver does not exist but user exists.
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: 0
        user:
          type: integer
          example: 1
        message:
          type: string
          example: "Driver does not exist"
    examples:
      application/json:
        exists: 0
        user: 1
        message: "Driver does not exist"
  200_c:
    description: User not found.
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: 0
        user:
          type: integer
          example: 0
        message:
          type: string
          example: "User not found"
    examples:
      application/json:
        exists: 0
        user: 0
        message: "User not found"
  401:
    description: User account is restricted.
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: -2
        user:
          type: integer
          example: -2
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        exists: -2
        user: -2
        message: "User restricted"
  201:
    description: Server error.
    schema:
      type: object
      properties:
        exists:
          type: integer
          example: -1
        user:
          type: integer
          example: 0
        message:
          type: string
          example: "Server error"
    examples:
      application/json:
        exists: -1
        user: 0
        message: "Server error"

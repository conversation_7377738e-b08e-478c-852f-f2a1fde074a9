//all JS functions related to website analytics
$(document).ready(function() {
    //color array for bar-charts
    window.chartColors = ['#19607C', '#033E5B', '#d16f05', '#0b5b0b'];
    //chart types
    window.chartTypes = ['Total Sales', 'Trips', 'Revenue'];
    //chart timescale
    window.chartTimeScales = ['Daily', 'Weekly', 'Monthly', 'Yearly'];
    $("#analytics").click(function() {
        //getCurrentStats();
    });
    $("#refreshAnalytics").click(function () {
        //number of successful trips conducted
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/completed_trips',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //get the count
                    $("#completedTrips").html(response['completed']);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });

        //revenue details
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/revenue',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //get the collected revenue
                    $("#revenue_collected").html(response['collected']);
                    //get total revenue
                    $("#revenue_generated").html(parseInt(response['uncollected']) + parseInt(response['collected']));
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });
        //total sales
        //revenue details
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/sales',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //get the collected revenue
                    $("#total_sales").html(response['sales']);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });

        //user count
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/user_count',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //get number of customers
                    $("#customer_count").html(response['customer_count']);
                    //get number of drivers
                    $("#driver_count").html(response['driver_count']);
                    //get number of active drivers
                    $("#active_driver_count").html(response['active_driver_count']);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });
    });

    //handle chart type toggle
    $("#previousChart").click(function() {
        var curChartType = parseInt(getChartTypeValue($("#chartHeader").find('.chart-type').html()));
        if(curChartType == 0) curChartType = window.chartTypes.length;
        showChartType((curChartType - 1) % window.chartTypes.length);
    });

    $("#nextChart").click(function() {
        var curChartType = parseInt(getChartTypeValue($("#chartHeader").find('.chart-type').html()));
        showChartType((curChartType + 1) % window.chartTypes.length);
    });

    $(".chart-timescale-change").click(function() {
        var chartType = $(".chart-type").html().replace(' ', '_');
        var incrDcr = -1;
        if($(this).attr('id').indexOf('out') >= 0) {
            //zoom out function
            incrDcr = 1;
        }
        var currTimeScale = parseInt(getTimeScaleValue($("#chartHeader").find('.timescale-type').html()));
        if(currTimeScale == 0 && incrDcr == -1) currTimeScale = window.chartTimeScales.length;
        var newTimeScale = window.chartTimeScales[(currTimeScale + incrDcr) % window.chartTimeScales.length];
        var targetChart = "#" + newTimeScale + '-' + chartType + '_Chart';
        //disable all charts
        $(".trend-charts").hide();
        $(".trend-charts").closest('div').attr('class', 'col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse');
        //enable charts for this trend
        $(targetChart).show();
        $(targetChart).closest('div').attr('class', 'col-lg-10 col-md-10 col-sm-10 col-xs-10');
        //change timescale text
        $("#chartHeader").find('.timescale-type').html(newTimeScale);
    });

    $('body').delegate("#refresh-Total_Sales_Chart",'click',function() {
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/all_sales',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //create year chart
                    var ytsc = document.getElementById("Yearly-Total_Sales_Chart").getContext('2d');
                    var Yearly_Total_Sales_Chart = new Chart(ytsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Total Sales (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create month chart
                    var mtsc = document.getElementById("Monthly-Total_Sales_Chart").getContext('2d');
                    var Monthly_Total_Sales_Chart = new Chart(mtsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Total Sales (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create weekly chart
                    var wtsc = document.getElementById("Weekly-Total_Sales_Chart").getContext('2d');
                    var Weekly_Total_Sales_Chart = new Chart(wtsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Total Sales (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create daily chart
                    var dtsc = document.getElementById("Daily-Total_Sales_Chart").getContext('2d');
                    var Daily_Total_Sales_Chart = new Chart(dtsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Total Sales (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //get sales data by year
                    for (i = 0; i < response['year_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['year_data'][i]);
                        //put month label
                        Yearly_Total_Sales_Chart.data.labels.push(this_entry.year);
                        //put sale data for year
                        Yearly_Total_Sales_Chart.data.datasets[0].data.push(this_entry.sales);
                    }
                    //get sales data by month
                    for (i = 0; i < response['month_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['month_data'][i]);
                        //put month label
                        Monthly_Total_Sales_Chart.data.labels.push(getMonthName(this_entry.month) + " , " + this_entry.year);
                        //put sale data for month
                        Monthly_Total_Sales_Chart.data.datasets[0].data.push(this_entry.sales);
                    }
                    //get sales data by week
                    for (i = 0; i < response['week_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['week_data'][i]);
                        //put month label
                        Weekly_Total_Sales_Chart.data.labels.push(getWeekBounds(this_entry.week - 1, this_entry.year));
                        //put sale data for week
                        Weekly_Total_Sales_Chart.data.datasets[0].data.push(this_entry.sales);
                    }
                    //get sales data by day
                    for (i = 0; i < response['day_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['day_data'][i]);
                        //put day label
                        Daily_Total_Sales_Chart.data.labels.push(this_entry.day);
                        //put sale data for day
                        Daily_Total_Sales_Chart.data.datasets[0].data.push(this_entry.sales);
                    }
                    Yearly_Total_Sales_Chart.data.datasets[0].backgroundColor = "rgba(0, 0, 255, 0.3)";
                    Yearly_Total_Sales_Chart.data.datasets[0].borderColor = window.chartColors[0];
                    Yearly_Total_Sales_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[0];
                    Yearly_Total_Sales_Chart.update();
                    Monthly_Total_Sales_Chart.data.datasets[0].backgroundColor = "rgba(0, 0, 255, 0.3)";
                    Monthly_Total_Sales_Chart.data.datasets[0].borderColor = window.chartColors[0];
                    Monthly_Total_Sales_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[0];
                    Monthly_Total_Sales_Chart.update();
                    Weekly_Total_Sales_Chart.data.datasets[0].backgroundColor = "rgba(0, 0, 255, 0.3)";
                    Weekly_Total_Sales_Chart.data.datasets[0].borderColor = window.chartColors[0];
                    Weekly_Total_Sales_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[0];
                    Weekly_Total_Sales_Chart.update();
                    Daily_Total_Sales_Chart.data.datasets[0].backgroundColor = "rgba(0, 0, 255, 0.3)";
                    Daily_Total_Sales_Chart.data.datasets[0].borderColor = window.chartColors[0];
                    Daily_Total_Sales_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[0];
                    Daily_Total_Sales_Chart.update();
                    //show timescale
                    $(".chart-timescale-change").attr('class', 'col-lg-1 col-md-1 col-sm-1 col-xs-1 chart-timescale-change');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });
    });

    $('body').delegate("#refresh-Trips_Chart",'click',function() {
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/all_trips',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //create year chart
                    var ytrsc = document.getElementById("Yearly-Trips_Chart").getContext('2d');
                    var Yearly_Trips_Chart = new Chart(ytrsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Completed Trips',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create month chart
                    var mtrsc = document.getElementById("Monthly-Trips_Chart").getContext('2d');
                    var Monthly_Trips_Chart = new Chart(mtrsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Completed Trips',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create weekly chart
                    var wtrsc = document.getElementById("Weekly-Trips_Chart").getContext('2d');
                    var Weekly_Trips_Chart = new Chart(wtrsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Completed Trips',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create daily chart
                    var dtrsc = document.getElementById("Daily-Trips_Chart").getContext('2d');
                    var Daily_Trips_Chart = new Chart(dtrsc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Completed',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //get trips data by year
                    for (i = 0; i < response['year_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['year_data'][i]);
                        //put month label
                        Yearly_Trips_Chart.data.labels.push(this_entry.year);
                        //put trips data for year
                        Yearly_Trips_Chart.data.datasets[0].data.push(this_entry.trips);
                    }
                    //get trips data by month
                    for (i = 0; i < response['month_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['month_data'][i]);
                        //put month label
                        Monthly_Trips_Chart.data.labels.push(getMonthName(this_entry.month) + " , " + this_entry.year);
                        //put trips data for month
                        Monthly_Trips_Chart.data.datasets[0].data.push(this_entry.trips);
                    }
                    //get trips data by week
                    for (i = 0; i < response['week_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['week_data'][i]);
                        //put month label
                        Weekly_Trips_Chart.data.labels.push(getWeekBounds(this_entry.week - 1, this_entry.year));
                        //put trips data for week
                        Weekly_Trips_Chart.data.datasets[0].data.push(this_entry.trips);
                    }
                    //get trips data by day
                    for (i = 0; i < response['day_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['day_data'][i]);
                        //put day label
                        Daily_Trips_Chart.data.labels.push(this_entry.day);
                        //put trips data for day
                        Daily_Trips_Chart.data.datasets[0].data.push(this_entry.trips);
                    }
                    Yearly_Trips_Chart.data.datasets[0].backgroundColor = "rgba(255, 165, 0, 0.4)";
                    Yearly_Trips_Chart.data.datasets[0].borderColor = window.chartColors[2];
                    Yearly_Trips_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[2];
                    Yearly_Trips_Chart.update();
                    Monthly_Trips_Chart.data.datasets[0].backgroundColor = "rgba(255, 165, 0, 0.4)";
                    Monthly_Trips_Chart.data.datasets[0].borderColor = window.chartColors[2];
                    Monthly_Trips_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[2];
                    Monthly_Trips_Chart.update();
                    Weekly_Trips_Chart.data.datasets[0].backgroundColor = "rgba(255, 165, 0, 0.4)";
                    Weekly_Trips_Chart.data.datasets[0].borderColor = window.chartColors[2];
                    Weekly_Trips_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[2];
                    Weekly_Trips_Chart.update();
                    Daily_Trips_Chart.data.datasets[0].backgroundColor = "rgba(255, 165, 0, 0.4)";
                    Daily_Trips_Chart.data.datasets[0].borderColor = window.chartColors[2];
                    Daily_Trips_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[2];
                    Daily_Trips_Chart.update();
                    //show timescale
                    $(".chart-timescale-change").attr('class', 'col-lg-1 col-md-1 col-sm-1 col-xs-1 chart-timescale-change');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });
    });

    $('body').delegate("#refresh-Revenue_Chart",'click',function() {
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/all_revenues',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //create year chart
                    var yrc = document.getElementById("Yearly-Revenue_Chart").getContext('2d');
                    var Yearly_Revenues_Chart = new Chart(yrc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Generated Revenue (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create month chart
                    var mrc = document.getElementById("Monthly-Revenue_Chart").getContext('2d');
                    var Monthly_Revenues_Chart = new Chart(mrc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Generated Revenue (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create weekly chart
                    var wrc = document.getElementById("Weekly-Revenue_Chart").getContext('2d');
                    var Weekly_Revenues_Chart = new Chart(wrc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Generated Revenue (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //create daily chart
                    var drc = document.getElementById("Daily-Revenue_Chart").getContext('2d');
                    var Daily_Revenues_Chart = new Chart(drc, {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'Generated Revenue (₹)',
                                borderWidth: 2
                            }]
                        },
                        options: {
                            scales: {
                                yAxes: [{
                                    ticks: {
                                        beginAtZero:true
                                    }
                                }]
                            },
                            elements: {
                                line: {
                                    tension: 0, // disables bezier curves
                                }
                            },
                            responsive: true
                        }
                    });
                    //get revenues data by year
                    for (i = 0; i < response['year_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['year_data'][i]);
                        //put month label
                        Yearly_Revenues_Chart.data.labels.push(this_entry.year);
                        //put revenues data for year
                        Yearly_Revenues_Chart.data.datasets[0].data.push(this_entry.revenues);
                    }
                    //get revenues data by month
                    for (i = 0; i < response['month_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['month_data'][i]);
                        //put month label
                        Monthly_Revenues_Chart.data.labels.push(getMonthName(this_entry.month) + " , " + this_entry.year);
                        //put revenues data for month
                        Monthly_Revenues_Chart.data.datasets[0].data.push(this_entry.revenues);
                    }
                    //get revenues data by week
                    for (i = 0; i < response['week_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['week_data'][i]);
                        //put month label
                        Weekly_Revenues_Chart.data.labels.push(getWeekBounds(this_entry.week - 1, this_entry.year));
                        //put revenues data for week
                        Weekly_Revenues_Chart.data.datasets[0].data.push(this_entry.revenues);
                    }
                    //get revenues data by day
                    for (i = 0; i < response['day_data'].length; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['day_data'][i]);
                        //put day label
                        Daily_Revenues_Chart.data.labels.push(this_entry.day);
                        //put revenues data for day
                        Daily_Revenues_Chart.data.datasets[0].data.push(this_entry.revenues);
                    }
                    Yearly_Revenues_Chart.data.datasets[0].backgroundColor = "rgba(0, 255, 0, 0.4)";
                    Yearly_Revenues_Chart.data.datasets[0].borderColor = window.chartColors[3];
                    Yearly_Revenues_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[3];
                    Yearly_Revenues_Chart.update();
                    Monthly_Revenues_Chart.data.datasets[0].backgroundColor = "rgba(0, 255, 0, 0.4)";
                    Monthly_Revenues_Chart.data.datasets[0].borderColor = window.chartColors[3];
                    Monthly_Revenues_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[3];
                    Monthly_Revenues_Chart.update();
                    Weekly_Revenues_Chart.data.datasets[0].backgroundColor = "rgba(0, 255, 0, 0.4)";
                    Weekly_Revenues_Chart.data.datasets[0].borderColor = window.chartColors[3];
                    Weekly_Revenues_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[3];
                    Weekly_Revenues_Chart.update();
                    Daily_Revenues_Chart.data.datasets[0].backgroundColor = "rgba(0, 255, 0, 0.4)";
                    Daily_Revenues_Chart.data.datasets[0].borderColor = window.chartColors[3];
                    Daily_Revenues_Chart.data.datasets[0].pointBackgroundColor = window.chartColors[3];
                    Daily_Revenues_Chart.update();
                    //show timescale
                    $(".chart-timescale-change").attr('class', 'col-lg-1 col-md-1 col-sm-1 col-xs-1 chart-timescale-change');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

        });
    });

});

function getCookie() {
    cookieSplit = document.cookie.split("; ");
    var cookieObject = {};
    cookieSplit.forEach( function(value, index) {
       var splitResult = value.split("=");
       cookieObject[splitResult[0]] = splitResult[1];
    });
    return cookieObject;
}

function checkRefresh(csrf_token, refresh_token) {
    /*var resp = false;
    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/token/verify',
        beforeSend: function(request) {
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        async: false,
        success: function(s) {
            if (s.success != true) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                    beforeSend: function(request) {
                        request.setRequestHeader('X-CSRF-Token', refresh_token);
                    },
                    async: false,
                    success: function(sr) {
                        if (sr.refresh != true) resp =  false;
                        else resp = true;
                    },
                    error: function(er) {
                        resp =  false;
                    }
                });
            } else {
                resp =  true;
            }
        },
        error: function(e) {
            if (e.status == 401) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                    beforeSend: function(request) {
                        request.setRequestHeader('X-CSRF-Token', refresh_token);
                    },
                    async: false,
                    success: function(sr) {
                        if (sr.refresh != true) resp = false;
                        else resp =  true;
                    },
                    error: function(er) {
                        resp = false;
                    }
                });
            } else {
                resp =  false;
            }
        }
    });*/
    return;
}

function getCurrentStats(category) {

    //clear stats
    $(".all-new-booking-count").html("0");
    $(".all-upcoming-booking-count").html("0");
    $(".all-ongoing-booking-count").html("0");
    $(".new-booking-count").html("0");
    $(".upcoming-booking-count").html("0");
    $(".onmyway-booking-count").html("0");
    $(".ongoing-booking-count").html("0");
    $(".successful-booking-count").html("0");
    $(".successful-booking-count").html("0");
    $(".successful-booking-count").html("0");
    $(".successful-booking-count").html("0");
    $(".sales-today").html("0");
    var region = parseInt(getCookie()["region"])
    var data=new FormData();
    data.append('category', category)
    data.append('region', region)
    //get today's stats
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/daily_stats',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //get today's counts
                    $(".all-new-booking-count").html(response['new_count']);
                    $(".all-upcoming-booking-count").html(response['upcoming_count']);
                    $(".all-ongoing-booking-count").html(response['ongoing_count']);
                    $(".new-booking-count").html(response['new_today_count']);
                    $(".upcoming-booking-count").html(response['upcoming_today_count']);
                    $(".onmyway-booking-count").html(response['omw_count']);
                    $(".ongoing-booking-count").html(response['ongoing_today_count']);
                    $(".successful-booking-count").html(response['success_today_count']);
                    $(".d4m-cancel-booking-count").html(response['d4m_cancelled_count']);
                    $(".quick-cancel-booking-count").html(response['cancelled_before_alloc']);
                    $(".customer-cancel-booking-count").html(response['cancelled_after_alloc']);
                    //based on sales, change label apprearance
                    var sale_fig_color = "rgb(200, 200, 200)";
                    var sales = parseFloat(response['sales']);
                    //handle null sales
                    if(isNaN(sales)) sales = 0;
                    $(".sales-today").html(sales);
                    if(sales >= 500 && sales < 1000) {
                        sale_fig_color = "lightsalmon";
                    }
                    else if(sales >= 1000 && sales < 2000) {
                        sale_fig_color = "orange";
                    }
                    else if(sales >= 2000 && sales < 3000) {
                        sale_fig_color = "yellow";
                    }
                    else if(sales >= 3000 && sales < 5000) {
                        sale_fig_color = "lime";
                    }
                    else if(sales >= 5000 && sales < 7500) {
                        sale_fig_color = "mediumaquamarine";
                    }
                    else if(sales >= 7500 && sales < 10000) {
                        sale_fig_color = "aquamarine";
                    }
                    else if(sales >= 10000) {
                        sale_fig_color = "gold";
                    }
                    $(".sales-today").css('background-color', sale_fig_color);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            }

    });
}

function getChartColor(colorPos) {
    return window.chartColors[colorPos];
}

function getMonthName(monthPos) {
    monthPos = parseInt(monthPos);
    if(monthPos >=1 && monthPos <= 12) {
        var tempDate = new Date();
        tempDate.setMonth(monthPos - 1);
        return tempDate.toLocaleString('en-GB',{month: 'long'});
    }
    return 'Unknown';
}

function getTimeScaleValue(timeScaleString) {
    var scale = window.chartTimeScales.indexOf(timeScaleString);
    if(scale < 0) return 0;
    return scale;
}

function getChartTypeValue(chartTypeString) {
    var type = window.chartTypes.indexOf(chartTypeString);
    if(type < 0) return 0;
    return type;
}

function showChartType(chartType) {
    //change header
    $("#chartHeader").find(".chart-type").html(window.chartTypes[chartType]);
    var targetRefresh = 'refresh-' + window.chartTypes[chartType].replace(' ', '_') + '_Chart';
    var targetChart = "#Monthly-" + targetRefresh.replace('refresh-', '');
    $(".chart-refresh").attr('id', targetRefresh);
    $(".chart-type").html(window.chartTypes[chartType]);
    $(".timespan-type").html("Monthly");
    //disable all charts
    $(".trend-charts").hide();
    $(".trend-charts").closest('div').attr('class', 'col-lg-10 col-md-10 col-sm-10 col-xs-10 collapse');
    //enable charts for this trend
    $(targetChart).show();
    $(targetChart).closest('div').attr('class', 'col-lg-10 col-md-10 col-sm-10 col-xs-10');
}

function getWeekBounds(week, year) {
    var curr = new Date(year, 0, week * 7);
    var firstday = new Date(curr.setDate(curr.getDate() - curr.getDay() + 1));
    var lastday = new Date(curr.setDate(curr.getDate() - curr.getDay()+7));
    firstYear = firstday.toLocaleString('en-GB', {   year: "numeric"});
    lastYear = lastday.toLocaleString('en-GB', {   year: "numeric"});
    if(firstYear == lastYear) {
        formattedFirstDay = firstday.toLocaleString('en-GB', {
                                                                month: "numeric",
                                                                day: "numeric"
                                                            });
        formattedLastDay = lastday.toLocaleString('en-GB', {
                                                                month: "numeric",
                                                                day: "numeric"
                                                            });
        return formattedFirstDay + "---" + formattedLastDay + "," + firstYear;
    }
    else {
        formattedFirstDay = firstday.toLocaleString('en-GB', {
                                                                year: "numeric",
                                                                month: "numeric",
                                                                day: "numeric"
                                                            });
        formattedLastDay = lastday.toLocaleString('en-GB', {
                                                                year: "numeric",
                                                                month: "numeric",
                                                                day: "numeric"
                                                            });
        return formattedFirstDay + "---" + formattedLastDay;
    }
}
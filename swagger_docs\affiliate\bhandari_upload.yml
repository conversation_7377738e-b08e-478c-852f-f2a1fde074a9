tags:
  - Affiliate
summary: Upload a Bhandari image for a driver.
description: This API allows a driver to upload an image for a Bhandari booking.
parameters:
  - name: booking_id
    in: formData
    description: The ID of the Bhandari booking.
    required: true
    type: integer
  - name: index
    in: formData
    description: The index for the image being uploaded.
    required: true
    type: integer
  - name: pic
    in: formData
    description: The image file to be uploaded.
    required: true
    type: file
responses:
  200_a:
    description: Bhandari image uploaded successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Bhandari image uploaded successfully"
    examples:
      application/json:
        success: 1
        msg: "Bhandari image uploaded successfully"
  201_a:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        msg:
          type: string
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        msg: "Incomplete form details"
  401_a:
    description: Unauthorized role, Not Driver.
    schema:
      type: object
      properties:
        error:
          type: integer
          example: 1
        msg:
          type: string
          example: "Unauthorized role"
    examples:
      application/json:
        error: 1
        msg: "Unauthorized role"
  401_b:
    description: User restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        msg: "User restricted"
  401_c:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Server error"
    examples:
      application/json:
        success: -1
        msg: "Server error"
  500_a:
    description: DB Error while committing data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        reason:
          type: string
          example: "DB Error"
        msg:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        reason: "DB Error"
        msg: "DB Error"
  500_b:
    description: Another DB error occurred during image upload.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        reason:
          type: string
          example: "DB Error"
        msg:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        reason: "DB Error"
        msg: "DB Error"

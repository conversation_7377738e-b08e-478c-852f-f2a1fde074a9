tags:
  - Affiliate_Login
summary: Affiliate authentication
description: >
  This endpoint allows affiliate representatives to log in using their mobile number and password or OTP. 
  Provides access and refresh tokens upon successful authentication.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the affiliate representative.
  - name: pwd
    in: formData
    type: string
    required: true
    description: The password or OTP for authentication.
  - name: type
    in: formData
    type: integer
    required: false
    description: >
      Type of authentication method. 
      0 for OTP (default), 1 for password-based authentication.
responses:
  200:
    description: Successfully authenticated affiliate representative.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        rep_id:
          type: integer
          description: ID of the affiliate representative.
          example: 101
        aff_id:
          type: integer
          description: ID of the affiliate.
          example: 1001
        aff_name:
          type: string
          description: Name of the affiliate.
          example: "Affiliate Corp"
        display_name:
          type: string
          description: Display name of the affiliate.
          example: "Affiliate Corp Display"
        rep_username:
          type: string
          description: Username of the affiliate representative.
          example: "johndoe"
        rep_mobile:
          type: string
          description: Mobile number of the affiliate representative.
          example: "1234567890"
        rep_email:
          type: string
          description: Email of the affiliate representative.
          example: "<EMAIL>"
        region:
          type: string
          description: Region associated with the affiliate.
          example: "North America"
        profile:
          type: string
          description: URL to the affiliate's logo/profile picture.
          example: "https://example.com/logo.png"
        access_token:
          type: string
          description: Access token for the affiliate representative.
          example: "eyJhbGciOiJIUzI1..."
  400:
    description: Missing required fields or invalid input.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        msg:
          type: string
          description: Error message.
          example: "Missing required fields"
  401:
    description: Invalid credentials, OTP, or token entry failure.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for invalid credentials, 0 for token failure).
          example: -3
        msg:
          type: string
          description: Error message.
          example: "Invalid credentials or OTP"
  403:
    description: First-time login requiring password reset.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for first-time login error).
          example: -2
        msg:
          type: string
          description: Error message.
          example: "First-time login. Please reset your password."
  404:
    description: Affiliate representative not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        msg:
          type: string
          description: Error message.
          example: "Affiliate representative not found"
  500:
    description: Internal server error or database failure.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error).
          example: -1
        msg:
          type: string
          description: Error message.
          example: "DB Error"

tags:
  - Affiliate_Profile
summary: Get All Addresses
description: >
  Fetches a list of all local and global addresses for the affiliate representative.
responses:
  200:
    description: List of addresses.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        addresses:
          type: array
          items:
            type: object
            properties:
              add_id:
                type: integer
                example: 1
              address:
                type: string
                example: 123 Main St.
              nickname:
                type: string
                example: Home
              lat:
                type: number
                example: 37.7749
              long:
                type: number
                example: -122.4194
              add_type:
                type: string
                example: local_address
              spocs:
                type: array
                items:
                  type: object
                  properties:
                    spoc_id:
                      type: integer
                      example: 1
                    spoc_name:
                      type: string
                      example: <PERSON>
  401:
    description: Unauthorized.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Unauthorized.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: An error occurred.

tags:
  - Affiliate_Profile
summary: Delete SPOC Data
description: >
  This endpoint allows an affiliate representative to delete an existing SPOC.
parameters:
  - name: spoc_id
    in: formData
    type: integer
    required: true
    description: The ID of the SPOC to delete.
responses:
  200:
    description: SPOC deleted successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: SPOC deleted successfully.
        spoc_id:
          type: integer
          example: 1
  400:
    description: Missing SPOC ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: Missing spoc id.
  404:
    description: SPOC not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: SPOC not found.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        error:
          type: string
          example: An error occurred.

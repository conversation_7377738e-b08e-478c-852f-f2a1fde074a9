tags:
  - Driver_admin
summary: Reverify Driver's Driving License
description: >
  This endpoint re-verifies a driver's driving license details. It allows forced verification if needed, or it matches the license details with external verification services to ensure the accuracy of the driver's information. The re-verification process includes face matching, name matching, date of birth matching, and license expiry checks.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose driving license is to be re-verified
    example: 101
  - name: remarks
    in: formData
    required: true
    type: string
    description: Remarks or notes provided during the re-verification process
    example: "Forced verification due to dispute"
  - name: force_verify
    in: formData
    required: false
    type: integer
    description: Flag indicating whether to force verification (1 for force verify, 0 for normal verification)
    example: 1
responses:
  200:
    description: Successfully reverified the driver's driving license
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the re-verification process (1 for successful verification, 0 for disputed, 3 for forced verification)
          example: 1
        message:
          type: string
          description: Message describing the outcome of the re-verification
          example: "Driving Licence Verified"
        details:
          type: object
          description: Re-verification details including name match, photo match, DOB match, and license expiry match
          properties:
            name_match:
              type: boolean
              description: Indicates whether the name matches
              example: true
            photo_match:
              type: boolean
              description: Indicates whether the photo matches
              example: true
            dob_match:
              type: boolean
              description: Indicates whether the date of birth matches
              example: true
            lic_exp_match:
              type: boolean
              description: Indicates whether the license expiry date matches
              example: true
            face_match_score:
              type: number
              format: float
              description: The score of the face match comparison
              example: 95.5
  400:
    description: Bad request (re-verification failed)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for errors)
          example: -1
        message:
          type: string
          description: Error message
          example: "Driving Licence Failed to Reverify"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-2 for server error)
          example: -2
        message:
          type: string
          description: Error message describing the server issue
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Internal server error"

from flask import Blueprint, jsonify, request
from _utils import complete, convert_datetime_to_utc
from flasgger import swag_from
import requests
from flask_jwt_extended import (jwt_required, get_jwt)
import random
import json
from models import db, Users, Drivers, Bookings, CallLog, CallRequestLog
from booking_params import BookingParams

callMasking = Blueprint('call_masking', __name__)

# function to generate unique did number
def assign_unique_did(cust_did_list, drv_did_list, did_list=BookingParams.DID_LIST):

    # set of current assigned did to cust and driver
    cust_did_set = set(cust_did_list)
    driver_did_set = set(drv_did_list)

    assigned_did_set = cust_did_set.union(driver_did_set)

    complete_did_set = set(did_list)

    unassigned_did = list(complete_did_set - assigned_did_set)

    random.shuffle(unassigned_did)

    # generate a unique did
    return random.choice(unassigned_did) if unassigned_did else -1

@callMasking.route('/api/masking/direct_calling', methods=['POST'])
def direct_calling():
    
    if not complete(request.form, ['uuid', 'call_to_number', 'call_id', 'caller_id_number', 'start_stamp']):
        return jsonify({"success": False, "message": "Missing required parameters"}), 400
        
    prefix = "+"
    uuid = request.form['uuid']
    call_to_number = prefix + request.form['call_to_number'] # Did no
    call_id = request.form['call_id']
    caller_id_number = request.form['caller_id_number']
    start_stamp = request.form['start_stamp']

    mobile = caller_id_number[-10:]
    print("Mobile", mobile)

    user = db.session.query(Users).filter(Users.mobile == mobile).first()
    # not registered user
    if not user:
        transfer_response = [{
                "recording": {
                    "type": "system",
                    "data": "152895" # recording id when user is not registered with us
                }
            }]
        return json.dumps(transfer_response)
    is_driver = user.role == Users.ROLE_DRIVER
    dest_mobile = ""
    if is_driver:
        driver_id = db.session.query(Drivers).filter(Drivers.user == user.id).first().id
        # check existing booking
        booking = db.session.query(Bookings).filter(Bookings.driver == driver_id).filter(Bookings.did_release == False). \
                                                filter(Bookings.did == call_to_number).first()
        if not booking:
            dest_mobile = BookingParams.DRIVER_OFFICE_CONTACT
        else:
            book_user = booking.user
            dest_mobile = db.session.query(Users).filter(Users.id == book_user).first().mobile
    else:
        # check existing booking
        booking = db.session.query(Bookings).filter(Bookings.user == user.id).filter(Bookings.did_release == False). \
                                                filter(Bookings.did == call_to_number).first()                              
        if not booking:
            dest_mobile = BookingParams.USER_OFFICE_CONTACT
        else:
            book_driver = booking.driver
            driver_user = db.session.query(Drivers).filter(Drivers.id == book_driver).first().user
            dest_mobile = db.session.query(Users).filter(Users.id == driver_user).first().mobile
    
    if not booking:
        book_id = 0
    else:
        book_id = booking.id

    log = CallLog(uuid, book_id)
        
    try:
        db.session.add(log)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'result': -1})
    
    transfer_response = [{"transfer":{"type":"number","data":[f"{dest_mobile}"]}}]
        
    return json.dumps(transfer_response)

@callMasking.route('/api/call/hangup', methods=['POST'])
def hangup_log():
    uuid = request.form.get('uuid', '').strip()
    did_num = request.form.get('call_to_number', '').strip()  # DID No
    src_num = request.form.get('caller_id_number', '').strip().replace("+91", "")   # Caller number
    dest_number = request.form.get('answered_agent_number', '').strip().replace("+91", "")   # Received number
    dest_num = dest_number[-10:] if dest_number else None
    start_stamp = convert_datetime_to_utc(request.form.get('start_stamp', ''))
    answer_stamp = convert_datetime_to_utc(request.form.get('answer_stamp', ''))
    end_stamp = convert_datetime_to_utc(request.form.get('end_stamp', ''))
    hangup_cause = request.form.get('hangup_cause', '').strip()
    duration = int(request.form.get('duration', 0))
    call_status = request.form.get('call_status', '').strip()
    call_id = request.form.get('call_id', '').strip()
    call_connected = int(request.form.get('call_connected', 0))
    recording_url = request.form.get('recording_url', '').strip()

    if not uuid:
        return jsonify({'success': -1, 'message': 'UUID is required'}), 400

    try:
        # Update the CallLog entry where uuid matches
        result = db.session.query(CallLog).filter(CallLog.uuid == uuid).update({
            CallLog.dest_num: dest_num,
            CallLog.did_num: did_num,
            CallLog.src_num: src_num,
            CallLog.start_stamp: start_stamp,
            CallLog.answer_stamp: answer_stamp,
            CallLog.end_stamp: end_stamp,
            CallLog.hangup_cause: hangup_cause,
            CallLog.duration: duration,
            CallLog.call_status: call_status,
            CallLog.call_id: call_id,
            CallLog.call_connected: bool(call_connected),
            CallLog.recording_url: recording_url
        })

        if result == 0:
            return jsonify({'success': 0, 'message': 'No record found with the given UUID'}), 404

        db.session.commit()
        return jsonify({'success': 1, 'message': 'Call log updated successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'message': f'Error updating call log: {str(e)}'}), 500


# Api when customer/driver click on particular booking on app
@callMasking.route('/api/calling/callRequest', methods=['POST'])
@swag_from('/app/swagger_docs/calling/callrequest.yml')
@jwt_required()
def callRequest(): 
    claims = get_jwt()
    bookCode = request.form['bookCode']
    is_driver = claims['roles'] == Users.ROLE_DRIVER
    userMobile = db.session.query(Bookings, Users).filter(Bookings.code == bookCode). \
                       filter(Bookings.user == Users.id).first()[1].mobile
    driverMobile = db.session.query(Bookings, Users, Drivers).filter(Bookings.code == bookCode). \
                        filter(Bookings.driver == Drivers.id). \
                        filter(Drivers.user == Users.id).first()[1].mobile
    print(is_driver)
    if is_driver:
        to_mobileNo = userMobile
        from_mobileNo = driverMobile
    else:
        to_mobileNo = driverMobile
        from_mobileNo = userMobile
    print(from_mobileNo)
    callRequestLog = CallRequestLog(bookCode, from_mobileNo, to_mobileNo)

    try:
        db.session.add(callRequestLog)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'result': -1, 'message': 'DB Error'})
        
    return jsonify({'success': 1, 'message': 'Successfully added call request log'})
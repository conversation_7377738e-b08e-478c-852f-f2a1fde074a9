from book_ride import point_inside_polygon,point_inside
from booking_params import Regions


if __name__ == '__main__':
    src_lat="22.7223"
    src_long="88.473"
    dest_lat = "22.608501" 
    dest_long = "88.387522" 
    dest_loc = "11" 
    locations_data = Regions.LOCATION_MAP
    book_type= 3
    city=Regions.REGN_KOLKATA
    print("result:",point_inside_polygon(dest_long, dest_lat, src_long, src_lat, book_type, locations_data, city))
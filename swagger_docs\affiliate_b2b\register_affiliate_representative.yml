tags:
  - Affiliate_B2B
summary: Register Affiliate Representative
description: >
  Registers a new affiliate representative.
parameters:
  - name: affiliate_id
    in: body
    type: string
    required: true
    description: The ID of the affiliate.
  - name: username
    in: body
    type: string
    required: true
    description: The username of the representative.
  - name: mobile
    in: body
    type: string
    required: true
    description: The mobile number of the representative.
  - name: email
    in: body
    type: string
    required: false
    description: The email address of the representative.
responses:
  200:
    description: Affiliate representative registered successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: Affiliate registered successfully.
  400:
    description: Invalid fields or incomplete form data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Invalid fields or Incomplete form.
  409:
    description: Integrity constraint error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: Integrity constraint error.
  500:
    description: An unexpected error occurred.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: An unexpected error occurred.

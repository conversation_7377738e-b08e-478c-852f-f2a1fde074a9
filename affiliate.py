#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  affiliate.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from flask import Blueprint, render_template, request, jsonify
from c24 import c24_upcoming_cust, c24_ongoing_cust, c24_past_cust, c24_book, c24_cancel, c24_pic_upload
from c24 import c24_add_rep, c24_alloc_list, c24_allocate, c24_past_chunked
from olx import olx_upcoming_cust, olx_ongoing_cust, olx_past_cust, olx_book, olx_cancel, olx_pic_upload
from olx import olx_add_rep, olx_alloc_list, olx_allocate, olx_past_chunked
from zoomcar import zoomcar_upcoming_cust, zoomcar_ongoing_cust, zoomcar_past_cust, zoomcar_book, zoomcar_cancel, zoomcar_pic_upload
from zoomcar import zoomcar_add_rep, zoomcar_alloc_list, zoomcar_allocate, zoomcar_past_chunked
from revv import _revv_book, _revv_upcoming_cust, _revv_ongoing_cust, _revv_past_cust
from revv import _revv_cancel
from gujral import _gujral_book, _gujral_upcoming_cust, _gujral_ongoing_cust, _gujral_past_cust
from gujral import _gujral_cancel
from cardekho import cardekho_upcoming_cust, cardekho_ongoing_cust, cardekho_past_cust, cardekho_book, cardekho_cancel, cardekho_pic_upload
from cardekho import cardekho_add_rep, cardekho_alloc_list, cardekho_allocate, cardekho_past_chunked
from bhandari import bhandari_upcoming_cust, bhandari_ongoing_cust, bhandari_past_cust, bhandari_book, bhandari_cancel, bhandari_pic_upload,bhandari_cancel_charge
from bhandari import bhandari_add_rep, bhandari_alloc_list, bhandari_allocate, bhandari_past_chunked
from mahindra import mahindra_upcoming_cust, mahindra_ongoing_cust, mahindra_past_cust, mahindra_book, mahindra_cancel, mahindra_pic_upload
from mahindra import mahindra_add_rep, mahindra_alloc_list, mahindra_allocate, mahindra_past_chunked
from pridehonda import pridehonda_upcoming_cust, pridehonda_ongoing_cust, pridehonda_past_cust, pridehonda_book, pridehonda_cancel, pridehonda_pic_upload,pridehonda_cancel_charge
from pridehonda import pridehonda_add_rep, pridehonda_alloc_list, pridehonda_allocate, pridehonda_past_chunked
from pridehonda import pridehonda_upcoming_cust, pridehonda_ongoing_cust, pridehonda_past_cust, pridehonda_book, pridehonda_cancel, pridehonda_pic_upload
from pridehonda import pridehonda_add_rep, pridehonda_alloc_list, pridehonda_allocate, pridehonda_past_chunked
# from revv_v2 import revv_v2_upcoming_cust, revv_v2_ongoing_cust, revv_v2_past_cust, revv_v2_book, revv_v2_cancel, revv_v2_pic_upload
# from revv_v2 import revv_v2_add_rep, revv_v2_alloc_list, revv_v2_allocate, revv_v2_past_chunked
from spinny import spinny_upcoming_cust, spinny_ongoing_cust, spinny_past_cust, spinny_book, spinny_cancel, spinny_pic_upload, get_spinny_book_state
from spinny import spinny_add_rep, spinny_alloc_list, spinny_allocate, spinny_past_chunked,spinny_cancel_charge, spinny_cancel_new, spinny_affiliate_book
from models import Users, db, C24Rep, C24Bookings, Drivers, OLXRep, OLXBookings, ZoomcarRep, ZoomcarBookings, CardekhoRep, CardekhoBookings, BookingCancelled
from models import BhandariRep, BhandariBookings, MahindraRep, MahindraBookings, RevvV2Rep, RevvV2Bookings, SpinnyRep, SpinnyBookings, PrideHondaRep, PrideHondaBookings
import html
import jsonpickle
import pytz, datetime
from dateutil import parser
from _utils_acc import account_enabled, validate_role
from _utils import complete, get_safe
from flask_jwt_extended import (
    jwt_required, get_jwt, get_jwt_identity
)
from affiliate_api.hook.utils import (
    _zoomcar_change_state, ZOOMCAR_STATE_CANCELED
)
from admin import _get_booking_pic
affiliate = Blueprint('affiliate', __name__)

'''
Methods for Cars24
'''

@affiliate.route('/affiliate/cars24/login', methods=['GET', 'POST'])
def c24_login():
    return render_template('c24Login.html')


@affiliate.route('/affiliate/cars24', methods=['GET', 'POST'])
@jwt_required()
def cars24_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('cars24.html')


@affiliate.route('/api/c24/upcoming', methods=['POST'])
@jwt_required()
def cars24_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return c24_upcoming_cust(to_fetch, user)


@affiliate.route('/api/c24/ongoing', methods=['POST'])
@jwt_required()
def cars24_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return c24_ongoing_cust(user)


@affiliate.route('/api/c24/past', methods=['POST'])
@jwt_required()
def cars24_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'month' in request.form and 'year' in request.form:
        return c24_past_chunked(user, int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return c24_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/c24/book', methods=['POST'])
@jwt_required()
def cars24_book():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    c24_type = int(get_safe(request.form, 'c24_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return c24_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, c24_type, dspoc_mob, rid, region)


@affiliate.route('/api/c24/fetch_rep', methods=['POST'])
@jwt_required()
def cars24_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(C24Rep).filter(C24Rep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/c24/save_cmt', methods=['POST'])
@jwt_required()
def cars24_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(C24Bookings).filter(C24Bookings.id == booking_id). \
                    update({C24Bookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


@affiliate.route('/api/c24/cancel', methods=['POST'])
@jwt_required()
def cars24_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    print(booking_id)
    return c24_cancel(booking_id, user)


@affiliate.route('/api/c24/add_rep', methods=['POST'])
@jwt_required()
def cars24_add_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_C24 and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return c24_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/c24/upload', methods=['POST'])
@jwt_required()
def cars24_pic_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1, 'msg': 'Unauthorised role: not Driver'}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1, 'msg': 'Server error'}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'msg': 'User restricted'}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form details"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return c24_pic_upload(booking_id, index, pic)


@affiliate.route('/api/c24/alloc_list', methods=['POST'])
@jwt_required()
def cars24_alloc_list():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    all = bool(request.form['all'])
    return c24_alloc_list(booking_id, all)


@affiliate.route('/api/c24/alloc_driver', methods=['POST'])
@jwt_required()
def cars24_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_C24, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return c24_allocate(booking_id, driver_id)


'''
Methods for Revv
'''
@affiliate.route('/affiliate/revv/login', methods=['GET', 'POST'])
def revv_login():
    return render_template('revvLogin.html')


@affiliate.route('/affiliate/revv', methods=['GET', 'POST'])
@jwt_required()
def revv_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('revv.html')


@affiliate.route('/api/revv/book', methods=['POST'])
@jwt_required()
def revv_book():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['drivers', 'dur', 'shift', 'book_dt']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    drivers = int(get_safe(request.form, 'drivers', 2))
    dur = int(get_safe(request.form, 'dur', 12))
    dur_t = str(dur) + ":00:00"
    shift = get_safe(request.form, 'shift', 0)
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return _revv_book(region, user, drivers, dur_t, shift, book_time, book_date)


@affiliate.route('/api/revv/upcoming', methods=['POST'])
@jwt_required()
def revv_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return _revv_upcoming_cust(to_fetch, user)


@affiliate.route('/api/revv/ongoing', methods=['POST'])
@jwt_required()
def revv_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return _revv_ongoing_cust(user)


@affiliate.route('/api/revv/past', methods=['POST'])
@jwt_required()
def revv_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        print('en')
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return _revv_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/revv/cancel', methods=['POST'])
@jwt_required()
def revv_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return _revv_cancel(booking_id, user)


'''
Methods for Gujral
'''
@affiliate.route('/affiliate/gujral/login', methods=['GET', 'POST'])
def gujral_login():
    return render_template('gujralLogin.html')


@affiliate.route('/affiliate/gujral', methods=['GET', 'POST'])
@jwt_required()
def gujral_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('gujral.html')


@affiliate.route('/api/gujral/book', methods=['POST'])
@jwt_required()
def gujral_book():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['drivers', 'dur', 'shift', 'book_dt']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    drivers = int(get_safe(request.form, 'drivers', 2))
    dur = int(get_safe(request.form, 'dur', 12))
    dur_t = str(dur) + ":00:00"
    shift = get_safe(request.form, 'shift', 0)
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return _gujral_book(region, user, drivers, dur_t, shift, book_time, book_date)


@affiliate.route('/api/gujral/upcoming', methods=['POST'])
@jwt_required()
def gujral_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return _gujral_upcoming_cust(to_fetch, user)


@affiliate.route('/api/gujral/ongoing', methods=['POST'])
@jwt_required()
def gujral_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return _gujral_ongoing_cust(user)


@affiliate.route('/api/gujral/past', methods=['POST'])
@jwt_required()
def gujral_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        print('en')
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return _gujral_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/gujral/cancel', methods=['POST'])
@jwt_required()
def gujral_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return _gujral_cancel(booking_id, user)

'''
Methods for OLX
'''

@affiliate.route('/affiliate/olx/login', methods=['GET', 'POST'])
def olx_login():
    return render_template('olxLogin.html')


@affiliate.route('/affiliate/olx', methods=['GET', 'POST'])
@jwt_required()
def olx_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('olx.html')


@affiliate.route('/api/olx/upcoming', methods=['POST'])
@jwt_required()
def olx_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return olx_upcoming_cust(to_fetch, user)


@affiliate.route('/api/olx/ongoing', methods=['POST'])
@jwt_required()
def olx_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return olx_ongoing_cust(user)


@affiliate.route('/api/olx/past', methods=['POST'])
@jwt_required()
def olx_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'month' in request.form and 'year' in request.form:
        return olx_past_chunked(user, int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return olx_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/olx/book', methods=['POST'])
@jwt_required()
def olx_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    olx_type = int(get_safe(request.form, 'olx_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return olx_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, olx_type, dspoc_mob, rid, region)


@affiliate.route('/api/olx/fetch_rep', methods=['POST'])
@jwt_required()
def olx_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(OLXRep).filter(OLXRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/olx/save_cmt', methods=['POST'])
@jwt_required()
def olx_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(OLXBookings).filter(OLXBookings.id == booking_id). \
                    update({OLXBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


@affiliate.route('/api/olx/cancel', methods=['POST'])
@jwt_required()
def olx_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return olx_cancel(booking_id, user)


@affiliate.route('/api/olx/add_rep', methods=['POST'])
@jwt_required()
def olx_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_OLX and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return olx_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/olx/upload', methods=['POST'])
@jwt_required()
def olx_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1, 'msg': 'Unauthorized role: not Driver'}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1, 'msg': 'Server error'}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'msg': 'User restricted'}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form details"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return olx_pic_upload(booking_id, index, pic)


@affiliate.route('/api/olx/alloc_list', methods=['POST'])
@jwt_required()
def olx_allocs():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    all = bool(request.form['all'])
    return olx_alloc_list(booking_id, all)


@affiliate.route('/api/olx/alloc_driver', methods=['POST'])
@jwt_required()
def olx_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_OLX, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return olx_allocate(booking_id, driver_id)

'''
Methods for Zoomcar
'''


@affiliate.route('/affiliate/zoomcar/login', methods=['GET', 'POST'])
def zoomcar_login():
    return render_template('zoomcarLogin.html')


@affiliate.route('/affiliate/zoomcar', methods=['GET', 'POST'])
@jwt_required()
def zoomcar_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('zoomcar.html')


@affiliate.route('/api/zoomcar/upcoming', methods=['POST'])
@jwt_required()
def zoomcar_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return zoomcar_upcoming_cust(to_fetch, user)


@affiliate.route('/api/zoomcar/ongoing', methods=['POST'])
@jwt_required()
def zoomcar_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return zoomcar_ongoing_cust(user)


@affiliate.route('/api/zoomcar/past', methods=['POST'])
@jwt_required()
def zoomcar_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'day' in request.form and 'month' in request.form and 'year' in request.form:
        return zoomcar_past_chunked(user,  int(request.form['day']), int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return zoomcar_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/zoomcar/book', methods=['POST'])
@jwt_required()
def zoomcar_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    zoomcar_type = int(get_safe(request.form, 'zoomcar_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return zoomcar_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, zoomcar_type, dspoc_mob, rid, region)


@affiliate.route('/api/zoomcar/fetch_rep', methods=['POST'])
@jwt_required()
def zoomcar_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(ZoomcarRep).filter(ZoomcarRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/zoomcar/save_cmt', methods=['POST'])
@jwt_required()
def zoomcar_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(ZoomcarBookings).filter(ZoomcarBookings.id == booking_id). \
                    update({ZoomcarBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


@affiliate.route('/api/zoomcar/cancel', methods=['POST'])
@jwt_required()
def zoomcar_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    success, fail_reason, d4m_id = zoomcar_cancel(booking_id, user, True)
    if success:
        _zoomcar_change_state(d4m_id, ZOOMCAR_STATE_CANCELED)
    return jsonify({"success": int(success), "reason": fail_reason})


@affiliate.route('/api/zoomcar/add_rep', methods=['POST'])
@jwt_required()
def zoomcar_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_ZOOMCAR and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return zoomcar_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/zoomcar/upload', methods=['POST'])
@jwt_required()
def zoomcar_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return zoomcar_pic_upload(booking_id, index, pic)


@affiliate.route('/api/zoomcar/alloc_list', methods=['POST'])
@jwt_required()
def zoomcar_allocs():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    all = bool(request.form['all'])
    return zoomcar_alloc_list(booking_id, all)


@affiliate.route('/api/zoomcar/alloc_driver', methods=['POST'])
@jwt_required()
def zoomcar_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_ZOOMCAR, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return zoomcar_allocate(booking_id, driver_id)

'''
Methods for Cardekho
'''


@affiliate.route('/affiliate/cardekho/login', methods=['GET', 'POST'])
def cardekho_login():
    return render_template('cardekhoLogin.html')


@affiliate.route('/affiliate/cardekho', methods=['GET', 'POST'])
@jwt_required()
def cardekho_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('cardekho.html')


@affiliate.route('/api/cardekho/upcoming', methods=['POST'])
@jwt_required()
def cardekho_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return cardekho_upcoming_cust(to_fetch, user)


@affiliate.route('/api/cardekho/ongoing', methods=['POST'])
@jwt_required()
def cardekho_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return cardekho_ongoing_cust(user)


@affiliate.route('/api/cardekho/past', methods=['POST'])
@jwt_required()
def cardekho_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'day' in request.form and 'month' in request.form and 'year' in request.form:
        return cardekho_past_chunked(user, int(request.form['day']), int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return cardekho_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/cardekho/book', methods=['POST'])
@jwt_required()
def cardekho_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    cardekho_type = int(get_safe(request.form, 'cardekho_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return cardekho_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, cardekho_type, dspoc_mob, rid, region)


@affiliate.route('/api/cardekho/fetch_rep', methods=['POST'])
@jwt_required()
def cardekho_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(CardekhoRep).filter(CardekhoRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/cardekho/save_cmt', methods=['POST'])
@jwt_required()
def cardekho_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(CardekhoBookings).filter(CardekhoBookings.id == booking_id). \
                    update({CardekhoBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


@affiliate.route('/api/cardekho/cancel', methods=['POST'])
@jwt_required()
def cardekho_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return cardekho_cancel(booking_id, user)


@affiliate.route('/api/cardekho/add_rep', methods=['POST'])
@jwt_required()
def cardekho_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_CARDEKHO and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return cardekho_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/cardekho/upload', methods=['POST'])
@jwt_required()
def cardekho_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1, 'message':'DB Error1'}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1, 'message':'DB Error2'}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message':'DB Error3'}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return cardekho_pic_upload(booking_id, index, pic)


@affiliate.route('/api/cardekho/alloc_list', methods=['POST'])
@jwt_required()
def cardekho_allocs():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    all = bool(request.form['all'])
    return cardekho_alloc_list(booking_id, all)


@affiliate.route('/api/cardekho/alloc_driver', methods=['POST'])
@jwt_required()
def cardekho_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_CARDEKHO, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return cardekho_allocate(booking_id, driver_id)

'''
Methods for Bhandari
'''


@affiliate.route('/affiliate/bhandari/login', methods=['GET', 'POST'])
def bhandari_login():
    return render_template('bhandariLogin.html')


@affiliate.route('/affiliate/bhandari', methods=['GET', 'POST'])
@jwt_required()
def bhandari_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('bhandari.html')


@affiliate.route('/api/bhandari/upcoming', methods=['POST'])
@jwt_required()
def bhandari_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return bhandari_upcoming_cust(to_fetch, user)


@affiliate.route('/api/bhandari/ongoing', methods=['POST'])
@jwt_required()
def bhandari_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return bhandari_ongoing_cust(user)


@affiliate.route('/api/bhandari/past', methods=['POST'])
@jwt_required()
def bhandari_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'month' in request.form and 'year' in request.form:
        return bhandari_past_chunked(user, int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return bhandari_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/bhandari/book', methods=['POST'])
@jwt_required()
def bhandari_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    bhandari_type = int(get_safe(request.form, 'bhandari_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return bhandari_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, bhandari_type, dspoc_mob, rid, region)


@affiliate.route('/api/bhandari/fetch_rep', methods=['POST'])
@jwt_required()
def bhandari_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(BhandariRep).filter(BhandariRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/bhandari/save_cmt', methods=['POST'])
@jwt_required()
def bhandari_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(BhandariBookings).filter(BhandariBookings.id == booking_id). \
                    update({BhandariBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})

@affiliate.route('/api/bhandari/cancel_charge', methods=['POST'])
@jwt_required()
def bhandari_cancel_penalty():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_B2B_OTHER))
    return bhandari_cancel_charge(booking_id, user,reason)

@affiliate.route('/api/bhandari/cancel', methods=['POST'])
@jwt_required()
def bhandari_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    reason_detail = str(get_safe(request.form, 'reason_detail', ""))
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_B2B_OTHER))
    return bhandari_cancel(booking_id, user,reason,reason_detail)


@affiliate.route('/api/bhandari/add_rep', methods=['POST'])
@jwt_required()
def bhandari_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_BHANDARI and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return bhandari_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/bhandari/upload', methods=['POST'])
@jwt_required()
def bhandari_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1, 'msg': "Unauthorized role"}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1, 'msg': "Server error"}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form details"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return bhandari_pic_upload(booking_id, index, pic)


@affiliate.route('/api/bhandari/alloc_list', methods=['POST'])
@jwt_required()
def bhandari_alloc():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': -2}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return bhandari_alloc_list(booking_id)


@affiliate.route('/api/bhandari/alloc_driver', methods=['POST'])
@jwt_required()
def bhandari_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_BHANDARI, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return bhandari_allocate(booking_id, driver_id, user)


'''
Methods for Mahindra
'''

@affiliate.route('/affiliate/jyoti/login', methods=['GET', 'POST'])
def mahindra_login():
    return render_template('mahindraLogin.html')


@affiliate.route('/affiliate/jyoti', methods=['GET', 'POST'])
@jwt_required()
def mahindra_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('mahindra.html')


@affiliate.route('/api/mahindra/upcoming', methods=['POST'])
@jwt_required()
def mahindra_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return mahindra_upcoming_cust(to_fetch, user)


@affiliate.route('/api/mahindra/ongoing', methods=['POST'])
@jwt_required()
def mahindra_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return mahindra_ongoing_cust(user)


@affiliate.route('/api/mahindra/past', methods=['POST'])
@jwt_required()
def mahindra_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'day' in request.form and 'month' in request.form and 'year' in request.form:
        return mahindra_past_chunked(user, int(request.form['day']), int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return mahindra_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/mahindra/book', methods=['POST'])
@jwt_required()
def mahindra_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    mahindra_type = int(get_safe(request.form, 'mahindra_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return mahindra_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, mahindra_type, dspoc_mob, rid, region)


@affiliate.route('/api/mahindra/fetch_rep', methods=['POST'])
@jwt_required()
def mahindra_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(MahindraRep).filter(MahindraRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/mahindra/save_cmt', methods=['POST'])
@jwt_required()
def mahindra_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(MahindraBookings).filter(MahindraBookings.id == booking_id). \
                    update({MahindraBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


@affiliate.route('/api/mahindra/cancel', methods=['POST'])
@jwt_required()
def mahindra_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return mahindra_cancel(booking_id, user)


@affiliate.route('/api/mahindra/add_rep', methods=['POST'])
@jwt_required()
def mahindra_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_MAHINDRA and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return mahindra_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/mahindra/upload', methods=['POST'])
@jwt_required()
def mahindra_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return mahindra_pic_upload(booking_id, index, pic)


@affiliate.route('/api/mahindra/alloc_list', methods=['POST'])
@jwt_required()
def mahindra_alloc():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': -2}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return mahindra_alloc_list(booking_id)


@affiliate.route('/api/mahindra/alloc_driver', methods=['POST'])
@jwt_required()
def mahindra_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return mahindra_allocate(booking_id, driver_id, user)

@affiliate.route('/api/mahindra/booking_pic', methods=['POST'])
@jwt_required()
def mahindra_booking_pic_fetch():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_MAHINDRA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': -2}), 401
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    res = _get_booking_pic(request.form["booking_id"])
    return jsonify({"success": 1, "pic_list": res})


'''
Methods for RevvV2
'''


# @affiliate.route('/affiliate/revv_v2/login', methods=['GET', 'POST'])
# def revv_v2_login():
#     return render_template('revv_v2Login.html')


# @affiliate.route('/affiliate/revv_v2', methods=['GET', 'POST'])
# @jwt_required()
# def revv_v2_console_page_base():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     return render_template('revv_v2.html')


# @affiliate.route('/api/revv_v2/upcoming', methods=['POST'])
# @jwt_required()
# def revv_v2_upcoming():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     to_fetch = int(get_safe(request.form, 'to_fetch', 3))
#     return revv_v2_upcoming_cust(to_fetch, user)


# @affiliate.route('/api/revv_v2/ongoing', methods=['POST'])
# @jwt_required()
# def revv_v2_ongoing():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     return revv_v2_ongoing_cust(user)


# @affiliate.route('/api/revv_v2/past', methods=['POST'])
# @jwt_required()
# def revv_v2_past():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         print(e)
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     if 'day' in request.form and 'month' in request.form and 'year' in request.form:
#         return revv_v2_past_chunked(user, int(request.form['day']), int(request.form['month']), int(request.form['year']))
#     to_fetch = int(get_safe(request.form, 'to_fetch', 3))
#     cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
#     return revv_v2_past_cust(user, to_fetch, cancelled_by)


# @affiliate.route('/api/revv_v2/book', methods=['POST'])
# @jwt_required()
# def revv_v2_book_trip():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

#     reflat = get_safe(request.form, 'reflat', 0)
#     reflong = get_safe(request.form, 'reflong', 0)
#     dest_reflat = get_safe(request.form, 'dest_reflat', 0)
#     dest_reflong = get_safe(request.form, 'dest_reflong', 0)
#     car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
#     loc_name = get_safe(request.form, 'loc_name', "")
#     dest_locname = get_safe(request.form, 'dest_locname', "")
#     appt = get_safe(request.form, 'appt_id', "")
#     veh_no = get_safe(request.form, 'veh_no', "")
#     veh_model = get_safe(request.form, 'veh_model', "")
#     revv_v2_type = int(get_safe(request.form, 'revv_v2_type', 0))
#     dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
#     rid = int(get_safe(request.form, 'rep', -1))
#     region = int(get_safe(request.form, 'region', 0))
#     try:
#         book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
#     except ValueError:
#         try:
#             book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
#         except ValueError:
#             book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

#     book_time = book_dt.time()
#     book_date = book_dt.date()

#     return revv_v2_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, revv_v2_type, dspoc_mob, rid, region)


# @affiliate.route('/api/revv_v2/fetch_rep', methods=['POST'])
# @jwt_required()
# def revv_v2_fetch_rep():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     region = int(get_safe(request.form, 'region', 0))
#     reps = db.session.query(RevvV2Rep).filter(RevvV2Rep.region == region).all()
#     result_json = []
#     for rep in reps:
#         result_json.append(jsonpickle.encode(rep))
#     return jsonify({'success': 1, 'data': result_json})


# @affiliate.route('/api/revv_v2/save_cmt', methods=['POST'])
# @jwt_required()
# def revv_v2_save_cmt():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     if not complete(request.form, ['booking_id', 'comment']):
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

#     comment = html.escape(request.form['comment'])
#     booking_id = int(request.form['booking_id'])
#     db.session.query(RevvV2Bookings).filter(RevvV2Bookings.id == booking_id). \
#                     update({RevvV2Bookings.comment: comment})
#     fail = False
#     fail_reason = ""
#     try:
#         db.session.commit()
#     except Exception as e:
#         db.session.rollback()
#         fail = True
#         fail_reason = "DB Error"
#         print(e)
#     if fail:
#         return jsonify({'success': -1, 'reason': fail_reason})
#     return jsonify({'success': 1})


# @affiliate.route('/api/revv_v2/cancel', methods=['POST'])
# @jwt_required()
# def revv_v2_cancel_trip():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     if not complete(request.form, ['booking_id']):
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
#     booking_id = int(request.form['booking_id'])
#     return revv_v2_cancel(booking_id, user)


# @affiliate.route('/api/revv_v2/add_rep', methods=['POST'])
# @jwt_required()
# def revv_v2_rep_add():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if claims['roles'] != Users.ROLE_REVV_V2 and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
#         return jsonify({'error': 1}), 401
#     if not complete(request.form, ['name', 'mobile']):
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
#     name = html.escape(request.form['name'])
#     mobile = request.form['mobile']
#     region = request.form['region']
#     return revv_v2_add_rep(name, mobile, user, region)


# @affiliate.route('/api/driver/revv_v2/upload', methods=['POST'])
# @jwt_required()
# def revv_v2_upload():
#     try:
#         claims = get_jwt()
#         if claims['roles'] != Users.ROLE_DRIVER:
#             return jsonify({'error': 1}), 401
#         driver_user = get_jwt_identity()
#         driver_res = Drivers.query.filter_by(user=driver_user)
#         driver = driver_res.first().id
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(driver_user):
#         return jsonify({'success': -1}), 401
#     if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
#     booking_id = int(request.form['booking_id'])
#     index = int(request.form['index'])
#     pic = request.files['pic']
#     return revv_v2_pic_upload(booking_id, index, pic)


# @affiliate.route('/api/revv_v2/alloc_list', methods=['POST'])
# @jwt_required()
# def revv_v2_allocs():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     if not complete(request.form, ['booking_id']):
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
#     booking_id = int(request.form['booking_id'])
#     all = bool(request.form['all'])
#     return revv_v2_alloc_list(booking_id, all)


# @affiliate.route('/api/revv_v2/alloc_driver', methods=['POST'])
# @jwt_required()
# def revv_v2_alloc_driver():
#     try:
#         user = get_jwt_identity()
#     except Exception as e:
#         return jsonify({'success': -1}), 401
#     if not account_enabled(user):
#         return jsonify({'success': -1}), 401
#     claims = get_jwt()
#     if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_REVV_V2, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
#         return jsonify({'error': 1}), 401
#     if not complete(request.form, ['booking_id', 'driver_id']):
#         return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
#     booking_id = int(request.form['booking_id'])
#     driver_id = int(request.form['driver_id'])
#     return revv_v2_allocate(booking_id, driver_id)


'''
Methods for Spinny
'''


@affiliate.route('/affiliate/spinny/login', methods=['GET', 'POST'])
def spinny_login():
    return render_template('spinnyLogin.html')


@affiliate.route('/affiliate/spinny', methods=['GET', 'POST'])
@jwt_required()
def spinny_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    return render_template('spinny.html')


@affiliate.route('/api/spinny/upcoming', methods=['POST'])
@jwt_required()
def spinny_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return spinny_upcoming_cust(to_fetch, user)


@affiliate.route('/api/spinny/ongoing', methods=['POST'])
@jwt_required()
def spinny_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    return spinny_ongoing_cust(user)


@affiliate.route('/api/spinny/past', methods=['POST'])
@jwt_required()
def spinny_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    if 'day' in request.form and 'month' in request.form and 'year' in request.form:
        return spinny_past_chunked(user, int(request.form['day']), int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return spinny_past_cust(user, to_fetch, cancelled_by)

@affiliate.route('/affiliate/spinny/book', methods=['POST'])
@jwt_required()
def spinny_affiliate_book_trip():
    try:
        try:
            user = get_jwt_identity()
        except Exception as e:
            return jsonify({
                    "message": "Unauthorized access",
                    "result": "FAILURE",
                    "status": 401
                }), 401

        if not account_enabled(user):
            return jsonify({
                    "message": "Forbidden access to user",
                    "result": "FAILURE",
                    "status": 403
                }), 403

        claims = get_jwt()
        if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
            return jsonify({
                    "message": "Forbidden access to user",
                    "result": "FAILURE",
                    "status": 403
                }), 403

        if not complete(request.form, ['reference_id', 'car_auto', 'drop_lat', 'drop_lng', 'pickup_lat', 'pickup_lng', 'pickup_address', 'drop_address', 'start_datetime', 'veh_number', 'veh_model', 'src_spoc_mobile', 'src_spoc_name', 'dest_spoc_name', 'dest_spoc_mobile', 'city']):
            return jsonify({
                    "message": "Incomplete form details",
                    "result": "FAILURE",
                    "status": 400
                }), 400

        reflat = get_safe(request.form, 'pickup_lat', 0)
        reflong = get_safe(request.form, 'pickup_lng', 0)
        dest_reflat = get_safe(request.form, 'drop_lat', 0)
        dest_reflong = get_safe(request.form, 'drop_lng', 0)
        car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
        loc_name = get_safe(request.form, 'pickup_address', "")
        dest_locname = get_safe(request.form, 'drop_address', "")
        appt = get_safe(request.form, 'reference_id', "")
        veh_no = get_safe(request.form, 'veh_number', "")
        veh_model = get_safe(request.form, 'veh_model', "")
        spinny_type = int(get_safe(request.form, 'spinny_type', 0))
        type_detail = get_safe(request.form, 'spinny_type_detail', "")
        bus_func = get_safe(request.form, 'business_func', "")
        bus_cat = get_safe(request.form, 'business_cat', "")
        dspoc_mob = str(get_safe(request.form, 'dest_spoc_mobile', 0)).replace(" ", "")
        region = int(get_safe(request.form, 'city', 0))
        src_spoc_mobile = request.form.get('src_spoc_mobile', 0).replace(" ", "")
        spinny_type = int(get_safe(request.form, 'spinny_type', 0))
        type_detail = get_safe(request.form, 'spinny_type_detail', "")
        src_spoc_name = get_safe(request.form, 'src_spoc_name', '')
        dest_spoc_name = get_safe(request.form, 'dest_spoc_name', '')

        if len(src_spoc_mobile) < 10 or len(dspoc_mob) < 10:
            return jsonify({
                "message": "Invalid Source/Destination SPOC Mobile number",
                "result": "FAILURE",
                "status": 400
            }), 400

        if spinny_type not in SpinnyBookings.SPINNY_TYPE_MAPPING:
            return jsonify({
                "message": "Invalid spinny type - trip type",
                "result": "FAILURE",
                "status": 400
            }), 400

        type_detail = type_detail if type_detail else SpinnyBookings.SPINNY_TYPE_MAPPING[spinny_type]

        input_datetime = request.form.get('start_datetime')
        book_dt = None
        
        formatlist = [
            "%Y-%m-%d %H:%M:%S %z", 
            "%H:%M:%S %I:%M %p %z", 
            "%d/%m/%Y %H:%M:%S %z",
            "%Y-%m-%d %H:%M:%S", 
            "%d/%m/%Y %H:%M:%S"
        ]

        if parser.parse(input_datetime).tzinfo is None:
            input_datetime += ' +0530'

        book_dt = None
        for date_format in formatlist:
            try:
                book_dt = datetime.datetime.strptime(input_datetime, date_format + (' %z' if ' %z' not in date_format else '')).astimezone(pytz.utc)
                break
            except ValueError:
                continue

        if book_dt < datetime.datetime.now(pytz.utc):
            return jsonify({
                "message": "Datetime should be greater than current time",
                "result": "FAILURE",
                "status": 403
            }), 403

        book_time = book_dt.time()
        book_date = book_dt.date()

        return spinny_affiliate_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, spinny_type, type_detail, bus_cat, bus_func, dspoc_mob, src_spoc_mobile, region, src_spoc_name, dest_spoc_name)
    except Exception as e:
        return jsonify({
                'error': str(e),
                "message": "Invalid input form data",
                "result": "FAILURE",
                "status": 400
            }), 400

@affiliate.route('/affiliate/spinny/cancel', methods=['POST'])
@jwt_required()
def spinny_cancel_trip_new():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({
                "status": 401,
                "result": "FAILURE",
                "message": "Unauthorized Access."
            }), 401
    if not account_enabled(user):
        return jsonify({
                "status": 403,
                "result": "FAILURE",
                "message": "User not allowed to make requests."
                }), 403
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({
                "status": 403,
                "result": "FAILURE",
                "message": "User not allowed to cancel."
            }), 403
    if not complete(request.form, ['book_id']):
        return jsonify({
                "status": 400,
                "result": "FAILURE",
                "message": "Incomplete form details."
            }), 400
    booking_id = int(request.form['book_id'])
    # reason_cancel = int(get_safe(request.form, 'reason', SpinnyBookings.DEFAULT_REASON_FROM_SPINNY)) + BookingCancelled.CONST_FOR_B2B_REASON_CONVERSION
    reason_cancel = int(get_safe(request.form, 'reason', SpinnyBookings.DEFAULT_REASON_FROM_SPINNY))

    reason_detail_mapping = SpinnyBookings.REASON_DETAIL_MAPPING

    if reason_cancel not in SpinnyBookings.REASON_SERIAL_MAPPING:
        return jsonify({
                "status": 400,
                "result": "FAILURE",
                "message": "Invalid reason."
            }), 400
            
    reason = SpinnyBookings.REASON_SERIAL_MAPPING.get(reason_cancel)

    reason_detail = reason_detail_mapping.get(reason, 'RSN_B2B_OTHER')
    return spinny_cancel_new(booking_id, user,reason,reason_detail)

@affiliate.route('/affiliate/spinny/book_state', methods=['POST'])
@jwt_required()
def spinny_booking_state():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({
                "status": 401,
                "result": "FAILURE",
                "message": "Unauthorized Access."
            }), 401
    if not account_enabled(user):
        return jsonify({
                "status": 403,
                "result": "FAILURE",
                "message": "User not allowed to make requests."
            }), 403
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({
                "status": 403,
                "result": "FAILURE",
                "message": "User not allowed to check state."
            }), 403
    if not complete(request.form, ['book_id']):
        return jsonify({
                "status": 400,
                "result": "FAILURE",
                "message": "Incomplete form details."
            }), 400
    booking_id = int(request.form['book_id'])
    return get_spinny_book_state(booking_id)

@affiliate.route('/api/spinny/book', methods=['POST'])
@jwt_required()
def spinny_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    return jsonify({'success': -3, 'msg': "Bookings are currently not allowed"}), 403
    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    spinny_type = int(get_safe(request.form, 'spinny_type', 0))
    type_detail = get_safe(request.form, 'spinny_type_detail', "")
    bus_func = get_safe(request.form, 'bus_func', "")
    bus_cat = get_safe(request.form, 'bus_cat', "")
    dspoc_mob = str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", "")
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    src_spoc_name = get_safe(request.form, 'src_spoc_name', '')
    dest_spoc_name = get_safe(request.form, 'dest_spoc_name', '')

    if len(dspoc_mob) < 10:
        return jsonify({'success': -1, 'reason': 'Invalid destination spoc mobile number'}), 400

    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return spinny_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, 
        book_time, book_date, appt, veh_no, veh_model, spinny_type, type_detail, bus_cat, bus_func, dspoc_mob, 
        rid, region, src_spoc_name)


@affiliate.route('/api/spinny/fetch_rep', methods=['POST'])
@jwt_required()
def spinny_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(SpinnyRep).filter(SpinnyRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})

@affiliate.route('/api/spinny/save_cmt', methods=['POST'])
@jwt_required()
def spinny_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(SpinnyBookings).filter(SpinnyBookings.id == booking_id). \
                    update({SpinnyBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})

@affiliate.route('/api/spinny/cancel_charge', methods=['POST'])
@jwt_required()
def spinny_cancel_penalty():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_B2B_OTHER))
    return spinny_cancel_charge(booking_id, user,reason)


@affiliate.route('/api/spinny/cancel', methods=['POST'])
@jwt_required()
def spinny_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    reason_detail = str(get_safe(request.form, 'reason_detail', ""))
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_B2B_OTHER))
    return spinny_cancel(booking_id, user,reason,reason_detail)


@affiliate.route('/api/spinny/add_rep', methods=['POST'])
@jwt_required()
def spinny_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_SPINNY and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return spinny_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/spinny/upload', methods=['POST'])
@jwt_required()
def spinny_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return spinny_pic_upload(booking_id, index, pic)


@affiliate.route('/api/spinny/alloc_list', methods=['POST'])
@jwt_required()
def spinny_alloc():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': -2}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    return spinny_alloc_list(booking_id)


@affiliate.route('/api/spinny/alloc_driver', methods=['POST'])
@jwt_required()
def spinny_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return spinny_allocate(booking_id, driver_id, user)

@affiliate.route('/api/spinny/booking_pic', methods=['POST'])
@jwt_required()
def spinny_booking_pic_fetch():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_SPINNY, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN, Users.ROLE_SPINNY_TEAM]):
        return jsonify({'error': -2}), 401
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    res = _get_booking_pic(request.form["booking_id"])
    return jsonify({"success": 1, "pic_list": res})


'''
Methods for PrideHonda
'''


@affiliate.route('/affiliate/pridehonda/login', methods=['GET', 'POST'])
def pridehonda_login():
    return render_template('pridehondaLogin.html')


@affiliate.route('/affiliate/pridehonda', methods=['GET', 'POST'])
@jwt_required()
def pridehonda_console_page_base():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return render_template('pridehonda.html')


@affiliate.route('/api/pridehonda/upcoming', methods=['POST'])
@jwt_required()
def pridehonda_upcoming():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    return pridehonda_upcoming_cust(to_fetch, user)


@affiliate.route('/api/pridehonda/ongoing', methods=['POST'])
@jwt_required()
def pridehonda_ongoing():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    return pridehonda_ongoing_cust(user)


@affiliate.route('/api/pridehonda/past', methods=['POST'])
@jwt_required()
def pridehonda_past():
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if 'day' in request.form and 'month' in request.form and 'year' in request.form:
        return pridehonda_past_chunked(user, int(request.form['day']), int(request.form['month']), int(request.form['year']))
    to_fetch = int(get_safe(request.form, 'to_fetch', 3))
    cancelled_by = int(get_safe(request.form, 'cancelled_by', -1))
    return pridehonda_past_cust(user, to_fetch, cancelled_by)


@affiliate.route('/api/pridehonda/book', methods=['POST'])
@jwt_required()
def pridehonda_book_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['appt_id', 'loc_name', 'dest_locname', 'book_dt', 'veh_no', 'veh_model', 'rep']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    reflat = get_safe(request.form, 'reflat', 0)
    reflong = get_safe(request.form, 'reflong', 0)
    dest_reflat = get_safe(request.form, 'dest_reflat', 0)
    dest_reflong = get_safe(request.form, 'dest_reflong', 0)
    car_auto = int(get_safe(request.form, 'car_auto', 0)) >= 1
    loc_name = get_safe(request.form, 'loc_name', "")
    dest_locname = get_safe(request.form, 'dest_locname', "")
    appt = get_safe(request.form, 'appt_id', "")
    veh_no = get_safe(request.form, 'veh_no', "")
    veh_model = get_safe(request.form, 'veh_model', "")
    pridehonda_type = int(get_safe(request.form, 'pridehonda_type', 0))
    dspoc_mob = int(str(get_safe(request.form, 'dspoc_mob', 0)).replace(" ", ""))
    rid = int(get_safe(request.form, 'rep', -1))
    region = int(get_safe(request.form, 'region', 0))
    try:
        book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d-%m-%Y %H:%M:%S").astimezone(pytz.utc)
    except ValueError:
        try:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%H:%M:%S %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            book_dt = datetime.datetime.strptime(request.form['book_dt'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)

    book_time = book_dt.time()
    book_date = book_dt.date()

    return pridehonda_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat, dest_reflong, dest_locname, book_time, book_date, appt, veh_no, veh_model, pridehonda_type, dspoc_mob, rid, region)


@affiliate.route('/api/pridehonda/fetch_rep', methods=['POST'])
@jwt_required()
def pridehonda_fetch_rep():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    region = int(get_safe(request.form, 'region', 0))
    reps = db.session.query(PrideHondaRep).filter(PrideHondaRep.region == region).all()
    result_json = []
    for rep in reps:
        result_json.append(jsonpickle.encode(rep))
    return jsonify({'success': 1, 'data': result_json})


@affiliate.route('/api/pridehonda/save_cmt', methods=['POST'])
@jwt_required()
def pridehonda_save_cmt():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'comment']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201

    comment = html.escape(request.form['comment'])
    booking_id = int(request.form['booking_id'])
    db.session.query(PrideHondaBookings).filter(PrideHondaBookings.id == booking_id). \
                    update({PrideHondaBookings.comment: comment})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})


@affiliate.route('/api/pridehonda/cancel_charge', methods=['POST'])
@jwt_required()
def pridehonda_cancel_penalty():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_B2B_OTHER))
    return pridehonda_cancel_charge(booking_id, user,reason)

@affiliate.route('/api/pridehonda/cancel', methods=['POST'])
@jwt_required()
def pridehonda_cancel_trip():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    reason_detail = str(get_safe(request.form, 'reason_detail', ""))
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_B2B_OTHER))
    return pridehonda_cancel(booking_id, user,reason,reason_detail)


@affiliate.route('/api/pridehonda/add_rep', methods=['POST'])
@jwt_required()
def pridehonda_rep_add():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_PRIDEHONDA and claims['roles'] != Users.ROLE_SUPERADMIN and claims['roles'] not in Users.ROLE_ADMIN:
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['name', 'mobile']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    name = html.escape(request.form['name'])
    mobile = request.form['mobile']
    region = request.form['region']
    return pridehonda_add_rep(name, mobile, user, region)


@affiliate.route('/api/driver/pridehonda/upload', methods=['POST'])
@jwt_required()
def pridehonda_upload():
    try:
        claims = get_jwt()
        if claims['roles'] != Users.ROLE_DRIVER:
            return jsonify({'error': 1}), 401
        driver_user = get_jwt_identity()
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    if not complete(request.form, ['booking_id', 'index']) or 'pic' not in request.files:
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    index = int(request.form['index'])
    pic = request.files['pic']
    return pridehonda_pic_upload(booking_id, index, pic)


@affiliate.route('/api/pridehonda/alloc_list', methods=['POST'])
@jwt_required()
def pridehonda_allocs():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    all = bool(request.form['all'])
    return pridehonda_alloc_list(booking_id, all)


@affiliate.route('/api/pridehonda/alloc_driver', methods=['POST'])
@jwt_required()
def pridehonda_alloc_driver():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': 1}), 401
    if not complete(request.form, ['booking_id', 'driver_id']):
        return jsonify({'success': -2, 'msg': "Incomplete form"}), 201
    booking_id = int(request.form['booking_id'])
    driver_id = int(request.form['driver_id'])
    return pridehonda_allocate(booking_id, driver_id,user)

@affiliate.route('/api/pridehonda/booking_pic', methods=['POST'])
@jwt_required()
def pridehonda_booking_pic_fetch():
    try:
        user = get_jwt_identity()
    except Exception as e:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'success': -1}), 401
    claims = get_jwt()
    if not validate_role(get_jwt_identity(), claims['roles'], [Users.ROLE_PRIDEHONDA, *Users.ROLE_ADMIN, Users.ROLE_SUPERADMIN]):
        return jsonify({'error': -2}), 401
    try:
        booking_id = int(request.form['booking_id'])
    except Exception as e:
        print(e)
        return jsonify({'success': -1})
    res = _get_booking_pic(request.form["booking_id"])
    return jsonify({"success": 1, "pic_list": res})

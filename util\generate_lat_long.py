from main import app
import requests
from time import sleep
import html
from db_config import db
from models import DriverInfo, DriverLoc
import csv
import logging
from sqlalchemy import or_
from datetime import datetime
import os

script_dir = os.path.dirname(os.path.abspath(__file__))

output_dir = os.path.join(script_dir, 'output')
os.makedirs(output_dir, exist_ok=True)

log_file_path = os.path.join(output_dir, 'generate_latlong.log')
csv_file_path = os.path.join(output_dir, 'generate_latlong.csv')

logging.basicConfig(filename=log_file_path, level=logging.INFO, format='%(asctime)s - %(message)s')

def generate_pres_region_driverinfo(skip=0):
    try:
        drivers = DriverInfo.query.filter(
            or_(DriverInfo.pres_addr_lat > 1, DriverInfo.pres_addr_lng > 1)
        ).offset(skip).all()

        count_null = 0
        count_unknown = 0
        count_non_null_non_unknown = 0
        updated_entries = []

        with open(csv_file_path, mode='a', newline='') as csv_file:
            fieldnames = ['driver_id', 'previous_region', 'new_region', 'lat', 'lng']
            writer = csv.DictWriter(csv_file, fieldnames=fieldnames)

            for driver in drivers:
                try:
                    # Explicitly check and reinitialize the database session if needed
                    if not db.session.is_active:
                        logging.info("Reinitializing the database session due to timeout.")
                        db.session.rollback()
                        db.session.close()

                    url = f"https://apis.mapmyindia.com/advancedmaps/v1/{app.config['MAP_MY_INDIA']}/rev_geocode"
                    params = {
                        'lat': driver.pres_addr_lat,
                        'lng': driver.pres_addr_lng
                    }
                    response = requests.get(url, params=params)

                    if response.status_code == 200:
                        data = response.json()
                        if 'results' in data and data['results']:
                            previous_region = driver.pres_region
                            locality = data['results'][0].get('locality') or \
                                       data['results'][0].get('village') or \
                                       data['results'][0].get('subDistrict') or \
                                       data['results'][0].get('district') or \
                                       data['results'][0].get('formatted_address')
                            driver.pres_region = html.escape(locality)
                            address_full = data['results'][0].get('formatted_address') if data['results'][0] and data['results'][0].get('formatted_address') else locality
                            driver.pres_addr = html.escape(address_full)

                            if previous_region is None or previous_region.lower() == 'unknown':
                                count_unknown += 1 if previous_region and previous_region.lower() == 'unknown' else 0
                                count_null += 1 if previous_region is None else 0
                            else:
                                count_non_null_non_unknown += 1

                            updated_entries.append(driver.driver_id)
                            db.session.commit()

                            logging.info(f'Updated driver_id {driver.driver_id}: '
                                         f'previous_region={previous_region}, new_region={driver.pres_region}, '
                                         f'lat={driver.pres_addr_lat}, lng={driver.pres_addr_lng}')

                            writer.writerow({
                                'driver_id': driver.driver_id,
                                'previous_region': previous_region,
                                'new_region': driver.pres_region,
                                'lat': driver.pres_addr_lat,
                                'lng': driver.pres_addr_lng
                            })
                        else:
                            logging.error(f'Failed to fetch data for driver_id {driver.driver_id}')
                            db.session.rollback()
                    else:
                        logging.error(f'MapMyIndia API request failed for driver_id {driver.driver_id} with status code {response.status_code}')
                        db.session.rollback()

                    # Delay between individual API requests
                    sleep(87)

                except Exception as driver_error:
                    logging.error(f'Error processing driver_id {driver.driver_id}: {str(driver_error)}')
                    db.session.rollback()

        summary = {
            'count_null': count_null,
            'count_unknown': count_unknown,
            'count_non_null_non_unknown': count_non_null_non_unknown,
            'updated_entries': updated_entries
        }

        logging.info(f"Summary: {summary}")

    except Exception as e:
        logging.error({'success': -1, 'error': 'An exception occurred', 'message': str(e)})
        db.session.remove()

if __name__ == '__main__':
    print("Started generating regions...", flush=True)
    with app.app_context():
        skip = 17024  # Define the number of drivers to skip
        generate_pres_region_driverinfo(skip=skip)

.Event-View-Container {
    z-index: 3;
    position: fixed;
    top: 20vh;
    right: 3vw;
}

/*#Event-Tick {
    font-size: 12px;
    color: white;
    text-shadow: 2px 2px 8px limegreen;
    letter-spacing: 1px;
}*/

#Event-View {
    height: 80px;
    width: 80px;
    cursor: pointer;
    border-radius: 50%;
    border: 6px solid green;
    box-shadow: 0 0 4px 2px #bdffbd;
    transition: transform 0.5s border 0.5s box-shadow: 0.5s;
    -ms-transition: -ms-transform 0.5s;
    -webkit-transition: -webkit-transform 0.5s;
    -moz-transition: -moz-transform 0.5s;
    -o-transition: -o-transform 0.5s;
}

#Event-View:hover {
    transform: rotate(20deg) scale(1.2);
    -ms-transform: rotate(20deg) scale(1.2);
    -webkit-transform: rotate(20deg) scale(1.2);
    -moz-transform: rotate(20deg) scale(1.2);
    -o-transform: rotate(20deg) scale(1.2);
    border: 6px solid orange;
    box-shadow: 0px 0px 4px 2px #ffdc9c;
}

#ThemeAddons-ChristmasRewardsModal .theme-header-background {
    background-image: url(https://wallpaperaccess.com/full/116264.jpg);
}

#ThemeAddons-ChristmasRewardsModal .modal-body {
    background: url('/static/assets/themes/Christmas-Rewards_BG.png');
}

#ThemeAddons-ChristmasRewardsModal .modal-content {
    border-radius: 20px;
}

#ThemeAddons-ChristmasRewardsModal .modal-header {
    padding: 0;
}

#ThemeAddons-ChristmasRewardsModal .theme-header-background {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    height: 30vh;
}

#ThemeAddons-ChristmasRewardsModal .modal-title {
    font-size: 40px;
    color: white;
    text-align: center;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 22vh;
}

.no-padding {
    padding: 0;
}

.no-margin {
    margin: 0;
}

#ThemeAddons-ChristmasRewardsModal .event-panel {
    max-height: 45vh;
    overflow-y: scroll;
}

.event-activity-tab {
    background: linear-gradient(to bottom, #fafafa , #aeaeae);
    border: 2px solid #bbb;
    border-radius: 12px;
    cursor: pointer;
}

.event-activity-tab.event-tab-active {
    background: linear-gradient(to bottom, #aaffaa, #88bb88);
    border: 2px solid #cceecc;
    border-radius: 12px;
}

.event-activity-tab:hover {
    border: 2px solid #ddffdd;
    border-radius: 12px;
}

.event-activity-tab h3 {
    color: black;
    text-align: center!important;
    letter-spacing: 2px!important;
    margin-top: 6px;
    margin-bottom: 6px;
}

.event-activity-tab.event-tab-active h3 {
  	color: #eee;
    text-shadow: 2px 2px #222;
}

#LoginPanel, #RewardsPanel {
    max-height: 60vh;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
}

#InfoPanel .panel {
    background: rgba(255, 255, 255, 0.8);
}

#LoginPanel .panel-heading {
    background: linear-gradient(to right, #eeffee, #99dd99, #eeffee);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

#ThemeAddons-ChristmasRewardsModal .form-control {
    max-height: 40px;
    border-radius: 10px;
    padding: 10px;
}

.Christmas-Rewards-Input-Label {
    text-align: center;
    margin: 10px;
    margin-top: 20px;
}

#Chirstmas-Rewards-Button-Submit {
    padding: 8px;
    border-radius: 16px;
}

#Christmas-Rewards-Auth-Toggle {
    cursor: pointer!important;
    color: rgba(0,0,0,0);
}

#Christmas-Rewards-Auth-Toggle:hover {
    text-decoration: underline!important;
}

#Chirstmas-Rewards-Button-OTPGen {
    padding: 5px;
    color: gold;
    border-radius: 16px;
}

.progress-image-addon {
    z-index: 4;
    top: -4px;
    position: absolute;
    height: 24px;
    display: inline;
}
  .progress-complex {
    position: relative;
    display: inline;
}
.progress {
    width: 90%;
}

#RewardsPanel .panel-heading {
    background: linear-gradient(to right, #fafae8, #fcff5e, #fafae8);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.progress-complex .progress-bar, .progress-complex .progress {
    border-radius: 16px;
    height: 15px;
}

#Phase-ICE .progress-bar {
    background-color: #22538a;
}

#Phase-ICE .progress.progress-full {
    border: 1px solid #22538a;
    box-shadow: 0 0 4px 2px #22538a;
}

#Phase-HOLLY .progress-bar {
    background-color: #0b7614;
}

#Phase-HOLLY .progress.progress-full {
    border: 1px solid #0b7614;
    box-shadow: 0 0 4px 2px #0b7614;
}

#Phase-PRESENT .progress-bar {
    background-color: #b42812;
}

#Phase-PRESENT .progress.progress-full {
    border: 1px solid #b42812;
    box-shadow: 0 0 4px 2px #b42812;
}

#Phase-STAR .progress-bar {
    background-color: #d1b200;
}

#Phase-STAR .progress.progress-full {
    border: 1px solid #d1b200;
    box-shadow: 0 0 4px 2px #d1b200;
}

.progress-bar {
    width: 0;
    animation: progress 0.5s ease-in-out forwards;
}
@keyframes progress {
    from {
        width: 0;
    }

    to {
        width: ;
    }
}

#Phase-ICE-Marker.phase-reward {
    text-shadow: 0 0 5px #22538a;
}

#Phase-HOLLY-Marker.phase-reward {
    text-shadow: 0 0 5px #0b7614;
}

#Phase-PRESENT-Marker.phase-reward {
    text-shadow: 0 0 5px #b42812;
}

#Phase-STAR-Marker.phase-reward {
    text-shadow: 0 0 5px #d1b200;
}

.phase-progress-marker {
    margin: 0;
}
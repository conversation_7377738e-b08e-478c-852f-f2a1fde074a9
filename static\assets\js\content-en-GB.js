const TripTypeInfo = {
    "InCityRound": {
        "title": "In City Round Trip",
        "theme": "primary",
        "intro": "In-City Round trips begin and end at the same location chosen by you. The location is within city bounds.",
        "base-fare": 59,
        "dur-min": 2,
        "dur-max": 10,
        "dur-unit": "HOUR",
        "ot-slab-low": 1.2,
        "ot-slab-high": 1.8,
        "ot-time-unit": "minute",
        "night-slab-low": 59,
        "night-slab-high": 89,
        "points": [
            "Fares are dynamic and are subject to change based on several factors - date, time, location, demand and others. For your convenience, the fare breakdown is shown in the estimate screen in your app.",
            "Always select the correct vehicle type and transmission type of your vehicle.",
            "The fare estimate shown is the minimum fare payable for your current booking.",
            "Trips booked at or after 9:30 P.M. have their own pricing rules.",
            "Extra charges may apply depending on the distance the D4M Companion has to travel to reach you.",
            "An extra charge (starting) <strong>₹20</strong> is applicable for automatic transmission vehicles."
        ]
    },

    "InCityOneWay": {
        "title": "In City One Way",
        "theme": "warning",
        "intro": "In-City One Way trips have different starting and destination locations both of which are selected by you. Both locations are within city bounds.",
        "base-fare": 69,
        "dur-min": 2,
        "dur-max": 10,
        "dur-unit": "HOUR",
        "ot-slab-low": 1.2,
        "ot-slab-high": 1.8,
        "ot-time-unit": "minute",
        "night-slab-low": 59,
        "night-slab-high": 89,
        "points": [
            "Fares are dynamic and are subject to change based on several factors - date, time, location, demand and others. For your convenience, the fare breakdown is shown in the estimate screen in your app.",
            "Always select the correct vehicle type and transmission type of your vehicle.",
            "The fare estimate shown is the minimum fare payable for your current booking.",
            "Trips booked at or after 9:30 P.M. have their own pricing rules.",
            "Extra charges may apply depending on the distance the D4M Companion has to travel to reach you.",
            "An extra charge (starting) <strong>₹20</strong> is applicable for automatic transmission vehicles."
        ]
    },

    "OutstationRound": {
        "title": "Outstation Round Trip",
        "theme": "danger",
        "intro": "Outstation trips have their starting locations inside the City bounds. The primary destination is outside the city. The final endpoint of the trip is the starting location itself. These trips may span multiple days and involve night stays.",
        "base-fare": 949,
        "dur-min": 1,
        "dur-max": 10,
        "dur-unit": "DAY",
        "ot-slab-low": 120,
        "ot-slab-high": 120,
        "ot-time-unit": "HOUR",
        "points": [
            "Fares are dynamic and are subject to change based on several factors - date, time, location, demand and others. For your convenience, the fare breakdown is shown in the estimate screen in your app.",
            "Always select the correct vehicle type and transmission type of your vehicle.",
            "The fare estimate shown is the minimum fare payable for your current booking.",
            "Extra charges may apply depending on the distance the D4M Companion has to travel to reach you.",
            "An extra charge (starting) <strong>₹30</strong> is applicable for automatic transmission vehicles.",
            "Charges for the driver's food and lodging are not included in the fare and <strong>must be provided by the Customer</strong> separately.",
            "Maximum driving time per day is limited to <strong>10 hours</strong>. Additional charges maybe applied if this limit is exceeded."
        ]
    },

    "OutstationMiniRound": {
        "title": "Mini Outstation Round Trip",
        "theme": "success",
        "intro": "Mini Outstation trips have their starting locations inside the City bounds. The primary destination is outside the city. The final endpoint of the trip is the starting location itself. These trips are of relatively shorter durations and do not involve night stay",
        "base-fare": 69,
        "dur-min": 4,
        "dur-max": 14,
        "dur-unit": "HOUR",
        "ot-slab-low": 1.5,
        "ot-slab-high": 2.0,
        "ot-time-unit": "minute",
        "night-slab-low": 59,
        "night-slab-high": 89,
        "points": [
            "Fares are dynamic and are subject to change based on several factors - date, time, location, demand and others. For your convenience, the fare breakdown is shown in the estimate screen in your app.",
            "Always select the correct vehicle type and transmission type of your vehicle.",
            "The fare estimate shown is the minimum fare payable for your current booking.",
            "Extra charges may apply depending on the distance the D4M Companion has to travel to reach you.",
            "An extra charge (starting) <strong>₹20</strong> is applicable for automatic transmission vehicles.",
            "In case night stay is required for the driver, the expenses for the same must be borne for the customer.",
            "Only Round trips are supported."
        ]
    }
};
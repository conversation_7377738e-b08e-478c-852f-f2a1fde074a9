tags:
  - Utilities_admin
summary: Add Admin Access
description: >
  This endpoint allows adding a new admin user or updating an existing user's role and access permissions.
  The endpoint assigns regions, tabs, notifications, and other admin-specific details such as calling number and agent ID.
  Only accessible by users with admin privileges.
parameters:
  - name: fname
    in: formData
    required: true
    type: string
    description: The first name of the admin user.
    example: "John"
  - name: lname
    in: formData
    required: true
    type: string
    description: The last name of the admin user.
    example: "Doe"
  - name: mobile
    in: formData
    required: true
    type: string
    description: The mobile number of the admin user.
    example: "9876543210"
  - name: email
    in: formData
    required: true
    type: string
    description: The email of the admin user.
    example: "<EMAIL>"
  - name: role
    in: formData
    required: true
    type: integer
    description: The role ID of the admin user.
    example: 2
  - name: regions
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of region IDs the admin will have access to.
    example: "1,2,3"
  - name: tab_access
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of tab IDs representing the tabs the admin will have access to.
    example: "1,4,7"
  - name: notifications
    in: formData
    required: false
    type: string
    description: >
      A comma-separated list of notification IDs that the admin will receive.
    example: "1,2"
  - name: calling_no
    in: formData
    required: false
    type: string
    description: >
      The calling number assigned to the admin user.
    example: "1234567890"
  - name: agent_id
    in: formData
    required: false
    type: string
    description: The agent ID for the admin user.
    example: "A1001"
responses:
  201:
    description: Successfully added admin access and logged the details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Admin access added and logged successfully"
  400:
    description: Admin access already exists for this user
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        message:
          type: string
          description: Error message
          example: "Admin access already exists for this user"
  404:
    description: User registration failed
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        message:
          type: string
          description: Error message
          example: "User registration failed"
  500:
    description: Internal server error or general failure
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        message:
          type: string
          description: Error message detailing the exception
          example: "Internal server error or exception details"

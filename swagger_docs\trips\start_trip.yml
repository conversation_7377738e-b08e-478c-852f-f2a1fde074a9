tags:
  - Trips
summary: Start a Trip
description: >
  This endpoint allows a driver to start a trip. It validates the role of the user as a driver and accepts booking IDs.
parameters:
  - name: book_id
    in: formData
    type: string
    required: true
    description: Booking ID to start the trip
  - name: c24
    in: formData
    type: integer
    required: false
    description: Indicator for C24 service (1 for true, 0 for false)
  - name: revv
    in: formData
    type: integer
    required: false
    description: Indicator for Revv service (1 for true, 0 for false)
  - name: zoomcar
    in: formData
    type: integer
    required: false
    description: Indicator for Zoomcar service (1 for true, 0 for false)
  - name: gujral
    in: formData
    type: integer
    required: false
    description: Indicator for Gujral service (1 for true, 0 for false)
  - name: olx
    in: formData
    type: integer
    required: false
    description: Indicator for OLX service (1 for true, 0 for false)
  - name: cardekho
    in: formData
    type: integer
    required: false
    description: Indicator for Cardekho service (1 for true, 0 for false)
  - name: bhandari
    in: formData
    type: integer
    required: false
    description: Indicator for Bhandari service (1 for true, 0 for false)
  - name: lat
    in: formData
    type: number
    required: false
    description: Latitude where the trip starts (optional)
  - name: lng
    in: formData
    type: number
    required: false
    description: Longitude where the trip starts (optional)
responses:
  200_a:
    description: Trip started successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Trip started successfully"
  200_b:
    description: B2B trip started successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message for B2B trips
          example: "B2B trip started successfully"
  201_a:
    description: Multiple B2B services selected (only one allowed)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error)
          example: -1
        message:
          type: string
          description: Error message
          example: "Multiple B2B services selected, only one allowed"
  201_b:
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete form)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
  401_a:
    description: User is not a driver
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized access)
          example: -1
        message:
          type: string
          description: Error message
          example: "User is not a driver"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted user)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
  500:
    description: Database error while starting the trip
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for database error)
          example: -4
        message:
          type: string
          description: Error message
          example: "DB Error"
  403:
    description: B2B booking reference not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for B2B booking reference not found)
          example: -1
        message:
          type: string
          description: Error message
          example: "B2B booking reference not found"
  404:
    description: Trip failed to start, booking issue encountered
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for trip start failure)
          example: -3
        message:
          type: string
          description: Error message
          example: "Trip failed to start, booking issue encountered"

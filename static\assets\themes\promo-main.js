$(document).ready(function() {

    var Offers = {
        "G2020": {
            "Header": "The Game of 20-20",
            "Description": "Waltz into the decade with this weekly offer. Complete 2 trips in a week to get 20 D4M Credits back. Each additional trip you complete within that week shall fetch you D4M Credits worth 20% of the estimate amount (upto 40 Credits per trip).",
            "Start": new Date(2020, 1, 24),
            "End": new Date(2020, 3, 31)
        },

        "VY": {
            "Header": "Voyager",
            "Description": "Crazy for picnics and outings? We've got just the thing for you, dear <PERSON>! For every 12 awesome hours you spend on Outstation or Mini-Outstation trips, we bring you an hour's worth of D4M Credits. Rewards are credited every fortnight and each reward cycle is limited to a maximum of 250 total Credits. How's that for a deal!",
            "Start": new Date(2020, 1, 13),
            "End": new Date(2020, 2, 2)
        },

        /*"NR": {
            "Header": "Night of Memories",
            "Description": "Love the night outs? You'll love this one then! On each successful trip that crossed midnight, get back 25% of your night charge as D4M Credits (upto 100 D4M credits per trip).",
            "Start": new Date(2020, 3, 22),
            "End": new Date(2020, 3, 28)
        },*/

        "TMK": {
            "Header": "Timekeeper's Gift",
            "Description": "Let's make the time you dedicated for us worthwhile. The timekeeper brings you a bonus 10 D4M Credits cashback for every hour of overtime, doubling the bonus after 6 hours of total overtime and tripling after 12 hours. Bonus credited every 10 days.",
            "Start": new Date(2020, 2, 15),
            "End": new Date(2020, 4, 12)
        },

        "YV": {
            "Header": "Your Verdict",
            "Description": "We would like to take this opportunity to hear your thoughts about us. We invite you to participate in our survey - 'Your Verdict'. We are eager to know about your opinions about us and your expectations from the Drivers4Me teams. Early participants will be elligible for exclusive gifts (upto 100 D4M Credits) at the end of the Survey! <br> Note: Only one response per mobile number will be considered as valid. <br> <a target='_blank' href='https://forms.gle/WiLACtPh2ziVuXmg9'>Take the Survey</a><br>",
            "Start": new Date(2020, 3, 5),
            "End": new Date(2020, 5, 6)
        }
    };

    $("body").append
    (
       '<div class="Event-View-Container">'+
            '<img id="Event-View" class="homepage-chirstmas-reward-element"' +
            'src="static/assets/images/Themes/2020.svg"/>'+
            '<i><h3 id="Event-Tick"><a target="_blank" href="https://forms.gle/WiLACtPh2ziVuXmg9">YOUR VERDICT - Take the Survey</a></h3></i>'+
        '</div>'

    );

    $("body").delegate("#Event-View", "click", function() {
        if(!$("#Promo-Modal").html()) {

            $("body").append(
                '<div class="modal fade" id="Promo-Modal" role="dialog">'+
                    '<div class="modal-dialog">'+

                        '<div class="modal-content">'+
                            '<div class="modal-header">'+
                                '<div class="theme-header-background">'+
                                    '<h4 class="modal-title">The 2020 Story</h4>'+
                                '</div>'+

                            '</div>'+
                            '<div class="modal-body">'+
                                '<div id="InfoPanel" class="row no-margin standard-top-padding event-panel">'+
                                    '<div class="panel panel-default">'+
                                        '<div class="panel-body">'+
                                            '<p><i>The best way to start a new Decade! We have several offers lined up for you this 2020.'+
                                            ' Each will be revealed in due course of time. You can view a list of all offers here. The offers'+
                                            ' that are no longer active or are yet to come are greyed out have faded display. Each has its own'+
                                            ' criteria and time frame. Each win may take upto 72 hours to get credited in your Drivers4Me Account.'+
                                            ' So lets get cracking with the trips to start winning!</i></p>'+
                                            '<hr>'+
                                            '<ul id="Offers_List">'+
                                            '</ul>'+
                                        '</div>'+
                                    '</div>'+
                                '</div>'+
                            '</div>'+
                            '<div class="modal-footer">'+
                                '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>'+
                            '</div>'+
                        '</div>'+
                    '</div>'+
                '</div>'
            );

            $.each(Offers, function(index, offerData) {
                if(((new Date()) - offerData["End"] <= 0)) {
                    var offerEndDate = offerData["End"].toLocaleString('en-GB',
                                                                        {   day: "numeric",
                                                                            month: "long",
                                                                            year: "numeric"});
                    var message = offerData["Description"] + " Offer valid upto : " + offerEndDate;
                    var inactive = false;
                    var offerStatus = "offer-inactive";
                    if(new Date() - offerData["Start"] <= 0) {
                        var offerStartDate = offerData["Start"].toLocaleString('en-GB',
                                                                        {   day: "numeric",
                                                                            month: "long",
                                                                            year: "numeric"});
                        message = "Offer available from " + offerStartDate;
                        inactive = true;
                    }
                    if(!inactive) offerStatus = "";
                    $("#Offers_List").append("<li class='offer "+ offerStatus +"'><h3>"+ offerData["Header"] +"</h3><span>"+ message +"</span></li>");
                }
            });

            $("body").delegate("#Event-View", "click", function() {
                $("#Promo-Modal").modal("show");
            });
        }
    });
});
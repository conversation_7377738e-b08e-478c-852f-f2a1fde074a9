tags:
  - Booking_admin
summary: Update booking details
description: >
  This endpoint updates the details of a specific booking based on the provided parameters.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking to be updated.
    example: 12345
  - name: search_id
    in: formData
    type: integer
    description: Optional ID to search for a specific driver or related entry.
    example: 678
  - name: start_date
    in: formData
    type: string
    format: date
    description: The new start date for the booking.
    example: "2024-10-18"
  - name: start_time
    in: formData
    type: string
    format: time
    description: The new start time for the booking.
    example: "15:00:00"
  - name: end_date
    in: formData
    type: string
    format: date
    description: The new end date for the booking.
    example: "2024-10-18"
  - name: end_time
    in: formData
    type: string
    format: time
    description: The new end time for the booking.
    example: "16:30:00"
  - name: duration
    in: formData
    type: string
    description: The new duration of the booking.
    example: "01:30:00"
  - name: remarks
    in: formData
    type: string
    description: Any remarks associated with the update.
    example: "Updated start and end times"
responses:
  200:
    description: Successfully updated booking details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Booking details updated successfully."
  400:
    description: Bad request, missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        error:
          type: string
          example: "booking_id parameter is required"
  404:
    description: Booking not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        error:
          type: string
          example: "Booking not found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"

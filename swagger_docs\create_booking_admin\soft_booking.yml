tags:
  - Create_booking_admin
summary: Soft book a driver
description: >
  This endpoint allows an admin to soft book a driver for a user. It checks for the availability of the search ID and processes the booking details. 
  Various parameters such as payment type, destination coordinates, and insurance can be passed for booking. The booking is created through multiple checks and conditions.
parameters:
  - name: search_id
    in: formData
    type: string
    required: true
    description: A comma-separated list of search IDs for which the booking should be created.
    example: "123,456"
  - name: payment_type
    in: formData
    type: string
    required: false
    description: The payment type for the booking (e.g., "cash", "card").
    example: "cash"
  - name: type
    in: formData
    type: integer
    required: false
    description: The booking type (e.g., roundtrip, one-way). Defaults to roundtrip.
    example: 1
  - name: loc
    in: formData
    type: string
    required: false
    description: The pickup location for the booking.
    example: "123 Main Street"
  - name: dest_lat
    in: formData
    type: number
    required: false
    description: The latitude of the destination.
    example: 12.9715987
  - name: dest_long
    in: formData
    type: number
    required: false
    description: The longitude of the destination.
    example: 77.594566
  - name: dest_loc
    in: formData
    type: string
    required: false
    description: The address of the destination.
    example: "456 Park Avenue"
  - name: insurance
    in: formData
    type: integer
    required: false
    description: Indicates whether insurance is included (1 for true, 0 for false).
    example: 1
responses:
  200:
    description: Successfully created the booking.
    schema:
      type: object
      properties:
        result:
          type: integer
          example: 1
        resp:
          type: object
          description: Response from the booking system or notification service.
          properties:
            msg:
              type: string
              example: "Booking created successfully"
            book_id:
              type: string
              example: "123,456"
  400:
    description: Bad request due to missing required parameters or invalid data.
    schema:
      type: object
      properties:
        result:
          type: integer
          example: -1
        message:
          type: string
          example: "Missing required parameters or invalid data"
  401:
    description: Unauthorized access due to invalid admin credentials.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized access"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        result:
          type: integer
          example: -1
        message:
          type: string
          example: "Error processing booking"

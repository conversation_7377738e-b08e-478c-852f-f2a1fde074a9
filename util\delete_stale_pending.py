from main import app
import sys
sys.path.append("/app/")
from time import sleep
from datetime import datetime, time, timedelta

from sqlalchemy import create_engine, exc, or_
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func

from booking_params import BookingParams
from db_config import db
from book_ride import get_best, create_pending_entries
from models import Bookings, BookPending, DriverSearch
from price import Price, PriceOutstation
from sqlalchemy.sql import text

SHORT_WAIT = 1 * 60
LONG_WAIT = 10 * 60
NIGHT_THRESH = [time(18,30), time(0,30)]

def time_to_sleep():
    d = datetime.utcnow().time()
    if d >= NIGHT_THRESH[0] or d <= NIGHT_THRESH[1]:
        return LONG_WAIT
    else:
        return SHORT_WAIT

def init_session(db_uri, pool_recycle):
    engine = create_engine(db_uri, pool_recycle=pool_recycle)
    Session = sessionmaker(engine)
    return Session

def delete_pending(session, book_ref):
    valid_cond = session.query(Bookings).filter(Bookings.id == book_ref). \
                                         filter(Bookings.valid != 0).first()
    if not valid_cond:
        print("Invalid booking, still in unallocated:", str(book_ref))
    else:
        print("Deleting entries for booking", book_ref, "status", valid_cond.valid)
        session.query(BookPending).filter(BookPending.book_id == book_ref).delete()
        session.commit()

def find_stale_pending(session):
    ret_list = []
    query = text("""
        SELECT book_ref, book_driver
        FROM bookings
        WHERE book_valid != 0
        AND book_ref IN (
            SELECT DISTINCT pending_book_id
            FROM book_pending_v2
        );
    """)
    # Use execution options to map results to dictionaries
    all_pending = session.execute(query).mappings()

    for booking in all_pending:
        print("Processing booking", booking["book_ref"], "allocated to driver", booking["book_driver"])
        ret_list.append(booking)
    print("Found", len(ret_list), "bookings")
    return ret_list

if __name__ == '__main__':
    with app.app_context():
        while True:
            try:
                session = init_session(app.config['SQLALCHEMY_DATABASE_URI'], 299)()
                all_unreleased = find_stale_pending(session)
                print("Found", len(all_unreleased), "stale pending entries", flush=True)
                for b in all_unreleased:
                    print("Deleting stale pending for booking", b["book_ref"], flush=True)
                    delete_pending(session, b["book_ref"])
            except exc.SQLAlchemyError as excp:
                print("Caught SQL error", str(excp))
                db.session.rollback()
                session.rollback()
            except Exception as excp:
                print("Caught exception", str(excp))
            finally:
                session.close()
                break
                time_to_sleep_sec = time_to_sleep()
                print("Sleeping for", time_to_sleep_sec, "seconds", flush=True)
                sleep(time_to_sleep_sec)
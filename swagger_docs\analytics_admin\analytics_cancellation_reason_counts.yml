tags:
  - Admin Analytics
summary: Get Cancellation Reason Counts by Stage
description: |
  This endpoint retrieves the count of cancellations grouped by the reason and stage of cancellation.
  The results are categorized by the cancellation stage: "Before Driver Allocation", "After Driver Allocation", and "Admin Cancel".
  You can filter the data by date range, time range, and region. The results are limited to the top 10 reasons per stage.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for the cancellation filter (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for the cancellation filter (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: from_time
    in: formData
    type: string
    required: false
    description: "Start time for the cancellation filter (HH:MM:SS). Defaults to '00:00:00'."
    example: "00:00:00"
  - name: to_time
    in: formData
    type: string
    required: false
    description: "End time for the cancellation filter (HH:MM:SS). Defaults to '23:59:59'."
    example: "23:59:59"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A region filter for cancellations.
      Provide a region ID or use '-1' to include all regions.
    example: "1,2,3"
responses:
  200:
    description: "Successfully retrieved cancellation reason counts by stage."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 for success)"
          example: 1
        data:
          type: array
          description: "List of cancellation stages and their respective reason counts"
          items:
            type: object
            properties:
              cancel_stage:
                type: string
                description: "The stage of cancellation"
                example: "Before Driver Allocation"
              reasons:
                type: array
                description: "List of reasons with their respective counts"
                items:
                  type: object
                  properties:
                    reason:
                      type: string
                      description: "Reason for cancellation"
                      example: "Change of Plans"
                    count:
                      type: integer
                      description: "Total number of cancellations for this reason"
                      example: 150
  400:
    description: "Invalid request or missing fields"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (-2 for invalid date, -4 for missing region)"
          example: -2
        error:
          type: string
          description: "Error message"
          example: "Missing or invalid date values"
  500:
    description: "Internal server error or database failure"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 for server error)"
          example: 0
        error:
          type: string
          description: "Error message"
          example: "Internal server error"

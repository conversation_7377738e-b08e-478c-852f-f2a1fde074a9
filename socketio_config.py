from flask_socketio import SocketIO

socketio = SocketIO(cors_allowed_origins="*", logger=True, engineio_logger=True, path='/api/socket')

def init_socketio(app, redis_url, async_mode):
    global socketio
    socketio = SocketIO(app,
                    async_mode=async_mode,
                    message_queue=redis_url,
                    cors_allowed_origins="*",
                    logger=True,
                    engineio_logger=True,
                    path='/api/socket')
    return socketio
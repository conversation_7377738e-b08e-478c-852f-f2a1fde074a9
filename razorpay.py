import requests
from flask import current_app as app

def get_auth(app):
    key_id = app.config['RAZORPAY_KEYID']
    key_secret = app.config['RAZORPAY_SECRET']
    auth = (key_id, key_secret)
    return auth

# Function to create order with basic details such as amount and currency
def create_order(order_amount, order_currency, order_receipt):

    url = f"https://api.razorpay.com/v1/orders"

    headers = {
            "content-type": "application/json",
    }

    payload = {
        "amount": order_amount,
        "currency": order_currency,
        "receipt":  order_receipt
    }

    response = requests.post(url, auth=get_auth(app), headers=headers, json=payload)

    return response

# Function to retrieve specific payments using its id
def fetch_order(paymentID):

    url = f"https://api.razorpay.com/v1/payments/{paymentID}"

    response = requests.get(url, auth=get_auth(app))

    return response

# Function to capture payment using paymentID
def capture_payment(paymentID, payment_amount, currency, auth=None):

    url = f"https://api.razorpay.com/v1/payments/{paymentID}/capture"

    headers = {
        "content-type": "application/json",
    }

    payload = {
        "amount": payment_amount,
        "currency": currency
    }
    if auth is None:
        auth = get_auth(app)

    response = requests.post(url,auth=auth,headers=headers,json=payload)

    return response
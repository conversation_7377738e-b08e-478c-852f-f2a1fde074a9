tags:
  - Calling_admin
summary: Initiate a call
description: >
  This endpoint allows an admin to initiate a call to a specified destination number. It requires the destination number and other optional parameters. The call is processed through a third-party API.
parameters:
  - name: dest_num
    in: formData
    type: string
    required: true
    description: The destination number to which the call will be initiated.
    example: "8650755357"
  - name: async
    in: formData
    type: integer
    required: false
    description: Indicates whether the call should be initiated asynchronously (1 for true, 0 for false). Defaults to 1.
    example: 1
  - name: call_timeout
    in: formData
    type: integer
    required: false
    description: The timeout for the call in seconds. Defaults to 10 seconds.
    example: 10
  - name: call_id
    in: formData
    type: integer
    required: false
    description: An identifier for the call. Defaults to 0.
    example: 0
responses:
  200:
    description: Successfully initiated the call.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        response:
          type: object
          description: Response data from the call API.
          properties:
            # Add properties according to the expected response structure from the API
            message:
              type: string
              example: "Call initiated successfully"
            call_id:
              type: string
              example: "1234567890"
  400:
    description: Bad request due to missing required parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Missing required parameters"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message details"

from datetime import datetime,date
from booking_params import BookingParams
from models import Drivers,BookingAlloc,Bookings,TripLog,BookingCancelled,Users,Trip,DriverInfo
from affiliate_b2b.affiliate_models import Affiliate, AffiliateRep,AffiliateDriverSearch,AffBookingLogs
from db_config import db
from conftest import create_b2b_booking, unique_user_data, create_user


""" Test cases for api: /api/affiliate/booking/trip_log """
# def test_affiliate_trip_log_success(client, affiliate_rep_login):
#     auth_headers, aff_rep = affiliate_rep_login

#     response_data = create_b2b_booking(client, auth_headers, aff_rep.affiliate_id, aff_rep.id)('1021')
#     assert response_data['success'] == 1
#     booking = db.session.query(Bookings).filter(Bookings.search_key == '1021').first()

#     alloc = BookingAlloc(
#         booking=booking.id,
#         driver= booking.driver,
#         alloc_user=booking.driver,
#     )
#     db.session.add(alloc)
#     db.session.commit()

#     trip_log = TripLog(
#         booking=booking.id,
#         driver=booking.driver,
#         action_user=booking.driver,
#     )
#     db.session.add(trip_log)
#     db.session.commit()

#     cancel = BookingCancelled(
#         user=None,
#         cancel_source=0,
#         booking=booking.id,
#         uid=1,
#         did=1,
#         penalty_user=0,
#         penalty_driver=0,
#     )
#     db.session.add(cancel)
#     db.session.commit()

#     # Step 2: Send POST request
#     response = client.post(
#         '/api/affiliate/booking/trip_log',
#         data={'booking_id': booking.id},
#         headers=auth_headers
#     )

#     # Step 3: Assertions
#     assert response.status_code == 200
#     assert response.json['success'] == 1
#     assert response.json['booking_id'] == booking.id
#     assert 'log_details' in response.json


def test_affiliate_trip_log_missing_booking_id(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    response = client.post('/api/affiliate/booking/trip_log', data={}, headers=auth_headers)
    assert response.status_code == 400 or response.json['success'] == 0
    assert 'booking_id' in response.json.get('error', '')

# Test 2: Invalid booking_id (non-existent)

def test_affiliate_trip_log_invalid_booking_id(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login
    response = client.post(
        '/api/affiliate/booking/trip_log',
        data={'booking_id': 9999},  # Assuming this ID doesn't exist
        headers=auth_headers
    )
    assert response.status_code == 404 or response.json['success'] == 0
    assert 'not found' in response.json.get('message', '').lower()

# Test 3: Unauthorized (No auth headers)

def test_affiliate_trip_log_unauthorized(client):
    response = client.post(
        '/api/affiliate/booking/trip_log',
        data={'booking_id': 1}  # Assuming booking ID 1 exists
    )
    assert response.status_code == 401 or response.json['success'] == 0


def test_affiliate_book_state_success(client, affiliate_rep_login):
    
    auth_headers, aff_rep = affiliate_rep_login
    aff_rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == aff_rep).first()
    response_data = create_b2b_booking(client, auth_headers, aff_rep.affiliate_id, aff_rep.id)('1022')
    assert response_data['success'] == 1
    booking = db.session.query(Bookings).filter(Bookings.search_key == '1022').first()

    book_code = booking.code
   
    # Add alloc and trip objects
    alloc = BookingAlloc(booking=booking.id, driver=booking.driver, alloc_user=None)
    db.session.add(alloc)
    trip = Trip(book=booking.id,status=Trip.TRIP_INIT, starttime=datetime.now())
    db.session.add(trip)
    db.session.commit()

    # Add trip log entry
    trip_log = TripLog(booking=booking.id, driver=booking.driver, action_user=booking.driver)
    db.session.add(trip_log)
    db.session.commit()

    # Trigger book_state API
    response = client.post(
        '/api/affiliate/book_state',
        data={'booking_code': book_code},
        headers=auth_headers
    )

    # Assertions
    assert response.status_code == 200
    assert response.json['result'] == 'SUCCESS'
    assert response.json['booking_state'] == Bookings.UNALLOCATED
    assert 'booking_history' in response.json    


def test_affiliate_book_state_failure_no_booking(client, affiliate_rep_login):
    """Test when booking_code does not match any booking"""
    auth_headers, _ = affiliate_rep_login

    response = client.post(
        '/api/affiliate/book_state',
        data={'booking_code': 'INVALID-CODE-123'},
        headers=auth_headers
    )

    assert response.status_code == 404
    assert response.json['result'] == 'FAILURE'
    assert response.json['message'] == 'Booking Not Found'


def test_affiliate_book_state_failure_incomplete_form(client, affiliate_rep_login):
    """Test when booking_code is missing from request"""
    auth_headers, _ = affiliate_rep_login

    response = client.post(
        '/api/affiliate/book_state',
        data={},
        headers=auth_headers
    )

    assert response.status_code == 400  # Assuming missing param leads to 400
    assert response.json['result'] == 'FAILURE'
    assert 'incomplete form details.' in response.json['message'].lower()


def test_affiliate_book_state_failure_unauthorized(client):
    """Test when no auth headers are provided"""
    response = client.post(
        '/api/affiliate/book_state',
        data={'booking_code': 'DUMMY-CODE'}
    )

    assert response.status_code == 401
    assert response.json['result'] == 'FAILURE'
    assert response.json ['error']['message'] == 'Unauthorized access or missing header'

# def test_fetch_partial_booking_details_success(client, affiliate_rep_login, driver_login):
#     # Setup driver and affiliate rep
#     state = driver_login
#     driver = db.session.query(Drivers).filter(Drivers.id == state['driver_id']).first()
#     driver.id = 1
#     db.session.commit()

#     auth_headers, aff_rep = affiliate_rep_login

#     # Create user
#     user_data = unique_user_data()
#     user = create_user(user_data)

#     # Required fields by the endpoint:
#     form_data = {
#         'aff_id': str(aff_rep.affiliate_id),
#         'search_ids': '1',
#         'vehicle_no': 'WB1202',
#         'loc': 'Ruby Hospital',
#         'remark': 'Test booking remark',
#         'trip_name': 'Customer to Hub',
#         'trip_type': '3',
#         'source_spoc_name': 'John Doe',
#         'source_spoc_contact': '**********',
#         # Optional fields
#         'appoint_ids': '101',
#         'vehicle_models': 'Sedan',
#         'src_nickname_id': '0',
#         'dest_nickname_id': '0',
#         'release': '1',
#         'dest_lat': '22.5839',
#         'dest_long': '88.3434',
#         'dest_loc': 'Howrah',
#     }

#     mock_search = AffiliateDriverSearch(
#         id="1",  # id should be a string since search_id is String(40)
#         affiliate= str(aff_rep.affiliate_id),  # Required
#         rep_id=1,  # Required
#         car_type=1,  # Required
#         reflat=22.57,  # Required
#         reflong=88.36,  # Required
#         dest_lat=22.5839,  # Required
#         dest_long=88.3434,  # Required
#         time=datetime.strptime("14:30", "%H:%M").time(),  # Required
#         date=datetime.strptime("2024-10-10", "%Y-%m-%d").date(),  # Required
#         dur=datetime.strptime("02:00", "%H:%M").time(),  # Required
#         timestamp=datetime.now(),  # Required
#         source="2-3",
#         estimate=100.0,
#     )
#     db.session.merge(mock_search)
#     db.session.commit()

#     # Post the form to the endpoint
#     response = client.post('/api/affiliate/book', data=form_data, headers=auth_headers)
#     assert response.status_code == 200

#     booking = db.session.query(Bookings).order_by(Bookings.id.desc()).first()
#     booking_id = booking.id
#     book_code = booking.code

#     # Insert driver details

#     driver_info = DriverInfo(
#         driver_id=driver.id,
#         pres_addr="Kolkata",
#         pres_region="East",
#         license="DL123456",
#         license_exp=date(2030, 1, 1),
#         dob=date(1990, 1, 1),
#         verf_name="John Doe",
#         verf_ph="**********",
#         verf_rel="Brother",
#         lat=22.5726,
#         lng=88.3639,
#         driver_id_doc_f="id_front.jpg",
#         driver_id_doc_b="id_back.jpg",
#         driver_lic_doc_f="lic_front.jpg",
#         driver_lic_doc_b="lic_back.jpg",
#         pic="driver_pic.jpg"
#     )
#     db.session.merge(driver_info)
#     db.session.commit()

#     # Call API
#     response = client.post(
#         '/api/affiliate/booking/partial/details',
#         data={'booking_id': str(booking_id)},
#         headers=auth_headers
#     )

#     # Assertions
#     assert response.status_code == 200
#     assert response.json['success'] == 1
#     assert response.json['booking_id'] == booking_id
#     assert 'data' in response.json
#     assert response.json['data']['basic_details']['book_code'] == book_code


def test_fetch_partial_booking_details_missing_booking_id(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login

    response = client.post(
        '/api/affiliate/booking/partial/details',
        data={},  # booking_id is missing
        headers=auth_headers
    )

    assert response.status_code == 400 
    assert response.json['success'] == -1
    assert 'Incomplete form' in response.json.get('message', '')


def test_fetch_partial_booking_details_invalid_booking_id(client, affiliate_rep_login):
    auth_headers, _ = affiliate_rep_login

    response = client.post(
        '/api/affiliate/booking/partial/details',
        data={'booking_id': '999999'},  # assuming this ID does not exist
        headers=auth_headers
    )

    assert response.status_code == 400
    assert response.json['success'] == -2
    assert 'Booking does not exist' in response.json.get('message', '')

def test_fetch_partial_booking_details_unauthorized(client):
    response = client.post(
        '/api/affiliate/booking/partial/details',
        data={'booking_id': '1'}  # assuming booking exists
    )

    assert response.status_code == 401
    assert response.json['success'] == -1
    assert response.json['error']['message'] == 'Unauthorized access or missing header'


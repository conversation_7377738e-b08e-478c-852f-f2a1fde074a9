from sqlalchemy.exc import IntegrityError
from io import Bytes<PERSON>
from unittest.mock import patch 
from models import db

#  API - /api/register/driver

def test_register_driver_success(client, mock_upload_pic):
    # Setup the mock return values for file uploads
    mock_upload_pic.side_effect = ['url_lic_f', 'url_lic_b', 'url_pic', 'url_id_f', 'url_id_b']

    # Prepare mock data for the registration
    data = {
        'fname': '<PERSON>',
        'lname': 'Doe',
        'mobile': '**********',
        'pwd': 'password',
        'lic_no': 'L123456',
        'region': 'Kolkata',
        'alt_mobile': '918888888888',
        'email': '<EMAIL>',
        'sex': 'Male',
        'perma': 1,
        'trip_pref': 3,
        'acc_no': '123456789012',
        'ifsc': 'IFSC0001',
        'pan_no': '**********',
        'lat':23,
        'lng':77,
        'region_name':'Kolkata',
        'verf_name' : '<PERSON>',
        'verf_rel'  :  'Brother',
        'verf_ph':**********,
        'id_no':'dfsdfdsfds',
        'pres_addr':"saaasasas",
        "pres_region":"Kolkata",
        "lic_exp_date":"20/04/2034",
        "dob":"20/04/2001"     
        
    }

    files = {
        'pic': (BytesIO(b'test_image_data'), 'test_pic.jpg'),
        'lic_doc': (BytesIO(b'test_license_data'), 'test_license.jpg'),
        'lic_doc_back': (BytesIO(b'test_license_back_data'), 'test_license_back.jpg'),
        'id_doc': (BytesIO(b'test_id_data'), 'test_id.jpg'),
        'id_doc_back': (BytesIO(b'test_id_back_data'), 'test_id_back.jpg')
    }

    response = client.post('/api/register/driver', data={**data, **files}, content_type='multipart/form-data')
    assert response.status_code == 200
    json_data = response.get_json()
    assert json_data['response'] == 0
    assert json_data['msg'] == "Success"

def test_register_driver_incomplete_form(client):
    # Test for incomplete form submission
    data = {
        'fname': 'John',
        'lname': 'Doe',
        'mobile': '**********',
        # 'pwd' is missing
        'lic_no': 'L123456',
        'region': 'Kolkata'
    }

    response = client.post('/api/register/driver', data=data)

    assert response.status_code == 201
    json_data = response.get_json()
    assert json_data['success'] == -1
    assert json_data['msg'] == "Incomplete form"

def test_register_driver_database_error(client, mock_upload_pic):
    # Setup the mock return values for file uploads
    mock_upload_pic.side_effect = ['url_lic_f', 'url_pic', 'url_id_f']

    # Prepare mock data for the registration
    data = {
        'fname': 'John',
        'lname': 'Doe',
        'mobile': '**********',
        'pwd': 'password',
        'lic_no': 'L123456',
        'region': 'Kolkata',
        'email': '<EMAIL>',
        'sex': 'Male',
        'perma': 1,
        'trip_pref': 3,
        'acc_no': '123456789012',
        'ifsc': 'IFSC0001',
        'pan_no': '**********',
        'lat':23,
        'lng':77,
        'region_name':'Kolkata',
        'verf_name' : 'Max',
        'verf_rel'  :  'Brother',
        'verf_ph':**********,
        'id_no':'dfsdfdsfds',
        'pres_addr':"saaasasas",
        "pres_region":"Kolkata",
        "lic_exp_date":"20/04/2034",
        "dob":"20/04/2001"
    }

    files = {
        'pic': (BytesIO(b'test_image_data'), 'test_pic.jpg'),
        'lic_doc': (BytesIO(b'test_license_data'), 'test_license.jpg'),
        'lic_doc_back': (BytesIO(b'test_license_back_data'), 'test_license_back.jpg'),
        'id_doc': (BytesIO(b'test_id_data'), 'test_id.jpg'),
        'id_doc_back': (BytesIO(b'test_id_back_data'), 'test_id_back.jpg')
    }

    # Simulate a general database error
    with patch('models.db.session.commit', side_effect=Exception("Database error")):
        response = client.post('/api/register/driver', data={**data, **files}, content_type='multipart/form-data')

        assert response.status_code == 500
        json_data = response.get_json()
        assert json_data['response'] == 4
        
# ---------------------------------
        
# def test_register_driver_integrity_error(client, mock_upload_pic):
#     # Setup the mock return values for file uploads
#     mock_upload_pic.side_effect = ['url_lic_f', 'url_pic', 'url_id_f']

#     # Prepare mock data for the registration
#     data = {
#         'fname': 'John',
#         'lname': 'Doe',
#         'mobile': '**********',
#         'pwd': 'password',
#         'lic_no': 'L123456',
#         'region': 'Kolkata',
#         'email': '<EMAIL>',
#         'sex': 'Male',
#         'perma': 1,
#         'trip_pref': 3,
#         'acc_no': '123456789012',
#         'ifsc': 'IFSC0001',
#         'pan_no': '**********',
#         'lat':23,
#         'lng':77,
#         'region_name':'Kolkata',
#         'verf_name' : 'Max',
#         'verf_rel'  :  'Brother',
#         'verf_ph':**********,
#         'id_no':'dfsdfdsfds',
#         'pres_addr':"saaasasas",
#         "pres_region":"Kolkata",
#         "lic_exp_date":"20/04/2034",
#         "dob":"20/04/2001"
#     }

#     files = {
#         'pic': (BytesIO(b'test_image_data'), 'test_pic.jpg'),
#         'lic_doc': (BytesIO(b'test_license_data'), 'test_license.jpg'),
#         'lic_doc_back': (BytesIO(b'test_license_back_data'), 'test_license_back.jpg'),
#         'id_doc': (BytesIO(b'test_id_data'), 'test_id.jpg'),
#         'id_doc_back': (BytesIO(b'test_id_back_data'), 'test_id_back.jpg')
#     }
    
#     with patch('models.db.session.add') as mock_add:
#         mock_add.side_effect = IntegrityError(
#             statement="INSERT INTO driver (acc_no, ifsc) VALUES (%s, %s)",
#             params={'acc_no': '123456789012', 'ifsc': 'IFSC0001'},
#             orig=Exception("Unique constraint failed")
#         )
        
#         # Simulate the POST request
#         response = client.post('/api/register/driver', data={**data, **files}, content_type='multipart/form-data')

#         # Assertions to verify the correct error handling
#         assert response.status_code == 403
#         json_data = response.get_json()
#         assert json_data['response'] == -3
#         assert json_data['msg'] == "Integrity constraint error"
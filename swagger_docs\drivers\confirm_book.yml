tags:
  - Driver
summary: Confirm a driver's acceptance of a booking.
description: >
  This API confirms a driver's acceptance of a booking. It checks various conditions like wallet balance, booking status, and validates the B2B booking types before confirming the driver’s allocation.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking to be accepted.
    example: 12345
  - name: c24
    in: formData
    type: integer
    required: false
    description: B2B booking flag for C24 bookings.
    example: 1
  - name: zoomcar
    in: formData
    type: integer
    required: false
    description: B2B booking flag for Zoomcar bookings.
    example: 1
  - name: olx
    in: formData
    type: integer
    required: false
    description: B2B booking flag for OLX bookings.
    example: 1
  - name: card<PERSON>ho
    in: formData
    type: integer
    required: false
    description: B2B booking flag for CarDekho bookings.
    example: 1
  - name: bhandari
    in: formData
    type: integer
    required: false
    description: B2B booking flag for Bhandari bookings.
    example: 1
  - name: mahindra
    in: formData
    type: integer
    required: false
    description: B2B booking flag for Mahindra bookings.
    example: 1
  - name: revv_v2
    in: formData
    type: integer
    required: false
    description: B2B booking flag for RevvV2 bookings.
    example: 1
  - name: spinny
    in: formData
    type: integer
    required: false
    description: B2B booking flag for Spinny bookings.
    example: 1
  - name: pridehonda
    in: formData
    type: integer
    required: false
    description: B2B booking flag for Pride Honda bookings.
    example: 1
responses:
  200_a:
    description: Booking confirmed successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: integer
          example: 1
    examples:
      application/json:
        success: 1
        msg: 1
  200_b:
    description: Slack message sent.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Slack message sent"
    examples:
      application/json:
        success: 1
        msg: "Slack message sent"
  200_c:
    description: FCM message sent.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "FCM message sent"
    examples:
      application/json:
        success: 1
        msg: "FCM message sent"
  201_a:
    description: Could not find booking ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Could not find booking id"
    examples:
      application/json:
        success: -1
        message: "Could not find booking id"
  201_b:
    description: Failed due to unknown reason.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        message:
          type: string
          example: "Failed: reason is unknown"
    examples:
      application/json:
        success: -4
        message: "Failed: reason is unknown"
  201_c:
    description: Already have a booking.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        message:
          type: string
          example: "Already have booking"
    examples:
      application/json:
        success: -5
        message: "Already have booking"
  201_d:
    description: Booking allocated to another driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        message:
          type: string
          example: "Booking is allocated"
    examples:
      application/json:
        success: -5
        message: "Booking is allocated"
  201_e:
    description: Booking cancelled by user.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -6
        message:
          type: string
          example: "Booking is cancelled by user"
    examples:
      application/json:
        success: -6
        message: "Booking is cancelled by user"
  201_f:
    description: Invalid B2B ID.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: "Invalid B2B id"
    examples:
      application/json:
        success: -3
        message: "Invalid B2B id"
  201_g:
    description: DB Integrity Error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"
  401_a:
    description: Unauthorized - not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: Driver account not enabled.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Driver account not enabled"
    examples:
      application/json:
        success: -1
        message: "Driver account not enabled"
  403:
    description: Unauthorized due to insufficient wallet balance.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Due is above threshold"
    examples:
      application/json:
        success: -2
        message: "Due is above threshold"
  500_a:
    description: Failed to commit to database.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Failed to commit to DB"
    examples:
      application/json:
        success: -1
        message: "Failed to commit to DB"
  500_b:
    description: Failed to update pending field.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        message:
          type: string
          example: "Failed to update pending field"
    examples:
      application/json:
        success: -4
        message: "Failed to update pending field"
  500_c:
    description: Could not set state, Zoomcar error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Could not set state: Zoomcar error"
    examples:
      application/json:
        success: -1
        message: "Could not set state: Zoomcar error"
  500_d:
    description: Failed to assign Bhandari booking.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Failed to assign Bhandari booking"
    examples:
      application/json:
        success: -1
        message: "Failed to assign Bhandari booking"
  500_e:
    description: Failed to assign Pride Honda booking.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Failed to assign Pride Honda booking"
    examples:
      application/json:
        success: -1
        message: "Failed to assign Pride Honda booking"
  200_d:
    description: Booking confirmed - Zoomcar state updated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Zoomcar booking state updated"
    examples:
      application/json:
        success: 1
        msg: "Zoomcar booking state updated"
  200_e:
    description: Booking confirmed - Bhandari booking SMS sent.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Bhandari booking confirmed"
    examples:
      application/json:
        success: 1
        msg: "Bhandari booking confirmed"
  200_f:
    description: Booking confirmed - Pride Honda booking SMS sent.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Pride Honda booking confirmed"
    examples:
      application/json:
        success: 1
        msg: "Pride Honda booking confirmed"
  200_g:
    description: Drivers4Me trip assignment message sent.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Drivers4Me trip assignment message sent"
    examples:
      application/json:
        success: 1
        msg: "Drivers4Me trip assignment message sent"
  200_h:
    description: Firebase message deleted successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Firebase message deleted"
    examples:
      application/json:
        success: 1
        msg: "Firebase message deleted"
  500_f:
    description: Database error during Slack/Firebase message delete.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Database error during Slack/Firebase message delete"
    examples:
      application/json:
        success: -1
        message: "Database error during Slack/Firebase message delete"
  200_i:
    description: Booking allocated successfully - No Slack/FCM message sent.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: "Booking allocated successfully, no Slack/FCM message sent"
    examples:
      application/json:
        success: 1
        msg: "Booking allocated successfully, no Slack/FCM message sent"
  500_g:
    description: Error sending FCM message.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Error sending FCM message"
    examples:
      application/json:
        success: -1
        message: "Error sending FCM message"

<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1">
  	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/custom-elements.css") }}">
    <link href="{{ url_for("static", filename="assets/css/hamburger.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/utility.css") }}">
  	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/nav-common.css") }}">
	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/bookings-section.css") }}">

	<!--script src="https://code.angularjs.org/1.6.9/angular.min.js"></script>-->
  	<script src="https://code.jquery.com/jquery-3.2.1.min.js" integrity="sha256-hwg4gsxgFZhOsEEamdOYGBf13FyQuiTwlAQgxVSNgt4=" crossorigin="anonymous"></script>
  	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/constants.js") }}"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/util-web.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/nav-common.js") }}"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/mapUtility.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/bookings-section.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/moment.js") }}"></script>
	<script src="https://wchat.freshchat.com/js/widget.js"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/freshchat.js") }}"></script>

</head>

<body>
    <nav id="brandNav" class="navbar navbar-default">
      <button id="mainNavMenuButton" class="hamburger hamburger--arrow js-hamburger" type="button">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>
	  <button id="bookingPreviewExit" class="hamburger hamburger--arrow js-hamburger is-active collapse" type="button">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>
	  <h5 id="pageTitle" class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="navbardrop" data-toggle="dropdown">Upcoming</a>
          <div id="mainBookingsMenu" class="dropdown-menu">
              <a class="dropdown-item text-body" href="#">Upcoming</a>
              <a class="dropdown-item text-body" href="#">Past</a>
          </div>
      </h5>
	  <h5 id="bookingPreviewTitle" class="nav-item collapse">Booking - <span class="booking-id-holder"></span></h5>
        <div class="container-fluid">
            <div class="navbar-header">
                <a id="brand" class="navbar-brand brand-basic" href="#">
                    <img src="{{ url_for("static", filename="assets/images/brandLogoMerger.png") }}" alt="DRIVERS4ME">
                </a>
                <a id="brandBorder" class="navbar-brand brand-basic" href="#">
                    <img src="" alt="">
                </a>
            </div>
            <div class="ml-auto">
                <div class="nav navbar-nav">
                    <a id="userProfile" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/Avatar.svg") }}">
                    </a>
                    <a class="nav-item nav-link active nav-user-info-main no-padding" href="#">
                        <p id="userName" class="nav-user-info">Very very elongated User Name</p>
                        <p id="userContact" class="nav-user-info">8882012345</p>
                        <span class="sr-only">(current)</span>
                    </a>
                    <a id="logout" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/logout.svg") }}">
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div id="mainNavMenu" class="side-nav subpage-sidenav">
        <div class="side-nav-header">
            <img id="userImageHolder" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}">
            <h6 id="userNameHolder">Sample User</h6>
            <h6 id="userContactHolder">88820 12345</h6>
        </div>
        <div class="side-nav-body">
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Bookings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Bookings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Payments.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Payments</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Refer.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Refer</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Settings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Settings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item nav-item-last row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Support.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Support</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
        </div>
        <div class="side-nav-footer">
            <p class="copyright-statement">All rights reserved. <br> &copy; Copyright <span id="copyrightYear"></span></p>
        </div>
    </div>


		<div id="bookingsCentre" class="row">
			<div id="bookingsList" class="col-lg-6 col-md-12 col-sm-12 col-12 no-padding">
				<div id="upcomingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab full-width collapse">
					<div class="row booking-entry allocated-upcoming-booking-entry">
						<input type="hidden" class="hdn-booking-id" value="12331">
						<input type="hidden" class="hdn-payment-mode" value="0">
						<input type="hidden" class="hdn-trip-type" value="1">
						<input type="hidden" class="hdn-driver-contact" value="8882012345">
						<input type="hidden" class="hdn-driver-licence" value="WB-2229727149377">
						<input type="hidden" class="hdn-driver-rating" value="4.5">
						<input type="hidden" class="hdn-src-lat" value="23.22">
						<input type="hidden" class="hdn-src-long" value="87.88">
						<input type="hidden" class="hdn-src-addr" value="24, Ramakanta Mistri Ln, Cossipore, Newland, College Square, Kolkata, West Bengal 700073, India">
						<input type="hidden" class="hdn-dest-lat" value="22.64">
						<input type="hidden" class="hdn-dest-long" value="88.7">
						<input type="hidden" class="hdn-dest-addr" value="375, Prince Anwar Shah Rd, South City Complex, Jadavpur, Kolkata, West Bengal 700068, India">
						<input type="hidden" class="hdn-veh-type" value="1">
						<input type="hidden" class="hdn-driver-pic-file" value="sample.jpg">

						<div class="col-6 col-lg-8 col-md-8 col-sm-6 align-middle no-padding">
							<div class="mr-auto booking-details">
								<h6 class="driver-name medium-bottom-margin">Driver Name</h6>
								<p class="date-time-text text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
									<span class="date-text">DD/MM/YYYY</span><span> at </span><span class="time-text">HH:MM AM</span>
								</p>
								<p class="text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Duration.svg" ) }}'>
									<span class="dur-text">4 hrs</span>
								</p>
								<p class="booking-status-text">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Upcoming.svg" ) }}'>
									<span class="stat-text">Upcoming</span>
								</p>
							</div>

						</div>
						<div class="col-6 col-lg-4 col-md-4 col-sm-6 align-middle no-padding">
							<div class="ml-auto trip-details-structure">
								<img class='trip-type-image' src='{{ url_for("static", filename="assets/images/elements/RoundTrip.svg" ) }}'>
								<p class="trip-type-text text-body">In-City (Round Trip)</p><hr class="booking-entry-separator ml-2 mr-2">
								<h5 class="no-margin">&#8377; <span class="estimate-text">326</span></h5>
								<p class="no-margin estimate-statement">Estimated Fare</p>
							</div>
						</div>
					</div>

					<div class="row booking-entry unallocated-upcoming-booking-entry">
						<input type="hidden" class="hdn-booking-id" value="12331">
						<input type="hidden" class="hdn-payment-mode" value="1">
						<input type="hidden" class="hdn-trip-type" value="2">
						<input type="hidden" class="hdn-driver-contact" value="8882012345">
						<input type="hidden" class="hdn-driver-licence" value="WB-2229727149377">
						<input type="hidden" class="hdn-driver-rating" value="4.1">
						<input type="hidden" class="hdn-src-lat" value="22.22">
						<input type="hidden" class="hdn-src-long" value="88.88">
						<input type="hidden" class="hdn-src-addr" value="24, Ramakanta Mistri Ln, Cossipore, Newland, College Square, Kolkata, West Bengal 700073, India">
						<input type="hidden" class="hdn-dest-lat" value="22.64">
						<input type="hidden" class="hdn-dest-long" value="88.7">
						<input type="hidden" class="hdn-dest-addr" value="375, Prince Anwar Shah Rd, South City Complex, Jadavpur, Kolkata, West Bengal 700068, India">
						<input type="hidden" class="hdn-veh-type" value="5">
						<input type="hidden" class="hdn-driver-pic-file" value="d0cf8408-ac32-47c4-b69a-e3164f28b2bc..jpg">
						<div class="col-6 col-lg-8 col-md-8 col-sm-6 align-middle no-padding">
							<div class="mr-auto booking-details">
								<h6 class="driver-name medium-bottom-margin">Not Allocated</h6>
								<p class="date-time-text text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
									<span class="date-text">DD/MM/YYYY</span><span> at </span><span class="time-text">HH:MM AM</span>
								</p>
								<p class="text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
									<span class="dur-text">2 hrs</span>
								</p>
								<p class="booking-status-text">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Pending.svg" ) }}'>
									<span class="stat-text">Pending</span>
								</p>
							</div>

						</div>
						<div class="col-6 col-lg-4 col-md-4 col-sm-6 align-middle no-padding">
							<div class="ml-auto trip-details-structure">
								<img class='trip-type-image' src='{{ url_for("static", filename="assets/images/elements/Oneway.svg" ) }}'>
								<p class="trip-type-text text-body">In-City (One Way)</p><hr class="booking-entry-separator ml-2 mr-2">
								<h5 class="no-margin">&#8377; <span class="estimate-text">277</span></h5>
								<p class="no-margin estimate-statement">Estimated Fare</p>
							</div>
						</div>
					</div>
			    </div>
			    <div id="pastBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab full-width">
					<div class="row booking-entry completed-booking-entry">
						<input type="hidden" class="hdn-booking-id" value="12331">
						<input type="hidden" class="hdn-payment-mode" value="0">
						<input type="hidden" class="hdn-trip-type" value="3">
						<input type="hidden" class="hdn-driver-contact" value="8882012345">
						<input type="hidden" class="hdn-driver-licence" value="WB-2229727149377">
						<input type="hidden" class="hdn-driver-rating" value="4.9">
						<input type="hidden" class="hdn-src-lat" value="22.22">
						<input type="hidden" class="hdn-src-long" value="88.88">
						<input type="hidden" class="hdn-src-addr" value="24, Ramakanta Mistri Ln, Cossipore, Newland, College Square, Kolkata, West Bengal 700073, India">
						<input type="hidden" class="hdn-dest-lat" value="20.64">
						<input type="hidden" class="hdn-dest-long" value="89.7">
						<input type="hidden" class="hdn-dest-addr" value="375, Prince Anwar Shah Rd, South City Complex, Jadavpur, Kolkata, West Bengal 700068, India">
						<input type="hidden" class="hdn-veh-type" value="3">
						<input type="hidden" class="hdn-driver-pic-file" value="sample.jpg">

						<div class="col-6 col-lg-8 col-md-8 col-sm-6 align-middle no-padding">
							<div class="mr-auto booking-details">
								<h6 class="driver-name medium-bottom-margin">Driver Name</h6>
								<p class="date-time-text text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
									<span class="date-text">DD/MM/YYYY</span><span> at </span><span class="time-text">HH:MM AM</span>
								</p>
								<p class="text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Duration.svg" ) }}'>
									<span class="dur-text">1.5 days(s)</span>
								</p>
								<p class="booking-status-text">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Completed.svg" ) }}'>
									<span class="stat-text">Completed</span>
								</p>
							</div>

						</div>
						<div class="col-6 col-lg-4 col-md-4 col-sm-6 align-middle no-padding">
							<div class="ml-auto trip-details-structure">
								<img class='trip-type-image' src='{{ url_for("static", filename="assets/images/elements/Outstation.svg" ) }}'>
								<p class="trip-type-text text-body">Outstation (Round Trip)</p><hr class="booking-entry-separator ml-2 mr-2">
								<h5 class="no-margin">&#8377; <span class="estimate-text">1886</span></h5>
								<p class="no-margin estimate-statement">Final Fare</p>
							</div>
						</div>
					</div>

					<div class="row booking-entry cancelled-booking-entry">
						<input type="hidden" class="hdn-booking-id" value="12331">
						<input type="hidden" class="hdn-payment-mode" value="1">
						<input type="hidden" class="hdn-trip-type" value="4">
						<input type="hidden" class="hdn-driver-contact" value="8882012345">
						<input type="hidden" class="hdn-driver-licence" value="WB-2229727149377">
						<input type="hidden" class="hdn-driver-rating" value="5.0">
						<input type="hidden" class="hdn-src-lat" value="22.22">
						<input type="hidden" class="hdn-src-long" value="88.88">
						<input type="hidden" class="hdn-src-addr" value="24, Ramakanta Mistri Ln, Cossipore, Newland, College Square, Kolkata, West Bengal 700073, India">
						<input type="hidden" class="hdn-dest-lat" value="21.64">
						<input type="hidden" class="hdn-dest-long" value="86.7">
						<input type="hidden" class="hdn-dest-addr" value="375, Prince Anwar Shah Rd, South City Complex, Jadavpur, Kolkata, West Bengal 700068, India">
						<input type="hidden" class="hdn-veh-type" value="5">
						<input type="hidden" class="hdn-driver-pic-file" value="d0cf8408-ac32-47c4-b69a-e3164f28b2bc..jpg">

						<div class="col-6 col-lg-8 col-md-8 col-sm-6 align-middle no-padding">
							<div class="mr-auto booking-details">
								<h6 class="driver-name medium-bottom-margin">Not Allocated</h6>
								<p class="date-time-text text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
									<span class="date-text">DD/MM/YYYY</span><span> at </span><span class="time-text">HH:MM AM</span>
								</p>
								<p class="text-body">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Duration.svg" ) }}'>
									<span class="dur-text">6 hrs</span>
								</p>
								<p class="booking-status-text">
									<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Cancelled.svg" ) }}'>
									<span class="stat-text">Cancelled</span>
								</p>
							</div>

						</div>
						<div class="col-6 col-lg-4 col-md-4 col-sm-6 align-middle no-padding">
							<div class="ml-auto trip-details-structure">
								<img class='trip-type-image' src='{{ url_for("static", filename="assets/images/elements/MiniOS.svg" ) }}'>
								<p class="trip-type-text text-body">Mini-Outstation (Round Trip)</p><hr class="booking-entry-separator ml-2 mr-2">
								<h5 class="no-margin">&#8377; <span class="estimate-text">0.00</span></h5>
								<p class="no-margin estimate-statement">Cancellation Charge</p>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div id="bookingPreview" class="col-lg-6 d-md-none d-sm-none d-none no-margin">
				<div id="map">
			    </div>
				<div class="row no-margin">
					<div class="col-lg-6 align-middle no-padding">
						<div class="mr-auto">
							<p class="no-margin">Booking ID</p>
							<h5 class="preview-booking-id text-success text-semibold"></h5>
						</div>
					</div>
					<div class="col-lg-6 align-middle no-padding">
						<div class="ml-auto">
							<button id="cancelBooking" class="btn btn-danger btn-sm preview-btn">Cancel Booking</button>
						</div>
					</div>
					<div class="col-12 no-padding"><hr class="booking-entry-separator"></div>
					<div class="col-lg-8 align-middle no-padding medium-bottom-margin">
						<div class="mr-auto preview-booking-details">
							<p class="date-time-text text-body">
								<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
								<span class="date-text"></span><span> at </span><span class="time-text"></span>
							</p>
							<p class="text-body">
								<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Duration.svg" ) }}'>
								<span class="preview-dur-text"></span>
							</p>
							<p class="booking-status-text">
								<img class="booking-entry-icon-img preview-status-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Upcoming.svg" ) }}'>
								<span class="theme-text preview-stat-text"></span>
							</p>
						</div>
					</div>
					<div class="col-lg-4 align-middle no-padding medium-bottom-margin">
						<div class="ml-auto payment-details">
							<p class="no-margin preview-estimate-statement">Estimated Fare</p>
							<h5 class="no-margin theme-text medium-bottom-margin theme-text">&#8377; <span class="preview-estimate-text"></span></h5>
							<p class="no-margin payment-mode-text"></p>&emsp;<img class="payment-mode-image" src='{{ url_for("static", filename="assets/images/elements/Cash.svg" ) }}'>
						</div>
					</div>
					<input type="hidden" class="hdn-preview-src-lat" value="0">
					<input type="hidden" class="hdn-preview-src-long" value="0">
					<input type="hidden" class="hdn-preview-dest-lat" value="0">
					<input type="hidden" class="hdn-preview-dest-long" value="0">
					<button id="mapRefresh" class="collapse">Set Location</button>
					<div class="col-12 align-middle no-padding location-graphics">
						<div class="row">
							<div class="col-lg-1 vertically-centered">
								<img class="preview-location-pin" src='{{ url_for("static", filename="assets/Green_pin_96sq.png" ) }}'>
							</div>
							<div class="col-lg-11 vertically-centered">
								<p class="source-text"></p>
							</div>
						</div>
					</div>
					<div class="col-12 align-middle no-padding location-graphics">
						<div class="row">
							<div class="col-lg-1 vertically-centered">
								<img class="preview-location-pin" src='{{ url_for("static", filename="assets/Red_pin_96sq.png" ) }}'>
							</div>
							<div class="col-lg-11 vertically-centered">
								<p class="destination-text"></p>
							</div>
						</div>
					</div>
					<div class="col-12 no-padding"><hr class="booking-entry-separator"></div>
					<div class="col-4 booking-preview-graphics car-type">
						<p>Car Type</p>
						<img class="preview-vehicle-type-image" src='{{ url_for("static", filename="assets/images/elements/Hatchback.svg" ) }}'>
						<h6 class="preview-vehicle-type text-success"></h6>
					</div>
					<div class="col-4 booking-preview-graphics">
						<p>Trip Type</p>
						<img class="preview-trip-type-image" src='{{ url_for("static", filename="assets/images/elements/RoundTrip.svg" ) }}'>
						<h6 class="preview-trip-type text-success"></h6>
					</div>
					<div class="col-4 booking-preview-graphics">
						<p>Gear Type</p>
						<img class="preview-transmission-type-image" src='{{ url_for("static", filename="assets/images/elements/gearType_manual.png" ) }}'>
						<h6 class="preview-transmission-type text-success"></h6>
					</div>
					<div class="col-12 no-padding"><hr class="booking-entry-separator"></div>
					<div class="col-12 align-middle no-padding preview-driver">
						<div class="row">
							<div class="col-lg-1 vertically-centered">
								<img class="preview-driver-image" src='{{ url_for("static", filename="assets/images/elements/user_placeholder.svg" ) }}'>
							</div>
							<div class="col-lg-11 vertically-centered type-upcoming-allocated collapse">
								<div class="d-inline">
									<h6 class="preview-driver-name theme-text"></h6>
									<p class="preview-licence d-inline"></p>
								</div>
								<div class="rating-section">
									<p><i class="fa fa-star"></i> <span class="preview-rating"></span></p>
								</div>
							</div>
							<div class="col-lg-11 vertically-centered type-upcoming-unallocated collapse">
								<div><h6 class="preview-driver-name theme-text">Not Allocated</h6></div>
							</div>
							<div class="col-lg-11 vertically-centered type-completed collapse">
								<div class="d-inline">
									<p>You Rated</p>
									<h6 class="preview-driver-name d-inline theme-text"></h6>
								</div>
								<div class="rating-section">
									<p><i class="fa fa-star"></i> <span class="preview-rating"></span></p>
								</div>
							</div>
							<div class="col-lg-11 vertically-centered type-cancelled-unallocated">
								<div><h6 class="preview-driver-name theme-text">Not Allocated</h6></div>
							</div>
							<div class="col-lg-11 vertically-centered type-cancelled-allocated collapse">
								<div><h6 class="preview-driver-name theme-text"></h6></div>
							</div>
							<div class="col-12 no-padding medium-bottom-margin"><hr class="booking-entry-separator"></div>
							<div class="col-12 medium-bottom-margin"><h6 class="help-section-text text-success">Help</h6></div>
							<div class="col-12 no-padding vertically-centered upcoming-booking-only help-topic">
								<p class="mr-auto d-inline">Get in touch with the driver.</p>
								<button id="faqButton" class="ml-auto btn btn-sm btn-success">Call</button>
							</div>
							<div class="col-12 no-padding medium-bottom-margin"><hr class="booking-entry-separator"></div>
							<div class="col-12 no-padding vertically-centered help-topic">
								<p class="mr-auto d-inline">Issue with the booking?</p>
								<button id="chatButton" class="ml-auto btn btn-sm btn-success">Chat</button>
							</div>
							<div class="col-12 no-padding medium-bottom-margin"><hr class="booking-entry-separator"></div>
							<div class="col-12 no-padding vertically-centered help-topic">
								<p class="mr-auto d-inline">Have Questions?</p>
								<button id="faqButton" class="ml-auto btn btn-sm btn-success">FAQ</button>
							</div>
						</div>
					</div>
				</div>
			</div>


		</div>
		<div id="respBookingPreview" class="d-lg-none d-block no-margin">
			<div id="respMap">
			</div>
			<div class="row no-margin">
				<div class="col-6 align-middle no-padding">
					<div class="mr-auto">
						<p class="no-margin">Booking ID</p>
						<h5 class="preview-booking-id text-success text-semibold"></h5>
					</div>
				</div>
				<div class="col-6 align-middle no-padding">
					<div class="ml-auto">
						<button id="cancelBooking" class="btn btn-danger btn-sm preview-btn">Cancel Booking</button>
					</div>
				</div>
				<div class="col-12 no-padding"><hr class="booking-entry-separator"></div>
				<div class="col-8 align-middle no-padding medium-bottom-margin">
					<div class="mr-auto preview-booking-details">
						<p class="date-time-text text-body">
							<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-DateTime.svg" ) }}'>
							<span class="date-text"></span><span> at </span><span class="time-text"></span>
						</p>
						<p class="text-body">
							<img class="booking-entry-icon-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Duration.svg" ) }}'>
							<span class="preview-dur-text"></span>
						</p>
						<p class="booking-status-text">
							<img class="booking-entry-icon-img preview-status-img" src='{{ url_for("static", filename="assets/images/elements/bookingHistory-Upcoming.svg" ) }}'>
							<span class="theme-text preview-stat-text"></span>
						</p>
					</div>
				</div>
				<div class="col-4 align-middle no-padding medium-bottom-margin">
					<div class="ml-auto payment-details">
						<p class="no-margin preview-estimate-statement">Estimated Fare</p>
						<h5 class="no-margin theme-text medium-bottom-margin theme-text">&#8377; <span class="preview-estimate-text"></span></h5>
						<p class="no-margin payment-mode-text"></p>&emsp;<img class="payment-mode-image" src='{{ url_for("static", filename="assets/images/elements/Cash.svg" ) }}'>
					</div>
				</div>
				<input type="hidden" class="hdn-preview-src-lat" value="0">
				<input type="hidden" class="hdn-preview-src-long" value="0">
				<input type="hidden" class="hdn-preview-dest-lat" value="0">
				<input type="hidden" class="hdn-preview-dest-long" value="0">
				<button id="respMapRefresh" class="collapse">Set Location</button>
				<div class="col-12 align-middle no-padding location-graphics">
					<div class="row">
						<div class="col-1 vertically-centered">
							<img class="preview-location-pin" src='{{ url_for("static", filename="assets/Green_pin_96sq.png" ) }}'>
						</div>
						<div class="col-11 vertically-centered">
							<p class="source-text"></p>
						</div>
					</div>
				</div>
				<div class="col-12 align-middle no-padding location-graphics">
					<div class="row">
						<div class="col-1 vertically-centered">
							<img class="preview-location-pin" src='{{ url_for("static", filename="assets/Red_pin_96sq.png" ) }}'>
						</div>
						<div class="col-11 vertically-centered">
							<p class="destination-text"></p>
						</div>
					</div>
				</div>
				<div class="col-12 no-padding"><hr class="booking-entry-separator"></div>
				<div class="col-4 booking-preview-graphics car-type">
					<p>Car Type</p>
					<img class="preview-vehicle-type-image" src='{{ url_for("static", filename="assets/images/elements/Hatchback.svg" ) }}'>
					<h6 class="preview-vehicle-type text-success"></h6>
				</div>
				<div class="col-4 booking-preview-graphics">
					<p>Trip Type</p>
					<img class="preview-trip-type-image" src='{{ url_for("static", filename="assets/images/elements/RoundTrip.svg" ) }}'>
					<h6 class="preview-trip-type text-success"></h6>
				</div>
				<div class="col-4 booking-preview-graphics">
					<p>Gear Type</p>
					<img class="preview-transmission-type-image" src='{{ url_for("static", filename="assets/images/elements/gearType_Manual.png" ) }}'>
					<h6 class="preview-transmission-type text-success"></h6>
				</div>
				<div class="col-12 no-padding"><hr class="booking-entry-separator"></div>
				<div class="col-12 align-middle no-padding preview-driver">
					<div class="row">
						<div class="col-1 vertically-centered">
							<img class="preview-driver-image" src='{{ url_for("static", filename="assets/images/elements/user_placeholder.svg" ) }}'>
						</div>
						<div class="col-11 vertically-centered type-upcoming-allocated collapse">
							<div class="d-inline">
								<h6 class="preview-driver-name text-success theme-text"></h6>
								<p class="preview-licence d-inline"></p>
							</div>
							<div class="rating-section">
								<p><i class="fa fa-star"> </i><span class="preview-rating"></span></p>
							</div>
						</div>
						<div class="col-11 vertically-centered type-upcoming-unallocated collapse">
							<div><h6 class="preview-driver-name theme-text">Not Allocated</h6></div>
						</div>
						<div class="col-11 vertically-centered type-completed collapse">
							<div class="d-inline">
								<p>You Rated</p>
								<h6 class="preview-driver-name d-inline theme-text"></h6>
							</div>
							<div class="rating-section">
								<p><i class="fa fa-star"> </i><span class="preview-rating"></span></p>
							</div>
						</div>
						<div class="col-11 vertically-centered type-cancelled-unallocated collapse">
							<div><h6 class="preview-driver-name theme-text"></h6></div>
						</div>
						<div class="col-11 vertically-centered type-cancelled-allocated collapse">
							<div><h6 class="preview-driver-name theme-text"></h6></div>
						</div>
						<div class="col-12 no-padding medium-bottom-margin"><hr class="booking-entry-separator"></div>
						<div class="col-12 medium-bottom-margin"><h6 class="help-section-text text-success">Help</h6></div>
						<div class="col-12 vertically-centered upcoming-booking-only help-topic">
							<p class="mr-auto d-inline">Get in touch with the driver.</p>
							<button id="faqButton" class="ml-auto btn btn-sm btn-success">Call</button>
						</div>
						<div class="col-12 no-padding medium-bottom-margin"><hr class="booking-entry-separator"></div>
						<div class="col-12 vertically-centered help-topic">
							<p class="mr-auto d-inline">Issue with the booking?</p>
							<button id="chatButton" class="ml-auto btn btn-sm btn-success">Chat</button>
						</div>
						<div class="col-12 no-padding medium-bottom-margin"><hr class="booking-entry-separator"></div>
						<div class="col-12 vertically-centered help-topic">
							<p class="mr-auto d-inline">Have Questions?</p>
							<button id="faqButton" class="ml-auto btn btn-sm btn-success">FAQ</button>
						</div>
					</div>
				</div>
			</div>
		</div>


    <!-- SNACKBAR -->
    <div id="snackbar"></div>

    <!-- LOADER -->
    <div id="loader" class="collapse">
        <div class="backdrop"></div>
        <div id="spinner-container">
            <div class="spinner">
                <div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div>
            </div>
        </div>
    </div>

<script>
	var pickupMarker, destinationMarker, respPickupMarker, respDestinationMarker,
	srcLat = KOLKATA_CENTER["lat"],
	srcLong = KOLKATA_CENTER["lng"],
	destLat = KOLKATA_CENTER["lat"],
	destLong = KOLKATA_CENTER["lng"];

	function staticMap() {
		map = new google.maps.Map(document.getElementById('map'), {
			zoom: 11,
			mapTypeControl: false,
			streetViewControl: false,
			fullscreenControl: false,
			draggable: false,
			zoomControl: false,
			scrollwheel: false,
			disableDoubleClickZoom: true,

			clickableIcons: false,
			gestureHandling: 'greedy',
			styles: D4M_DEFAULT_MAP_THEME,

		});

		respMap = new google.maps.Map(document.getElementById('respMap'), {
			zoom: 11,
			mapTypeControl: false,
			streetViewControl: false,
			fullscreenControl: false,
			draggable: false,
			zoomControl: false,
			scrollwheel: false,
			disableDoubleClickZoom: true,

			clickableIcons: false,
			gestureHandling: 'greedy',
			styles: D4M_DEFAULT_MAP_THEME,

		});

	  pickupMarker = new google.maps.Marker(
	  {
	  	map:map,
	  	draggable:false,
	  	animation: google.maps.Animation.DROP,
	  	position: new google.maps.LatLng(srcLat, srcLong),
	  	icon: {
		    url: "{{ url_for("static", filename="assets/Green_pin_96sq.png") }}", // url
		    scaledSize: new google.maps.Size(40, 40), // scaled size
		    origin: new google.maps.Point(0,0), // origin
		    anchor: new google.maps.Point(0, 0) // anchor
		}
	  });

		destinationMarker = new google.maps.Marker(
		{
			map:map,
			draggable:false,
			animation: google.maps.Animation.DROP,
			position: new google.maps.LatLng(destLat, destLong),
			icon: {
			    url: "{{ url_for("static", filename="assets/Red_pin_96sq.png") }}", // url
			    scaledSize: new google.maps.Size(40, 40), // scaled size
			    origin: new google.maps.Point(0,0), // origin
			    anchor: new google.maps.Point(0, 0) // anchor
			}
		});

		respPickupMarker = new google.maps.Marker(
		{
			map:respMap,
			draggable:false,
			animation: google.maps.Animation.DROP,
			position: new google.maps.LatLng(srcLat, srcLong),
			icon: {
			    url: "{{ url_for("static", filename="assets/Green_pin_96sq.png") }}", // url
			    scaledSize: new google.maps.Size(40, 40), // scaled size
			    origin: new google.maps.Point(0,0), // origin
			    anchor: new google.maps.Point(0, 0) // anchor
			}
		});

		respDestinationMarker = new google.maps.Marker(
	    {
	    	map:respMap,
	    	draggable:false,
	    	animation: google.maps.Animation.DROP,
	    	position: new google.maps.LatLng(destLat, destLong),
	    	icon: {
		  	    url: "{{ url_for("static", filename="assets/Red_pin_96sq.png") }}", // url
		  	    scaledSize: new google.maps.Size(40, 40), // scaled size
		  	    origin: new google.maps.Point(0,0), // origin
		  	    anchor: new google.maps.Point(0, 0) // anchor
		  	}
	    });

	  //Create LatLngBounds object.
	  var latlngbounds = new google.maps.LatLngBounds();
	  latlngbounds.extend(pickupMarker.position);
	  latlngbounds.extend(destinationMarker.position);
	  var bounds = new google.maps.LatLngBounds();
	  map.setCenter(latlngbounds.getCenter());
	  map.fitBounds(latlngbounds);
	  respMap.fitBounds(latlngbounds);

	google.maps.event.addDomListener(document.getElementById("mapRefresh"), 'click', function() {
		var previewElement = document.getElementById("bookingPreview");
		var pMarkerLat = parseFloat(previewElement.getElementsByClassName("hdn-preview-src-lat")[0].value);
		var pMarkerLong = parseFloat(previewElement.getElementsByClassName("hdn-preview-src-long")[0].value);
		var dMarkerLat = parseFloat(previewElement.getElementsByClassName("hdn-preview-dest-lat")[0].value);
		var dMarkerLong = parseFloat(previewElement.getElementsByClassName("hdn-preview-dest-long")[0].value);
		pickupMarker.setPosition(new google.maps.LatLng(pMarkerLat, pMarkerLong));
		destinationMarker.setPosition(new google.maps.LatLng(dMarkerLat, dMarkerLong));
		//Create LatLngBounds object.
	    var latlngbounds = new google.maps.LatLngBounds();
	    latlngbounds.extend(pickupMarker.position);
	    latlngbounds.extend(destinationMarker.position);
	    var bounds = new google.maps.LatLngBounds();
	    map.setCenter(latlngbounds.getCenter());
	    map.fitBounds(latlngbounds);
	});

	google.maps.event.addDomListener(document.getElementById("respMapRefresh"), 'click', function() {
		var previewElement = document.getElementById("respBookingPreview");
		var pMarkerLat = parseFloat(previewElement.getElementsByClassName("hdn-preview-src-lat")[0].value);
		var pMarkerLong = parseFloat(previewElement.getElementsByClassName("hdn-preview-src-long")[0].value);
		var dMarkerLat = parseFloat(previewElement.getElementsByClassName("hdn-preview-dest-lat")[0].value);
		var dMarkerLong = parseFloat(previewElement.getElementsByClassName("hdn-preview-dest-long")[0].value);
		respPickupMarker.setPosition(new google.maps.LatLng(pMarkerLat, pMarkerLong));
		respDestinationMarker.setPosition(new google.maps.LatLng(dMarkerLat, dMarkerLong));
		//Create LatLngBounds object.
	    var latlngbounds = new google.maps.LatLngBounds();
	    latlngbounds.extend(pickupMarker.position);
	    latlngbounds.extend(destinationMarker.position);
	    var bounds = new google.maps.LatLngBounds();
	    respMap.setCenter(latlngbounds.getCenter());
	    respMap.fitBounds(latlngbounds);
	});

	}
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBYzKp4rmUW215GsTgaPS8U5wYxLXZwhpI&callback=staticMap&libraries=places"></script>
</body>

</html>

from flask import current_app as app
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token
import datetime
from conftest import create_user_and_driver,unique_user_data

#   API - /token/remove -----------

def test_logout(client):
    data = unique_user_data()
    user,driver = create_user_and_driver(data)
    expires_access = datetime.timedelta(days=365)
    # Create a test user and get a JWT token
    identity_with_claims = {
        'id': user.id,
        'roles': 1,
        'region': user.region,
        'fname': user.fname,
        'lname': user.lname,
    }
    with app.app_context():
        access_token = create_access_token(identity=identity_with_claims, additional_claims=identity_with_claims, expires_delta=expires_access)
    headers = {'Authorization': f'Bearer {access_token}'}

    # Call the logout endpoint
    response = client.post('/token/remove', headers=headers)

    assert response.status_code == 200
    response_json = response.get_json()
    assert response_json['logout'] is True

    # Check if the specified cookies are unset (i.e., they are empty)
    set_cookie_header = response.headers.getlist('Set-Cookie')

    assert any('restore_id=; Expires=' in cookie for cookie in set_cookie_header)
    assert any('name=; Expires=' in cookie for cookie in set_cookie_header)
    assert any('user_id=; Expires=' in cookie for cookie in set_cookie_header)


#   ---------------------------------
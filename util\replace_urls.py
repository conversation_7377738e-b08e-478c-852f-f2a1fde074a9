import os
import re
import json

def replace_flask_urls_in_files(folder_path, mapping_file):
    # Load the mapping from the JSON file
    with open(mapping_file, "r") as f:
        mappings = json.load(f)

    # Compile the regex pattern to match flask_util.url_for('key') calls
    pattern = re.compile(r"flask_util\.url_for\(['\"](.*?)['\"]\)")

    # Iterate over all files in the folder recursively
    for root, _, files in os.walk(folder_path):
        for file in files:
            file_path = os.path.join(root, file)

            # Only process .js files
            if file.endswith(".js"):
                print(f"Processing file: {file_path}")  # Debugging line
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                # Check if the pattern exists in the file
                matches = list(pattern.finditer(content))
                if matches:
                    print(f"Pattern found in file: {file_path}")
                else:
                    print(f"No pattern found in file: {file_path}")

                # Find and replace matches in the file
                def replacer(match):
                    key = match.group(1)
                    print(f"Found key: {key}")  # Debugging line
                    if key in mappings:
                        return f"window.location.protocol + '//' + window.location.host + '{mappings[key]}'"
                    return match.group(0)  # No replacement if key is not in mapping

                new_content = pattern.sub(replacer, content)

                # Write the updated content back to the file if changes were made
                if content != new_content:
                    with open(file_path, "w", encoding="utf-8") as f:
                        f.write(new_content)
                    print(f"Updated: {file_path}")

if __name__ == "__main__":
    folder_to_scan = r"<here add your path of folder where all js files placed>" # Example - r"C:\\Users\\<USER>\\Desktop\\new_admin\\admin-backend-main-3.11\\static"
    mapping_file = "mapping.json"

    replace_flask_urls_in_files(folder_to_scan, mapping_file)
    print("Replacement completed!")

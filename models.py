#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  models.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import sys
sys.path.insert(1, '/home/<USER>/drivers4me-web-prod')
import html
import hashlib
import uuid
from datetime import datetime, timedelta
from booking_params import BookingParams

from sqlalchemy import PrimaryKeyConstraint, Table,create_engine
from numpy import base_repr
from _utils import get_salt, get_pwd, get_ref_code, gen_otp
from db_config import db
from booking_params import Regions, BookingParams
from sqlalchemy.ext.declarative import declarative_base

from flask import current_app as app


class Users(db.Model, Regions):
    ROLE_USER = 0
    ROLE_DRIVER = 1
    ROLE_ADMIN = [2,21,22,31,32,33,34,41,51,61,71,81]
    # ROLE_ADMIN = 2
    ROLE_C24 = 3
    ROLE_REVV = 4
    ROLE_ZOOMCAR = 5
    ROLE_GUJRAL = 6
    ROLE_OLX = 7
    ROLE_CARDEKHO = 8
    ROLE_BHANDARI = 9
    ROLE_MAHINDRA = 10
    ROLE_REVV_V2 = 11
    ROLE_SPINNY = 12
    ROLE_PRIDEHONDA = 13
    ROLE_CLUSTER_HEAD = 21
    ROLE_CITY_HEAD = 22
    ROLE_OP_MANAGER = 31 # Operation Manager
    ROLE_OP_SUP = 32 # Operation Supervisor
    ROLE_OP_COOR = 33 # Operation Coordinator
    ROLE_OP_EXEC = 34 # Operation Executive
    ROLE_BD = 41 # Business Development
    ROLE_MARKETING = 51
    ROLE_CUS_SUPPORT = 61 # Customer Support
    ROLE_HR = 71
    ROLE_ACCOUNTANT = 81

    ROLE_SUPERADMIN = 127
    ROLE_SPINNY_TEAM = 121
    NON_ADMIN_ROLES = [0,1,3,4,5,6,7,8,9,10,11,12,13]

    SEX_MALE = 0
    SEX_FEMALE = 1
    SEX_OTHER = 2

    LABEL_VIP = 1
    LABEL_ISSUE = 2
    LABEL_BAD = 3
    LABEL_CANCEL = 4
    LABEL_OTHER = 32
    
    VALID_COUNTRY_CODES = {"91", "1", "44", "61", "81"} 
    # Reason Codes for Account Deletion
    RSN_HAVE_OWN_DRIVER = 0             # User has their own driver.
    RSN_NOTIFICATIONS_ISSUE = 1            # User is facing notification issues.
    RSN_FACING_ISSUES = 2                 # User is facing general issues.
    RSN_CREATING_NEW_ACCOUNT = 3          # User wants to create a new account.
    RSN_MOBILE_NUMBER_CHANGED = 4         # User has changed their mobile number.
    RSN_NO_LONGER_NEEDED = 5              # User no longer needs the account.
    RSN_FOUND_ANOTHER_APP = 6             # User found another app/service.
    RSN_OTHER = 7                       # Other reason.
    
    VALID_REASON_CODES = {
        RSN_HAVE_OWN_DRIVER,
        RSN_NOTIFICATIONS_ISSUE,
        RSN_FACING_ISSUES,
        RSN_CREATING_NEW_ACCOUNT,
        RSN_MOBILE_NUMBER_CHANGED,
        RSN_NO_LONGER_NEEDED,
        RSN_FOUND_ANOTHER_APP,
        RSN_OTHER,
    }

    id = db.Column('user_id', db.Integer, primary_key=True)
    mobile = db.Column('user_mobile', db.String(15), unique=True)
    country_code = db.Column('user_mobile_country_code', db.String(15))
    fname = db.Column('user_fname', db.Text)
    lname = db.Column('user_lname', db.Text)
    email = db.Column('user_email', db.Text, unique=True)
    sex = db.Column('user_sex', db.Text)
    pwd = db.Column('user_password', db.Text)
    salt = db.Column('user_pwd_salt', db.Text)
    role = db.Column('user_role', db.Integer)
    region = db.Column('user_region', db.Integer)
    reg = db.Column('user_registration', db.DateTime)
    credit = db.Column('user_credit', db.Float)
    enabled = db.Column('user_acc_enabled', db.Boolean)
    ref_code = db.Column('user_ref_code', db.String)
    restore_id = db.Column('user_restore_id', db.String)
    marked = db.Column('user_marked', db.Boolean)
    label_bv = db.Column('user_label_bv', db.Integer)
    alt_mobile = db.Column('user_alt_mobile', db.String(15))
    customer_remark = db.Column('customer_remarks', db.String(250))
    bookcount = db.Column('user_bookcount', db.Integer, default=0) # user's no of bookings
    is_email_verified = db.Column('is_verified_email',db.Boolean, default=False, nullable=False)
    emergency_contact_country_code = db.Column(db.String(10), nullable=True)
    emergency_contact_number = db.Column(db.String(20), nullable=True)

    def is_authenticated(self):
        return True

    def is_active(self):
        return True

    def is_anonymous(self):
        return False

    def get_id(self):
        return str(self.id)

    def get_name(self):
        if not self.lname:
            return self.fname
        elif not self.fname:
            return self.lname
        else:
            return self.fname + " " + self.lname

    def get_fname(self):
        return self.fname

    def get_lname(self):
        return self.lname

    @staticmethod
    def convert_sex(sex_s):
        if sex_s == 'Male':
            return Users.SEX_MALE
        elif sex_s == 'Female':
            return Users.SEX_FEMALE
        elif sex_s == 'Other':
            return Users.SEX_OTHER
        return Users.SEX_MALE

    def __init__(self, fname, lname, mobile, email, pwd, role, region=0,alt_mobile=None,customer_remark="",enabled =True, country_code="91"):
        self.mobile = mobile
        self.fname = html.escape(fname)
        self.lname = html.escape(lname)
        if len(email) == 0:
            self.email = None
        else:
            self.email = html.escape(email)
        self.salt = get_salt()
        self.pwd = get_pwd(pwd, self.salt)
        self.restore_id = ""
        self.role = role
        self.reg = datetime.utcnow()
        self.enabled = enabled
        self.credit = 0
        self.restore_id = ""
        self.ref_code = ""
        self.region = region
        self.marked = False
        self.label_bv = 0
        self.alt_mobile = mobile if alt_mobile == None else alt_mobile
        self.customer_remark = customer_remark
        self.bookcount = 0
        self.country_code = country_code
        self.is_email_verified = False
        self.emergency_contact_country_code = None
        self.emergency_contact_number = None

class DeletedUser(db.Model):
    RSN_HAVE_OWN_DRIVER = 0
    RSN_NOTIFICATIONS_ISSUE = 1
    RSN_FACING_ISSUES = 2
    RSN_CREATING_NEW_ACCOUNT = 3
    RSN_MOBILE_NUMBER_CHANGED = 4
    RSN_NO_LONGER_NEEDED = 5
    RSN_FOUND_ANOTHER_APP = 6
    RSN_OTHER = 7
    RSN_ADMIN_DELETE = 127
    
    VALID_REASON_CODES = {
        RSN_HAVE_OWN_DRIVER,
        RSN_NOTIFICATIONS_ISSUE,
        RSN_FACING_ISSUES,
        RSN_CREATING_NEW_ACCOUNT,
        RSN_MOBILE_NUMBER_CHANGED,
        RSN_NO_LONGER_NEEDED,
        RSN_FOUND_ANOTHER_APP,
        RSN_OTHER,
    }

    __tablename__ = 'deleted_user'
    id = db.Column('deleted_id', db.Integer, primary_key=True, autoincrement=True)
    userid = db.Column('deleted_user_id', db.Integer)
    reason = db.Column('deleted_user_reason', db.Integer, default=None)
    mobile = db.Column('deleted_user_mobile', db.Integer)
    newmobile = db.Column('deleted_user_newmobile', db.Integer)
    timestamp = db.Column('deleted_user_timestamp', db.DateTime)

    def __init__(self, userid, mobile, newmobile, reason=7):
        self.userid = userid
        self.mobile = mobile
        self.newmobile = newmobile
        self.reason = reason
        self.timestamp = datetime.utcnow()

class MobileChange(db.Model):
    __tablename__ = 'mobile_change'
    pid = db.Column('mobile_change_pid', db.Integer, primary_key=True, autoincrement=True)
    id = db.Column('mobile_change_id', db.Integer)
    mobile = db.Column('mobile_change_mobile', db.String(15))
    change_mobile = db.Column('mobile_change_new_mobile', db.String(15))
    timestamp = db.Column('mobile_change_timestamp', db.DateTime)

    def __init__(self, userid, mobile, change_mobile):
        self.id = userid
        self.mobile = mobile
        self.change_mobile = change_mobile
        self.timestamp = datetime.utcnow()


class Drivers(db.Model):
    APPROVED=1
    UNAPPROVED=-1
    PENDING=-2
    INACTIVE=-3
    BANNED =-4
    STATUS_MAP = {
        APPROVED: "Approved",
        UNAPPROVED: "Unapproved",
        PENDING: "Pending",
        INACTIVE: "Inactive",
        BANNED: "Banned"
    }
    
    id = db.Column('driver_id', db.Integer, primary_key=True)
    user = db.Column('driver_user', db.Integer,unique=True)
    licenseNo = db.Column('driver_license_no', db.String(50), unique=True)
    licenseDoc = db.Column('driver_license_doc', db.Text)
    #licenseDoc_back = db.Column('driver_license_doc_2', db.Text)
    pic = db.Column('driver_pic', db.Text)
    approved = db.Column('driver_approved', db.Integer)
    available = db.Column('driver_available', db.Integer)
    rating = db.Column('driver_rating', db.Float)
    note = db.Column('driver_note', db.String(1000))
    perma = db.Column('driver_perma', db.Boolean)
    total_strike_count = db.Column('driver_strike_count', db.Integer)
    was_approved= db.Column('driver_was_approved', db.Integer)
    
    def get_driver_id(self):
        return str(self.id)

    def __init__(self, curUser, licNo, licDoc, pic, perma, approved=-1, licenseDoc_back=None,total_strike_count=0,was_approved=-1):
        self.user = curUser.id
        self.licenseNo = html.escape(licNo)
        self.licenseDoc = licDoc
        #self.licenseDoc_back = licenseDoc_back
        self.pic = pic
        self.approved = approved
        self.available = 1
        self.rating = 0
        self.note = ""
        self.perma = perma
        self.total_strike_count = total_strike_count
        self.was_approved=was_approved


class UserToken(db.Model):
    CUST_LOGIN = 1
    DRIVER_LOGIN = 2
    ADMIN_LOGIN = 3
    AFFILIATE_LOGIN = 4
    OTHER_LOGIN = 0  #unknown or old login 
    
    __tablename__ = 'user_token'
    __table_args__ = (PrimaryKeyConstraint('token_user_id', 'token_refresh'),)
    user_id = db.Column('token_user_id', db.Integer)
    login_from = db.Column('token_login_from', db.Integer) #0 for not known | 1 for customer | 2 for driver | 3 for admin
    refresh = db.Column('token_refresh', db.String(200))
    expiry = db.Column('token_expiry', db.DateTime)
    agent = db.Column('token_agent', db.String(150))
    timestamp = db.Column('token_timestamp', db.DateTime)

    def __init__(self, uid, refresh, agent, expiry, login_from = OTHER_LOGIN):
        self.user_id = uid
        self.refresh = hashlib.sha512(refresh.encode('utf-8')).hexdigest()
        self.agent = agent
        self.expiry = expiry
        self.login_from = login_from
        self.timestamp = datetime.utcnow()


class DriverSearch(db.Model):
    __tablename__ = "driver_search"
    id = db.Column('search_id', db.String(40), primary_key=True)
    user = db.Column('search_user_id', db.Integer)
    reflat = db.Column('search_reflat', db.Float)
    reflong = db.Column('search_reflong', db.Float)
    reloc = db.Column('search_reloc', db.String)
    destlat = db.Column('search_destlat', db.Float)
    destlong = db.Column('search_destlong', db.Float)
    destloc = db.Column('search_destloc', db.String)
    car_type = db.Column('search_cartype', db.Integer)
    gear_type = db.Column('search_gear_type', db.Integer)
    type = db.Column('search_type', db.Integer)
    time = db.Column('search_time', db.Time)
    date = db.Column('search_date', db.Date)
    dur = db.Column('search_dur', db.Time)
    days = db.Column('search_dur_days', db.Integer)
    dist = db.Column('search_dist', db.Integer)
    insurance_num = db.Column('search_insurance_num', db.Integer)
    region = db.Column('search_region', db.Integer)
    source = db.Column('search_source', db.String)
    coupon_id = db.Column('search_coupon_id', db.Integer)
    timestamp = db.Column('search_timestamp', db.DateTime)

    def __init__(self, id, user, car_type,gear_type, reflat, reflong, time, date, dur, timestamp, type=1, days=0, insurance_num=0, region=0, dist=0, source='unknown',coupon_id=None,reloc="N/A",destlat=None,destlong=None,destloc="N/A"):
        self.id = id
        self.user = int(user)
        self.car_type = int(car_type)
        self.gear_type = int(gear_type)
        self.reflat = reflat
        self.reflong = reflong
        self.time = time
        self.date = date
        self.dur = dur
        self.type = type
        self.days = days
        self.dist = dist
        self.insurance_num = insurance_num
        self.region = region
        self.timestamp = timestamp
        self.source = html.escape(source.lower())
        self.coupon_id = coupon_id
        self.reloc = reloc
        self.destlat = destlat
        self.destlong = destlong
        self.destloc = destloc
        

class DriverSearchHistory(db.Model):
    __tablename__ = "driver_search_history"
    id = db.Column('search_id', db.String(40), primary_key=True)
    user = db.Column('search_user_id', db.Integer)
    reflat = db.Column('search_reflat', db.Float)
    reflong = db.Column('search_reflong', db.Float)
    reloc = db.Column('search_reloc', db.String)
    destlat = db.Column('search_destlat', db.Float)
    destlong = db.Column('search_destlong', db.Float)
    destloc = db.Column('search_destloc', db.String)
    car_type = db.Column('search_cartype', db.Integer)
    gear_type = db.Column('search_gear_type', db.Integer)
    type = db.Column('search_type', db.Integer)
    time = db.Column('search_time', db.Time)
    date = db.Column('search_date', db.Date)
    dur = db.Column('search_dur', db.Time)
    days = db.Column('search_dur_days', db.Integer)
    dist = db.Column('search_dist', db.Integer)
    insurance_num = db.Column('search_insurance_num', db.Integer)
    region = db.Column('search_region', db.Integer)
    source = db.Column('search_source', db.String)
    coupon_id = db.Column('search_coupon_id', db.Integer)
    timestamp = db.Column('search_timestamp', db.DateTime)

    def __init__(self, id, user, car_type,gear_type, reflat, reflong, time, date, dur, timestamp, type=1, days=0, insurance_num=0, region=0, dist=0, source='unknown',coupon_id=None,reloc="N/A",destlat=None,destlong=None,destloc="N/A"):
        self.id = id
        self.user = int(user)
        self.car_type = int(car_type)
        self.gear_type = int(gear_type)
        self.reflat = reflat
        self.reflong = reflong
        self.time = time
        self.date = date
        self.dur = dur
        self.type = type
        self.days = days
        self.dist = dist
        self.insurance_num = insurance_num
        self.region = region
        self.timestamp = timestamp
        self.source = html.escape(source.lower())
        self.coupon_id = coupon_id
        self.reloc = reloc
        self.destlat = destlat
        self.destlong = destlong
        self.destloc = destloc
        
   
def quarter_from_ts(ts: datetime) -> int:
    # Maps month 1–3 → 1, 4–6 → 2, 7–9 → 3, 10–12 → 4
    return (ts.month - 1) // 3 + 1

def table_for_ts(ts: datetime) -> str:
    """Given a timestamp like 2025-05-20 14:30, returns 'driver_search_2025_q2'."""
    q = quarter_from_ts(ts)
    return f"driver_search_{ts.year}_q{q}"
     
        
def get_ds_partition_model(ts: datetime):
    name = table_for_ts(ts)

    # 1) if we already have a mapped class by that name, return it
    if name in _partition_models:
        return _partition_models[name]

    # 2) If the Table isn’t in metadata yet, clone it
    if name not in Base.metadata.tables:
        master = Base.metadata.tables["driver_search"]
        cols   = [c.copy() for c in master.columns]
        Table(name, Base.metadata, *cols, extend_existing=True)
        # Persist DDL if needed:
        base_uri = app.config['SQLALCHEMY_DATABASE_URI']
        engine = create_engine(base_uri)
        Base.metadata.create_all(engine, tables=[Base.metadata.tables[name]])

    # 3) Dynamically construct & cache the ORM class
    cls = type(
        name,
        (Base,),
        {"__table__": Base.metadata.tables[name]}
    )
    _partition_models[name] = cls
    return cls

class DriverDetails(db.Model):
    __tablename__ = 'driver_details'
    driver_id = db.Column('details_driver_id', db.Integer, primary_key=True)
    ride_count = db.Column('details_driver_rides', db.Integer) # total bookings of driver - b2b + b2c
    rating_count = db.Column('details_rating_rides', db.Integer)
    hour_count = db.Column('details_driver_hours', db.Integer)
    rating = db.Column('details_driver_rating', db.Integer)
    earning = db.Column('driver_details_earning', db.Float)
    owed = db.Column('driver_details_owed', db.Float)
    wallet = db.Column('driver_details_wallet', db.Integer)
    withdrawable = db.Column('driver_details_withdrawable', db.Integer)
    approval_ts = db.Column('driver_details_approval_ts', db.DateTime)
    reactivation_ts = db.Column('driver_details_reactivation_ts', db.DateTime)
    ride_activing_ts = db.Column('driver_details_ride_activing_ts', db.DateTime)
    timestamp = db.Column('driver_details_timestamp', db.DateTime)
    b2b_ride_count = db.Column('driver_b2b_ride_count', db.Integer, default=0) # no of b2b bookings

    def __init__(self, did, ride=0, hour=0, rating=0, earning=0, owed=0,
                 wallet=0, withdrawable=0, rating_ride=0):
        self.driver_id = did
        self.ride_count = ride
        self.rating_count = rating_ride
        self.hour_count = hour
        self.rating = rating
        self.earning = earning
        self.owed = owed
        self.wallet = wallet
        self.withdrawable = withdrawable
        self.approval_ts = None
        self.reactivation_ts = None
        self.ride_activing_ts = None
        self.timestamp = datetime.utcnow()
        self.b2b_ride_count = 0


class DriverPaid(db.Model):
    PAYMENT_OFFLINE = 0
    PAYMENT_ONLINE = 1
    TRIP_DUE = 2

    SOURCE_TRIP = 0
    SOURCE_ADMIN = 1

    __tablename__ = 'driver_paid'
    id = db.Column('driver_paid_id', db.Integer, primary_key=True)
    driver_id = db.Column('driver_paid_did', db.Integer)
    amount = db.Column('driver_paid_amt', db.Float)
    source = db.Column('driver_paid_source', db.Integer)
    source_type = db.Column('driver_paid_source_type', db.Boolean)
    due = db.Column('driver_paid_due', db.Float)
    timestamp = db.Column('driver_paid_timestamp', db.DateTime)

    def __init__(self, did, amt, source=0, due=0, source_type=1):
        self.driver_id = did
        self.amount = amt
        self.source = source
        self.source_type = source_type
        self.due = due
        self.timestamp = datetime.utcnow()


class DriverBaseLoc(db.Model):
    __tablename__ = 'driver_base_loc'
    driver_id = db.Column('driver_loc_id', db.Integer, primary_key=True)
    homelat = db.Column('driver_loc_lat', db.Float)
    homelong = db.Column('driver_loc_long', db.Float)
    homeaddr = db.Column('driver_loc_name', db.String)

    def __init__(self, did, lat, lng, add=None):
        self.driver_id = did
        self.homelat = lat
        self.homelong = lng
        self.homeaddr = html.escape(add)


class Bookings(db.Model):
    ALLOCATED = 1
    UNALLOCATED = 0
    CANCELLED_USER = -1
    CANCELLED_DRIVER = -2
    CANCELLED_D4M = -3
    
    DEFAULT_DRIVER = 1
    
    id = db.Column('book_ref', db.Integer, primary_key=True)
    search_key = db.Column('book_search_id', db.String, unique=True)
    # in case of affiliate, it will store affiliate id
    user = db.Column('book_user', db.Integer)
    driver = db.Column('book_driver', db.Integer)
    valid = db.Column('book_valid', db.Integer)
    lat = db.Column('book_lat', db.Float)
    long = db.Column('book_long', db.Float)
    starttime = db.Column('book_starttime', db.Time)
    startdate = db.Column('book_startdate', db.Date)
    region = db.Column('book_region', db.Integer)
    endtime = db.Column('book_endtime', db.Time)
    enddate = db.Column('book_enddate', db.Date)
    dur = db.Column('book_dur', db.Time)
    user_rating = db.Column('book_user_rating', db.Integer)
    driver_rating = db.Column('book_driver_rating', db.Integer)
    estimate = db.Column('book_estimate_price', db.Float)
    estimate_pre_tax = db.Column('book_estimate_pre_tax', db.Integer)
    price = db.Column('book_real_price', db.Float) # deprecated
    loc = db.Column('book_loc_name', db.String)
    type = db.Column('book_type', db.Integer)
    car_type = db.Column("book_car_type",db.Integer)
    timestamp = db.Column('book_timestamp', db.DateTime)
    created_at = db.Column('book_created_at', db.DateTime)
    days = db.Column('book_dur_days', db.Integer)
    comment = db.Column('book_comment', db.String(1000))
    payment_type = db.Column('book_payment_type', db.Integer)
    code = db.Column('book_code', db.String)
    insurance_cost = db.Column('book_insurance_cost', db.Integer)
    insurance_num = db.Column('book_insurance_num', db.Integer)
    cancelled_dt = db.Column('book_cancelled_timestamp', db.DateTime)
    otp = db.Column('book_otp', db.String)
    did = db.Column('book_did', db.String(30))
    did_release = db.Column('book_did_release', db.Boolean)
    immediate = db.Column('book_immediate', db.Boolean) # is this an immediate booking?

    def __init__(self, user, skey, driver, lat, long, starttime, startdate, dur, endtime, enddate, estimate, pre_tax, loc, car_type, type=1,
                        days=0, comment = "", payment_type=0, region=0, insurance_cost=0, insurance_num=0, user_rating=0, driver_rating=0, immediate=False):
        self.user = user
        self.search_key = skey
        self.driver = driver
        self.valid = 0
        self.lat = lat
        self.long = long
        self.starttime = starttime
        self.startdate = startdate
        self.endtime = endtime
        self.enddate = enddate
        self.dur = dur
        self.user_rating = user_rating
        self.driver_rating = driver_rating
        self.estimate = estimate
        self.estimate_pre_tax = pre_tax
        self.price = estimate
        if loc:
            self.loc = html.escape(loc)
        else:
            self.loc = 'N/A'
        self.car_type=car_type
        self.type = int(type)
        self.timestamp = datetime.utcnow()
        self.days = days
        self.comment = comment
        self.payment_type = payment_type  # this should be preprocessed by sender!
        self.region = region
        self.insurance_cost = insurance_cost
        self.insurance_num = insurance_num
        self.code = ""
        self.otp = gen_otp(6)
        self.created_at = datetime.utcnow()
        self.did = "-1"
        self.did_release = True
        if self.type < BookingParams.TYPE_C24:
            self.did_release = False
        self.immediate = immediate


class Trip(db.Model):
    # On the way
    TRIP_INIT = 6
    # Reached source loc (Checkin)
    TRIP_REACHED_SRC = 5
    # Uploaded start pic
    TRIP_START_PIC = 4
    # Clicked start
    TRIP_STARTED = 3
    # Reached dest loc
    TRIP_REACHED_DEST = 2
    # Uploaded stop pic
    TRIP_STOP_PIC = 1
    # Trip stopped
    TRIP_STOPPED = 0

    id = db.Column('trip_id', db.Integer, primary_key=True)
    book_id = db.Column('trip_book', db.String, unique=True)
    starttime = db.Column('trip_start', db.DateTime)
    endtime = db.Column('trip_stop', db.DateTime)
    price = db.Column('trip_price', db.Float)
    price_pre_tax = db.Column('trip_price_pre_tax', db.Float)
    due = db.Column('trip_due', db.Float)
    trans = db.Column('trip_trans', db.String)
    aff_trans = db.Column('trip_aff_trans', db.String)
    driver_trans = db.Column('trip_driver_trans', db.String)
    comment = db.Column('trip_comment', db.String)
    status = db.Column('trip_status', db.Integer)
    start_lat = db.Column('trip_start_lat', db.Float)
    start_lng = db.Column('trip_start_lng', db.Float)
    stop_lat = db.Column('trip_stop_lat', db.Float)
    stop_lng = db.Column('trip_stop_lng', db.Float)
    cgst = db.Column('trip_cgst', db.Integer)
    sgst = db.Column('trip_sgst', db.Integer)
    otp = db.Column('trip_otp', db.String)
    net_rev = db.Column('trip_net_revenue', db.Integer)
    timestamp = db.Column('trip_timestamp', db.DateTime)

    def __init__(self, book, starttime=None, lat=-1, lng=-1, status=6):
        self.book_id = book
        self.starttime = starttime
        self.price = 0
        self.endtime = None
        self.due = 0
        self.timestamp = datetime.utcnow()
        self.comment = ""
        self.start_lat = lat
        self.start_lng = lng
        self.stop_lat = -1
        self.stop_lng = -1
        self.status = int(status)
        self.cgst = 0
        self.sgst = 0
        self.price_pre_tax = 0
        self.net_rev = 0
        self.otp = gen_otp(6)
     


class AdminUserLog(db.Model):
    USER_CREATED = 0
    USER_UPDATED = 1

    __tablename__ = 'admin_user_log'
    id = db.Column('admin_user_log_id', db.Integer, primary_key=True)
    admin = db.Column('admin_id', db.Integer)
    user = db.Column('user_id', db.Integer) # Make unique
    action = db.Column('admin_action', db.Integer)
    timestamp = db.Column('admin_timestamp', db.DateTime)

    def __init__(self, admin, user, action):
        self.admin = admin
        self.user = user
        self.action = action
        self.timestamp = datetime.utcnow()

class AdminUserSearch(db.Model):
    DRIVER_SEARCHED_BY_ADMIN_FOR_USER =  0

    __tablename__ = 'admin_user_search'
    id = db.Column('admin_user_search_id', db.Integer, primary_key=True)
    admin = db.Column('admin_id', db.Integer)
    user = db.Column('user_id', db.Integer)
    action = db.Column('admin_action', db.Integer)
    timestamp = db.Column('admin_timestamp', db.DateTime)

    def __init__(self, admin, user, action):
        self.admin = admin
        self.user = user
        self.action = action
        self.timestamp = datetime.utcnow()        

class AdminUserBooking(db.Model):

    USER_BOOKED =  0

    __tablename__ = 'admin_user_booking'
    id = db.Column('admin_user_booking_id', db.Integer, primary_key=True)
    admin = db.Column('admin_id', db.Integer)
    user = db.Column('user_id', db.Integer)
    action = db.Column('admin_action', db.Integer)
    timestamp = db.Column('admin_timestamp', db.DateTime)

    def __init__(self, admin,user, action):
        self.admin = admin
        self.user = user
        self.action = action
        self.timestamp = datetime.utcnow()        



class SearchHold(db.Model):
    __tablename__ = 'search_hold'
    __table_args__ = (PrimaryKeyConstraint('hold_search_id', 'hold_driver_id'),)
    search_id = db.Column('hold_search_id', db.String(40))
    driver_id = db.Column('hold_driver_id', db.Integer)
    price = db.Column('hold_price', db.Float)
    active = db.Column('hold_active', db.Integer)
    starttime = db.Column('hold_starttime', db.Time)
    startdate = db.Column('hold_startdate', db.Date)
    endtime = db.Column('hold_endtime', db.Time)
    enddate = db.Column('hold_enddate', db.Date)
    timestamp = db.Column('hold_timestamp', db.DateTime)

    def __init__(self, sid, did, active, price, starttime, startdate, endtime, enddate):
        self.search_id = sid
        self.driver_id = did
        self.active = active
        self.price = price
        self.starttime = starttime
        self.startdate = startdate
        self.endtime = endtime
        self.enddate = enddate
        self.timestamp = datetime.utcnow()


class SearchDecline(db.Model):
    __tablename__ = 'search_decline'
    __table_args__ = (PrimaryKeyConstraint('decline_search_id', 'decline_driver_id'),)
    search_id = db.Column('decline_search_id', db.String(40))
    driver_id = db.Column('decline_driver_id', db.Integer)

    def __init__(self, sid, did):
        self.search_id = sid
        self.driver_id = did


class BookDest(db.Model):
    __tablename__ = 'book_dest'
    book_id = db.Column('dest_book_id', db.Integer, primary_key=True)
    lat = db.Column('dest_book_lat', db.Integer)
    lng = db.Column('dest_book_long', db.Float)
    name =  db.Column('dest_book_name', db.String(100))

    def __init__(self, book_id, lat, lng, name):
        self.book_id = book_id
        self.lat =lat
        self.lng = lng
        self.name = html.escape(name)


class SearchDest(db.Model):
    __tablename__ = 'search_dest'
    search_id = db.Column('dest_search_id', db.String, primary_key=True)
    lat = db.Column('dest_search_lat', db.Integer)
    lng = db.Column('dest_search_long', db.Float)
    name =  db.Column('dest_search_name', db.String(100))

    def __init__(self, search_id, lat, lng, name):
        self.search_id = search_id
        self.lat =lat
        self.lng = lng
        self.name = html.escape(name)


class BookPending(db.Model):
    __tablename__ = 'book_pending_v2'
    __table_args__ = (PrimaryKeyConstraint('pending_book_id', 'pending_driver_id'),)

    ALLOCATED = 0
    BROADCAST = 1
    SUPPRESSED = 2

    BROADCAST_DRIVER_ID = 1
    
    book_id = db.Column('pending_book_id', db.Integer)
    driver = db.Column('pending_driver_id', db.Integer)
    valid = db.Column('pending_valid', db.Integer)
    phase = db.Column('pending_phase', db.Integer)
    score = db.Column('pending_score', db.Float)

    def __init__(self, bid, did, state=0, phase=0, score=-99):
        self.book_id = bid
        self.driver = did
        self.valid = state
        self.phase = int(phase)
        self.score = float(score)



class AdminLog(db.Model):
    __tablename__ = 'admin_log'
    id = db.Column('admin_log_id', db.Integer, primary_key=True)
    user = db.Column('admin_user_id', db.Integer)
    action = db.Column('admin_action', db.String(40))
    content = db.Column('admin_content', db.String(100))
    timestamp = db.Column('admin_timestamp', db.DateTime)

    def __init__(self, user, action, content):
        self.user = user
        self.action = action
        self.content = content
        self.timestamp = datetime.utcnow()


class DriverLog(db.Model):
    __tablename__ = 'driver_log'
    id = db.Column('driver_log_id', db.Integer, primary_key=True)
    driver = db.Column('driver_did', db.Integer)
    action = db.Column('driver_action', db.String(40))
    content = db.Column('driver_content', db.String(100))
    timestamp = db.Column('driver_timestamp', db.DateTime)

    def __init__(self, driver, action, content):
        self.driver = driver
        self.action = action
        self.content = content
        self.timestamp = datetime.utcnow()

class DriverCancelled(db.Model):
    __tablename__ = 'driver_cancelled'
    id = db.Column('cancelled_id', db.Integer, primary_key=True)
    driver = db.Column('cancelled_driver_id', db.Integer)
    booking = db.Column('cancelled_book_id', db.Integer)
    penalty = db.Column('cancelled_penalty', db.Integer)
    timestamp = db.Column('cancelled_timestamp', db.DateTime)

    def __init__(self, driver, booking, penalty):
        self.driver = driver
        self.booking = booking
        self.penalty = penalty
        self.timestamp = datetime.utcnow()


class UserCancelled(db.Model):
    __tablename__ = 'user_cancelled'
    id = db.Column('u_cancelled_id', db.Integer, primary_key=True)
    user = db.Column('u_cancelled_uid', db.Integer)
    booking = db.Column('u_cancelled_book_id', db.Integer)
    penalty = db.Column('u_cancelled_penalty', db.Integer)
    timestamp = db.Column('u_cancelled_timestamp', db.DateTime)

    def __init__(self, user, booking, penalty):
        self.user = user
        self.booking = booking
        self.penalty = penalty
        self.timestamp = datetime.utcnow()

class BookingCancelled(db.Model):
    # Source is bitvector (multiple can be true)
    SRC_USER = 0
    SRC_DRIVER = 1
    SRC_ADMIN = 2

    # Reasons are ints, random string in reason_detail
    RSN_PLANS_CHANGED = 0
    RSN_DRIVER_DENIED = 1
    RSN_FAVORITE_DRIVER_NOT_ASSIGNED = 2
    RSN_DRIVER_REQUESTING_EXTRA_FARE = 3
    RSN_DRIVER_ASKING_TO_TAKE_OFFLINE = 4
    RSN_SELECTED_WRONG_LOCATION = 5
    RSN_SELECTED_DIFFERENT_SERVICE = 6
    RSN_BOOKED_BY_MISTAKE = 7
    RSN_WAIT_TIME_TOO_LONG = 8
    RSN_GOT_DRIVER_ELSEWHERE = 9
    RSN_CHECKING_PRICE_ESTIMATE = 10
    RSN_TAKING_TOO_LONG_TO_ALLOCATE = 11
    RSN_DIRECT_TRIP = 12
    RSN_WRONGLY_TAKEN = 13
    RSN_PREVIOUS_TRIP_NOT_ENDED = 14
    RSN_PERSONAL_ISSUE = 15
    RSN_TRANSPORTATION_PROBLEM = 16
    RSN_CUSTOMER_NOT_RESPONDING = 17
    RSN_CUSTOMER_ASKED_TO_CANCEL = 18
    RSN_FAVORITE_DRIVER_NOT_ASSIGNED_ADMIN = 19
    RSN_REVERSE_CANCELLATION=20

    CONST_FOR_B2B_REASON_CONVERSION=20 #USED to generate 21,22,23,.. b2b cancel reasons from serial 1,2,3,4,5... affiliate use

    RSN_B2B_DRIVER_NOT_ASSIGNED=21 #none
    RSN_B2B_DRIVER_DENIED=22 #flat driver
    RSN_B2B_WAIT_TIME_TOO_LONG=23 #var
    RSN_B2B_RESCHEDULED=24 #variable #aff #waiver
    RSN_B2B_BOOKING_CANCELLED=25  #variable #aff
    RSN_B2B_D4M_CANCEL=26 #neither #fixed
    RSN_B2B_CAR_NOT_READY = 27 #variable #aff
    RSN_B2B_CUSTOMER_NOT_RESPONDING = 28 #variable #aff
    RSN_B2B_CAR_NOT_AVAILABLE_AT_LOCATION = 29 #variable #aff
    RSN_B2B_WRONGLY_ALLOCATED = 30 #neither #fixed
    
    RSN_B2B_OTHER=61
    RSN_OTHER = 62
    RSN_NO_ALLOC = 63

    # Cancel Types
    TYPE_CANCELLATION = 0
    TYPE_UNALLOCATION = 1

    # To charge
    CUSTOMER = [RSN_PLANS_CHANGED, RSN_FAVORITE_DRIVER_NOT_ASSIGNED,
                RSN_SELECTED_WRONG_LOCATION,
                RSN_SELECTED_DIFFERENT_SERVICE, RSN_BOOKED_BY_MISTAKE,
                RSN_GOT_DRIVER_ELSEWHERE, RSN_CHECKING_PRICE_ESTIMATE, RSN_CUSTOMER_NOT_RESPONDING, RSN_CUSTOMER_ASKED_TO_CANCEL, RSN_OTHER,RSN_B2B_CAR_NOT_AVAILABLE_AT_LOCATION,RSN_B2B_CUSTOMER_NOT_RESPONDING,RSN_B2B_CAR_NOT_READY, RSN_B2B_BOOKING_CANCELLED]
    DRIVER = [RSN_DRIVER_DENIED, RSN_DRIVER_ASKING_TO_TAKE_OFFLINE, RSN_DRIVER_REQUESTING_EXTRA_FARE, RSN_WRONGLY_TAKEN, RSN_PREVIOUS_TRIP_NOT_ENDED, RSN_PERSONAL_ISSUE, RSN_TRANSPORTATION_PROBLEM, RSN_WAIT_TIME_TOO_LONG,RSN_B2B_DRIVER_DENIED,RSN_B2B_WAIT_TIME_TOO_LONG]
    NEITHER = [RSN_TAKING_TOO_LONG_TO_ALLOCATE, RSN_NO_ALLOC, RSN_FAVORITE_DRIVER_NOT_ASSIGNED_ADMIN, RSN_B2B_DRIVER_NOT_ASSIGNED,RSN_B2B_D4M_CANCEL,RSN_B2B_OTHER,RSN_B2B_WRONGLY_ALLOCATED]
    BOTH = [RSN_DIRECT_TRIP]
    WAIVER = [RSN_FAVORITE_DRIVER_NOT_ASSIGNED, RSN_SELECTED_WRONG_LOCATION, RSN_SELECTED_DIFFERENT_SERVICE, RSN_BOOKED_BY_MISTAKE, RSN_WAIT_TIME_TOO_LONG, RSN_TAKING_TOO_LONG_TO_ALLOCATE,RSN_B2B_RESCHEDULED]

    # Fixed or variable
    FIXED = [RSN_DRIVER_DENIED, RSN_DRIVER_REQUESTING_EXTRA_FARE, RSN_DRIVER_ASKING_TO_TAKE_OFFLINE, RSN_GOT_DRIVER_ELSEWHERE, RSN_DIRECT_TRIP,RSN_B2B_DRIVER_NOT_ASSIGNED,RSN_B2B_DRIVER_DENIED,RSN_B2B_D4M_CANCEL,RSN_B2B_OTHER,RSN_B2B_WRONGLY_ALLOCATED]
    VARIABLE = [RSN_PLANS_CHANGED, RSN_FAVORITE_DRIVER_NOT_ASSIGNED, RSN_SELECTED_WRONG_LOCATION, RSN_SELECTED_DIFFERENT_SERVICE, RSN_BOOKED_BY_MISTAKE, RSN_WRONGLY_TAKEN,
    RSN_WAIT_TIME_TOO_LONG, RSN_CHECKING_PRICE_ESTIMATE, RSN_TAKING_TOO_LONG_TO_ALLOCATE, RSN_PERSONAL_ISSUE, RSN_TRANSPORTATION_PROBLEM, RSN_CUSTOMER_NOT_RESPONDING,
    RSN_PREVIOUS_TRIP_NOT_ENDED, RSN_CUSTOMER_ASKED_TO_CANCEL, RSN_OTHER, RSN_NO_ALLOC, RSN_FAVORITE_DRIVER_NOT_ASSIGNED_ADMIN,RSN_B2B_WAIT_TIME_TOO_LONG,RSN_B2B_CAR_NOT_READY,RSN_B2B_CUSTOMER_NOT_RESPONDING,RSN_B2B_CAR_NOT_AVAILABLE_AT_LOCATION, RSN_B2B_RESCHEDULED, RSN_B2B_BOOKING_CANCELLED]


    #b2b
    BOOKING_CANCELLATION_REASON_MAPPING = {
            RSN_B2B_DRIVER_NOT_ASSIGNED: 'DRIVER_NOT_ASSIGNED', #21
            RSN_B2B_DRIVER_DENIED: 'DRIVER_DENIED', #22
            RSN_B2B_WAIT_TIME_TOO_LONG: 'WAIT_TIME_TOO_LONG', #23
            RSN_B2B_RESCHEDULED: 'RESCHEDULED_TRIP', #24
            RSN_B2B_BOOKING_CANCELLED: 'BOOKING_CANCELLED_BY_AFFILIATE', #25
            RSN_B2B_D4M_CANCEL: 'BOOKING_CANCELLED_BY_D4M', #26
            RSN_B2B_WRONGLY_ALLOCATED: 'WRONGLY_ALLOCATED', #30
            RSN_B2B_OTHER: 'OTHER_REASON', #61
        }

    FORGIVE_DELTA = timedelta(seconds=300)

    __tablename__ = 'booking_cancelled'
    id = db.Column('b_cancelled_id', db.Integer, primary_key=True)
    user = db.Column('b_cancelled_uid', db.Integer)
    cancel_source = db.Column('b_cancelled_source', db.Integer)
    booking = db.Column('b_cancelled_book_id', db.Integer)
    reason = db.Column('b_cancelled_reason', db.Integer)
    reason_detail = db.Column('b_cancelled_reason_detail', db.String)
    uid = db.Column('b_cancelled_book_uid', db.Integer)
    utransid=db.Column('b_cancelled_utrans_id',db.String)
    atransid = db.Column('b_cancelled_atrans_id', db.String)
    did = db.Column('b_cancelled_book_did', db.Integer)
    dtransid=db.Column('b_cancelled_dtrans_id',db.String)
    penalty_user = db.Column('b_cancelled_penalty_user', db.Integer)
    penalty_driver = db.Column('b_cancelled_penalty_driver', db.Integer)
    cancel_reversed = db.Column('b_cancel_reversed',db.Boolean)
    cancel_type = db.Column('b_cancelled_type',db.Integer)
    timestamp = db.Column('b_cancelled_timestamp', db.DateTime)

    def reason_type(rsn):
        to_charge = -1
        if rsn in BookingCancelled.CUSTOMER:
            to_charge = 0
        elif rsn in BookingCancelled.DRIVER:
            to_charge = 1
        elif rsn in BookingCancelled.BOTH:
            to_charge = 2
        elif rsn in BookingCancelled.NEITHER:
            to_charge = 3
        if rsn in BookingCancelled.FIXED:
            fixed = 1
        else:
            fixed = 0
        return to_charge, fixed

    def __init__(self, user, cancel_source, booking, uid, did,  penalty_user, penalty_driver, rsn=63, 
            reason_detail="", atransid=None, utransid=None,dtransid=None,cancel_reversed=False,cancel_type=0):
        self.user = user
        self.booking = booking
        self.uid = uid
        self.did = int(did)
        self.cancel_source = int(cancel_source)
        self.penalty_user = int(penalty_user)
        self.penalty_driver = int(penalty_driver)
        self.reason = int(rsn)
        self.reason_detail = reason_detail
        self.atransid = atransid
        self.utransid=utransid
        self.dtransid=dtransid
        self.cancel_reversed=cancel_reversed
        self.cancel_type=cancel_type
        self.timestamp = datetime.utcnow()

class UserTrans(db.Model):
    INITIATED = 0
    COMPLETED = 1
    FAILED = 2
    INVALID = 3
    INVALID_COMPLETED = 4
    
    excluded_user_ids = [1]

    __tablename__ = 'user_trans'
    id = db.Column('user_trans_id', db.String, primary_key=True)
    user_id = db.Column('user_trans_uid', db.Integer)
    amount = db.Column('user_trans_amt', db.Float)
    cash = db.Column('user_trans_cash', db.Integer)
    method = db.Column('user_trans_method', db.String(60))
    status = db.Column('user_trans_status', db.Integer)
    start_timestamp = db.Column('user_trans_starttime', db.DateTime)
    stop_timestamp = db.Column('user_trans_stoptime', db.DateTime)
    timestamp = db.Column('user_trans_timestamp', db.DateTime)
    remark = db.Column('user_trans_remark', db.String(255))
    admin_name = db.Column('user_trans_name', db.String(255), nullable=True)
    admin_id = db.Column('user_trans_admin_id', db.Integer, nullable=True)
    trans_payment_id = db.Column('user_trans_tpid', db.String(60), nullable =True)
    description = db.Column('user_trans_description', db.String(200),nullable=True)
    credit_type_reason = db.Column('user_trans_credit_reason', db.Text, nullable=True)
    wallet_before = db.Column('user_trans_wallet_before', db.Integer)
    wallet_after = db.Column('user_trans_wallet_after', db.Integer)

    def __init__(self, uid, amt, method="N/A", status=0, cash=0, stop=False, admin_name=None, admin_id=None, trans_payment_id="N/A", description="", remark='', credit_type_reason="", wall_a=None, wall_b=None):
        self.id = uuid.uuid4().hex
        self.user_id = uid
        self.amount = amt
        self.remark = remark
        self.method = method
        self.status = status
        self.timestamp = datetime.utcnow()
        self.cash = cash
        self.start_timestamp = datetime.utcnow()
        self.admin_name = admin_name
        self.admin_id = admin_id
        self.trans_payment_id = trans_payment_id
        self.credit_type_reason = credit_type_reason
        if stop:
            self.stop_timestamp = datetime.utcnow()
        self.description=description  
        self.wallet_after=wall_a
        self.wallet_before=wall_b  

class DriverTrans(db.Model):
    INITIATED = 0
    COMPLETED = 1
    FAILED = 2
    INVALID = 3
    INVALID_COMPLETED = 4
    
    excluded_driver_ids = [1, 2]

    __tablename__ = 'driver_trans'
    id = db.Column('driver_trans_id', db.String, primary_key=True)
    driver_id = db.Column('driver_trans_uid', db.Integer)
    amount = db.Column('driver_trans_amt', db.Float)
    cash = db.Column('driver_trans_cash', db.Integer)
    method = db.Column('driver_trans_method', db.String(60))
    remarks = db.Column('driver_trans_remarks', db.String(200))
    status = db.Column('driver_trans_status', db.Integer)
    start_timestamp = db.Column('driver_trans_starttime', db.DateTime)
    stop_timestamp = db.Column('driver_trans_stoptime', db.DateTime)
    wallet_before = db.Column('driver_trans_wallet_before', db.Integer)
    wallet_after = db.Column('driver_trans_wallet_after', db.Integer)
    withdrawable_before = db.Column('driver_trans_withdraw_before', db.Integer)
    withdrawable_after = db.Column('driver_trans_withdraw_after', db.Integer)
    timestamp = db.Column('driver_trans_timestamp', db.DateTime)
    admin_user_id = db.Column('driver_trans_admin_user_id', db.Integer, nullable=True)
    admin_name = db.Column('driver_trans_name', db.String(255), nullable=True)
    trans_payment_id = db.Column('driver_trans_tpid', db.String(60),nullable=True)
    description = db.Column('driver_trans_description', db.String(200),nullable=True)
    due_type_reason = db.Column('driver_trans_due_reason', db.Text, nullable=True)

    def __init__(self, did, amt, wall_a=0, wall_b=0, with_a=0, with_b=0,
                 method="N/A", status=0, cash=0, trans_payment_id="N/A", stop=False,admin_user_id=None, admin_name="", remarks="", description="", due_type_reason=""):
        self.id = uuid.uuid4().hex
        self.driver_id = did
        self.amount = amt
        self.method = html.escape(method)
        self.status = status
        self.timestamp = datetime.utcnow()
        self.cash = cash
        self.wallet_after = wall_a
        self.wallet_before = wall_b
        self.withdrawable_after = with_a
        self.withdrawable_before = with_b
        self.start_timestamp = datetime.utcnow()
        self.admin_user_id=admin_user_id
        self.admin_name=admin_name
        self.trans_payment_id = trans_payment_id
        if stop:
            self.stop_timestamp = datetime.utcnow()
        self.remarks = html.escape(remarks)
        self.description = description
        self.due_type_reason = due_type_reason

class PaymentDataPT(db.Model):
    __tablename__ = 'payment_data_pt'
    trans_id = db.Column('pt_trans_id', db.String)
    order_id = db.Column('pt_order_id', db.String, primary_key=True)
    payment_id = db.Column('pt_payment_id', db.String)
    description = db.Column('pt_description', db.String)
    method = db.Column('pt_method', db.String)
    status = db.Column('pt_status', db.String)
    amount = db.Column('pt_amount', db.Integer)
    timestamp = db.Column('pt_timestamp', db.DateTime)

    def __init__(self, tid, oid, pid=None, status="", desc=None, method=None, amount=0, timestamp=None):
        self.trans_id = tid
        self.order_id = oid
        if pid:
            self.payment_id = pid
            self.description = html.escape(desc)
            self.method = html.escape(method)
            self.amount = amount
            self.status = html.escape(status)
            self.timestamp = datetime.fromtimestamp(timestamp)
        else:
            self.pid = self.desc = self.method = self.timestamp = None
            self.status = "Initiated"
            self.amount = 0


class PaymentDataRP(db.Model):
    __tablename__ = 'payment_data_rp'
    trans_id = db.Column('rp_trans_id', db.String)
    order_id = db.Column('rp_order_id', db.String, primary_key=True)
    payment_id = db.Column('rp_payment_id', db.String)
    description = db.Column('rp_description', db.String)
    method = db.Column('rp_method', db.String)
    status = db.Column('rp_status', db.String)
    amount = db.Column('rp_amount', db.Integer)
    timestamp = db.Column('rp_timestamp', db.DateTime)
    credit_start_ts = db.Column('rp_credit_start_ts', db.DateTime)

    def __init__(self, tid, oid, pid=None, status="", desc=None, method=None, amount=0, timestamp=None):
        self.trans_id = tid
        self.order_id = oid
        self.credit_start_ts = datetime.utcnow()
        if pid:
            self.payment_id = pid
            self.description = html.escape(desc)
            self.method = html.escape(method)
            self.amount = amount
            self.status = html.escape(status)
            self.timestamp = datetime.fromtimestamp(timestamp)
        else:
            self.pid = self.desc = self.method = self.timestamp = None
            self.status = "Initiated"
            self.amount = 0

class DriverRegion(db.Model):
    __tablename__ = 'driver_region'
    __table_args__ = (PrimaryKeyConstraint('driver_region_did', 'driver_region_no'),)
    driver = db.Column('driver_region_did', db.Integer)
    region = db.Column('driver_region_no', db.Integer)

    def __init__(self, driver, region):
        self.driver = driver
        self.region = region


class C24Rep(db.Model):
    __tablename__ = 'c24_rep'
    id = db.Column('ctf_rep_id', db.Integer, primary_key=True)
    name = db.Column('ctf_name', db.String)
    owner_id = db.Column('ctf_rep_cr', db.Integer)
    mobile = db.Column('ctf_rep_mob', db.Integer)
    region = db.Column('ctf_region', db.Integer)
    timestamp = db.Column('ctf_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = user
        self.mobile = mob
        self.region = regn
        self.timestamp = datetime.utcnow()


class C24Bookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1

    __tablename__ = 'c24_bookings'
    id = db.Column('ctf_book_id', db.Integer, primary_key=True)
    ref = db.Column('ctf_book_ref', db.Integer)
    appt = db.Column('ctf_appt_id', db.String)
    veh_no = db.Column('ctf_veh_reg', db.String)
    veh_model = db.Column('ctf_veh_mdl', db.String)
    trip_type = db.Column('ctf_trip_type', db.Integer)
    drop_mob = db.Column('ctf_drop_no', db.Integer)
    rep = db.Column('ctf_pc_rep', db.Integer)
    region = db.Column('ctf_b_region', db.Integer)
    comment = db.Column('ctf_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == C24Bookings.TRIP_PICKUP:
            self.trip_type = C24Bookings.TRIP_PICKUP
        elif type == C24Bookings.TRIP_DELIVERY:
            self.trip_type = C24Bookings.TRIP_DELIVERY
        else:
            self.trip_type = C24Bookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""


class OLXRep(db.Model):
    __tablename__ = 'olx_rep'
    id = db.Column('olx_rep_id', db.Integer, primary_key=True)
    name = db.Column('olx_name', db.String)
    owner_id = db.Column('olx_rep_cr', db.Integer)
    mobile = db.Column('olx_rep_mob', db.Integer)
    region = db.Column('olx_region', db.Integer)
    timestamp = db.Column('olx_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = user
        self.mobile = mob
        self.region = regn
        self.timestamp = datetime.utcnow()


class OLXBookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1

    __tablename__ = 'olx_bookings'
    id = db.Column('olx_book_id', db.Integer, primary_key=True)
    ref = db.Column('olx_book_ref', db.Integer)
    appt = db.Column('olx_appt_id', db.String)
    veh_no = db.Column('olx_veh_reg', db.String)
    veh_model = db.Column('olx_veh_mdl', db.String)
    trip_type = db.Column('olx_trip_type', db.Integer)
    drop_mob = db.Column('olx_drop_no', db.Integer)
    dist = db.Column('olx_dist', db.Integer)
    rep = db.Column('olx_pc_rep', db.Integer)
    region = db.Column('olx_b_region', db.Integer)
    comment = db.Column('olx_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=dist):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == OLXBookings.TRIP_PICKUP:
            self.trip_type = OLXBookings.TRIP_PICKUP
        elif type == OLXBookings.TRIP_DELIVERY:
            self.trip_type = OLXBookings.TRIP_DELIVERY
        else:
            self.trip_type = OLXBookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = int(dist)


class UsernameMap(db.Model):
    __tablename__ = 'username_map'
    uid = db.Column('um_uid', db.Integer, primary_key=True)
    uname = db.Column('um_uname', db.String)

    def __init__(self, id, username):
        self.uid = id
        self.uname = html.escape(username)



class DriverPermaInfo(db.Model):
    ALLOC_CUST = 0
    ALLOC_C24 = 1
    ALLOC_REVV = 2
    ALLOC_ZOOMCAR = 3
    ALLOC_GUJRAL = 4
    ALLOC_OLX = 5
    ALLOC_CARDEKHO = 6
    ALLOC_BHANDARI = 7
    ALLOC_MAHINDRA = 8
    ALLOC_REVV_V2 = 9
    ALLOC_SPINNY = 10
    ALLOC_PRIDEHONDA = 11
    # add for more b2b partners

    __tablename__ = 'driver_perma_info'
    driver_id = db.Column('perm_driver_id', db.Integer, primary_key=True)
    alloc = db.Column('perm_driver_alloc', db.Integer)
    base = db.Column('perm_driver_base', db.Integer)
    ta = db.Column('perm_driver_ta', db.Integer)
    ot = db.Column('perm_driver_ot', db.Integer)
    hours = db.Column('perm_driver_hours',db.Integer)

    def __init__(self, did, alloc, base, ta, ot, hrs=10):
        self.driver_id = did
        self.alloc = alloc
        self.base = base
        self.ta = ta
        self.ot = ot
        self.hours = hrs


class C24Pic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'c24_pic'
    pic_id = db.Column('ctf_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('ctf_pic_book', db.Integer)
    idx = db.Column('ctf_pic_index', db.Integer)
    pic = db.Column('ctf_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class C24Util(db.Model):
    __tablename__ = 'c24_util'
    util_id = db.Column('ctf_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('ctf_util_book', db.Integer)
    idx = db.Column('ctf_util_index', db.Integer)
    pic = db.Column('ctf_util_pic', db.String)
    amt = db.Column('ctf_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)


class OLXPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'olx_pic'
    pic_id = db.Column('olx_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('olx_pic_book', db.Integer)
    idx = db.Column('olx_pic_index', db.Integer)
    pic = db.Column('olx_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class OLXUtil(db.Model):
    __tablename__ = 'olx_util'
    util_id = db.Column('olx_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('olx_util_book', db.Integer)
    idx = db.Column('olx_util_index', db.Integer)
    pic = db.Column('olx_util_pic', db.String)
    amt = db.Column('olx_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)


class RevvBookings(db.Model):
    DAY_SHIFT = 0
    NIGHT_SHIFT = 1
    __tablename__ = 'revv_bookings'
    id = db.Column('revv_booking_id', db.Integer, primary_key=True)
    ref = db.Column('revv_book_ref', db.Integer)
    region = db.Column('revv_booking_region', db.Integer)
    shift = db.Column('revv_booking_shift', db.Integer)

    def __init__(self, booking, region, shift):
        self.ref = booking
        self.region = region
        self.shift = shift


class GujralBookings(db.Model):
    DAY_SHIFT = 0
    NIGHT_SHIFT = 1
    __tablename__ = 'gujral_bookings'
    id = db.Column('gujral_booking_id', db.Integer, primary_key=True)
    ref = db.Column('gujral_book_ref', db.Integer)
    region = db.Column('gujral_booking_region', db.Integer)
    shift = db.Column('gujral_booking_shift', db.Integer)

    def __init__(self, booking, region, shift):
        self.ref = booking
        self.region = region
        self.shift = shift


class ZoomcarRep(db.Model):
    __tablename__ = 'zoomcar_rep'
    id = db.Column('zc_rep_id', db.Integer, primary_key=True)
    name = db.Column('zc_name', db.String)
    owner_id = db.Column('zc_rep_cr', db.Integer)
    mobile = db.Column('zc_rep_mob', db.Integer)
    region = db.Column('zc_region', db.Integer)
    timestamp = db.Column('zc_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = user
        self.mobile = mob
        self.region = regn
        self.timestamp = datetime.utcnow()


class ZoomcarPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'zoomcar_pic'
    pic_id = db.Column('zc_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('zc_pic_book', db.Integer)
    idx = db.Column('zc_pic_index', db.Integer)
    pic = db.Column('zc_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class ZoomcarBookings(db.Model):
    STATUS_DEFAULT = -1
    STATUS_CREATED = 0
    STATUS_ACCEPTED = 1
    STATUS_REJECTED = 2

    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2
    TRIP_RECOVERY = 3

    __tablename__ = 'zoomcar_bookings'
    id = db.Column('zc_book_id', db.Integer, primary_key=True)
    ref = db.Column('zc_book_ref', db.Integer)
    appt = db.Column('zc_appt_id', db.String)
    veh_no = db.Column('zc_veh_reg', db.String)
    veh_model = db.Column('zc_veh_mdl', db.String)
    trip_type = db.Column('zc_trip_type', db.Integer)
    drop_mob = db.Column('zc_drop_no', db.Integer)
    rep = db.Column('zc_pc_rep', db.Integer)
    region = db.Column('zc_b_region', db.Integer)
    dist = db.Column('zc_dist', db.Integer)
    comment = db.Column('zc_comment', db.String)
    status = db.Column('zc_state', db.Integer)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=0, status=-1, comment=""):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == ZoomcarBookings.TRIP_PICKUP:
            self.trip_type = ZoomcarBookings.TRIP_PICKUP
        elif type == ZoomcarBookings.TRIP_DELIVERY:
            self.trip_type = ZoomcarBookings.TRIP_DELIVERY
        else:
            self.trip_type = ZoomcarBookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = html.escape(comment)
        self.dist = dist
        self.status = int(status)


class ZoomcarUtil(db.Model):
    __tablename__ = 'zoomcar_util'
    util_id = db.Column('zc_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('zc_util_book', db.Integer)
    idx = db.Column('zc_util_index', db.Integer)
    pic = db.Column('zc_util_pic', db.String)
    amt = db.Column('zc_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)


class CardekhoRep(db.Model):
    __tablename__ = 'cardekho_rep'
    id = db.Column('cd_rep_id', db.Integer, primary_key=True)
    name = db.Column('cd_name', db.String)
    owner_id = db.Column('cd_rep_cr', db.Integer)
    mobile = db.Column('cd_rep_mob', db.Integer)
    region = db.Column('cd_region', db.Integer)
    timestamp = db.Column('cd_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = user
        self.mobile = mob
        self.region = regn
        self.timestamp = datetime.utcnow()


class CardekhoPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'cardekho_pic'
    pic_id = db.Column('cd_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('cd_pic_book', db.Integer)
    idx = db.Column('cd_pic_index', db.Integer)
    pic = db.Column('cd_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class CardekhoBookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2

    __tablename__ = 'cardekho_bookings'
    id = db.Column('cd_book_id', db.Integer, primary_key=True)
    ref = db.Column('cd_book_ref', db.Integer)
    appt = db.Column('cd_appt_id', db.String)
    veh_no = db.Column('cd_veh_reg', db.String)
    veh_model = db.Column('cd_veh_mdl', db.String)
    trip_type = db.Column('cd_trip_type', db.Integer)
    drop_mob = db.Column('cd_drop_no', db.Integer)
    rep = db.Column('cd_pc_rep', db.Integer)
    region = db.Column('cd_b_region', db.Integer)
    dist = db.Column('cd_dist', db.Integer)
    comment = db.Column('cd_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=0):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == CardekhoBookings.TRIP_PICKUP:
            self.trip_type = CardekhoBookings.TRIP_PICKUP
        elif type == CardekhoBookings.TRIP_DELIVERY:
            self.trip_type = CardekhoBookings.TRIP_DELIVERY
        else:
            self.trip_type = CardekhoBookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = dist


class CardekhoUtil(db.Model):
    __tablename__ = 'cardekho_util'
    util_id = db.Column('cd_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('cd_util_book', db.Integer)
    idx = db.Column('cd_util_index', db.Integer)
    pic = db.Column('cd_util_pic', db.String)
    amt = db.Column('cd_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)



class MahindraRep(db.Model):
    __tablename__ = 'mahindra_rep'
    id = db.Column('mh_rep_id', db.Integer, primary_key=True)
    name = db.Column('mh_name', db.String)
    owner_id = db.Column('mh_rep_cr', db.Integer)
    mobile = db.Column('mh_rep_mob', db.Integer)
    region = db.Column('mh_region', db.Integer)
    timestamp = db.Column('mh_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = int(user)
        self.mobile = int(mob)
        self.region = int(regn)
        self.timestamp = datetime.utcnow()


class MahindraPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'mahindra_pic'
    pic_id = db.Column('mh_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('mh_pic_book', db.Integer)
    idx = db.Column('mh_pic_index', db.Integer)
    pic = db.Column('mh_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class MahindraBookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2

    __tablename__ = 'mahindra_bookings'
    id = db.Column('mh_book_id', db.Integer, primary_key=True)
    ref = db.Column('mh_book_ref', db.Integer)
    appt = db.Column('mh_appt_id', db.String)
    veh_no = db.Column('mh_veh_reg', db.String)
    veh_model = db.Column('mh_veh_mdl', db.String)
    trip_type = db.Column('mh_trip_type', db.Integer)
    drop_mob = db.Column('mh_drop_no', db.Integer)
    rep = db.Column('mh_pc_rep', db.Integer)
    region = db.Column('mh_b_region', db.Integer)
    dist = db.Column('mh_dist', db.Integer)
    comment = db.Column('mh_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=0):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == MahindraBookings.TRIP_PICKUP:
            self.trip_type = MahindraBookings.TRIP_PICKUP
        elif type == MahindraBookings.TRIP_DELIVERY:
            self.trip_type = MahindraBookings.TRIP_DELIVERY
        else:
            self.trip_type = MahindraBookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = dist


class MahindraUtil(db.Model):
    __tablename__ = 'mahindra_util'
    util_id = db.Column('mh_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('mh_util_book', db.Integer)
    idx = db.Column('mh_util_index', db.Integer)
    pic = db.Column('mh_util_pic', db.String)
    amt = db.Column('mh_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)


class RevvV2Rep(db.Model):
    __tablename__ = 'revv_v2_rep'
    id = db.Column('revv_v2_rep_id', db.Integer, primary_key=True)
    name = db.Column('revv_v2_name', db.String)
    owner_id = db.Column('revv_v2_rep_cr', db.Integer)
    mobile = db.Column('revv_v2_rep_mob', db.Integer)
    region = db.Column('revv_v2_region', db.Integer)
    timestamp = db.Column('revv_v2_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = user
        self.mobile = mob
        self.region = regn
        self.timestamp = datetime.utcnow()


class RevvV2Pic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'revv_v2_pic'
    pic_id = db.Column('revv_v2_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('revv_v2_pic_book', db.Integer)
    idx = db.Column('revv_v2_pic_index', db.Integer)
    pic = db.Column('revv_v2_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class RevvV2Bookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2

    __tablename__ = 'revv_v2_bookings'
    id = db.Column('revv_v2_book_id', db.Integer, primary_key=True)
    ref = db.Column('revv_v2_book_ref', db.Integer)
    appt = db.Column('revv_v2_appt_id', db.String)
    veh_no = db.Column('revv_v2_veh_reg', db.String)
    veh_model = db.Column('revv_v2_veh_mdl', db.String)
    trip_type = db.Column('revv_v2_trip_type', db.Integer)
    drop_mob = db.Column('revv_v2_drop_no', db.Integer)
    rep = db.Column('revv_v2_pc_rep', db.Integer)
    region = db.Column('revv_v2_b_region', db.Integer)
    dist = db.Column('revv_v2_dist', db.Integer)
    comment = db.Column('revv_v2_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=0):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == RevvV2Bookings.TRIP_PICKUP:
            self.trip_type = RevvV2Bookings.TRIP_PICKUP
        elif type == RevvV2Bookings.TRIP_DELIVERY:
            self.trip_type = RevvV2Bookings.TRIP_DELIVERY
        else:
            self.trip_type = RevvV2Bookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = dist


class RevvV2Util(db.Model):
    __tablename__ = 'revv_v2_util'
    util_id = db.Column('revv_v2_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('revv_v2_util_book', db.Integer)
    idx = db.Column('revv_v2_util_index', db.Integer)
    pic = db.Column('revv_v2_util_pic', db.String)
    amt = db.Column('revv_v2_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)


class UserFCM(db.Model):
    __tablename__ = 'user_fcm'
    __table_args__ = (PrimaryKeyConstraint('user_fcm_id', 'user_fcm_device'),)
    user_id = db.Column('user_fcm_id', db.Integer)
    token = db.Column('user_fcm_token', db.String)
    device = db.Column('user_fcm_device', db.String)

    def __init__(self, user_id, device, token):
        self.user_id = int(user_id)
        self.device = device
        self.token = token
        
        
class UserAnalyticsToken(db.Model):
    __tablename__ = 'user_analytics_token'
    __table_args__ = (PrimaryKeyConstraint('user_analytics_id', 'user_analytics_device'),)
    user_id = db.Column('user_analytics_id', db.Integer)
    analytics_token = db.Column('user_analytics_token', db.String)
    device = db.Column('user_analytics_device', db.String)

    def __init__(self, user_id, device, analytics_token):
        self.user_id = int(user_id)
        self.device = device
        self.analytics_token = analytics_token


class DriverFCM(db.Model):
    __tablename__ = 'driver_fcm'
    __table_args__ = (PrimaryKeyConstraint('driver_fcm_id', 'driver_fcm_device'),)
    driver_id = db.Column('driver_fcm_id', db.Integer)
    token = db.Column('driver_fcm_token', db.String)
    device = db.Column('driver_fcm_device', db.String)

    def __init__(self, driver_id, device, token):
        self.driver_id = int(driver_id)
        self.device = device
        self.token = token
        
class DriverAnalyticsToken(db.Model):
    __tablename__ = 'driver_analytics_token'
    __table_args__ = (PrimaryKeyConstraint('driver_analytics_id', 'driver_analytics_device'),)
    driver_id = db.Column('driver_analytics_id', db.Integer)
    analytics_token = db.Column('driver_analytics_token', db.String)
    device = db.Column('driver_analytics_device', db.String)

    def __init__(self, driver_id, device, analytics_token):
        self.driver_id = int(driver_id)
        self.device = device
        self.analytics_token = analytics_token



class DriverIdle(db.Model):
    __tablename__ = 'driver_idle'
    id = db.Column('driver_idle_id', db.Integer, primary_key=True)
    driver_id = db.Column('driver_idle_did', db.Integer)
    idle_date = db.Column('driver_idle_date', db.Date)

    def __init__(self, driver_id, date):
        self.driver_id = int(driver_id)
        self.idle_date = date


class DriverBank(db.Model):
    DOC_VERIFIED = 1
    DOC_DISPUTED = 0
    DOC_NOT_FOUND = -1
    DOC_NOT_VERIFIED = -2 
    
    __tablename__ = 'driver_bank'
    id = db.Column('db_id', db.Integer, primary_key=True)
    driver_id = db.Column('db_driver_id', db.Integer)
    #bank_name = db.Column('db_bank_name', db.String)
    acc_no = db.Column('db_acc_no', db.String)
    bank_verified= db.Column('db_bank_verified', db.Integer)
    ifsc = db.Column('db_ifsc', db.String)
    pan_no = db.Column('db_pan_no', db.String)
    acc_doc = db.Column('db_acc_doc', db.String)

    def __init__(self, driver_id, acc_no, ifsc, pan_no, acc_doc,bank_verified=-2):
        self.driver_id = int(driver_id)
        self.acc_no = html.escape(acc_no)
        self.ifsc = html.escape(ifsc)
        self.pan_no = html.escape(pan_no)
        self.acc_doc = acc_doc
        self.bank_verified=bank_verified


class DriverSkill(db.Model):
    __tablename__ = 'driver_skill'
    id = db.Column('ds_id', db.Integer, primary_key=True)
    driver_id = db.Column('ds_driver_id', db.Integer)
    hb_m = db.Column('ds_hb_m', db.Integer)
    sed_m = db.Column('ds_sed_m', db.Integer)
    suv_m = db.Column('ds_suv_m', db.Integer)
    lux_m = db.Column('ds_lux_m', db.Integer)
    hb_a = db.Column('ds_hb_a', db.Integer)
    sed_a = db.Column('ds_sed_a', db.Integer)
    suv_a = db.Column('ds_suv_a', db.Integer)
    lux_a = db.Column('ds_lux_a', db.Integer)

    def __init__(self, driver_id, hb_m, sed_m, suv_m, lux_m, hb_a, sed_a, suv_a, lux_a):
        self.driver_id = int(driver_id)
        self.hb_m = hb_m
        self.sed_m = sed_m
        self.suv_m = suv_m
        self.lux_m = lux_m
        self.hb_a = hb_a
        self.sed_a = sed_a
        self.suv_a = suv_a
        self.lux_a = lux_a


class DriverInfo(db.Model):
    
    DOC_VERIFIED = 1
    DOC_DISPUTED = 0
    DOC_NOT_FOUND = -1
    DOC_NOT_VERIFIED = -2 
        
    __tablename__ = 'driver_info'
    id = db.Column('di_id', db.Integer, primary_key=True)
    driver_id = db.Column('di_driver_id', db.Integer)
    license = db.Column('di_license', db.String)
    license_exp = db.Column('di_license_exp', db.Date)
    license_verified=db.Column('di_lic_verified',db.Integer)
    dob = db.Column('di_dob', db.Date)
    pres_region = db.Column('di_pres_region', db.String)
    pres_addr = db.Column('di_pres_addr', db.String)
    pres_addr_lat = db.Column('di_pres_addr_lat', db.Float)
    pres_addr_lng = db.Column('di_pres_addr_lng', db.Float)
    verf_name = db.Column('di_verf_name', db.String)
    verf_ph = db.Column('di_verf_ph', db.String)
    verf_rel = db.Column('di_verf_rel', db.String)
    id_no = db.Column('di_id_no', db.String)
    id_verified=db.Column('di_id_verified',db.Integer)
    behav = db.Column('di_behav', db.Integer)
    road = db.Column('di_road', db.Integer)
    body = db.Column('di_body', db.Integer)
    pic = db.Column('di_pic', db.String)
    driver_id_doc_f = db.Column('di_driver_id_doc_f', db.String)
    driver_id_doc_b = db.Column('di_driver_id_doc_b', db.String)
    driver_lic_doc_f = db.Column('di_driver_lic_doc_f', db.String)
    driver_lic_doc_b = db.Column('di_driver_lic_doc_b', db.String)
    driver_description = db.Column('di_description', db.String(1000))
    driver_remark = db.Column('di_remark', db.String(1000))
    driver_trip_pref = db.Column('di_driver_trip_pref', db.Integer, nullable=True)
    driver_languages=db.Column('di_driver_languages', db.String)
    driver_reg_source=db.Column('di_r_source',db.String(50))
    driver_bike_status = db.Column('di_bike_status', db.Boolean)
    extras = db.Column('driver_information_extras',db.JSON, nullable=True)
    reg_lat = db.Column('driver_registration_lat',db.Float)
    reg_lng = db.Column('driver_registration_lng', db.Float)
    timestamp = db.Column('driver_registration_timestamp', db.DateTime)

    def __init__(self, driver_id, license, license_exp, dob, pres_region, pres_addr,
                        verf_name, verf_ph, verf_rel, lat, lng, driver_id_doc_f, driver_id_doc_b,
                        driver_lic_doc_f, driver_lic_doc_b, pic, id_no="",
                        description="", driver_trip_pref=None, id_verified=-2, license_verified=-2,extras= {"height": 0,"weight":0},driver_languages="",driver_reg_source=None,driver_bike_status=False,reg_lat= None,reg_lng = None):
        self.driver_id = int(driver_id)
        self.dob = dob
        self.license = license
        self.license_exp = license_exp
        self.pres_region = html.escape(pres_region)
        self.pres_addr = html.escape(pres_addr)
        self.verf_name = html.escape(verf_name)
        self.verf_ph = int(verf_ph)
        self.verf_rel = html.escape(verf_rel)
        self.behav = 0
        self.road = 0
        self.body = 0
        self.pres_addr_lat = lat
        self.pres_addr_lng = lng
        self.driver_id_doc_f = driver_id_doc_f
        self.driver_id_doc_b = driver_id_doc_b
        self.driver_lic_doc_f = driver_lic_doc_f
        self.driver_lic_doc_b = driver_lic_doc_b
        self.pic = pic
        self.id_no = html.escape(id_no)
        self.driver_description = html.escape(description)
        self.driver_trip_pref = driver_trip_pref 
        self.id_verified=id_verified
        self.license_verified=license_verified
        self.extras =extras
        self.reg_lat = reg_lat
        self.reg_lng = reg_lng
        self.timestamp = datetime.utcnow()
        self.driver_languages= driver_languages
        self.driver_reg_source=driver_reg_source
        self.driver_bike_status=driver_bike_status
        

class DriverVerify(db.Model):
    __tablename__ = 'driver_verify'
    driver_id = db.Column('dv_id', db.Integer, primary_key=True)
    id_card = db.Column('dv_id_card', db.Boolean)
    photo = db.Column('dv_photo', db.Boolean)
    ref = db.Column('dv_ref', db.Boolean)
    lic = db.Column('dv_lic', db.Boolean)
    bank = db.Column('dv_bank', db.Boolean)

    def __init__(self, driver_id):
        self.driver_id = int(driver_id)
        self.photo = self.ref = self.id_card = self.lic = self.bank = False


class BookingAlloc(db.Model):
    __tablename__ = 'booking_alloc'
    id = db.Column('ba_id', db.Integer, primary_key=True)
    booking_id = db.Column('ba_book_id', db.Integer)
    driver_id = db.Column('ba_driver_id', db.Integer)
    alloc_id = db.Column('ba_alloc_id', db.Integer)
    action = db.Column('ba_action', db.Integer)
    phase = db.Column('ba_pending_phase', db.Integer)
    score = db.Column('ba_score', db.Float)
    timestamp = db.Column('ba_timestamp', db.DateTime)

    def __init__(self, booking, driver, alloc_user, action=0, phase=-1,
                 score=-99):
        self.driver_id = int(driver)
        self.booking_id = int(booking)
        self.alloc_id = alloc_user
        self.action = int(action)
        self.timestamp = datetime.utcnow()
        self.phase = int(phase)
        self.score = float(score)


class BookingFeedback(db.Model):
    __tablename__ = 'booking_feedback'
    FB_POS = 1
    FB_NEUT = 0
    FB_NEG = -1
    id = db.Column('bf_book_id', db.Integer, primary_key=True)
    feedback = db.Column('bf_book_fb', db.Integer)
    admin_id = db.Column('bf_admin_id', db.Integer)

    def __init__(self, booking, feedback, admin_user):
        self.id = int(booking)
        self.feedback = int(feedback)
        self.admin_id = int(admin_user)


class UserLoc(db.Model):
    __tablename__ = 'user_loc'
    id = db.Column('ul_id', db.Integer, primary_key=True)
    user_id = db.Column('ul_user_id', db.Integer)
    lat = db.Column('ul_lat', db.Integer)
    lng = db.Column('ul_lng', db.Integer)
    timestamp = db.Column('ul_timestamp', db.DateTime)

    def __init__(self, user, lat, lng):
        self.user_id = int(user)
        self.lat = float(lat)
        self.lng = float(lng)
        self.timestamp = datetime.utcnow()


class DriverLoc(db.Model):
    __tablename__ = 'driver_loc'
    id = db.Column('dl_id', db.Integer, primary_key=True)
    driver_id = db.Column('dl_driver_id', db.Integer)
    lat = db.Column('dl_lat', db.Integer)
    lng = db.Column('dl_lng', db.Integer)
    timestamp = db.Column('dl_timestamp', db.DateTime)

    def __init__(self, driver, lat, lng):
        self.driver_id = int(driver)
        self.lat = float(lat)
        self.lng = float(lng)
        self.timestamp = datetime.utcnow()

class DriverSetLoc(db.Model): 
    __tablename__ = 'driver_set_loc' 
    id = db.Column('dsl_id', db.Integer, primary_key=True) 
    driver_id = db.Column('dsl_driver_id', db.Integer) 
    lat = db.Column('dsl_lat', db.Integer) 
    lng = db.Column('dsl_lng', db.Integer) 
    timestamp = db.Column('dl_timestamp', db.DateTime)

    def __init__(self, driver, lat, lng):
        self.driver_id = int(driver)
        self.lat = float(lat)
        self.lng = float(lng)
        self.timestamp = datetime.utcnow()
        
class TripStartPic(db.Model):
    __tablename__ = 'trip_start_pic'
    id = db.Column('ts_id', db.Integer, primary_key=True)
    book_id = db.Column('ts_book_id', db.Integer)
    car_left = db.Column('ts_car_left', db.String)
    car_right = db.Column('ts_car_right', db.String)
    car_back = db.Column('ts_car_back', db.String)
    car_front = db.Column('ts_car_front', db.String)
    selfie = db.Column('ts_selfie', db.String)
    extra1 = db.Column('ts_extra1', db.String)
    extra2 = db.Column('ts_extra2', db.String)
    extra3 = db.Column('ts_extra3', db.String)
    extra4 = db.Column('ts_extra4', db.String)
    timestamp = db.Column('ts_timestamp', db.DateTime)

    def __init__(self, book_id, car_left, car_right, car_back, car_front, selfie,
                    extra1, extra2, extra3, extra4):
        self.book_id = int(book_id)
        self.car_left = car_left
        self.car_right = car_right
        self.car_back = car_back
        self.car_front = car_front
        self.selfie = selfie
        self.extra1 = extra1
        self.extra2 = extra2
        self.extra3 = extra3
        self.extra4 = extra4
        self.timestamp = datetime.utcnow()


class TripEndPic(db.Model):
    __tablename__ = 'trip_end_pic'
    id = db.Column('te_id', db.Integer, primary_key=True)
    book_id = db.Column('te_book_id', db.Integer)
    car_left = db.Column('te_car_left', db.String)
    car_right = db.Column('te_car_right', db.String)
    car_back = db.Column('te_car_back', db.String)
    car_front = db.Column('te_car_front', db.String)
    extra1 = db.Column('te_extra1', db.String)
    extra2 = db.Column('te_extra2', db.String)
    extra3 = db.Column('te_extra3', db.String)
    extra4 = db.Column('te_extra4', db.String)
    timestamp = db.Column('te_timestamp', db.DateTime)

    def __init__(self, book_id, car_left, car_right, car_back, car_front,
                    extra1, extra2, extra3, extra4):
        self.book_id = int(book_id)
        self.car_left = car_left
        self.car_right = car_right
        self.car_back = car_back
        self.car_front = car_front
        self.extra1 = extra1
        self.extra2 = extra2
        self.extra3 = extra3
        self.extra4 = extra4
        self.timestamp = datetime.utcnow()


class DownloadCampaign(db.Model):
    __tablename__ = 'download_campaign'
    id = db.Column('dc_id', db.Integer, primary_key=True)
    campaign_str = db.Column('dc_campaign_str', db.String(200))
    campaign_info = db.Column('dc_campaign_info', db.DateTime)
    timestamp = db.Column('dc_timestamp', db.DateTime)

    def __init__(self, campaign_str, campaign_info):
        self.campaign_str = campaign_str.upper()
        self.campaign_info = html.escape(campaign_info)
        self.timestamp = datetime.utcnow()


class DownloadReq(db.Model):
    __tablename__ = 'download_req'
    id = db.Column('dr_id', db.Integer, primary_key=True)
    campaign_id = db.Column('dr_campaign_id', db.String(200))
    redirect_target = db.Column('dr_redirect_target', db.String(100))
    agent = db.Column('dr_agent', db.String(150))
    timestamp = db.Column('dr_timestamp', db.DateTime)

    def __init__(self, campaign_id, redirect_target, agent):
        self.campaign_id = campaign_id
        self.redirect_target = redirect_target
        self.agent = agent
        self.timestamp = datetime.utcnow()


class TripLog(db.Model):
    ACTION_START = 1
    ACTION_STOP = 2
    ACTION_RESTART = 3
    ACTION_NEWSTOP = 4
    ACTION_START_PIC = 5
    ACTION_STOP_PIC = 6
    ACTION_START_CHANGE = 7
    ACTION_OTHER = 8

    ACTION_INIT = 10
    ACTION_REACHED_SRC = 11
    ACTION_REACHED_DEST = 12

    __tablename__ = 'trip_log'
    id = db.Column('tl_id', db.Integer, primary_key=True)
    booking_id = db.Column('tl_book_id', db.Integer)
    driver_user = db.Column('tl_driver_id', db.Integer) # this is driver's user id not driver id
    action_user = db.Column('tl_action_user', db.Integer)
    action = db.Column('tl_action', db.Integer)
    lat = db.Column('tl_lat', db.Float)
    lng = db.Column('tl_lng', db.Float)
    timestamp = db.Column('tl_timestamp', db.DateTime)

    def __init__(self, booking, driver, action_user=0, action=7, lat=-1.0, lng=-1.0):
        self.driver_user = int(driver)
        self.booking_id = int(booking)
        self.action_user = int(action_user)
        self.action = int(action)
        self.lat = float(lat)
        self.lng = float(lng)
        self.timestamp = datetime.utcnow()

class BookPricing(db.Model):
    __tablename__ = 'book_pricing'

    book_id = db.Column('book_pricing_book_id', db.Integer, primary_key=True)
    estimate = db.Column('book_pricing_estimate', db.Float)
    base_ch = db.Column('book_pricing_base_ch', db.Float)
    cartype_ch = db.Column('book_pricing_cartype_ch', db.Float)
    night_ch = db.Column('book_pricing_night_ch', db.Float)
    food_ch = db.Column('book_pricing_food_ch', db.Float)
    booking_ch = db.Column('book_pricing_booking_ch', db.Float)
    dist_ch = db.Column('book_pricing_dist_ch', db.Float)
    cgst = db.Column('book_pricing_cgst', db.Float)
    sgst = db.Column('book_pricing_sgst', db.Float)
    est_pre_tax = db.Column('book_pricing_estimate_pre_tax' , db.Float)
    insurance_ch = db.Column('book_pricing_insurance_ch', db.Float)
    driver_base_ch = db.Column('book_pricing_driver_base_ch', db.Float)
    driver_night_ch = db.Column('book_pricing_driver_night_ch', db.Float)
    driver_type_ch = db.Column('book_pricing_driver_type_ch', db.Float) #premium driver
    coupon_discount = db.Column('book_pricing_coupon_discount', db.Float)

    def __init__(self, bid, est=0, base=0, cartype=0, night=0, food=0,
                 booking=0, dist=0, cgst=0, sgst=0, est_pre_tax=0, state=0,
                 insurance=0, driver_base=0, driver_night=0, driver_type=0, coupon_discount=0):
        self.book_id = bid
        self.estimate = est
        self.cartype_ch = cartype
        self.night_ch = night
        self.food_ch = food
        self.booking_ch = booking
        self.dist_ch = dist
        self.base_ch = base
        self.cgst = cgst
        self.sgst = sgst
        self.est_pre_tax = est_pre_tax
        self.insurance_ch = insurance
        self.driver_base_ch = driver_base
        self.driver_night_ch = driver_night
        self.driver_type_ch = driver_type
        self.coupon_discount = coupon_discount


class TripPricing(db.Model):
    __tablename__ = 'trip_pricing'

    book_id = db.Column('trip_pricing_book_id', db.Integer, primary_key=True)
    base_ch = db.Column('trip_pricing_base_ch', db.Float)
    cartype_ch = db.Column('trip_pricing_cartype_ch', db.Float)
    night_ch = db.Column('trip_pricing_night_ch', db.Float)
    ot_ch = db.Column('trip_pricing_ot_ch', db.Float)
    booking_ch = db.Column('trip_pricing_booking_ch', db.Float)
    dist_ch = db.Column('trip_pricing_dist_ch', db.Float)
    cgst = db.Column('trip_pricing_cgst', db.Float)
    sgst = db.Column('trip_pricing_sgst', db.Float)
    insurance_ch = db.Column('trip_pricing_insurance_ch', db.Float)
    driver_base_ch = db.Column('trip_pricing_driver_base_ch', db.Float)
    driver_night_ch = db.Column('trip_pricing_driver_night_ch', db.Float)
    driver_ot_ch = db.Column('trip_pricing_driver_ot_ch', db.Float)

    def __init__(self, book_id, base_ch, night_ch, ot_ch, booking_ch, 
                  insurance_ch, driver_base_ch, driver_night_ch, driver_ot_ch, cartype_ch=0, dist_ch=0, cgst=0, sgst=0):
        self.book_id = book_id
        self.base_ch = base_ch
        self.cartype_ch = cartype_ch
        self.night_ch = night_ch
        self.ot_ch = ot_ch
        self.booking_ch = booking_ch
        self.dist_ch = dist_ch
        self.cgst = cgst
        self.sgst = sgst
        self.insurance_ch = insurance_ch
        self.driver_base_ch = driver_base_ch
        self.driver_night_ch = driver_night_ch
        self.driver_ot_ch = driver_ot_ch



class BhandariRep(db.Model):
    __tablename__ = 'bhandari_rep'
    id = db.Column('bd_rep_id', db.Integer, primary_key=True)
    name = db.Column('bd_name', db.String)
    owner_id = db.Column('bd_rep_cr', db.Integer)
    mobile = db.Column('bd_rep_mob', db.Integer)
    region = db.Column('bd_region', db.Integer)
    timestamp = db.Column('bd_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = user
        self.mobile = mob
        self.region = regn
        self.timestamp = datetime.utcnow()


class BhandariPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'bhandari_pic'
    pic_id = db.Column('bd_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('bd_pic_book', db.Integer)
    idx = db.Column('bd_pic_index', db.Integer)
    pic = db.Column('bd_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class BhandariBookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2

    __tablename__ = 'bhandari_bookings'
    id = db.Column('bd_book_id', db.Integer, primary_key=True)
    ref = db.Column('bd_book_ref', db.Integer)
    appt = db.Column('bd_appt_id', db.String)
    veh_no = db.Column('bd_veh_reg', db.String)
    veh_model = db.Column('bd_veh_mdl', db.String)
    trip_type = db.Column('bd_trip_type', db.Integer)
    drop_mob = db.Column('bd_drop_no', db.Integer)
    rep = db.Column('bd_pc_rep', db.Integer)
    region = db.Column('bd_b_region', db.Integer)
    dist = db.Column('bd_dist', db.Integer)
    comment = db.Column('bd_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=0):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == BhandariBookings.TRIP_PICKUP:
            self.trip_type = BhandariBookings.TRIP_PICKUP
        elif type == BhandariBookings.TRIP_DELIVERY:
            self.trip_type = BhandariBookings.TRIP_DELIVERY
        else:
            self.trip_type = BhandariBookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = dist


class BhandariUtil(db.Model):
    __tablename__ = 'bhandari_util'
    util_id = db.Column('bd_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('bd_util_book', db.Integer)
    idx = db.Column('bd_util_index', db.Integer)
    pic = db.Column('bd_util_pic', db.String)
    amt = db.Column('bd_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)


class ReferralUse(db.Model):
    __tablename__ = 'referral_use'
    REFERRAL_SRC_AMT = 50
    REFERRAL_DEST_AMT = 100
    REFERRAL_SRC_BONUS_AMT = 0
    REFERRAL_DEST_BONUS_AMT = 150

    ru_id = db.Column('ru_id', db.Integer, primary_key=True)
    ru_src_uid = db.Column('ru_src_uid', db.Integer)
    ru_dest_uid = db.Column('ru_dest_uid', db.Integer)
    ru_src_user_trans = db.Column('ru_src_user_trans', db.String)
    ru_dest_user_trans = db.Column('ru_dest_user_trans', db.String)
    ru_src_driver_trans = db.Column('ru_src_driver_trans', db.String)
    ru_dest_driver_trans = db.Column('ru_dest_driver_trans', db.String)
    ru_timestamp = db.Column('ru_timestamp', db.TIMESTAMP, server_default=db.func.current_timestamp())

    def __init__(self, ru_src_uid, ru_dest_uid, ru_src_user_trans=None, ru_dest_user_trans=None,
                 ru_src_driver_trans=None, ru_dest_driver_trans=None):
        self.ru_src_uid = ru_src_uid
        self.ru_dest_uid = ru_dest_uid
        self.ru_src_user_trans = ru_src_user_trans
        self.ru_dest_user_trans = ru_dest_user_trans
        self.ru_src_driver_trans = ru_src_driver_trans
        self.ru_dest_driver_trans = ru_dest_driver_trans


class SpinnyRep(db.Model):
    __tablename__ = 'spinny_rep'
    id = db.Column('sp_rep_id', db.Integer, primary_key=True)
    name = db.Column('sp_name', db.String)
    owner_id = db.Column('sp_rep_cr', db.Integer)
    mobile = db.Column('sp_rep_mob', db.Integer)
    region = db.Column('sp_region', db.Integer)
    timestamp = db.Column('sp_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = int(user)
        self.mobile = int(mob)
        self.region = int(regn)
        self.timestamp = datetime.utcnow()


class SpinnyPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'spinny_pic'
    pic_id = db.Column('sp_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('sp_pic_book', db.Integer)
    idx = db.Column('sp_pic_index', db.Integer)
    pic = db.Column('sp_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class SpinnyBookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2

    DEFAULT_REASON_FROM_SPINNY = 6

    SPINNY_TYPE_MAPPING = {
        0: "Customer to hub",
        1: "Customer to partner workshop",
        2: "Customer to Spinny workshop",
        3: "hub to customer",
        4: "hub to Spinny workshop",
        5: "Spinny workshop to hub",
        6: "hub to hub",
        7: "hub to partner workshop",
        8: "partner workshop to hub",
        9: "hub to OEM",
        10: "OEM to hub",
        11: "partner workshop to Spinny workshop",
        12: "Spinny workshop to partner workshop",
        13: "Partner workshop to customer",
        14: "OEM to workshop",
        15: "OEM to partner workshop",
        16: "OEM to spinny workshop"
    }

    REASON_DETAIL_MAPPING = {
        BookingCancelled.RSN_B2B_DRIVER_NOT_ASSIGNED: 'DRIVER_NOT_ASSIGNED', #21
        BookingCancelled.RSN_B2B_DRIVER_DENIED: 'DRIVER_DENIED', #22
        BookingCancelled.RSN_B2B_WAIT_TIME_TOO_LONG: 'WAIT_TIME_TOO_LONG', #23
        BookingCancelled.RSN_B2B_RESCHEDULED: 'RESCHEDULED_TRIP', #24
        BookingCancelled.RSN_B2B_BOOKING_CANCELLED: 'BOOKING_CANCELLED_BY_SPINNY', #25
        BookingCancelled.RSN_B2B_D4M_CANCEL: 'BOOKING_CANCELLED_BY_D4M', #26
        BookingCancelled.RSN_B2B_OTHER: 'OTHER_REASON', #61
    }
    REASON_SERIAL_MAPPING = {
        1:BookingCancelled.RSN_B2B_DRIVER_NOT_ASSIGNED, #21
        2:BookingCancelled.RSN_B2B_DRIVER_DENIED, #22
        3:BookingCancelled.RSN_B2B_WAIT_TIME_TOO_LONG, #23
        4:BookingCancelled.RSN_B2B_RESCHEDULED, #24
        5:BookingCancelled.RSN_B2B_BOOKING_CANCELLED, #25
        6:BookingCancelled.RSN_B2B_OTHER, #61
    }

    __tablename__ = 'spinny_bookings'
    id = db.Column('sp_book_id', db.Integer, primary_key=True)
    ref = db.Column('sp_book_ref', db.Integer)
    appt = db.Column('sp_appt_id', db.String)
    veh_no = db.Column('sp_veh_reg', db.String)
    veh_model = db.Column('sp_veh_mdl', db.String)
    trip_type = db.Column('sp_trip_type', db.Integer)
    trip_type_detail = db.Column('sp_trip_type_detail', db.String)
    business_func = db.Column('sp_business_func', db.String)
    business_cat = db.Column('sp_business_cat', db.String)
    drop_mob = db.Column('sp_drop_no', db.Integer)
    rep = db.Column('sp_pc_rep', db.Integer)
    region = db.Column('sp_b_region', db.Integer)
    dist = db.Column('sp_dist', db.Integer)
    comment = db.Column('sp_comment', db.String)

    def __init__(self, booking, appt, no, model, type, type_detail,
				 bus_func, bus_cat, drop_mob, rep, region, dist=0):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == SpinnyBookings.TRIP_PICKUP:
            self.trip_type = SpinnyBookings.TRIP_PICKUP
        elif type == SpinnyBookings.TRIP_DELIVERY:
            self.trip_type = SpinnyBookings.TRIP_DELIVERY
        else:
            self.trip_type = SpinnyBookings.TRIP_BOTH
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = dist
        self.trip_type_detail = type_detail
        self.business_func = bus_func
        self.business_cat = bus_cat


class SpinnyUtil(db.Model):
    __tablename__ = 'spinny_util'
    util_id = db.Column('sp_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('sp_util_book', db.Integer)
    idx = db.Column('sp_util_index', db.Integer)
    pic = db.Column('sp_util_pic', db.String)
    amt = db.Column('sp_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)

class MyOpCallLog(db.Model):
    __tablename__ = 'my_operator_log'
    id = db.Column('cl_id', db.Integer, primary_key=True)
    uid = db.Column('cl_uid', db.String, unique=True)
    node_id = db.Column('cl_node_id', db.String)
    timestamp = db.Column('cl_timestamp', db.DateTime)
    caller_id = db.Column('cl_caller_id', db.String)
    invalid_count = db.Column('cl_invalid_count', db.Integer)
    call_success = db.Column('cl_call_success', db.Boolean)
    def __init__(self, uid, node_id, timestamp, callerId, callSuccess=False):
        self.uid = uid
        self.node_id = node_id
        self.timestamp = timestamp
        self.caller_id = callerId
        self.invalid_count = 0
        self.call_success = callSuccess

class CallLog(db.Model):
    __tablename__ = 'call_log'
    id =  db.Column('chl_id', db.Integer, primary_key=True)
    uuid = db.Column('chl_uuid', db.String(40))
    dest_num = db.Column('chl_dest_num', db.String(20))
    did_num = db.Column('chl_did_num', db.String(20))  
    src_num = db.Column('chl_src_num', db.String(20))
    start_stamp = db.Column('chl_start_stamp', db.DateTime)
    answer_stamp = db.Column('chl_answer_stamp', db.DateTime)
    end_stamp = db.Column('chl_end_stamp', db.DateTime)
    hangup_cause = db.Column('chl_hangup_cause', db.String(50))
    duration = db.Column('chl_duration', db.Integer)
    call_status = db.Column('chl_call_status', db.String(20))
    call_id = db.Column('chl_call_id', db.String(40))
    call_connected = db.Column('chl_call_connected', db.Boolean)
    recording_url = db.Column('chl_recording_url', db.String(255))
    book_id = db.Column('chl_book_id', db.Integer)

    def __init__(self, uuid, book_id, dest_num=None, did_num=None, src_num=None, start_stamp=None, answer_stamp=None, end_stamp=None, 
                        hangup_cause=None, duration=None, call_status=None, call_id=None, call_connected=None, recording_url=None):
        
        self.uuid = uuid
        self.dest_num = dest_num
        self.did_num = did_num
        self.src_num = src_num
        self.start_stamp = start_stamp
        self.answer_stamp = answer_stamp
        self.end_stamp = end_stamp
        self.hangup_cause = hangup_cause
        self.duration = duration
        self.call_status = call_status
        self.call_id = call_id
        self.call_connected = call_connected
        self.recording_url = recording_url
        self.book_id = book_id

class CallerInput(db.Model):
    __tablename__ = 'caller_input'
    id = db.Column('ci_id', db.Integer, primary_key=True)
    uid = db.Column('ci_uid', db.String)
    call_log_id = db.Column('ci_call_log_id', db.Integer)
    key_press = db.Column('ci_key_press', db.Integer)
    book_code = db.Column('ci_book_code', db.String)
    mobile = db.Column('ci_mobile', db.Integer)

    def __init__(self, uid, callLogId, keyPress, bookCode, mobileNo):
        self.uid = uid
        self.call_log_id = int(callLogId)
        self.key_press = int(keyPress)
        self.book_code = bookCode
        self.mobile = int(mobileNo)

class CallRequestLog(db.Model):
    __tablename__ = 'call_req_log'
    id = db.Column('crl_id', db.Integer, primary_key=True)
    book_code = db.Column('crl_book_code', db.String)
    from_mobile = db.Column('crl_from_mobile', db.Integer)
    to_mobile = db.Column('crl_to_mobile', db.Integer)
    timestamp = db.Column('crl_timestamp', db.DateTime)

    def __init__(self, bookCode, from_mobileNo, to_mobileNo):
        self.book_code = bookCode
        self.from_mobile = int(from_mobileNo)
        self.to_mobile = int(to_mobileNo)
        self.timestamp = datetime.utcnow()

class PrideHondaRep(db.Model):
    __tablename__ = 'pridehonda_rep'
    id = db.Column('ph_rep_id', db.Integer, primary_key=True)
    name = db.Column('ph_name', db.String)
    owner_id = db.Column('ph_rep_cr', db.Integer)
    mobile = db.Column('ph_rep_mob', db.Integer)
    region = db.Column('ph_region', db.Integer)
    timestamp = db.Column('ph_cr_timestamp', db.DateTime)

    def __init__(self, name, user, mob, regn):
        self.name = html.escape(name)
        self.owner_id = int(user)
        self.mobile = int(mob)
        self.region = int(regn)
        self.timestamp = datetime.utcnow()


class PrideHondaPic(db.Model):
    CAR_PIC = 0
    SHEET_PIC = 1
    OTHER_PIC = 2

    __tablename__ = 'pridehonda_pic'
    pic_id = db.Column('ph_pic_id', db.Integer, primary_key=True)
    booking_id = db.Column('ph_pic_book', db.Integer)
    idx = db.Column('ph_pic_index', db.Integer)
    pic = db.Column('ph_pic_url', db.String)

    def __init__(self, book, idx, pic):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic


class PrideHondaBookings(db.Model):
    TRIP_DELIVERY = 0
    TRIP_PICKUP = 1
    TRIP_BOTH = 2

    __tablename__ = 'pridehonda_bookings'
    id = db.Column('ph_book_id', db.Integer, primary_key=True)
    ref = db.Column('ph_book_ref', db.Integer)
    appt = db.Column('ph_appt_id', db.String)
    veh_no = db.Column('ph_veh_reg', db.String)
    veh_model = db.Column('ph_veh_mdl', db.String)
    trip_type = db.Column('ph_trip_type', db.Integer)
    drop_mob = db.Column('ph_drop_no', db.Integer)
    rep = db.Column('ph_pc_rep', db.Integer)
    region = db.Column('ph_b_region', db.Integer)
    dist = db.Column('ph_dist', db.Integer)
    comment = db.Column('ph_comment', db.String)

    def __init__(self, booking, appt, no, model, type, drop_mob, rep, region, dist=0):
        self.ref = booking
        self.appt = html.escape(appt)
        self.veh_no = html.escape(no)
        self.veh_model = html.escape(model)
        if type == PrideHondaBookings.TRIP_PICKUP:
            self.trip_type = PrideHondaBookings.TRIP_PICKUP
        elif type == PrideHondaBookings.TRIP_DELIVERY:
            self.trip_type = PrideHondaBookings.TRIP_DELIVERY
        else:
            self.trip_type = PrideHondaBookings.TRIP_DELIVERY
        self.drop_mob = drop_mob
        self.rep = rep
        self.region = region
        self.comment = ""
        self.dist = dist


class PrideHondaUtil(db.Model):
    __tablename__ = 'pridehonda_util'
    util_id = db.Column('ph_util_id', db.Integer, primary_key=True)
    booking_id = db.Column('ph_util_book', db.Integer)
    idx = db.Column('ph_util_index', db.Integer)
    pic = db.Column('ph_util_pic', db.String)
    amt = db.Column('ph_util_amt', db.Integer)

    def __init__(self, book, idx, pic, amt):
        self.booking_id = int(book)
        self.idx = int(idx)
        self.pic = pic
        self.amt = int(amt)

class DriverApprovalLog(db.Model):
    __tablename__ = 'd_approval_log'
    id = db.Column('d_approval_log_id', db.Integer, primary_key=True)
    driver = db.Column('d_approval_did', db.Integer, nullable=False)
    changes = db.Column('d_approval_changes', db.String(50))
    approval = db.Column('d_approval', db.Integer)
    editedby = db.Column('d_approval_editedby', db.String(50))
    change_from = db.Column('d_approval_from', db.String(50))
    change_to = db.Column('d_approval_to', db.String(50))
    remark = db.Column('d_approval_remark', db.String(100))
    timestamp = db.Column('d_approval_timestamp', db.DateTime, default=datetime.utcnow)
    def __init__(self, driver, changes=None, approval=None, editedby=None, change_from=None, change_to=None, remark=None):
        self.driver = driver
        self.changes = changes
        self.approval = approval
        self.editedby = editedby
        self.change_from = change_from
        self.change_to = change_to
        self.remark = remark
        self.timestamp = datetime.utcnow()


class CustomerDetailsLog(db.Model):
    __tablename__ = 'c_details_log'
    id = db.Column('c_details_log_id', db.Integer, primary_key=True)
    user = db.Column('c_details_uid', db.Integer, nullable=False)
    changes = db.Column('c_details_changes', db.String(50))
    editedby = db.Column('c_details_editedby', db.String(50))
    change_from = db.Column('c_details_from', db.String(250))
    change_to = db.Column('c_details_to', db.String(250))
    timestamp = db.Column('c_details_timestamp', db.DateTime, default=datetime.utcnow)
    def __init__(self, user, changes=None, editedby=None, change_from=None, change_to=None):
        self.user = user
        self.changes = changes
        self.editedby = editedby
        self.change_from = change_from
        self.change_to = change_to
        self.timestamp = datetime.utcnow()

class UserMultipleBooking(db.Model):
    __tablename__ = 'user_multiple_book'
    id = db.Column('user_multiple_book_id', db.Integer, primary_key=True)
    user=db.Column('user_multiple_book_uid', db.Integer, nullable=False)
    def __init__(self, user):
        self.user = user

class DriverGLIDDocDetails(db.Model):
    # Document Types
    TYPE_AADHAR = 0
    TYPE_VOTERID = 1
    __tablename__ = 'grid_lines_iddoc'
    id = db.Column('g_iddoc_id', db.Integer, primary_key=True)
    driver_id = db.Column('g_iddoc_driver_id', db.Integer)
    id_type=db.Column('g_driver_id_type', db.Integer)
    id_card_no =db.Column('g_id_card_no', db.String)
    name = db.Column('g_driver_i_name', db.Text)
    gender = db.Column('g_driver_i_gender', db.Text)
    driver_pic = db.Column('g_driver_i_pic', db.String)
    dob = db.Column('g_driver_i_dob', db.Date)
    house = db.Column('g_driver_house', db.String(50))
    street = db.Column('g_driver_street', db.String(255))
    district = db.Column('g_driver_i_district', db.String(255))
    sub_district = db.Column('g_driver_sub_district', db.String(255))
    landmark = db.Column('g_driver_landmark', db.String(255))
    state = db.Column('g_driver_i_state', db.String(255))
    pincode = db.Column('g_driver_i_pincode', db.String(10))
    country = db.Column('g_driver_country', db.String(255))
    vtc_name = db.Column('g_driver_vtc_name', db.String(255))
    name_status = db.Column('g_driver_i_name_status', db.Boolean)
    photo_status = db.Column('g_driver_i_photo_status', db.Boolean)
    photo_score = db.Column('g_driver_i_photo_score', db.Float)
    dob_status = db.Column('g_driver_i_dob_status', db.Boolean)
    timestamp=db.Column('g_driver_i_timestamp', db.DateTime, default=datetime.utcnow)
    json_dump = db.Column('g_driver_i_json_dump', db.JSON)
    
    def __init__(self, driver_id, id_type, id_card_no, name, gender, driver_pic, dob, house, street,
                 district, sub_district, landmark, state, pincode, country, vtc_name,name_status=False,
                 photo_status=False,photo_score=0.0,dob_status=False,json_dump=None):
        self.driver_id = driver_id
        self.id_type = id_type
        self.id_card_no = id_card_no
        self.name = name
        self.gender = gender
        self.driver_pic = driver_pic
        self.dob = dob
        self.house = house
        self.street = street
        self.district = district
        self.sub_district = sub_district
        self.landmark = landmark
        self.state = state
        self.pincode = pincode
        self.country = country
        self.vtc_name = vtc_name
        self.name_status=name_status
        self.photo_status=photo_status
        self.photo_score=photo_score
        self.dob_status=dob_status
        self.timestamp = datetime.utcnow()
        self.json_dump = json_dump
    
class  DriverGLBankDetails(db.Model):
    __tablename__ = 'grid_lines_bankdoc'

    id = db.Column('g_bankdoc_id', db.Integer, primary_key=True)
    driver_id = db.Column('g_bankdoc_driver_id', db.Integer)
    name = db.Column('g_driver_b_name', db.Text)
    acc_no = db.Column('g_driver_acc_no', db.String)
    ifsc = db.Column('g_driver_ifsc', db.String)
    bank_name=db.Column('g_driver_bank_name', db.String)
    district = db.Column('g_driver_b_district', db.String)
    bank_branch=db.Column('g_driver_bank_branch',db.String)
    name_status = db.Column('g_driver_bank_name_status', db.Boolean)
    timestamp=db.Column('g_driver_bank_timestamp', db.DateTime, default=datetime.utcnow)
    json_dump = db.Column('g_driver_bank_json_dump', db.JSON)
    
    def __init__(self, driver_id, name, acc_no ,ifsc, bank_name, district, bank_branch,name_status=False,json_dump=None):
        self.driver_id = driver_id
        self.name = name
        self.acc_no=acc_no
        self.ifsc=ifsc
        self.bank_name = bank_name
        self.district = district
        self.bank_branch=bank_branch
        self.timestamp = datetime.utcnow()
        self.name_status=name_status
        self.json_dump = json_dump
    
class DriverGLDrivLicDetails(db.Model):
    __tablename__ = 'grid_lines_drivlicdoc'
    id = db.Column('g_dl_id', db.Integer, primary_key=True)
    driver_id = db.Column('g_dl_driver_id', db.Integer)
    dl_no= db.Column('g_dl_no', db.String(50), unique=True)
    name = db.Column('g_driver_dl_name', db.Text)
    driver_pic = db.Column('g_driver_dl_pic', db.String)
    dob = db.Column('g_driver_dob', db.Date)
    addr = db.Column('g_driver_dl_addr', db.String)
    pincode = db.Column('g_driver_dl_pincode', db.String(10))
    license_exp = db.Column('g_driver_dl_license_exp', db.Date)
    state = db.Column('g_driver_dl_state', db.String(255))
    name_status = db.Column('g_driver_dl_name_status', db.Boolean)
    photo_status = db.Column('g_driver_dl_photo_status', db.Boolean)
    photo_score = db.Column('g_driver_dl_photo_score', db.Float)
    dob_status = db.Column('g_driver_dl_dob_status', db.Boolean)
    lic_exp_status = db.Column('g_driver_dl_licexp_status', db.Boolean)
    license_issue = db.Column('g_driver_dl_license_issue', db.Date)
    timestamp=db.Column('g_driver_dl_timestamp', db.DateTime, default=datetime.utcnow)
    json_dump = db.Column('g_driver_dl_json_dump', db.JSON)

    
    def __init__(self, driver_id,dl_no, name, driver_pic ,dob, addr, pincode, license_exp,state,license_issue,name_status=False,
                 photo_status=False,photo_score=0.0,dob_status=False,lic_exp_status=False,json_dump=None):
        self.driver_id = driver_id
        self.name = name
        self.dl_no=dl_no
        self.driver_pic=driver_pic
        self.dob=dob
        self.addr = addr
        self.pincode = pincode
        self.license_exp=license_exp
        self.state=state
        self.license_issue=license_issue
        self.name_status=name_status
        self.photo_status=photo_status
        self.photo_score=photo_score
        self.dob_status=dob_status
        self.lic_exp_status=lic_exp_status
        self.timestamp = datetime.utcnow()
        self.json_dump = json_dump
    
class Coupons(db.Model):
    LABEL_INACTIVE = 0
    LABEL_ACTIVE = 1

    __tablename__ = 'coupons'
    coupon_id = db.Column('coupon_id', db.Integer, primary_key=True)
    code = db.Column('coupon_code', db.String, unique=True)
    mobile = db.Column('coupon_mobile', db.Integer)
    percent_off = db.Column('coupon_percent_off', db.Float)
    max_off = db.Column('coupon_max_off', db.Float)
    flat_off = db.Column('coupon_flat_off', db.Float)
    min_trip_price = db.Column('coupon_min_trip_price', db.Float)
    min_trip = db.Column('coupon_min_trip', db.Integer)
    valid_till = db.Column('coupon_valid_till', db.Date)
    user_redeem_limit = db.Column('coupon_redeem_limit', db.Integer)
    
    state = db.Column('coupon_state', db.Integer)
    created_at = db.Column('coupon_created_at', db.DateTime)

    def __init__(self, code, min_trip_price, min_trip, valid_till,  mobile=0, percent_off=0, max_off=0, flat_off=0, redeem_limit=0):
        self.code = code
        self.mobile = mobile
        self.percent_off = percent_off
        self.max_off = max_off
        self.flat_off = flat_off
        self.min_trip_price = min_trip_price
        self.min_trip = min_trip
        self.valid_till = valid_till
        self.user_redeem_limit = redeem_limit
        self.state = 0
        self.created_at = datetime.utcnow()

class CouponCityMap(db.Model):

    __tablename__ = 'coupon_city_map'
    id = db.Column('ccm_id', db.Integer, primary_key=True)
    cid = db.Column('ccm_cid', db.Integer)
    coupon_city = db.Column('ccm_coupon_city', db.String)

    def __init__(self, couponId, couponCity):
        self.cid = couponId
        self.coupon_city = couponCity

class AppliedCoupon(db.Model):

    __tablename__ = 'applied_coupon'
    id = db.Column('ac_id', db.Integer, primary_key=True)
    user = db.Column('ac_user', db.Integer)
    code_id = db.Column('ac_code_id', db.Integer)
    code = db.Column('ac_code', db.String)
    timestamp = db.Column('ac_timestamp', db.DateTime)

    def __init__(self, code, user):
        self.code = code
        self.user = user
        self.timestamp = datetime.utcnow()

class EstimateRemarks(db.Model):
    __tablename__ = 'estimate_remarks'
    
    id = db.Column('id', db.Integer, primary_key=True, autoincrement=True)
    user_id = db.Column('user_id', db.Integer, nullable=False)
    remark = db.Column('remark', db.String(400), nullable=False)
    change_by_name = db.Column('change_by_name', db.String(50), nullable=False)
    created_at = db.Column('created_at', db.DateTime, nullable=False, default=datetime.utcnow)
    expiry_time = db.Column('expiry_time', db.DateTime, nullable=False)
    display = db.Column('display', db.Boolean, nullable=False)
    change_by_id = db.Column('change_by_id', db.Integer, nullable=False)

    def __init__(self, user_id, remark, change_by_name, expiry_time, display, change_by_id):
        self.user_id = user_id
        self.remark = remark
        self.change_by_name = change_by_name
        self.expiry_time = expiry_time
        self.display = display
        self.change_by_id = change_by_id

class BookingDetailsUpdate(db.Model):
    __tablename__ = 'booking_details_update'
    
    id = db.Column('bd_id', db.Integer, primary_key=True)
    book_id = db.Column('bd_book_id', db.Integer, nullable=False)
    changes = db.Column('bd_changes', db.String(50), nullable=False)
    status = db.Column('bd_status', db.Integer, nullable=True)
    edited_by = db.Column('bd_edited_by', db.Integer, nullable=False)
    editedby_name = db.Column('bd_editedby_name', db.String(50))
    change_from = db.Column('bd_from', db.String(500))
    change_to = db.Column('bd_to', db.String(500))
    remark = db.Column('bd_remark', db.String(500))
    timestamp = db.Column('bd_timestamp', db.DateTime, default=datetime.utcnow)

    def __init__(self, book_id, changes, edited_by, status=None, editedby_name=None, 
                 change_from=None, change_to=None, remark=None):
        self.book_id = book_id
        self.changes = changes
        self.edited_by = edited_by
        self.status = status
        self.editedby_name = editedby_name
        self.change_from = change_from
        self.change_to = change_to
        self.remark = remark
        self.timestamp = datetime.utcnow()

class AdminAccess(db.Model):
    __tablename__ = 'admin_access'
    
    admin_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    admin_user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    admin_tab_access = db.Column(db.BigInteger, nullable=False, default=0)  
    admin_regions_access = db.Column(db.BigInteger, nullable=False, default=0)
    admin_notification_access = db.Column(db.BigInteger, nullable=False, default=0)  
    calling_number = db.Column(db.String(15), nullable=True)  
    agent_id = db.Column(db.String(50), nullable=True) 
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    recent_edited_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = db.relationship('Users', backref='admin_access')

    def __init__(self, admin_user_id, admin_tab_access=0, admin_regions_access=0, admin_notification_access=0, calling_number=None, agent_id=None):
        self.admin_user_id = admin_user_id
        self.admin_tab_access = admin_tab_access
        self.admin_regions_access = admin_regions_access
        self.admin_notification_access = admin_notification_access
        self.calling_number = calling_number
        self.agent_id = agent_id


class AdminLogs(db.Model):
    __tablename__ = 'admin_logs'
    
    log_id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    admin_id = db.Column(db.Integer, db.ForeignKey('admin_access.admin_id'), nullable=False)
    admin_user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    action = db.Column(db.Integer, nullable=False)  # 0 for created, 1 for edited
    changed_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)  
    changed_by_name = db.Column(db.String(100), nullable=False) 
    created_at = db.Column(db.DateTime, default=datetime.utcnow) 
    oldvalue = db.Column(db.Text, nullable=True) 
    newvalue = db.Column(db.Text, nullable=True) 
    remark = db.Column(db.Text, nullable=True)
    change_type = db.Column(db.Integer, nullable=False)

    def __init__(self, admin_id, admin_user_id, action, changed_by, changed_by_name, change_type, oldvalue=None, newvalue=None, remark=None):
        self.admin_id = admin_id
        self.admin_user_id = admin_user_id
        self.action = action
        self.changed_by = changed_by
        self.changed_by_name = changed_by_name
        self.change_type = change_type
        self.oldvalue = oldvalue
        self.newvalue = newvalue
        self.remark = remark

class AdminCallLog(db.Model):
    __tablename__ = 'admin_call_log'
    id =  db.Column('acl_id', db.Integer, primary_key=True)
    uuid = db.Column('acl_uuid', db.String(40))
    dest_num = db.Column('acl_dest_num', db.String(20))
    did_num = db.Column('acl_did_num', db.String(20))  
    src_num = db.Column('acl_src_num', db.String(20))
    start_stamp = db.Column('acl_start_stamp', db.DateTime)
    answer_stamp = db.Column('acl_answer_stamp', db.DateTime)
    end_stamp = db.Column('acl_end_stamp', db.DateTime)
    hangup_cause = db.Column('acl_hangup_cause', db.String(50))
    duration = db.Column('acl_duration', db.Integer)
    call_status = db.Column('acl_call_status', db.String(20))
    call_connected = db.Column('acl_call_connected', db.Boolean)
    call_id = db.Column('acl_call_id', db.String(40))
    recording_url = db.Column('acl_recording_url', db.String(255))
    book_id = db.Column('acl_book_id', db.Integer)

    def __init__(self, call_id, book_id, uuid=None, dest_num=None, did_num=None, src_num=None, start_stamp=None, answer_stamp=None, end_stamp=None, 
                        hangup_cause=None, duration=None, call_status=None, call_connected=None, recording_url=None):
        self.call_id = call_id
        self.book_id = book_id
        self.uuid = uuid
        self.dest_num = dest_num
        self.did_num = did_num
        self.src_num = src_num
        self.start_stamp = start_stamp
        self.answer_stamp = answer_stamp
        self.end_stamp = end_stamp
        self.hangup_cause = hangup_cause
        self.duration = duration
        self.call_status = call_status
        self.call_connected = call_connected
        self.recording_url = recording_url

class Pricing(db.Model):
    __tablename__ = "pricing"
    id =  db.Column('price_id', db.Integer, primary_key=True)
    city = db.Column('price_city', db.String(30))
    trip_type = db.Column('price_trip_type', db.String(30))
    occasion = db.Column('price_occasion', db.JSON)
    night_fare = db.Column('price_night_fare', db.JSON)
    basic_fare = db.Column('price_basic_fare', db.JSON)
    car_fare = db.Column('price_car_fare', db.JSON)
    insurance_fare = db.Column('price_insurance_fare', db.JSON)
    variable_charges = db.Column('price_var_charges', db.JSON)
    static_charges = db.Column('price_static_charges', db.JSON)

    def __init__(self, city, trip_type, basic_fare, car_fare, night_fare=None, insurance_fare=None, 
                        occasion=None, variable_ch=None, static_ch=None):

        self.city = city
        self.trip_type = trip_type
        self.night_fare = night_fare
        self.basic_fare = basic_fare
        self.car_fare = car_fare
        self.insurance_fare = insurance_fare
        self.occasion = occasion
        self.variable_charges = variable_ch
        self.static_charges = static_ch
        
class UserRegistrationDetails(db.Model):
    predefined_sources = [
    "Friends/Relatives",
    "Google Search",
    "Play Store/App Store",
    "Facebook",
    "Instagram",
    "YouTube",
    "LinkedIn",
    "WhatsApp",
    "Founder/Office Employee",
    "No Parking Board",
    "Auto Advertisement",
    "Flyer",
    "Restaurants/Clubs",
    "Events",
    "Driver's T-Shirt",
    "Driver",
    "He is a Driver / Non Customer"
    ]
    __tablename__ = 'user_reg_details'
    id = db.Column('u_r_id', db.Integer, primary_key=True)
    user_id = db.Column('u_r_user_id', db.Integer,unique=True)
    lat = db.Column('u_r_lat', db.Float)
    lng = db.Column('u_r_lng', db.Float)
    city=db.Column('u_r_city',db.String)
    address=db.Column('u_r_address',db.Integer)
    source=db.Column('u_r_source',db.String(50))
    region = db.Column('u_r_region', db.Integer) 
    timestamp = db.Column('u_r_timestamp', db.DateTime)
    


class ScheduledReport(db.Model):
    __tablename__ = 'scheduled_report'

    # Enum constants
    REPORT_BOOKING = 1
    REPORT_CANCELLATION = 2

    STATUS_ACTIVE= 1
    STATUS_DRAFT = 2
    STATUS_CANCELLED = 3

    FREQUENCY_DAILY = 1
    FREQUENCY_WEEKLY = 2
    FREQUENCY_MONTHLY = 3

    DURATION_PREVIOUS_DAY = 1
    DURATION_THIS_WEEK = 2
    DURATION_THIS_MONTH = 3

    id = db.Column(db.Integer, primary_key=True)

    report_type = db.Column(db.Integer, nullable=False)        
    frequency = db.Column(db.Integer, nullable=False)           
    duration = db.Column(db.Integer, nullable=False)           


    email_list = db.Column(db.JSON, nullable=False)             
    regions= db.Column(db.JSON)                          
    affiliates = db.Column(db.JSON)                       

    subject = db.Column(db.String(255))
    message = db.Column(db.Text)
    status = db.Column(db.Integer, default=STATUS_ACTIVE)
    is_active = db.Column(db.Boolean, default=True)
    last_sent_at = db.Column(db.DateTime, default=None, nullable=True)
    last_sent_status = db.Column(db.Boolean, default=None, nullable=True)
    last_sent_error = db.Column(db.Text, default=None, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __init__(self, report_type, frequency, duration, email_list,
        subject=None, message=None, regions=None, affiliates=None, status=STATUS_ACTIVE,is_active=True):
        self.report_type = report_type
        self.frequency = frequency
        self.duration = duration
        self.email_list = email_list or []
        self.regions = regions or []
        self.affiliates = affiliates or []
        self.subject = subject
        self.message = message
        self.is_active=is_active
        self.status = status
        
class DriverStrike(db.Model):
    __tablename__ = 'driver_strikes'
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    driver_id = db.Column(db.Integer, db.ForeignKey('drivers.driver_id'), nullable=False)
    reason = db.Column(db.Integer, db.ForeignKey('strike_reasons.id'), nullable=False)
    strike_weight = db.Column(db.Integer, default=1, nullable=False)
    cool_down_period = db.Column(db.Integer, nullable=False)  # in days
    fine = db.Column(db.Float, default=0.0)    
    remarks = db.Column(db.Text)
    book_id= db.Column(db.Integer,db.ForeignKey('bookings.book_ref'),nullable=True)
    resolved = db.Column(db.Boolean, default=False)
    strike_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    resolved_by= db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    starts_at = db.Column(db.DateTime, nullable=False)  
    expires_at = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    resolved_at = db.Column(db.DateTime, nullable=True)
    strike_reason = db.relationship('StrikeReason', backref='strikes', lazy='joined')
    driver = db.relationship('Drivers', foreign_keys=[driver_id], backref='strikes', lazy='joined')
    def __init__(self, driver_id, reason, remarks,starts_at,expires_at,strike_by,strike_weight,cool_down_period,fine,book_id=None,resolved_by=None,resolved_at=None,resolved=False):
        self.book_id = book_id
        self.driver_id = driver_id
        self.reason = reason
        self.remarks = remarks 
        self.expires_at = expires_at 
        self.starts_at = starts_at
        self.strike_by = strike_by
        self.resolved_by = resolved_by
        self.created_at = datetime.utcnow()
        self.resolved_at = resolved_at
        self.resolved = resolved
        self.strike_weight = strike_weight
        self.cool_down_period = cool_down_period
        self.fine = fine

class StrikeReason(db.Model):
    __tablename__ = 'strike_reasons'
    STRIKE_THRESHOLD = 5  
    STRIKE_MIN = 1
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    brief = db.Column(db.String(100), nullable=False)
    active = db.Column(db.Boolean, default=True)
    strike_weight = db.Column(db.Integer, default=1, nullable=False)
    cool_down_period = db.Column(db.Integer, nullable=False)  # in days
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    fine = db.Column(db.Float, default=0.0)
    created_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)

    def to_dict(self):
        return {
            "id": self.id,
            "brief": self.brief,
            "strike_weight": self.strike_weight,
            "cool_down_period": self.cool_down_period,
            "fine": self.fine,
        } 
        
class StrikeReasonLog(db.Model):
    __tablename__ = 'strike_reason_log'
    CREATED = 0
    EDITED = 1
    ENABLED = 2
    DISABLED = 3
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    reason_id = db.Column(db.Integer, db.ForeignKey('strike_reasons.id'), nullable=False)
    changes = db.Column(db.String(50), nullable=True)
    action_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)
    change_from = db.Column(db.String(50), nullable=True)
    change_to = db.Column(db.String(50), nullable=True)
    remark = db.Column(db.String(100), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    action_type = db.Column(db.Integer, nullable=False)  # 0 for created, 1 for edited
    def __init__(self, reason_id, changes, action_by, change_from, change_to,action_type,remark=None):
        self.reason_id = reason_id
        self.changes = changes
        self.action_by = action_by
        self.change_from = change_from
        self.change_to = change_to
        self.remark = remark
        self.action_type=action_type
        self.timestamp = datetime.utcnow()
        
class DriverSelfies(db.Model):
    COMPANY_TSHIRT = 1
    NOT_COMPANY_TSHIRT = -1
    NOT_A_PERSON = -2
    
    COLOR_YELLOW = 1
    COLOR_BLUE = 2
    
    LABELLED = 2
    UNLABELLED = 1
    ALL = 0
    
    __tablename__ = 'driver_selfies'
    id = db.Column('ds_id', db.Integer, primary_key=True)
    driver_id = db.Column('ds_driver_id', db.Integer, nullable=False)
    labeled_by = db.Column('ds_labeled_by', db.Integer, nullable=False)
    selfie = db.Column('ds_selfie', db.String(255), nullable=False)
    created_at = db.Column('ds_created_at', db.DateTime, default=datetime.utcnow)
    book_id = db.Column('ds_book_id', db.Integer, nullable=False)
    img_main_class= db.Column('ds_img_main_class', db.Integer, nullable=False)
    img_color_class = db.Column('ds_img_color_class', db.Integer, nullable=True,default=None)
    fine_waived=db.Column('ds_fine_waived', db.Boolean)
    strike_id = db.Column('ds_strike_id', db.Integer, nullable=True) 
    def __init__(self, driver_id, selfie,fine_waived, book_id, img_main_class, img_color_class, labeled_by,strike_id=None):
        self.driver_id = driver_id
        self.selfie = selfie
        self.fine_waived = fine_waived
        self.book_id = book_id
        self.img_main_class = img_main_class
        self.img_color_class = img_color_class
        self.labeled_by = labeled_by
        self.timestamp = datetime.utcnow()
        self.strike_id = strike_id
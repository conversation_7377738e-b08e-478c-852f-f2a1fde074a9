tags:
  - Affiliate
summary: Delete an Address for an Affiliate Representative
description: >
  Deletes an address entry for an affiliate representative.
parameters:
  - in: formData
    name: address_id
    type: integer
    required: true
    description: The ID of the address to delete.
    example: 1
  - in: formData
    name: regions
    type: string
    required: true
    description: >
      Comma-separated region codes that the representative should have access to.
    example: "0,1"
responses:
  200:
    description: Address deleted successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Address deleted successfully"
  404:
    description: Address not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Address not found"
  500:
    description: Internal Server Error.
    schema:
      type: object
      properties:
        error:
          type: string
          example: "Detailed error message"

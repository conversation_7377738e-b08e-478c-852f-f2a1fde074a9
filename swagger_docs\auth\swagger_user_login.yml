tags:
  - Authentication
summary: User login with mobile number and password or OTP
description: Authenticates a user using their mobile number and password or OTP, and provides access and refresh tokens if valid.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user
  - name: pwd
    in: formData
    type: string
    required: true
    description: Password or OTP of the user
  - name: type
    in: formData
    type: string
    required: false
    description: Indicates if OTP or password is used for login
  - name: remember
    in: formData
    type: boolean
    required: false
    description: Whether to remember the user for a long time (affects token expiry)
responses:
  200:
    description: Successful login
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        user_id:
          type: integer
          example: 12345
        user_fname:
          type: string
          example: "John"
        user_lname:
          type: string
          example: "Doe"
        user_mobile:
          type: string
          example: "9876543210"
        user_email:
          type: string
          example: "<EMAIL>"
        user_restore_id:
          type: string
          example: "restore_12345"
        user_ref_code:
          type: string
          example: "REF12345"
        access_token:
          type: string
          example: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
        csrf:
          type: string
          example: "csrf_token_here"
    examples:
      application/json:
        success: 1
        user_id: 12345
        user_fname: "John"
        user_lname: "Doe"
        user_mobile: "9876543210"
        user_email: "<EMAIL>"
        user_restore_id: "restore_12345"
        user_ref_code: "REF12345"
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
        csrf: "csrf_token_here"
  400:
    description: Incomplete form data
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Incomplete form"
    examples:
      application/json:
        success: 0
        message: "Incomplete form"
  401_a:
    description: OTP or password not validated
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "OTP not validated"
    examples:
      application/json:
        success: 0
        message: "OTP not validated"
  401_b:
    description: User restricted from logging in
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: 0
        message: "User restricted"
  403:
    description: User not allowed to login with the current role
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: 0
        message: "User restricted"
  500:
    description: Server error occurred while logging in
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -5
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -5
        message: "DB Error"

tags:
  - Customer_admin
summary: Get User Logs
description: >
  This endpoint allows admins to retrieve logs for user and driver accounts,
  including deleted users and contact number changes. It supports filtering by 
  account type, deletion status, contact change status, date range, and search 
  terms (name or phone number). Results are paginated.
  Only accessible to users with Customers tab access.
parameters:
  - name: account_type
    in: formData
    required: false
    type: string
    enum: ['0', '1']
    description: Filter by account type - Customer (0) or Driver (1)
    example: "0"
  - name: deleted
    in: formData
    required: false
    type: string
    enum: ['1']
    description: Set to '1' to retrieve deleted user records
    example: "1"
  - name: contact_changed
    in: formData
    required: false
    type: string
    enum: ['1']
    description: Set to '1' to retrieve records with contact number changes
    example: "1"
  - name: start_date
    in: formData
    required: false
    type: string
    format: date
    description: Start date for filtering records (Format YYYY-MM-DD)
    example: "2023-01-01"
  - name: end_date
    in: formData
    required: false
    type: string
    format: date
    description: End date for filtering records (Format YYYY-MM-DD)
    example: "2023-12-31"
  - name: search_name
    in: formData
    required: false
    type: string
    description: Search term for filtering by name or phone number
    example: "John"
  - name: search_mode
    in: formData
    required: false
    type: string
    enum: ['0', '1']
    default: '0'
    description: Search mode - by name (0) or by phone number (1)
    example: "0"
  - name: page
    in: formData
    required: false
    type: integer
    default: 1
    description: Page number for pagination
    example: 1
  - name: per_page
    in: formData
    required: false
    type: integer
    default: 20
    description: Number of records per page
    example: 20
responses:
  200:
    description: Successfully retrieved user logs
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: array
          description: List of user log records
          items:
            type: object
            properties:
              date_time:
                type: string
                description: Formatted date and time of the log entry
                example: "01 Jan 2023, 12:30:45"
              account_type:
                type: string
                description: Type of account (Customer or Driver)
                example: "Customer"
              name:
                type: string
                description: Full name of the user
                example: "John Doe"
              status:
                type: string
                description: Status of the log entry (Deleted or Changed)
                example: "Deleted"
              contact_after:
                type: string
                description: New contact number (for contact changed logs)
                example: "**********"
              old_contact_number:
                type: string
                description: Original contact number (for contact changed logs)
                example: "**********"
              user_id:
                type: integer
                description: Unique identifier of the user
                example: 123
              customer_remark:
                type: string
                description: Additional remarks about the customer
                example: "Account deleted at customer request"
        total_count:
          type: integer
          description: Total number of records matching the search criteria
          example: 50
       
  400:
    description: Bad request due to invalid parameters
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Error message describing the invalid parameter
          example: "Invalid account type selected. (Valid Account types- 0 for Customer, 1 for Driver)"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        message:
          type: string
          description: Error message describing the issue
          example: "An internal server error occurred"
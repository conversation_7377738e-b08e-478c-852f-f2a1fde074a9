tags:
  - Booking_admin
summary: Get driver list for booking allocation
description: >
  This endpoint provides a list of drivers available for booking allocation, including filtering and sorting options.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: search_query
    in: formData
    type: string
    required: false
    description: Search term to filter drivers by name, mobile, or ID.
  - name: filter_by
    in: formData
    type: string
    required: false
    description: Filter drivers by availability (1 for available, 2 for not available).
  - name: sort_by
    in: formData
    type: string
    required: false
    description: Sort drivers by criteria (e.g., 1 for ascending by ID, 3 for descending by score).
responses:
  200:
    description: List of drivers available for booking allocation.
    schema:
      type: object
      properties:
            success:
              type: integer
              example: 1
            data:
              type: array
              items:
                type: object
                properties:
                  driver_id:
                    type: integer
                    example: 123
                  driver_name:
                    type: string
                    example: John Doe
                  driver_mobile:
                    type: string
                    example: "+1234567890"
                  driver_score:
                    type: number
                    format: float
                    example: 4.5
                  driver_rating:
                    type: number
                    format: float
                    example: 4.2
                  driver_trip_count:
                    type: integer
                    example: 50
  500:
    description: Internal server error
    schema:
      type: object
      properties:
            success:
              type: integer
              example: -3

tags:
  - Booking_admin
summary: Calculate Cancellation Charge
description: API to calculate the cancellation charge for a specific booking based on the provided reason. Handles cases for drivers, on-trip status, and waiver rules.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking for which the cancellation charge is to be calculated.
  - name: reason
    in: formData
    type: integer
    required: true
    description: The reason for the cancellation, used to determine penalty/waiver.
responses:
  200:
    description: Success response indicating the cancellation charge was calculated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        charge:
          type: array
          items:
            type: integer
          example: [100, 50]  # Example penalty values for user and driver
  400:
    description: Bad request due to missing or incorrect data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: 'Invalid booking or reason provided'
  500:
    description: Internal server error indicating a failure while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: 'Internal Server Error'
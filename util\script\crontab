# Edit this file to introduce tasks to be run by cron.
# 
# Each task to run has to be defined through a single line
# indicating with different fields when the task will be run
# and what command to run for the task
# 
# To define the time you can provide concrete values for 
# minute (m), hour (h), day of month (dom), month (mon), 
# and day of week (dow) or use '*' in these fields (for 'any'). 
#  
# Notice that tasks will be started based on the cron's system 
# daemon's notion of time and timezones. 
#  
# Output of the crontab jobs (including errors) is sent through 
# email to the user the crontab file belongs to (unless redirected). 
# 
# For example, you can run a backup of all your user accounts
# at 5 a.m every week with:
# 0 5 * * 1 tar -zcf /var/backups/home.tgz /home/
# 
# For more information see the manual pages of crontab(5) and cron(8)
# 
# m h  dom mon dow   command
PYTHONPATH=/app
30 3 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/b2c_info_mail.sh) >> /app/util/log/b2c_info_mail.log 2>&1 #B2C Booking Daily Mail
30 * * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/delete_stale_pending.sh) >> /app/util/log/delete_stale_pending.log 2>&1 #Delete Pending Entries
30 4 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/due_deduction_admin.sh) >> /app/util/log/due_deduction_admin.log 2>&1 #Send Admin Due Deduction History Daily
30 4 5 * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/transaction_monthly.sh) >> /app/util/log/transaction_monthly.log 2>&1 #Send Monthly Data of Accounts
30 3 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/new_driver.sh) >> /app/util/log/new_driver.log 2>&1 #Send Registered Driver Data Citywise
30 4 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/pay_driver_bonus_weekly.sh) >> /app/util/log/pay_driver_bonus_weekly.log 2>&1 #Weekly Driver Bonus Script
30 4 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/pay_driver_bonus.sh) >> /app/util/log/pay_driver_bonus.log 2>&1 #Daily Driver Bonus Script
30 3 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/b2b_info_mail.sh) >> /app/util/log/b2b_info_mail.log 2>&1 #B2B Booking Daily Mail
30 4 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/update_pending_due.sh) >> /app/util/log/update_pending_due.log 2>&1 #Due Clear Mail
0 */6 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/firebase_stale_entry_delete.sh) >> /app/util/log/firebase_stale_entry_delete.log 2>&1 #Due Clear Mail
30 4 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/scheduled_report_runner.sh) >> /app/util/log/test.log 2>&1
30 18 * * * (TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/affiliate_balance_alert.sh) >> /app/util/log/affiliate_balance_alert.log 2>&1
0 */6 * * *(TZ='Asia/Kolkata' date "+\%Y-\%m-\%d \%H:\%M:\%S"; /bin/bash /app/util/script/driver_strike_resolution.sh) >> /app/util/log/driver_strike_resolution.log 2>&1


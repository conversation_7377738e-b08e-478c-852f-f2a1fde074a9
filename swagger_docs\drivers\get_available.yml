tags:
  - Driver
summary: Get Driver Availability
description: >
  This API fetches the availability status of a driver. Only drivers are authorized to use this endpoint.
parameters:
  - name: "Authorization"
    in: "header"
    description: "JWT token for authentication"
    required: true
    type: "string"
    example: "Bearer <JWT token>"
  - name: "Content-Type"
    in: "header"
    description: "Content-Type must be set to application/x-www-form-urlencoded"
    required: true
    type: "string"
    example: "application/x-www-form-urlencoded"
responses:
  200:
    description: Availability status successfully fetched.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for successful fetch)
          example: 1
        available:
          type: boolean
          description: Driver's availability status (True or False)
          example: true
        message:
          type: string
          description: Success message
          example: "Fetched available status"
    examples:
      application/json:
        success: 1
        available: true
        message: "Fetched available status"
  401_a:
    description: Failed to get identity.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failed identity)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User account is restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  200_b:
    description: Unauthorized role, Not a Driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for unauthorized role)
          example: -2
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -2
        message: "Unauthorized role: not Driver"
  201:
    description: Server error during execution.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for server error)
          example: -3
        message:
          type: string
          description: Error message
          example: "Server error"
    examples:
      application/json:
        success: -3
        message: "Server error"

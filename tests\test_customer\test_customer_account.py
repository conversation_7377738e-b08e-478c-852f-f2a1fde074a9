from models import Users, Bookings, DriverSearch,db, Drivers
from conftest import create_user_and_driver,unique_user_data
from datetime import datetime,timedelta

def test_change_name_success(client, customer_login):
    auth_headers,data = customer_login()
    change_name_data = {
        'fname': '<PERSON>',
        'lname': 'Doe'
    }

    response = client.post('/api/profile/change_name', data=change_name_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'success' in res_data
    assert res_data['success'] == 1


def test_change_email_success(client, customer_login):
    auth_headers,data = customer_login()
    change_email_data = {
        'email': '<EMAIL>'
    }

    response = client.post('/api/profile/change_email', data=change_email_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'success' in res_data
    assert res_data['success'] == 1  # Email successfully changed

def test_change_pwd_wrong_old_password(client, customer_login):
    auth_headers,data = customer_login()
    change_pwd_data = {
        'oldpwd': 'oldpassword123',
        'newpwd': 'newpassword456'
    }

    response = client.post('/api/profile/change_pwd', data=change_pwd_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'success' in res_data
    assert res_data['success'] == 0  # Old password is wrong


def test_change_pwd_success(client, customer_login):
    auth_headers,data = customer_login()
    change_pwd_data = {
        'oldpwd': 'password',
        'newpwd': 'newpassword456'
    }

    response = client.post('/api/profile/change_pwd', data=change_pwd_data, headers=auth_headers)
    assert response.status_code == 200
    res_data = response.get_json()

    assert 'success' in res_data
    assert res_data['success'] == 1  # Password successfully changed

def test_get_restore_id_success(client, customer_login):
    auth_headers,user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    # Assuming the customer_login creates a user with restore_id
    user = Users.query.filter_by(mobile=user.mobile).first()
    user.restore_id = 'valid_restore_id'
    db.session.commit()

    response = client.post('/api/user/restore_id/get', headers=auth_headers)
    
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'success' in res_data
    assert res_data['success'] == 1
    assert 'restore_id' in res_data
    assert res_data['restore_id'] == 'valid_restore_id'

def test_set_restore_id_success(client, customer_login):
    auth_headers, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    user_mobile = user.mobile
    restore_data = {
        'restore_id': 'new_restore_id'
    }

    # Set new restore ID via API
    response = client.post('/api/user/restore_id/set', data=restore_data, headers=auth_headers)

    assert response.status_code == 200
    res_data = response.get_json()

    assert 'success' in res_data
    assert res_data['success'] == 1
    assert 'restore_id' in res_data
    assert res_data['restore_id'] == 'new_restore_id'

    # Re-query the user to check the update in the database
    db_user = Users.query.filter_by(mobile=user_mobile).first()

    # Verify that the database has been updated
    assert db_user.restore_id == 'new_restore_id'



def test_user_rate_success(client,customer_login):
    auth_headers,user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    data = unique_user_data()
    driver_user,driver =create_user_and_driver(data)
    assert driver is not None, "Driver creation failed."

    booking=Bookings(
        user=user.id,
        skey='some_secret_key',
        driver= driver.id,
        lat=0.0,
        long=0.0,
        starttime=datetime.utcnow().strftime("%H:%M:%S"),
        startdate=datetime.utcnow().strftime("%Y-%m-%d"),
        dur=datetime.utcnow().strftime("%H:%M:%S"),
        endtime=(datetime.utcnow() + timedelta(minutes=60)).strftime("%H:%M:%S"),
        enddate=datetime.utcnow().strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
    booking.valid=1
    db.session.add(booking)
    db.session.commit()

    booking_id=booking.id
    rate_data = {
        'book_id': booking_id,
        'user_rating': 5,
        'comment': 'Great ride!'
    }

    response = client.post('/api/trip/user_rate', data=rate_data, headers= auth_headers)
    print("Response:",response.get_json())
    assert response.status_code == 200
    res_data = response.get_json()
    
    assert 'success' in res_data
    assert res_data['success'] == 1
    
    # Verify the database was updated with the correct rating
    updated_booking = Bookings.query.filter_by(id=booking_id).first()
    assert updated_booking.user_rating == 5


$(document).ready(function () {
    //focus to phone number on load
    $("#username").focus();
	//Password visibility functionality.................................................................
    $(".input-group-addon").click(function() {
        var toggle = $(this).find("i").attr('class');
        if(toggle=="glyphicon glyphicon-eye-open") {
            $(this).parent().find("input").attr('type','text');
            $(this).find("i").attr('class','glyphicon glyphicon-eye-close');
        }
        else {
            $(this).parent().find("input").attr('type','password');
            $(this).find("i").attr('class','glyphicon glyphicon-eye-open');
        }
    });

    //enter key triggers submit
    $("#username").keyup(function(e) {
        if(e.which == 13) $("#login").trigger('click');
    });
    $("#pwd").keyup(function(e) {
        if(e.which == 13) $("#login").trigger('click');
    });
    //Login form Submit click....................................................................................
    $("#login").click(function (e) {
        $("#username").focus();
    	e.preventDefault();
    	var username = $("#username").val().trim();
    	var password = $("#pwd").val();
    	var validationFieldList = [];
    	var validData = true;

    	//username validation
    	if (isNullOrEmpty(username)) {
          	$("#username").addClass("input-error");			//error indication
          	validData = false;
          	validationFieldList.push("Username - Empty");
      	}

        if (isNullOrEmpty(password)) {
            $("#pwd").addClass("input-error");			//error indication
            validData = false;
          	validationFieldList.push("Password - Empty");
      	}

        //send login data
        if (validData) {

            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/token/login/cardekho',
                data: {
                    username: username,
                    pwd: password,
                    remember: 'true'
                },
                dataType: "json",
                success: function(e) {
                    var msg = JSON.stringify(e);
                    if (e.success == 1)
                    {
                    	setCookie("name", e.user_fname + " " + e.user_lname, "username", e.user_uname, 365, e.region);
                       	window.location = window.location.protocol + '//' + window.location.host + '/affiliate/cardekho';
                    }
                    else {
                        openInformationModal("Information", "Login Failed! Incorrect Credentials", "danger");
                    }

                },
                error: function(e) {
                    openInformationModal("Information", "Login Failed! Incorrect Credentials", "danger");
                }
            });
        }
        else {
            var validationList = getValidationMessageListHtml(validationFieldList);
            openInformationModal("Information", "<p>The following issues were found with some of the fields:</p>" + validationList, "danger");
        }
    });

    //cookie related code...............................................................................
    function getCookie() {
        cookieSplit = document.cookie.split("; ");
        var cookieObject = {};
        cookieSplit.forEach( function(value, index) {
           var splitResult = value.split("=");
           cookieObject[splitResult[0]] = splitResult[1];
        });
        return cookieObject;
    }
    function setCookie(propName,nameVal,propPhone,phoneVal,exDays,region) {
        if(typeof exDays === "undefined") exDays = 0;
        var d = new Date();
        d.setDate(d.getDate() + parseInt(exDays));
        var expires = "expires=" + d;
        document.cookie = propName + "=" + nameVal;
        document.cookie = propPhone + "=" + phoneVal;
        document.cookie = expires;
        document.cookie = "path=/";
        document.cookie = "region=" + region  + "; " + expires;
    }
    var cookie = getCookie();
    var refresh = cookie['csrf_refresh_token'];
    var access = cookie['csrf_access_token'];
    if (access && refresh) {
        var ans = true;
        if (ans) {
            window.location = window.location.protocol + '//' + window.location.host + '/affiliate/cardekho';
        }
    }

});

function getInformationModalHtml() {
    return (    '<div id="InformationModal" class="modal fade" role="dialog">'+
                    '<div class="modal-dialog">'+
                        '<div class="modal-content">'+
                            '<div class="modal-header" data-dismiss="modal">'+
                                '<p class="modal-title"><i class="fa fa-info-circle"></i>&emsp;Information</p>'+
                            '</div>'+
                            '<div class="modal-body">'+
                            '</div>'+
                            '<div class="modal-footer">'+
                                '<button type="button" class="btn close-button" data-dismiss="modal">Close&emsp;X</button>'+
                            '</div>'+
                        '</div>'+
                    '</div>'+
                '</div>');
}

function getInformationModalContent(message, color) {
    $("#InformationModal").find(".modal-header").removeClass("d4m-element-primary d4m-element-danger").addClass("d4m-element-" + color);
    $("#InformationModal").find(".modal-footer").removeClass("d4m-element-primary d4m-element-danger").addClass("d4m-element-" + color);
    $("#InformationModal").find(".modal-body").html(message);
}
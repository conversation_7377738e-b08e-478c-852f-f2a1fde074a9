from export_csv import export_csv, run_query
import pandas as pd
import gmplot
import sys
import configparser

if __name__ == '__main__':
    try:
        loc = sys.argv[1]
    except Exception:
        loc = "output/b2c-bloc.html"
    config_parser = configparser.ConfigParser()
    try:
        cfg = sys.argv[2]
    except Exception:
        cfg = 'configs/user_loc.ini'
    config_parser.read(cfg, encoding='utf8')
    filename = config_parser.get('general', 'filename')
    query = config_parser.get('general', 'query')
    records = run_query(query)
    export_csv(records, filename)

    df = pd.read_csv("output/user_loc.csv")
    df.columns = ['Latitude', 'Longitude']
    df = df.groupby(['Latitude', 'Longitude']).size().to_frame(name = 'weight').reset_index()
    gmap = gmplot.GoogleMapPlotter(17.4020637, 78.4840052, 10)
    gmap.apikey = 'AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo'
    gmap.heatmap(df['Latitude'], df['Longitude'], radius=20)
    gmap.scatter(df['Latitude'], df['Longitude'], '#3B0B39', c='r', size=250, marker=True)
    gmap.draw(loc)

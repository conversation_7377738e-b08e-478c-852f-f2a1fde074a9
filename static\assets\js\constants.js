//booking visibility types
const bookingVisibility = {
    0: "ALLOCATED",
    1: "BROADCAST",
    2: "SUPPRESSED"
};

/* BOOKING CONSTANTS */

const minBookingThreshold = 1 * 60 * 60 * 1000; // in hours
const NIGHT_START = "10:29 P.M.";
const NIGHT_END = "06:29 A.M.";

/* REGEX */
const phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");
const fnameRegex = new RegExp("^[a-zA-Z]");
const lnameRegex = new RegExp("^[a-zA-Z]");

/* LOGIN OR REGISTRATION CONSTANTS */
const minPasswordLength = 6;
const OTPLength = 4;

const regions = {
    0: "Kolkata",
    1: "Hyderabad",
    2: "Guwahati",
    3: "Nagpur",
    4: "Pune",
    5: "Mumbai",
    6: "Delhi",
    7: "Chennai",
    8: "Bangalore",
    9: "Ahmedabad",
    10: "<PERSON><PERSON><PERSON><PERSON>",
    11: "Bhubaneswar",
    12: "Patna",
    13: "Ranchi",
    14: "Gurgaon",
    15: "Noida",
    16: "Jaipur",
    17: "Lucknow",
    18: "Chandigarh",
    19: "Alampur"
};

//trip types
const tripTypes = {
    0: "New Trip Type?",
    1: "In City Round Trip",
    2: "Outstation Round Trip",
    3: "In City One Way",
    4: "Mini Outstation Round Trip",
    5: "Outstation Oneway",
    6: "Mini Outstation Oneway",
    50: "Cars24 Trip",
    51: "Revv Duty Shift",
    52: "Zoomcar Trip",
    53: "Gujral Rental Duty",
    54: "OLX Trip",
    55: "Cardekho Trip",
    56: "Bhandari Trip",
    57: "Mahindra Trip",
    58: "RevvV2 Trip",
    59: "Mahindra Trip",
    500: "Cars24 Home Delivery",
    501: "Cars24 Pickup",
    520: "Home Delivery (HD)",
    521: "Collection from CX",
    540: "OLX Home Delivery",
    541: "OLX Pickup",
    550: "Cardekho Home Delivery",
    551: "Cardekho Pickup",
    560: "Bhandari Home Delivery",
    561: "Bhandari Pickup",
    570: "Mahindra Home Delivery",
    571: "Mahindra Pickup",
    580: "RevvV2 Home Delivery",
    581: "RevvV2 Pickup",
    590: "Spinny Home Delivery",
    591: "Spinny Pickup",
    600: "Pride Honda Home Delivery",
    601: "Pride Honda Pickup"

};

//trip types with destination
const destTripTypes = [2, 3, 4, 5, 6, 50, 52, 54, 55, 56, 500, 501, 521, 520, 540, 541, 550, 551, 560, 561,
                       57, 570, 571, 58, 580, 581, 59, 590, 591, 60, 600, 601];

const C24_BOOKING = [50, 500, 501];

const REVV_BOOKING = 51;

const ZOOM_BOOKING = [52, 520, 521];

const GUJRAL_BOOKING = 53;

const OLX_BOOKING = [54, 540, 541];

const CARDEKHO_BOOKING = [55, 550, 551];

const BHANDARI_BOOKING = [56, 560, 561];

const MAHINDRA_BOOKING = [57, 570, 571];

const SPINNY_BOOKING = [59, 590, 591];

const REVV_V2_BOOKING = [58, 580, 581];

const PRIDEHONDA_BOOKING = [60, 600, 601];

//B2B trip types
const b2bTripTypes = {
    DELIVERY: 0,
    PICKUP: 1,
    BOTH: 2
};

//Zoomcar Hub locations
const ZOOMCAR_HUBS = {
    "Zoomcar Alipore Hub":
    {
        "latitude":  22.5065017,
        "longitude": 88.3118099,
        "status": "200"
    },
    "Zoomcar Hiland Park Hub":
    {
        "latitude":  22.486534,
        "longitude": 88.3868783,
        "status": "200"
    },
    "Zoomcar Kaikhali Hub":
    {
        "latitude":  22.627738,
        "longitude": 88.4331766,
        "status": "200"
    }
};

//Zoomcar trip type texts
const ZOOMCAR_TRIP_TYPE_DISPLAY = {
    0: "Home Delivery (HD)",
    1: "Customer (CX) Pickup"
};

//List of Cities
const CITY = {
    "KOL": "Kolkata",
    "BHP": "Behrampore"
};

//Branch list per city
const BRANCH = {
    "KOL" : {
        "SLS5": "Sector V",
        "RUBY": "Ruby"
    },
    "BHP" : {
        "LLD": "Laldighi (H.O.)"
    }
};

//Branch Addresses
const BRANCH_ADDRESS = {
    "KOL-SLS5" : "Ashram Building, Ground floor,<br>" + "Salt Lake Electronics Complex,<br>" + "34/2 GN Block, Sector V,<br>" + "Kolkata-700091,<br>" + "West Bengal." ,
    "KOL-RUBY" : "GF-17 Ground floor,<br>" + "Rajdanga Main Road,<br>" + "Ruby Crossing, near Gateway Hotel,<br>" + "EKTP Phase 3, Sector G,<br>" + "Kolkata-700107,<br>" + "West Bengal",
    "BHP-LLD"  : "B/2 Laldighi Apartment,<br>" + "69 R.N. Tagore Road,<br>" + "Behrampore,<br>" + "Murshidabad - 742101,<br>" + "West Bengal."
};

//log action types
const logActionTypes = {
    0: "Allocated",
    1: "Cancelled",
    2: "Booked",
    3: "Trip start",
    4: "Trip stop",
    5: "Trip restart",
    6: "Trip new stop",
    7: "Trip start pic upload",
    8: "Trip stop pic upload",
    9: "Trip start time change",
    12: "On the way",
    13: "Checkin",
    14: "Reached destination",
    "unknown": "Unknown",
};

const cancelTypes = {
    0: "User",
    1: "Driver",
    2: "Admin",
};

/* D4M Default Map Themes */
const D4M_DEFAULT_MAP_THEME = [
    {
    "featureType": "administrative",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#d6e2e6"
      }
    ]
    },
    {
    "featureType": "administrative",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
    },
    {
    "featureType": "administrative",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#7492a8"
      }
    ]
    },
    {
    "featureType": "administrative.neighborhood",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "lightness": 25
      }
    ]
    },
    {
    "featureType": "administrative.land_parcel",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
    },
    {
    "featureType": "landscape.man_made",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#d6e2e6"
      }
    ]
    },
    {
    "featureType": "landscape.man_made",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
    },
    {
    "featureType": "landscape.natural",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#dae6eb"
      }
    ]
    },
    {
    "featureType": "landscape.natural",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#7492a8"
      }
    ]
    },
    {
    "featureType": "landscape.natural.terrain",
    "elementType": "all",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
    },
    {
    "featureType": "poi",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#d6e2e6"
      }
    ]
    },
    {
    "featureType": "poi",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#588ca4"
      }
    ]
    },
    {
    "featureType": "poi",
    "elementType": "labels.icon",
    "stylers": [
      {
        "saturation": -100
      }
    ]
    },
    {
    "featureType": "poi.park",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#cae7a8"
      }
    ]
    },
    {
    "featureType": "poi.park",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#bae6a1"
      }
    ]
    },
    {
    "featureType": "poi.sports_complex",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#c6e8b3"
      }
    ]
    },
    {
    "featureType": "poi.sports_complex",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#bae6a1"
      }
    ]
    },
    {
    "featureType": "road",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#41626b"
      }
    ]
    },
    {
    "featureType": "road",
    "elementType": "labels.icon",
    "stylers": [
      {
        "saturation": -45
      },
      {
        "lightness": 10
      },
      {
        "visibility": "off"
      }
    ]
    },
    {
    "featureType": "road.highway",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#f7fdff"
      }
    ]
    },
    {
    "featureType": "road.highway",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#beced4"
      }
    ]
    },
    {
    "featureType": "road.arterial",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#eef3f5"
      }
    ]
    },
    {
    "featureType": "road.arterial",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
    },
    {
    "featureType": "road.local",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#edf3f5"
      }
    ]
    },
    {
    "featureType": "road.local",
    "elementType": "geometry.stroke",
    "stylers": [
      {
        "color": "#cddbe0"
      }
    ]
    },
    {
    "featureType": "road.local",
    "elementType": "labels",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
    },
    {
    "featureType": "transit",
    "elementType": "labels.icon",
    "stylers": [
      {
        "saturation": -70
      }
    ]
    },
    {
    "featureType": "transit.line",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#588ca4"
      }
    ]
    },
    {
    "featureType": "transit.station",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#008cb5"
      }
    ]
    },
    {
    "featureType": "transit.station.airport",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "saturation": -100
      },
      {
        "lightness": -5
      }
    ]
    },
    {
    "featureType": "water",
    "elementType": "geometry.fill",
    "stylers": [
      {
        "color": "#a6cbe3"
      }
    ]
    }
    ]
    
tags:
  - Admin Analytics
summary: Get Total Customer Registration Count by Source
description: |
  This endpoint retrieves the total count of customer registrations, grouped by their registration source (e.g., predefined sources or others). 
  The data can be filtered by a specific date range and region.
parameters:
  - name: from_date
    in: formData
    type: string
    format: date
    required: true
    description: "Start date for the registration filter (YYYY-MM-DD)."
    example: "2024-01-01"
  - name: to_date
    in: formData
    type: string
    format: date
    required: true
    description: "End date for the registration filter (YYYY-MM-DD)."
    example: "2024-01-31"
  - name: from_time
    in: formData
    type: string
    required: false
    description: "Start time for the registration filter (HH:MM:SS). Defaults to '00:00:00'."
    example: "00:00:00"
  - name: to_time
    in: formData
    type: string
    required: false
    description: "End time for the registration filter (HH:MM:SS). Defaults to '23:59:59'."
    example: "23:59:59"
  - name: search_region
    in: formData
    type: string
    required: true
    description: >
      A region filter for customer registrations.
      Provide a region ID or use '-1' to include all regions.
    example: "1,2,3"
responses:
  200:
    description: "Successfully retrieved total customer registration counts by source."
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Success flag (1 for success)"
          example: 1
        data:
          type: array
          description: "List of registration sources and their respective counts"
          items:
            type: object
            properties:
              source:
                type: string
                description: "Registration source"
                example: "Facebook"
              count:
                type: integer
                description: "Total number of customer registrations for this source"
                example: 150
  400:
    description: "Invalid request or missing fields"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (-2 for invalid date, -4 for missing region)"
          example: -2
        error:
          type: string
          description: "Error message"
          example: "Missing or invalid date values"
  500:
    description: "Internal server error or database failure"
    schema:
      type: object
      properties:
        success:
          type: integer
          description: "Failure flag (0 for server error)"
          example: 0
        error:
          type: string
          description: "Error message"
          example: "Internal server error"
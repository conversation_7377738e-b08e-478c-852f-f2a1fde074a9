import pandas as pd
import sys
import html
sys.path.append("/app/")
from datetime import datetime, timedelta
from models import ZoomcarBookings, Bookings, ZoomcarRep, BookPending, Users, Drivers
from _utils_booking import booking_has_trip, driver_accept_booking
from db_config import db
import pytz
from booking_params import Regions
from book_ride import DriverBook, create_pending_entry
from zoomcar import zoomcar_book

PICKUP_DELTA = timedelta(seconds=0.5*60*60)
DROP_DELTA = timedelta(seconds=1.0*60*60)

def _gen_pending_entry(realloc_driver_id, cur_price, booking):
    realloc_driver = db.session.query(Users, Drivers).filter(
                Drivers.user == Users.id).filter(
                Drivers.id == realloc_driver_id).first()
    driver_book_entry = DriverBook(realloc_driver[1].id, realloc_driver[0].fname,
                                   realloc_driver[0].lname, realloc_driver[0].mobile,
                                   realloc_driver[1].pic, -1, -1,
                                   realloc_driver[1].rating, cur_price, 0)
    create_pending_entry(driver_book_entry, booking, BookPending.BROADCAST)

def _conv_csv_to_df(csv_path):
    df = pd.read_csv(csv_path)
    return df

def _conv_xls_to_df(csv_path):
    df = pd.read_excel(csv_path, encoding = "utf-8", engine='openpyxl')
    return df


def _create_zoomcar_bookings(df):
    new_col = []
    for col in df.columns:
        new_col.append(str(col.lower()))
    df.columns = new_col
    created = cancelled = 0
    for index, row in df.iterrows():
        print(row)
        # Zoomcar Delhi default
        user = 33658
        # TODO
        car_auto = 0
        appt = str(row['booking_id'])
        try:
            zoomcar_type = (int(row['trip_type']) -1)* (-1)
        except Exception:
            zoomcar_type = 1
            print("NO TRIP TYPE SENT, USING PICKUP")
        zcb_prev_all = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.appt == appt). \
                        filter(ZoomcarBookings.trip_type == zoomcar_type).all()
        max_book_id = -1
        driver_id = -1
        realloc_driver_id = -1
        to_break = False
        to_reallocate = False
        comment = ""
        last_booking_id = -1
        for zcb_prev in zcb_prev_all:
            cur_booking = db.session.query(Bookings).filter(Bookings.id == zcb_prev.ref)
            # Capture driver of last booking for trip appointment.
            if zcb_prev.ref > max_book_id:
                max_book_id = zcb_prev.ref
                driver_id = cur_booking.first().driver
                print("Got booking id", max_book_id, "for driver", driver_id)
                if driver_id > 1:
                    print("Planning to reallocate", max_book_id, "for driver", driver_id)
                    to_reallocate = True
                    realloc_driver_id = driver_id
                else:
                    print("Not reallocating", max_book_id)
                    to_reallocate = False
                    realloc_driver_id = 1
            if not booking_has_trip(cur_booking.first().id):
                cur_booking.update({Bookings.valid: -1})
                comment = cur_booking.first().comment
                print("Cancelling appointment id", appt, "with booking id", zcb_prev.ref, "and rebooking")
                print("Copying comment", comment)
                db.session.commit()
                last_booking_id = cur_booking.first().id
            else:
                driver_id = -1
                realloc_driver_id = -1
                print("Could not cancel appointment id", appt, "as trip", zcb_prev.ref, "complete!")
                to_break = True
            if to_break:
                break
        if to_break:
            print("Skipping completed trip", zcb_prev.ref)
            continue
        try:
            city = row['city'].title()
            if "delhi" in city.lower():
                city = 'Delhi'
        except Exception:
            city = 0
        region = Regions.city_to_region(city)
        reflat = float(row['pickup_lat'])
        reflong = float(row['pickup_lng'])
        loc_name = html.escape(str(row['pickup_address']))
        dest_reflat = float(row['dropoff_lat'])
        dest_reflong = float(row['dropoff_lng'])
        dest_locname = html.escape(str(row['dropoff_address']))
        try:
            if "UTC" in row['time']:
                book_ts = datetime.strptime(row['time'].replace("UTC", "+0530"), "%Y-%m-%d %H:%M:%S %z").astimezone(pytz.utc)
            else:
                book_ts = datetime.strptime(row['time'].replace("T", " ").replace(".000+05:30"," +0530"), "%Y-%m-%d %H:%M:%S %z").astimezone(pytz.utc)
        except Exception:
            if "UTC" in row['booking_start_time']:
                book_ts_actual  = datetime.strptime(row['booking_start_time'].replace(
                                "UTC", "+0530"),
                                "%Y-%m-%d %H:%M:%S %z").astimezone(pytz.utc)
            else:
                book_ts_actual = datetime.strptime(row['booking_start_time'].replace("T", " ").replace(".000+05:30"," +0530"), "%Y-%m-%d %H:%M:%S %z").astimezone(pytz.utc)
            if zoomcar_type == 1:
                time_early = PICKUP_DELTA
            else:
                time_early = DROP_DELTA
            book_ts = book_ts_actual - time_early
        book_date = book_ts.date()
        veh_no = str(row['car_license'])
        veh_model = "UNKNOWN"
        try:
            dspoc_mob = int(row['user_mobile'])
            if len(str(dspoc_mob)) > 10:
                dspoc_mob = int(str(dspoc_mob)[-10:])
        except Exception as e:
            dspoc_mob = 0
            print("Exception in Destination", str(e))
        # TODO
        if 'zc_number' in row:
            zspoc_mobile = int(row['zc_number'])
        else:
            zspoc_mobile = **********
        if len(str(zspoc_mobile)) > 10:
            zspoc_mobile = int(str(zspoc_mobile)[-10:])
        zspoc_exists = db.session.query(ZoomcarRep). \
                    filter(ZoomcarRep.mobile == zspoc_mobile)
        if zspoc_exists.first():
            rid = zspoc_exists.first().id
        else:
            zspoc_name = str(row['zc_spoc'])
            zc_rep = ZoomcarRep(zspoc_name, user, zspoc_mobile, region)
            db.session.add(zc_rep)
            db.session.commit()
            rid = db.session.query(ZoomcarRep). \
                    filter(ZoomcarRep.mobile == zspoc_mobile).first().id
        # TODO if cancelled
        print("Booking zoomcar trip", appt)
        try:
            res, cur_price, booking = zoomcar_book(user, car_auto, reflat, reflong, loc_name,
                    dest_reflat, dest_reflong, dest_locname, book_ts,
                    book_date, appt, veh_no, veh_model, zoomcar_type, dspoc_mob,
                    rid, region, from_app=False, comment=comment)
        except Exception as e:
            res = -1
            print("Exception in book", str(e))
            continue
        cur_booking = db.session.query(ZoomcarBookings).filter(
                ZoomcarBookings.appt == appt).filter(
                ZoomcarBookings.trip_type == zoomcar_type).order_by(
                ZoomcarBookings.ref.desc()).first()
        if to_reallocate:
            print("Accepting booking", cur_booking.ref, "for driver", realloc_driver_id)
            pending_exists = db.session.query(BookPending). \
                                filter(BookPending.driver == realloc_driver_id). \
                                filter(BookPending.book_id == cur_booking.ref).first()
            if not pending_exists:
                print("No pending entry found")
                try:
                    _gen_pending_entry(realloc_driver_id, cur_price, booking)
                except Exception as e:
                    db.session.rollback()
                    print("Rollbacking pending entry, error", e)
                    continue
            resp = driver_accept_booking(cur_booking.ref, realloc_driver_id,
                        is_user=False, update_pending=False)
            print("Trip accepted with response", resp)
        else:
            if last_booking_id > 0:
                all_pending = db.session.query(BookPending).filter(BookPending.book_id == last_booking_id).filter(BookPending.driver > 1)
                try:
                    all_pending.update({BookPending.book_id: cur_booking.ref})
                    db.session.commit()
                    print("Allocated book pending entries from", last_booking_id, "to", cur_booking.ref)
                except Exception as e:
                    print("Error in pending entry", str(e))
        if res == 1:
            created += 1
    return created, cancelled

def create_zoomcar_bookings(csv_excel_to_read, is_excel=False):
    try:
        if not is_excel:
            df = _conv_csv_to_df(csv_excel_to_read)
        else:
            df = _conv_xls_to_df(csv_excel_to_read)
    except Exception as e:
        print("Exception:", e)
        return
    print("Counted", len(df), "entries")
    created, cancelled = _create_zoomcar_bookings(df)
    print("Created", created, "and cancelled", cancelled, "bookings")

if __name__ == '__main__':
    try:
        csv_excel_to_read = sys.argv[1]
        try:
            is_excel = bool(sys.argv[2])
        except Exception:
            is_excel = False
    except Exception:
        print("Could not proceed")
        exit(0)
    create_zoomcar_bookings(csv_excel_to_read, is_excel)



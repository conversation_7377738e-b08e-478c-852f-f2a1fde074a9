var featuresContent = {
	"verified" : {
		"name" : "Verified",
		"description" : "Our Driver Companions are tested, and regularly trained and groomed. They undergo a thorough background check and document verification. When you book, your Driver Companion's profile is visible on the app giving you an assurance of authenticity.",
		"imagePath" : "../static/assets/images/Verified-Art.svg"
	},
	"tracking" : {
		"name" : "Tracking",
		"description" : "You can track your driver in real-time! You can get the latest location whereabouts of your Driver Companion on your app, right from the time they are on their way to your pickup point, upto the very end of your trip.",
		"imagePath" : "../static/assets/images/Tracking-Art.svg"
	},
	"customer_support" : {
		"name" : "Support",
		"description" : "Have a query or facing any issue? Our support team is here 24x7 to help you out. Reach out to us through the in-app chat feature or our customer helpline <strong><a href='tel:+91 888 2 012345'>888 2 012345</a></strong>. You can also mail your concern to <a href='mailto:<EMAIL>' target='_blank'><EMAIL></a>.",
		"imagePath" : "../static/assets/images/Support-Art.svg"
	},
	"payment" : {
		"name" : "Payments",
		"description" : "Meet D4M Credits! An online payment method that auto-deducts the fare from a balance amount which you can recharge via Paytm, UPI, Google Pay and other common modes. You can also pay the driver companion in cash at the very end of the trip.",
		"imagePath" : "../static/assets/images/Payment-Art.svg"
	},
	"customize" : {
		"name" : "Customize",
		"description" : "We cater to round trips, one way, outstations and more convenient categories. You can find a driver well suited to all private car segments as well as pick from a wide-range of duration options. So go ahead and customise your own booking!",
		"imagePath" : "../static/assets/images/Customizable-Art.svg"
	}
};

var alternatingTextsCollection = {
	0: {
		"header" : "Need Private Car Drivers?",
		"sub" : "Hire highly trained and professional drivers for your car whenever and wherever you need!"
	},
	1: {
		"header" : "Don't feel like Driving?",
		"sub" : "Book a car driver with Drivers4Me and relax in the backseat!"
	},
	2: {
		"header" : "Emergency Appointments?",
		"sub" : "Hire an experienced Driver, and get to places in no time!"
	},
	3: {
		"header" : "Big Vacation Travel Plans?",
		"sub" : "Book a professional driver for outstation trip at reasonable prices!"
	}
};

var textAlterInterval = 4500; //(minimum 1200)
var textAlterIndex = 0;

mobiscroll.settings = {
	theme: 'ios',
	themeVariant: 'light'
};

/* PARAMS FOR PRICE ESTIMATION */
var tripType = 1;
var dateSel = "";
var timeSel = "";
var durSel = "";
// (0-3)
var vehicleType = 0;
// (0,4)
var transmissionType = 0;
var estimateResp = null;
// vehicleType and transmissionType combine to make carType (0-7)
var carType = 0;
var region = 6;

$(document).ready(function() {
//	showLoader();
	transitionNavBars();
	setTimeout(switchLandingTexts, textAlterInterval);

	//adjust map location size
	$(".estimator-input").each(function () {
        $(this).attr('size', $(this).attr('placeholder').length);
    });

	// Initialize Autocomplete initMapInputs
	initMapInputs(true);

	// Initialize Date and Time Inputs
	$("#dateText").text(fmtDate(new Date()));
	$("#timeText").text(fmtTime(new Date()));
	dateSel = new Date();
	timeSel = new Date();
	alterDestinationVisibility();
	setDurationUnit();
	setDefaultDuration();

	$('[data-toggle="popover"]').popover({
        content: function() {
            return $('#popover-content').html();
        }
  });

    var cookie = getCookie();
    var refresh = cookie['csrf_refresh_token'];
    var access = cookie['csrf_access_token'];
    if (access && refresh) {
        $(".nav-login").addClass('collapse');
        $(".userName").html(cookie['name']);
		$(".userMobile").html(cookie['phone'])
		$("#userInfoDrop").removeClass('collapse');
		$("#userInfoDropSecondary").removeClass('collapse');
    } else {
		$("#userInfoDrop").addClass('collapse');
		$("#userInfoDropSecondary").addClass('collapse');
		$(".nav-login").removeClass('collapse');
    }
	// Initialize Screenshot carousel
	$("#screenshotCarousel").owlCarousel({
		loop:true,
		dots: true,
		autoplay:true,
		autoplayTimeout:3000,
		autoplayHoverPause:true,
		responsive:{
	        0:{
	            items:1
	        },
			576: {
				items:1
			},
	        992:{
	            items:2
	        },
			1200:{
				items:3
			}
	    },
		center: true
	});

	// Initialize Screenshot carousel
	$("#testimonialsCarousel").owlCarousel({
		loop:true,
		dots: true,
		autoplay:true,
		autoplayTimeout:4000,
		autoplayHoverPause:true,
		responsive:{
	        0:{
	            items:1
	        },
			576: {
				items:1
			},
	        992:{
	            items:2
	        },
			1200:{
				items:3
			}
	    },
		center: true
	});

	$("#mediaCarousel").owlCarousel({
		loop:true,
		dots: true,
		autoplay:true,
		autoplayTimeout:4000,
		autoplayHoverPause:true,
		responsive:{
	        0:{
	            items:1
	        },
			576: {
				items:1
			},
	        992:{
	            items:3
	        },
			1200:{
				items:5
			}
	    },
		center: true
	});

	$(window).on("load", function() {
		//setTimeout(function() { hideLoader(); }, 1000);
	});

	$(".step-card-header").click(function() {
		parentStep = $(this).closest(".step-content");
		if(parentStep.hasClass("expanded")) {
			$(this).find(".expand-toggle").removeClass("fa-chevron-up").addClass("fa-chevron-down");
			parentStep.removeClass("expanded");
		}

		else {
			$(".step-content.expanded").find(".expand-toggle").removeClass("fa-chevron-up").addClass("fa-chevron-down");
			$(".step-content.expanded").removeClass("expanded");
			parentStep.addClass("expanded");
			$(this).find(".expand-toggle").removeClass("fa-chevron-down").addClass("fa-chevron-up");
		}
	});

	$(".feature-thumbnail").click(function() {
		$(".feature-thumbnail").removeClass("active");
		$(".feature-flaticon").removeClass("flaticon-active");
		$(this).addClass("active");
		$(this).find(".feature-flaticon").addClass("flaticon-active");
		var featureInfo = featuresContent[$(this).attr("id").replace("thumbnail_", "")];
		$("#Feature_Description").find(".feature-header").html(featureInfo["name"]);
		$("#Feature_Description").find(".feature-text").html(featureInfo["description"]);
		$("#Features_Main").attr("src", featureInfo["imagePath"]);
	});

	$("#tripTypeSwitch").click(function() {
		if($(this).hasClass("enabled-right")) {
			$("#tripTypeSwitch").addClass("enabled-left").removeClass("enabled-right");
			$(".price-card").removeClass("flipped");
		}
		else {
			$("#tripTypeSwitch").addClass("enabled-right").removeClass("enabled-left");
			$(".price-card").addClass("flipped");
		}
	});

	$("#submitPhone_AppLink").click(function() {
		var valid = true;
		var phoneNumber = $("#appLinkPhoneNumber").val().trim();
		if(!phoneRegex.test(phoneNumber)) {
			$("#appLinkPhoneNumber").addClass("input-error-bg");
			showSnackbar("Invalid/Missing Phone number!", "snackbar-danger", 2500);
			valid = false;
		}

		if(!valid) return;

		else {
		    data = new FormData();
            data.append('mobile', phoneNumber);
            var url = window.location.protocol + '//' + window.location.host + '/api/ref_msg';
            $.ajax({
                    type:"POST",
                    url: url,
                    data: data,
                    dataType: "json",
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if (response['success'] == 1) {
                            showSnackbar("Message sent to your phone number!");
                        } else {
                            showSnackbar("Unable to send message to phone number!", "snackbar-danger", 2500);
                        }
        		}
	        });
		}
	});

	$(window).scroll(function() {
		transitionNavBars();
    });

    $(".logout").click(function() {
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/token/remove',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function() {
                window.location = "/";
            }
        });
    });

	$('body').delegate(".option-card",'click',function() {
		$(this).closest(".popover-body").find(".option-card").removeClass("active");
		$(this).addClass("active");
	});

	$('body').delegate(".trip-option-card",'click',function() {
		var parentElement = $(this).closest(".popover-body");
		var newTripType = parseInt($(this).attr("data-content").split("|")[0]);
		var newAlternateTripType = parseInt($(this).attr("data-alt"));
		$("#triptype-popover").find(".option-card").removeClass("active");
		$("#triptype-popover").find(".option-card[data-content^='"+ newTripType +"']").addClass("active");
		$("#triptype-popover").find(".option-card[data-content^='"+ newAlternateTripType +"']").addClass("active");
		tripType = newTripType;
		activeElementData = $(this).attr("data-content").split("|");
		$("#bookingTypeImage").attr("src", "../static/assets/images/elements/" + activeElementData[1]);
		$("#bookingTypeText").html(activeElementData[2]);
		alterDestinationVisibility();
		setDurationUnit();
		setDefaultDuration();
	});

	$('body').delegate(".trip-toggle",'click',function() {
		if(!$(this).hasClass("active")) {
			var parentElement = $(this).closest(".popover");
			var alternateTripType = parseInt(parentElement.find(".option-card.active[data-content^='"+ tripType +"']").attr("data-alt"));
			if(alternateTripType) {
			    $("#destinationType-popover").find(".trip-param-toggle-option").removeClass("active");
    			var destinationType = $(this).find("input").val().split("|")[0];
    			$("#destinationType-popover").find(".trip-param-toggle-option[data-content^='"+ destinationType +"']").addClass("active");
    			$("#triptype-popover").find(".option-card").addClass("collapse");
    			if(destinationType == 0) {
    				$("#triptype-popover").find(".round-trip").removeClass("collapse");
    			}
    			else if(destinationType == 1) {
    				$("#triptype-popover").find(".one-way").removeClass("collapse");
    			}
    			$(".popover-body").html($("#triptype-popover").html());
    			tripType = alternateTripType;
    			activeElementData = parentElement.find(".option-card[data-content^='"+ tripType +"']").attr("data-content").split("|");
    			$("#bookingTypeImage").attr("src", "../static/assets/images/elements/" + activeElementData[1]);
    			$("#bookingTypeText").html(activeElementData[2]);
    			alterDestinationVisibility();
    			setDurationUnit();
    			setDefaultDuration();
			}
		}
	});

	$("#bookingType").click(function() {
		$(".popover-body").html($("#triptype-popover").html());
		$(".popover-header").html($("#destinationType-popover").html());
    });

	$("#dateDisplayBlock").click(function() {
        var minDate = new Date();
        minDate.setHours(minDate.getHours() + (minBookingThreshold / 3600 / 1000));
        var endDate = new Date(minDate.getTime() + 10 * 24 * 60 * 60 * 1000);
        mobiscroll.date('#datePicker', {
            display: 'center',
            min: minDate,
            max: endDate,
            onInit: function (event, inst) {
                inst.setVal(minDate, true);
            },
			onSet: function (event, inst) {
			   dateSel = inst.getVal();
			   $("#dateText").text(fmtDate(event.valueText));
			   handleEstimateReverseTransitionUI();
		   },
        });
        $("#datePicker").trigger("click");
    });

	$("#timeDisplayBlock").click(function() {
        var minTime = new Date();
        minTime.setHours(minTime.getHours() + (minBookingThreshold / 3600 / 1000));
        mobiscroll.time('#timePicker', {
            display: 'center',
            onInit: function (event, inst) {
                inst.setVal(minTime, true);
            },
			onSet: function (event, inst) {
			   timeSel = inst.getVal();
			   $("#timeText").text(event.valueText);
			   handleEstimateReverseTransitionUI();
		   },
        });
        $("#timePicker").trigger("click");
    });

	$("#durDisplayBlock").click(function() {
        mobiscroll.scroller('#durPicker', {
            display: 'center',
            wheels: [
               [{
                   data: getDurArray()
               }]
           ],
           circular: false,
		   onSet: function (event, inst) {
		   		durSel = event.valueText;
			  	$("#durText").text(durSel);
				handleEstimateReverseTransitionUI();
		   },
        });
        $("#durPicker").trigger("click");

    });

	$("#carType").click(function() {
		$(".popover-body").html($("#cartype-popover").html());
		$(".popover-header").html($("#transmission-popover").html());
	});

	$('body').delegate(".car-option-card",'click',function() {
		var parentElement = $(this).closest(".popover-body");
		var newCarType = parseInt($(this).attr("data-content").split("|")[0]);
		$("#cartype-popover").find(".option-card").removeClass("active");
		$("#cartype-popover").find(".option-card[data-content^='"+ newCarType +"']").addClass("active");
		vehicleType = newCarType;
		setCarType();
		activeElementData = $(this).attr("data-content").split("|");
		$("#carTypeImage").attr("src", "../static/assets/images/elements/" + activeElementData[1]);
		$("#carTypeText").html(activeElementData[2]);
	});


	$("#regionSelect").click(function() {
		$(".popover-body").html($("#region-popover").html());
		$(".popover-header").remove();
	});

	$('body').delegate(".region-option-card",'click',function() {
		var parentElement = $(this).closest(".popover-body");
		var newregion = parseInt($(this).attr("data-content").split("|")[0]);
		$("#region-popover").find(".option-card").removeClass("active");
		$("#region-popover").find(".option-card[data-content^='"+ newregion +"']").addClass("active");
		region = newregion;
		activeElementData = $(this).attr("data-content").split("|");
		$("#regionText").html(activeElementData[1]);
	});

	$('body').delegate(".gear-toggle",'click',function() {
		$("#transmission-popover").find(".trip-param-toggle-option").removeClass("active");
		var parentElement = $(this).closest(".popover");

		transmissionType = parseInt($(this).find("input").val().split("|")[0]);

		$("#transmission-popover").find(".trip-param-toggle-option[data-content^='"+ transmissionType +"']").addClass("active");
		setCarType();
	});

	$('body').on('click', function (e) {
	    $('[data-toggle=popover]').each(function () {
	        // hide any open popovers when the anywhere else in the body is clicked
	        if (!$(this).is(e.target) && $(this).has(e.target).length === 0 && $('.popover').has(e.target).length === 0) {
	            $(this).popover('hide');
	        }
	    });
	});

	$(".avatar-drop").click(function(e) {
		e.stopPropagation();
		if($(this).hasClass("dropped") && !$(this).closest(".nav-item").find(".dropdown-menu").hasClass("show")) {
			$(this).removeClass("dropped");
		}
		else $(this).addClass("dropped");
		$(this).closest(".nav-item").find(".nav-link").click();
	});

	$(window).click(function() {
		if(!$("#userInfoMenu").hasClass("show")) {
			$("#userInfoDrop").find("i").removeClass("dropped");
		}

		if(!$("#userInfoMenuSecondary").hasClass("show")) {
			$("#userInfoDropSecondary").find("i").removeClass("dropped");
		}
	});

	$("#userInfoDrop").find(".nav-link").click(function() {
		if(!$("#userInfoMenu").hasClass("show")) {
			$("#userInfoDrop").find("i").addClass("dropped");
		}

		else {
			$("#userInfoDrop").find("i").removeClass("dropped");
		}
	});

	$("#userInfoDropSecondary").find(".nav-link").click(function() {
		if(!$("#userInfoMenuSecondary").hasClass("show")) {
			$("#userInfoDropSecondary").find("i").addClass("dropped");
		}

		else {
			$("#userInfoDropSecondary").find("i").removeClass("dropped");
		}
	});


	$("#checkEstimate").click(function() {
	    durSel = $("#durText").html()
		var curDate = mobiscroll.date('#datePicker').getVal();
		var curTime = mobiscroll.time('#timePicker').getVal();
		if (!curDate || !curTime) {
		    curDate = dateSel;
		    curTime = timeSel;
		}
		console.log(curTime)
		showLoader();
	    resolveLocAndGetEstimate(tripType, curDate, curTime, durSel, carType, $("#sourceSearch").val(), $("#destinationSearch").val())
	})

	$("#sourceSearch, #destinationSearch").change(function() {
	    estimateResp = null;
	    handleEstimateReverseTransitionUI();
	});

	$('body').delegate(".car-option-card, .gear-toggle, .trip-option-card, .trip-toggle, .region-option-card", "click", function() {
		estimateResp = null;
	    handleEstimateReverseTransitionUI();
	});

	$("#bookTrip").click(function() {
        if (estimateResp) {
            estimateResp["trip_type"] = tripType
            estimateResp["date_sel"] = dateSel
            estimateResp["time_sel"] = timeSel
            estimateResp["dur_sel"] = durSel
            setCookieSingle("search_id", estimateResp["id"], 1);
            setCookieSingle("estimate_content", JSON.stringify(estimateResp), 1);
            window.location = '/book'
        } else {
            showSnackbar("Please check estimate, then try again!", "snackbar-danger", 2500);
        }
	});

	$(".media-thumbnail").click(function(e) {
		if($(this).find(".media-article-link").get(0)) {
			$(this).find(".media-article-link").get(0).click();
		}
	});

	$("#mediaCarousel").find(".media-item").click(function() {
		if($(this).find(".media-article-link").get(0)) {
			$(this).find(".media-article-link").get(0).click();
		}
	});

	$("#saveContactUs").click(function(e) {
	    // TODO: Have some validation using regex?
	    let name = $("#label_name").val()
	    if (name == "") {
            showSnackbar("Please Enter a Valid Name", "snackbar-danger", 2500);
            return;
	    }
	    let phoneNo = parseInt($("#label_mobile").val());
	    if (isNaN(phoneNo) || !(/^[6-9]\d{9}$/).test(phoneNo)) {
            showSnackbar("Please Enter a Valid Phone Number", "snackbar-danger", 2500);
            return;
	    }
	    let src = $("#label_source").val()
	    let dest = $("#label_destination").val()
	    sendContactResponse(phoneNo, name, src, dest);
	})
});


function transitionNavBars() {
    if($("#brandNavSecondary").hasClass("suppressed-nav")) {
        if($(window).scrollTop() >= $("#brandNav")[0].scrollHeight) {
			$("#brandNav").addClass("suppressed-nav");
			$("#brandNavSecondary").removeClass("suppressed-nav");
			if($("#brandNav").find(".dropdown-menu").hasClass("show") && !$("#brandNavSecondary").find(".dropdown-menu").hasClass("show")) {
			    $("#brandNavSecondary").find(".dropdown-toggle").click();
			}
        }
	}

	else {
		if($(window).scrollTop() < $("#brandNav")[0].scrollHeight) {
			$("#brandNavSecondary").addClass("suppressed-nav");
			$("#brandNav").removeClass("suppressed-nav");
			if($("#brandNavSecondary").find(".dropdown-menu").hasClass("show") && !$("#brandNav").find(".dropdown-menu").hasClass("show")) {
			    $("#brandNav").find(".dropdown-toggle").click();
			}
		}
	}
}

function setDefaultDuration() {
	var durOptions = getDurArray();
	$("#durText").text(durOptions[0]);
}

function setDurationUnit() {
	if([2,5].indexOf(tripType) > -1) {
		$("#durUnit").html("day(s)");
	}

	else {
		$("#durUnit").html("hour(s)");
	}
}

function resolveLocAndGetEstimate(tType, sDate, sTime, sDur, cType, srcString, destString, init=true) {
    geocoder = new google.maps.Geocoder();
    geocoder.geocode
    ({
        address: srcString
    },
    function(results, status) {
        if (status == google.maps.GeocoderStatus.OK) {
            if (init)
                resolveLocAndGetEstimate(tType, sDate, sTime, sDur, cType, destString, results[0].geometry.location, false)
            else
                getEstimate(tType, sDate, sTime, sDur, cType, destString, results[0].geometry.location)
        } else {
            if (init)
                resolveLocAndGetEstimate(tType, sDate, sTime, sDur, cType, destString, false, false)
            else
                getEstimate(tType, sDate, sTime, sDur, cType, destString, false)

        }
    })
}

function getEstimate(tType, sDate, sTime, sDur, cType, srcObj, destObj) {
    if (!srcObj) {
        hideLoader();
        showSnackbar("Invalid pickup location!", "snackbar-danger", 2500);
        return false;
    }  // add callback
    srcLat = srcObj.lat()
    srcLng = srcObj.lng()
    if (!destObj) {
        destLat = destLng = -1;
    } else {
        destLat = destObj.lat()
        destLng = destObj.lng()
    }
    dur = getParsedDur(sDur, tType)

	var date = zeroPad(sDate.getDate());
	var month = zeroPad(sDate.getMonth()+1);
	var year = sDate.getFullYear();
	var hour = zeroPad(sTime.getHours());
	var minute = zeroPad(sTime.getMinutes());
	var sec = zeroPad(sTime.getSeconds());
	var tz = convTZ(sTime.getTimezoneOffset());
	actDT = date + "/" + month + "/" + year + " " + hour + ":" + minute + ":" + sec + " " + tz;
    data = new FormData()
    data.append('type', tType)
    data.append('time', actDT)
    data.append('dur', dur)
    data.append('car_type', cType)
    data.append('src_lat', srcLat)
    data.append('src_lng', srcLng)
    data.append('dest_lat', destLat)
    data.append('dest_lng', destLng)
    data.append('region', region)
    window.d = data
    console.log(data)
    var url = window.location.protocol + '//' + window.location.host + '/api/get_estimate';
    $.ajax({
            type:"POST",
            url: url,
            data: data,
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response['success'] == 1) {
                    $("#estimateText").html(response['min_est'])
                    estimateResp = response
                    handleEstimateTransitionUI();
                    hideLoader();

                } else {
                    showSnackbar("Unable to calculate estimate!", "snackbar-danger", 2500);
                    hideLoader();
                }
		}
    });
}

function sendContactResponse(phoneNo, name, from, to) {
    data = new FormData()
    data.append('mobile', phoneNo)
    data.append('name', name)
    data.append('from', from)
    data.append('to', to)
    var url = window.location.protocol + '//' + window.location.host + '/website/contact_us';
    $.ajax({
            type:"POST",
            url: url,
            data: data,
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response['success'] == 1) {
                    showSnackbar("Submitted response!", "snackbar-info", 2500);
                    $('#label_name').val('');
                    $('#label_mobile').val('');
                    $('#label_source').val('');
                    $('#label_destination').val('');
                } else {
                    showSnackbar("Could not submit response!", "snackbar-danger", 2500);
                }
                console.log(response)
		}
    });
}

function alterDestinationVisibility() {
	if(tripType != 1) {
		$("#destinationField").removeClass("collapse");
	}

	else {
		$("#destinationField").addClass("collapse");
	}
}

function setCarType() {
	carType = vehicleType + transmissionType;
}

function handleEstimateTransitionUI() {
	$("#checkEstimate").addClass("collapse");
	$("#estimateSpace").removeClass("d-none").addClass("d-inline-block");
}

function handleEstimateReverseTransitionUI() {
	$("#checkEstimate").removeClass("collapse");
	$("#estimateSpace").addClass("d-none").removeClass("d-inline-block");
}

function displayFareEstimate(estimateValue) {
	handleEstimateTransitionUI();
	$("#estimateText").text(0.00);
	$("#estimateText").attr("data-count", estimateValue);
	// init counters
	$('.counter').each(function() {
        var $this = $(this),
        countTo = $this.attr('data-count');

        $({ countNum: $this.text()}).animate({
            countNum: countTo
        },

        {

            duration: $this.attr("data-duration"),
            easing:'linear',
            step: function() {
                $this.text(Math.floor(this.countNum));
            },
            complete: function() {
                $this.text(this.countNum);
            }

        });
    });
}

function switchLandingTexts() {
	prevIndex = textAlterIndex;
	textAlterIndex = parseInt((textAlterIndex + 1) % Object.keys(alternatingTextsCollection).length);
	$(".home-header-alternating-text").removeClass("active");
	$(".home-sub-alternating-text").removeClass("active");
	setTimeout(function() {
		$(".home-header-alternating-text").text(alternatingTextsCollection[textAlterIndex]["header"]);
		$(".home-sub-alternating-text").text(alternatingTextsCollection[textAlterIndex]["sub"]);
	}, 300);
	setTimeout(function() {
		$(".home-header-alternating-text").addClass("active");
		$(".home-sub-alternating-text").addClass("active");
	}, 300);

	setTimeout(switchLandingTexts, textAlterInterval);
}

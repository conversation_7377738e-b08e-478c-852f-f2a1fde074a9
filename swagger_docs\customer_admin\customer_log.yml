tags:
  - Customer_admin
summary: Get Customer Change Logs
description: >
  This endpoint retrieves the change logs for a specific customer. It requires authentication and
  the appropriate access rights. The logs include details about what changes were made to the 
  customer profile, who made the changes, and when they were made.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The ID of the user whose logs are being requested
    example: "123"
responses:
  200:  # Success response when logs are fetched successfully
    description: Logs fetched successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: array
          description: List of logs for the customer
          items:
            type: object
            properties:
              date_and_time:
                type: string
                description: Date and time of the change (in IST format)
                example: "12 Sep 23,14:45:30"
              changedby:
                type: string
                description: Name of the admin or user who made the changes
                example: "Admin John Doe"
              changes:
                type: string
                description: Details of what was changed
                example: "First Name, Last Name"
              change_from:
                type: string
                description: Previous values before the change
                example: "<PERSON>, <PERSON><PERSON>"
              change_to:
                type: string
                description: New values after the change
                example: "<PERSON>, <PERSON><PERSON>"
  400:  # Bad request due to missing user_id
    description: User ID is required but not provided
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Error message detailing the issue
          example: "User ID is required"
  500:  # Internal server error
    description: Internal server error due to issues with processing the request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        error:
          type: string
          description: Error message detailing the issue
          example: "An unexpected error occurred"
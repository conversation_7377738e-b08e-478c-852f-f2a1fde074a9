$( document ).ready(function() {
	var pos=0;
	var isLogin = false;
	var isRegister = true;

	mobiscroll.settings = {
        theme: 'ios',
        themeVariant: 'light'
    };
    var cookie = getCookie();
    var refresh = cookie['csrf_refresh_token'];
    var access = cookie['csrf_access_token'];
    if (access && refresh) {
        //var ans = checkRefresh(access, refresh);
        if (true || ans) {
            window.location = window.location.protocol + '//' + window.location.host + '/book';
        }
    }
	populateCountriesInHTMLList($("#cc-picker"));
	$("#continueLogin").click(function() {
		// phone number validation
		var valid = true;
		var phoneNumber = $("#Input_Phone").val().trim();
		if(!phoneRegex.test(phoneNumber)) {
			$("#Input_Phone").addClass("input-error-bg");
			showSnackbar("Invalid/Missing Phone number!", "snackbar-danger", 2500);
			valid = false;
		}

		if(!valid) return;

		$("#nameBody").css("display", "")
		$("#otpBody").css("display", "")
		$("#passwordBody").css("display", "")
		$("#setPasswordBody").css("display", "")
		$("#setOtpBody").css("display", "")//Run this before SV Call

		//Run This after SV call is successful
		showLoader();
		checkExists(phoneNumber);
	});
	$("#nameBack").click(function() {
		pos=pos+100
		animateRegister()
	});
	$("#setPasswordBack").click(function() {
		pos=pos+100
		animateRegister()
	});
	$("#nextName").click(function() {
		// name validation
		var valid = true;
		var fname = $("#Input_First_Name").val().trim();
		var lname = $("#Input_Last_Name").val().trim();
		if(!fnameRegex.test(fname)) {
			$("#Input_First_Name").addClass("input-error-bg");
			showSnackbar("Invalid/Missing Name!", "snackbar-danger", 2500);
			valid = false;
		}

		if(!lnameRegex.test(lname)) {
			$("#Input_Last_Name").addClass("input-error-bg");
			if(valid) showSnackbar("Invalid/Missing Name!", "snackbar-danger", 2500);
			valid = false;
		}

		if(!valid) return;

		pos=pos-100
		animateRegister();
	});
	$("#nextPassword").click(function() {
		// passwords validation
		var pass = $("#Input_Set_Password").val();
		var rePass = $("#Input_Set_Re_Password").val();
		if(pass.length < minPasswordLength) {
			$("#Input_Set_Password").addClass("input-error-bg");
			showSnackbar("Password must be at least 6 characters long!", "snackbar-danger", 2500);
			return;
		}

		if (pass != rePass) {
			$("#Input_Set_Re_Password").addClass("input-error-bg");
			showSnackbar("Re-entered Password does not match!", "snackbar-danger", 2500);
			return;
		}
		genOTP($("#Input_Phone").val(), 1);
		pos=pos-100
		animateRegister()
	});

	$("#otpBack").click(function() {
		pos=pos+100
		animateLogin()
	});
	$("#setOtpBack").click(function() {
		pos=pos+100
		animateRegister()
	});
	$("#passwordSwitch").click(function() {
		pos=pos-100
		animateLogin()
	});

	$("#passwordBack").click(function() {
		pos=pos+100
		animateLogin()
	});

	$("#confirmLogin").click(function() {
		// OTP validation
		var OTP = $("#Input_OTP").val();
		if(OTP.length !== OTPLength) {
			$("#Input_OTP").addClass("input-error-bg");
			showSnackbar("Missing/Invalid OTP!", "snackbar-danger", 2500);
			return;
		}

		var mobile = $("#Input_Phone").val()
		showLoader();
		loginUser(mobile, OTP, true)
	});

	$("#confirmLoginPass").click(function() {
		// passwords validation
		var pass = $("#Input_Pass").val();
		if(pass.length < minPasswordLength) {
			$("#Input_Pass").addClass("input-error-bg");
			showSnackbar("Missing/Invalid Password!", "snackbar-danger", 2500);
			return;
		}

		var mobile = $("#Input_Phone").val()
		var pwd = $("#Input_Pass").val()
		showLoader();
		loginUser(mobile, pwd, false)
	});

	$("#confirmRegister").click(function() {
		// OTP validation
		var OTP = $("#Input_Set_OTP").val();
		if(OTP.length !== OTPLength) {
			$("#Input_Set_OTP").addClass("input-error-bg");
			showSnackbar("Missing/Invalid OTP!", "snackbar-danger", 2500);
			return;
		}

		var fname = $("#Input_First_Name").val()
		var lname = $("#Input_Last_Name").val()
		var mobile = $("#Input_Phone").val()
		var pwd = $("#Input_Set_Password").val()
		var otp = $("#Input_Set_OTP").val()
		showLoader();
		registerValOTP(mobile, otp, fname, lname, pwd)
	});

	$("#resendOTP").click(function() {
		genOTP($("#Input_Phone").val())
	});

	$("#resendSetOTP").click(function() {
		genOTP($("#Input_Phone").val(), 1)
	});

	$("#ccSelect").click(function() {
		mobiscroll.image('#cc-picker', {
        display: 'center',
        enhance: true,
        showInput: false,
        onSet:  function (event, inst) {
			var countryDetail = getCountryDetailsByID(event.valueText);
			$("#ccDisplay").find(".cc-picker-flag").attr("class", "cc-picker-flag " + (event.valueText).toLowerCase());
			$("#ccDisplay").find(".ccode").html(countryDetail["phoneCode"]);
         }
      });
      $("#cc-picker").trigger("click");
	});

	function animateLogin(){
		$("#phnBody").animate({left: pos+'%'})
		$("#otpBody").animate({left: pos+'%'})
		$("#passwordBody").animate({left: pos+'%'})
	}
	function animateRegister(){
		$("#nameBody").animate({left: pos+'%'})
		$("#phnBody").animate({left: pos+'%'})
		$("#setPasswordBody").animate({left: pos+'%'})
		$("#setOtpBody").animate({left: pos+'%'})
	}


	function showLogin(mobile) {
		hideLoader();
		pos=pos-100
		$("#nameBody").css("display", "none")
		$("#setPasswordBody").css("display", "none")
		$("#setOtpBody").css("display", "none")
		animateLogin()
	}

	function showRegister(mobile) {
		hideLoader();
		pos=pos-100
		$("#otpBody").css("display", "none")
		$("#passwordBody").css("display", "none")
		animateRegister()
	}

	function loginUser(mobile, pwd, type) {
		var data = new FormData()
		data.append('mobile', mobile)
		data.append('pwd', pwd)
		data.append('remember', true)
		if (type)
			data.append('type', 1)
		$.ajax({
				type:"POST",
				url: window.location.protocol + '//' + window.location.host + '/token/login',
				data: data,
				dataType: "json",
				contentType: false,
				processData: false,
				success: function(e) {
					var msg = JSON.stringify(e)
					console.log(msg)
					if (e.success == 1)
					{
						setCookie("name", e.user_fname + " " + e.user_lname, "phone", e.user_mobile, e.user_id, e.user_restore_id, 365, e.region)
						hideLoader();
						window.location = window.location.protocol + '//' + window.location.host + '/book'
					}
					else {
						showSnackbar("Incorrect Credentials. Either Phone number or password is wrong.", "snackbar-danger")
						hideLoader();
					}

				},
				error: function(e) {
					if(e.status == 401) {
						showSnackbar("Incorrect Credentials. Either Phone number or password is wrong.", "snackbar-danger")
						hideLoader();
					}
				}

		  })
	}

	function checkExists(mobile) {
		var data = new FormData()
		data.append('mobile', mobile)
		data.append('gen_otp', 1)
		$.ajax({
				type:"POST",
				url: window.location.protocol + '//' + window.location.host + '/token/exists',
				data: data,
				dataType: "json",
				contentType: false,
				processData: false,
				success: function(e) {
					var msg = JSON.stringify(e)

					if (e.exists == 1)
					{
						showLogin(mobile)
					}
					else if (e.exists == 0) {
						showRegister(mobile);
					} else {
						showRegister(mobile);
					}


				},
				error: function(e) {
					// this indicates could not find user
					if (e.status == 401 && e.exists == -2)
						showSnackbar("Your account has been disabled. Please contact customer support for details.", " ")
					else if (e.status == 500)
						showSnackbar("Unknown error occurred. Please reload and try again.", " ")
					else
						showRegister(mobile);
				}

		  })
	}

	function genOTP(mobile, isNew=0) {
		var data = new FormData()
		data.append('mobile', mobile)
		if (isNew)
			data.append('new', 1)
		$.ajax({
				type:"POST",
				url: window.location.protocol + '//' + window.location.host + '/token/otp/generate',
				data: data,
				dataType: "json",
				contentType: false,
				processData: false,
				success: function(e) {
					showSnackbar("OTP sent successfully to : " + mobile.trim(), "", 2500);
				},
				error: function(e) {
					// silently fail
				}

		  })
	}

	/* Only use for new users. */
	function registerValOTP(mobile, otp, fname, lname, pwd) {
		var data = new FormData()
		data.append('mobile', mobile)
		data.append('otp', otp)
		data.append('new', 1)
		$.ajax({
				type:"POST",
				url: window.location.protocol + '//' + window.location.host + '/token/otp/validate',
				data: data,
				dataType: "json",
				contentType: false,
				processData: false,
				success: function(e) {
					if (e.success == 1) {
						registerUser(mobile, fname, lname, pwd)
					}
					else
						showSnackbar("Invalid OTP. Please try again.", "snackbar-danger")
						hideLoader()
				},
				error: function(e) {
					showSnackbar("Invalid OTP. Please try again.", "snackbar-danger")
					hideLoader()
				}

		  })
	}

	function registerUser(mobile, fname, lname, pwd) {
		var data = new FormData()
		data.append('mobile', mobile)
		data.append('fname', fname)
		data.append('lname', lname)
		data.append('pwd', pwd)
		$.ajax({
				type:"POST",
				url: window.location.protocol + '//' + window.location.host + '/api/register_cust',
				data: data,
				dataType: "json",
				contentType: false,
				processData: false,
				success: function(e) {
					if (e[0].response == 0)
					{
						loginUser(mobile, pwd)
					}
					else {

						showSnackbar("Unknown error occurred. Please reload and try again.", "snackbar-danger")
						hideLoader()
					}

				},
				error: function(e) {
					showSnackbar("Unknown error occurred. Please reload and try again.", "snackbar-danger")
					hideLoader()
				}

		  })
	}
});

	/*$("#phnBody").css("display", "none");
		$('#phnBody').addClass('animated slideOutLeft').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
			$("#phnBody").removeClass('animated slideOutLeft');


		});
		$("#passwordBody").css("display", "block");
		$('#passwordBody').addClass('animated slideInRight').one('webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend', function(){
				$("#passwordBody").removeClass('animated slideInRight');
		});*/

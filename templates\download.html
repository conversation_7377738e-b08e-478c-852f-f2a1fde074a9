<head>
    <title>Download our app</title>

<script async src="https://www.googletagmanager.com/gtag/js?id=UA-123063824-1"></script>
	<script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());

	  gtag('config', 'UA-123063824-1');
	  gtag('config', 'AW-794617004');

	</script>



<!-- Event snippet for Website conversion conversion page
In your html page, add the snippet and call gtag_report_conversion when someone clicks on the chosen link or button. -->
<script>
function gtag_report_conversion(url) {
  var callback = function () {
    if (typeof(url) != 'undefined') {
      window.location = url;
    }
  };
  gtag('event', 'conversion', {
      'send_to': 'AW-794617004/hyKRCN7WhY8BEKzJ8_oC',
      'event_callback': callback
  });
  return false;
}
</script>


<script>
    gtag_report_conversion('https://play.google.com/store/apps/details?id=com.drivers4me');
</script>
<!--<script>
    if(navigator.userAgent.toLowerCase().indexOf("android") > -1) {
        if(confirm("Download app?")) {
            window.location.href= "market://details?id=com.drivers4me";
        }
}-->
</script>
</head>
<body>
 <a href='#' onclick="gtag_report_conversion('https://play.google.com/store/apps/details?id=com.drivers4me&pcampaignid=MKT-Other-global-all-co-prtnr-py-PartBadge-Mar2515-1')"><img alt='Get it on Google Play' style="width: 200px; cursor: pointer;" src='https://play.google.com/intl/en_us/badges/images/generic/en_badge_web_generic.png' data-toggle="tooltip" data-placement="bottom" title="Download our App from PlayStore and book now."/></a>

</body>
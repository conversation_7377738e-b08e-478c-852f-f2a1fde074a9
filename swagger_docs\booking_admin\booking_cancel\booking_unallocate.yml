tags:
  - Booking_admin
summary: Unallocate a driver from a booking
description: This API allows an admin to unallocate a driver from a booking and handle the corresponding cancellation logic, including penalties.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The ID of the booking to be unallocated.
  - name: reason
    in: formData
    type: integer
    required: false
    description: The reason code for unallocating the driver.
  - name: reason_details
    in: formData
    type: string
    required: false
    description: Detailed reason for the unallocation.
responses:
  200:
    description: Success response indicating the booking was successfully unallocated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Booking Unallocated Successfully
  400:
    description: Error response indicating required parameters are missing or the trip has already started.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Required parameters are missing or Trip already started
  404:
    description: Error response indicating the booking or driver was not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Booking or driver not found
  500:
    description: Error response indicating an internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Internal Server Error
@font-face
{
	font-family: 'Uni Sans Thin';
	src: url('Uni Sans Thin.otf') format("opentype");
}
@font-face
{
	font-family: 'Uni Sans Heavy';
	src: url('Uni Sans Heavy.otf') format("opentype");
}
body {
	height: 100%;
	font-family: 'Uni Sans Thin', Arial, sans-serif!important;
}
table,tbody,tr,td{
 		border-top-style: none!important;
 		border-bottom-style: none!important;
}
.popover-content {
 		background-color: rgb(183, 15, 23);
 		text-decoration: solid;
 		color: white;
 		border-radius: 5px;
 		border-color: black;
 		text-align: center;
}
td {
  	padding: 5px!important;
}
#registerUser div.form-group,#login div.form-group {
  padding-top: 1.2em;
  padding-bottom: 1.2em;
}
a {
	color: white!important;
}
ul.pagination > li.notactive > a {
	color: rgb(70,70,70)!important;			
}
ul.pagination > li.active > a {
	color: white!important;			
}
ul.nav > li.notactive > a {
	color: white!important;			
}
ul.nav > li.active > a {
	color: rgb(70,70,70)!important;			
}
ul.nav > li.open > a {
	color: rgb(70,70,70)!important;	
}
ul.dropdown-menu > li > a {
	color: rgb(70,70,70)!important;
}


.container-fluid {
	background-color: rgb(28,72,97) !important;
}
input[type="text"],[type="email"],[type="password"],[type="mobile"], #lno {
	background-color: rgb(3, 141, 162)!important;
	color: white !important;
	font-family: 'Helvetica Neue',Helvetica,Arial,sans-serif;
}
input::-webkit-input-placeholder {
color: rgb(200, 200, 200) !important;
}
 
input:-moz-placeholder { /* Firefox 18- */
color: rgb(200, 200, 200) !important;  
}
 
input::-moz-placeholder {  /* Firefox 19+ */
color: rgb(200, 200, 200) !important;  
}
 
input:-ms-input-placeholder {   
color: rgb(200, 200, 200) !important;  
}
.btn-success, .btn-success:hover {
	background-image: linear-gradient(to bottom, #346f91 0%, #1c4861 100%)!important;
	background-color: #1c4861!important;
	background-repeat: repeat-x!important;
	border-color: #1c4861!important;
}
.btn-success:active {
  color: #ffffff!important;
  background-image: linear-gradient(to bottom, #1c4861 0%, #346f91 100%)!important;
	background-color: #346f91!important;
	background-repeat: repeat-x!important;
	border-color: #1c4861!important;
}
.btn-danger, .btn-danger:hover {
	background-image: linear-gradient(to bottom, #e83029 0%, #720703 100%)!important;
	background-color: #720703!important;
	background-repeat: repeat-x!important;
	border-color: #720703!important;
}
.btn-danger:active {
  color: #ffffff!important;
  background-image: linear-gradient(to bottom, #720703 0%, #e83029 100%)!important;
	background-color: #e83029!important;
	background-repeat: repeat-x!important;
	border-color: #720703!important;
}
.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus {
	background-color: #1c4861!important;
	border-color: #1c4861!important;
}
#forgot {
	color: black!important;
}
.label-primary {
  background-color: #1c4861!important;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #1c4861!important;
}
.user {
	float: right;
	background-color: #1c4861!important;
	max-width: 200px;
	word-wrap: break-word;
	margin-bottom: 2px;
	margin-top: 2px;
	border-radius: 6px;
	color: white;
	box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border: 5px solid #1c4861;
    font-size: 12px;
    margin-top: 10px;
}
.support-personnel {
	float: left;
	background-color: rgb(230, 230, 230);
	max-width: 200px;
	word-wrap: break-word;
	margin-bottom: 2px;
	margin-top: 2px;
	border-radius: 6px;
	box-sizing: border-box;
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    border: 5px solid rgb(220, 220, 220);
    font-size: 12px;
    margin-top: 10px;
}
.panel > .panel-heading {
	background-image: linear-gradient(to bottom, #346f91 0%, #1c4861 100%)!important;
	background-color: #1c4861!important;
	background-repeat: repeat-x!important;
	border-color: #1c4861!important;
	color: white;
}
.panel > .panel-body {
	background-image: linear-gradient(to bottom, #038da2 0%, #ffffff 100%)!important;
	background-color: #038da2!important;
	background-repeat: repeat-x!important;
	border-color: #038da2!important;
}
.item {
	height: 590px;
	background-size: 100% 100%;
}
.getStarted {
	position: absolute;
	top: 70%;
	left: 45%;
	font-size: 25px!important;
	cursor: pointer;
}
/*css for 5-star rating display*/
.rating-static {
  width: 60px;
  height: 16px;
  display: block;
  background: url(star-rating.png);
  background-position: 0 0;
  background-repeat: no-repeat;
}
.navbar-brand {
	position: absolute;
}
.chat {
	float: right;
	bottom: 20px;
	right: 10px;
	position: fixed;
}
.scrollSpy {
	padding-top: 50px;
	min-height: 100%;
	height: auto!important;
	background-repeat: no-repeat;
	background-size: cover;
}
/*
*This part of the code is not in action yet
*Its purpose would be to optimize the webpage at smaller (in particular, thinner) resolutions
*/
html {
	height: 100%;
}
.keyFeature {
	opacity: 0.4;
	background: linear-gradient(to right, rgba(28,72,97,0), rgba(28,72,97,1));
	color: white;
	font-family: 'Uni Sans Heavy', Arial, sans-serif!important; 
}
.keyFeature:hover {
	opacity: 1.0;
}
.keyFeature > div {
	padding-left: 0;
}
.smallRes {

}
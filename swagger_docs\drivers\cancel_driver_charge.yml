tags:
  - Driver
summary: Retrieve cancellation charge for a driver.
description: >
  This API allows a driver to retrieve the penalty or cancellation charge that would be applied for canceling a booking, based on the booking details and time of cancellation.
parameters:
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The ID of the booking to retrieve the cancellation charge for.
    example: 12345
  - name: reason
    in: formData
    type: integer
    required: false
    description: Reason for cancellation, used to determine the cancellation category.
    example: 1
responses:
  200:
    description: Successfully retrieved cancellation charge.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        cancel_charge:
          type: array
          description: Penalty details for both user and driver.
          items:
            type: number
            example: [200.00, 100.00]
    examples:
      application/json:
        success: 1
        cancel_charge: [200.00, 100.00]
  200_a:
    description: Booking does not exist.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: "Booking does not exist"
    examples:
      application/json:
        success: -3
        message: "Booking does not exist"
  200_b:
    description: Cannot cancel after check-in.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Can not cancel after check-in"
    examples:
      application/json:
        success: -2
        message: "Can not cancel after check-in"
  401_a:
    description: Unauthorized role, not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not Driver"
  401_b:
    description: Driver account disabled.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  403:
    description: Database error occurred.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

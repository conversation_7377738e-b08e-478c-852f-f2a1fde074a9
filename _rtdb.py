#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  book_ride.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from db_config import db
from models import Bookings, <PERSON>
from firebase_admin import db as rtdb
from datetime import datetime


def _update_user_count(user_id):
    try:
        refCount = rtdb.reference('upcomingCountCustomer')
        refUpcoming = rtdb.reference('upcomingTripCustomer')
        customer_pending = db.session.query(Bookings).filter(Bookings.valid == Bookings.UNALLOCATED). \
                                            filter(Bookings.user == user_id)
        customer_upcoming = db.session.query(Bookings).filter(Bookings.valid == Bookings.ALLOCATED). \
                                            filter(Bookings.id.notin_(db.session.query(Trip.book_id))). \
                                            filter(Bookings.user == user_id)
        refCount.child(str(user_id)).set({'pending': customer_pending.count(), 'upcoming': customer_upcoming.count()})
        first_upcoming = customer_upcoming.order_by(Bookings.startdate).order_by(Bookings.starttime).first()
        if not first_upcoming:
            refUpcoming.child(str(user_id)).delete()
            return
        timestamp = datetime(year=first_upcoming.startdate.year, month=first_upcoming.startdate.month,
                              day=first_upcoming.startdate.day, hour=first_upcoming.starttime.hour,
                              minute=first_upcoming.starttime.minute, second=first_upcoming.starttime.second)
        refUpcoming.child(str(user_id)).set({'timestamp': timestamp.strftime("%Y-%m-%d %H:%M:%S")})
    except Exception as e:
        print(e)
        db.session.rollback()
    return


def _update_driver_count(driver_id):
    try:
        refCount = rtdb.reference('upcomingCountDriver')
        refUpcoming = rtdb.reference('upcomingTripDriver')
        if driver_id == 1:
            return
        driver_pending = db.session.query(Bookings).filter(Bookings.valid == Bookings.UNALLOCATED). \
                                            filter(Bookings.driver == driver_id)
        driver_upcoming = db.session.query(Bookings).filter(Bookings.valid == Bookings.ALLOCATED). \
                                            filter(Bookings.id.notin_(db.session.query(Trip.book_id))). \
                                            filter(Bookings.driver == driver_id)
        refCount.child(str(driver_id)).set({'pending': driver_pending.count(), 'upcoming': driver_upcoming.count()})
        first_upcoming = driver_upcoming.order_by(Bookings.startdate).order_by(Bookings.starttime).first()
        if not first_upcoming:
            refUpcoming.child(str(driver_id)).delete()
            return
        timestamp = datetime(year=first_upcoming.startdate.year, month=first_upcoming.startdate.month,
                              day=first_upcoming.startdate.day, hour=first_upcoming.starttime.hour,
                              minute=first_upcoming.starttime.minute, second=first_upcoming.starttime.second)
        if not refUpcoming.child(str(driver_id)).get():
            # no entry, so insert this
            refUpcoming.child(str(driver_id)).set({'timestamp': timestamp.strftime("%Y-%m-%d %H:%M:%S")})
        else:
            # if this is before saved entry insert this
            try:
                saved_ts = refUpcoming.child(str(driver_id)).get()['timestamp']
                saved_ts_dt = datetime.strptime(saved_ts, '%Y-%m-%d %H:%M:%S')
                # if this is more recent, update
                if saved_ts_dt > timestamp:
                    refUpcoming.child(str(driver_id)).set({'timestamp': timestamp.strftime("%Y-%m-%d %H:%M:%S")})
            except Exception:
                # broken entry
                refUpcoming.child(str(driver_id)).set({'timestamp': timestamp.strftime("%Y-%m-%d %H:%M:%S")})
                return
    except Exception as e:
        print(e)
        db.session.rollback()
    return


def _update_user_pending(user_id):
    #thread = Thread(target=_update_user_count, args=[user_id])
    #thread.start()
    _update_user_count(user_id)
    return


def _update_driver_pending(driver_id):
    _update_driver_count(driver_id)
    #thread = Thread(target=_update_driver_count, args=[driver_id])
    #thread.start()
    return

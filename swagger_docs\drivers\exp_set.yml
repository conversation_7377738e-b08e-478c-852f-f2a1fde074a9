tags:
  - Driver
summary: Set Driver Experience
description: >
  This endpoint allows a driver to set their experience details.
parameters:
  - name: hb_m
    in: formData
    type: string
    required: true
    description: Experience with hatchback manual vehicles
  - name: sed_m
    in: formData
    type: string
    required: true
    description: Experience with sedan manual vehicles
  - name: suv_m
    in: formData
    type: string
    required: true
    description: Experience with SUV manual vehicles
  - name: lux_m
    in: formData
    type: string
    required: true
    description: Experience with luxury manual vehicles
  - name: hb_a
    in: formData
    type: string
    required: true
    description: Experience with hatchback automatic vehicles
  - name: sed_a
    in: formData
    type: string
    required: true
    description: Experience with sedan automatic vehicles
  - name: suv_a
    in: formData
    type: string
    required: true
    description: Experience with SUV automatic vehicles
  - name: lux_a
    in: formData
    type: string
    required: true
    description: Experience with luxury automatic vehicles
responses:
  200:
    description: Driver experience added successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Added driver experience successfully"
    examples:
      application/json:
        success: 1
        message: "Added driver experience successfully"
  401_a:
    description: Unauthorized role (user is not a driver)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized role)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role"
  401_b:
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for user restriction)
          example: -2
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -2
        message: "User restricted"
  500:
    description: DB Error while setting driver experience
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for database error)
          example: -3
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -3
        message: "DB Error"

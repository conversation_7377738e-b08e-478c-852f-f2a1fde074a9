from main import app
from db_config import db, fb_db
from models import Bookings, Trip
from sqlalchemy import and_, or_
from booking_params import BookingParams

def fetch_data_from_firestore(collection_path):
    """
    Fetch data from a Firestore collection structured as trip_started > user_id > booking_id.

    :param collection_path: Path to the Firestore collection (e.g., "trip_started")
    """
    trip_started_ref = fb_db.collection(collection_path)

    # Get all user documents in the trip_started collection
    users = trip_started_ref.stream()

    for user_doc in users:
        user_id = user_doc.id
        user_bookings_ref = trip_started_ref.document(user_id).collections()
        count = db.session.query(Bookings, Trip)\
            .filter(Bookings.id == Trip.book_id)\
            .filter(Bookings.user == user_id)\
            .filter(or_(
                Trip.status != Trip.TRIP_STOPPED,
                Bookings.valid <= 0
            ))\
            .filter(Bookings.type < BookingParams.TYPE_C24)\
            .count()
        print(f"User ID: {user_id} {count}")
        if count==0:
            try:
                fb_db.collection(u'trip_started').document(str(user_id)).delete()
            except Exception as e:
                pass

if __name__ == "__main__":
    with app.app_context():
        try:
            # Example collection path
            collection_path = "trip_started"  # Replace with your actual collection path
            fetch_data_from_firestore(collection_path)
        except Exception as e:
            print(f"Error while fetching data: {e}")

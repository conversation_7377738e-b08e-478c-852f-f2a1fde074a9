$( document ).ready(function() {
    var sendOtp = true;
    var counter = 30;
    $('#sendOtp').click(function(){
        if(sendOtp){
            $('#label_mobile').attr('disabled', false);
            genOTP($('#label_mobile').val());
        }else{
            showSnackbar("Wait for "+ counter +" seconds  before resending OTP", "snackbar-danger")
        }
    }); 

    $('#deleteAccBtn').click(function(){
        if (confirm("Are you sure you want to delete your account? Deleted account cannot be recovered.") == true){
            valOTP($('#label_mobile').val(),$('#otpInput').val());
        }
    }); 
    $('#resetNumber').click(function(){
        $('#label_mobile').val("");
        $('#otpInput').val("");
        $('#label_mobile').attr('disabled', false);
        $('#otpInput').attr('disabled', true);
        $('#deleteAccBtn').attr('disabled', true);
    }); 
    function genOTP(mobile) {
        var data = new FormData()
        data.append('mobile', mobile)
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/token/otp/generate/delete',
                data: data,
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(e) {
                    console.log(e);
                    if(e.success == 1){
                        counter = 30
                        sendOtp = false;
                        var interval = setInterval(function() {
                            counter--;
                            if (counter <= 0) {
                                sendOtp = true; 
                                return;
                            }
                        }, 1000);
                        $('#label_mobile').attr('disabled', true);
                        $('#otpInput').attr('disabled', false);
                        $('#deleteAccBtn').attr('disabled', false);
                        showSnackbar("OTP sent successfully to : " + mobile.trim(), "", 2500);
                    }else{
                        $('#label_mobile').attr('disabled', false);
                        $('#otpInput').attr('disabled', true);
                        $('#deleteAccBtn').attr('disabled', true);
                        showSnackbar("OTP couldn't be sent.", "snackbar-danger")
                    }
                },
                error: function(e) {
                    $('#label_mobile').attr('disabled', false);
                    $('#otpInput').attr('disabled', true);
                    $('#deleteAccBtn').attr('disabled', true);
                    showSnackbar("OTP couldn't be sent.", "snackbar-danger")
                }

        })
    }
    function valOTP(mobile, otp) {
		var data = new FormData()
		data.append('mobile', mobile)
		data.append('otp', otp)
		$.ajax({
				type:"POST",
				url: window.location.protocol + '//' + window.location.host + '/token/otp/validate/delete',
				data: data,
				dataType: "json",
				contentType: false,
				processData: false,
				success: function(e) {
                    console.log(e);
					if (e.success == 1) {
                        if($('#label_mobile').val() == mobile){
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                            if (refresh_token) {
                                if (checkRefresh(csrf_token, refresh_token) == false) {
                                    window.location  = "/login";
                                }
                                deleteAcc(mobile) 
                            }
                        }else{
                            showSnackbar("Mobile number is changed.", "snackbar-danger")
                        }
					}
					else{
						showSnackbar("Invalid OTP. Please try again.", "snackbar-danger")
						hideLoader()
                        
                    }
				},
				error: function(e) {
					showSnackbar("Invalid OTP. Please try again.", "snackbar-danger")
					hideLoader()
				}

		  })
	}
    function checkRefresh(csrf_token, refresh_token) {
        var resp = false;
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/token/verify',
            beforeSend: function(request) {
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            async: false,
            success: function(s) {
                if (s.success != true) {
                    $.ajax({
                        type: "POST",
                        url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                        beforeSend: function(request) {
                            request.setRequestHeader('X-CSRF-Token', refresh_token);
                        },
                        async: false,
                        success: function(sr) {
                            if (sr.refresh != true) resp =  false;
                            else resp = true;
                        },
                        error: function(er) {
                            resp =  false;
                        }
                    });
                } else {
                    resp =  true;
                }
            },
            error: function(e) {
                if (e.status == 401) {
                    $.ajax({
                        type: "POST",
                        url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                        beforeSend: function(request) {
                            request.setRequestHeader('X-CSRF-Token', refresh_token);
                        },
                        async: false,
                        success: function(sr) {
                            if (sr.refresh != true) resp = false;
                            else resp =  true;
                        },
                        error: function(er) {
                            resp = false;
                        }
                    });
                } else {
                    resp =  false;
                }
            }
        });
        return;
    }
    function deleteAcc(mobile) {
        $('#deleteAccBtn').attr('disabled', true);
        $('#label_mobile').attr('disabled', true);
        $('#otpInput').attr('disabled', true);
        var data = new FormData()
        data.append('mobile', mobile)
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/delete',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(e) {
                    console.log(e);
                    if(e.success == 1){
                        $('#label_mobile').val("");
                        $('#otpInput').val("");
                        $('#label_mobile').attr('disabled', false);
                        $('#otpInput').attr('disabled', true);
                        $('#deleteAccBtn').attr('disabled', true);
                        showSnackbar("Account successfully deleted!", "", 2500);
                    }else{
                        $('#label_mobile').val("");
                        $('#otpInput').val("");
                        $('#label_mobile').attr('disabled', false);
                        $('#otpInput').attr('disabled', true);
                        $('#deleteAccBtn').attr('disabled', true);
                        showSnackbar("Account couldn't be deleted, please contact admin.", "snackbar-danger");
                    }
                },
                error: function(e) {
                    $('#label_mobile').attr('disabled', false);
                    $('#otpInput').attr('disabled', true);
                    $('#deleteAccBtn').attr('disabled', true);
                    showSnackbar("Account couldn't be deleted, please contact admin.", "snackbar-danger");
                }

        })
    }
});
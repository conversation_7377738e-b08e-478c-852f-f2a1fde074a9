tags:
  - Affiliate
summary: Get Affiliate Representative Logs
description: >
  Retrieves the logs for a specific affiliate representative. The logs include details such as the action performed (Created or Edited),
  the admin who made the change, what changes were made, and the timestamp of the action.
parameters:
  - in: query
    name: rep_id
    type: integer
    required: true
    description: The ID of the affiliate representative for which logs are to be retrieved.
    example: "101"
  - in: query
    name: regions
    type: string
    required: true
    description: >
      Comma-separated region codes that the representative should have access to.
    example: "0,1"

responses:
  200:
    description: Logs retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        logs:
          type: array
          items:
            type: object
            properties:
              rep_id:
                type: string
                example: "101"
              action:
                type: string
                example: "Created"
              changed_by:
                type: integer
                example: 1
              changed_by_name:
                type: string
                example: "Admin Name"
              changes_made:
                type: string
                example: "Updated mobile number"
              created_at:
                type: string
                example: "15 Apr 2025, 14:30"
              oldvalue:
                type: string
                example: "9876543210"
              newvalue:
                type: string
                example: "0123456789"
  400:
    description: Missing or invalid input.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "rep_id is required"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        message:
          type: string
          example: "Internal Server Error"

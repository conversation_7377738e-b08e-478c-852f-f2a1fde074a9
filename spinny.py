#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  affiliate.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import sys
sys.path.insert(1, '/home/<USER>/drivers4me-web-prod')

from flask import jsonify
import math
from sqlalchemy import exc,union_all
from sqlalchemy.sql import func, or_,desc,asc,and_,exists,case
from flask import current_app as app
from book_ride import DriverBook, create_pending_entry, create_pricing_entry
from booking_params import Rating
import jsonpickle
import os, shutil
import pytz
from booking_params import BookingParams, Regions
from _utils_booking import get_car_type
from _ops_message import send_slack_msg
import _sms
from _fcm import send_fcm_msg_driver,send_fcm_msg
from _utils import  strfdelta, get_pic_url,compute_driver_wallet, convert_to_ist
from _utils_acc import account_enabled,get_driver_user_id
import uuid, json
from _rtdb import _update_user_pending, _update_driver_pending
from price import convert_to_semihours, Price
from payments import PaymentType
from datetime import datetime, timedelta
from price_spinny_kolkata import PriceSpinnyKolkata
from price_spinny_delhi import PriceSpinnyDelhi
from price_spinny_hyderabad import PriceSpinnyHyderabad
from price_spinny_bangalore import PriceSpinnyBangalore
from _utils_booking import get_dist_on_map,fetch_booking_trip,get_b2b_book_code
from models import DriverSearch, SpinnyRep, SpinnyBookings, BookDest, DriverDetails, SpinnyPic, BookingCancelled, BookingAlloc,DriverCancelled,DriverTrans
from models import Bookings, Trip, Drivers, Users, UserTrans, BookPending, DriverPermaInfo, BookPricing, SpinnyRep, TripLog
from models import db, SpinnyUtil
from register_driver import upload_pic

def convert_timedelta(duration):
    try:
        days, seconds = duration.days, duration.seconds
    except AttributeError:
        days = 0
        seconds = duration.seconds
    hours = days * 24 + seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = (seconds % 60)
    if hours < 10:
        if hours < 0: hours = 0
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    if seconds < 10:
        seconds = '0' + str(seconds)

    return str(hours), str(minutes), str(seconds)


def convert_mindelta(minutes):
    hours = minutes // 60
    minutes = minutes % 60
    if hours < 10:
        if hours < 0: hours = 0
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    seconds = "00"

    return str(hours), str(minutes), str(seconds)


class SpinnyPrice:
    base = 175
    ot_rate = 78
    os_rate = 150


class BookingSpinny:
    def __init__(self, bookingid, bookingcode, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile="", driver_pic=None, loc='N/A', trip_type=1, dest_lat=0, dest_long=0, dest_loc="", comment=""):
        self.success = 1
        self.bookingid = bookingid
        self.bookingcode = bookingcode
        self.id = id
        self.appt = appt
        self.name = name
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.endtime = str(endtime)
        self.enddate = str(enddate)
        self.dur = str(dur)
        self.car_type = car_type
        if driver_pic:
            self.driver_pic = get_pic_url(driver_pic)
        self.loc = loc
        self.lat = lat
        self.long = lng
        self.mobile = mobile
        self.trip_type = trip_type
        self.dest_lat = dest_lat
        self.dest_long = dest_long
        self.dest_loc = dest_loc
        self.veh_no = veh_no
        self.veh_model = veh_model
        self.comment = comment


class BookingSpinnyOngoing(BookingSpinny):
    def __init__(self, bookingid, bookingcode, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile="", driver_pic=None, loc='N/A', trip_type=1, dest_lat=0, dest_long=0, dest_loc="", comment="", did=1):
        super().__init__(bookingid, bookingcode, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile, driver_pic, loc, trip_type, dest_lat, dest_long, dest_loc, comment)
        self.driver_id = did


class BookingSpinnyPast(BookingSpinny):
    def __init__(self, bookingid, bookingcode, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile="", driver_pic=None, loc='N/A', trip_type=1, dest_lat=0, dest_long=0, dest_loc="", comment="",
                sch_date=None, sch_time=None, ot="00:00:00", file_url="", file_count=0):
        super().__init__(bookingid, bookingcode, id, appt, name, startdate, starttime, enddate, endtime, dur, car_type, lat, lng, rep_name, rep_mob,
                drop_spoc, veh_no, veh_model, mobile, driver_pic, loc, trip_type, dest_lat, dest_long, dest_loc, comment)
        self.sch_startdate = sch_date
        self.sch_starttime = sch_time
        self.ot = ot
        self.file_url = file_url
        self.file_count = file_count

# pylint: disable=unused-argument
def calc_spinny_price(book_id, book_region, book_delta, real_delta, est,
                        book_starttime, book_stoptime,
                        trip_starttime, trip_stoptime,
                        startdate, enddate):
    if book_region == Regions.REGN_KOLKATA:
        return PriceSpinnyKolkata.get_trip_price(est, book_starttime, book_stoptime,
                       trip_starttime, trip_stoptime, startdate, enddate)
    elif book_region == Regions.REGN_DELHI:
        return PriceSpinnyDelhi.get_trip_price(est, book_starttime, book_stoptime,
                       trip_starttime, trip_stoptime, startdate, enddate)
    elif book_region == Regions.REGN_HYDERABAD:
        return PriceSpinnyHyderabad.get_trip_price(est, book_starttime, book_stoptime,
                       trip_starttime, trip_stoptime, startdate, enddate)
    elif book_region == Regions.REGN_BANGALORE:
        return PriceSpinnyBangalore.get_trip_price(est, book_starttime, book_stoptime,
                       trip_starttime, trip_stoptime, startdate, enddate)
    else:
        overtime = (real_delta - book_delta)
        if overtime < 0:
            return SpinnyPrice.base
        return SpinnyPrice.base + overtime * SpinnyPrice.ot_rate / 60

def spinny_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat,
            dest_reflong, dest_locname, book_time, book_date,appt, veh_no,
            veh_model, spinny_type, type_detail, bus_cat, bus_func,
            dspoc_mob, rid, region=Users.REGN_KOLKATA, src_spoc_name = '',
            app=True):
    now = datetime.utcnow()
    search_id = uuid.uuid4().urn[9:]
    if car_auto:
        car_type = 3
    else:
        car_type = 0
    dur = "02:00:00"
    dur_t = datetime.strptime(dur, "%H:%M:%S")
    rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == rid).first()
    #update name if already present and name is different
    if src_spoc_name and rep.name != src_spoc_name:
        rep.name = src_spoc_name
    rep_id = rep.id
    book_type = BookingParams.TYPE_SPINNY
    if not dspoc_mob:
        try:
            dspoc_mob = rep_id.mobile
        except Exception:
            dspoc_mob = 0
    dist =  get_dist_on_map(reflat, reflong, dest_reflat, dest_reflong)
    search_entry = DriverSearch(search_id, user, car_type, reflat, reflong,
                                book_time, book_date, dur, now, book_type, 0,
                                dist=dist, region=region, source="spinny")
    db.session.add(search_entry)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail and app:
        return jsonify({'success': -1, 'reason': fail_reason})
    elif fail and not app:
        return -1
    end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day,
                                  search_entry.time.hour, search_entry.time.minute, search_entry.time.second) +
                timedelta(search_entry.days, dur_t.hour * 3600 +
                          dur_t.minute * 60 + dur_t.second))

    cur_price = Price.get_price(book_type, dur_t.hour + dur_t.minute/60,
                                search_entry.time, end_time, search_entry.dist, search_entry.car_type,
                                search_entry.date, end_time.date(), 0, insurance=search_entry.insurance,
                                insurance_num=search_entry.insurance_num, city=region)
    print("Cur price", cur_price, "dist", search_entry.dist)
    # Update region when fixed
    booking = Bookings(user, search_id, BookingParams.BOOKING_DUMMY_ID, search_entry.reflat, search_entry.reflong,
                                search_entry.time,
                                search_entry.date, str(search_entry.dur), end_time.time(), end_time.date(),
                                cur_price[0], 0, loc_name, search_entry.car_type, search_entry.type, search_entry.days, payment_type=PaymentType.PAY_BILL,
                                region=region)

    db.session.add(booking)
    db.session.flush()
    booking.code = get_b2b_book_code(booking.id)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except exc.SQLAlchemyError as excp:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(excp)
    try:
        temp_driver = db.session.query(Users, Drivers).filter(
                Drivers.user == Users.id).filter(
                Drivers.id == BookingParams.BOOKING_DUMMY_ID).first()
        driver_book_entry = DriverBook(temp_driver[1].id, temp_driver[0].fname, temp_driver[0].lname, temp_driver[0].mobile, temp_driver[1].pic,
                                       -1, -1, temp_driver[1].rating, cur_price, 0)
        if not BookingParams.get_no_broadcast():
            pending_state = BookPending.BROADCAST
        else:
            pending_state = BookPending.SUPPRESSED
        create_pending_entry(driver_book_entry, booking, pending_state)
        create_pricing_entry(driver_book_entry, booking)
    except exc.SQLAlchemyError as excp:
        db.session.rollback()
        fail = True
        fail_reason = "Pending"
        print("Exception in pending create (ZC)", str(excp))

    if fail and app:
        return jsonify({'success': -1, 'reason': fail_reason})
    elif fail and not app:
        return -1

    driver_list = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.alloc == DriverPermaInfo.ALLOC_SPINNY).all()
    for d in driver_list:
        pending = BookPending(booking.id, d.driver_id,  1)
        db.session.add(pending)
    dest = BookDest(booking.id, dest_reflat, dest_reflong, dest_locname)
    db.session.add(dest)
    spinny_booking = SpinnyBookings(booking=booking.id, appt=appt, no=veh_no, model=veh_model, type=spinny_type, drop_mob=dspoc_mob,
                                rep=rep_id, region=region, dist=dist, type_detail=type_detail, bus_cat=bus_cat, bus_func=bus_func)
    db.session.add(spinny_booking)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail and app:
        return jsonify({'success': -1, 'reason': fail_reason})
    elif fail and not app:
        return -1
    if spinny_type == 1:
        book_type = "Pickup"
    else:
        book_type = "Home Delivery"
    msg_content = "#" + str(booking.id) + ": Spinny added a new booking of type " + book_type + " from " + loc_name + \
                    " to " + dest_locname + ". Car model is " + veh_model + " and number is " + \
                    veh_no + ". The date is " + str(book_date) + " and time is " + str(book_time) + "."
    try:
        send_slack_msg(1, msg_content)
    except Exception as ex:
        print(ex)
    if not app:
        return 1
    return jsonify({'success': 1})

def spinny_affiliate_book(user, car_auto, reflat, reflong, loc_name,  dest_reflat,
            dest_reflong, dest_locname, book_time, book_date,appt, veh_no,
            veh_model, spinny_type, type_detail, bus_cat, bus_func,
            dspoc_mob, src_spoc_mobile, region=Users.REGN_KOLKATA, src_spoc_name = '', dest_spoc_name = '',
            app=True):
    try:
        now = datetime.utcnow()
        search_id = uuid.uuid4().urn[9:]
        if car_auto:
            car_type = 3
        else:
            car_type = 0
        dur = "02:00:00"
        dur_t = datetime.strptime(dur, "%H:%M:%S")
        rep = db.session.query(SpinnyRep).filter(SpinnyRep.mobile == src_spoc_mobile).first()
        rep_obj =''
        if not rep:
            new_spoc_entry = SpinnyRep(src_spoc_name, user, src_spoc_mobile, region)
            try:
                db.session.add(new_spoc_entry)
                db.session.commit()
            except Exception as e:
                return jsonify({
                    "message": "Failed creating booking - DB error.",
                    "result": "FAILURE",
                    "status": 500
                }), 500
            rep_obj = new_spoc_entry
            rep_id = rep_obj.id
            # o_rep = db.session.query(SpinnyRep).filter(SpinnyRep.owner_id == user).first()
            # if not o_rep:
            #     rep_id = 0
            # else:
            #     rep_obj = o_rep
            #     rep_id = o_rep.id
        else:
            #update name if already present and name is different
            if src_spoc_name and rep.name != src_spoc_name:
                rep.name= src_spoc_name
            rep_obj = rep
            rep_id = rep.id
        book_type = BookingParams.TYPE_SPINNY
        if not dspoc_mob:
            try:
                dspoc_mob = rep_obj.mobile
            except Exception:
                dspoc_mob = 0
        dist =  get_dist_on_map(reflat, reflong, dest_reflat, dest_reflong)
        search_entry = DriverSearch(search_id, user, car_type, reflat, reflong,
                                    book_time, book_date, dur, now, book_type, 0,
                                    dist=dist, region=region, source="spinny")
        db.session.add(search_entry)
        fail = False
        fail_reason = ""
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            fail = True
            fail_reason = "DB Error"
            print(str(e))
            return jsonify({
                    "message": "Failed creating booking - DB error.",
                    "result": "FAILURE",
                    "status": 500
                }), 500
        end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day,
                                    search_entry.time.hour, search_entry.time.minute, search_entry.time.second) +
                    timedelta(search_entry.days, dur_t.hour * 3600 +
                            dur_t.minute * 60 + dur_t.second))

        cur_price = Price.get_price(book_type, dur_t.hour + dur_t.minute/60,
                                    search_entry.time, end_time, search_entry.dist, search_entry.car_type,
                                    search_entry.date, end_time.date(), 0, insurance=search_entry.insurance,
                                    insurance_num=search_entry.insurance_num, city=region)
        print("Cur price", cur_price, "dist", search_entry.dist)
        # Update region when fixed
        booking = Bookings(user, search_id, BookingParams.BOOKING_DUMMY_ID, search_entry.reflat, search_entry.reflong,
                                    search_entry.time,
                                    search_entry.date, str(search_entry.dur), end_time.time(), end_time.date(),
                                    cur_price[0], 0, loc_name, search_entry.car_type, search_entry.type, search_entry.days, payment_type=PaymentType.PAY_BILL,
                                    region=region)
        db.session.add(booking)
        db.session.flush()
        booking.code = get_b2b_book_code(booking.id)
        fail = False
        fail_reason = ""
        try:
            db.session.commit()
        except exc.SQLAlchemyError as excp:
            db.session.rollback()
            fail = True
            fail_reason = "DB Error"
            print(excp)
        try:
            temp_driver = db.session.query(Users, Drivers).filter(
                    Drivers.user == Users.id).filter(
                    Drivers.id == BookingParams.BOOKING_DUMMY_ID).first()
            driver_book_entry = DriverBook(temp_driver[1].id, temp_driver[0].fname, temp_driver[0].lname, temp_driver[0].mobile, temp_driver[1].pic,
                                        -1, -1, temp_driver[1].rating, cur_price, 0)
            if not BookingParams.get_no_broadcast():
                pending_state = BookPending.BROADCAST
            else:
                pending_state = BookPending.SUPPRESSED
            create_pending_entry(driver_book_entry, booking, pending_state)
            create_pricing_entry(driver_book_entry, booking)
        except exc.SQLAlchemyError as excp:
            db.session.rollback()
            fail = True
            fail_reason = "Pending"
            print("Exception in pending create (ZC)", str(excp))
            return jsonify(jsonify({
                    "message": "Pending, something went wrong - DB error.",
                    "result": "FAILURE",
                    "status": 500
                })), 500

        driver_list = db.session.query(DriverPermaInfo).filter(DriverPermaInfo.alloc == DriverPermaInfo.ALLOC_SPINNY).all()
        for d in driver_list:
            pending = BookPending(booking.id, d.driver_id,  1)
            db.session.add(pending)
        dest = BookDest(booking.id, dest_reflat, dest_reflong, dest_locname)
        db.session.add(dest)
        spinny_booking = SpinnyBookings(booking=booking.id, appt=appt, no=veh_no, model=veh_model, type=spinny_type, drop_mob=dspoc_mob,
                                    rep=rep_id, region=region, dist=dist, type_detail=type_detail, bus_cat=bus_cat, bus_func=bus_func)
        db.session.add(spinny_booking)

        fail = False
        fail_reason = ""
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            fail = True
            fail_reason = "DB Error"
            print(e)
            return jsonify({
                    "message": "Failed creating booking - DB error.",
                    "result": "FAILURE",
                    "status": 500
                }), 500
        if spinny_type == 1:
            book_type = "Pickup"
        else:
            book_type = "Home Delivery"
        msg_content = "#" + str(booking.id) + ": Spinny added a new booking of type " + book_type + " from " + loc_name + \
                        " to " + dest_locname + ". Car model is " + veh_model + " and number is " + \
                        veh_no + ". The date is " + str(book_date) + " and time is " + str(book_time) + "."
        try:
            send_slack_msg(1, msg_content)
        except Exception as ex:
            print(ex)
        if not app:
            return 1
        return jsonify({
                "reference_id": appt,
                "book_id": booking.id,
                "track_link": "https://www.drivers4me.com/track/booking/" + str(booking.id),
                "result": "SUCCESS",
                "status": 201
            }), 201
    except Exception as e:
        print(str(e))
        return jsonify({
            "reference_id": appt,
            "result": "Internal server error",
            "status": 500
        }), 500

def spinny_cancel_new(booking_id, user,reason,reason_detail):
    book_obj = db.session.query(Bookings).filter(Bookings.id == booking_id)
    booking = book_obj.first()
    if booking is None or booking.type != BookingParams.TYPE_SPINNY:
        return jsonify({
                "status": 404,
                "result": "FAILURE",
                "message": "Booking Not Found"
            }), 404
    trip = fetch_booking_trip(booking_id).first()
    is_ontrip = trip is not None
    if is_ontrip:
            return jsonify({
                "status": 409,
                "result": "FAILURE",
                "message": "Trip has been already started cannot cancel"
            }), 409
    if booking.valid < Bookings.UNALLOCATED:
        return jsonify({
                "status": 404,
                "result": "FAILURE",
                "message": "Booking has been already cancelled"
            }), 404
    try:
        # Update booking status
        book_obj.update({Bookings.valid: Bookings.CANCELLED_USER})
        book_obj.update({Bookings.cancelled_dt: datetime.utcnow()})
        if booking.valid == 0: #if trip is unallocated condition
            bc = BookingCancelled(
                user=user,
                cancel_source=BookingCancelled.SRC_USER,
                booking=booking.id,
                uid=booking.user,
                did=booking.driver,
                penalty_user=0,
                penalty_driver=0,
                rsn=reason,
                reason_detail=reason_detail
            )
            db.session.add(bc)
            db.session.commit()
            return jsonify({
            "status": 200,
            "cancel_charge": 0,
            "result": "SUCCESS",
            "message": "Booking has been successfully cancelled"
            }), 200
        # Calculate start time
        starttime = datetime(
            booking.startdate.year, booking.startdate.month,
            booking.startdate.day, booking.starttime.hour,
            booking.starttime.minute, booking.starttime.second
        )
        _update_driver_pending(booking.driver)
        # Calculate penalties
        penalty, level = Price.get_cancel_ch(datetime.utcnow(), starttime, city=booking.region, cancel_cat=reason, is_ontrip=is_ontrip)
        user_fine, driver_fine = penalty
        last_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking.id).filter(BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first()
        if last_alloc_time:
            last_alloc_time=last_alloc_time.timestamp
        if (last_alloc_time and reason in BookingCancelled.WAIVER
            and datetime.utcnow() - last_alloc_time < BookingCancelled.FORGIVE_DELTA or booking.driver == 1):
            driver_fine=0
        # Handle driver fine and transactions
        dt_id = None
        if driver_fine > 0:
            dc = DriverCancelled(booking.driver, booking.id, driver_fine)
            db.session.add(dc)
            driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver).first()
            wallet, withdrawable = compute_driver_wallet(driver_details, driver_fine)
            dt = DriverTrans(
                booking.driver, -driver_fine * 100,
                wall_a=wallet, wall_b=driver_details.wallet,
                with_a=withdrawable, with_b=driver_details.withdrawable,
                method=f"Cancellation: Booking {booking.code}",
                status=DriverTrans.COMPLETED, stop=True
            )
            db.session.add(dt)
            db.session.commit()
            dt_id = dt.id
            db.session.query(DriverDetails).filter(
                DriverDetails.driver_id == booking.driver
            ).update({
                DriverDetails.owed: DriverDetails.owed + driver_fine,
                DriverDetails.wallet: wallet,
                DriverDetails.withdrawable: withdrawable
            })
        else:
            dc = DriverCancelled(booking.driver, booking.id, 0)
            db.session.add(dc)
        # Send notification to the driver
        try:
            reason_details_dict_driver = {
                "22": "because you denied the booking.",
                "23": "because the wait time was too long."
            }
            reason_detail_message_driver = reason_details_dict_driver.get(str(reason))
            if driver_fine > 0:
                send_fcm_msg_driver(
                    booking.driver,
                    title=f"Cancellation of booking #{booking.code}",
                    smalltext=f"Spinny cancelled the booking #{booking.code}",
                    bigtext=f"Spinny cancelled the booking #{booking.code} allocated to you {reason_detail_message_driver}. "
                            f"You were charged a penalty of ₹{driver_fine}."
                )
            else:
                send_fcm_msg_driver(
                    booking.driver,
                    title=f"Cancellation of booking #{booking.code}",
                    smalltext=f"Spinny cancelled the booking #{booking.code}",
                    bigtext=f"Spinny cancelled the booking #{booking.code} allocated to you."
                )
        except Exception:
            pass
        # Add booking cancellation
        bc = BookingCancelled(
            user=user,
            cancel_source=BookingCancelled.SRC_USER,
            booking=booking.id,
            uid=booking.user,
            did=booking.driver,
            penalty_user=user_fine,
            penalty_driver=driver_fine,
            rsn=reason,
            reason_detail=reason_detail,
            dtransid=dt_id
        )
        db.session.add(bc)
        db.session.commit()
        return jsonify({
            "status": 200,
            "result": "SUCCESS",
            "cancel_charge": driver_fine,
            "message": "Booking has been successfully cancelled"
        }),200
    except Exception as e:
        db.session.rollback()
        return jsonify({
                "status": 500,
                "result": "FAILURE",
                "message": "Failed to cancel booking"
            }), 500

def get_spinny_book_state(booking_id):
    try:
        action_mapping = {
            TripLog.ACTION_INIT: 2,
            TripLog.ACTION_REACHED_SRC: 3,
            TripLog.ACTION_START: 4,
            TripLog.ACTION_START_PIC: 4,
            TripLog.ACTION_REACHED_DEST: 5,
            TripLog.ACTION_STOP: 6,
            TripLog.ACTION_STOP_PIC: 6
        }
        book_obj = db.session.query(Bookings).filter(Bookings.id == booking_id)
        booking = book_obj.first()
        if booking is None or booking.type != BookingParams.TYPE_SPINNY:
            return jsonify({
                    "status": 404,
                    "result": "FAILURE",
                    "message": "Booking Not Found"
                }), 404
        trip_logs = (
            db.session.query(
                TripLog.timestamp.label('timestamp'),
                func.concat(Users.fname, " ", Users.lname).label('driver_name'),
                Users.mobile.label('driver_mobile'),
                case(
                    *[(TripLog.action == key, value) for key, value in action_mapping.items()],
                    else_=None  # Default value if no match is found
                ).label('booking_state'),
            )
            .join(Users, TripLog.driver_user == Users.id)
            .filter(TripLog.booking_id == booking_id)
        )
        booking_cancels = (
        db.session.query(
            BookingCancelled.timestamp.label('timestamp'),
            case(
                (BookingCancelled.did == 1, "-"),  # Pass individual conditions as positional arguments
                else_=func.concat(Users.fname, " ", Users.lname)
            ).label("driver_name"),
            case(
                (BookingCancelled.did == 1, "-"),  # Pass individual conditions as positional arguments
                else_=Users.mobile
            ).label("driver_mobile"),
            case(
                (BookingCancelled.cancel_source == BookingCancelled.SRC_USER, -1),  # Pass individual conditions
                (BookingCancelled.cancel_source == BookingCancelled.SRC_DRIVER, 8),
                else_=-2
            ).label("booking_state"),
        )
        .join(Drivers, BookingCancelled.did == Drivers.id)
        .join(Users, Users.id == Drivers.user)
        .filter(BookingCancelled.booking == booking_id)
        )

        booking_allocs = (
            db.session.query(
                BookingAlloc.timestamp.label('timestamp'),
                func.concat(Users.fname, " ", Users.lname).label('driver_name'),
                Users.mobile.label('driver_mobile'),
                db.literal(1).label('booking_state'),
            )
        .join(Drivers, BookingAlloc.driver_id == Drivers.id)  # Join BookingCancelled to Drivers
            .join(Users, Users.id == Drivers.user)  # Join Drivers to Users
            .filter(BookingAlloc.booking_id == booking_id)
        )
        combined_logs = union_all(trip_logs, booking_allocs, booking_cancels).alias('combined_logs')  # Add alias here

        # Fetch and sort combined logs
        booking_history = (
            db.session.query(
                combined_logs.c.timestamp.label("timestamp"),
                combined_logs.c.booking_state.label("booking_state"),
                combined_logs.c.driver_name.label("driver_name"),
                combined_logs.c.driver_mobile.label("driver_mobile"),
            )
            .order_by(desc(combined_logs.c.timestamp))
            .all()
            )
        filtered_logs = []
        last_state = None

        for book in booking_history:
            if book.booking_state in [4, 6]:
                # Only add the log if the state has changed from the previous log
                if book.booking_state != last_state:
                    filtered_logs.append(book)
                    last_state = book.booking_state
            else:
                filtered_logs.append(book)
                last_state = book.booking_state

        booking_history_list = [
        {
        "timestamp": convert_to_ist(book.timestamp).strftime("%Y-%m-%d %H:%M:%S"),
        "booking_state": book.booking_state,
        "driver_name": book.driver_name,
        "driver_mobile": book.driver_mobile,
        }
        for book in filtered_logs
        ]

        driver_search = (
        db.session.query(DriverSearch.timestamp.label("timestamp"))
        .filter(DriverSearch.id == booking.search_key)
        .first()
            )

        if driver_search :
            initial_entry = {
            "timestamp": convert_to_ist(driver_search.timestamp).strftime("%Y-%m-%d %H:%M:%S"),
            "booking_state": 0,
            "driver_name": '-',
            "driver_mobile": '-',
            }
            booking_history_list.append(initial_entry)

        driver_user=get_driver_user_id(booking.driver)
        driver_details = db.session.query(Users).filter(Users.id == driver_user).first()

        trip = fetch_booking_trip(booking_id).first()
        is_ontrip = trip is not None
        book_cancel = db.session.query(BookingCancelled) \
                .filter(BookingCancelled.booking == booking_id) \
                .order_by(BookingCancelled.id.desc()) \
                .first()
        reason_detail_mapping = SpinnyBookings.REASON_DETAIL_MAPPING
        reason_cancel=""
        if book_cancel:
            if book_cancel.reason in reason_detail_mapping: # means if b2b reason then show them as it is but for old reasons of D4M cancel show 'BOOKING_CANCELLED_BY_D4M' reason
                    reason_cancel = reason_detail_mapping.get(book_cancel.reason, 'OTHER_REASON')
            else:
                    reason_cancel = 'BOOKING_CANCELLED_BY_D4M'
        if booking.valid== Bookings.UNALLOCATED and book_cancel:
            if book_cancel.cancel_source ==  BookingCancelled.SRC_DRIVER:
                 return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":8,
                        "cancel_reason": reason_cancel,
                        "driver_details": {
                        "driver_name": driver_details.get_name() if driver_details else "-",
                        "driver_mobile": driver_details.mobile  if driver_details else "-",
                    },
                    "booking_history": booking_history_list,
                        "booking_status":'Booking Unallocated',
                        'timestamp':convert_to_ist(book_cancel.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
            elif book_cancel.cancel_source ==  BookingCancelled.SRC_ADMIN:
                return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":-2,
                        "cancel_reason": reason_cancel,
                        "driver_details": {
                        "driver_name": driver_details.get_name() if driver_details else "-",
                        "driver_mobile": driver_details.mobile  if driver_details else "-",
                    },
                    "booking_history": booking_history_list,
                        "booking_status":'Booking Unallocated by D4M',
                        'timestamp':convert_to_ist(book_cancel.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200

        if booking.valid== Bookings.UNALLOCATED :
            return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":0,
                    "booking_status":'Booking Created, not allocated to driver',
                     "booking_history": booking_history_list,
                    'timestamp':convert_to_ist(driver_search.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
        elif booking.valid== Bookings.ALLOCATED:
            booking_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking_id)
            if not is_ontrip:
                return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":1,
                    "driver_details": {
                        "driver_name": driver_details.get_name(),
                        "driver_mobile": driver_details.mobile
                    },
                    "booking_status":'Booking allocated to Driver',
                     "booking_history": booking_history_list,
                    'timestamp':convert_to_ist(booking_alloc_time.first().timestamp).strftime("%Y-%m-%d %H:%M:%S") if booking_alloc_time else None
                    }), 200
            else:
                trip_log = db.session.query(TripLog).filter(TripLog.booking_id == booking_id).order_by(desc(TripLog.timestamp)).first()
                timestamp_last_state = convert_to_ist(trip_log.timestamp).strftime("%Y-%m-%d %H:%M:%S") if trip_log else None
                if trip.status==Trip.TRIP_INIT:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":2,
                    "booking_status":'Driver On the Way',
                   "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status==Trip.TRIP_REACHED_SRC:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":3,
                    "driver_details": {
                        "driver_name": driver_details.get_name(),
                        "driver_mobile": driver_details.mobile
                    },
                    "booking_status":'Driver Reached Source Location - Check In',
                    "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status in [Trip.TRIP_START_PIC, Trip.TRIP_STARTED]:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":4,
                    "booking_status":'OnGoing Trip',
                    "driver_details": {
                        "driver_name": driver_details.get_name(),
                        "driver_mobile": driver_details.mobile
                    },
                    "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status==Trip.TRIP_REACHED_DEST:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "driver_details": {
                        "driver_name": driver_details.get_name(),
                        "driver_mobile": driver_details.mobile
                    },
                    "booking_state":5,
                     "booking_history": booking_history_list,
                    "booking_status":'Driver Reached Destination Location',
                    'timestamp':timestamp_last_state
                    }), 200
                elif trip.status in [Trip.TRIP_STOPPED, Trip.TRIP_STOP_PIC]:
                    return jsonify({
                    "status": 200,
                    "result": "SUCCESS",
                    "booking_state":6,
                    "driver_details": {
                        "driver_name": driver_details.get_name(),
                        "driver_mobile": driver_details.mobile
                    },
                    "booking_status":'Trip Completed',
                     "booking_history": booking_history_list,
                    'timestamp':timestamp_last_state
                    }), 200
        elif booking.valid <  Bookings.UNALLOCATED:
            if booking.valid ==  Bookings.CANCELLED_USER:
                return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":-1,
                        "cancel_reason": reason_cancel,
                        "driver_details": {
                        "driver_name": driver_details.get_name() if driver_details else "-",
                        "driver_mobile": driver_details.mobile  if driver_details else "-",
                         },
                         "booking_history": booking_history_list,
                        "booking_status":'Cancelled by Spinny',
                        'timestamp':convert_to_ist(book_cancel.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
            else:
                return jsonify({
                        "status": 200,
                        "result": "SUCCESS",
                        "booking_state":-2,
                        "cancel_reason": reason_cancel,
                        "driver_details": {
                        "driver_name": driver_details.get_name() if driver_details else "-",
                        "driver_mobile": driver_details.mobile  if driver_details else "-",
                        },
                        "booking_status":'Cancelled by D4M',
                        "booking_history": booking_history_list,
                        'timestamp': convert_to_ist(book_cancel.timestamp).strftime("%Y-%m-%d %H:%M:%S")
                    }), 200
    except Exception as e:
        return jsonify({
                "status": 500,
                "message": "Internal Server Error"
            }), 500

def add_rep(user, rep_mob, region):
    rep = SpinnyRep(user=user, mob=rep_mob, regn=region)
    db.session.add(rep)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"

    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})

    return jsonify({'success': 1})


def spinny_start_trip(spinny_book_id, driver_user, time_start):
    book = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == spinny_book_id). \
                        first().ref

    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver) \
        .filter(Bookings.valid > 0).first()
    if booking:
        booking_id = booking.id
    else:
        return jsonify({'success': -3}), 201
    already_trip = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
    if already_trip:
        return jsonify({'success': -2}), 200  # trip exists
    new_trip = Trip(booking_id, time_start, status=Trip.TRIP_STARTED)
    db.session.add(new_trip)

    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    target_user = db.session.query(Users).filter(Users.id == booking.user).first()
    start_time_ist = time_start + _sms.IST_OFFSET_TIMEDELTA
    msg_content = "Spinny trip, id #" + str(booking.id) + " was started by " + \
                  driver_name + " at approximately " + start_time_ist.strftime("%I:%M %p %d/%m/%Y")

    send_slack_msg(2, msg_content)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"

    if fail:
        return jsonify({'success': -4, 'reason': fail_reason})

    return jsonify({'success': 1})


def _spinny_newstop_trip(spinny_book_id, driver_user, time_stop):
    book = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == spinny_book_id). \
                        first().ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    est = booking.estimate
    delta = time_stop - cur_trip.starttime
    # Bug - only works for 60min calcs
    orig_delta = convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0]
    time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
    estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.HOUR_RATIO + \
                     math.ceil((booking.dur.minute * Price.HOUR_RATIO) / 60)
    # Now calculate price
    price = est
    price = calc_spinny_price(book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=est,
                                     book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                     trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
                                     startdate=cur_trip.starttime.date(), enddate=time_stop.date())
    if price > est:
        surcharge = True
    else:
        surcharge = False
    d_hr, d_min, d_sec = convert_timedelta(delta)
    dur = d_hr + ':' + d_min + ':' + d_sec
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: -price*100})
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: time_stop, Trip.price: price, Trip.status: Trip.TRIP_STOPPED})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta + d_hr})

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1})

    try:
        stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
        msg_content = "Spinny trip, id #" + str(booking.id) + " of " + \
                      driver_name + " was re-stopped at approximately " + \
                      stop_time_ist.strftime("%I:%M %p %d/%m/%Y")
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1, 'price': 0, 'dur': dur, 'surcharge': int(surcharge)})


def _spinny_restart_trip(spinny_book_id, driver_user):
    book = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == spinny_book_id). \
                        first().ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    orig_delta = convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0]
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: 0})
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: None, Trip.price: 0, Trip.status: Trip.TRIP_STARTED})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count - 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count - 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating - Rating.RATING_DEFAULT})
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1})

    try:
        msg_content = "Spinny trip, id #" + str(booking.id) + " of " + \
                      driver_name + " was restarted."
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1})


def spinny_stop_trip(spinny_book_id, driver_user, time_stop):
    book = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == spinny_book_id). \
                        first().ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    if cur_trip.price != 0 or cur_trip.endtime:
        calc_dur = strfdelta(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
        return jsonify({'success': 1, 'price': 0, 'dur': calc_dur})
    est = booking.estimate
    delta = time_stop - cur_trip.starttime
    # Bug - only works for 60min calcs
    time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
    estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.HOUR_RATIO + \
                     math.ceil((booking.dur.minute * Price.HOUR_RATIO) / 60)
    # Now calculate price
    price = est
    price = calc_spinny_price(book_id=booking.id, book_region=booking.region, book_delta=estimate_delta, real_delta=time_delta, est=est,
                                     book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                     trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
                                     startdate=cur_trip.starttime.date(), enddate=time_stop.date())
    if price > est:
        surcharge = True
    else:
        surcharge = False
    d_hr, d_min, d_sec = convert_timedelta(delta)
    dur = d_hr + ':' + d_min + ':' + d_sec
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        trans = UserTrans(booking.user, -price*100, "Spinny trip", UserTrans.COMPLETED, 0, stop=True)
        db.session.add(trans)
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: time_stop,
                                        Trip.trans: trans.id, Trip.due: 0, Trip.price: price, Trip.status: Trip.TRIP_STOPPED})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count + 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count + 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count + d_hr})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating + Rating.RATING_DEFAULT})

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1})

    try:
        stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
        msg_content = "Spinny trip, id #" + str(booking.id) + " was stopped by " + \
                      driver_name + " at approximately " + \
                      stop_time_ist.strftime("%I:%M %p %d/%m/%Y")
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1, 'price': 0, 'dur': dur, 'surcharge': int(surcharge)})


def spinny_past_cust(user, to_fetch, cancelled_by):
    # to_fetch: 1 -> completed, 2 -> cancelled, 3 -> both
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region or not to_fetch:
        return jsonify({'success': -1})

    # This is basically admin panel with the caveat that we fetch all Spinny trips of a region
    user_region = user_region.region
    if user_region == -1:
        all_reg = True
    else:
        all_reg = False

    cur_dt = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

    if not all_reg:
        results = db.session.query(Bookings, Drivers, Users, Trip, SpinnyBookings).filter(SpinnyBookings.region == user_region). \
            filter(Bookings.type == BookingParams.TYPE_SPINNY). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(Trip.book_id == Bookings.id). \
            filter(Trip.endtime < cur_dt). \
            filter(SpinnyBookings.ref == Bookings.id). \
            order_by(Trip.starttime.desc()).all()
    else:
        results = db.session.query(Bookings, Drivers, Users, Trip, SpinnyBookings). \
            filter(Bookings.type == BookingParams.TYPE_SPINNY). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(Trip.book_id == Bookings.id). \
            filter(Trip.endtime < cur_dt). \
            filter(SpinnyBookings.ref == Bookings.id). \
            order_by(Trip.starttime.desc()).all()


    if cancelled_by == -1:
        if not all_reg:
            results_canceled = db.session.query(Bookings, Drivers, Users, SpinnyBookings).filter(SpinnyBookings.region == user_region). \
                filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
                filter(SpinnyBookings.ref == Bookings.id). \
                filter(Bookings.valid < 0).order_by(Bookings.starttime.desc()).all()
        else:
            results_canceled = db.session.query(Bookings, Drivers, Users, SpinnyBookings). \
                filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
                filter(SpinnyBookings.ref == Bookings.id). \
                filter(Bookings.valid < 0).order_by(Bookings.starttime.desc()).all()
    elif cancelled_by == 0:
        if not all_reg:
            results_canceled = db.session.query(Bookings, Drivers, Users, SpinnyBookings).filter(SpinnyBookings.region == user_region). \
                filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
                filter(SpinnyBookings.ref == Bookings.id). \
                filter(Bookings.valid == Bookings.CANCELLED_USER).order_by(Bookings.starttime.desc()).all()
        else:
            results_canceled = db.session.query(Bookings, Drivers, Users, SpinnyBookings). \
                filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
                filter(SpinnyBookings.ref == Bookings.id). \
                filter(Bookings.valid == Bookings.CANCELLED_USER).order_by(Bookings.starttime.desc()).all()
    elif cancelled_by == 1:
        if not all_reg:
            results_canceled = db.session.query(Bookings, Drivers, Users, SpinnyBookings).filter(SpinnyBookings.region == user_region). \
                filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
                filter(SpinnyBookings.ref == Bookings.id). \
                filter(Bookings.valid == Bookings.CANCELLED_D4M).order_by(Bookings.starttime.desc()).all()
        else:
            results_canceled = db.session.query(Bookings, Drivers, Users, SpinnyBookings).filter(SpinnyBookings.region == user_region). \
                filter(Bookings.type == BookingParams.TYPE_SPINNY). \
                filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
                filter(SpinnyBookings.ref == Bookings.id). \
                filter(Bookings.valid == Bookings.CANCELLED_D4M).order_by(Bookings.starttime.desc()).all()


    result_json = []

    if to_fetch & 1:
        for result in results:
            car_type = get_car_type(result[0].id)
            if car_type < 4:
                car_type = 0
            else:
                car_type = 1
            d_hr, d_min, d_sec = convert_timedelta(result[3].endtime - result[3].starttime)
            days = float(d_hr) // 24
            d_hr_fl = float(d_hr) - days * 24
            dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
            delta = result[3].endtime - result[3].starttime
            # Bug - only works for 60min calcs
            time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
            estimate_delta = (result[0].days * 24 + result[0].dur.hour) * Price.HOUR_RATIO + \
                             math.ceil((result[0].dur.minute * Price.HOUR_RATIO) / 60)
            # OT in minutes (ideally we would have a fn for this and above but ehhh)
            overtime = time_delta - estimate_delta
            if overtime < 0:
                ot_str = "00:00:00"
            else:
                oth, otm, ots = convert_mindelta(overtime)
                ot_str = str(oth) + ':' + str(otm) + ':' + str(ots)

            dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            spinny_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == result[4].rep).first()
            try:
                driver_name = result[2].get_name()
            except TypeError:
                driver_name = ''
            file_url, file_count = spinny_pic_download(result[4].id)
            res_data = BookingSpinnyPast(bookingid=result[0].id, bookingcode=result[0].code, id=result[4].id, appt=result[4].appt, name=driver_name,
                            startdate=result[3].starttime.strftime("%Y-%m-%d"),
                            starttime=result[3].starttime.strftime("%H:%M:%S"),
                            enddate=result[3].endtime.strftime("%Y-%m-%d"),
                            mobile=result[2].mobile,
                            endtime=result[3].endtime.strftime("%H:%M:%S"), dur=dur, car_type=car_type,
                            lat=result[0].lat, lng=result[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                            drop_spoc=result[4].drop_mob, veh_no=result[4].veh_no, veh_model=result[4].veh_model, driver_pic=result[1].pic,
                            loc=result[0].loc, trip_type=result[4].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                            sch_date=str(result[0].startdate), sch_time=str(result[0].starttime), comment=result[4].comment, ot=ot_str,
                            file_url=file_url, file_count=file_count)
            result_json.append(jsonpickle.encode(res_data))

    if to_fetch & 2 == 2:
        for result_c in results_canceled:
            car_type = get_car_type(result_c[0].id)
            dest = db.session.query(BookDest).filter(BookDest.book_id == result_c[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            try:
                driver_name = result_c[2].get_name()
            except TypeError:
                driver_name = ''
            spinny_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == result_c[3].rep).first()
            if result_c[1].id != BookingParams.BOOKING_DUMMY_ID:
                res_data = BookingSpinny(bookingid=result_c[0].id, bookingcode=result_c[0].code, id=result_c[3].id, appt=result_c[3].appt, name=driver_name, startdate=result_c[0].startdate,
                        mobile=result_c[2].mobile,
                        starttime=result_c[0].starttime, enddate=result_c[0].enddate, endtime=result_c[0].endtime, dur=result_c[0].dur, car_type=car_type,
                        lat=result_c[0].lat, lng=result_c[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                        drop_spoc=result_c[3].drop_mob, veh_no=result_c[3].veh_no, veh_model=result_c[3].veh_model, driver_pic=result_c[1].pic,
                        loc=result_c[0].loc, trip_type=result_c[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc, comment=result_c[3].comment)
            else:
                res_data = BookingSpinny(bookingid=result_c[0].id, bookingcode=result_c[0].code, id=result_c[3].id, appt=result_c[3].appt, name="Not Allocated", startdate=result_c[0].startdate,
                        starttime=result_c[0].starttime, enddate=result_c[0].enddate, endtime=result_c[0].endtime, dur=result_c[0].dur, car_type=car_type,
                        lat=result_c[0].lat, lng=result_c[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                        drop_spoc=result_c[3].drop_mob, veh_no=result_c[3].veh_no, veh_model=result_c[3].veh_model, driver_pic=None,
                        loc=result_c[0].loc, trip_type=result_c[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc, comment=result_c[3].comment)
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})



def spinny_past_chunked(user, day, month, year):
    # to_fetch: 1 -> completed, 2 -> cancelled, 3 -> both

    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region:
        return jsonify({'success': -1, 'error': 0})

    # This is basically admin panel with the caveat that we fetch all Spinny trips of a region
    if user_region.region == -1:
        user_region = list(range(0, len(Regions.REGN_NAME) + 1))
    else:
        user_region = [user_region.region]

    #st_dt = str(year)+"-"+str(month)+"-01"
    st_dt = str(year)+"-"+str(month)+"-"+str(day)
    end_dt = (datetime(year, month, day) + timedelta(1)).strftime("%Y-%m-%d")
    #month = month + 1
    #if month > 12:
    #    month = 1
    #    year = year + 1
    #end_dt = str(year)+"-"+str(month)+"-01"
    results = db.session.query(Bookings, Drivers, Users, Trip, SpinnyBookings).filter(SpinnyBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.starttime >= st_dt). \
        filter(Trip.starttime < end_dt). \
        filter(Trip.status == 0). \
        filter(SpinnyBookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    result_json = []

    for result in results:
        car_type = get_car_type(result[0].id)
        if car_type < 4:
            car_type = 0
        else:
            car_type = 1
        d_hr, d_min, d_sec = convert_timedelta(result[3].endtime - result[3].starttime)
        days = float(d_hr) // 24
        d_hr_fl = float(d_hr) - days * 24
        dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
        delta = result[3].endtime - result[3].starttime
        # Bug - only works for 60min calcs
        time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
        estimate_delta = (result[0].days * 24 + result[0].dur.hour) * Price.HOUR_RATIO + \
                         math.ceil((result[0].dur.minute * Price.HOUR_RATIO) / 60)
        # OT in minutes (ideally we would have a fn for this and above but ehhh)
        overtime = time_delta - estimate_delta
        if overtime < 0:
            ot_str = "00:00:00"
        else:
            oth, otm, ots = convert_mindelta(overtime)
            ot_str = str(oth) + ':' + str(otm) + ':' + str(ots)

        dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
        dest_lat = dest.lat
        dest_long = dest.lng
        dest_loc = dest.name
        spinny_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == result[4].rep).first()
        try:
            driver_name = result[2].get_name()
        except TypeError:
            driver_name = ''
        file_url, file_count = spinny_pic_download(result[4].id)
        res_data = BookingSpinnyPast(bookingid=result[0].id, bookingcode=result[0].code, id=result[4].id, appt=result[4].appt, name=driver_name,
                        startdate=result[3].starttime.strftime("%Y-%m-%d"),
                        starttime=result[3].starttime.strftime("%H:%M:%S"),
                        enddate=result[3].endtime.strftime("%Y-%m-%d"),
                        mobile=result[2].mobile,
                        endtime=result[3].endtime.strftime("%H:%M:%S"), dur=dur, car_type=car_type,
                        lat=result[0].lat, lng=result[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                        drop_spoc=result[4].drop_mob, veh_no=result[4].veh_no, veh_model=result[4].veh_model, driver_pic=result[1].pic,
                        loc=result[0].loc, trip_type=result[4].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                        sch_date=str(result[0].startdate), sch_time=str(result[0].starttime), comment=result[4].comment, ot=ot_str,
                        file_url=file_url, file_count=file_count)
        result_json.append(jsonpickle.encode(res_data))

    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def spinny_ongoing_cust(user):
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region:
        return jsonify({'success': -1})

    # This is basically admin panel with the caveat that we fetch all Spinny trips of a region
    #user_region = user_region.region
    if user_region.region == -1:
        user_region = range(100)
    else:
        user_region = [user_region.region]
    results = db.session.query(Bookings, Drivers, Users, Trip, SpinnyBookings).filter(SpinnyBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.endtime == None). \
        filter(SpinnyBookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    result_json = []
    res_data = []
    for result in results:
        car_type = get_car_type(result[0].id)
        if car_type < 3:
            car_type = 0
        else:
            car_type = 1
        if not result[3].starttime:
            result[3].starttime = datetime.utcnow()
        d_hr, d_min, d_sec = convert_timedelta(datetime.utcnow() - result[3].starttime)
        days = float(d_hr) // 24
        d_hr_fl = float(d_hr) - days * 24
        dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
        dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
        dest_lat = dest.lat
        dest_long = dest.lng
        dest_loc = dest.name
        spinny_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == result[4].rep).first()
        try:
            driver_name = result[2].get_name()
        except TypeError:
            driver_name = ''
        res_data = BookingSpinnyOngoing(bookingid=result[0].id, bookingcode=result[0].code, id=result[4].id, appt=result[4].appt, name=driver_name, startdate=result[3].starttime.strftime("%Y-%m-%d"),
                starttime=result[3].starttime.strftime("%H:%M:%S"), enddate="",
                endtime="", dur=dur, car_type=car_type,
                lat=result[0].lat, lng=result[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                drop_spoc=result[4].drop_mob, veh_no=result[4].veh_no, veh_model=result[4].veh_model, driver_pic=result[1].pic,
                loc=result[0].loc, trip_type=result[4].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,mobile=result[2].mobile,
                did=result[1].id, comment=result[4].comment)
        result_json.append(jsonpickle.encode(res_data))
    print(len(result_json))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def spinny_upcoming_cust(to_fetch, user):

    # to_fetch: 1 -> allocated, 2 -> new, 3 -> both
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region or not to_fetch:
        return jsonify({'success': -1})

    # This is basically admin panel with the caveat that we fetch all Spinny trips of a region
    #user_region = user_region.region=
    if user_region.region == -1:
        user_region = list(range(0, len(Regions.REGN_NAME) + 1))
    else:
        user_region = [user_region.region]
    trip_book = db.session.query(Trip.book_id)
    results = db.session.query(Bookings, Drivers, Users, SpinnyBookings).filter(SpinnyBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 1). \
        filter(Bookings.driver > BookingParams.BOOKING_DUMMY_ID). \
        filter(~Bookings.id.in_(trip_book)). \
        filter(SpinnyBookings.ref == Bookings.id). \
        order_by(Bookings.startdate.desc(),Bookings.starttime.desc()).all()

    results_uc = db.session.query(Bookings, Drivers, Users, SpinnyBookings).filter(SpinnyBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_SPINNY). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 0). \
        filter(Bookings.driver == BookingParams.BOOKING_DUMMY_ID). \
        filter(SpinnyBookings.ref == Bookings.id). \
        order_by(Bookings.startdate.desc(),Bookings.starttime.desc()).all()


    result_json = []
    res_data = []

    if to_fetch & 1:
        for result in results:
            car_type = get_car_type(result[0].id)
            if car_type < 3:
                car_type = 0
            else:
                car_type = 1
            dest = db.session.query(BookDest).filter(BookDest.book_id == result[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            spinny_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == result[3].rep).first()
            try:
                driver_name = result[2].get_name()
            except TypeError:
                driver_name = ''
            res_data = BookingSpinny(bookingid=result[0].id, bookingcode=result[0].code, id=result[3].id, appt=result[3].appt, name=driver_name, startdate=result[0].startdate,
                    starttime=result[0].starttime, enddate=result[0].enddate, endtime=result[0].endtime, dur=result[0].dur,
                    car_type=car_type, lat=result[0].lat, lng=result[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                    drop_spoc=result[3].drop_mob, veh_no=result[3].veh_no, veh_model=result[3].veh_model, driver_pic=result[1].pic,
                    loc=result[0].loc, trip_type=result[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc,
                    mobile=result[2].mobile, comment=result[3].comment)
            result_json.append(jsonpickle.encode(res_data))

    if to_fetch & 2 == 2:
        for result_uc in results_uc:
            car_type = get_car_type(result_uc[0].id)
            dest = db.session.query(BookDest).filter(BookDest.book_id == result_uc[0].id).first()
            dest_lat = dest.lat
            dest_long = dest.lng
            dest_loc = dest.name
            spinny_rep = db.session.query(SpinnyRep).filter(SpinnyRep.id == result_uc[3].rep).first()
            try:
                driver_name = result_uc[2].get_name()
            except TypeError:
                driver_name = ''
            res_data = BookingSpinny(bookingid=result_uc[0].id, bookingcode=result_uc[0].code, id=result_uc[3].id, appt=result_uc[3].appt, name="Not Allocated", startdate=result_uc[0].startdate,
                        starttime=result_uc[0].starttime, enddate=result_uc[0].enddate, endtime=result_uc[0].endtime, dur=result_uc[0].dur, car_type=car_type,
                        lat=result_uc[0].lat, lng=result_uc[0].long, rep_name=spinny_rep.name, rep_mob=spinny_rep.mobile,
                        drop_spoc=result_uc[3].drop_mob, veh_no=result_uc[3].veh_no, veh_model=result_uc[3].veh_model, driver_pic=None,
                        loc=result_uc[0].loc, trip_type=result_uc[3].trip_type, dest_lat=dest_lat, dest_long=dest_long, dest_loc=dest_loc, comment=result_uc[3].comment)
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def spinny_add_rep(name, mobile, owner, region):
    rep = db.session.query(SpinnyRep).filter(SpinnyRep.region == region). \
                    filter(SpinnyRep.mobile == mobile).first()
    if rep:
        rep.name = name
    else:
        spinny_rep = SpinnyRep(name=name, user=owner, mob=mobile, regn=region)
        db.session.add(spinny_rep)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    spinny = db.session.query(SpinnyRep).filter(SpinnyRep.region == region). \
                    filter(SpinnyRep.mobile == mobile).first()
    return jsonify({'success': 1, 'rep_id': spinny.id})

def spinny_cancel_charge(booking_id, user,reason):
    book_obj = db.session.query(Bookings).filter(Bookings.id == booking_id)
    booking = book_obj.first()
    trip = fetch_booking_trip(booking.id).first()
    is_ontrip = trip is not None
    if booking.driver != 1:
        last_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking.id).filter(BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first()
        if ( last_alloc_time and reason in BookingCancelled.WAIVER
            and datetime.utcnow() - last_alloc_time.timestamp < BookingCancelled.FORGIVE_DELTA or
            booking.valid <= 0):
            return jsonify({'success': 1, 'charge': [0,0]})
        penalty, level = Price.get_cancel_ch(datetime.utcnow(), datetime.combine(booking.startdate,booking.starttime), city=booking.region, cancel_cat=reason,
                                         is_ontrip=is_ontrip)
        return jsonify({'success': 1, 'charge': penalty})
    else:
        return jsonify({'success': 1, 'charge': [0,0]})


def spinny_cancel(booking_id, user,reason,reason_detail):
    book_obj = db.session.query(Bookings).filter(Bookings.id == booking_id)
    booking = book_obj.first()
    trip = fetch_booking_trip(booking_id).first()
    is_ontrip = trip is not None
    if booking.valid >= 0:
        try:
            # Update booking status
            book_obj.update({Bookings.valid: Bookings.CANCELLED_USER})
            book_obj.update({Bookings.cancelled_dt: datetime.utcnow()})

            if booking.valid == 0: #if trip is unallocated condition
                bc = BookingCancelled(
                    user=user,
                    cancel_source=BookingCancelled.SRC_USER,
                    booking=booking.id,
                    uid=booking.user,
                    did=booking.driver,
                    penalty_user=0,
                    penalty_driver=0,
                    rsn=reason,
                    reason_detail=reason_detail
                )
                db.session.add(bc)
                db.session.commit()
                return jsonify({'success': 2})

            # Calculate start time
            starttime = datetime(
                booking.startdate.year, booking.startdate.month,
                booking.startdate.day, booking.starttime.hour,
                booking.starttime.minute, booking.starttime.second
            )

            _update_driver_pending(booking.driver)

            # Calculate penalties
            penalty, level = Price.get_cancel_ch(datetime.utcnow(), starttime, city=booking.region, cancel_cat=reason, is_ontrip=is_ontrip)
            user_fine, driver_fine = penalty

            last_alloc_time = db.session.query(BookingAlloc).filter(
                BookingAlloc.booking_id == booking.id,
                BookingAlloc.driver_id == booking.driver
            ).order_by(BookingAlloc.timestamp.desc()).first()

            if last_alloc_time and reason in BookingCancelled.WAIVER:
                last_alloc_timestamp = last_alloc_time.timestamp
                if datetime.utcnow() - last_alloc_timestamp < BookingCancelled.FORGIVE_DELTA:
                    driver_fine = 0

            # Handle driver fine and transactions
            dt_id = None
            if driver_fine > 0:
                dc = DriverCancelled(booking.driver, booking.id, driver_fine)
                db.session.add(dc)

                driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver).first()
                wallet, withdrawable = compute_driver_wallet(driver_details, driver_fine)

                dt = DriverTrans(
                    booking.driver, -driver_fine * 100,
                    wall_a=wallet, wall_b=driver_details.wallet,
                    with_a=withdrawable, with_b=driver_details.withdrawable,
                    method=f"Cancellation: Booking {booking.code}",
                    status=DriverTrans.COMPLETED, stop=True
                )
                db.session.add(dt)
                db.session.commit()

                dt_id = dt.id

                db.session.query(DriverDetails).filter(
                    DriverDetails.driver_id == booking.driver
                ).update({
                    DriverDetails.owed: DriverDetails.owed + driver_fine,
                    DriverDetails.wallet: wallet,
                    DriverDetails.withdrawable: withdrawable
                })
            else:
                dc = DriverCancelled(booking.driver, booking.id, 0)
                db.session.add(dc)

            # Send notification to the driver
            try:
                reason_details_dict_driver = {
                    "22": "because you denied the booking.",
                    "23": "because the wait time was too long."
                }
                reason_detail_message_driver = reason_details_dict_driver.get(str(reason))
                if driver_fine > 0:
                    send_fcm_msg_driver(
                        booking.driver,
                        title=f"Cancellation of booking #{booking.code}",
                        smalltext=f"Spinny cancelled the booking #{booking.code}",
                        bigtext=f"Spinny cancelled the booking #{booking.code} allocated to you {reason_detail_message_driver}. "
                                f"You were charged a penalty of ₹{driver_fine}."
                    )
                else:
                    send_fcm_msg_driver(
                        booking.driver,
                        title=f"Cancellation of booking #{booking.code}",
                        smalltext=f"Spinny cancelled the booking #{booking.code}",
                        bigtext=f"Spinny cancelled the booking #{booking.code} allocated to you."
                    )
            except Exception:
                pass

            # Add booking cancellation
            bc = BookingCancelled(
                user=user,
                cancel_source=BookingCancelled.SRC_USER,
                booking=booking.id,
                uid=booking.user,
                did=booking.driver,
                penalty_user=user_fine,
                penalty_driver=driver_fine,
                rsn=reason,
                reason_detail=reason_detail,
                dtransid=dt_id
            )
            db.session.add(bc)
            db.session.commit()

            return jsonify({'success': 1})
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify({'success': -1}), 401
    else:
        return jsonify({'success': 0})

def spinny_alloc_list(book_id):
    try:
        region = db.session.query(Bookings).filter(Bookings.id == book_id).first().region
        spinny_trip = (
            db.session.query(Bookings, Drivers, Users)
            .join(Drivers, Bookings.driver == Drivers.id)
            .join(Users, Drivers.user == Users.id)
            .filter(
                Bookings.valid == 1,
                Bookings.type == BookingParams.TYPE_SPINNY,
                Drivers.approved == 1,
                Bookings.region == region
            )
            .all()
        )
        spinny_driver_info = {}
        for booking, driver, user in spinny_trip:
            if driver.id not in spinny_driver_info:
                spinny_driver_info[driver.id] = {
                    "id": driver.id,
                    "fname": user.fname,
                    "lname": user.lname,
                    "name": user.fname + " " + user.lname,
                    "mobile": user.mobile
                }
        return jsonify({"success": 1, "data": spinny_driver_info})
    except Exception as e:
        print(e)
        return jsonify({'success': -1}), 500


def spinny_allocate(book_id, driver_id, user):
    # Get the driver
    try:
        driver = db.session.query(Users, Drivers).filter(Users.id == Drivers.user).filter(Drivers.approved == 1). \
            filter(Drivers.id == driver_id).first()
        # add handler if driver is not found
        if driver is None:
            return jsonify({'success': -2}), 201
    except Exception as e:
        print(e)
        return jsonify({'success': -2}), 201
    # Get booking info
    try:
        booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
        orig_driver = booking.driver
        # add handler if booking is not found (unlikely)
    except Exception as e:
        print(e)
        return jsonify({'success': -3}), 201
    # Get corresponding book pending entry
    try:
        pending_entry = db.session.query(BookPending).filter(BookPending.book_id == booking.id). \
                            filter(BookPending.driver == driver[1].id).first()
        if pending_entry:
            ba = BookingAlloc(booking.id, driver[1].id, user,
                              phase=pending_entry.phase,
                              score=pending_entry.score)
        else:
            ba = BookingAlloc(booking.id, driver[1].id, user)
        db.session.add(ba)
        db.session.commit()
        book_pricing = db.session.query(BookPricing).filter(BookPricing.book_id == booking.id).first()
        if not book_pricing:
            return jsonify({'success': -42}), 201
        # update all book pending entries for that booking
        # update the booking entry
        try:
            db.session.query(BookPending).filter(BookPending.book_id == booking.id).delete()
            # get book pending entry for that driver
            db.session.query(Bookings).filter(Bookings.id == booking.id).update({Bookings.driver: driver[1].id,
                                                                                 Bookings.estimate: book_pricing.estimate,
                                                                                 Bookings.estimate_pre_tax: book_pricing.est_pre_tax,
                                                                                 Bookings.valid: 1,
                                                                                 Bookings.insurance_cost: book_pricing.insurance_ch})
            db.session.commit()
            # firebase
            driver_name = driver[0].get_name()
            driver_id = driver[1].id
            target_user = db.session.query(Users).filter(Users.id == booking.user).first()
            if booking.type >= BookingParams.TYPE_C24:
                user_name = BookingParams.get_type_to_str(booking.type)
            else:
                user_name = target_user.get_name()
            start_time = datetime(year=booking.startdate.year, month=booking.startdate.month,
                                  day=booking.startdate.day, hour=booking.starttime.hour,
                                  minute=booking.starttime.minute, second=booking.starttime.second)
            start_time_ist = start_time + _sms.IST_OFFSET_TIMEDELTA
            msg_content_json = {"booking-type-str": user_name,
                                "booking-start-dt": str(start_time_ist.strftime("%I:%M %p %d/%m/%Y"))}
            resp = _sms.send_msg_flow(str(driver[0].mobile), _sms.FLOWS['admin-driver-alloc'], msg_content_json)
            if resp:
                return jsonify({'success': 1, 'msg': 1}), 200
            else:
                return jsonify({'success': 1, 'msg': 0}), 200
        except Exception as e:
            print("Allocate error", e)
            return jsonify({'success': -7}), 201
    except Exception as e:
        print(e)
        return jsonify({'success': -4}), 201
    return jsonify({'success': -1})


def spinny_pic_upload(spinny_book_id, idx, pic):
    url_pic = upload_pic(pic, path="spinny" + str(spinny_book_id))
    full_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], "spinny" + str(spinny_book_id))

    base_fold = full_path
    zip_base = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], 'zip')
    zip_path = os.path.join(zip_base, "spinny" + str(spinny_book_id))
    os.makedirs(zip_path, exist_ok=True)
    if os.path.exists(os.path.join(zip_path, "spinny_" + str(spinny_book_id) + '.zip')):
        os.remove(os.path.join(zip_path, "spinny_" + str(spinny_book_id) + '.zip'))

    path_base = app.config['UPLOAD_ROOT_REL'] + app.config['UPLOAD_FOLDER']
    shutil.make_archive('../' + path_base + '/zip/' + str(spinny_book_id) + '/spinny_' + str(spinny_book_id), 'zip', base_fold)
    pic_reg = SpinnyPic(book=spinny_book_id, idx=idx, pic=url_pic)
    db.session.add(pic_reg)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})

def spinny_fuel_upload(spinny_book_id, pic, amount):
    url_pic = upload_pic(pic, path=str(spinny_book_id))
    full_path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], str(spinny_book_id))
    full_path = os.path.join(full_path, str('util'))
    base_fold = full_path
    zip_base = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], 'zip')
    zip_path = os.path.join(zip_base, str(spinny_book_id))
    zip_path = os.path.join(zip_path, str('util'))
    os.makedirs(zip_path, exist_ok=True)
    if os.path.exists(os.path.join(zip_path, str(spinny_book_id) + '.zip')):
        os.remove(os.path.join(zip_path, str(spinny_book_id) + '.zip'))

    path_base = app.config['UPLOAD_ROOT_REL'] + app.config['UPLOAD_FOLDER']
    shutil.make_archive('../' + path_base + '/zip/' + str(spinny_book_id) + '/spinny_' + str(spinny_book_id) + '_util', 'zip', base_fold)
    prev_util = db.session.query(SpinnyUtil).filter(SpinnyUtil.booking_id == spinny_book_id).all()
    idx = len(prev_util) + 1
    pic_reg = SpinnyUtil(book=spinny_book_id, idx=idx, pic=url_pic, amt=int(amount))
    db.session.add(pic_reg)
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})

def spinny_pic_download(spinny_book_id):
    # This is a /kinda/ lazy implementation - it just zips the folder
    # We could technically do a DB lookup but this is easier (and faster!)
    path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], "spinny" + str(spinny_book_id))
    zip_base = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], 'zip')
    zip_path = os.path.join(zip_base, "spinny" + str(spinny_book_id))

    if not os.path.isdir(path):
        book = db.session.query(SpinnyBookings).filter(SpinnyBookings.id == spinny_book_id). \
                        first().ref
        path = os.path.join(app.config['UPLOAD_FOLDER_COMPLETE'], "book-" + str(book))
        if not os.path.isdir(path):
            return "", 0
    p, d, f = os.walk(path).__next__()
    if len(f) == 0:
        return "", 0
    path_base = app.config['UPLOAD_ROOT_REL'] + app.config['UPLOAD_FOLDER']
    if not os.path.exists(os.path.join(zip_path, 'spinny_' + str(spinny_book_id) + '.zip')):
        shutil.make_archive('../' + path_base + '/zip/' + str(spinny_book_id) +  '/spinny_' + str(spinny_book_id), 'zip', path)
    ret_url = str(app.config['CUR_URL']) + str(app.config['UPLOAD_FOLDER']) + \
                     'zip/' + str(spinny_book_id) +'/spinny_' + str(spinny_book_id) + '.zip'
    return ret_url, len(f)

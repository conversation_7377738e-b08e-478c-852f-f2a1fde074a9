import sys
sys.path.append("/app/")
from export_csv import D4M_UTIL_PATH
from datetime import datetime, timedelta
from _email import send_mail

date_str = (datetime.now() - timedelta(days=1)).date().strftime("%d%m%Y")
filepathD = D4M_UTIL_PATH + 'output/delhi-mahindra.csv'
subjectD = "Mahindra Daily Update - Delhi - "+date_str
content = "Please find the attached data."
from_addr = "<EMAIL>"
to_addr_list_delhi = ["<EMAIL>", "<EMAIL>", "<EMAIL>" , "<EMAIL>" , "<EMAIL>"]
send_mail(from_addr, to_addr_list_delhi, subjectD, content, filepathD)
tags:
  - Driver_admin
summary: Verify Driver's Bank Document
description: >
  This endpoint verifies a driver's bank account details by sending the account number and IFSC code to an external verification service. The verification process checks the account holder's name, bank details, and status of the bank account.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose bank account is to be verified
    example: 101
  - name: acc_no
    in: formData
    required: true
    type: string
    description: The bank account number to be verified
    example: "************"
  - name: ifsc
    in: formData
    required: true
    type: string
    description: The IFSC code of the bank branch
    example: "SBIN0001234"
responses:
  200:
    description: Successfully verified the driver's bank account details
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the verification process (1 for successful verification, 2 for invalid details, 10 for fetched but not verified)
          example: 1
        message:
          type: string
          description: Message describing the outcome of the verification
          example: "Bank Details Verified"
        details:
          type: object
          description: Verification details including name match, bank branch, and district
          properties:
            name_match:
              type: boolean
              description: Indicates whether the account holder's name matches
              example: true
            bank_name:
              type: string
              description: The name of the bank
              example: "State Bank of India"
            district:
              type: string
              description: The district or city where the bank branch is located
              example: "Mumbai"
            bank_branch:
              type: string
              description: The branch of the bank
              example: "CST Branch"
  400:
    description: Bad request (verification failed due to missing or invalid details)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-9 for missing driver details, -7 for already fetched document)
          example: -9
        message:
          type: string
          description: Error message
          example: "Driver PrevDetails Not Found"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for API request errors, -2 for database errors)
          example: -1
        message:
          type: string
          description: Error message
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Internal server error"

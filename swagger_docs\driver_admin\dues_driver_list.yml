tags:
  - Driver_admin
summary: Retrieve Driver Dues List
description: >
  This endpoint allows admins to retrieve a list of drivers, their details, and dues based on various filters and sort criteria. 
  It supports filtering by region, driver availability, and other parameters, as well as sorting and paginating the result.
parameters:
  - name: region
    in: formData
    required: false
    type: integer
    description: The region for which the driver data is being fetched. If not provided, the region of the logged-in user is used.
    example: 1
  - name: search_query
    in: formData
    required: false
    type: string
    description: Search query to filter drivers by name, mobile number, or ID.
    example: "John Doe"
  - name: filter_by
    in: formData
    required: false
    type: string
    description: Filter to apply on driver availability. (1 for available, 2 for not available)
    example: "1,2"
  - name: sort_by
    in: formData
    required: false
    type: integer
    description: Sorting criteria (1 for ID Asc, 2 for ID Desc, 3 for Score Desc, 4 for Score Asc, 5 for Rating Desc, 6 for Rating Asc)
    example: 3
  - name: l_limit
    in: formData
    required: false
    type: integer
    description: Lower limit for pagination (default 0).
    example: 0
  - name: u_limit
    in: formData
    required: false
    type: integer
    description: Upper limit for pagination (default 50).
    example: 50
responses:
  200:
    description: Successfully retrieved the driver list with dues
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              driver_id:
                type: integer
                description: Unique ID of the driver
                example: 123
              driver_pic:
                type: string
                description: URL of the driver's profile picture
                example: "https://example.com/images/driver.jpg"
              driver_name:
                type: string
                description: Full name of the driver
                example: "John Doe"
              driver_mobile:
                type: string
                description: Mobile number of the driver
                example: "**********"
              availablity:
                type: integer
                description: Availability status of the driver (1 for available, 0 for not available)
                example: 1
              license:
                type: string
                description: Driver's license number
                example: "DL1234567890"
              driver_labels:
                type: string
                description: Labels or tags associated with the driver
                example: "Top Performer"
              driver_score:
                type: number
                format: float
                description: Calculated score of the driver
                example: 4.85
              driver_rating:
                type: number
                format: float
                description: Rating of the driver
                example: 4.7
              driver_rating_count:
                type: integer
                description: Number of ratings received by the driver
                example: 100
              driver_cancelled_trips:
                type: integer
                description: Number of trips canceled by the driver
                example: 5
              driver_trip_count:
                type: integer
                description: Total number of trips completed by the driver
                example: 200
              total_balance:
                type: number
                format: float
                description: Driver's total balance in the wallet
                example: 1000.50
              withdrawable:
                type: number
                format: float
                description: Amount that can be withdrawn by the driver
                example: 500.00
              total_dues:
                type: number
                format: float
                description: Total dues owed by the driver
                example: 200.00
        time:
          type: number
          description: Time taken to execute the query (in seconds)
          example: 0.03
  400:
    description: Bad request due to invalid input parameters
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for bad request)
          example: -1
  500:
    description: Internal server error while fetching driver data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for server error)
          example: -1
        message:
          type: string
          description: Error message for the internal server error
          example: "Internal server error"

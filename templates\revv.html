<!DOCTYPE html>
<html>

    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Drivers4Me | Revv Booking Console</title>
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='bootstrap.css') }}">
        <!--Required for glyphicons-->
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/DateTimePicker/bootstrap-datetimepicker.min.css') }}">
        <!-- fa icons -->
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/font-awesome/css/font-awesome.min.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/operations-common.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/revv.css') }}">
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/custom-elements.css') }}">

        <script>
        window.jQuery || document.write('<script src="{{ url_for('static', filename='jquery.js') }}">\x3C/script>');
        </script>
        <script src="{{ url_for('static', filename='assets/DateTimePicker/moment.js') }}"></script>     <!-- for datetimepicker -->
        <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
        <!-- for datetimepicker-->
        <script src="{{ url_for('static', filename='assets/DateTimePicker/bootstrap-datetimepicker.min.js') }}"></script>
        <!--Vertical text clamping-->
        <script src="{{ url_for('static', filename='clamp.js') }}"></script>

        <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>

        <!-- Add Firebase products that you want to use -->
        <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
        <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
        
        <script src="{{ url_for('static', filename='assets/js/operations-common.js') }}"></script>
        <script src="{{ url_for('static', filename='assets/js/utility.js') }}"></script>
        <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo&region=Dharmatala,Kolkata,West+Bengal,India&libraries=places&libraries=places"></script>
        <script src="{{ url_for('static', filename='assets/js/revv-bookings.js') }}"></script>
        <script src="{{ url_for('static', filename='assets/js/revv.js') }}"></script>
    </head>

    <body style="background-color: var(--d4m-body-primary);">
        <nav id="topMenu" class="navbar navbar-default navbar-fixed-top" style="margin-bottom: 0;">
            <a id="brandName" class="navbar-brand" href="/" style="padding-left: 15px!important; padding-top: 0!important;">
                <img src="{{ url_for('static',filename='assets/images/revv_collab.svg') }}" class="img-rounded" alt="Drivers4me" style="height: 100px;">
            </a>
            <button id="collapsedMainMenu" type="button" class="navbar-toggle" data-toggle="collapse" data-target="">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <div class="container-fluid collapse navbar-collapse" id="topBar">
                <button id="logout" class="btn navbar-btn d4m-primary-button navbar-right">
                    Logout&emsp;<span class="glyphicon glyphicon-off"></span>
                </button>
                <ul id="navComponents" class="nav navbar-nav nav-components navbar-right">
                    <li class="nav-tab active" id="newBooking"><a href="#newBooking">New Booking</a></li>
                    <li class="nav-tab inactive" id="bookings"><a href="#bookingList">Bookings</a></li>
                </ul>
            </div>
        </nav>
        <div id="newBookingPanel" class="row container-fluid function-panel collapse">

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-holder">
        <h3 class="standard-header">New Booking</h3><hr class="d4m-separator">
        <form id="newBookingForm" enctype="multipart/form-data" autocomplete="off">
            <!--Booking ID and Trip Type-->
           <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Scheduled Date (DD/MM/YYYY)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_pickup_date'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Number of drivers</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">

                            <input type="number" id='num-drivers' step="1" min="1" value="3" style="width: 100%;">
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
            </div>
            <!--Model Name and Vehicle Registration Number-->
            <!--Transmission Type and Region-->
            <div class="row">

                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Shift</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <!--dt picker-->
                            <select id='shift-select' style="width: 100%">
                              <option value="0">Day</option>
                              <option value="1">Night</option>
                            </select>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Reporting Time (HH:MM)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_pickup_time'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Clock.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Number of hours</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">

                            <input type="number" id='num-hours' step="1" min="10" value="12" max="24" style="width: 100%;">
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Region</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                             <select style="width: 100%" id="region">
                              <option value="0">Kolkata</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row small-bottom-padding"></div>
            <!--Button-->
            <div class="row small-bottom-padding">
                <div class=" col-lg-12 col-md-12 col-sm-12 col-xs-12 center-elements form_element">
                    <button id="book" type="button" class="btn btn-lg d4m-primary-button">Book Now</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="bookingsPanel" class="row container-fluid function-panel collapse">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">
        <nav id="bookingMenu" class="navbar navbar-default d4m-nav">
            <button id="collapsedBookingMenu" type="button" class="navbar-toggle" data-toggle="collapse" data-target="">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <div class="container-fluid collapse navbar-collapse" id="bookingTypeBar">
                <ul class="nav navbar-nav">
                    <li class="book-tab inactive" id="new"><a href="#newBookings" role="button">New</a></li>
                    <li class="book-tab inactive" id="upcoming"><a href="#upcomingBookings" role="button">Upcoming</a></li>
                    <li class="book-tab inactive" id="ongoing"><a href="#ongoingBookings" role="button">Ongoing</a></li>
                    <li class="book-tab inactive" id="completed"><a href="#completedBookings" role="button">Completed</a></li>
                    <li class="dropdown book-tab">
                        <a class="dropdown-toggle" data-toggle="dropdown" role="button">Cancelled
                            <span class="dropdown-choice-info trollFont collapse"></span><span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li class="book-tab inactive" id="cancel-d4m"><a href="#d4mCancelBookings" role="button">by D4M</a></li>
                            <li class="book-tab inactive" id="cancel-revv"><a href="#revvCancelBookings" role="button">by Revv</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
    <div id="newBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="upcomingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="ongoingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="completedBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="cancel-d4mBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="cancel-revvBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
</div>

</body>
    </body>
</html>
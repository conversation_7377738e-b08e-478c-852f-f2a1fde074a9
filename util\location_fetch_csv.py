from main import app
import sys
sys.path.append("/app/")
import os
import pandas as pd
import requests

from datetime import datetime, timedelta
from sqlalchemy.sql import func
from _email import send_mail

from _email import send_mail_v2
from _utils import get_dt_ist
from booking_params import BookingParams, Regions
from models import db, Bookings, Drivers, Users, Trip, UserLoc
from export_csv import D4M_UTIL_PATH

yesterday = datetime.now() - timedelta(days=1)
date_str = yesterday.date().strftime("%d%m%Y")

def get_city_name(lat, lng, api_key):
    if lat is None or lng is None:
        return 'No location.'
    url = 'https://maps.googleapis.com/maps/api/geocode/json'
    params = {
        'latlng': f'{lat},{lng}',
        'key': api_key
    }

    try:
        response = requests.get(url, params=params)
        data = response.json()

        if data['status'] == 'OK':
            for result in data['results']:
                for component in result['address_components']:
                    if 'locality' in component['types']:
                        city = component['long_name']
                        return city

            return 'City not found.'

        else:
            return 'Error: {}'.format(data['status'])

    except requests.exceptions.RequestException as e:
        return 'Error: {}'.format(e)

with app.app_context():
    subquery = db.session.query(
        UserLoc.user_id.label('user_id'),
        func.max(UserLoc.lat).label('latest_lat'),
        func.max(UserLoc.lng).label('latest_lng')
    ).filter(
        UserLoc.timestamp >= yesterday,
        UserLoc.timestamp < datetime.now(),
    ).group_by(
        UserLoc.user_id
    ).subquery()

    query = db.session.query(
        Users.fname,
        Users.lname,
        Users.mobile,
        func.date(Users.reg).label('registration_date'),
        subquery.c.latest_lat,
        subquery.c.latest_lng
    ).outerjoin(subquery, Users.id == subquery.c.user_id).filter(
        func.date(Users.reg) == yesterday.date(),
    ).filter(Users.role == 0)

    results = query.all()
record_count = len(results)
print("Num records:", record_count)
res_csv = []
res_csv.append(["FirstName", "LastName", "Mobile", "RegDate", "LatestLat", "LatestLong", "CityName"])

for result in results:
    res_csv.append(list([str(i) for i in result]) + [get_city_name(result[-2], result[-1], "AIzaSyCuXbo1CBK_rRlVGP6zaAiXcJV2a3OAis8")])

headers = res_csv[0]
data_rows = res_csv[1:]

# Create a pandas DataFrame
df = pd.DataFrame(data_rows, columns=headers)

# Export DataFrame to CSV
filepathH = D4M_UTIL_PATH + "output/reg-data.csv"
df.to_csv(filepathH, index=False)

subjectH = "Registration data - " + date_str
content = "Please find Attached. "
from_addr = "<EMAIL>"
to_addr_list = ["<EMAIL>"]
send_mail(from_addr, to_addr_list, subjectH, content, filepathH)
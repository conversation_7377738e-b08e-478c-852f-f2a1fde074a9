#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  _ops_message.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON> Mitra

import requests
from flask import current_app as app
DRIVERS_CHANNEL = 0
NEW_BOOKING_CHANNEL = 1
TRIP_CHANNEL = 2
PENDING_CHANNEL = 3
DRIVERS_URL = ('https://hooks.slack.com/services/'
               'T88LDTZMZ/BE4KFJU85/7exxZ3v3zot6NrjOckuWryjM')
NEW_BOOKING_URL = ('https://hooks.slack.com/services/T88LDTZMZ/BE4DFM5QS/'
                   'Iy2ZzI2HH2ACuNWEJ1x59M8G')
TRIP_URL = ('https://hooks.slack.com/services/T88LDTZMZ/BE3RDGB7B/'
            '9QkATgI1H9VdUKQpYPowYjaR')

SERVER_DEV = -1
SERVER_DEV_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/'
                  'BLDCD3AF5/8R2uwSTkhuLXAgDO03jslmPY')
DEV_TEST_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/BLJDYKDRP/'
                'scSLfjMecNy48pnpxXhYjY1x')
SLACK_DEV_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/BU42LBT1D/'
                 'KVfp69KVicXqS9YjCDb9SBVB')
SLACK_DRIVERS_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/BN1AGU5JP/'
                     'KW5HZfeLYdWitCIMspsNHdHz')
SLACK_NEW_BOOKING_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/BMREUDCCF/'
                         '9F0sV5tjLGLimJCFSRpZxLIW')
SLACK_TRIP_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/BMRQ84CCE/'
                  'cLOLSovYouFyfz9cg4t17IbQ')
SLACK_PENDING_URL = ('https://hooks.slack.com/services/TKJ1YUJKB/BMC5LN4Q2/'
                     'vpN2LBUCtyYtAFfjUkQvz3qP')

class SlackSendError(Exception):
    pass

def send_discord_msg(_channel, _content):
    pass

def send_slack_msg(channel, content):
    if not app.config['SEND_OPS_MSG']:
        return
    send_slack_msg_new(channel, content)
    return

def send_slack_msg_new(channel, content):
    if not app.config['SEND_OPS_MSG']:
        return False
    if channel == SERVER_DEV:
        url = SERVER_DEV_URL
    elif channel == DRIVERS_CHANNEL:
        url = SLACK_DRIVERS_URL
    elif channel == NEW_BOOKING_CHANNEL:
        url = SLACK_NEW_BOOKING_URL
    elif channel == TRIP_CHANNEL:
        url = SLACK_TRIP_URL
    elif channel == PENDING_CHANNEL:
        url = SLACK_PENDING_URL
    else:
        url = SLACK_DEV_URL
    payload = '{"text": "' + content + '"}'
    headers = {'content-type': 'application/json'}
    try:
        ret = requests.post(url, data=payload, headers=headers)
    except Exception as excp:
        raise SlackSendError(excp)
    return bool(ret)

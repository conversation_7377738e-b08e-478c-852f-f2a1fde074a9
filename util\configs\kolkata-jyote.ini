[general]
filename = kolkata-jyote.csv
query = select book_ref as 'Booking ID', mh_appt_id as 'Appointment ID', mh_veh_reg as 'Vehicle Number', CASE mh_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END as 'Trip Type', book_loc_name as 'Pickup Location', dest_book_name as 'Destination Location', convert_tz(trip_start,'+00:00', '+05:30') as 'Start Time', convert_tz(trip_stop,'+00:00', '+05:30') as 'Stop Time', CONCAT(mh_dist,' Km') as 'Distance', CONCAT(user_fname," ", user_lname) as 'Driver Name', CASE WHEN mh_dist <= 30 THEN 260 ELSE 260 + (mh_dist - 30) * 8 END AS 'Cost of the trip' from bookings, book_dest, trip, users, drivers, mahindra_bookings where book_ref=dest_book_id and mh_book_ref=book_ref and trip_book=book_ref and book_valid=1 and book_region=0 and book_driver=driver_id and user_id=driver_user and (date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))>= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 1) and date(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'))<= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 0));
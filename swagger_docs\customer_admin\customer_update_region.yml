tags:
  - Customer_admin
summary: Update Customer Registration Region
description: >
  This endpoint allows updating the registration region for a customer. 
  It updates the `region` field in the customer's registration details. 
  If no registration entry exists for the user, a new entry will be created.
  Only accessible by users with appropriate admin privileges.
parameters:
  - name: user_id
    in: formData
    required: true
    type: string
    description: The unique user ID of the customer whose registration source is to be updated.
    example: "101"
  - name: user_region
    in: formData
    required: true
    type: number
    description: The new registration region to be updated for the customer.
    example: "0"
  - name: region
    in: formData
    required: false
    type: string
    description: (Optional) Comma-separated list of region IDs to filter customers by region. Use '-1' for no filtering.
    example: "1,2,3"
responses:
  200:
    description: Successfully updated the registration region
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Region updated successfully"
  201:
    description: Successfully created a new registration region entry for the customer
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Source updated successfully"
  400:
    description: Missing required parameters or invalid input
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failure)
          example: -1
        error:
          type: string
          description: Error message indicating the issue
          example: "User ID is required"
  404:
    description: User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for user not found)
          example: -2
        error:
          type: string
          description: Error message indicating the user was not found
          example: "User not found"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for failure)
          example: -3
        error:
          type: string
          description: Error message indicating a server-side issue
          example: "Failed to log changes"

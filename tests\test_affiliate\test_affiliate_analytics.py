import pytest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from sqlalchemy.sql import func
from sqlalchemy.sql.functions import Function
from affiliate_b2b.affiliate_models import AffiliateRep
from db_config import db

""" Test cases for api: /api/affiliate/analytics_count """

def test_analytics_count_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/analytics_count', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']


def test_analytics_count_empty_date(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '',
        'end_date': ''
    }

    response = client.post('/api/affiliate/analytics_count', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'Missing or invalid date values' in response.json['message']

def test_analytics_count_invalid_date_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '01-01-2024',
        'end_date': '2024-01-07 23:59:59'
    }

    response = client.post('/api/affiliate/analytics_count', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'Invalid date format' in response.json['message']

def test_analytics_count_start_date_after_end_date(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '2024-01-10 00:00:00',
        'end_date': '2024-01-01 00:00:00'
    }

    response = client.post('/api/affiliate/analytics_count', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'start_date' in response.json['message']

def test_analytics_count_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/analytics_count', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']

def test_analytics_count_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/analytics_count', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_analytics_count_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'abc,xyz',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/analytics_count', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']

def test_analytics_count_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': str(rep.affiliate_id),
        'regions': '0,1,6,8',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }

    response = client.post("/api/affiliate/analytics_count", data=form_data, headers=auth_headers)
    assert response.status_code == 200

    json_data = response.get_json()
    assert json_data['success'] == 1


""" Test cases for api: /api/affiliate/daily_expenses_and_trips """

def test_daily_expenses_trips_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/daily_expenses_and_trips', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']

def test_daily_expenses_trips_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/daily_expenses_and_trips', data=form_data, headers=auth_headers)
  
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']


def test_daily_expenses_trips_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/daily_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_daily_expenses_trips_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'a,b,c',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/daily_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']


def test_daily_expenses_trips_invalid_type(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'type': 'sales'
    }
    response = client.post('/api/affiliate/daily_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid type' in response.json['message']


def test_daily_expenses_trips_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/daily_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1


""" Test cases for api: /api/affiliate/weekly_expenses_and_trips """

def test_weekly_expenses_trips_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/weekly_expenses_and_trips', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']

def test_weekly_expenses_trips_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/weekly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']


def test_weekly_expenses_trips_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/weekly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_weekly_expenses_trips_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'a,b,c',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/weekly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']

def test_weekly_expenses_trips_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/weekly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: /api/affiliate/monthly_expenses_and_trips """

def test_monthly_expenses_trips_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/monthly_expenses_and_trips', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']

def test_monthly_expenses_trips_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/monthly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']


def test_monthly_expenses_trips_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/monthly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_monthly_expenses_trips_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'a,b,c',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/monthly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']
    
def test_monthly_expenses_trips_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/monthly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: /api/affiliate/yearly_expenses_and_trips """

def test_yearly_expenses_trips_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/yearly_expenses_and_trips', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']

def test_yearly_expenses_trips_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/yearly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']


def test_yearly_expenses_trips_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/yearly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_yearly_expenses_trips_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'a,b,c',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/yearly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']

def test_yearly_expenses_trips_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'type': 'usages'
    }
    response = client.post('/api/affiliate/yearly_expenses_and_trips', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: /api/affiliate/allocation_counts """

def test_allocation_counts_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/allocation_counts', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']


def test_allocation_counts_empty_date(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '',
        'end_date': ''
    }

    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'Missing or invalid date values' in response.json['message']

def test_allocation_counts_invalid_date_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '01-01-2024',
        'end_date': '2024-01-07 23:59:59'
    }

    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'Invalid date format' in response.json['message']

def test_allocation_counts_start_date_after_end_date(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '2024-01-10 00:00:00',
        'end_date': '2024-01-01 00:00:00'
    }

    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'start_date' in response.json['message']

def test_allocation_counts_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']

def test_allocation_counts_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_allocation_counts_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'abc,xyz',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']

def test_allocation_counts_trips_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/allocation_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: /api/affiliate/cancellation_reason_counts """

def test_cancellation_reason_counts_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/cancellation_reason_counts', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']


def test_cancellation_reason_counts_empty_date(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '',
        'end_date': ''
    }

    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'Missing or invalid date values' in response.json['message']

def test_cancellation_reason_counts_invalid_date_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '01-01-2024',
        'end_date': '2024-01-07 23:59:59'
    }

    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'Invalid date format' in response.json['message']

def test_cancellation_reason_counts_start_date_after_end_date(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data =  {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,2',
        'start_date': '2024-01-10 00:00:00',
        'end_date': '2024-01-01 00:00:00'
    }

    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -3
    assert 'start_date' in response.json['message']

def test_cancellation_reason_counts_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']

def test_cancellation_reason_counts_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_cancellation_reason_counts_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'abc,xyz',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']

def test_cancellation_reason_counts_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6',
        'start_date': '2024-01-01 00:00:00',
        'end_date': '2024-01-07 23:59:59'
    }
    response = client.post('/api/affiliate/cancellation_reason_counts', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: /api/affiliate/trip_metrics """

def test_trip_metrics_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/trip_metrics', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']


def test_trip_metrics_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2'
    }
    response = client.post('/api/affiliate/trip_metrics', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']

def test_trip_metrics_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': ''
    }
    response = client.post('/api/affiliate/trip_metrics', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_trip_metrics_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'abc,xyz'
    }
    response = client.post('/api/affiliate/trip_metrics', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']


def test_trip_metrics_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6'
    }
    response = client.post('/api/affiliate/trip_metrics', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1

""" Test cases for api: /api/affiliate/pct_trips_delayed """

def test_pct_trip_delayed_incomplete_form_data(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    response = client.post('/api/affiliate/pct_trips_delayed', data={}, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Incomplete form details' in response.json['message']


def test_pct_trip_delayed_invalid_affiliate_id(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    form_data =  {
        'affiliate_ids': 'abc',
        'regions': '0,1,2'
    }
    response = client.post('/api/affiliate/pct_trips_delayed', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid affiliate ID(s) provided' in response.json['message']

def test_pct_trip_delayed_empty_region(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': ''
    }
    response = client.post('/api/affiliate/pct_trips_delayed', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'No region(s) provided' in response.json['message']

def test_pct_trip_delayed_invalid_region_format(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': 'abc,xyz'
    }
    response = client.post('/api/affiliate/pct_trips_delayed', data=form_data, headers=auth_headers)
    assert response.status_code == 400
    assert response.json['success'] == -1
    assert 'Invalid region(s)' in response.json['message']
    

def test_pct_trip_delayed_successful_response(client, affiliate_rep_login):
    auth_headers, rep = affiliate_rep_login
    rep = db.session.query(AffiliateRep).filter(AffiliateRep.id == rep).first()
    form_data = {
        'affiliate_ids': rep.affiliate_id,
        'regions': '0,1,6'
    }
    response = client.post('/api/affiliate/pct_trips_delayed', data=form_data, headers=auth_headers)
    assert response.status_code == 200
    assert response.json['success'] == 1
tags:
  - Affiliate_Wallet
summary: Mark Credit Transaction as Failed
description: >
  This endpoint allows an affiliate representative to mark a credit transaction as failed.
parameters:
  - name: order_id
    in: formData
    type: string
    required: true
    description: The order ID associated with the credit transaction.
responses:
  200:
    description: Transaction marked as failed successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        message:
          type: string
          description: Success message.
          example: "Transaction failed updated."
  401:
    description: Failed to get identity.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error).
          example: -1
        message:
          type: string
          description: Error message.
          example: "Failed to get identity."
  404:
    description: Affiliate representative or order transaction not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 or -4 for errors).
          example: -4
        message:
          type: string
          description: Error message.
          example: "Order transaction not present."
  201:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for error).
          example: -2
        message:
          type: string
          description: Error message.
          example: "Incomplete form details."
  500:
    description: Database error during transaction update.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for error).
          example: -3
        message:
          type: string
          description: Error message.
          example: "DB Error"

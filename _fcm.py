#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  book_ride.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON>hat<PERSON> Mi<PERSON>
#

from firebase_admin import messaging
from db_config import db
from models import UserFCM, DriverFCM
from flask import current_app as app

def send_fcm_msg(user, title, smalltext, bigtext):
    if not app.config['SEND_OPS_MSG']:
        return False
    my_tokens = db.session.query(UserFCM).filter(UserFCM.user_id == user).all()
    if not my_tokens:
        return False
    for token in my_tokens:
        if not token.token:
            continue
        if token.device == 'android':
            message = messaging.Message(
                data={
                    'title': title,
                    'smallText': smalltext,
                    'bigText': bigtext,
                },
                token=token.token,
            )
        else:
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=bigtext,
                ),
                token=token.token,
            )
        # pylint: disable=broad-except
        try:
            messaging.send(message)
        except Exception as excp:
            print(excp)
    return True


def send_fcm_msg_driver(driver, title, smalltext, bigtext):
    if not app.config['SEND_OPS_MSG']:
        return False
    my_tokens = db.session.query(DriverFCM).filter(DriverFCM.driver_id == driver).all()
    if not my_tokens:
        return False
    for token in my_tokens:
        if token.device == 'android':
            message = messaging.Message(
                data={
                    'title': title,
                    'smallText': smalltext,
                    'bigText': bigtext,
                },
                token=token.token,
            )
        else:
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=bigtext,
                ),
                token=token.token,
            )
        # pylint: disable=broad-except
        try:
            messaging.send(message)
        except Exception as excp:
            print(excp)
    return True

def send_fcm_msg_driver_request(driver, title, smalltext, bigtext):
    if not app.config['SEND_OPS_MSG']:
        return False
    my_tokens = db.session.query(DriverFCM).filter(DriverFCM.driver_id == driver).all()
    if not my_tokens:
        return False
    for token in my_tokens:
        if token.device == 'android':
            message = messaging.Message(
                data={
                    'title': title,
                    'smallText': smalltext,
                    'bigText': bigtext,
                    'pending': "1",
                },
                token=token.token,
            )
        else:
            message = messaging.Message(
                notification=messaging.Notification(
                    title=title,
                    body=bigtext,
                ),
                token=token.token,
            )
        # pylint: disable=broad-except
        try:
            messaging.send(message)
        except Exception as excp:
            print(excp)
    return True

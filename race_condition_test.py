from main import app
import threading
import datetime
import time
from _ops_message import send_slack_msg
from _rtdb import _update_user_pending, _update_driver_pending
from _utils_acc import account_enabled
from _utils_booking import fetch_booking_trip
from sqlalchemy.orm import sessionmaker

from models import Drivers, Bookings, BookPending, db, BookPricing, BookingAlloc

global start_event
start_event = threading.Event()
#Session = sessionmaker(bind=db.engine)

def acceptBooking(booking_id,driver_user):
    with app.app_context():
        Session = sessionmaker(bind=db.engine)
        start_event.wait()  # Wait until the start event is set
        session=Session()
        driver_user = driver_user
        driver_id = Drivers.query.filter_by(user=driver_user).first().id
        booking_id = booking_id

        with session.begin():
            with session.begin_nested():
            # Lock the booking row
                print("locking now ...",driver_user)
                booking = session.query(Bookings) \
                    .filter(Bookings.id == booking_id) \
                    .with_for_update() \
                    .first()

                if not booking:
                    print("Booking does not exist")
                    return
                if booking.valid == -1:
                    print("Booking is cancelled by user")
                    return
                if booking.driver != 1:
                    print("Booking is allocated")
                    return

                # Query to get pricing information for the booking
                check_exists = session.query(BookPricing, Bookings) \
                    .filter(Bookings.id == booking_id) \
                    .filter(BookPricing.book_id == Bookings.id) \
                    .first()

                if not check_exists:
                    print("Booking does not exist")
                    return

                # Extract pricing details from the result tuple (BookPricing, Bookings)
                book_pricing = check_exists[0]

                # Update the booking attributes directly on the object.
                # Since the booking row is locked, these changes are safe from concurrent modifications.
                booking.estimate = book_pricing.estimate
                booking.estimate_pre_tax = book_pricing.est_pre_tax
                booking.insurance_cost = book_pricing.insurance_ch
                booking.valid = 1
                booking.driver = driver_id
                pending = session.query(BookPending).filter(BookPending.book_id == booking_id)
                try:
                    pending_phase = pending.filter(BookPending.driver == driver_id).first().phase
                    pending_score = pending.filter(BookPending.driver == driver_id).first().score
                except Exception:
                    pending_phase = -1
                    pending_score = -99
                pending.update({BookPending.valid: 0})
                ba = BookingAlloc(booking_id, driver_id, driver_user, phase=pending_phase,
                                score=pending_score)
                # db.session.add(ba)
                # db.session.commit()
                print("Driver Accepted booking :",driver_user)

# def accept2Booking():
#     with app.app_context():
#         start_event.wait()  # Wait until the start event is set
#         driver_user = 7
#         driver_id = Drivers.query.filter_by(user=driver_user).first().id
#         booking_id = 671036

#         time.sleep(1)
#         with db.session.begin():
#             with db.session.begin_nested():
#                 # Lock the booking row
#                 print("locking now 2...")
#                 booking = db.session.query(Bookings) \
#                     .filter(Bookings.id == booking_id) \
#                     .with_for_update() \
#                     .first()

#                 if not booking:
#                     print("Booking does not exist")
#                     return
#                 if booking.valid == -1:
#                     print("Booking is cancelled by user")
#                     return
#                 if booking.driver != 1:
#                     print("Booking is allocated")
#                     return

#                 # Query to get pricing information for the booking
#                 check_exists = db.session.query(BookPricing, Bookings) \
#                     .filter(Bookings.id == booking_id) \
#                     .filter(BookPricing.book_id == booking_id) \
#                     .first()

#                 if not check_exists:
#                     print("Booking does not exist")
#                     return

#                 # Extract pricing details from the result tuple (BookPricing, Bookings)
#                 book_pricing = check_exists[0]

#                 # Update the booking attributes directly on the object.
#                 # Since the booking row is locked, these changes are safe from concurrent modifications.
#                 booking.estimate = book_pricing.estimate
#                 booking.estimate_pre_tax = book_pricing.est_pre_tax
#                 booking.insurance_cost = book_pricing.insurance_ch
#                 booking.valid = 1
#                 booking.driver = driver_id

#                 pending = db.session.query(BookPending).filter(BookPending.book_id == booking_id)
#                 try:
#                     pending_phase = pending.filter(BookPending.driver == driver_id).first().phase
#                     pending_score = pending.filter(BookPending.driver == driver_id).first().score
#                 except Exception:
#                     pending_phase = -1
#                     pending_score = -99
#                 pending.update({BookPending.valid: 0})
#                 ba = BookingAlloc(booking_id, driver_id, driver_user, phase=pending_phase,
#                                 score=pending_score)
#                 # db.session.add(ba)
#                 # db.session.commit()
#                 print("Driver2 Accepted booking")

# def cancelledBooking():
#     with app.app_context():
#         start_event.wait()  # Wait until the start event is set
#         booking_id = 663776
#         user_id = 220075

#         with db.session.begin_nested():
#             # Lock the booking row
#             booking = db.session.query(Bookings).filter(Bookings.user == user_id).filter(Bookings.id == booking_id).with_for_update().first()
#         if not account_enabled(user_id):
#             print("Account not enabled")
#             return
#         trip = fetch_booking_trip(booking_id).first()
#         if trip:
#             if trip.starttime:
#                 print("Trip start time exist")
#                 return
#             else:
#                 db.session.delete(trip)

#         if booking.valid >= 0:
#             Bookings.query.filter(Bookings.id == booking.id).update({Bookings.valid: Bookings.CANCELLED_USER})
#             Bookings.query.filter(Bookings.id == booking.id).update({Bookings.cancelled_dt: datetime.datetime.utcnow()})
#             db.session.commit()
#             _update_user_pending(booking.user)
#             _update_driver_pending(booking.driver)
#             print("Booking is cancelled")
#         else:
#             print("Booking is already cancelled")

if __name__ == '__main__':
    with app.app_context():

        # Run this threads to accept two booking at the same time
        # thread1 = threading.Thread(target=accept1Booking)
        # thread2 = threading.Thread(target=accept2Booking)


        # Run this threads to accept booking and cancelled booking at the same timme
        thread1 = threading.Thread(target=acceptBooking,args=(671036,755))
        thread2 = threading.Thread(target= acceptBooking,args=(671036,7))
        thread2.start()
        thread1.start()

        start_event.set()  # Signal both threads to start

        # Wait for both threads to complete
        thread1.join()
        thread2.join()

        print("functions executed")
tags:
  - Booking_admin
summary: Retrieve booking logs
description: >
  This endpoint retrieves the logs associated with a specific booking ID. It returns details of the changes made to the booking along with timestamps and editor information.
parameters:
  - name: booking_id
    in: path
    type: integer
    required: true
    description: The ID of the booking for which logs are being retrieved.
    example: 456
responses:
  200:
    description: Successfully retrieved booking logs.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        logs:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
              booking_id:
                type: integer
                example: 456
              changed_field:
                type: string
                example: "status"
              old_value:
                type: string
                example: "Pending"
              new_value:
                type: string
                example: "Confirmed"
              edited_by:
                type: integer
                example: 789
              editedby_name:
                type: string
                example: "Admin User"
              remark:
                type: string
                example: "Booking confirmed by admin."
              timestamp:
                type: string
                format: date-time
                example: "2024-10-17 14:30:00"
  404:
    description: No logs found for the given booking ID.
    schema:
      type: object
      properties:
        message:
          type: string
          example: "No logs found for booking_id 456"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        status:
          type: integer
          example: 500
        error:
          type: string
          example: "Internal server error message"

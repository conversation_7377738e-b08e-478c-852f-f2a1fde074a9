tags:
  - Driver_admin
summary: Reverify Driver's Aadhaar Document
description: >
  This endpoint re-verifies a driver's Aadhaar details. It can either force the verification process or match the driver's details against the stored information, updating the verification status accordingly.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose Aadhaar document is to be reverified
    example: 101
  - name: force_verify
    in: formData
    required: false
    type: integer
    description: Indicates if the verification should be forced (1 for true, 0 for false)
    example: 1
  - name: remarks
    in: formData
    required: false
    type: string
    description: Remarks or comments regarding the verification process
    example: "Reverifying due to discrepancies"
responses:
  200:
    description: Successfully reverified the driver's Aadhaar details
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the re-verification process (3 for forced verification, 1 for successful verification)
          example: 3
        message:
          type: string
          description: Message describing the outcome of the re-verification
          example: "Aadhaar Details Forced Verified"
        details:
          type: object
          description: Verification details including name match and other relevant information
          properties:
            name_match:
              type: boolean
              description: Indicates whether the account holder's name matches
              example: true
            photo_match:
              type: boolean
              description: Indicates whether the photo matches
              example: true
            dob_match:
              type: boolean
              description: Indicates whether the date of birth matches
              example: true
            face_match_score:
              type: number
              description: The score of the face match
              example: 0.81
  400:
    description: Bad request (verification failed due to issues in the re-verification process)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for re-verification failure)
          example: -1
        message:
          type: string
          description: Error message
          example: "Aadhaar Details Failed to Reverify"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-2 for database errors)
          example: -2
        message:
          type: string
          description: Error message
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Internal server error"

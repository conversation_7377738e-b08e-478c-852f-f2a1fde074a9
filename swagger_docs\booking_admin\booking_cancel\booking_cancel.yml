tags:
  - Booking_admin
summary: Admin Cancel Booking
description: This API allows an admin to cancel a booking by providing the booking ID and reason for cancellation. Penalties may be applied based on the reason.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The ID of the booking to cancel.
  - name: reason
    in: formData
    type: integer
    required: true
    description: The reason code for the cancellation.
  - name: reason_details
    in: formData
    type: string
    required: false
    description: Additional details for the cancellation reason (optional).
responses:
  200:
    description: Success response indicating the booking was successfully canceled.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Booking Cancelled Successfully
  400:
    description: Bad request due to missing or incomplete form data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Required parameters are missing
  404:
    description: Booking not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: Booking not found
  409:
    description: Conflict response when the booking is already canceled.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Booking already cancelled
  500:
    description: Internal server error indicating a failure to process the cancellation.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Internal Server Error

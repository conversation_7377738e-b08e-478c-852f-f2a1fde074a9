from O365 import Account, FileSystemTokenBackend
import os
import argparse
import requests
import re
from config import BaseConfig

# === CONFIG ===
CLIENT_ID = BaseConfig.MAIL_CLIENT_ID
CLIENT_SECRET = BaseConfig.MAIL_CLIENT_SECRET
TENANT_ID = BaseConfig.MAIL_TENANT
FROM_ADDR = "<EMAIL>"
TO_ADDRS = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
]
LOG_FILE_PATH = "/app/logs/pytest.log"

# === BITBUCKET CONFIG ===
BB_USER = BaseConfig.BB_USER
BB_APP_PASSWORD = BaseConfig.BB_APP_PASSWORD
WORKSPACE = BaseConfig.WORKSPACE
REPO_SLUG = BaseConfig.REPO_SLUG

def get_commit_sha() -> str:
    try:
        with open('.git/HEAD', 'r') as f:
            ref = f.read().strip()
        if ref.startswith('ref: '):
            ref_path = os.path.join('.git', ref[5:])
            with open(ref_path, 'r') as rf:
                return rf.read().strip()
        return ref
    except Exception:
        return 'unknown-commit'

def get_branch_and_author_from_bitbucket(sha: str) -> tuple[str, str, str, str,str]:
    # Fetch branch data (includes commit and author info)
    branches_url = f"https://api.bitbucket.org/2.0/repositories/{WORKSPACE}/{REPO_SLUG}/refs/branches?q=target.hash=\"{sha}\""
    resp = requests.get(branches_url, auth=(BB_USER, BB_APP_PASSWORD))

    if not resp.ok:
        return ("unknown-branch", "unknown-author", "", "","")

    data = resp.json()
    values = data.get("values", [])
    if not values:
        return ("unknown-branch", "unknown-author", "", "","")

    branch_name = values[0]["name"]
    target = values[0].get("target", {})
    author_info = target.get("author", {})
    user = author_info.get("user", {})
    author = user.get("display_name") or author_info.get("raw", "unknown-author")
    commit_msg = target.get("message", "")

    # Detect if it’s a merge commit
    merge_match = re.search(r"Merged in ([^\s]+) \(pull request #([0-9]+)\)", commit_msg)
    if merge_match:
        from_branch = merge_match.group(1)
        pr_number = merge_match.group(2)
        to_branch = branch_name
    else:
        from_branch = branch_name
        to_branch = ""
        pr_number = ""

    return from_branch, to_branch, author, commit_msg, pr_number

def authenticate_account():
    token_backend = FileSystemTokenBackend(token_path='/app', token_filename='o365_token.txt')
    credentials = (CLIENT_ID, CLIENT_SECRET)
    account = Account(
        credentials,
        auth_flow_type='credentials',
        tenant_id=TENANT_ID,
        token_backend=token_backend,
        main_resource=FROM_ADDR
    )
    if account.authenticate():
        return account
    return None

def extract_test_summary_and_failures() -> str:
    """Parse pytest log to get emoji-enhanced summary and list of failed test names."""
    if not os.path.exists(LOG_FILE_PATH):
        return "No test summary found."

    with open(LOG_FILE_PATH, "r") as f:
        lines = f.readlines()

    summary_line = ""
    bad_tests = []

    for i, line in enumerate(reversed(lines)):
        if not summary_line and "==" in line and any(word in line for word in ("failed", "passed", "warnings", "errors")):
            clean_line = line.strip("= \n")
            clean_line = re.sub(r"(\d+)\s+failed", r"❌ \1 failed", clean_line)
            clean_line = re.sub(r"(\d+)\s+passed", r"✅ \1 passed", clean_line)
            clean_line = re.sub(r"(\d+)\s+warnings?", r"⚠️ \1 warnings", clean_line)
            clean_line = re.sub(r"(\d+)\s+errors?", r"🛑 \1 errors", clean_line)
            clean_line = re.sub(r"in\s+([\d\.]+s|\(\d+:\d+:\d+\))", r" in \1", clean_line)
            summary_line = clean_line

        if "::" in line and line.strip().startswith(("FAILED", "ERROR")):
            match = re.search(r"::([^\s]+)", line)
            if match:
                bad_tests.append(match.group(1))

    if not summary_line:
        summary_line = "Test summary not found."

    failed_section = ""
    if bad_tests:
        failed_section = "\n\n❗ Failed / Error Tests:\n" + "\n".join(f"- `{name}`" for name in bad_tests)

    return f"\n{summary_line}{failed_section}\n"

def get_pr_title(pr_number: str) -> str:
    if not pr_number:
        return ""
    url = f"https://api.bitbucket.org/2.0/repositories/{WORKSPACE}/{REPO_SLUG}/pullrequests/{pr_number}"
    resp = requests.get(url, auth=(BB_USER, BB_APP_PASSWORD))
    if resp.ok:
        data = resp.json()
        return data.get("title", "")
    return ""

def send_pytest_logs(from_branch: str, to_branch: str, git_user: str, commit_sha: str, subject_prefix: str = "Pytest Logs", pr_number: str = "", pr_title: str = ""):
    if not os.path.exists(LOG_FILE_PATH):
        print("Log file does not exist.")
        return

    summary_line = extract_test_summary_and_failures()

    account = authenticate_account()
    if not account:
        print("Authentication failed.")
        return

    mailbox = account.mailbox()
    msg = mailbox.new_message()
    msg.sender.address = FROM_ADDR
    for recipient in TO_ADDRS:
        msg.to.add(recipient)

    if to_branch != "main":
        branch_display = f"{from_branch} → {to_branch}"
    else:
        branch_display = from_branch

    msg.subject = f"{subject_prefix}: {branch_display} @ {commit_sha[:7]} by {git_user}"
    to_branch_line = f"To branch:   {to_branch}\n" if to_branch != "main" else ""
    summary_html = summary_line.replace("\n", "<br>")

    # Convert failed test names to HTML list
    failed_tests_html = ""
    if "❗ Failed / Error Tests:" in summary_html:
        parts = summary_html.split("❗ Failed / Error Tests:<br>")
        summary_part = parts[0]
        failed_lines = parts[1].split("<br>") if len(parts) > 1 else []
        failed_tests_html = "<ul>" + "".join(f"<li><code>{line.strip('- `')}</code></li>" for line in failed_lines if line.strip()) + "</ul>"
        summary_html = summary_part + "<br><br><strong>❗ Failed / Error Tests:</strong><br>" + failed_tests_html

    # Only show "To branch" line if not main
    to_branch_line = f"To branch:   <strong>{to_branch}</strong><br>" if to_branch != "main" else ""
    pr_line = f"PR Number: <strong>#{pr_number}</strong><br>" if pr_number else ""
    pr_title_line = f"PR Title: <strong>{pr_title}</strong><br>" if pr_title else ""
    html_body  = (
        f"<p>Hello team,</p>"
        f"<p>"
        f"Tests Executed by: <strong>{git_user}</strong><br>"
        f"From branch: <strong>{from_branch}</strong><br>"
        f"{to_branch_line}"
        f"{pr_line}"
        f"{pr_title_line}"
        f"Commit: <strong>{commit_sha[:7]}</strong>"
        f"</p>"
        f"<p>{summary_html}</p>"
        f"<p>Please find attached the full pytest logs.</p>"
    )
    msg.body = html_body
    msg.body_type = 'HTML'  # <-- Important: specify the body is HTML
    msg.attachments.add(LOG_FILE_PATH)

    try:
        msg.send()
        print(f"Email sent for {from_branch} → {to_branch}.")
    except Exception as e:
        print("Failed to send email:", e)
        
# === MAIN ===
if __name__ == "__main__":
    p = argparse.ArgumentParser(description="Send pytest logs via O365")
    p.add_argument("--to-branch", help="Target branch name (default: main)")
    args = p.parse_args()

    commit_sha = get_commit_sha()
    from_branch, to_branch_detected, git_user, commit_msg, pr_number = get_branch_and_author_from_bitbucket(commit_sha)
    to_br = args.to_branch or to_branch_detected or "main"
    subject_prefix = "Pytest Merge Logs" if "Merged in" in commit_msg else "Pytest Logs"
    
    pr_title = get_pr_title(pr_number) if pr_number else ""
    send_pytest_logs(from_branch, to_br, git_user, commit_sha, subject_prefix, pr_number,pr_title)
    
    
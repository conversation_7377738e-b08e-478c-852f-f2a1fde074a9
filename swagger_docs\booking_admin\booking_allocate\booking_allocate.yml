tags:
  - Booking_admin
summary: Allocate or reallocate a driver to a booking
description: This API allows the admin to allocate or reallocate a driver to a booking and handle cancellation penalties if necessary.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: reallocating
    in: formData
    type: integer
    required: true
    description: Flag to indicate if this is a reallocation (1 for reallocation, 0 for fresh allocation).
  - name: reassigning
    in: formData
    type: integer
    required: true
    description: Flag to indicate if this is a reassignment (1 for reassignment, 0 for no reassignment).
  - name: booking_id
    in: formData
    type: string
    required: true
    description: The booking ID for which the driver is being allocated.
  - name: driver_id
    in: formData
    type: integer
    required: true
    description: The driver ID to be allocated for the booking.
  - name: reason
    in: formData
    type: integer
    required: false
    description: Reason for reallocation or cancellation, defaults to 'Other'.
  - name: reason_details
    in: formData
    type: string
    required: false
    description: Additional details for the reason of reallocation or cancellation.
responses:
  200:
    description: Success response indicating the booking was successfully allocated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        msg:
          type: string
          example: Booking Allocated Successfully
  400:
    description: Bad request indicating trip creation issues or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Trip already created
  404:
    description: Not found response indicating that the booking or driver was not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Driver Not Found
  409:
    description: Conflict response indicating that the booking was already cancelled or allocated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -4
        message:
          type: string
          example: Booking already allocated
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: Internal Server Error

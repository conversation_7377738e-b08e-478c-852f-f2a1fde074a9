#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  price_revv_v2_delhi.py
#
#  Copyright 2017-2021 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime
from booking_params import BookingParams
from _utils import get_dt_ist
from db_config import db
from models import Bookings, Trip
import math

class PriceRevvV2Delhi:
    MIN_DIST_THRESH = 32

    BASE_MINIMUM = 220
    BASE_FLAT = 7
    NIGHT_HIKE = 80

    BASE_MINIMUM_OP = (200, 200, 200)
    BASE_FLAT_OP = (8, 8, 8)
    NIGHT_HIKE_OP = (100, 100, 100)

    NIGHT_THRESH_0 = datetime.time(16, 30, 0)
    NIGHT_THRESH_1 = datetime.time(0, 30, 0)

    OP_DAYS = [[], [], []] #changed 90 120 120

    @staticmethod
    def update_op_days():
        PriceRevvV2Delhi.OP_DAYS[0] = []
        PriceRevvV2Delhi.OP_DAYS[1] = []
        PriceRevvV2Delhi.OP_DAYS[2] = []

    @staticmethod
    def get_cancel_ch(curtime, starttime):
        return 0

    @staticmethod
    def get_booking_ch(car_type, driver_id, booking_id):
        return 0

    @staticmethod
    def get_insurance_ch(booking_id, city=0):
        return 0

    @staticmethod
    def get_dist_fare(dist, city=0):
        extra_dist = max(0, dist - PriceRevvV2Delhi.MIN_DIST_THRESH)
        return extra_dist * PriceRevvV2Delhi.BASE_FLAT

    @staticmethod
    def get_dist_fare_op(dist, op_level, city=0):
        extra_dist = max(0, dist - PriceRevvV2Delhi.MIN_DIST_THRESH)
        return extra_dist * PriceRevvV2Delhi.BASE_FLAT_OP[op_level]

    @staticmethod
    def get_op_level(startdate, starttime, enddate, endtime):
        start_ist = get_dt_ist(startdate, starttime)
        for level in range(len(PriceRevvV2Delhi.OP_DAYS) - 1, -1, -1):

            if start_ist.date() in PriceRevvV2Delhi.OP_DAYS[level]:
                return level
        return -1

    @staticmethod
    def get_price(dur, starttime, endtime, dist, startdate, enddate):
        PriceRevvV2Delhi.update_op_days()
        op_level = PriceRevvV2Delhi.get_op_level(startdate, starttime, enddate, endtime)
        if op_level == -1:
            return PriceRevvV2Delhi.get_price_flat(dur, starttime, endtime, dist, startdate, enddate)
        else:
            return PriceRevvV2Delhi.get_price_op(dur, starttime, endtime, dist, startdate, enddate)

    @staticmethod
    def get_price_flat(dur, starttime, endtime, dist, startdate, enddate):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0

        base_fare = PriceRevvV2Delhi.BASE_MINIMUM
        dist_fare = PriceRevvV2Delhi.get_dist_fare(dist)
        night = PriceRevvV2Delhi.is_trip_night(starttime, endtime)
        night_fare = PriceRevvV2Delhi.NIGHT_HIKE * int(night)
        total_fare = base_fare + dist_fare + night_fare
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), \
            int(round(dist_fare)), 0, 0, int(round(total_fare)), 0, 0, \
            int(round(total_fare)), 0, False  # surcharge


    @staticmethod
    def get_price_op(type, dur, starttime, endtime, dist, car_type, loc_cluster, op_level, insurance, insurance_num):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0

        base_fare = PriceRevvV2Delhi.BASE_MINIMUM_OP[op_level]
        dist_fare = PriceRevvV2Delhi.get_dist_fare_op(dist, op_level)
        night = PriceRevvV2Delhi.is_trip_night(starttime, endtime)
        night_fare = PriceRevvV2Delhi.NIGHT_HIKE_OP[op_level] * int(night)
        total_fare = base_fare + dist_fare + night_fare
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), \
            int(round(dist_fare)), 0, 0, int(round(total_fare)), 0, 0, \
            int(round(total_fare)), 0, False  # surcharge


    @staticmethod
    def get_trip_price(est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime,
                       startdate, enddate):

        PriceRevvV2Delhi.update_op_days()
        op_level = PriceRevvV2Delhi.get_op_level(startdate, book_starttime, enddate, book_stoptime)
        if op_level == -1:
            return PriceRevvV2Delhi.get_trip_price_gen(est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime)
        else:
            # assume no 7 day roundtrip (lol)
            return PriceRevvV2Delhi.get_trip_price_op(est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime, op_level)

    @staticmethod
    def get_trip_price_gen(est, book_starttime, book_stoptime, trip_starttime, trip_stoptime):
        price = est
        night = PriceRevvV2Delhi.is_trip_night(trip_starttime, trip_stoptime)
        est_night = PriceRevvV2Delhi.is_trip_night(book_starttime, book_stoptime)
        if night and not est_night:
            price += PriceRevvV2Delhi.NIGHT_HIKE
        elif est_night and not night:
            price -= PriceRevvV2Delhi.NIGHT_HIKE
        return int(round(price))

    @staticmethod
    def get_trip_price_op(est, book_starttime, book_stoptime, trip_starttime, trip_stoptime,
                          op_level):
        price = est
        night = PriceRevvV2Delhi.is_trip_night(trip_starttime, trip_stoptime)
        est_night = PriceRevvV2Delhi.is_trip_night(book_starttime, book_stoptime)
        if night and not est_night:
            price += PriceRevvV2Delhi.NIGHT_HIKE_OP[op_level]
        elif est_night and not night:
            price -= PriceRevvV2Delhi.NIGHT_HIKE_OP[op_level]
        return int(round(price))

    @staticmethod
    def is_trip_night(start, end):
        start = datetime.time(start.hour, start.minute)
        end = datetime.time(end.hour, end.minute)
        if PriceRevvV2Delhi.NIGHT_THRESH_0 < PriceRevvV2Delhi.NIGHT_THRESH_1:
            return ((start >= PriceRevvV2Delhi.NIGHT_THRESH_0 and
                     start <= PriceRevvV2Delhi.NIGHT_THRESH_1) or
                    (end >= PriceRevvV2Delhi.NIGHT_THRESH_0 and
                     end <= PriceRevvV2Delhi.NIGHT_THRESH_1))
        else:
            # end of night is after midnight UTC
            return ((start >= PriceRevvV2Delhi.NIGHT_THRESH_0 or
                     start <= PriceRevvV2Delhi.NIGHT_THRESH_1) or
                    (end >= PriceRevvV2Delhi.NIGHT_THRESH_0 or
                     end <= PriceRevvV2Delhi.NIGHT_THRESH_1))
[general]
filename = bangalore-spinny-driver.csv
query = select book_ref, sp_appt_id, CASE sp_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, sp_veh_reg, REPLACE(convert_tz(CONCAT(book_startdate, " ", book_starttime),'+00:00', '+05:30'), '.000000', '') , convert_tz(trip_start,'+00:00', '+05:30'), convert_tz(trip_stop,'+00:00', '+05:30'), TIMESTAMPDIFF(MINUTE, trip_start, trip_stop), sp_dist, trip_price, user_fname, user_lname, user_mobile, replace(book_comment,  '&#x27;','’')  from bookings, trip, users, drivers, spinny_bookings where sp_book_ref=book_ref and trip_book=book_ref and book_valid=1 and book_region=8 and book_driver=driver_id and user_id=driver_user and (date(convert_tz(trip_start,'+00:00', '+05:30'))>= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 2) and date(convert_tz(trip_start,'+00:00', '+05:30'))<= subdate(convert_tz(Date(NOW()),'+00:00', '+05:30'), 1));
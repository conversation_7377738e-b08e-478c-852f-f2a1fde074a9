tags:
  - Trips
summary: Validate Booking OTP
description: >
  This API allows drivers to validate the OTP associated with a specific booking. It also regenerates the OTP after validation.
parameters:
  - name: otp
    in: formData
    type: string
    required: true
    description: The OTP to be validated.
    example: "123456"
  
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking for which the OTP is being validated.
    example: 12345

  - name: lat
    in: formData
    type: float
    required: false
    description: The latitude of the driver's current location (optional).
    example: 12.9715987

  - name: lng
    in: formData
    type: float
    required: false
    description: The longitude of the driver's current location (optional).
    example: 77.594566
responses:
  200:
    description: OTP validated and regenerated successfully.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Success flag (200 for successful validation)
          example: 200
        message:
          type: string
          description: Success message
          example: "OTP validated and regenerated successfully"
    examples:
      application/json:
        status: 200
        message: "OTP validated and regenerated successfully"
  401_a:
    description: Unauthorized role, Not a driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized role)
          example: -1
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not driver"
    examples:
      application/json:
        success: -1
        message: "Unauthorized role: not driver"
  401_b:
    description: User account is restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  400:
    description: Invalid parameters (missing or incorrect booking ID).
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (400 for invalid parameters)
          example: 400
        error:
          type: object
          properties:
            message:
              type: string
              description: Error message
              example: "Invalid params"
    examples:
      application/json:
        status: 400
        error:
          message: "Invalid params"
  403:
    description: OTP did not match the booking's OTP.
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (403 for OTP mismatch)
          example: 403
        error:
          type: object
          properties:
            message:
              type: string
              description: Error message
              example: "OTP did not match"
    examples:
      application/json:
        status: 403
        error:
          message: "OTP did not match"

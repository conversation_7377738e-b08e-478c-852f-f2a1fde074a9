from main import app
from datetime import datetime, timedelta
from models import ScheduledReport,BookingAlloc
from _email import send_mail
from db_config import db,mdb
from affiliate_b2b.affiliate_models import AffBookingLogs, Affiliate,AffiliateCollections
from sqlalchemy.sql import func
import os
from adminnew.affiliate.affiliate_admin import create_excel_file,build_affiliate_report_data,build_affiliate_report_query


def get_duration_range(frequency: int, duration_type: int, today: datetime = None):
    today = today or datetime.today()
    today = today.replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday = today - timedelta(days=1)

    if frequency == ScheduledReport.FREQUENCY_DAILY:
        if duration_type == ScheduledReport.DURATION_PREVIOUS_DAY:
            start_date = yesterday
            end_date = yesterday.replace(hour=23, minute=59, second=59)
        elif duration_type == ScheduledReport.DURATION_THIS_WEEK:
            if today.weekday() == 0:  # Monday
                start_date = today - timedelta(days=7)
            else:
                start_date = today - timedelta(days=today.weekday())
            end_date = yesterday.replace(hour=23, minute=59, second=59)
        elif duration_type == ScheduledReport.DURATION_THIS_MONTH:
            if today.day == 1:
                prev_month_end = today - timedelta(days=1)
                start_date = prev_month_end.replace(day=1)
                end_date = prev_month_end
            else:
                start_date = today.replace(day=1)
                end_date = yesterday
            end_date = end_date.replace(hour=23, minute=59, second=59)
        else:
            raise ValueError("Invalid duration type for daily frequency")
    elif frequency == ScheduledReport.FREQUENCY_WEEKLY:
        if duration_type == ScheduledReport.DURATION_THIS_WEEK:
            start_date = today - timedelta(days=7)
            end_date = yesterday.replace(hour=23, minute=59, second=59)
        elif duration_type == ScheduledReport.DURATION_THIS_MONTH:
            if today.day <= 7:  # First week's Monday
                # Get previous month's full data
                prev_month_end = today.replace(day=1) - timedelta(days=1)
                start_date = prev_month_end.replace(day=1)
                end_date = prev_month_end
            else:
                # Current month data up to yesterday
                start_date = today.replace(day=1)
                end_date = yesterday
            end_date = end_date.replace(hour=23, minute=59, second=59)
        else:
            raise ValueError("Invalid duration type for weekly frequency")
            
    elif frequency == ScheduledReport.FREQUENCY_MONTHLY:
        prev_month = today - timedelta(days=1)
       
        if duration_type == ScheduledReport.DURATION_THIS_MONTH:
            start_date = prev_month.replace(day=1)
            end_date = prev_month.replace(hour=23, minute=59, second=59)
        else:
            raise ValueError("Invalid duration type for monthly frequency")
    else:
        raise ValueError("Invalid frequency")

    return start_date, end_date




def is_due(report: ScheduledReport, today: datetime = None):
    today = today or datetime.today()
    if not report.is_active:
        return False

    if report.frequency == ScheduledReport.FREQUENCY_DAILY:
        return True

    if report.frequency == ScheduledReport.FREQUENCY_WEEKLY:
        return today.weekday() == 0  # Monday only

    if report.frequency == ScheduledReport.FREQUENCY_MONTHLY:
        return today.day == 3 

    return False



def run_scheduled_reports():
    today = datetime.now()
    reports = ScheduledReport.query.filter_by(is_active=True).all()
    i=1
    for report in reports:
        report_type_str = "Booking" if report.report_type == ScheduledReport.REPORT_BOOKING else "Cancellation"

        start_date, end_date = get_duration_range(report.frequency,report.duration, today)
        
        if(not is_due(report, today)):
            continue
        try:
            book_valid = [1] if report.report_type == ScheduledReport.REPORT_BOOKING else [-1, -2, -3]
            query = build_affiliate_report_query(
            report_type=report_type_str,
            book_valid=book_valid,
            start_date=start_date,
            end_date=end_date,
            selected_affiliates=report.affiliates,
            city_filter=report.regions
            )
            results = query.all()

            # Get allocation times
            book_ids = [booking.id for booking in results]
            latest_alloc_times = {}
            if book_ids:
                latest_alloc_times = dict(
                    db.session.query(
                        BookingAlloc.booking_id,
                        func.max(BookingAlloc.timestamp)
                    ).filter(BookingAlloc.booking_id.in_(book_ids))
                    .group_by(BookingAlloc.booking_id)
                    .all()
                )

            # Mongo data
            mongo_data_map = {
                doc['book_ref']: doc
                for doc in AffiliateCollections.affiliates_book.find(
                    {"book_ref": {"$in": book_ids}},
                    {
                        "book_ref": 1,
                        "trip_type": 1,
                        "client_name": 1,
                        "custom_data": 1,
                        "vehicle_no": 1,
                        "spoc_data": 1,
                        "rep_fullname": 1,
                        "appointment_id": 1,
                        "vehicle_model": 1
                    }
                )
            }

            data = build_affiliate_report_data(results, report_type_str, latest_alloc_times, mongo_data_map)
            # Create Excel file
                
            filename = f"affiliate_report_{str(i)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = create_excel_file(data,filename)
                
            i+=1
            # Email content
            subject = report.subject if report.subject else f"{report.report_type} Report"
            content = report.message if report.message is not None else f"Hi,\n\nPlease find attached the affiliate report.\n\nRegards,\nTeam"
            content = content.replace('\n', '<br>') 
            from_address="<EMAIL>"

            try:
                if(len(data)==0):
                    raise Exception('No Data available for report')
                send_mail(from_address, report.email_list, subject, content, file_path)
                report.last_sent_at = datetime.utcnow()
                report.last_sent_status = True
                report.last_sent_error = None
            except Exception as e:
                report.last_sent_status = False
                report.last_sent_error = str(e)
            finally:
                if os.path.exists(file_path):
                    os.remove(file_path)
                db.session.commit()



           

        except Exception as e:
            print(f"Error sending report ID {report.id}: {str(e)}")


if __name__ == '__main__':
    with app.app_context():
        run_scheduled_reports()

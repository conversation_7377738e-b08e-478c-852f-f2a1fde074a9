tags:
  - Affiliate_Profile
summary: Add New Address
description: >
  Allows an affiliate representative to add new addresses and associated SPOCs.
parameters:
  - name: body
    in: body
    required: true
    schema:
      type: array
      items:
        type: object
        properties:
          address:
            type: string
            description: Address.
          lat:
            type: number
            format: float
            description: Latitude.
          long:
            type: number
            format: float
            description: Longitude.
          nickname:
            type: string
            description: Nickname for the address.
          global:
            type: boolean
            description: Address type (global/local).
          spoc_list:
            type: array
            items:
              type: object
              properties:
                spoc_id:
                  type: integer
                  description: SPOC ID.
                spoc_name:
                  type: string
                  description: SPOC name.
responses:
  200:
    description: Addresses added successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Addresses added successfully.
  400:
    description: Invalid payload or missing fields.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Missing 'address' or 'nickname' for entry.
  500:
    description: Server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -3
        message:
          type: string
          example: An error occurred.

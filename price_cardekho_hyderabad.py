#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  price_cardekho_hyderabad.py
#
#  Copyright 2017-2021 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import datetime
from booking_params import BookingParams
from _utils import get_dt_ist
from db_config import db
from models import Bookings, Trip
import math

class PriceCardekhoHyderabad:
    MIN_DIST_THRESH = 25

    BASE_MINIMUM = 200
    BASE_FLAT = 5
    NIGHT_HIKE = 0

    BASE_MINIMUM_OP = (200, 200, 200)
    BASE_FLAT_OP = (5, 5, 5)
    NIGHT_HIKE_OP = (0, 0, 0)

    NIGHT_THRESH_0 = datetime.time(17, 0, 0)
    NIGHT_THRESH_1 = datetime.time(0, 30, 0)

    OP_DAYS = [[], [], []] #changed 90 120 120

    @staticmethod
    def update_op_days():
        PriceCardekhoHyderabad.OP_DAYS[0] = []
        PriceCardekhoHyderabad.OP_DAYS[1] = []
        PriceCardekhoHyderabad.OP_DAYS[2] = []

    @staticmethod
    def get_cancel_ch(curtime, starttime):
        return 0

    @staticmethod
    def get_booking_ch(car_type, driver_id, booking_id):
        return 0

    @staticmethod
    def get_insurance_ch(booking_id, city=0):
        return 0

    @staticmethod
    def get_dist_fare(dist, city=0):
        extra_dist = max(0, dist - PriceCardekhoHyderabad.MIN_DIST_THRESH)
        return extra_dist * PriceCardekhoHyderabad.BASE_FLAT

    @staticmethod
    def get_dist_fare_op(dist, op_level, city=0):
        extra_dist = max(0, dist - PriceCardekhoHyderabad.MIN_DIST_THRESH)
        return extra_dist * PriceCardekhoHyderabad.BASE_FLAT_OP[op_level]

    @staticmethod
    def get_op_level(startdate, starttime, enddate, endtime):
        start_ist = get_dt_ist(startdate, starttime)
        for level in range(len(PriceCardekhoHyderabad.OP_DAYS) - 1, -1, -1):

            if start_ist.date() in PriceCardekhoHyderabad.OP_DAYS[level]:
                return level
        return -1

    @staticmethod
    def get_price(dur, starttime, endtime, dist, startdate, enddate):
        PriceCardekhoHyderabad.update_op_days()
        op_level = PriceCardekhoHyderabad.get_op_level(startdate, starttime, enddate, endtime)
        if op_level == -1:
            return PriceCardekhoHyderabad.get_price_flat(dur, starttime, endtime, dist, startdate, enddate)
        else:
            return PriceCardekhoHyderabad.get_price_op(dur, starttime, endtime, dist, startdate, enddate)

    @staticmethod
    def get_price_flat(dur, starttime, endtime, dist, startdate, enddate):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0

        base_fare = PriceCardekhoHyderabad.BASE_MINIMUM
        dist_fare = PriceCardekhoHyderabad.get_dist_fare(dist)
        night = PriceCardekhoHyderabad.is_trip_night(starttime, endtime)
        night_fare = PriceCardekhoHyderabad.NIGHT_HIKE * int(night)
        total_fare = base_fare + dist_fare + night_fare
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), \
            int(round(dist_fare)), 0, 0, int(round(total_fare)), 0, 0, \
            int(round(total_fare)), 0, False  # surcharge


    @staticmethod
    def get_price_op(type, dur, starttime, endtime, dist, car_type, loc_cluster, op_level, insurance, insurance_num):
        base_fare = 0
        night_fare = 0
        dist_fare = 0
        total_fare = 0

        base_fare = PriceCardekhoHyderabad.BASE_MINIMUM_OP[op_level]
        dist_fare = PriceCardekhoHyderabad.get_dist_fare_op(dist, op_level)
        night = PriceCardekhoHyderabad.is_trip_night(starttime, endtime)
        night_fare = PriceCardekhoHyderabad.NIGHT_HIKE_OP[op_level] * int(night)
        total_fare = base_fare + dist_fare + night_fare
        return int(round(total_fare)), int(round(base_fare)), int(round(night_fare)), \
            int(round(dist_fare)), 0, 0, int(round(total_fare)), 0, 0, \
            int(round(total_fare)), 0, False  # surcharge


    @staticmethod
    def get_trip_price(est, book_starttime, book_stoptime, trip_starttime,
                       trip_stoptime,
                       startdate, enddate):

        PriceCardekhoHyderabad.update_op_days()
        op_level = PriceCardekhoHyderabad.get_op_level(startdate, book_starttime, enddate, book_stoptime)
        if op_level == -1:
            return PriceCardekhoHyderabad.get_trip_price_gen(est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime)
        else:
            # assume no 7 day roundtrip (lol)
            return PriceCardekhoHyderabad.get_trip_price_op(est, book_starttime, book_stoptime, trip_starttime,
                                            trip_stoptime, op_level)

    @staticmethod
    def get_trip_price_gen(est, book_starttime, book_stoptime, trip_starttime, trip_stoptime):
        price = est
        night = PriceCardekhoHyderabad.is_trip_night(trip_starttime, trip_stoptime)
        est_night = PriceCardekhoHyderabad.is_trip_night(book_starttime, book_stoptime)
        if night and not est_night:
            price += PriceCardekhoHyderabad.NIGHT_HIKE
        elif est_night and not night:
            price -= PriceCardekhoHyderabad.NIGHT_HIKE
        return int(round(price))

    @staticmethod
    def get_trip_price_op(est, book_starttime, book_stoptime, trip_starttime, trip_stoptime,
                          op_level):
        price = est
        night = PriceCardekhoHyderabad.is_trip_night(trip_starttime, trip_stoptime)
        est_night = PriceCardekhoHyderabad.is_trip_night(book_starttime, book_stoptime)
        if night and not est_night:
            price += PriceCardekhoHyderabad.NIGHT_HIKE_OP[op_level]
        elif est_night and not night:
            price -= PriceCardekhoHyderabad.NIGHT_HIKE_OP[op_level]
        return int(round(price))

    @staticmethod
    def is_trip_night(start, end):
        start = datetime.time(start.hour, start.minute)
        end = datetime.time(end.hour, end.minute)
        if PriceCardekhoHyderabad.NIGHT_THRESH_0 < PriceCardekhoHyderabad.NIGHT_THRESH_1:
            return ((start >= PriceCardekhoHyderabad.NIGHT_THRESH_0 and
                     start <= PriceCardekhoHyderabad.NIGHT_THRESH_1) or
                    (end >= PriceCardekhoHyderabad.NIGHT_THRESH_0 and
                     end <= PriceCardekhoHyderabad.NIGHT_THRESH_1))
        else:
            # end of night is after midnight UTC
            return ((start >= PriceCardekhoHyderabad.NIGHT_THRESH_0 or
                     start <= PriceCardekhoHyderabad.NIGHT_THRESH_1) or
                    (end >= PriceCardekhoHyderabad.NIGHT_THRESH_0 or
                     end <= PriceCardekhoHyderabad.NIGHT_THRESH_1))
[general]
#filename = drv.csv
#query = select COUNT(*),month(book_startdate) from bookings where book_type between 0 and 5 and year(book_startdate)=2019 group by month(book_startdate);
filename = gstudio-test-2022.csv
query = SELECT * from (bookings inner join (drivers inner join (select user_id as driver_user_id, user_mobile as driver_user_mobile, user_fname as driver_user_fname, user_lname as driver_user_lname from users where user_role=1) u1 on driver_user=u1.driver_user_id) on driver_id=book_driver) inner join users u2 on book_user=u2.user_id where book_type < 50 and book_startdate >= "2022-01-01";

<!DOCTYPE html>
<html>

<head>
    <title>Drivers4Me | Bhandari Booking Console</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
    <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='bootstrap.css') }}">
    <!--Required for glyphicons-->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/DateTimePicker/bootstrap-datetimepicker.min.css') }}">
    <!-- fa icons -->
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/font-awesome/css/font-awesome.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/bhandari.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/operations-common.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename = 'assets/css/custom-elements.css') }}">

    <script>
        window.jQuery || document.write('<script src="{{ url_for('static', filename='jquery.js') }}">\x3C/script>');
    </script>
    <script src="{{ url_for('static', filename='assets/DateTimePicker/moment.js') }}"></script>     <!-- for datetimepicker -->
    <script src="{{ url_for('static', filename='bootstrap.min.js') }}"></script>
    <!-- for datetimepicker-->
    <script src="{{ url_for('static', filename='assets/DateTimePicker/bootstrap-datetimepicker.min.js') }}"></script>
    <!--Vertical text clamping-->
    <script src="{{ url_for('static', filename='clamp.js') }}"></script>

    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>

    <!-- Add Firebase products that you want to use -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
    
    <script src="{{ url_for('static', filename='assets/js/operations-common.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/utility.js') }}"></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo&region=Dharmatala,Kolkata,West+Bengal,India&libraries=places&libraries=places"></script>
    <script src="{{ url_for('static', filename='assets/js/mapHelper.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/bhandari-bookings.js') }}"></script>
    <script src="{{ url_for('static', filename='assets/js/bhandari.js') }}"></script>
</head>

<body style="background-color: var(--d4m-body-primary);">
<nav id="topMenu" class="navbar navbar-default navbar-fixed-top" style="margin-bottom: 0;">
    <a id="brandName" class="navbar-brand" href="/" style="padding-left: 15px!important; padding-top: 0!important;">
        <img src="{{ url_for('static',filename='assets/images/bhandari_collab.svg') }}" class="img-rounded" alt="Bhandari + Drivers4Me" style="height: 100px;">
    </a>
    <button id="collapsedMainMenu" type="button" class="navbar-toggle" data-toggle="collapse" data-target="">
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
    </button>
    <div class="container-fluid collapse navbar-collapse" id="topBar">
        <button id="logout" class="btn navbar-btn d4m-primary-button navbar-right">
            Logout&emsp;<span class="glyphicon glyphicon-off"></span>
        </button>
        <ul id="navComponents" class="nav navbar-nav nav-components navbar-right">
            <!--<li class="nav-tab active" id="home"><a href="/bhandari">Home</a></li>-->
            <li class="nav-tab active" id="newBooking"><a href="#newBooking">New Booking</a></li>
            <li class="nav-tab inactive" id="bookings"><a href="#bookingList">Bookings</a></li>
            <li class="nav-tab inactive" id="newSpoc"><a href="#newSpoc">New SPOC</a></li>
        </ul>
    </div>
</nav>

<!--<div id="homePanel" class="row container-fluid function-panel">

</div>-->
<div id="newBookingPanel" class="row container-fluid function-panel collapse">

    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-holder">
        <h3 class="standard-header">New Booking</h3><hr class="d4m-separator">
        <form id="newBookingForm" enctype="multipart/form-data" autocomplete="off">
            <!--Booking ID and Trip Type-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Bhandari ID</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input name="text" type="text" class="form-control" id="clientBookingId" placeholder="Bhandari ID" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Trip Type</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <div>
                                <select class="form-control styled-select" id="tripTypeSelect">
                                    <option value="2">Home Delivery & Pickup</option>
                                    <option value="0">Home Delivery</option>
                                    <option value="1">Pickup</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--Model Name and Vehicle Registration Number-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Vehicle Model</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input name="text" type="text" class="form-control" id="vehicleModel" placeholder="Vehicle Model" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Vehicle Registration No.</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="text" type="text" class="form-control" id="vehicleRegistrationNumber" placeholder="Vehicle Registration Number" />
                        </div>
                    </div>
                </div>
            </div>
            <!--Transmission Type and Region-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Transmission Type</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <div>
                                <select class="form-control styled-select" id="transmissionTypeSelect">
                                    <option value="0">Manual</option>
                                    <option value="3">Automatic</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Region</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <select style="width: 100%" id="region">
                              <option value="0">Kolkata</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <!--Addresses-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Source Address</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input id="pickupSearch" list="p_hubs" class="form-control" type="text" placeholder="Source address">
                              <datalist id="p_hubs">
                                <option value="Bhandari - Salap Parking">
                                <option value="Bhandari - Sreerampore Parking">
                                <option value="Bhandari - Topsia Parking">
                                <option value="Bhandari - Maheshtala Parking">
                                <option value="Bhandari - Newtown Parking">
                                <option value="Bhandari - Alampur Parking">
                              </datalist>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Destination Address</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input id="dropSearch" list="d_hubs" class="form-control" type="text" placeholder="Destination address">
                            <datalist id="d_hubs">
                                <option value="Bhandari - Salap Parking">
                                <option value="Bhandari - Sreerampore Parking">
                                <option value="Bhandari - Topsia Parking">
                                <option value="Bhandari - Maheshtala Parking">
                                <option value="Bhandari - Newtown Parking">
                                <option value="Bhandari - Alampur Parking">
                             </datalist>

                        </div>
                    </div>
                </div>
            </div>
            <!--SPOC Details-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Source SPOC Name</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <select class="form-control styled-select" id="pickupNameSelect">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Source SPOC Contact</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="mobile" type="mobile" class="form-control" id="pickupMobile" value="" maxlength="10" disabled/>
                        </div>
                    </div>
                </div>

            </div>
            <!--Customer Name-->
            <div class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Destination SPOC Name</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <input name="dropName" type="text" class="form-control" id="dropName" placeholder="Customer Name" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Destination SPOC Contact</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="mobile" type="mobile" class="form-control" id="dropMobile" placeholder="Customer Contact Number" maxlength="10" onkeydown="return restrictToNumericInput(event)"/>
                        </div>
                    </div>
                </div>
                <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Destination SPOC Contact</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <input name="mobile" type="mobile" class="form-control" id="dropMobile" placeholder="Destination Contact Number" maxlength="10" onkeydown="return restrictToNumericInput(event)"/>
                        </div>
                    </div>
                </div>-->
            </div>
            <!--Scheduled delivery datetime-->
            <div id="PickupTimeInputSection" class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Pickup Date (DD/MM/YYYY)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_pickup_date'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Pickup Time (HH:MM)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_pickup_time'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Clock.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
            </div>
            <div id="DeliveryTimeInputSection" class="row small-bottom-padding">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <p>Delivery Date (DD/MM/YYYY)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_delivery_date'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                    <div class="row">
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <p>Delivery Time (HH:MM)</p>
                        </div>
                        <div class="col-lg-11 col-md-11 col-sm-11 col-xs-12 form_element align-right">
                            <!--dt picker-->
                            <div class="form-group">
                                <div class='input-group date' id='datetimepicker_delivery_time'>
                                    <input type='text' class="form-control" />
                                    <span class="input-group-addon custom-input-group">
                                        <span><img src="{{ url_for("static", filename="assets/images/booking-form/Clock.svg") }}"></span>
                                    </span>
                                </div>
                            </div>
                            <!--dtpicker-->
                        </div>
                    </div>
                </div>
            </div>
            <!--Button-->
            <div class="row small-bottom-padding">
                <div class=" col-lg-12 col-md-12 col-sm-12 col-xs-12 center-elements form_element">
                    <button id="book" type="button" class="btn btn-lg d4m-primary-button">Book Now</button>
                </div>
            </div>
        </form>
    </div>
</div>
<div id="bookingsPanel" class="row container-fluid function-panel collapse">
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">
        <nav id="bookingMenu" class="navbar navbar-default d4m-nav">
            <button id="collapsedBookingMenu" type="button" class="navbar-toggle" data-toggle="collapse" data-target="">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <div class="container-fluid collapse navbar-collapse" id="bookingTypeBar">
                <ul class="nav navbar-nav">
                    <li class="book-tab inactive" id="new"><a href="#newBookings" role="button">New</a></li>
                    <li class="book-tab inactive" id="upcoming"><a href="#upcomingBookings" role="button">Upcoming</a></li>
                    <li class="book-tab inactive" id="ongoing"><a href="#ongoingBookings" role="button">Ongoing</a></li>
                    <li class="book-tab inactive" id="completed"><a href="#completedBookings" role="button">Completed</a></li>
                    <li class="dropdown book-tab">
                        <a class="dropdown-toggle" data-toggle="dropdown" role="button">Cancelled
                            <span class="dropdown-choice-info trollFont collapse"></span><span class="caret"></span></a>
                        <ul class="dropdown-menu">
                            <li class="book-tab inactive" id="cancel-d4m"><a href="#d4mCancelBookings" role="button">by D4M</a></li>
                            <li class="book-tab inactive" id="cancel-bhandari"><a href="#bhandariCancelBookings" role="button">by Bhandari</a></li>
                        </ul>
                    </li>
                </ul>
                <button id="filters" class="btn navbar-btn" style="visibility:hidden;">
                    Filters&emsp;
                    <span id="filters-off" class="fa-stack fa-xs">
                      <i class="fa fa-filter fa-stack-1x"></i>
                      <i class="fa fa-ban fa-stack-2x"></i>
                    </span>
                    <span id="filters-on" class="fa fa-filter text-success fa-lg fa-collapse"></span>
                </button>
                <button id="refreshCurrent" class="btn navbar-btn align-right"><i class="fa fa-refresh"></i></button>
            </div>
        </nav>
    </div>
    <div id="filtersTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 collapse">
        <div class="row force-content-vertical-center">
            <div class="col-lg-offset-1 col-lg-5 col-md-offset-1 col-md-5 col-sm-12 col-xs-12 tiny-top-padding tiny-bottom-padding">
                <p><i>Click on any circular button to activate that filter group.</i></p>
            </div>
            <div class="col-lg-offset-1 col-lg-5 col-md-offset-1 col-md-5 col-sm-12 col-xs-12 tiny-top-padding tiny-bottom-padding">
                <p>
                    <i>Click on the search icon to refine the search with the current filters.</i>&emsp;
                    <button id="refreshBookingList" class="btn d4m-primary-button text-white"><i class="fa fa-search"></i></button>
                </p>
            </div>
        </div>
        <div id="dateFilters" class="row filter-container tiny-bottom-margin">
            <div class="col-lg-1 col-md-1 col-sm-6 col-xs-12">
                <div class="row force-content-vertical-center">
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 no-padding">
                        <div class="filter-category"></div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 no-padding">
                        <span>Filter by Dates</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 col-md-5 col-sm-12 col-xs-12">
                <label class="radio-inline btn d4m-primary-button filter-radio activity-trigger">
                    <input type="radio" name="dateOpt" value="day">&emsp;Today
                </label>
                <label class="radio-inline btn d4m-primary-button filter-radio activity-trigger">
                    <input type="radio" name="dateOpt" value="specific_date">&emsp;Date
                </label>
                <label id="customDateRangeFilter" class="radio-inline btn d4m-primary-button filter-radio activity-trigger">
                    <input type="radio" name="dateOpt" value="date_range">&emsp;Custom Range&emsp;
                </label>
            </div>
            <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                <div class="row">
                    <div id="single_date_picker" class="col-lg-6 col-md-6 col-sm-12 col-xs-12 collapse">
                        <span>Start&emsp;</span>
                        <!--dt picker-->
                        <div class="form-group">
                            <div class='input-group date activity-trigger' id='datetimepicker_filter_startdate'>
                                <input type='text' class="form-control" />
                                <span class="input-group-addon custom-input-group">
                                    <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                </span>
                            </div>
                        </div>
                        <!--dtpicker-->
                    </div>
                    <div id="date_range_picker" class="col-lg-6 col-md-6 col-sm-12 col-xs-12 collapse">
                        <span>End&emsp;</span>
                        <!--dt picker-->
                        <div class="form-group">
                            <div class='input-group date activity-trigger' id='datetimepicker_filter_enddate'>
                                <input type='text' class="form-control" />
                                <span class="input-group-addon custom-input-group">
                                    <span><img src="{{ url_for("static", filename="assets/images/booking-form/Calendar.svg") }}"></span>
                                </span>
                            </div>
                        </div>
                        <!--dtpicker-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="newBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="upcomingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="ongoingBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="completedBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="cancel-d4mBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
    <div id="cancel-bhandariBookingTab" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 booking-tab collapse full-width">

    </div>
</div>

</body>
tags:
  - Driver_admin
summary: Get Locality Information
description: >
  This endpoint retrieves the locality information based on the latitude and longitude provided by the user. It uses 
  MapMyIndia's reverse geocoding API to determine the locality. If no locality is found, it returns an appropriate response.
parameters:
  - name: lat
    in: formData
    required: true
    type: number
    description: Latitude of the location
    example: 28.7041
  - name: long
    in: formData
    required: true
    type: number
    description: Longitude of the location
    example: 77.1025
responses:
  200:
    description: Successfully retrieved the locality information
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        locality:
          type: string
          description: The locality name returned by the API
          example: "Connaught Place"
  400:
    description: Bad request (invalid input or API error)
    schema:
      type: string
      description: Error message if the API request failed
      example: "Error: 400, Bad Request"
  404:
    description: No locality found for the given coordinates
    schema:
      type: object
      properties:
        success:
          type: integer
          description: No locality found flag (0 for no locality)
          example: 0
        locality:
          type: string
          description: No locality found message
          example: "No locality found"
  500:
    description: Internal server error or exception during request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for server error)
          example: -1
        error:
          type: string
          description: Error message describing the server issue
          example: "Internal server error"
  422:
    description: Missing required latitude or longitude parameters
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for missing parameters)
          example: -4
        error:
          type: string
          description: Error message for missing parameters
          example: "I need input lat and long, how careless you ARE!"

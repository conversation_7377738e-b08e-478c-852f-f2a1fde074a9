tags:
  - Affiliate
summary: Get Affiliate Logs
description: >
  This endpoint retrieves logs related to affiliate activities, including details of changes made, the section where changes occurred, and who made the changes. Supports pagination and search functionality.
parameters:
  - name: page
    in: query
    required: false
    type: integer
    description: Page number for pagination (default is 1).
  - name: per_page
    in: query
    required: false
    type: integer
    description: Number of logs per page (default is 30).
  - name: client_name
    in: query
    required: true
    type: string
    description: Name of the affiliate client for which logs are being fetched.
  - name: search_query
    in: query
    required: false
    type: string
    description: Keyword to search logs by `changed_by`, `section`, `client_name`, or `changes_made`.
  - name: region
    in: query
    required: true
    type: string
    description: comma seperated region.
responses:
  200:
    description: Successfully retrieved affiliate logs.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (1 for success).
        log_details:
          type: array
          items:
            type: object
            properties:
              log_id:
                type: integer
                description: Unique ID of the log entry.
              log_date_time:
                type: string
                description: Timestamp of when the log entry was created, in `DD MMM YYYY, HH:mm` format.
              changed_by:
                type: string
                description: Name of the user who made the change.
              section:
                type: string
                description: Section of the affiliate data that was changed.
              changes_made:
                type: string
                description: Description of the changes made.
              changed_from:
                type: string
                description: Previous value before the change.
              changed_to:
                type: string
                description: New value after the change.
              affiliate_foreign_log_id:
                type: integer
                description: Foreign key ID linking to the affiliate record.
              client_name:
                type: string
                description: Name of the affiliate client associated with the log entry.
        total:
          type: integer
          description: Total number of logs available for the specified client.
        page:
          type: integer
          description: Current page number.
        per_page:
          type: integer
          description: Number of logs per page.
        pages:
          type: integer
          description: Total number of pages available.
  400:
    description: Client name is required but not provided.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (0 for failure).
        error:
          type: string
          description: Error message indicating missing `client_name`.
  500:
    description: Failed to retrieve affiliate logs due to an internal error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (0 for failure).
        error:
          type: string
          description: Detailed error message describing the internal issue.

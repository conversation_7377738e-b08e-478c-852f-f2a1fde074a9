var app = angular.module('D4M',[]);
app.controller('upcoming-ctrl', function($scope, $location){
	$scope.bookingEntries =[];
	angular.element(document).ready(function() {
		
		$("#pastTab").click(function() {
			$(".book-tab").removeClass("active");
			$(".booking-tab").addClass("collapse");
			$(this).addClass("active");
			$("#" + $(this).attr("id").replace("Tab", "") + "BookingTab").removeClass("collapse");
		});
		$("#upcomingTab").click(function() {
			showLoader();
			loadAllUpcoming($scope);
		});
		if ($location.hash() == "upcomingBookings" || $location.hash() == "") {
			showLoader();
			loadAllUpcoming($scope);
		}
	});
		
});

app.controller('past-ctrl', function($scope, $location){
	$scope.bookingEntries =[];
	angular.element(document).ready(function() {
		
		$("#upcomingTab").click(function() {
			$(".book-tab").removeClass("active");
			$(".booking-tab").addClass("collapse");
			$(this).addClass("active");
			$("#" + $(this).attr("id").replace("Tab", "") + "BookingTab").removeClass("collapse");
		
		});
		
		$("#pastTab").click(function() {
			showLoader();
			loadAllPast($scope);
		});
		if ($location.hash() == "pastBookings") {
			$(".book-tab").removeClass("active");
			$(".booking-tab").addClass("collapse");
			$(this).addClass("active");
			$("#pastTab").click();
		}
	});
});

function loadAllUpcoming($scope) {
	$scope.bookingEntries = [];
	$.ajax({
			type:"POST",
			url: "https://staging.drivers4me.com/api/user/pending_ride",
			dataType: "json",
			beforeSend: function(request) {
				//var c = getCookie();
				var csrf_token = "c91e47e2-4912-4886-afbe-b3cf4b4dd7d5"; //c['csrf_access_token'];
				var refresh_token = "28b89acd-b21a-4312-83c1-8e4f8ed053db" //c['csrf_refresh_token'];
				if (refresh_token) {
					/*if (checkRefresh(csrf_token, refresh_token) == false) {
						alert("Unfortunately, your session has expired. Please login again");
						window.location  = window.location.protocol + '//' + window.location.host + '/api/register_cust';
					}*/
				}
				request.setRequestHeader('X-CSRF-Token', csrf_token);
			 },
			contentType: false,
			processData: false,
			success: function(response) {
				if (response.success == '1')
					for (var i = 0; i < response.data.length; ++i) {
						setUpcomingData($scope, JSON.parse(response.data[i]))
					}
				
				hideLoader();
			},
			error: function(er) {
				showSnackbar("Could not fetch bookings", "snackbar-danger", 2000);
				hideLoader();
			}

	  })
}

function loadAllPast($scope) {
	$scope.bookingEntries = [];
	$.ajax({
			type:"POST",
			url: "https://staging.drivers4me.com/api/user/past_ride",
			dataType: "json",
			beforeSend: function(request) {
				//var c = getCookie();
				var csrf_token = "c91e47e2-4912-4886-afbe-b3cf4b4dd7d5"; //c['csrf_access_token'];
				var refresh_token = "28b89acd-b21a-4312-83c1-8e4f8ed053db" //c['csrf_refresh_token'];
				if (refresh_token) {
					/*if (checkRefresh(csrf_token, refresh_token) == false) {
						alert("Unfortunately, your session has expired. Please login again");
						window.location  = window.location.protocol + '//' + window.location.host + '/api/register_cust';
					}*/
				}
				request.setRequestHeader('X-CSRF-Token', csrf_token);
			 },
			contentType: false,
			processData: false,
			success: function(response) {
				if (response.success == '1')
					for (var i = 0; i < response.data.length; ++i) {
						setPastData($scope, JSON.parse(response.data[i]))
					}
				
				hideLoader();
			},
			error: function(er) {
				showSnackbar("Could not fetch bookings", "snackbar-danger", 2000);
				hideLoader();
			}

	  })
}

function setUpcomingData($scope, response) {
	
	tripType = webTripType(response["trip_type"])
	bookingID = response["id"]
	estimate = response["estimate"]
	paymentMode = response["payment"]
	paymentModeText = getPaymentModeText(paymentMode)
	bookingType = response["mobile"] == '' ? 'new' : 'upcoming'
	allocated = response["mobile"] != ''
	carType = parseInt(response["car_type"])
	vehicleCategory = getVehicleCategoryInformation(carType)
	driverName = response["driver_name"]
	driverMobile = response["mobile"]
	driverLicense = response["license"]
	driverRating = response["rating"]
	
	days = parseInt(response["days"])
	duration = getFormattedDuration(response["dur"])
	if (days > 0) {
		if (duration.length == 0)
			duration = days + " day(s)"
		else
			duration = days + " day(s), " + duration
	}
	scheduledDateTime = getFormattedDateTime(response["startdate"], response["starttime"])
	
	sourceLat = response["lat"]
	sourceLong = response["long"]
	sourceAddressString = response["loc"]
	destLat = response["dest_lat"]
	destLong = response["dest_long"]
	destAddressString = response["dest_loc"]
	
	
	if (!destLat && !destLong) {
		destLat = sourceLat
		destLong = sourceLong
		destAddressString = sourceAddressString
	}
		
	
	$scope.bookingEntries.push(
			{ 'bookingType': bookingType, 'tripTextType': getTripTypeText(tripType), 'bookingID': bookingID, 'allocated': allocated,
			  'paymentMode': paymentMode, 'fareEstimate': estimate, 'paymentModeText': paymentModeText,
			  'vehicleType': vehicleCategory["vehicleType"], 'transmissionType': vehicleCategory["transmissionType"],
			  'duration': duration,
			  'scheduledDateTime':scheduledDateTime, 'driverName': driverName,
			  'driverContact': driverMobile, 'driverLicence': driverLicense,
			  'driverRating': driverRating, 'sourceLat': sourceLat, 'sourceLong': sourceLong,
			  'destLat': destLat, 'destLong': destLong, 
			  'sourceAddressString': sourceAddressString,
			  'destAddressString': destAddressString
			}
		);
	$scope.$digest();
}


function setPastData($scope, response) {
	if (response["estimate"] == 0) {
		setCancelledData($scope, response)
	} else {
		setCompletedData($scope, response)
	}
}

function setCompletedData($scope, response) {
	tripType = webTripType(response["trip_type"])
	bookingID = response["id"]
	estimate = response["estimate"]
	paymentMode = response["payment"]
	paymentModeText = getPaymentModeText(paymentMode)
	bookingType = 'completed'
	allocated = response["driver_name"] != "Not Allocated"	
	carType = parseInt(response["car_type"])
	vehicleCategory = getVehicleCategoryInformation(carType)
	
	driverName = response["driver_name"]
	
	days = parseInt(response["days"])
	duration = getFormattedDuration(response["dur"])
	if (days > 0) {
		if (duration.length == 0)
			duration = days + " day(s)"
		else
			duration = days + " day(s), " + duration
	}
	scheduledDateTime = getFormattedDateTime(response["startdate"], response["starttime"])
	
	sourceLat = response["lat"]
	sourceLong = response["long"]
	sourceAddressString = response["loc"]
	destLat = response["dest_lat"]
	destLong = response["dest_long"]
	destAddressString = response["dest_loc"]
	
	estimate = response["estimate"]
	if (!destLat && !destLong) {
		destLat = sourceLat
		destLong = sourceLong
		destAddressString = sourceAddressString
	}
		
	
	$scope.bookingEntries.push(
			{ 'bookingType': bookingType, 'tripTextType': getTripTypeText(tripType), 'bookingID': bookingID, 'allocated': allocated,
			  'paymentMode': paymentMode, 'fareEstimate': estimate, 'paymentModeText': paymentModeText,
			  'vehicleType': vehicleCategory["vehicleType"], 'transmissionType': vehicleCategory["transmissionType"],
			  'duration': duration,
			  'scheduledDateTime':scheduledDateTime, 'driverName': driverName,
			  'sourceLat': sourceLat, 'sourceLong': sourceLong,
			  'destLat': destLat, 'destLong': destLong, 
			  'sourceAddressString': sourceAddressString,
			  'destAddressString': destAddressString,
			  'estimate': estimate
			}
		);
	$scope.$digest();
}

function setCancelledData($scope, response) {
	tripType = webTripType(response["trip_type"])
	bookingID = response["id"]
	estimate = response["estimate"]
	paymentMode = response["payment"]
	paymentModeText = getPaymentModeText(paymentMode)
	bookingType = 'cancel-d4m'
	allocated = response["driver_name"] != "Not Allocated"	
	carType = parseInt(response["car_type"])
	vehicleCategory = getVehicleCategoryInformation(carType)
	
	driverName = response["driver_name"]
	
	days = parseInt(response["days"])
	duration = getFormattedDuration(response["dur"])
	if (days > 0) {
		if (duration.length == 0)
			duration = days + " day(s)"
		else
			duration = days + " day(s), " + duration
	}
	scheduledDateTime = getFormattedDateTime(response["startdate"], response["starttime"])
	
	sourceLat = response["lat"]
	sourceLong = response["long"]
	sourceAddressString = response["loc"]
	destLat = response["dest_lat"]
	destLong = response["dest_long"]
	destAddressString = response["dest_loc"]
	
	cancelCharge = response["canc_ch"]
	if (!destLat && !destLong) {
		destLat = sourceLat
		destLong = sourceLong
		destAddressString = sourceAddressString
	}
		
	
	$scope.bookingEntries.push(
			{ 'bookingType': bookingType, 'tripTextType': getTripTypeText(tripType), 'bookingID': bookingID, 'allocated': allocated,
			  'paymentMode': paymentMode, 'fareEstimate': estimate, 'paymentModeText': paymentModeText,
			  'vehicleType': vehicleCategory["vehicleType"], 'transmissionType': vehicleCategory["transmissionType"],
			  'duration': duration,
			  'scheduledDateTime':scheduledDateTime, 'driverName': driverName,
			  'sourceLat': sourceLat, 'sourceLong': sourceLong,
			  'destLat': destLat, 'destLong': destLong, 
			  'sourceAddressString': sourceAddressString,
			  'destAddressString': destAddressString,
			  'cancelCharge': cancelCharge
			}
		);
	$scope.$digest();
}

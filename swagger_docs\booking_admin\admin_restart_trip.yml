tags:
  - Affiliate
summary: Restart a Trip as Admin
description: >
  Allows an admin (with BOOKINGS_AFFILIATE or BOOKINGS access) to restart a previously completed trip.
  The booking must exist and its trip must have been stopped before it can be restarted.
parameters:
  - in: formData
    name: book_id
    required: true
    type: string
    description: The ID of the booking whose trip should be restarted.
    example: "123"
  - in: formData
    name: regions
    required: true
    type: string
    description: comma seperated region.
    example: "1,2,3"
responses:
  200:
    description: Trip restarted successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: "Trip restarted successfully"
  201:
    description: Incomplete request or trip not in a stoppable state.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Trip is not stopped"
  404:
    description: Booking not found or cannot be restarted.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Booking not found"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Internal Server Error"

tags:
  - Driver_admin
summary: Verify Driver's Driving License
description: >
  This endpoint verifies a driver's driving license details by fetching data from an external API. 
  It checks if the driving license already exists in the system, fetches the license details if not present, 
  and verifies the details using a face match and other validations.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose driving license is to be verified
    example: 101
  - name: dl_no
    in: formData
    required: true
    type: string
    description: The driving license number of the driver
    example: "DL123456789"
  - name: dob
    in: formData
    required: true
    type: string
    format: date
    description: Driver's date of birth (YYYY-MM-DD format)
    example: "1990-01-01"
responses:
  200:
    description: Successfully verified the driver's driving license
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Status of the verification process (1 for successful verification, 0 for dispute, -1 for errors)
          example: 1
        message:
          type: string
          description: Message describing the outcome of the verification
          example: "Driving Licence Verified"
        details:
          type: object
          description: Verification details including name match, photo match, DOB match, and license expiry match
          properties:
            name_match:
              type: boolean
              description: Indicates whether the name matches
              example: true
            photo_match:
              type: boolean
              description: Indicates whether the photo matches
              example: true
            dob_match:
              type: boolean
              description: Indicates whether the date of birth matches
              example: true
            lic_exp_match:
              type: boolean
              description: Indicates whether the license expiry date matches
              example: true
            face_match_score:
              type: number
              format: float
              description: The score of the face match comparison
              example: 95.5
  400:
    description: Bad request (invalid driving license number or date of birth)
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for errors)
          example: -1
        message:
          type: string
          description: Error message
          example: "Invalid Driving Licence Number"
        error_code:
          type: string
          description: The specific error code returned from the external API
          example: "INVALID_DRIVING_LICENSE"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        status:
          type: integer
          description: Failure flag (-1 for server error)
          example: -1
        message:
          type: string
          description: Error message describing the server issue
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Internal server error"

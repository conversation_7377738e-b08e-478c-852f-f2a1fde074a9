#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  discord.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from flask import current_app as app
from db_config import db
from models import Bookings
from _ops_message import send_slack_msg
from booking_params import BookingParams

if __name__ == '__main__':
    with app.app_context():
        new_bookings = db.session.query(Bookings).filter(Bookings.valid == 0).all()
        no_broad = BookingParams.get_no_broadcast()
        if no_broad:
            is_shown = " NOT"
        else: is_shown = ""
        msg = "There are currently " + str(len(new_bookings)) + \
                " unallocated bookings. Bookings are currently" + is_shown + " being shown to all drivers."
        send_slack_msg(3, msg)
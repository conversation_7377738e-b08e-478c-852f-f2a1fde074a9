tags:
  - Affiliate
summary: Get Affiliate Data
description: >
  This endpoint retrieves affiliate data from Redis, MySQL, and MongoDB, based on the client name provided.
parameters:
  - name: body
    in: body
    required: true
    description: JSON payload containing the client name.
    schema:
      type: object
      properties:
        client_name:
          type: string
          description: Name of the affiliate client.
        regions:
          type: string
          description: A comma-separated list of region IDs for filtering.
        master:
          type: string
          description: Master for this Affiliate (Default 0).
responses:
  200:
    description: Successfully retrieved affiliate data.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (1 for success).
        source:
          type: string
          description: Source of the data (Redis or MySQL/MongoDB).
        master:
          type: string
          description: Name of the master affiliate.
        master_id:
          type: integer
          description: ID of the master affiliate.
        data:
          type: object
          description: Core affiliate data.
          properties:
            master:
              type: integer
              description: Master affiliate ID.
            slaves:
              type: array
              items:
                type: string
              description: List of slave affiliates.
            form_field_oneway:
              type: object
              description: Form field details for one-way trips.
            form_field_round:
              type: object
              description: Form field details for round trips.
            form_details:
              type: object
              description: Details about the affiliate form.
              properties:
                client_name:
                  type: string
                  description: Affiliate client name.
                client_display_name:
                  type: string
                  description: Display name of the affiliate client.
                select_group:
                  type: array
                  items:
                    type: string
                  description: Group selection options.
                city:
                  type: string
                  description: Region of the affiliate client.
            tripTypeLabel:
              type: string
              description: Label for trip type.
            tripTypePlaceholder:
              type: string
              description: Placeholder for trip type.
            tripTypes:
              type: array
              items:
                type: string
              description: List of trip types.
        customer_pricing_data:
          type: object
          description: Customer pricing details.
          properties:
            oneway:
              type: object
              description: Pricing data for one-way trips.
            roundtrip:
              type: object
              description: Pricing data for round trips.
        driver_pricing_data:
          type: object
          description: Driver pricing details.
          properties:
            oneway:
              type: object
              description: Pricing data for one-way trips.
            roundtrip:
              type: object
              description: Pricing data for round trips.
        pricing_cancellation_data:
          type: object
          description: Pricing data for cancellations.
  400:
    description: Client name is required but not provided.
    schema:
      type: object
      properties:
        error:
          type: string
          description: Error message indicating missing client name.
  404:
    description: Affiliate data not found in SQL or MongoDB.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (0 for not found).
        message:
          type: string
          description: Error message indicating data not found.
  500:
    description: Failed to retrieve affiliate data due to an internal error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Status of the operation (-1 for failure).
        error:
          type: string
          description: Error description.
        details:
          type: string
          description: Detailed error message.

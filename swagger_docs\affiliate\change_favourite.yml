tags:
  - Affiliate
summary: Change Favourite Status of a Draft
description: >
  This endpoint allows the user to mark or unmark a draft as a favourite. The user can only change the favourite status of their own drafts.

parameters:
  - name: body
    in: body
    required: true
    description: JSON payload containing the draft ID.
    schema:
      type: object
      properties:
        draft_id:
          type: integer
          description: The ID of the draft whose favourite status is to be changed.
        regions:
          type: string
          description: A comma-separated list of region IDs for filtering.
    required: true

responses:
  '200':
    description: Draft marked as favourite successfully.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: 1
            message:
              type: string
              example: Draft marked as favourite successfully
  '400':
    description: Invalid or missing draft ID in the request body.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Draft ID is required
  '403':
    description: Unauthorized user attempting to modify a draft they don't own.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Sorry! You are not authorized to use this Draft!
  '404':
    description: Draft not found or does not belong to the current user.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Draft not found or does not belong to you
  '500':
    description: Server error while attempting to mark the draft as favourite.
    content:
      application/json:
        schema:
          type: object
          properties:
            success:
              type: integer
              example: -1
            message:
              type: string
              example: Failed to mark draft as favourite
            error:
              type: string
              example: Error marking draft as favourite

#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  register_driver.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: Prachatos Mitra

import os
import uuid

from flask import Blueprint, request, jsonify
from sqlalchemy import exc
from werkzeug.utils import secure_filename
import datetime
from _utils import complete, upload_pic, is_valid_lat_long_world, get_locality_by_lat_long
import __init__, html
from _utils import validate_fields
from _utils import get_salt, get_pwd, get_safe
from booking_params import Regions
from models import Users, Drivers, DriverPermaInfo, DriverInfo
from models import db, DriverInfo, DriverBank, DriverVerify, DriverSkill
import time
from flasgger import swag_from
from sqlalchemy.exc import IntegrityError

from socketio_app import send_notification_to_channel, live_update_to_channel

regdrive = Blueprint('register_driver', __name__)

def create_driver_details(did):
    from db_config import db
    from models import DriverDetails
    details = DriverDetails(did=did)
    return details

def allowed_extension(ext):
    ext_list = ['jpg', 'png', 'jpeg']
    if ext not in ext_list:
        return False
    else:
        return True


def _complete(req):
    reqFields = ['mobile', 'pwd', 'pic', 'licPic', 'licNo']
    for field in reqFields:
        if field not in req.form:
            if req.files:
                if field not in req.files:
                    return False
            else:
                return False
    return True


# def create_driver_base_loc(driver_id, form):
#     lat = get_safe(form, 'lat', '')
#     lng = get_safe(form, 'lng', '')
#     pres_region = get_safe(form, 'region_name', '')
#     driver_base = DriverBaseLoc(driver_id, lat, lng, pres_region)
#     return driver_base


def create_driver_exp(driver_id, hb_m, sed_m, suv_m, lux_m, hb_a, sed_a, suv_a, lux_a):
    has_s = db.session.query(DriverSkill).filter(DriverSkill.driver_id == driver_id)
    if has_s.first():
        db.session.delete(has_s.first())
    s = DriverSkill(driver_id, hb_m, sed_m, suv_m, lux_m, hb_a, sed_a, suv_a, lux_a)
    return s


def create_driver_info(driver_id, files, form, url_lic_f, url_lic_b, url_id_f, url_id_b, url_pic, trip_pref,driver_languages,driver_reg_source,driver_bike_status, locality='', address=''):
    lic_no = form['lic_no']
    try:
        lic_exp = datetime.datetime.strptime(form['lic_exp_date'], "%d/%m/%Y").date()
    except Exception:
        lic_exp = None
    try:
        dob = datetime.datetime.strptime(form['dob'], "%d/%m/%Y").date()
    except Exception:
        dob = None
    pres_region = get_safe(form, 'region_name', '')
    pres_addr = get_safe(form, 'pres_addr', '')
    lat = get_safe(form, 'lat', '')
    lng = get_safe(form, 'lng', '')
    verf_name = get_safe(form, 'verf_name', '')
    verf_rel = get_safe(form, 'verf_rel', '')
    verf_ph = int(get_safe(form, 'verf_ph', 0))
    id_no = get_safe(form, 'id_no', '')

    # Extract and convert height
    height_feet = int(get_safe(form, 'height_feet', 0))  # Default to 0 if not provided
    height_inches = int(get_safe(form, 'height_inches', 0))  # Default to 0 if not provided
    height_cm = (height_feet * 30.48) + (height_inches * 2.54)  # Convert to cm

    # Extract weight
    weight = int(get_safe(form, 'weight', 0))  # Default to 0 if not provided

    # Prepare extras
    extras = {"height": round(height_cm, 2), "weight": weight}

    drv_info = DriverInfo(
        driver_id=driver_id,
        dob=dob,
        license=lic_no,
        license_exp=lic_exp,
        pres_region=locality if locality else pres_region,
        pres_addr=address if address else pres_addr,
        verf_name=verf_name,
        verf_ph=verf_ph,
        verf_rel=verf_rel,
        lat=lat,
        lng=lng,
        driver_id_doc_f=url_id_f,
        driver_id_doc_b=url_id_b,
        driver_lic_doc_f=url_lic_f,
        driver_lic_doc_b=url_lic_b,
        pic=url_pic,
        id_no=id_no,
        driver_trip_pref=trip_pref,
        extras=extras, # Pass the extras dictionary
        reg_lat = None,
        reg_lng= None,
        driver_languages=driver_languages,
        driver_reg_source=driver_reg_source,
        driver_bike_status=driver_bike_status
    )
    return drv_info

def create_driver_bank(driver_id, files, acc_no, ifsc, pan_no):
    acc_doc = get_safe(files, 'acc_doc', '')
    if acc_doc:
        acc_doc_url = upload_pic(acc_doc)
    else:
        acc_doc_url = ''
    drv_bank = DriverBank(driver_id, acc_no, ifsc, pan_no, acc_doc_url)
    return drv_bank


def create_driver_verif(driver_id):
    verif = DriverVerify(driver_id)
    return verif

@regdrive.route('/api/500', methods=['GET'])
def throw_500():
    1/0

@regdrive.route('/api/register/driver/validate', methods=['POST'])
def check_reg_complete():
    if not complete(request.form, ['mobile']):
        return jsonify({'success': -1, 'valid': -1})
    mobile = (get_safe(request.form, 'mobile', 0))
    user_exists = db.session.query(Users).filter(Users.mobile == mobile).first()
    if user_exists.first():
        driver_exists = db.session.query(Drivers).filter(Drivers.user == user_exists.first().id)
        if driver_exists.first():
            dinfo = db.session.query(DriverInfo).filter(DriverInfo.driver_id == driver_exists.first().id).first()
            dbank = db.session.query(DriverBank).filter(DriverBank.driver_id == driver_exists.first().id).first()
            if dinfo and dbank:
                valid = 3
            elif dinfo:
                valid = 2
            elif dbank:
                valid = 1
            else:
                valid = 0
            return jsonify({'success': 1, 'valid': valid})
        else:
            return jsonify({'success': 1, 'valid': -1})
    else:
        return jsonify({'success': 1, 'valid': -1})

@regdrive.route('/api/register/driver', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/reg_drv.yml')
def reg_drv():
    if not complete(request.form, ['fname', 'lname', 'mobile', 'pwd', 'lic_no', 'region']) or not complete(request.files, ['id_doc_back', 'lic_doc_back', 'pic', 'lic_doc', 'id_doc']):
        return jsonify({'success': -1, 'msg': "Incomplete form"}), 201

    from_where_register = request.headers.get('from_where_register', 0, type=int)

    alt_mobile = request.form.get("alt_mobile")
    # Basic details
    mobile = (get_safe(request.form, 'mobile', 0))
    pwd = request.form['pwd']
    lic_no = request.form['lic_no']
    pic_dat = request.files['pic']
    lic_doc_f_dat = request.files['lic_doc']
    lic_doc_b_dat = request.files['lic_doc_back']
    id_doc_f_dat = request.files['id_doc']
    id_doc_b_dat = request.files['id_doc_back']
    perma = bool(int(get_safe(request.form, 'perma', 0)))
    email = get_safe(request.form, 'email', '')
    region = int(Regions.city_to_region(get_safe(request.form, 'city', "Kolkata")))
    url_lic_f = ''
    url_pic = ''
    sex = Users.convert_sex((get_safe(request.form, 'sex', 'Male')))

    # Bank details
    acc_no = get_safe(request.form, 'acc_no', '')
    ifsc = get_safe(request.form, 'ifsc', '')
    pan_no = get_safe(request.form, 'pan_no', '')
    fname = get_safe(request.form, 'fname', '')
    lname = get_safe(request.form, 'lname', '')
    driver_languages = get_safe(request.form,'driver_languages','0')
    raw_bike_status = get_safe(request.form, 'driver_bike_status', 'false')
    driver_bike_status = 1 if raw_bike_status.lower() == 'true' else 0
    driver_reg_source = get_safe(request.form,'driver_reg_source',None)

    trip_pref = request.form.get('trip_pref', 3, type=int)
    # Other fields

    if not validate_fields(mobile, email):
        return jsonify({'response': -2, 'msg': "Invalid fields", 'fields': "Mobile or email", 'status': -2}), 201

    lat = request.form.get("lat", 0, type=int)
    lng = request.form.get("lng", 0, type=int)
    if not is_valid_lat_long_world(lat, lng):
        return jsonify({'response': -2, 'msg': "Invalid fields", 'fields': "lat or long", 'status': -2}), 201

    user_exists = db.session.query(Users).filter(Users.mobile == mobile).first()
    if not user_exists:
        if alt_mobile:
            user = Users(mobile=mobile, fname=fname,
                        lname=lname, email=email, pwd=pwd,
                        role=Users.ROLE_DRIVER, region=region,alt_mobile=alt_mobile)
        else:
            user = Users(mobile=mobile, fname=fname,
                        lname=lname, email=email, pwd=pwd,
                        role=Users.ROLE_DRIVER, region=region)
        db.session.add(user)
    else:
        salt = get_salt()
        db.session.query(Users).filter(Users.id == user_exists.id).update({Users.role: Users.ROLE_DRIVER, Users.sex: sex})
        db.session.query(Users).filter(Users.id == user_exists.id).update({Users.region: region})
        db.session.query(Users).filter(Users.id == user_exists.id).update({Users.salt: salt})
        db.session.query(Users).filter(Users.id == user_exists.id).update({Users.pwd: get_pwd(pwd, salt)})
        db.session.query(Users).filter(Users.id == user_exists.id).update({Users.fname: html.escape(fname), Users.lname: html.escape(lname)})
    try:
        db.session.flush()
    except exc.IntegrityError:
        db.session.rollback()
        return jsonify({'response': -3, 'msg': "Integrity constraint error"}), 403
    url_lic_f = upload_pic(lic_doc_f_dat)
    url_lic_b = upload_pic(lic_doc_b_dat)
    url_pic = upload_pic(pic_dat)
    url_id_f = upload_pic(id_doc_f_dat)
    url_id_b = upload_pic(id_doc_b_dat)
    if not url_lic_f or not url_pic or not url_id_f:
        db.session.flush()
        return jsonify({'response': -4, 'msg': "Invalid fields", 'fields': "License / ID or profile picture"}), 201
        return jsonify({'response': -4, 'msg': "Invalid fields", 'fields': "License / ID or profile picture"}), 201
    cur_user = Users.query.filter_by(mobile=request.form['mobile'])
    if not cur_user or not cur_user.first():
        db.session.flush()
        return jsonify({'response': -5, 'msg': 'Failed to create account'}), 403
    driver_exists = db.session.query(Drivers).filter(Drivers.user == cur_user.first().id)
    if not driver_exists.first():
        driver = Drivers(curUser=cur_user.first(), licNo=lic_no, licDoc=url_lic_f,
                         pic=url_pic,
                         #id=url_id,
                         perma=perma)
    try:
        if not driver_exists.first():
            db.session.add(driver)
            db.session.flush()
            driver_id = driver.id
            print("Driver id is", driver_id)
        else:
            driver_id = driver_exists.first().id
        details = create_driver_details(driver_id)

        loc_and_addr = get_locality_by_lat_long(request.form.get("lat"), request.form.get("lng"))
        locality, address = '', ''
        if loc_and_addr:
            locality, address = loc_and_addr[0], loc_and_addr[1]
            print('locality', locality, address, flush=True)
        bank = create_driver_bank(driver_id, request.files, acc_no, ifsc, pan_no)
        info = create_driver_info(driver_id, request.files, request.form, url_lic_f, url_lic_b, url_id_f, url_id_b, url_pic, trip_pref,driver_languages,driver_reg_source,driver_bike_status, locality, address)
        verif = create_driver_verif(driver_id)
        # base_loc = create_driver_base_loc(driver_id, request.form)
        try:
            hb_m = request.form.get("hb_m", 1, type=int)
            sed_m = request.form.get("sed_m", 1, type=int)
            suv_m = request.form.get("suv_m", 1, type=int)
            lux_m = request.form.get("lux_m", 1, type=int)
            hb_a = request.form.get("hb_a", 1, type=int)
            sed_a = request.form.get("sed_a", 1, type=int)
            suv_a = request.form.get("suv_a", 1, type=int)
            lux_a = request.form.get("lux_a", 1, type=int)
            driver_exp = create_driver_exp(driver_id, hb_m, sed_m, suv_m, lux_m, hb_a, sed_a, suv_a, lux_a)
            db.session.add(driver_exp)
        except Exception as e:
            print(str(e))
        db.session.add(details)
        db.session.add(bank)
        db.session.add(info)
        db.session.add(verif)
        # db.session.add(base_loc)

        driver_data = {
            "driver_id": driver_id,
            "mobile": cur_user.first().mobile,
            "name": cur_user.first().fname + ' '+cur_user.first().lname,
            "location": info.pres_region,
            "region": cur_user.first().region,
            "rating": driver.rating,
            "rating_count": details.rating_count,
            "approval": driver.approved,
            "status": driver.available,
            "image": driver.pic,
            "timestamp": f'{cur_user.first().reg}',
            "label": cur_user.first().label_bv
        }
        room_name = 'Driver Register:'+str(cur_user.first().region)
        # socketio.emit('channel_data', {'info': driver_data, 'channel': room_name}, room=room_name, namespace='/')
        notification = {
            'id':driver_id,
            'type': 'Admin Register' if from_where_register else 'App Register',
            'username': str(cur_user.first().id),
            'content': f'New driver {fname} {lname} registered.',
            'imageUrl': '/assets/icons/action/actionDriver.svg',
            'timestamp': int(time.time() * 1000)
        }
        send_notification_to_channel(notification, room_name)
        live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=cur_user.first().region, channel= 'new_driver')
        db.session.commit()
    except IntegrityError as ie:
        db.session.rollback()
        print(f"IntegrityError: {ie}")
        return jsonify({'response': 4, 'msg': "Duplicate license or mobile entry detected"}), 403
    except Exception as e:
        print(e)
        db.session.rollback()
        return jsonify({'response': 4, 'msg': "Database error - " + str(e)}), 500
    return jsonify({'response': 0, 'msg': "Success"}), 200

# DEPRECATED
def register_driv_scr():
    if request.method == 'POST':
        if not _complete(request):
            return jsonify([{'response': 1, 'msg': "Incomplete form"}])
        else:
            if 'perma' not in request.form:
                perma = False
            else:
                perma = bool(int(request.form['perma']))
            if 'email' not in request.form:
                e_mail = ''
            else:
                e_mail = request.form['email']
            if 'region' not in request.form:
                region = 0
            else:
                region = int(request.form['region'])
            url_lic = ''
            url_pic = ''
            if validate_fields(request.form['mobile'], e_mail) and 'regmob' not in request.form:
                user_exists = db.session.query(Users).filter(Users.mobile == request.form['mobile']). \
                                    first()
                if not user_exists:
                    user = Users(mobile=request.form['mobile'], fname=request.form['fname'],
                                 lname=request.form['lname'], email=e_mail, pwd=request.form['pwd'],
                                 role=Users.ROLE_DRIVER, region=region)
                    try:
                        db.session.add(user)
                    except exc.IntegrityError:
                        return jsonify([{'response': 4, 'msg': "Integrity constraint error"}])
                else:

                    salt = get_salt()
                    db.session.query(Users).filter(Users.id == user_exists.id).update({Users.role: Users.ROLE_DRIVER})
                    db.session.query(Users).filter(Users.id == user_exists.id).update({Users.salt: salt})
                    db.session.query(Users).filter(Users.id == user_exists.id).update({Users.pwd: get_pwd(request.form['pwd'], salt)})
                db.session.commit()
                url_lic = upload_pic(request.files['licPic'])
                url_pic = upload_pic(request.files['pic'])
                if url_lic is False or url_pic is False:
                    print("Here")
                    db.session.flush()
                    return jsonify([{'response': 3, 'msg': "Invalid fields", 'fields': "License or profile picture"}])
                try:
                    curUser = Users.query.filter_by(mobile=request.form['mobile']).first()
                except exc.IntegrityError:
                    return jsonify([{'response': 4, 'msg': "Integrity constraint error"}])
            elif 'regmob' in request.form:
                print("Here2")
                curUser = Users.query.filter_by(mobile=request.form['regmob']).first()
            else:
                print("Here3")
                return jsonify([{'response': 2, 'msg': "Invalid fields", 'fields': "Mobile or email"}])
            driver = Drivers(curUser=curUser, licNo=request.form['licNo'], licDoc=url_lic,
                             pic=url_pic, perma=perma)
            try:
                db.session.add(driver)
                db.session.commit()
                driver_id = db.session.query(Drivers).filter(Drivers.user == curUser.id).first().id
                create_driver_details(driver_id)
                if perma:
                    hours = get_safe(request.form, 'hours', 10)
                    salary = get_safe(request.form, 'salary', 10000)
                    ta = get_safe(request.form, 'ta', 20)
                    ot = get_safe(request.form, 'ot', 40)
                    perm = DriverPermaInfo(driver_id, DriverPermaInfo.ALLOC_CUST, salary, ta, ot, hours)
                    db.session.add(perm)
                    db.session.commit()
            except exc.IntegrityError as e:
                print("Here4", e)
                db.session.rollback()
                return jsonify([{'response': 4, 'msg': "Integrity constraint error"}])
            return jsonify([{'response': 0, 'msg': "Success"}])

    else:
        return jsonify([{'response': 3, 'msg': "Bad request"}])

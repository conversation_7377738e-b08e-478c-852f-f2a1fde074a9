tags:
  - User
summary: Set User Location
description: >
  This endpoint allows a user to set or update their current latitude and longitude. It stores the user's location in the database.
parameters:
  - name: lat
    in: formData
    type: string
    required: true
    description: Latitude of the user's location
  - name: lng
    in: formData
    type: string
    required: true
    description: Longitude of the user's location
responses:
  200:
    description: Location successfully updated.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Location added successfully"
    examples:
      application/json:
        success: 1
        message: "Location added successfully"
  401:
    description: Unauthorized - Invalid user credentials or restricted user
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User does not exist"
    examples:
      application/json:
        success: -1
        message: "User does not exist"
  201:
    description: Incomplete form data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -1
        message: "Incomplete form details"

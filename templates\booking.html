<!DOCTYPE html>
<html>

<head>

    <title>Book a driver | Drivers4Me</title>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">

    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/bootstrap.css") }}"/>
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/jquery-ui.css") }}"/>
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/jquery.flipster.css") }}">
    <!--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.css">-->
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
    <!--<link rel="stylesheet" href="https://unpkg.com/bootstrap-material-design@4.1.1/dist/css/bootstrap-material-design.min.css" integrity="sha384-wXznGJNEXNG1NFsbm0ugrLFMQPWswR3lds2VeinahP8N0zJw9VWSopbjv2x7WCvX" crossorigin="anonymous">-->
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/mobiscroll-lite/mobiscroll.jquery.lite.min.css") }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/material-datepicker.css") }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/utility.css") }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/nav-common.css") }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/booking-page.css") }}">
    <link href="{{ url_for("static", filename="assets/css/hamburger.css") }}" rel="stylesheet">

    
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/jquery-3.2.1.min.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/jquery-ui.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/popper.min.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/bootstrap.min.js") }}"/></script>
    <!--<script src="https://unpkg.com/bootstrap-material-design@4.1.1/dist/js/bootstrap-material-design.js" integrity="sha384-CauSuKpEqAFajSpkdjv3z9t8E7RlpJ1UP0lKM/+NdtSarroVKu069AlsRPKkFBz9" crossorigin="anonymous"></script>-->
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/dateTimeUtility.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/jquery.flipster.min.js") }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.24.0/moment.min.js"></script>
    <!--<script type="text/javascript" src="{{ url_for("static", filename="assets/js/bootstrap-datetimepicker-v4.17.47.js") }}"></script>-->
    <script type="text/javascript" src="{{ url_for("static", filename="assets/mobiscroll-lite/mobiscroll.jquery.lite.min.js") }}"></script>
    <!--<script src="https://wchat.freshchat.com/js/widget.js"></script>-->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <!-- Add Firebase products that you want to use -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-database.js"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/constants.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/moment.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/material-datepicker.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/util-web.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/nav-common.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/mapUtility.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/booking-page.js') }}"></script>
    <script type="text/javascript" src="{{ url_for('static', filename='assets/js/booking-integ.js') }}"></script>    
    <!--<script type="text/javascript" src="{{ url_for("static", filename="assets/js/freshchat.js") }}"></script>-->
</head>

<body>
    <nav id="brandNav" class="navbar navbar-default">
      <!--<button id="mainNavMenuButton" class="hamburger hamburger--arrow js-hamburger" type="button">
        <span class="hamburger-box">
          <span class="hamburger-inner"></span>
        </span>
      </button>
      -->
      <h5 id="pageTitle">Bookings</h5>
        <div class="container-fluid">
            <div class="navbar-header">
                <a id="brand" class="navbar-brand brand-basic" href="/">
                    <img src="{{ url_for("static", filename="assets/images/brandLogoMerger.png") }}" alt="DRIVERS4ME">
                </a>
                <a id="brandBorder" class="navbar-brand brand-basic" href="/">
                    <img src="" alt="">
                </a>
            </div>
            <div class="ml-auto">
                <div class="nav navbar-nav">
                    <a id="userProfile" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/Avatar.svg") }}">
                    </a>
                    <a class="nav-item nav-link active nav-user-info-main no-padding" href="#">
                        <p id="userName" class="nav-user-info"></p>
                        <p id="userContact" class="nav-user-info">8882012345</p>
                        <span class="sr-only">(current)</span>
                    </a>
                    <a id="logout" class="nav-item nav-link active no-padding" href="#">
                        <img src="{{ url_for("static", filename="assets/images/elements/logout.svg") }}">
                    </a>
                </div>
            </div>
        </div>
    </nav>
    <!-- Toggler/collapsibe Button for SideNav -->

    <div id="sidenavBackdrop" class="backdrop collapse"></div>
    <div id="mainNavMenu" class="side-nav">
        <div class="side-nav-header">
            <img id="userImageHolder" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}">
            <h6 id="userNameHolder">Sample User</h6>
            <h6 id="userContactHolder">88820 12345</h6>
        </div>
        <div class="side-nav-body">
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Bookings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Bookings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Payments.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Payments</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Refer.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Refer</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
            <div class="nav-menu-item row">
                <div class="col-3">
                    <img src="{{ url_for("static", filename="assets/images/elements/Settings.svg") }}" alt="DRIVERS4ME">
                </div>
                <div class="col-9 nav-item-text"><p>Settings</p></div>
            </div>
            <div class="navMenuItemBorder">
            </div>
        </div>
        <div class="side-nav-footer">
            <p class="copyright-statement">All rights reserved. <br> &copy; Copyright <span id="copyrightYear"></span></p>
        </div>
    </div>

  <div id="map">
  </div>
  <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyA3TVv_EAXPHYtxnsFpaj6UmZNEMSLdFpo&callback=myMap&libraries=places"></script>
  <!--div id="tripType" style="width:400px;height:10vh; position: absolute; bottom: 0;left: 0;right: 0;margin: auto;background-color: white;">
    </div!-->
  <div class="row booking-location-section" id="locations">
    <div class="col-12 col-sm-12 col-md-8 col-lg-6 offset-lg-3 offset-md-2 booking-param-container">
      <div id="wrapper-source">
        <div class="green-circle"></div><div class="from-address">From</div>
      </div>
      <input id="sourceSearch" class="form-control" type="text" placeholder="Enter Pickup Location">
      <hr id="locSeparator" class="collapse">
        <div id="wrapper-destination" class="collapse">
          <div class="red-circle circle-inactive"></div><div class="to-address">To</div>
        </div>
      <input class="collapse form-control" id="destinationSearch" type="text" placeholder="Enter Drop Location">
    </div>
  </div>
  <script>initMapInputs();</script>

    <button id="liveTripRefresh" class="btn btn-success animated-hover-widen button-hide">
        <i class="fa fa-refresh pr-2"></i> Live Trip Details
    </button>

    <button id="newBooking" class="btn btn-success button-hide">
        <i class="fa fa-plus pr-2"></i> New Booking <i class="fa fa-chevron-right pl-2"></i>
    </button>

    <button id="trackDriver" class="btn btn-success button-hide">
        <i class="fa fa-map-marker pr-2"></i> Track Driver
    </button>

    <button id="currentTripDetails" class="btn btn-warning button-hide">On Trip Details</button>

    <button id="pendingBookings" class="btn btn-success button-hide">
        Pending Bookings&emsp;<span id="pending-count" class="badge badge-pill badge-light">0</span>
    </button>

  <div class="row booking-param-section shrinked collapse" id="onTripDetails">
      <div class="col-12 col-sm-12 col-md-12 col-lg-12 booking-param-container">
          <button id="onTripExtensionToggle" class="btn btn-success">
              <img src="{{ url_for("static", filename="assets/images/elements/angle-double-up.png") }}">
          </button>
              <div class="extended-content" id="onTripHeader">
                  <span class="on-trip-header-text">Booking ID: </span><span class="on-trip-header-text" id="liveBookID">18923</span>
                  <img class="pre-estimate-header-element collapse" id="liveIndSurge" src="{{ url_for("static", filename="assets/images/elements/Surcharge.svg") }}">
                  <img class="pre-estimate-header-element collapse" id="liveIndNight1" src="{{ url_for("static", filename="assets/images/elements/night1.svg") }}">
                  <img class="pre-estimate-header-element collapse" id="liveIndNight2" src="{{ url_for("static", filename="assets/images/elements/night2.svg") }}">
              </div>
          <div class="row no-margin on-trip-container extended-content">
              <div class="col-6 col-lg-6 col-md-6 col-sm-6 on-trip-details">
                  <img id="driverPicture" src="{{ url_for("static", filename="assets/images/elements/user_placeholder.svg") }}" />
                  <p class="small-top-margin live-trip-label">Driver Name</p>
                  <p class="small-top-margin" id="liveDriverName">Test Driver</p>
              </div>
              <div class="col-6 col-lg-6 col-md-6 col-sm-6 on-trip-details">
                  <p class="live-trip-label">Payment Mode</p>
                  <p class="small-top-margin" id="livePaymentMode"><img src="{{ url_for("static", filename="assets/images/elements/d4m_credit.svg") }}" /> <span class="payment-mode-text">D4M Credit</span></p>
                  <p class="live-trip-label medium-top-margin">Trip Started On</p>
                  <p class="small-top-margin" id="liveTripStart">09:30 A.M. | 23/02/2020</p>
              </div>
          </div>
          <div class="row no-margin on-trip-container">
              <div class="col-6 col-lg-6 col-md-6 col-sm-6 on-trip-details">
                  <p class="live-trip-label medium-top-margin">Fare</p>
                  <p class="small-top-margin" id="liveTripFare">&#8377; <span class="fare-amount">999</span></p>
                  <button id="emergency" class="btn btn-danger on-trip-button">Emergency</button>
              </div>
              <div class="col-6 col-lg-6 col-md-6 col-sm-6 on-trip-details">
                  <p class="live-trip-label medium-top-margin">Trip Duration</p>
                  <p class="small-top-margin" id="liveTripDur">3 Hrs, 12 Mins</p>
                  <button id="callDriver" class="btn btn-success on-trip-button">Call driver</button>
              </div>
          </div>

      </div>
  </div>

  <div class="row booking-param-section" id="tripType">
    <div id="tripContainer" class="col-12 col-sm-12 col-md-12 col-lg-12 booking-param-container">
      <div class="trip-type-wrapper">
        <div style="width: 25%;display: inline-block;margin: 0px;text-align: center;height: 100%;">
          <div id="RoundTrip" class="trip-type active" trip-type-value="1">
              <img class="trip-type-image" src="{{ url_for("static", filename="assets/images/elements/RoundTrip_Selected.svg") }}" width="80" height="80">
          </div>
            <p class="trip-type-caption">In-City Round
                <!--img class="trip-type-information" src="{{ url_for("static", filename="assets/images/elements/info_green.svg") }}"
                data-toggle="popover" title="In-City Round Trip" data-trigger="hover" data-placement="top"
                data-content="A city has its own bounds as specified in our map with a black dotted boundary. In City trips are with the bounds of that particular city.
                In round trips, your trip begins and ends at the same (starting) location."!-->
            </p>
        </div>
        <div style="width: 25%;display: inline-block;margin: 0px;text-align: center;">
            <div id="OneWay" class="trip-type" trip-type-value="2">
                <img class="trip-type-image" src="{{ url_for("static", filename="assets/images/elements/OneWay.svg") }}" width="80" height="80">
            </div>
            <p class="trip-type-caption">In-City One-Way
                <!--img class="trip-type-information" src="{{ url_for("static", filename="assets/images/elements/info_green.svg") }}"
                data-toggle="popover" title="In-City Oneway Trip" data-trigger="hover" data-placement="top"
                data-content="A city has its own bounds as specified in our map with a black dotted boundary. In City trips are with the bounds of that particular city.
                In one way trips, your trip will begin at the chosen starting location. The Driver Companion will end your trip at the chosen destination location."!-->
            </p>
        </div>
        <div style="width: 25%;display: inline-block;margin: 0px;text-align: center;">
          <div id="MiniOS" class="trip-type" trip-type-value="3">
              <img class="trip-type-image" src="{{ url_for("static", filename="assets/images/elements/MiniOS.svg") }}" width="80" height="80">
          </div>
            <p class="trip-type-caption">Mini Outstation
                <!--img class="trip-type-information" src="{{ url_for("static", filename="assets/images/elements/info_green.svg") }}"
                data-toggle="popover" title="Mini-Outstation Round Trip" data-trigger="hover" data-placement="top"
                data-content="Mini-Outstation trips are shorter versions long-distance trips where one or more city bounds maybe crossed.
                Currently we support only Mini-Outstation round trips. Standard mini-outstation trips may be booked for 3-14 hours."!-->
            </p>
        </div>
        <div style="width: 25%;display: inline-block;margin: 0px;text-align: center;">
          <div id="Outstation" class="trip-type" trip-type-value="4">
              <img class="trip-type-image" src="{{ url_for("static", filename="assets/images/elements/Outstation.svg") }}" width="80" height="80">
          </div>
            <p class="trip-type-caption">Outstation
                <!--img class="trip-type-information" src="{{ url_for("static", filename="assets/images/elements/info_green.svg") }}"
                data-toggle="popover" title="Outstation Round Trip" data-trigger="hover" data-placement="top"
                data-content="Outstation trips are long-distance trips where one or more city bounds maybe crossed.
                Currently we support only Outstation round trips. Standard outstation trips may be booked for 1-10 days."!-->
            </p>
        </div>
      </div>
      <button id="continueTripType" type="button" class="btn btn-success btn-booking-step-proceed">Continue</button>
    </div>
  </div>

  <div class="row booking-param-section collapse" id="carType">
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 booking-param-container">
      <div style="height:51px;width:100%;background-color: whitesmoke;position:absolute;top:0px;text-align: center;">
        <font style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;text-align: center;vertical-align: middle;line-height: 51px;" size="4">Select Your Car Type</font>
      </div>
      <div class="btn-group btn-group-toggle" data-toggle="buttons" style="width: 250px;left: 50%;margin-left: -125px;top: 61px;">
        <label class="btn btn-outline-primary active">
          <input type="radio" name="options" id="manual" autocomplete="off" checked> Manual
        </label>
        <label class="btn btn-outline-primary">
          <input type="radio" name="options" id="auto" autocomplete="off"> Automatic
        </label>
      </div>
      <div class="carSelect" style="height:105px;width:100%;position: absolute; bottom: 98px;">
        <ul>
          <li id="hatchback"><div  style="height: 82px;width: 220px;text-align: center;"><img src="{{ url_for("static", filename="assets/images/elements/Hatchback.svg") }}">Hatchback</li>
          <li id="sedan"><div  style="height: 82px;width: 220px;text-align: center;"><img src="{{ url_for("static", filename="assets/images/elements/Sedan.svg") }}">Sedan</li>
          <li id="suv"><div  style="height: 82px;width: 220px;text-align: center;"><img src="{{ url_for("static", filename="assets/images/elements/suv.svg") }}">SUV</li>
          <li id="luxury"><div  style="height: 82px;width: 220px;text-align: center;"><img src="{{ url_for("static", filename="assets/images/elements/luxury.svg") }}">Luxury</li>
        </ul>
      </div>
      <script>

      </script>
      <button id="closeCarType" class="btn btn-outline-danger btn-booking-step-cancel btn-sm">Close</button>
      <button id="continueCarType" class="btn btn-success btn-booking-step-proceed">Schedule</button>
    </div>
  </div>

  <div class="row booking-param-section collapse" id="schedule">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 booking-param-container">
          <div class="time-header">
              <font style="font-size: 22px;font-weight: 600;">Schedule Your Trip</font><font>Tap to Edit</font>
          </div>
            <input class="collapse" mbsc-input id="date-picker" data-input-style="box" placeholder="Please Select..." />
            <div id="openDatePicker" class="timing-params-container">
                <p id="dateText">Select Date</p>
            </div>
            <input class="collapse" mbsc-input id="time-picker" data-input-style="box" placeholder="Please Select..." />
            <div id="openTimePicker" class="timing-params-container">
                <p id="timeText">Select Time</p>
            </div>
            <input class="collapse" mbsc-input id="dur-picker" data-input-style="box" placeholder="Please Select..." />
            <div id="openDurationPicker" class="timing-params-container">
                <p id="durText">Select <span class="dur-type"></span></p>
            </div>
            <button id="closeSchedule" class="btn btn-sm btn-outline-danger btn-booking-step-cancel">Close</button>

            <button id="continueSchedule" class="btn btn-success btn-booking-step-proceed">Get Estimate</button>
        </div>
    </div>

    <div class="row booking-param-section collapse" id="fareEstimate">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 booking-param-container">
            <div class="estimate-header">
                <div class="center-header-estimate">
                    <img class="pre-estimate-header-element hidden" id="ind-Surge" src="{{ url_for("static", filename="assets/images/elements/Surcharge.svg") }}">
                    <img class="pre-estimate-header-element hidden" id="ind-Night1" src="{{ url_for("static", filename="assets/images/elements/night1.svg") }}">
                    <img class="pre-estimate-header-element collapse" id="ind-Night2" src="{{ url_for("static", filename="assets/images/elements/night2.svg") }}">
                    <span id="estimatedAmount" class="text-white estimate-style">0</span>
                    <img class="pre-estimate-header-element" id="viewFareBreakdown" src="{{ url_for("static", filename="assets/images/elements/info_white.svg") }}">
                </div>
              <p align="center" class="default-fare text-white">Estimated Fare</p>
            </div>
            <div class="estimate-holder-size">
              <div class="row estimate-holder-button-size">
                  <div class="col-6 col-sm-6 col-lg-6 param-change-container">
                      <div class="pre-book-alter" id="alterDateTime">
                          <img class="pre-estimate-element" src="{{ url_for("static", filename="assets/images/elements/date_time.svg") }}">
                          <span class="date-text">--/--/----</span> <span class="pre-book-text">at</span> <span class="time-text">--:-- -M</span>
                      </div>
                  </div>
                  <div class="col-6 col-sm-6 col-md-6 col-lg-6 param-change-container-right">
                      <div class="pre-book-alter-right" id="alterDur">
                          <img class="pre-estimate-element" src="{{ url_for("static", filename="assets/images/elements/Duration.svg") }}">
                          <span class="pre-book-text">Requested for</span> <span class="dur-text">0</span> <span class="pre-book-text pre-book-time-unit-text">hours</span>
                      </div>
                  </div>
              </div>
              <div class="row estimate-holder-button-size" >
                  <div class="col-6 col-sm-6 col-md-6 col-lg-6 param-change-container">
                      <div class="pre-book-alter" id="couponSelector">
                          <img title="Coupons are coming soon." class="pre-estimate-element" src="{{ url_for("static", filename="assets/images/elements/apply_coupon.svg") }}">
                          <span title="Coupons are coming soon." class="pre-book-text">Apply Coupon</span> <span title="Coupons are coming soon." class="coupon-text"></span>
                      </div>
                  </div>
                  <div class="col-6 col-sm-6 col-md-6 col-lg-6 param-change-container-right">
                      <div class="pre-book-alter-right" id="alterPaymentMode">
                        <img id="payment-type-image" class="pre-estimate-element" src="{{ url_for("static", filename="assets/images/elements/select_payment_method.svg") }}">
                        <span id="payment-type-text"class="payment-text">Select Payment Mode</span>
                    </div>
                  </div>
                  <ul id="payment-picker" class="mbsc-cloak">
                    <li data-val="0">
                        <img src="{{ url_for("static", filename="assets/images/elements/Cash.svg") }}" />
                        <p>Cash</p>
                    </li>
                    <li data-val="1">
                        <img src="{{ url_for("static", filename="assets/images/elements/d4m_credit.svg") }}" />
                        <p>D4M Credit</p>
                    </li>
                  </ul>
              </div>
            </div>
            <button id="closeFareEstimate" class="btn btn-danger btn-booking-step-option">Cancel</button>
            <button id="confirmFareEstimate" class="btn btn-success btn-booking-step-option">Confirm</button>
        </div>
    </div>
    <div class="row booking-param-section collapse" id="confirmation">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 booking-param-container">
            <div class="estimate-header">
                <div class="confirmation-header">
                    <div id="confimationHeadLeft">
                        <img src="{{ url_for("static", filename="assets/images/elements/check_green.svg") }}" />
                    </div>
                    <div id="confimationHeadRight">
                        <h6>BOOKING IS SET!</h6>
                    </div>
                </div>
            </div>
            <div class="fare-confirmation">
              <div class="center-header-final-fare"><span id="finalAmount" class="confirm-estimate-style">2071</span>
                <img class="pre-estimate-header-element" id="viewFinalFareBreakdown" src="{{ url_for("static", filename="assets/images/elements/info_green.svg") }}">
              </div>
              <p align="center" class="confirmation-text">Fare Estimate</p>
              <p align="center" class="confirmation-text">Check your booking status from
                  <strong>Upcoming Bookings.</strong>
              </p>
              <table class="booking-details-table">
                  <tbody>
                      <tr>
                          <td class="element-table-left">Date &amp; Time:</td>
                          <td>Duration:</td>
                      </tr>
                      <tr>
                          <td class="element-table-left">
                              <span id="finalDate">05/02/2020</span> at
                              <span id="finalTime">10:25 A.M.</span>
                          </td>
                          <td><span id="finalDur">7.5</span> <span id="finalDurUnit">Hours</span></td>
                      </tr>
                  </tbody>
              </table>

              <p align="center" class="small-top-padding info-text policy-text">* Rate may change if the trip goes to overtime.</p>
              <p align="center" class="info-text policy-text">* Fare may vary depending on the duration, car type and other factors.</p>
              <p align="center" class="info-text policy-text">* Night charges may apply in addition for trips ending after 10:30 P.M.</p>
            </div>
            <button id="doneConfirmation" class="btn btn-success btn-booking-step-proceed">Done</button>
        </div>
    </div>


  <!-- MODALS -->
    <!-- Date Picker Modal -->
    <div class="modal fade" id="datePickerModal" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <!-- DATE PICKER -->
                <div id="date-picker">
                    <div>
                        <div id="date-picker-header">
                            <div id="date-picker-dow"></div>
                            <div id="date-picker-month"></div>
                            <div id="date-picker-day"></div>
                            <div id="date-picker-year"></div>
                        </div>
                        <div id="date-picker-cal-month">
                            <span id="date-picker-cal-month-prev"><i class="material-icons fa fa-angle-left"></i>&nbsp;&nbsp;&nbsp;</span>
                            <p id="date-picker-cal-month-label"></p>
                            <span id="date-picker-cal-month-next">&nbsp;&nbsp;&nbsp;<i class="material-icons fa fa-angle-right"></i></span>
                        </div>
                        <div id="date-picker-cal-wrapper">
                            <table id="date-picker-cal" class="table borderless">
                                <thead>
                                    <tr>
                                        <td>S</td><td>M</td><td>T</td><td>W</td><td>T</td><td>F</td><td>S</td>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                        <div id="date-picker-buttons" class="text-right">
                            <a id="date-picker-reset-button" title="Go to Today" class="btn btn-sm btn-flat"><i class="material-icons fa fa-calendar-check-o"></i></a>
                            <a id="date-picker-cancel-button" class="btn btn-sm btn-flat">Cancel</a>
                            <a id="date-picker-ok-button" class="btn btn-sm btn-flat">OK</a>
                        </div>
                    </div>
                </div>
                <!-- DATE PICKER -->
            </div>
        </div>
    </div>

    <!-- Time Picker Modal -->
    <div class="modal fade" id="timePickerModal" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">
                <!-- TIME PICKER -->
                <div id="time-picker">
                    <div>
                        <div id="time-picker-header" class="time-picker-bg">
                            <p>
                                <span id="time-picker-hour"></span>
                                <span id="time-picker-mins"></span>
                                <span id="time-picker-ampm"></span>
                            </p>
                        </div>
                        <div>
                            <a id="time-picker-alt-ok-button" class="btn btn-sm btn-flat"><i class="material-icons">check_circle</i></a>
                        </div>
                        <div id="time-picker-clock">
                            <span id="time-picker-hour-hand"></span>
                            <span id="time-picker-hour-center"></span>
                            <p id="time-picker-hour-1" class="time-picker-hour" data-value="1"></p>
                            <p id="time-picker-hour-2" class="time-picker-hour" data-value="2"></p>
                            <p id="time-picker-hour-3" class="time-picker-hour" data-value="3"></p>
                            <p id="time-picker-hour-4" class="time-picker-hour" data-value="4"></p>
                            <p id="time-picker-hour-5" class="time-picker-hour" data-value="5"></p>
                            <p id="time-picker-hour-6" class="time-picker-hour" data-value="6"></p>
                            <p id="time-picker-hour-7" class="time-picker-hour" data-value="7"></p>
                            <p id="time-picker-hour-8" class="time-picker-hour" data-value="8"></p>
                            <p id="time-picker-hour-9" class="time-picker-hour" data-value="9"></p>
                            <p id="time-picker-hour-10" class="time-picker-hour" data-value="10"></p>
                            <p id="time-picker-hour-11" class="time-picker-hour" data-value="11"></p>
                            <p id="time-picker-hour-12" class="time-picker-hour" data-value="12"></p>
                        </div>
                        <p id="time-picker-am-button" class="time-picker-ampm-button">AM</p>
                        <p id="time-picker-pm-button" class="time-picker-ampm-button">PM</p>
                        <div id="time-picker-buttons" class="text-right">
                            <a id="time-picker-reset-button" class="btn btn-sm btn-flat"><i class="material-icons">today</i></a>
                            <a id="time-picker-cancel-button" class="btn btn-sm btn-flat">Cancel</a>
                            <a id="time-picker-ok-button" class="btn btn-sm btn-flat">OK</a>
                        </div>
                    </div>
                </div>
                <!-- TIME PICKER -->
            </div>
        </div>
    </div>

    <!-- Fare Estimate Modal -->
    <div class="modal fade" id="estimateBreakdownModal" role="dialog">
        <div class="modal-dialog">

            <!-- Modal content-->
            <div class="modal-content">

                <div class="modal-header">
                    <div class="est-breakdown-header">
                        <h5 class="text-black">Fare Breakdown <img data-toggle="popover" title="Important Points to Note:&emsp;(Close)" id="pricingInfo" src="{{ url_for("static", filename="assets/images/elements/pricingInfo.svg") }}" data-placement="right"></h5>
                    </div>
                </div>
                <div class="modal-body">
                    <table class="breakdown-table">
                        <tbody>
                            <tr class="fare-component">
                                <td class="component-text">Base Fare:</td>
                                <td class="component-value"><span>&#8377;</span> <span id="estimateBase" class="component-figure">0</span></td>
                            </tr>
                            <tr class="fare-component collapse">
                                <td class="component-text">Car Charge:</td>
                                <td class="component-value"><span>&#8377;</span> <span id="estimateCar" class="component-figure">0</span></td>
                            </tr>
                            <tr class="fare-component estimate-book collapse">
                                <td class="component-text">Booking Charge:</td>
                                <td class="component-value"><span>&#8377;</span> <span id="estimateBooking" class="component-figure">0</span></td>
                            </tr>
                            <tr class="fare-component estimate-dist collapse">
                                <td class="component-text">Distance Charge:</td>
                                <td class="component-value"><span>&#8377;</span> <span id="estimateDist" class="component-figure">0</span></td>
                            </tr>
                            <tr class="fare-component estimate-night collapse">
                                <td class="component-text">Night Charge:</td>
                                <td class="component-value"><span>&#8377;</span> <span id="estimateNight" class="component-figure">0</span></td>
                            </tr>
                            <tr class="fare-component estimate-gst collapse">
                                <td class="component-text">GST (5%):</td>
                                <td class="component-value"><span>&#8377;</span> <span id="estimateGST" class="component-figure">0</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <div class="row no-margin no-padding">
                        <div class="col-lg-12 col-sm-12 no-margin">
                            <table class="breakdown-table">
                                <tbody>
                                    <tr class="fare-component estimate-total">
                                        <td class="component-text">Total Charge:</td>
                                        <td class="component-value"><span>&#8377;</span> <span id="estimateTotal" class="component-figure">0</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-lg-12 col-sm-12 no-margin normal-ot-policy small-top-padding collapse" style="height: 0px; overflow: hidden;">
                            <h6 class="info-text">
                                Overtime rate for the first hour is &#8377; <span class="ot-level-1">1.2</span>/min
                                 and then &#8377;<span class="ot-level-2">1.8</span>/min.
                            </h6>
                        </div>
                        <div class="col-lg-12 col-sm-12 no-margin night-policy collapse" style="height: 0px;">
                            <h6 class="info-text">
                                Night charge is applicable for trips after <span class="night-bounds-start"></span>
                                <span class="night-bounds-end"></span>
                            </h6>
                        </div>
                        <div class="col-lg-12 col-sm-12 no-margin os-ot-policy collapse" style="height: 0px;">
                            <h6 class="info-text">Overtime rate is &#8377; <span class="os-ot-value"></span>/hr</h6>
                        </div>
                        <div class="col-lg-12 col-sm-12 no-margin os-expense-policy collapse" style="height: 0px;">
                            <h6 class="info-text">Food and lodging of the driver must be provided by the customer.</h6>
                        </div>
                    </div>
                    <button type="button" class="btn btn-default modal-close" data-dismiss="modal">OK</button>
                </div>

            </div>
        </div>
    </div>

    <!-- SNACKBAR -->
    <div id="snackbar"></div>

    <!-- LOADER -->
    <div id="loader" class="collapse">
        <div class="backdrop"></div>
        <div id="spinner-container">
            <div class="spinner">
                <div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div>
            </div>
        </div>
    </div>
</body>

</html>

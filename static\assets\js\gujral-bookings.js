$(document).ready(function () {
// <editor-fold desc="Events">

    //expand function on a booking entry (UNUSED)
    $('body').delegate(".book-entry-expand",'click',function() {
        if($(this).find('i').attr('class').indexOf("expand") >= 0) {
            //expand additionals
            $(this).closest(".booking-entry").find(".additional").attr('class', 'col-lg-3 col-md-3 col-sm-6 col-xs-6 additional standard-top-padding');
            $(this).find('i').attr('class','fa fa-sm fa-compress');
            $(this).attr('title','less...');
        }
        else {
            //collapse additionals
            $(this).closest(".booking-entry").find(".additional").attr('class', 'col-lg-3 col-md-3 col-sm-6 col-xs-6 additional standard-top-padding collapse');
            $(this).find('i').attr('class','fa fa-sm fa-expand');
            $(this).attr('title','more...');
        }
    });

    //cancel booking
    $('body').delegate(".btn-cancel", "click", function() {
        data = new FormData();
        data.append('booking_id', $(this).closest(".booking-entry").find(".book-id").html());
        $.ajax({
            type: "POST",
            url: window.location.protocol + '//' + window.location.host + '/api/gujral/cancel',
            data: data,
            beforeSend: function (request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {


                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function (response) {
                if(response["success"] == 1) {
                    var bookingType = $(this).closest(".booking-entry");
                    if(bookingType.hasClass("new-booking-entry")) {
                        window.location.hash = "#newBookings";
                    }
                    if(bookingType.hasClass("upcoming-booking-entry")) {
                        window.location.hash = "#upcomingBookings";
                    }
                    if(bookingType.hasClass("ongoing-booking-entry")) {
                        window.location.hash = "#ongoingBookings";
                    }
                    if(bookingType.hasClass("completed-booking-entry")) {
                        window.location.hash = "#completedBookings";
                    }
                    if(bookingType.hasClass("cancel-d4m-booking-entry")) {
                        window.location.hash = "#d4mCancelBookings";
                    }
                    if(bookingType.hasClass("cancel-gujral-booking-entry")) {
                        window.location.hash = "#gujralCancelBookings";
                    }
                    window.location.reload();
                    openModal("Information", "Booking Cancelled!", "primary");
                }
                else {
                    openModal("Information", "Cancellation failed!", "danger");
                }
            }
        });
    });
// </editor-fold>
});

function populateBookingEntries(bookingType) {
    //get the Request URL
    var urlDetails = getRequestURL(bookingType);
    var listOfURLs = urlDetails.split(':');
    //for each URL make the AJAX call, process the data and feed to the respective panel
    // (TO DO - Remove this concept of each URL, there is only one URL)
    // TO DO - implement promises
    // TO DO - implement filters
    $.each(listOfURLs, function (index, value) {
       toFetch = value.split('|')[1];
       fromURL = "/api/gujral/" + value.split('|')[0];
       //prepare data for AJAX call
        data = new FormData();
        if(toFetch !== 0) data.append('to_fetch', parseInt(toFetch));
        if(toFetch >= 2) data.append('cancelled_by', value.split('|')[2]);
       //AJAX call
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + fromURL,
            data: data,
            beforeSend: function(request) {
               var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response["success"] == 1) {
                    if(response["data"] !== "") {
                        $.each(response["data"], function (index, value) {
                            var bookingEntry = JSON.parse(value);
                            switch(bookingType) {
                                case "new":
                                    addNewBooking(bookingEntry);
                                    break;
                                case "upcoming":
                                    addUpcomingBooking(bookingEntry);
                                    break;
                                case "ongoing":
                                    addOngoingBooking(bookingEntry);
                                    break;
                                case "completed":
                                    addCompletedBooking(bookingEntry);
                                    break;
                                // TO DO - split these two cases
                                case "cancel-d4m":
                                    addCancelledBooking(bookingEntry, "d4m");
                                    break;
                                case "cancel-gujral":
                                    addCancelledBooking(bookingEntry, "gujral");
                                case "all":
                                default:
                                // TO DO - review if needed
                            }
                        });
                        //center all address text
                        forceAllAddressToCenter();
                    }
                    else {
                        $("#" + bookingType + "BookingTab").html(
                            '<div class="row standard-top-padding">' +
                                '<div class="col-lg-offset-4 col-md-offset-4 col-sm-offset-4 col-xs-offset-4 col-lg-4 col-md-4 col-sm-4 col-xs-4"' +
                                'style="text-align: center;">' +
                                    '<h3>No Bookings Found</h3>' +
                                '</div>'+
                            '</div>'
                        );
                    }
                //reverse map to book entry processing functions
                }
                else {
                    $("#" + bookingType + "BookingTab").html(
                        '<div class="row standard-top-padding">' +
                            '<div class="col-lg-offset-4 col-md-offset-4 col-sm-offset-4 col-xs-offset-4 col-lg-4 col-md-4 col-sm-4 col-xs-4"' +
                            'style="text-align: center;">' +
                                '<h3>No Bookings Found</h3>' +
                            '</div>'+
                        '</div>'
                    );
                }
            }
        });
    });
}

/**
 * Accepts a booking type as string and determines the API call
 * and fetch parameters for it. Where no fetch (to_fetch) is required
 * zero is returned. Multiple URL fragments are ':' separated
 * @param bookingType - new/upcoming/ongoing/completed/cancelled by gujral/cancelled by d4m
 * @return {string} - url_fragment|to_fetch:url_fragment|to_fetch:url_fragment|to_fetch....
 */
function getRequestURL(bookingType) {
    switch(bookingType) {
        case "new":
            return "upcoming|2";
        case "upcoming":
            return "upcoming|1";
        case "ongoing":
            return "ongoing|0";
        case "completed":
            return "past|1";
        // Past 2 and Past 3 corresponds to D4M cancel (cancelled_by = 0), gujral cancel (cancelled_by = 1)
        case "cancel-d4m":
            return "past|2|1";
        case "cancel-gujral":
            return "past|2|0";
        // TO DO - Review if needed
        case "all":
        default:
            return "upcoming|1:upcoming|2:ongoing|0:past|1:past|2";
    }
}

// <editor-fold desc="Booking Entry Data Processing">

function addNewBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $("#newBookingTab").append
    (
        constructNewBookingEntry
        (
            bookingEntry.id, bookingEntry.shift, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, startDateTime, bookingEntry.trip_type,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.comment
        )
    );
}

function addUpcomingBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $("#upcomingBookingTab").append
    (
        constructUpcomingBookingEntry
        (
            bookingEntry.id, bookingEntry.shift, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, startDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.comment
        )
    );
}

function addOngoingBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $("#ongoingBookingTab").append
    (
        constructOngoingBookingEntry
        (
            bookingEntry.id, bookingEntry.shift, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, startDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.driver_id,
            bookingEntry.comment
        )
    );
}

function addCompletedBooking(bookingEntry) {
    //merge date and time into one date object convert to local
    var scheduledDateTime = mergeDateTime(bookingEntry.sch_startdate, bookingEntry.sch_starttime);
    var startDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    var endDateTime = mergeDateTime(bookingEntry.enddate, bookingEntry.endtime);
    $("#completedBookingTab").append
    (
        constructCompletedBookingEntry
        (
            bookingEntry.id, bookingEntry.shift, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, scheduledDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile, startDateTime, bookingEntry.dur,
            bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            endDateTime, bookingEntry.ot, bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc,
            bookingEntry.comment, bookingEntry.file_url, bookingEntry.file_count
        )
    );
}

function addCancelledBooking(bookingEntry, cancelledBy) {
    //merge date and time into one date object convert to local
    var scheduledDateTime = mergeDateTime(bookingEntry.startdate, bookingEntry.starttime);
    $('#cancel-' + cancelledBy + 'BookingTab').append
    (
        constructCancelledBookingEntry
        (
            cancelledBy, bookingEntry.id, bookingEntry.shift, bookingEntry.veh_model, bookingEntry.car_type,
            bookingEntry.veh_no, scheduledDateTime, bookingEntry.trip_type,
            bookingEntry.name, bookingEntry.mobile, bookingEntry.lat, bookingEntry.lng, bookingEntry.loc,
            bookingEntry.dest_lat, bookingEntry.dest_long, bookingEntry.dest_loc, bookingEntry.comment
        )
    );
}


// </editor-fold>

// <editor-fold desc="HTML content generation">

/**
 * Construct the HTML for a new booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param shift
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by C24
 * @return {string} - HTML content as string
 */
function constructNewBookingEntry(bookingID, shift, vehicleModel, transmissionType,
                                  vehicleRegistrationNumber, scheduledDateTime, tripType,
                                  sourceLat, sourceLong, sourceAddressString,
                                  destLat, destLong, destAddressString, comments) {
    return(
        '<div class="container row booking-entry new-booking-entry full-width">'+

            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Booking ID:</span>&emsp;'+
                        '<span class="book-id">' + bookingID + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Shift:</span>&emsp;'+
                        '<span class="booking-shift">' + getShiftText(shift) + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Source Address:</span>&emsp;'+
                        '<span class="source-address">' + sourceAddressString + '</span>'+
                    '</div>'+
                '</div>'+
            '</div>'+

            '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">Gujral Car Rentals</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>'+
                        '<div class="row booking-info-label"><span class="driver-name">---</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">---</span></div>'+
                    '</div>'+
                '</div>'+
            '</div>'+

            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">'+
                '<div class="row padded-container">'+
                '</div>' +
                '<div class="row padded-container">'+
                    '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">'+
                        '<button class="btn btn-md d4m-danger-button btn-cancel" style="width: 100%">Cancel Booking</button>'+
                    '</div>'+
                '</div>'+
            '</div>'+
        '</div>'
    );
}

/**
 * Construct the HTML for an upcoming booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param shift - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by C24
 * @return {string} - HTML content as string
 */
function constructUpcomingBookingEntry(bookingID, shift, vehicleModel, transmissionType,
                                      vehicleRegistrationNumber, scheduledDateTime, tripType,
                                      driverName, driverContact,
                                      sourceLat, sourceLong, sourceAddressString,
                                      destLat, destLong, destAddressString, comments) {
        return(
            '<div class="container row booking-entry upcoming-booking-entry full-width">'+
                '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">'+
                    '<div class="row padded-container">'+
                        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                            '<span class="param-label">Booking ID:</span>&emsp;'+
                            '<span class="book-id">' + bookingID + '</span>'+
                        '</div>'+
                        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                            '<span class="param-label">Shift:</span>&emsp;'+
                            '<span class="booking-shift">' + getShiftText(shift) + '</span>'+
                        '</div>'+
                        '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                            '<span class="param-label">Source Address:</span>&emsp;'+
                            '<span class="source-address">' + sourceAddressString + '</span>'+
                        '</div>'+
                    '</div>'+
                '</div>'+

                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">'+
                    '<div class="row padded-container">'+
                        '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">'+
                            '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>'+
                            '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>'+
                        '</div>'+
                        '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">'+
                            '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">Gujral Car Rentals</span></div>'+
                        '</div>'+
                        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                            '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>'+
                            '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>'+
                        '</div>'+
                        '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                            '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>'+
                            '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>'+
                        '</div>'+
                    '</div>'+
                '</div>'+

                '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">'+
                    '<div class="row padded-container">'+
                    '</div>' +
                    '<div class="row padded-container">'+
                        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 full-width">'+
                            '<button class="btn btn-md d4m-danger-button btn-cancel" style="width: 100%">Cancel Booking</button>'+
                        '</div>'+
                    '</div>'+
                '</div>'+
            '</div>'
        );
}

/**
 * Construct the HTML for an ongoing booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param shift
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param startDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param driverId
 * @param comments - Remarks by C24
 * @return {string} - HTML content as string
 */
function constructOngoingBookingEntry(bookingID, shift, vehicleModel, transmissionType,
                                       vehicleRegistrationNumber, startDateTime, tripType,
                                       driverName, driverContact,
                                       sourceLat, sourceLong, sourceAddressString,
                                       destLat, destLong, destAddressString, driverId, comments) {
    return(
        '<div class="container row booking-entry upcoming-booking-entry full-width">'+
            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Booking ID:</span>&emsp;'+
                        '<span class="book-id">' + bookingID + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Shift:</span>&emsp;'+
                        '<span class="booking-shift">' + getShiftText(shift) + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Source Address:</span>&emsp;'+
                        '<span class="source-address">' + sourceAddressString + '</span>'+
                    '</div>'+
                '</div>'+
            '</div>'+

            '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(startDateTime) + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>'+
                    '<div class="row booking-info-label text-no-wrap"><span class="booking-type">Gujral Car Rentals</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>'+
                        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>'+
                    '</div>'+
                '</div>'+
            '</div>'+

            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">'+

            '</div>'+
        '</div>'
    );
}

/**
 * Construct the HTML for a completed booking entry using
 * the data in the following parameters
 * @param bookingID - Booking ID unique to D4M
 * @param shift - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param startDateTime
 * @param duration
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param stopDateTime
 * @param overtime
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by C24
 * @param fileUrl - URL of zip file containing uploaded pictures
 * @param fileCount - Number of files in the fileUrl zip
 * @return {string} - HTML content as string
 */
function constructCompletedBookingEntry(bookingID, shift, vehicleModel, transmissionType, vehicleRegistrationNumber,
                                        scheduledDateTime, tripType, driverName, driverContact,
                                        startDateTime, duration, sourceLat, sourceLong, sourceAddressString,
                                        stopDateTime, overtime, destLat, destLong, destAddressString, comments,
                                        fileUrl, fileCount) {
    return(
        '<div class="container row booking-entry upcoming-booking-entry full-width">'+
            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Booking ID:</span>&emsp;'+
                        '<span class="book-id">' + bookingID + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Shift:</span>&emsp;'+
                        '<span class="booking-shift">' + getShiftText(shift) + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Source Address:</span>&emsp;'+
                        '<span class="source-address">' + sourceAddressString + '</span>'+
                    '</div>'+
                '</div>'+
            '</div>'+

            '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">Gujral Car Rentals</span></div>'+
                    '</div>'+
                    '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Start Date and Time</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="start-date-time">' + getFormattedDateTimeString(startDateTime) + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>'+
                        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Duration</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="duration">' + duration + '</span></div>'+
                    '</div>'+
                '</div>'+
                '<div class="row padded-container location-graphics">'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Scheduled Date and Time</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Stop Date and Time</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="stop-date-time">' + getFormattedDateTimeString(stopDateTime) + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Overtime</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="overtime">' + overtime + '</span></div>'+
                    '</div>'+
                '</div>'+
                '<div class="row padded-container location-graphics">'+


                '</div>'+
            '</div>'+

            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">'+

            '</div>'+
        '</div>'
    );
}

/**
 * Construct the HTML for a cancelled booking entry using
 * the data in the following parameters
 * @param cancelledBy - d4m or Gujral Car Rentals
 * @param bookingID - Booking ID unique to D4M
 * @param shift - Client provided, aka Client Booking ID
 * @param vehicleModel
 * @param transmissionType - 0:Manual 1:Automatic
 * @param vehicleRegistrationNumber
 * @param scheduledDateTime
 * @param tripType - 1:Pickup 0:Home Delivery
 * @param driverName
 * @param driverContact
 * @param sourceLat - a double
 * @param sourceLong - a double
 * @param sourceAddressString
 * @param destLat - a double
 * @param destLong - a double
 * @param destAddressString
 * @param comments - Remarks by C24
 * @return {string} - HTML content as string
 */
function constructCancelledBookingEntry(cancelledBy, bookingID, shift, vehicleModel, transmissionType,
                                        vehicleRegistrationNumber, scheduledDateTime, tripType,
                                        driverName, driverContact,
                                        sourceLat, sourceLong, sourceAddressString,
                                        destLat, destLong, destAddressString, comments) {
    return(
        '<div class="container row booking-entry cancel-' + cancelledBy + '-booking-entry full-width">'+

            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel full-width justified-text">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Booking ID:</span>&emsp;'+
                        '<span class="book-id">' + bookingID + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Shift:</span>&emsp;'+
                        '<span class="booking-shift">' + getShiftText(shift) + '</span>'+
                    '</div>'+
                    '<div class="col-lg-12 tiny-bottom-padding booking-info-label text-no-wrap">'+
                        '<span class="param-label">Source Address:</span>&emsp;'+
                        '<span class="source-address">' + sourceAddressString + '</span>'+
                    '</div>'+
                '</div>'+
            '</div>'+

            '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 justified-text full-width mid-panel">'+
                '<div class="row padded-container">'+
                    '<div class="col-lg-4 col-md-4 col-sm-4 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Date and Time</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="schedule-date-time">' + getFormattedDateTimeString(scheduledDateTime) + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-2 col-md-2 col-sm-2 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Trip Type</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="booking-type">Gujral Car Rentals</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Name</span></div>'+
                        '<div class="row booking-info-label"><span class="driver-name">' + driverName + '</span></div>'+
                    '</div>'+
                    '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 full-width">'+
                        '<div class="row booking-info-label text-no-wrap"><span class="param-label">Driver Contact</span></div>'+
                        '<div class="row booking-info-label text-no-wrap"><span class="driver-phone">' + driverContact + '</span></div>'+
                    '</div>'+
                '</div>'+

            '</div>'+

            '<div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 side-panel right-panel full-width justified-text">'+

            '</div>'+
        '</div>'
    );
}

// </editor-fold>
var searchId= Math.random();
$(document).ready(function() {
    window.curIndex = [];
    window.curIndex["Customer"] = 0
    window.curIndex["C24"] = 0
    window.curIndex["Revv"] = 0
    window.curIndex["Gujral"] = 0
    window.curIndex["OLX"] = 0
    window.curIndex["Zoomcar"] = 0
    window.curIndex["Cardekho"] = 0
    window.curIndex["Bhandari"] = 0
    window.curIndex["Mahindra"] = 0
    window.curIndex["RevvV2"] = 0
    window.curIndex["Spinny"] = 0
    window.curIndex["PrideHonda"] = 0
    window.bookList = []
    window.bookList["Customer"] = []
    window.bookList["C24"] = []
    window.bookList["Revv"] = []
    window.bookList["Gujral"] = []
    window.bookList["OLX"] = []
    window.bookList["Zoomcar"] = []
    window.bookList["Cardekho"] = []
    window.bookList["Bhandari"] = []
    window.bookList["Mahindra"] = []
    window.bookList["RevvV2"] = []
    window.bookList["Spinny"] = []
    window.bookList["PrideHonda"] = []
    window.lastAllocateRegion = -9999
    window.lock = false;
    window.firstaccess = [true, true, true, true, true, true, true];
    window.due = "";
    //displayed bookings trips count
    window.bookings = 0;
    //displayed cancelled trips count
    window.cancellations = 0;
    //displayed estimators count
    window.estimates = 0;
    //search id of estimate entry
    window.searchID = "";
    //store vehicle type
    window.vehicleType = 'Hatchback';
    //store duration
    window.duration = 0;
    //store latitude
    window.latitude = {
        "degree" : 0,
        "minute" : 0,
        "seconds" : 0.0
    };
    //store longitude
    window.longitude = {
        "degree" : 0,
        "minute" : 0,
        "seconds" : 0.0
    };
    //stores source location info
    window.source = {
        "latitude" : "",
        "longitude" : "",
        "address" : "",
    }
    //stores source location info
    window.destination = {
        "latitude" : "",
        "longitude" : "",
        "address" : "",
    }
    //get customer details
    window.customer = {
        "id": "",
        "name" : "",
        "mobile" : "",
        "booking_type" : "",
        "booking_type_id": -1,
        "comment" : ""
    };
    //get driver details
    window.driver = {
        "id" : "",
        "name" : "",
        "mobile" : ""
    };
    //get booking log
    window.booklog = {
        "type": "",
        "bookid":"",
        "driver_name": "",
        "user_name": "",
        "cancel_src": "",
        "fine_driver": "",
        "fine_user": "",
        "cancel_source": "",
        "reason_number": "",
        "reason_detail": "",
        "timestamp": "",
        "lat": "",
        "lng": "",
        "fine": "",
        "alloc_user": "",
        "cancel_reversed":""
    };
    //get driver due log
    window.driverduelog = {
        "dname": "",
        "timestamp": "",
        "paid" : "",
        "source": ""
    };
    //booking date time
    window.booking = {
        "start" : ""
    };
    //trip-start date time
    window.trip = {
        "start" : ""
    };
    //trip-stop date time
    window.tripStop = {
        "year" : 0,
        "month" : 0,
        "day" : 0,
        "hour" : 0,
        "minute" : 0
    };
    //driver licence information
    window.licence = {
        "num" : ""
    };
    //store trip id and booking id
    window.tripID = 0;
    window.bookID = 0;
    //store location name
    window.locationName = "";
    //store final fare
    window.finalFare = 0.0;
    window.cash = 0.0;
    window.credit = 0.0;
    //store estimated fare
    window.estimateFare = 0.0;

    // prediction lists
    window.pendingSuggestions = [];
    window.driverSuggestions = [];
    window.bookingLog = [];
    window.driverDueLog= [];

    window.logoSpacing = 50;    //tweak this value to alter the spacing between logo and brand-name
    //proper display of brand and logo

    initMapInputs();

    //list of level-3 admins
    var su = parseInt('{{ superuser }}');
    console.log(su);
    //conditional display of analytics
    var cookie = getCookie();
    if(su == 0) {
        //is not an L3admin
        $("#analyticsForm").html('');
        $("#navComponents").find('li').last().remove();
        $("#globalHold").remove();
    }

    var config = {
        apiKey: "AIzaSyCY7Bp_IhHJ8fySdrOswK8PE-kLjU6Z77Y",
        databaseURL: "https://drivers4me-prod-default-rtdb.asia-southeast1.firebasedatabase.app/",
    };
    firebase.initializeApp(config);

    // Get a reference to the database service
    var db = firebase.database();
    /*//remove autoslide in car type selector in fare calculator
    $('.carousel').carousel({
        interval: false
    });

    //initialize fare calculator trip start datetimepicker
        $('#datetimepicker_fare_calc').datetimepicker({
            locale: 'en',
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $('#datetimepicker_fare_calc').data("DateTimePicker").date(new Date());*/
    if(window.location.hash) {
      if (window.location.hash == "#drivers") {

      }
    }
    /*if($('#topMenu').height() > 54) {
        $("#navComponents").css('padding-top','43px');
    }*/
    $("#logo").css('left',$("#brandName").css('left').replace("px","")-window.logoSpacing+"px");
    //refresh input boxes
    $("#driver_search_mobile").val('');
    $("#driver_amount_change").val('');


    //hamburger close on nav-tab select
    $(".nav-tab").click(function() {
        $(".navbar-toggle").trigger('click');
    });

    //auto refresh bookings table on tab select

    $("#CustomerBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[0]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            //$("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Customer", 0, from,  to); //refresh booking table for current month only
            // Alternative - racy!
            //var pastDate = new Date(new Date().setDate(new Date().getDate()-30))
            //refreshBooking("Customer", 0, pastDate, 45); //refresh booking table for current month only
            window.firstaccess[0] = false;
        }
        //stats refresh
        getCurrentStats("Customer");
    });

    $("#C24BookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[1]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("C24", 0, from, to); //refresh booking table for current month only
            window.firstaccess[1] = false;
        }
        //stats refresh
        getCurrentStats("C24");
    });
    $("#OLXBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[5]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("OLX", 0, from, to); //refresh booking table for current month only
            window.firstaccess[5] = false;
        }
        //stats refresh
        getCurrentStats("OLX");
    });

    $("#ZoomcarBookingsView").click(function() {
        getBookingHoldStatusZoom();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[3]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Zoomcar", 0, from, to); //refresh booking table for current month only
            window.firstaccess[3] = false;
        }
        //stats refresh
        getCurrentStats("Zoomcar");
    });

    $("#CardekhoBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[4]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Cardekho", 0, from, to); //refresh booking table for current month only
            window.firstaccess[4] = false;
        }
        //stats refresh
        getCurrentStats("Cardekho");
    });
    $("#BhandariBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[6]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Bhandari", 0, from, to); //refresh booking table for current month only
            window.firstaccess[6] = false;
        }
        //stats refresh
        getCurrentStats("Bhandari");
    });
    $("#MahindraBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[6]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Mahindra", 0, from, to); //refresh booking table for current month only
            window.firstaccess[6] = false;
        }
        //stats refresh
        getCurrentStats("Mahindra");
    });
    $("#RevvV2BookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[6]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("RevvV2", 0, from, to); //refresh booking table for current month only
            window.firstaccess[6] = false;
        }
        //stats refresh
        getCurrentStats("RevvV2");
    });
    $("#SpinnyBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[6]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Spinny", 0, from, to); //refresh booking table for current month only
            window.firstaccess[6] = false;
        }
        //stats refresh
        getCurrentStats("Spinny");
    });
    $("#PrideHondaBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[6]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("PrideHonda", 0, from, to); //refresh booking table for current month only
            window.firstaccess[6] = false;
        }
        //stats refresh
        getCurrentStats("PrideHonda");
    });


    $("#RevvBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[2]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Revv", 0, from, to); //refresh booking table for current month only
            window.firstaccess[2] = false;
        }
        //stats refresh
        getCurrentStats("Revv");
    });

    $("#GujralBookingsView").click(function() {
        getBookingHoldStatus();
        /*
        * Refresh on tab change for first load
        */
        //change background
        if (window.firstaccess[3]) {
            var bookingCategory = resolveBookingCategory();
            $("#loaderModal").find(".modal-header").css('background','springgreen');
            //change text
            $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
            $("#loaderModal").modal('show');
            setFilters(bookingCategory, 0); //partial display
            var from = nDaysAgo(2), to = nDaysAgo(-1);
            refreshBooking("Gujral", 0, from, to); //refresh booking table for current month only
            window.firstaccess[3] = false;
        }
        //stats refresh
        getCurrentStats("Gujral");
    });
    //toggle booking hold status
    $("#globalHold, #globalHoldZoom").click(function() {

        if($(this).hasClass('hold')) {
            //release bookings
            if(!confirm(
                "Release all bookings?"
            )) return true;
            if (resolveBookingCategory() == "Zoomcar") {
                setBookingHoldValue("False", true);
                getBookingHoldStatusZoom();
            } else {
                setBookingHoldValue("False");
                getBookingHoldStatus();
            }
        }
        else {
            //hold bookings
            if(!confirm(
                "Hold all bookings?"
            )) return true;
            if (resolveBookingCategory() == "Zoomcar") {
                setBookingHoldValue("True", true);
                getBookingHoldStatusZoom();
            } else {
                setBookingHoldValue("True");
                getBookingHoldStatus();
            }
        }
    });

    $(".nameRefresh").click(function() {
        var bookingCategory = $(this).closest(".admin-function").attr("id").replace("BookingsViewForm", "");
        var bookingCategorySelector = $(this).closest(".admin-function");
        var from = new Date("June 1 2019");
        var to = new Date();
        //change background
        $("#loaderModal").find(".modal-header").css('background','springgreen');
        //change text
        $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
        $("#loaderModal").modal('show');
        var name = $("#searchName").val();
        var type = $("#searchCat").val()
        //setFilters(bookingCategory, 0); //partial display
        refreshBooking(bookingCategory, 0, from, to, name, type);
        //stats refresh
        getCurrentStats(bookingCategory);
    })

    $(".dateBoundRefresh").click(function() {
        var bookingCategory = $(this).closest(".admin-function").attr("id").replace("BookingsViewForm", "");
        var bookingCategorySelector = $(this).closest(".admin-function");
        var from = new Date(bookingCategorySelector.find('.datetimepicker_book_table_start_change').data("DateTimePicker").date().format("MM/DD/YYYY 00:00 UTC"));
        var to = new Date(bookingCategorySelector.find('.datetimepicker_book_table_end_change').data("DateTimePicker").date().format("MM/DD/YYYY 00:00 UTC"));
        //change background
        $("#loaderModal").find(".modal-header").css('background','springgreen');
        //change text
        $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
        $("#loaderModal").modal('show');
        //setFilters(bookingCategory, 0); //partial display
        refreshBooking(bookingCategory, 0, from, to);
        //stats refresh
        getCurrentStats(bookingCategory);
    });

    $(".dateRefresh").click(function() {
        var bookingCategory = $(this).closest(".admin-function").attr("id").replace("BookingsViewForm", "");
        //change background
        $("#loaderModal").find(".modal-header").css('background','springgreen');
        //change text
        $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
        $("#loaderModal").modal('show');
        //setFilters(bookingCategory, 0); //partial display
        refreshBooking(bookingCategory, 0, new Date(), new Date(), false);
        //stats refresh
        getCurrentStats(bookingCategory);
    });

    //click event for quick refresh, works same way as auto refresh on tab select
    $(".partialRefresh").click(function() {
        var bookingCategory = $(this).closest(".admin-function").attr("id").replace("BookingsViewForm", "");
        //change background
        $("#loaderModal").find(".modal-header").css('background','springgreen');
        //change text
        $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
        $("#loaderModal").modal('show');
        //setFilters(bookingCategory, 0); //partial display
        var from = nDaysAgo(2), to = nDaysAgo(-1);
        refreshBooking(bookingCategory, 0, from, to); //refresh booking table for current month only
        //stats refresh
        getCurrentStats(bookingCategory);
    });
    //refresh daily stats
    $(".refresh-stats").click(function() {
        var bookingCategory = $(this).closest(".admin-function").attr("id").replace("BookingsViewForm", "");
        getCurrentStats(bookingCategory);
    });

  $(window).resize(function() {
    $("#navComponents").css('padding-top','0');
    //hack for proper display of brand and logo
    /*if($("#topMenu").height() > 54) {
      $("#navComponents").css('padding-top','43px');
    }
    else if($("#topMenu").height() <= 50) {
      $("#navComponents").css('padding-top','0');
    }*/
    var logoLeft = $("#brandName").css('left').replace("px","")-window.logoSpacing;
    $("#logo").css('left',logoLeft+"px");
  });

    $("#driver").val('');

    //opening the correct admin function....................................................................................
    $(".navbar-nav").find(".inactive").click(function() {
        $(".navbar-nav").find(".active").attr('class','inactive');
        $(this).attr('class','active');
        //code for close all other forms
        $(".admin-function").attr('class','row container-fluid admin-function collapse');
        //open the correct form
        $("#"+$(this).attr('id')+"Form").attr('class','row container-fluid admin-function');

    });


    //google map link function for bookings/cancelled trips
    $('body').delegate(".mapLink",'click',function() {
        var locationType = $(this).attr('class').indexOf('source-map') >=0 ? "source" : "destination";
        var location = $(this).parent();
        var lat = parseFloat(location.find("." + locationType).find(".latitude").html());
        var long = parseFloat(location.find("." + locationType).find(".longitude").html());
        var link = "https://www.google.com/maps/?q=" + lat + "," + long;
        window.open(link, '_blank');
    });

    //google map link function for bookings/cancelled trips
    $('body').delegate(".curLoc",'click',function() {
        var driverId = $(this).attr('data');
        var mapLoc = window.location.protocol + '//' + window.location.host + "/track/<int:driver_id>";
        window.open(mapLoc, "blank_");
    });

    $('body').delegate(".book-track-driver",'click',function() {
        var bookId = $(this).attr('data-track');
        window.open("track/booking/" + bookId, "blank_");
    });
    $('body').delegate(".book-track-driver-available",'click',function() {
        var bookId = $(this).attr('data-track');
        window.open("admin/available/" + bookId, "blank_");
    });
    $('body').delegate(".book-history-driver",'click',function() {
        var bookId = $(this).attr('data-track');
        window.open("history/booking/" + bookId, "blank_");
    });
    //function to dynamically add a cancelled trip
    function addCancelledTrip(status) {
        if(status == "unaccepted") {
            $("#cancelled-stack").append('<div id="cancellation' + (++window.cancellations) + '" class="row cancelled-trip">'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><h3>Booking Details</h3></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Booking Status :&emsp;<span class="label status-unaccepted">unaccepted</span></h4>'+
                '</div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Vehicle Type :&emsp;<span class="label vehicleType">'+window.vehicleType+'</span></h4></div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Duration :&emsp;<span class="label duration">'+window.duration+'</span> Hours</h4></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">'+
                    '<h4 class="location-section">' +
                        '<span class="source">Source:&nbsp;' +
                            '<span class="label latitude">'+window.source["latitude"]+'&deg;N</span>' +
                            '<span class="label longitude">'+window.source["longitude"]+'&deg;E</span>' +
                        '</span>' +
                        '<button type="button" class="btn mapLink source-map">' +
                            '<span class="glyphicon glyphicon-map-marker"></span>' +
                        '</button>' +
                    '</h4></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">'+
                    '<h4>Date and Time of Booking :&emsp;'+window.booking["start"]+'</h4></div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">'+
                    '<h4>Date and Time of Trip :&emsp;'+window.trip["start"]+'</h4></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><hr class="d4m-ending"><h3>Customer Details&emsp;<span style="color: white;" class="ccancel label-danger glyphicon glyphicon-remove"> </span></h3></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<a<h4>Name :&emsp;<a class="label cust_name">'+window.customer["name"]+'</a></h4></div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">'+
                    '<h4>Phone number :&emsp;<span class="label cust_phone">'+window.customer["mobile"]+'</span></h4></div>'
            );
        }
        else if(status == "accepted") {
            $("#cancelled-stack").append('<div id="cancellation' + (++window.cancellations) + '" class="row cancelled-trip">'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><h3>Booking Details</h3></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Booking Status :&emsp;<span class="label status-accepted">accepted</span></h4>'+
                '</div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Vehicle Type :&emsp;<span class="label vehicleType">'+window.vehicleType+'</span></h4></div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Duration :&emsp;<span class="label duration">'+window.duration+'</span> Hours</h4></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">'+
                    '<h4 class="location-section">' +
                        '<span class="source">Source:&nbsp;' +
                            '<span class="label latitude">'+window.source["latitude"]+'&deg;N</span>' +
                            '<span class="label longitude">'+window.source["longitude"]+'&deg;E</span>' +
                        '</span>' +
                        '<button type="button" class="btn mapLink source-map">' +
                            '<span class="glyphicon glyphicon-map-marker"></span>' +
                        '</button>' +
                    '</h4></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">'+
                    '<h4>Date and Time of Booking :&emsp;'+window.booking["start"]+'</h4></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">'+
                    '<h4>Date and Time of Trip :&emsp;'+window.trip["start"]+'</h4></div>'+

                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><hr class="d4m-ending"><h3>Customer Details&emsp;<span style="color: white;" class="ccancel label-danger glyphicon glyphicon-remove"> </span></h3></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Name :&emsp;<a class="label cust_fname">'+window.customer["name"]+'</a></h4></div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">'+
                    '<h4>Phone number :&emsp;<span class="label cust_phone">'+window.customer["mobile"]+'</span></h4></div>'+

                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><hr class="d4m-ending"><h3>Driver Details&emsp;<span style="color: white;" class="dcancel label-danger glyphicon glyphicon-remove"> </span></h3></div>'+
                '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 standard-top-padding">'+
                    '<h4>Name :&emsp;<span class="label driver_fname">'+window.driver["name"]+'</span></h4></div>'+
                '<div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">'+
                    '<h4>Phone number :&emsp;<span class="label driver_phone">'+window.driver["mobile"]+'</span></h4></div>'
            );
        }
    }

    function addEstimateEntry() {
        $("#estimate-stack").append('<tr id=estimate' + (++window.estimates) + '>' +
            '<td>Car Type : '+window.vehicleType +
            '<hr>Duration : ' + window.duration + ' Hours</td>' +
            '<td> Name:&emsp;'+window.customer["name"]+'<hr>' +
            'Contact:&emsp;'+window.customer["mobile"]+'<hr>' +
            'City:&emsp;'+regions[parseInt(window.region)]+'</td>' +
            '<td>Start : '+window.booking['start'] +
            '<hr>Search Timestamp : ' + window.booking['timestamp'] + '</td>'+

            '<td style="max-width: 150px;"><h4 class="location-section">' +
                        '<span class="source collapse">Source:&nbsp;' +
                            '<span class="label latitude">'+window.source["latitude"]+'&deg;N</span>' +
                            '<span class="label longitude">'+window.source["longitude"]+'&deg;E</span>' +
                        '</span>' +
                        '<button type="button" class="btn mapLink source-map">' +
                            '<span class="glyphicon glyphicon-map-marker"></span>' +
                        '</button>' +
                    '</h4></div>'+
            '</tr>');
    }

    $("#driverSearch").click(function() {
        //write code here
        searchDriver();
    });
    $("#approve").click(function() {
        var did=$("#driver").text();
        if(did)
        {
            var data=new FormData();
          data.append("driver_id",did);

          $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin_appr',
                data:data,
                dataType: "json",
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {
                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = window.location.protocol + '//' + window.location.host + "/api/register_cust";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                contentType: false,
                processData: false,
                success: function() {
                    alert("Approved! Moving to next");
                    searchDriver();
                },
                error: function(er) {
                    alert("Could not approve driver, try again or click on Search");
                }

          });
        }
    });
    $("#problem").click(function() {
        var did=$("#driver").text();
        if(did)
        {
            var data=new FormData();
          data.append("driver_id",did);

          $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + "/track/<int:driver_id>",
                data:data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {
                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = window.location.protocol + '//' + window.location.host + "/api/register_cust";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function() {
                    alert("Marked as problematic license! Moving to next.");
                    searchDriver();
                },
                error: function(er) {
                    alert("Could not reject driver, try again or click on Search");
                }

          });
        }
    });

    //get cancelled list
    $("body").delegate("#refreshCancelled",'click',function() {
        //Wait message...............................................................
        //change background
        $("#loaderModal").find(".modal-header").css('background','mediumspringgreen');
        //change text
        $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
        $("#loaderModal").modal('show');
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/cancelled',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    window.cancellations = 0;
                    $("#cancelled-stack").html('<center><h2>List of Cancelled Trips</h2>'+
                '<form>'+
                    '<div class="row standard-top-padding">'+
                        '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">'+
                            '<button id="refreshCancelled" type="button" class="btn"><span class="glyphicon glyphicon-refresh"></span>&emsp;Refresh List</button>'+
                        '</div>'+
                    '</div>'+
                '</form>'+
            '</center>');
                    var cancelled_entries = response['data'].length;
                    for (i=0;i<cancelled_entries;i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['data'][i]);
                        //get vehicle type
                        window.vehicleType = getVehicleType(this_entry.car_type);
                        ///get lat long of source
                        var latVal = this_entry.lat;
                        var longVal = this_entry.lng;
                        var locName = this_entry.loc_name;
                        getLatLong(latVal, longVal, locName, 0); //source
                        //get lat long of destination (conditional)
                        if(hasDestination(this_entry.booking_type)) {
                            var latVal = this_entry.destlat;
                            var longVal = this_entry.destlng;
                            var locName = this_entry.destname;
                            getLatLong(latVal, longVal, locName, 1); //destination
                        }

                        //get duration
                        window.duration = getFormattedDuration(this_entry.dur);
                        //get customer details
                        window.customer = {
                            "id" : this_entry.cid,
                            "name" : this_entry.cname,
                            "mobile" : this_entry.cmob
                        };
                        //get cancelled booking start datetime
                        //utc to ist
                        var bookStart = getDateTime(this_entry.booktime);
                        //handle different locales in distant future
                        window.booking = {
                            "start" : bookStart.toLocaleString('en-GB',
                                                                {   day: "numeric",
                                                                    month: "long",
                                                                    year: "numeric",
                                                                    hour: 'numeric',
                                                                    minute: 'numeric',
                                                                    hour12: true })
                        }


                        //get trip start datetime
                            //utc to ist
                            var tripStart = getDateTime(this_entry.startdate, this_entry.starttime);
                            //handle different locales in distant future
                            window.trip = {
                                "start" : tripStart.toLocaleString('en-GB',
                                                                    {   day: "numeric",
                                                                        month: "long",
                                                                        year: "numeric",
                                                                        hour: 'numeric',
                                                                        minute: 'numeric',
                                                                        hour12: true })
                            };

                        if(this_entry.dname == "") {
                            //unaccepted
                            addCancelledTrip("unaccepted");

                        }
                        else {
                            //accepted
                            window.driver = {
                                "name" : this_entry.dname,
                                "mobile" : this_entry.dmob
                            };
                            addCancelledTrip("accepted");
                        }
                        /*if(window.booking["hour"] > 12) {
                            window.booking["hour"] -= 12;
                            $("#cancellation"+window.cancellations).find('.book_am_pm').html('PM');
                        }
                        if(window.trip["hour"] > 12) {
                            window.trip["hour"] -= 12;
                            $("#cancellation"+window.cancellations).find('.trip_am_pm').html('PM');
                        }*/
                        //who has cancelled
                        if(window.driver['name'] == "") {
                            //customer cancelled before a driver could accept
                            $("#cancellation"+window.cancellations).find('.ccancel').attr('class','ccancel label-danger glyphicon glyphicon-remove');
                            $("#cancellation"+window.cancellations).find('.dcancel').attr('class','dcancel label-danger glyphicon glyphicon-remove collapse');
                        }
                        else {
                            if(this_entry.cancelled == 1) {
                                //customer cancelled
                                $("#cancellation"+window.cancellations).find('.ccancel').attr('class','ccancel label-danger glyphicon glyphicon-remove');
                                $("#cancellation"+window.cancellations).find('.dcancel').attr('class','dcancel label-danger glyphicon glyphicon-remove collapse');
                            }
                            else if(this_entry.cancelled == 0) {
                                //driver cancelled
                                $("#cancellation"+window.cancellations).find('.ccancel').attr('class','ccancel label-danger glyphicon glyphicon-remove collapse');
                                $("#cancellation"+window.cancellations).find('.dcancel').attr('class','dcancel label-danger glyphicon glyphicon-remove');
                            }
                            //otherwise : 2 --> no one accepted, 3 --> driver accepted
                        }
                    }
                    $("#loaderModal").find('.btn-dismiss').trigger('click');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });

    //booking entry filters
    $(".moreBookingViewOptions").click(function() {
        var bookingCategorySelector = $(this).closest(".admin-function");
        var bookingCategory = bookingCategorySelector.attr("id").replace("BookingsViewForm", "");
        if($(this).attr('class').indexOf('active') < 0) {
            //activate options
            bookingCategorySelector.find(".more-book-table-filters").attr(
                'class', 'col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters standard-top-padding');
            $(this).attr('class', 'btn btn-primary btn-xs tiny-bottom-margin active');
            //initialize start and end datetime pickers
            var defaultDate = new Date();
            bookingCategorySelector.find('.datetimepicker_book_table_start_change').datetimepicker({
                locale: 'en',
                format: 'DD/MM/YYYY',
                widgetPositioning: {
                    horizontal: 'right',
                    vertical: 'bottom'
                }
            });
            bookingCategorySelector.find('.datetimepicker_book_table_start_change').data("DateTimePicker").date(defaultDate);
            bookingCategorySelector.find('.datetimepicker_book_table_end_change').datetimepicker({
                locale: 'en',
                format: 'DD/MM/YYYY',
                widgetPositioning: {
                    horizontal: 'right',
                    vertical: 'bottom'
                }
            });
            bookingCategorySelector.find('.datetimepicker_book_table_end_change').data("DateTimePicker").date(defaultDate);
        }

        else {
            //hide options
            bookingCategorySelector.find(".more-book-table-filters").attr(
                'class', 'col-lg-12 col-md-12 col-sm-12 col-xs-12 more-book-table-filters standard-top-padding collapse');
            $(this).attr('class', 'btn btn-primary btn-xs tiny-bottom-margin');
        }
    });

    $("#booking-comment").click(function(e) {
        if($(this).attr('class').indexOf('removed') != -1) {
            // if removed, bring back
            $("#booking-stack").find('.has-comment').each(function() {
                $(this).closest('tr').removeClass('collapse');
            });
        } else {
            $(this).addClass('removed');
            // welp, this is ugly
            var bc = false;
            var qc = false;
            var cc = false;
            var d4mc = false;
            var dc = false;
            var bn = false;
            var ba = false;
            var bo = false;
            if ($('#booking-completed').attr('class').indexOf('removed') != -1) {
                $('#booking-completed').click();
                bc = true;
            }
            if ($('#booking-quick-cancel').attr('class').indexOf('removed') != -1) {
                $('#booking-quick-cancel').click();
                qc = true;
            }
            if ($('#booking-customer-cancel').attr('class').indexOf('removed') != -1) {
                $('#booking-customer-cancel').click();
                cc = true;
            }
            if ($('#booking-driver-cancel').attr('class').indexOf('removed') != -1) {
                $('#booking-driver-cancel').click();
                dc = true;
            }
            if ($('#booking-d4m-cancel').attr('class').indexOf('removed') != -1) {
                $('#booking-d4m-cancel').click();
                d4mc = true;
            }
            if ($('#booking-new').attr('class').indexOf('removed') != -1) {
                $('#booking-new').click();
                bn = true;
            }
            if ($('#booking-accepted').attr('class').indexOf('removed') != -1) {
                $('#booking-accepted').click();
                ba = true;
            }
            if ($('#booking-ongoing').attr('class').indexOf('removed') != -1) {
                $('#booking-ongoing').click();
                bo = true;
            }
            if (bc) {
                $('#booking-completed').click();
            }
            if (qc) {
                $('#booking-quick-cancel').click();
            }
            if (cc) {
                $('#booking-customer-cancel').click();
            }
            if (dc) {
                $('#booking-driver-cancel').click();
            }
            if (d4mc) {
                $('#booking-d4m-cancel').click();
            }
            if (bn) {
                $('#booking-new').click();
            }
            if (ba) {
                $('#booking-accepted').click();
            }
            if (bo) {
                $('#booking-ongoing').click();
            }
        }
    });

    //booking type filters
    $(".book-filter").click(function() {
        var bookingCategorySelector = $(this).closest(".admin-function");
        var bookingCategory = bookingCategorySelector.attr("id").replace("BookingsViewForm", "");
        var row_selector = $(this).attr("id");
        if (row_selector == 'booking-comment') {
            return;
        }
        //var comments_on = bookingCategorySelector.find('#'+ bookingCategory +'-booking-comment').attr('class').indexOf('removed') == -1;
        if($(this).attr('class').indexOf('removed') != -1) {
            //bring back entries of this color
            $(".booking-stack").find('.' + row_selector).each(function() {
                $(this).attr('class', row_selector);
            });
            //disable this filter
            $(this).removeClass('removed');
        }

        else {
            //hide entries of this color
            $(".booking-stack").find('.' + row_selector).each(function() {
                if (true/*!comments_on*/)
                    $(this).addClass('collapse');
                else {
                    if (!$(this).find('.book-comments').attr('comment')) {
                        $(this).addClass('collapse');
                    }
                }
            });
            //enable this filter
            $(this).addClass('removed');
        }
    });
    //Trip Photo
    var fileURLs = [];
    var zip = new JSZip();
    var count = 0;
    var fileNameId="";
    $("body").delegate(".display-photo", 'click', function() {
        //get booking id
        fileURLs = [];
        var bookId = $(this).parent().find(".entry-book-id").html().replace('ID:','').trim();
        fileNameId = bookId;
        data = new FormData();
        data.append('booking_id', bookId)
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/booking_pic',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response['success'] == 1) {
                //do stuff
                    var photo_entries = response['pic_list'].length;
                    var i = 0;
                    while (i < photo_entries) {
                        var this_photo = response['pic_list'][i];
                        var totalItems = $(".item").length;
                        if(totalItems === 0) {
                            itemClass = "item active";
                            slideText = "active";
                        }
                        else {
                            itemClass = "item";
                            slideText = "";
                        }
                        console.log(totalItems);
                        var thisImage =  'https://storage.drivers4me.com/' + this_photo.pic_url;
                        $('.photo-count').append('<li data-target="#TripPhotoCarousel" data-slide-to="'+i+'" class="added '+slideText+'"></li>');
                        $('.photo-display').append('<div class="added '+itemClass+'">' +
                            '<img class="d-block" src="' + thisImage + '" alt="" style="width:100%;">' +
                            '<div class="carousel-caption">' +
                            '<h3>' + this_photo.type + '</h3>' +
                            '<p>' + this_photo.timestamp + '</p>' +
                            '</div>' + '</div>');
                        i++;
                        fileURLs = fileURLs.concat([thisImage]);
                    }
                }
                if(i==0){
                    alert("No photo found!")
                }else{
                    $("#photoModal").modal('show');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });
    $("#downloadZip").click(function() {
        var zip = new JSZip();
        var count = 0;
        var zipFilename = fileNameId+".zip";
        // loading a file and add it in a zip file
        fileURLs.forEach(async function (url){
            const urlArr = url.split('/');
            const filename = urlArr[urlArr.length - 1];
            try {
              const file = await JSZipUtils.getBinaryContent(url)
              zip.file(filename, file, { binary: true});
              count++;
              if(count === fileURLs.length) {
                zip.generateAsync({type:'blob'}).then(function(content) {
                  saveAs(content, zipFilename);
                });
              }
            } catch (err) {
              console.log(err);
            }
          });
    });
    $("#photoModal").on('hide.bs.modal', function(){
        $('.added').remove();
    });

    //driver type filters
    $(".driver-filter").click(function() {
        var row_selector = $(this).attr('id');
        if($(this).hasClass('removed')) {
            //bring back entries of this color
            $(this).closest('.admin-function').find('.' + row_selector).each(function() {
                $(this).removeClass('collapse');
            });
            //disable this filter
            $(this).removeClass('removed');
        }

        else {
            //hide entries of this color
            $(this).closest('.admin-function').find('.' + row_selector).each(function() {
                $(this).addClass('collapse');
            });
            //enable this filter
            $(this).addClass('removed');
        }
    });

    //get driver base loc address
    /*$("body").delegate(".get-address", 'click', function() {
        var parent_selector = $(this).parent();
        var latVal = parseFloat(parent_selector.find('.latDeg').html()) + parseFloat(parent_selector.find('.latMin').html()) / 60 + parseFloat(parent_selector.find('.latSec').html()) / 3600;
        var longVal = parseFloat(parent_selector.find('.longDeg').html()) + parseFloat(parent_selector.find('.longMin').html()) / 60 + parseFloat(parent_selector.find('.longSec').html()) / 3600;
        alert(latVal);
        address = getAddressString(latVal, longVal);
        parent_selector.find(".base-loc").html(address);
        parent_selector.find(".base-loc").attr('class', 'collapse');
    });*/

    //refresh booking entry
    $("body").delegate(".refresh-entry", 'click', function() {
        //get booking id
        var bookId = $(this).parent().find(".entry-book-id").html().replace('ID:','').trim();
        var parentRow = $(this).parent().parent();
        var toAdd = parentRow.attr('class').split("-")[0] + "-"
        data = new FormData();
        data.append('booking_id', bookId);

        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/book_entry_single',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    var this_entry = JSON.parse(response['data']);
                    bookingCategory = getCategoryStr(this_entry.booking_type);
                    //process entry
                    var fareString = bookingCategory == "Customer" ? '<br>Cash: ' + window.cash+'<br>Credit: ' + window.credit:"";
                    var currPaymentOption = this_entry.payment_type == 1 ? "Payment-Mode-Credit" : "Payment-Mode-Cash";
                    //load new booking info
                    getBookEntryInfo(this_entry);
                    //get parent row
                    //wipe old data
                    parentRow.html('');
                    //put new data
                    parentRow.append('<td><span class="entry-book-id">ID:&emsp;'+window.bookID + '&emsp;</span>'+
                            '<button class="btn btn-xs btn-primary refresh-entry" type="button"><span class="glyphicon glyphicon-refresh"></span></button>'+
                            '<button class="btn btn-xs btn-info change-book-comments" type="button">'+
                                '<span class="glyphicon glyphicon-pencil"></span></button>'+
                            '<button class="btn btn-xs btn-info display-photo" type="button">'+
                                '<span class="glyphicon glyphicon-camera"></span></button>'+
                            '<span class="dropdown" style="padding-top: 0px; padding-bottom: 0px">'+
                                '<button class="btn btn-primary dropdown-toggle booking-tools" type="button" data-toggle="dropdown">Action&emsp;'+
                                    '<span class="caret"></span></button>'+
                                '<ul class="dropdown-menu">'+
                                    '<button class="btn btn-xs btn-success triptype-convert" type="button" data-toggle="tooltip" title="Convert trip type">'+
                                        '<i class="fa fa-globe"></i></button>'+
                                    '<button class="btn btn-xs btn-secondary cartype-convert" type="button" data-toggle="tooltip" title="Convert Car Type">'+
                                        '<i class="fa fa-car"></i></button>'+
                                    '<button class="btn btn-xs btn-primary book-log" type="button" data-toggle="tooltip" title="Booking Entry Log">'+
                                        '<i class="fa fa-list-alt"></i></button>'+
                                '</ul>'+
                            '</span> '+
                        '<br><span class="entry-book-code">Code:&emsp;'+ this_entry.code +'</span>'+
                        '<br><span class="entry-book-code">OTP:&emsp;'+ this_entry.otp +'</span>'+
                        '<br><span class="entry-book-insurance">Insurance:&emsp;'+ (!!parseInt(this_entry.insurance) ? 'Yes' : 'No') +'</span>'+
                        '<br><span class="entry-book-start">Start:&emsp;'+window.booking['start']+'</span>'+
                        '<br>Expected Duration:&emsp;<span class="entry-duration">'+window.duration+'</span></td>'+
                        '<td>Car Type:&emsp;'+window.vehicleType+
                        '<br><span class="entry-trip-id collapse">ID:&emsp;'+window.tripID + '&emsp;</span>'+'<span class="entry-trip-start">Start:&emsp;'+window.trip['start']+'</span>'+
                        '<br>Stop:&emsp;'+window.tripStop['datetime']+'</td>'+
                        '<td><span class="glyphicon glyphicon-comment book-comments" data-toggle="tooltip"></span>'+
                        '<a class="cust-link" id='+ window.customer["id"] + ">" + window.customer["name"]+'</a>' +
                        ((getBit(this_entry.user_label,0)==1)?' <span title="VIP Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">V</span>' : '') +
                        ((getBit(this_entry.user_label,1)==1)?' <span title="Known Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">K</span>' : '') +
                        ((getBit(this_entry.user_label,2)==1)?' <span title="Issue previously" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">I</span>' : '') +
                        ((getBit(this_entry.user_label,3)==1)?' <span title="Bad Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">B</span>' : '') +
                        ((getBit(this_entry.user_label,4)==1)?' <span title="Cancels trip" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
                        '<br>Contact:&emsp;'+window.customer["mobile"] +
                        '<br><i>' +
                        ((this_entry.booking_type==520)?'<p style="color:red;">' : '<p>') +
                        window.customer['booking_type'] + '</i></td>'+
                        '<td class="entry-driver-details">' +
                        '<a class="driv-link" id='+ window.driver["id"] + ">" + window.driver["name"]+'</a>'+
                        ((getBit(this_entry.driver_label,30)==1)?' <span title="Lot of Cancelation" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
                        ((getBit(this_entry.driver_label,29)==1)?' <span title="Late on Trip" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">L</span>' : '') +
                        ((getBit(this_entry.driver_label,28)==1)?' <span title="Behavioural Issues" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">B</span>' : '') +
                        ((getBit(this_entry.driver_label,27)==1)?' <span title="Not Groomed" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">G</span>' : '') +
                        ((getBit(this_entry.driver_label,26)==1)?' <span title="Doesnt Have T-Shirt" style="font-weight: normal !important;color: #fff !important;" class="label label-success">T</span>' : '') +
                        ((getBit(this_entry.driver_label,25)==1)?' <span title="Bad Driving Skill" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">S</span>' : '') +
                        ((getBit(this_entry.driver_label,24)==1)?' <span title="Doing Direct" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">D</span>' : '') +
                        '<span class="glyphicon glyphicon-map-marker book-track-driver collapse" data-track="'+ window.bookID +'"></span>'+
                        '<span class="glyphicon glyphicon-map-marker book-track-driver-available collapse" data-track="'+ window.bookID +'"></span>'+
                        '<span class="glyphicon glyphicon-map-marker book-history-driver collapse" data-track="'+ window.bookID +'"></span><br>'+
                        'Contact:&emsp;'+window.driver["mobile"]+'<br><span class="region" region="' + this_entry.region + '">' +
                        ((parseInt(this_entry.region)==0) ? '<p style="color:DarkSlateBlue;">' : ((parseInt(this_entry.region)==6) ?'<p style="color:Crimson;">' : ((parseInt(this_entry.region)==1)?'<p style="color:#FF6F61;">':'<p style="color:Brown;">'))) +
                        'City:&emsp;'+regions[parseInt(this_entry.region)] + '</span></td>'+
                        '<td>'+window.estimateFare+'&nbsp;<div title="Change Payment Mode" class="payment-option '+ currPaymentOption +'"></div><br>' + window.due + '</td>'+
                        '<td>Total Fare: ' + window.finalFare + fareString + "</td>" +
                        '<td class="location-section" style="max-width: 200px;">' +
                                '<span class="source collapse">Source:&nbsp;' +
                                    '<span class="label latitude">'+window.source["latitude"]+'</span>' +
                                    '<span class="label longitude">'+window.source["longitude"]+'</span>' +
                                '</span>' +
                            '<button type="button" class="btn mapLink source-map source-map-booking">' +
                                '<span class="glyphicon glyphicon-map-marker"></span>' +
                            '</button><span class="addr">'+window.source['address'] + "</span><br>" +
                        '</td>'
                    );
                    if (hasDestination(window.customer['booking_type_id'])) {
                        parentRow.find(".location-section").append(
                            '<span class="destination collapse">Destination:&nbsp;' +
                                        '<span class="label latitude">'+window.destination["latitude"]+'</span>' +
                                        '<span class="label longitude">'+window.destination["longitude"]+'</span>' +
                                    '</span>' +
                                '<button type="button" class="btn btn-info mapLink destination-map destination-map-booking">' +
                                    '<span class="glyphicon glyphicon-map-marker"></span>' +
                                '</button><span class="addr">' + window.destination['address'] + "</span><br>"
                        );
                    }

                    // set comment
                    var bookingEntry = parentRow.closest('tr');
                    var comment = window.customer["comment"] == undefined || window.customer['comment'] == "" ? "" : window.customer['comment'];
                    $(bookingEntry).find(".book-comments").attr('title', "Comment:" + comment);
                    if (comment)
                        $(bookingEntry).find(".book-comments").addClass('has-comment');
                        (bookingEntry).find(".book-comments").attr('comment', "1");
                    $(bookingEntry).find(".book-comments").tooltip();

                    //color the row according to status
                    //also, initially hide all except {unallocated, upcoming, driver-cancelled and on-going}
                    if(this_entry.cancelled == 1) {
                        //cancelled before anyone could accept
                        if(window.driver['name'] == '-') {
                                $(parentRow).attr('class', toAdd+'booking-quick-cancel');
                            //allocation needed
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                        }
                        //customer cancelled
                        else {
                            $(parentRow).attr('class', toAdd+'booking-customer-cancel');
                            //allocation needed
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                        }
                    }
                    else if(this_entry.cancelled == -1) {
                        //cancelled by d4m
                        $(parentRow).attr('class', toAdd+'booking-d4m-cancel');
                        //allocation needed
                        $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                    }
                    else if(this_entry.cancelled == 0) {
                        //driver cancelled
                        $(parentRow).attr('class', toAdd+'booking-driver-cancel');
                        //allocation needed
                        $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                        //change estimate
                            /*$(parentRow).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/

                        //unallocate
                        /*if(!(this_entry.booking_type < 50))
                            $(parentRow).find('.dropdown-menu').prepend(
                                '<button type="button" class="btn btn-xs unallocate" title="Unallocate">'+
                                    '<i class="fa fa-trash">' +
                                '</button>');*/
                    }
                    else if(this_entry.cancelled == 2) {
                        //no one accepted
                        $(parentRow).attr('class', toAdd+'booking-new');
                        //pending list needed
                        $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary pending-list">Show pending</button></li>');
                        //allocation needed
                        $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                        //d4m cancel
                        $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                        //d4m unallocate
                        $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');
                        //broadcast option
                        $(parentRow).find(".book-track-driver-available").removeClass("collapse")
                        if(getBookHoldingStatusText(this_entry.suppressed) == "SUPPRESSED") {
                            var broadcastPlacer = $(parentRow).find('.change-book-comments');
                            $("<button class=\"btn btn-xs btn-warning broadcast\" type=\"button\" data-toggle=\"tooltip\" title=\"Broadcast Booking request\">" +
                                "<i class=\"fa fa-bullhorn\"></i></button>").insertAfter(broadcastPlacer);
                        }
                        //customer cancel
                        /*if(!(this_entry.booking_type < 50 || this_entry.booking_type == 520 || this_entry.booking_type == 521))
                            $(parentRow).find('.dropdown-menu').prepend(
                                '<button type="button" class="btn btn-xs btn-danger customer-cancel" data-toggle="tooltip" title="Customer Cancelled">'+
                                '<i class="fa fa-user-times"></i></button>');*/
                    }
                    else if(this_entry.cancelled == 3) {
                        //someone accepted
                        //trip not started
                        if(window.tripID == '-') {
                            $(parentRow).attr('class', toAdd+'booking-accepted');
                            //if re-allocation is needed
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                            //add trip start option
                            //if(!(this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-start">Start Trip</button></li>');
                            //d4m cancel
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                            //d4m unallocate
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');
                            //change estimate
                            /*$(parentRow).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                            //customer cancel
                            /*if(!(this_entry.booking_type < 50 || this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                $(parentRow).find('.dropdown-menu').prepend(
                                    '<button type="button" class="btn btn-xs btn-danger customer-cancel" data-toggle="tooltip" title="Customer Cancelled">'+
                                    '<i class="fa fa-user-times"></i></button>');*/

                            //driver cancel
                            /*$(parentRow).find('.dropdown-menu').prepend(
                                '<button type="button" class="btn btn-xs btn-warning driver-cancel" title="Driver Cancel">'+
                                    '<i class="fa fa-ban text-danger">' +
                                '</button>');*/

                            //unallocate
                            /*if(!(this_entry.booking_type < 50))
                                $(parentRow).find('.dropdown-menu').prepend(
                                    '<button type="button" class="btn btn-xs unallocate" title="Unallocate">'+
                                        '<i class="fa fa-trash">' +
                                    '</button>');*/
                        }
                        //ongoing trip
                        else if (!this_entry.trip_start) {
                            $(parentRow).attr('class', bookingCategoryDash+'booking-driver-cancel');
                            //d4m cancel
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                            //d4m unallocate
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');

                            /*if (c24Booking) {
                                $(parentRow).removeClass('booking-ongoing').addClass('c24-booking-ongoing');
                            }*/
                            //change button for book start
                            // $(parentRow).find('.dropdown-menu').append(
                            //     '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary book-start-change">Change Book Start</button></li>');
                            //add trip start option
                            //if(!(this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-start">Start Trip</button></li>');
                            //driver cancel
                            /*$(parentRow).find('.dropdown-menu').prepend(
                                '<button type="button" class="btn btn-xs btn-warning driver-cancel" title="Driver Cancel">'+
                                    '<i class="fa fa-ban text-danger">' +
                                '</button>');*/
                            //change estimate
                            /*$("#booking"+myID).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                            //$("#loaderModal").find('.btn-dismiss').trigger('click');

                            //track
                            $(parentRow).find(".book-track-driver").removeClass("collapse");
                        }
                        else if(this_entry.trip_stop == "None") {
                            $(parentRow).attr('class', toAdd+'booking-ongoing');
                            //d4m cancel
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                            //d4m unallocate
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');

                            //change button for trip start
                            $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-start-change">Change Trip Start</button></li>');
                            //trip stop
                            //if(!(this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                $(parentRow).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-stop">Stop Trip</button></li>');
                            //change estimate
                            /*$(parentRow).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                            // tracking
                            $(parentRow).find(".book-track-driver").removeClass("collapse");
                        }
                        //otherwise, successful trip
                        else {
                            $(parentRow).attr('class', toAdd+'booking-completed');
                            $(parentRow).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-stop-change">Change Stop Time</button></li>');
                            $(parentRow).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-restart">Restart Trip</button></li>');
                            //change fare
                            $(parentRow).find('.dropdown-menu').append(
                                '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-fare">Change Final Fare</button></li>');
                                $(parentRow).find(".book-history-driver").removeClass("collapse");
                        }
                    }
                    if (this_entry.c_color)
                        $(parentRow).children().eq(2).addClass('new-customer')
                    if (this_entry.d_color)
                        $(parentRow).children().eq(3).addClass('new-driver')
                    if (this_entry.f_color)
                        $(parentRow).find(".change-book-comments").first().addClass('has-feedback')
                    if (this_entry.m_color)
                        $(parentRow).children().eq(2).addClass('marked-customer')
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });

    $("#feedback_modal").on('hidden.bs.modal', function() {
        var booking_id = parseInt($("#feedback_modal").find(".booking-id").first().html())
        var feedback = $("input[name='feedback']:checked").val()
        if (feedback === undefined) {
            return
        }
        data = new FormData();
        data.append('booking_id', booking_id)
        data.append('feedback', feedback)
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/feedback/set',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,

            });
        $(".feedInp").prop('checked', false);
    });
    //edit comment button click
    $("body").delegate(".change-book-comments", 'click', function() {
        var commentModal = $("#feedback_modal");
        var booking_id = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        commentModal.find(".booking-id").html(booking_id);
        commentModal.css({"top": "20%", "left": "20%"});
        var booking_id = parseInt(booking_id);
        data = new FormData();
        data.append('booking_id', booking_id)
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/feedback/get',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response['success'] == 1) {
                        if (response['feedback'] == 0) {
                            $("#feedNeut").click();
                        } else if (response['feedback'] == -1) {
                            $("#feedAng").click();
                        } else {
                            $("#feedHap").click();
                        }
                    }
                    commentModal.modal('show');
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    });

    //customer cancel button click
    $("body").delegate(".customer-cancel", 'click', function() {
        var parent = $(this).closest('tr');
        bookId = parseInt(parent.find(".entry-book-id").html().replace('ID:','').trim());
        if(!confirm(
                "Cancel #" + bookId + " on behalf of customer?")
            ) return true;
        data = new FormData();
        data.append('booking_id',bookId);
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/customer_decline',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','gold');
                        //change text
                        $("#infoModal").find(".modal-title").html("Customer Cancel successful!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                        parent.find('.refresh-entry').trigger('click');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });

        var booking_entry = $(".entry-book-id").filter(function( index ) {
                    return $(this).text().indexOf(bookId) >= 0;
                  }).closest('td').find('.refresh-entry').click();
    });

    //save edited booking comment
    $("body").delegate("#confirm-comment-change", 'click', function() {
        var commentModal = $("#commentEdit_modal");
        var booking_id = commentModal.find(".booking-id").html();
        var oldComment = window.comment;
        var newComment = commentModal.find("#comment").val();
        if(oldComment != newComment) {
            data = new FormData();
            data.append('booking_id', commentModal.find(".booking-id").html());
            data.append('comment', newComment);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/change_book_comment',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {
                        commentModal.find('.modal-footer').find('.btn-default').trigger('click');
                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Comment updated successfully");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
            var booking_entry = $(".entry-book-id").filter(function( index ) {
                    return $(this).text().indexOf(booking_id) >= 0;
                  }).closest('td').find('.refresh-entry').click();

        }
        else {
            alert("No changes made!");
        }
    });
    $('#c24-select').change(function() {
        if ($('#c24-select').is(":checked")) {
            $(".booking-completed").addClass("collapse");
            $(".booking-quick-cancel").addClass("collapse");
            $(".booking-customer-cancel").addClass("collapse");
            $(".booking-driver-cancel").addClass("collapse");
            $(".booking-d4m-cancel").addClass("collapse");
            $(".booking-new").addClass("collapse");
            $(".booking-accepted").addClass("collapse");
            $(".booking-ongoing").addClass("collapse");
            $(".booking-comment").addClass("collapse");
            if (!$("#booking-completed").hasClass("removed")) {
                $(".c24-booking-completed").removeClass("collapse");
            }
            if (!$("#booking-quick-cancel").hasClass("removed")) {
                $(".c24-booking-quick-cancel").removeClass("collapse");
            }
            if (!$("#booking-completed").hasClass("removed")) {
                $(".c24-booking-completed").removeClass("collapse");
            }
            if (!$("booking-customer-cancel").hasClass("removed")) {
                $(".c24-booking-customer-cancel").removeClass("collapse");
            }
            if (!$("#booking-driver-cancel").hasClass("removed")) {
                $(".c24-booking-driver-cancel").removeClass("collapse");
            }
            if (!$("#booking-d4m-cancel").hasClass("removed")) {
                $(".c24-booking-d4m-cancel").removeClass("collapse");
            }
            if (!$("#booking-new").hasClass("removed")) {
                $(".c24-booking-new").removeClass("collapse");
            }
            if (!$("#booking-accepted").hasClass("removed")) {
                $(".c24-booking-accepted").removeClass("collapse");
            }
            if (!$("#booking-ongoing").hasClass("removed")) {
                $(".c24-booking-ongoing").removeClass("collapse");
            }
            if (!$("#booking-comment").hasClass("removed")) {
                $(".c24-booking-comment").removeClass("collapse");
            }
        } else  {
            $(".c24-booking-completed").addClass("collapse");
            $(".c24-booking-quick-cancel").addClass("collapse");
            $(".c24-booking-customer-cancel").addClass("collapse");
            $(".c24-booking-driver-cancel").addClass("collapse");
            $(".c24-booking-d4m-cancel").addClass("collapse");
            $(".c24-booking-new").addClass("collapse");
            $(".c24-booking-accepted").addClass("collapse");
            $(".c24-booking-ongoing").addClass("collapse");
            $(".c24-booking-comment").addClass("collapse");
            if (!$("#booking-completed").hasClass("removed")) {
                $(".booking-completed").removeClass("collapse");
            }
            if (!$("#booking-quick-cancel").hasClass("removed")) {
                $(".booking-quick-cancel").removeClass("collapse");
            }
            if (!$("#booking-completed").hasClass("removed")) {
                $(".booking-completed").removeClass("collapse");
            }
            if (!$("booking-customer-cancel").hasClass("removed")) {
                $(".booking-customer-cancel").removeClass("collapse");
            }
            if (!$("#booking-driver-cancel").hasClass("removed")) {
                $(".booking-driver-cancel").removeClass("collapse");
            }
            if (!$("#booking-d4m-cancel").hasClass("removed")) {
                $(".c2booking-d4m-cancel").removeClass("collapse");
            }
            if (!$("#booking-new").hasClass("removed")) {
                $(".booking-new").removeClass("collapse");
            }
            if (!$("#booking-accepted").hasClass("removed")) {
                $(".booking-accepted").removeClass("collapse");
            }
            if (!$("#booking-ongoing").hasClass("removed")) {
                $(".booking-ongoing").removeClass("collapse");
            }
            if (!$("#booking-comment").hasClass("removed")) {
                $(".booking-comment").removeClass("collapse");
            }
        }
    });
    $(".list-filter-control").on("input", function() {
        var modalSelector = $(this).closest(".modal");
        var modalType = modalSelector.attr("id").replace("ListModal", "");
        var inputString =  $(this).val();
        modalSelector.find(".height-constrained-table").find("tr").filter(function( index ) {
            $(this).toggle($(this).text().toLowerCase().indexOf(inputString.toLowerCase()) > -1)
        });
    });

    //populate estimates table
    $("#refreshEstimates").click(function() {
        //Wait message...............................................................
        //change background
        $("#loaderModal").find(".modal-header").css('background','mediumspringgreen');
        //change text
        $("#loaderModal").find(".modal-title").html("Please wait while we populate the table...");
        $("#loaderModal").modal('show');
        var region = parseInt(getCookie()['region'])
        data = new FormData();
        data.append('region', region);
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/estimate_no_book',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                $("#loaderModal").find('.btn-dismiss').trigger('click');
                if(response['success'] == 1) {

                    var estimate_entries = response['data'].length;
                    window.estimates = 0;
                    //clear table
                    $("#estimate-stack").html('');

                    for (i=0;i<estimate_entries;i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['data'][i]);
                        //get search id
                        window.searchID = this_entry.search_id;
                        //get estimate booking start datetime
                        //utc to ist
                        var bookStart = getDateTime(this_entry.startdate, this_entry.starttime);
                        //handle different locales in distant future
                        //get search timestamp
                        //utc to ist
                        var estTimeStamp = getDateTime(this_entry.timestamp);
                        //handle different locales in distant future
                        window.booking = {
                            "start" : bookStart.toLocaleString('en-GB',
                                                                {   day: "numeric",
                                                                    month: "long",
                                                                    year: "numeric",
                                                                    hour: 'numeric',
                                                                    minute: 'numeric',
                                                                    hour12: true }),
                            "timestamp" : estTimeStamp.toLocaleString('en-GB',
                                                                {   day: "numeric",
                                                                    month: "long",
                                                                    year: "numeric",
                                                                    hour: 'numeric',
                                                                    minute: 'numeric',
                                                                    hour12: true })
                        };
                        //get trip duration
                        window.duration = getFormattedDuration(this_entry.dur);
                        //get customer details
                        window.customer = {
                            "id" : this_entry.cid,
                            "name" : this_entry.cname,
                            "mobile" : this_entry.cmob
                        };
                        //get car type
                        window.vehicleType = getVehicleType(this_entry.car_type);
                        //get fare estimate
                        //window.estimateFare = '₹' + this_entry.estimate;
                        //get lat long
                        var latVal = this_entry.lat;
                        var longVal = this_entry.lng;
                        window.region = this_entry.region;
                        getLatLong(latVal, longVal, "", 0);
                        //get location name
                        //window.locationName = this_entry.loc_name;

                        addEstimateEntry();
                    }
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });
    //logout
    $("#logout").click(function() {
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/token/remove',
            data: "",
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function() {
                window.location = "/adminLogin";
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });

    //detect driver account status save requirement
    $("body").delegate(".approval-selection", 'change', function() {
        $(this).closest('td').find('.driver-action').attr('class', 'driver-action driver-action-needed glyphicon glyphicon-floppy-disk');
    });
    $("body").delegate(".availability", 'click', function() {
        $(this).closest('td').find('.driver-action').attr('class', 'driver-action driver-action-needed glyphicon glyphicon-floppy-disk');
    });
    $("body").delegate(".base-loc", 'keyup', function() {
        $(this).closest('tr').find('.driver-action').attr('class', 'driver-action driver-action-needed glyphicon glyphicon-floppy-disk');
    });
    $("body").delegate(".driver-cur-alloc", 'change', function() {
        $(this).closest('tr').find('.driver-action').attr('class', 'driver-action driver-action-needed glyphicon glyphicon-floppy-disk');
    });
    //save driver status
    $("body").delegate(".driver-action", 'click', function() {
        if(!confirm(
                "Save changes?")
            ) return true;
        var parentSelector = $(this).closest('td');
        var rowSelector = $(this).closest('tr');
        //get driver id
        var driverId = parseInt(parentSelector.find('.driver-id').html());
        //get availability status
        var availability = parentSelector.find(".availability").prop('checked') == true ? 1 : 0;
        //get approval status
        var approval = parseInt(parentSelector.find(".approval-selection").find('option:selected').val());
        //also write locname
        var locname = rowSelector.find(".base-loc").val();
        var curalloc = rowSelector.find(".driver-cur-alloc").val();
        if (typeof curalloc == 'undefined') {
            curalloc = -1;
        }
        //save to db
        data = new FormData();
        data.append('driver_id', driverId);
        data.append('availability', availability);
        data.append('approval', approval);
        data.append('locname', locname);
        data.append('curalloc', curalloc);
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_acc_alter',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Account status successfully updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1400);
                        //update due amount
                        parentSelector.find('.driver-action').attr('class', 'driver-action glyphicon glyphicon-floppy-disk');
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });

    });
    //driver dues functions

    //reset modify amount input
    $("#driverDues").click(function() {
        $("#amount_change").val('');
    });
    //add to due amount
    $("#incrAmount").click(function() {
        currDue = parseInt($("#curr_due").html());
        if($("#amount_change").val() == '') amount_change = 0;
        else amount_change = parseInt($("#amount_change").val());
        currDue = currDue + amount_change;
        $("#curr_due").html(currDue);
        $("#amount_change").val('');
    });
    //subtract from due amount
    $("#decrAmount").click(function() {
        currDue = parseInt($("#curr_due").html());
        if($("#amount_change").val() == '')
            amount_change = 0;
        else
            amount_change = parseInt($("#amount_change").val());
        currDue = currDue - amount_change;
        $("#curr_due").html(currDue);
        $("#amount_change").val('');
    });
    //clear dues
    $("#clearDues").click(function() {
        $("#curr_due").html('0');
    });

    //get driver list for dues
    $("#getDriversList_dues").click(function() {
        //modify driver list modal
        $("#driversListModal").find('.modal-header').html(
            '<h4 class="modal-title">Drivers List - for dues ' +
                    '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>' +
                    '<h6><i>Click to load dues</i></h6>');
        $("#driversListModal").find('tbody').attr('id', 'dues-driver-list');
        loadAllocateDriversList("dues-driver-list"); //load without base loc
        $("#driversListModal").modal('show');
    });

    //load due info based on list
    $("body").delegate("#dues-driver-list > tr", 'click', function() {
        //a hack that triggers a search by mobile event
        $("#driver_search_mobile").val($(this).find('td:eq(2)').html());
        //close the list
        $("#driversListModal").modal('hide');
        //trigger the due display event
        $("#searchDriver_phone").trigger('click');
    });
    //search driver based on phone number
    $("#searchDriver_phone").click(function(e) {
        e.preventDefault();
        //$("#amount_change").val('');
        //$("#remarks").val('');
        var mobile = $("#driver_search_mobile").val().trim();
        var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");           //regex for phone validation

        var validPhone = true;

        //phone validation
        if (mobile == "") {
            $("#driver_search_mobile").css('border','2px solid red');          //error indication
            //error popover
            $("#driver_search_mobile").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#driver_search_mobile').data('bs.popover').options.content = "This field cannot be blank";
            validPhone = false;
        }

        else if (!phoneRegex.test(mobile)) {
            $("#driver_search_mobile").css('border','2px solid red');          //error indication
            //error popover
            $("#driver_search_mobile").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#driver_search_mobile').data('bs.popover').options.content = "Enter correct mobile number without country-code";
            validPhone = false;
        }

        else {
            $("#driver_search_mobile").css('border','');
            $("#driver_search_mobile").popover('destroy');
            validPhone = true;
            var data = new FormData();
            data.append('phone', $("#driver_search_mobile").val())
            window.driver["mobile"] = $("#driver_search_mobile").val();
            //now call the API to get the driver with corresponding mobile number
            $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/search_by_mobile',
            dataType:"json",
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {
                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = window.location.protocol + '//' + window.location.host + "/api/register_cust";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            processData:false,
            contentType:false,
            success: function(response) {
                if(response.success == 1) {

                    //get driver name and contact
                    if(response.driver_name != '') {
                        window.driver = {
                            "id" : response.driver_id,
                            "name" : response.driver_name,
                            'mobile' : $("#driver_search_mobile").val(),
                            "due" : response.due,
                            "wallet": response.wallet,
                            "withdrawable": response.withdrawable
                        };
                        //get driver licence
                        window.licence = {
                            "num" : response.driver_license
                        };

                        displayDueData();
                    }
                    //driver not found
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Driver not found");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                        window.driver = {
                            "id" : '',
                            "name" : '',
                            'mobile' : '',
                            "due" : 0
                        };
                        //get driver licence
                        window.licence = {
                            "num" : ''
                        };

                        displayDueData();
                    }
                }
            },
        });
        }
    });
    //customer label customer search
    var label_val;
    $("#Search_Label_Cust").click(function() {
    var mobile = $("#LabelCust_SearchInput_Phone").val().trim();
    var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");           //regex for phone validation
    var UIDRegex = new RegExp("^[1-9]{1,1}[0-9]*$")             //regex for user ID validation
    var validData = true;

        //phone validation
        if (!mobile || mobile === "") {
            $("#LabelCust_SearchInput_Phone").css('border','2px solid red');            //error indication
            //error popover
            $("#LabelCust_SearchInput_Phone").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#LabelCust_SearchInput_Phone').data('bs.popover').options.content = "This field cannot be blank";
            validData = false;
        }

        else if (!phoneRegex.test(mobile)) {
            $("#LabelCust_SearchInput_Phone").css('border','2px solid red');            //error indication
            //error popover
            $("#LabelCust_SearchInput_Phone").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#LabelCust_SearchInput_Phone').data('bs.popover').options.content = "Enter correct mobile number without country-code";
            validData = false;
        }

        else {
            $("#LabelCust_SearchInput_Phone").popover('destroy');
            validData = true;
        }

        if(validData) {
            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/user_label/get',
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                data: {
                    mobile: mobile
                },

                dataType: "json",
                success: function(user) {
                    if (user.success == 1 && user.is_user==0)
                    {
                        $("#custId_Label").html(parseInt(user.id));
                        $("#custPhone_Label").html(parseInt(user.mobile));
                        $("#custName_Label").html(user.name);
                        label_val = parseInt(user.labels)
                        if(getBit(user.labels,0)==1){
                            $('#custCheckbox1').prop('checked', true);
                        }else{
                            $('#custCheckbox1').prop('checked', false);
                        }
                        if(getBit(user.labels,1)==1){
                            $('#custCheckbox2').prop('checked', true);
                        }else{
                            $('#custCheckbox2').prop('checked', false);
                        }
                        if(getBit(user.labels,2)==1){
                            $('#custCheckbox3').prop('checked', true);
                        }else{
                            $('#custCheckbox3').prop('checked', false);
                        }
                        if(getBit(user.labels,3)==1){
                            $('#custCheckbox4').prop('checked', true);
                        }else{
                            $('#custCheckbox4').prop('checked', false);
                        }
                        if(getBit(user.labels,4)==1){
                            $('#custCheckbox5').prop('checked', true);
                        }else{
                            $('#custCheckbox5').prop('checked', false);
                        }
                        if(getBit(user.labels,5)==1){
                            $('#custCheckbox6').prop('checked', true);
                        }else{
                            $('#custCheckbox6').prop('checked', false);
                        }
                    }
                    else if(user.success == 1 && user.is_user==1){
                        alert("This user is registered as a driver");
                    }
                    else {
                        alert("This is not a valid number");
                    }

                },
                error: function(e) {
                    $("#LabelCust_SearchInput_Phone").css('border','2px solid red');            //error indication
                    //error popover
                    $("#LabelCust_SearchInput_Phone").popover({
                        placement: "bottom",
                        trigger: "hover"
                    });
                    $('#LabelCust_SearchInput_Phone').data('bs.popover').options.content = "Authentication Failed! Invalid Credentials";
                }
            });
        }
    });
    $("#saveCustLabel").click(function() {
        //not a valid customer
        if($("#custPhone_Label").html()=='') {
            //change background
            $("#infoModal").find(".modal-header").css('background','lightsalmon');
            //change text
            $("#infoModal").find(".modal-title").html("Customer not found");
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
        }
        else {
            //send current payment information
            data = new FormData();
            data.append('mobile', $("#custPhone_Label").html());
            var label = label_val;
            //var disc = $('#paid_check').is(":checked") ? 0: 1;
            if($('#custCheckbox1').is(':checked')){
                label = setBit(label,0,1)
            }else{
                label = setBit(label,0,0)
            }
            if($('#custCheckbox2').is(':checked')){
                label = setBit(label,1,1)
            }else{
                label = setBit(label,1,0)
            }
            if($('#custCheckbox3').is(':checked')){
                label = setBit(label,2,1)
            }else{
                label = setBit(label,2,0)
            }
            if($('#custCheckbox4').is(':checked')){
                label = setBit(label,3,1)
            }else{
                label = setBit(label,3,0)
            }
            if($('#custCheckbox5').is(':checked')){
                label = setBit(label,4,1)
            }else{
                label = setBit(label,4,0)
            }
            if($('#custCheckbox6').is(':checked')){
                label = setBit(label,5,1)
            }else{
                label = setBit(label,5,0)
            }
            data.append('label', label);
            //data.append('disc', disc);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/user_label/set',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Labels successfully updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }
    });

    //driver label driver search
    $("#Search_Label_Driver").click(function() {
    var mobile = $("#LabelDriver_SearchInput_Phone").val().trim();
    var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");           //regex for phone validation
    var UIDRegex = new RegExp("^[1-9]{1,1}[0-9]*$")             //regex for user ID validation
    var validData = true;

        //phone validation
        if (!mobile || mobile === "") {
            $("#LabelDriver_SearchInput_Phone").css('border','2px solid red');          //error indication
            //error popover
            $("#LabelDriver_SearchInput_Phone").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#LabelDriver_SearchInput_Phone').data('bs.popover').options.content = "This field cannot be blank";
            validData = false;
        }

        else if (!phoneRegex.test(mobile)) {
            $("#LabelDriver_SearchInput_Phone").css('border','2px solid red');          //error indication
            //error popover
            $("#LabelDriver_SearchInput_Phone").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#LabelDriver_SearchInput_Phone').data('bs.popover').options.content = "Enter correct mobile number without country-code";
            validData = false;
        }

        else {
            $("#LabelDriver_SearchInput_Phone").popover('destroy');
            validData = true;
        }

        if(validData) {
            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/user_label/get',
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                data: {
                    mobile: mobile
                },

                dataType: "json",
                success: function(user) {
                    if (user.success == 1 && user.is_user==1)
                    {
                        $("#driverId_Label").html(parseInt(user.id));
                        $("#driverPhone_Label").html(parseInt(user.mobile));
                        $("#driverName_Label").html(user.name);
                        if(getBit(user.labels,30)==1){
                            $('#driverCheckbox1').prop('checked', true);
                        }else{
                            $('#driverCheckbox1').prop('checked', false);
                        }
                        if(getBit(user.labels,29)==1){
                            $('#driverCheckbox2').prop('checked', true);
                        }else{
                            $('#driverCheckbox2').prop('checked', false);
                        }
                        if(getBit(user.labels,28)==1){
                            $('#driverCheckbox3').prop('checked', true);
                        }else{
                            $('#driverCheckbox3').prop('checked', false);
                        }
                        if(getBit(user.labels,27)==1){
                            $('#driverCheckbox4').prop('checked', true);
                        }else{
                            $('#driverCheckbox4').prop('checked', false);
                        }
                        if(getBit(user.labels,26)==1){
                            $('#driverCheckbox5').prop('checked', true);
                        }else{
                            $('#driverCheckbox5').prop('checked', false);
                        }
                        if(getBit(user.labels,25)==1){
                            $('#driverCheckbox6').prop('checked', true);
                        }else{
                            $('#driverCheckbox6').prop('checked', false);
                        }
                        if(getBit(user.labels,24)==1){
                            $('#driverCheckbox7').prop('checked', true);
                        }else{
                            $('#driverCheckbox7').prop('checked', false);
                        }
                    }
                    else if(user.success == 1 && user.is_user==0){
                        alert("The user is not a driver");
                    }
                    else {
                        alert("This is not a valid number");
                    }

                },
                error: function(e) {
                    $("#LabelDriver_SearchInput_Phone").css('border','2px solid red');          //error indication
                    //error popover
                    $("#LabelDriver_SearchInput_Phone").popover({
                        placement: "bottom",
                        trigger: "hover"
                    });
                    $('#LabelDriver_SearchInput_Phone').data('bs.popover').options.content = "Authentication Failed! Invalid Credentials";
                }
            });
        }
    });

    $("#saveDriverLabel").click(function() {
        //not a valid Driver
        if($("#driverPhone_Label").html()=='') {
            //change background
            $("#infoModal").find(".modal-header").css('background','lightsalmon');
            //change text
            $("#infoModal").find(".modal-title").html("Driver not found");
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
        }
        else {
            //send current payment information
            data = new FormData();
            data.append('mobile', $("#driverPhone_Label").html());
            var label = label_val;
            //var disc = $('#paid_check').is(":checked") ? 0: 1;
            if($('#driverCheckbox1').is(':checked')){
                label = setBit(label,30,1)
            }else{
                label = setBit(label,30,0)
            }
            if($('#driverCheckbox2').is(':checked')){
                label = setBit(label,29,1)
            }else{
                label = setBit(label,29,0)
            }
            if($('#driverCheckbox3').is(':checked')){
                label = setBit(label,28,1)
            }else{
                label = setBit(label,28,0)
            }
            if($('#driverCheckbox4').is(':checked')){
                label = setBit(label,27,1)
            }else{
                label = setBit(label,27,0)
            }
            if($('#driverCheckbox5').is(':checked')){
                label = setBit(label,26,1)
            }else{
                label = setBit(label,26,0)
            }
            if($('#driverCheckbox6').is(':checked')){
                label = setBit(label,25,1)
            }else{
                label = setBit(label,25,0)
            }
            if($('#driverCheckbox7').is(':checked')){
                label = setBit(label,24,1)
            }else{
                label = setBit(label,24,0)
            }
            data.append('label', label);
            //data.append('disc', disc);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/user_label/set',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Labels successfully updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }
    });

    function getBit(n, k)//find if kth bit is set from right for integer n
    {
        return (n >> k) % 2;
    }

    function setBit(n, k, i)//set kth bit of integer n
    {
        if(i==1){
            return ((1 << k) | n);
        }else{
            return (n & ~(1 << k));
        }
    }
    //input error reset
    $("#driver_search_mobile").click(function() {
      $(this).css('border','');
    });

    $("#confirmAmount").click(function() {
        if($("#driverId_due").html()=='') {
            //change background
            $("#infoModal").find(".modal-header").css('background','lightsalmon');
            //change text
            $("#infoModal").find(".modal-title").html("Driver not found");
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
            $("#confirmAmount").prop("disabled",false);
        }
        else if($("#remarks").val().trim() == "" && !(document.getElementById('cash').checked && !document.getElementById('gift').checked && !document.getElementById('adminfine').checked && !document.getElementById('creditwithdraw').checked))
        {
            $("#remarks").css('border','2px solid red');          //error indication
            //error popover
            $("#remarks").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#remarks').data('bs.popover').options.content = "This field cannot be blank";
        }else if(parseFloat($("#amount_change").val()) <= 0)
        {
            $("#amount_change").css('border','2px solid red');          //error indication
            //error popover
            $("#amount_change").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#amount_change').data('bs.popover').options.content = "Amount can not be 0 or less!";
        }
        else {
            $("#remarks").css('border','');
            $("#amount_change").css('border','');
            $("#remarks").popover('destroy');
            $("#amount_change").popover('destroy');
            //send current payment information
            data = new FormData();
            data.append('driver_id', $("#driverId_due").html());
            remarks = $("#remarks").val();
            var cause = "";
            var amount_paid = 0;
            var withdraw = 0;
            if (parseFloat($("#amount_change").val()) == 0){
                alert("Amount can not be 0!");
                return;
            }
            if(document.getElementById('duededuction').checked) {
                amount_paid = parseFloat($("#amount_change").val());
                withdraw = 0;
                cause = "Due Deduction";
                if(document.getElementById('online').checked){
                    remarks = "Online - "+ remarks
                }else{
                    remarks = "Cash"
                }

            }else if(document.getElementById('gift').checked) {
                amount_paid = parseFloat($("#amount_change").val());
                withdraw = 0;
                cause = "Gift";
            }else if(document.getElementById('adminfine').checked) {
                amount_paid = -parseFloat($("#amount_change").val());
                withdraw = 0;
                cause = "Admin Fine";
            }else if(document.getElementById('creditwithdraw').checked) {
                amount_paid = -parseFloat($("#amount_change").val());
                withdraw = 1;
                cause = "Credit Withdraw";
            }else if(document.getElementById('tshirt').checked) {
                amount_paid = parseFloat($("#amount_change").val());
                withdraw = 2;
                cause = "T-Shirt";
                if(document.getElementById('online').checked){
                    remarks = "Online - " + remarks + " Size - " + document.getElementById("size").value + " Quantity - " + document.getElementById("quantity").value
                }else{
                    remarks = "Cash" + " Size - " + document.getElementById("size").value + " Quantity - " + document.getElementById("quantity").value
                }
            }else if(document.getElementById('registration').checked) {
                amount_paid = parseFloat($("#amount_change").val());;
                withdraw = 2;
                cause = "Registration";
                if(document.getElementById('online').checked){
                    remarks = "Online - " + remarks + " Size - " + document.getElementById("size").value + " Quantity - " + document.getElementById("quantity").value
                }else{
                    remarks = "Cash" + " Size - " + document.getElementById("size").value + " Quantity - " + document.getElementById("quantity").value
                }
            }else{
                amount_paid = 0
                alert("Please Select the type of Payment!");
                return;
            }
            data.append('amount', amount_paid);
            data.append('cause', cause);
            data.append('withdraw', withdraw);
            data.append('remarks', remarks);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_paid',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Dues successfully updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                        //update due amount
                        $("#amount_due").html($("#curr_due").html());
                        $("#confirmAmount").prop("disabled",false);
                        $("#amount_change").val('');
                        $("#remarks").val('');
                        $("#searchDriver_phone").click()
                    }else if(response['success'] == -1){
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html(response['msg']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    $("#confirmAmount").prop("disabled",false);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }
    });
    if(document.getElementById('typePayment') != null){
        document.getElementById('typePayment').addEventListener('change', function(e) {
            if(e.target.value.localeCompare("Gift") == 0 || e.target.value.localeCompare("Admin Fine") == 0 || e.target.value.localeCompare("Credit Withdraw") == 0){
                $("#remarks").show();
            }else{
                if(document.getElementById('cash').checked){
                    $("#remarks").hide();
                }else{
                    $("#remarks").show();
                }
            }
            if(e.target.value.localeCompare("Credit Withdraw") == 0 || e.target.value.localeCompare("Gift") == 0 || e.target.value.localeCompare("Admin Fine") == 0){
                $("#typePaymentType").hide();
            }else{
                $("#typePaymentType").show();
            }
            if(e.target.value.localeCompare("T-Shirt") == 0 || e.target.value.localeCompare("Registration") == 0){
                $("#size").show();
                $("#sizeLabel").show();
                $("#quantity").show();
                $("#quantityLabel").show();
            }else{
                $("#size").hide();
                $("#sizeLabel").hide();
                $("#quantity").hide();
                $("#quantityLabel").hide();
            }
        }, false);
    }
    if(document.getElementById('typePaymentType') != null){
        document.getElementById('typePaymentType').addEventListener('change', function(e) {
            if(document.getElementById('creditwithdraw').checked || document.getElementById('adminfine').checked0 || document.getElementById('gift').checked){
                $("#remarks").show();
            }else if(e.target.value.localeCompare("Cash") == 0){
                $("#remarks").hide();
            }else{
                $("#remarks").show();
            }
        }, false);
    }
    //save current due
    $("#saveAmount").click(function() {
        $("#saveAmount").prop("disabled",true);
        //not a valid driver
        if($("#driverId_due").html()=='') {
            //change background
            $("#infoModal").find(".modal-header").css('background','lightsalmon');
            //change text
            $("#infoModal").find(".modal-title").html("Driver not found");
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
            $("#saveAmount").prop("disabled",false);
        }
        else {
            //send current payment information
            data = new FormData();
            data.append('driver_id', $("#driverId_due").html());
            var amount_change = parseFloat($("#amount_due").html()) - parseFloat($("#curr_due").html());
            var disc = $('#paid_check').is(":checked") ? 0: 1;
            data.append('amount', amount_change);
            data.append('disc', disc);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_paid',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Dues successfully updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                        //update due amount
                        $("#amount_due").html($("#curr_due").html());
                        $("#saveAmount").prop("disabled",false);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    $("#saveAmount").prop("disabled",false);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }
    });

    //display drivers list on unallocated trips on click
    $("body").delegate(".allocate",'click',function() {
        //modify driver list modal
        $("#driversListModal").find('.modal-header').html(
            '<h4 class="modal-title">Drivers List - Allocate for <span class="alloc_book_id"></span></h4>'+
                    '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>'+
                    '<h6><i>Click to allocate</i></h6>');
        $("#driversListModal").find('tbody').attr('id', 'alloc-driver-list');
        $("#driversListModal").find('.alloc_book_id').html($(this).parent().parent().parent().parent().find(".entry-book-id").html());
        var regionCode = parseInt($(this).closest('tr').find('.region').attr("region"))
        if (window.lastAllocateRegion != regionCode) {
            loadAllocateDriversList("alloc-driver-list", regionCode); //load drivers for new regions
        }
        $("#driversListModal").modal('show');
    });
    $("body").delegate(".book-log",'click',function() {
        window.bookingLog = [];
        //get booking id
        var bookID = parseInt($(this).parent().parent().parent().parent().find(".entry-book-id").html().replace('ID:','').trim());
        showLog("book-log-list", bookID);
    });

    $("#logDues").click(function() {
        window.driverDueLog = [];
        //get mobile
        var mobile = $("#driver_search_mobile").val().trim();
        showDriverDueLog("driver-due-list", mobile);
    });


    //book start change : show timepicker first
    $("#datetimepicker_book_start_change").on('dp.show', function() {
      $(document).find('.picker-switch a[data-action="togglePicker"]').trigger('click');
    });

    //trip start change : show timepicker first
    $("#datetimepicker_trip_start_change").on('dp.show', function() {
      $(document).find('.picker-switch a[data-action="togglePicker"]').trigger('click');
    });

    //book start change
    $("body").delegate(".book-start-change",'click',function() {
        $("#datetimepicker_modal").attr('class', 'modal fade book-start-datetime')
        $("#datetimepicker_modal").find(".modal-title").html('Choose Book Start date and time for Booking ID: <span class="time_change_book_id">' +
            $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim() + '</span>');
        $("#datetimepicker_modal").modal('show');
        //Initialize datetime pickers
        var start_Date = new Date($(this).parent().closest('tr').find(".entry-book-start").html().replace('Start:','').trim());
        //min_Date = new Date(start_Date.getTime() - 20 * 60 * 1000); //upto 20 minutes from book time
        //var max_Date = new Date(start_Date.getTime() + 2 * 60 * 60 * 1000); //upto 2 hrs from book time
        //initialize datetimepicker
        $('#datetimepicker_book_start_change').datetimepicker({
            locale: 'en',
            format: "DD/MM/YYYY hh:mm A",
            //minDate: min_Date,
            //maxDate: max_Date,
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $('#datetimepicker_book_start_change').data("DateTimePicker").date(start_Date);
    });

    //trip start change
    $("body").delegate(".trip-start-change",'click',function() {
        $("#datetimepicker_modal").attr('class', 'modal fade trip-start-change-datetime')
        $("#datetimepicker_modal").find(".modal-title").html('Choose Trip Start date and time for Booking ID: <span class="time_change_book_id">' +
            $(this).parent().parent().parent().parent().find(".entry-book-id").html().replace('ID:','').trim() + '</span>');
        $("#datetimepicker_modal").modal('show');
        //Initialize datetime pickers
        var start_Date = new Date($(this).closest('tr').find(".entry-book-start").html().replace('Start:','').trim());
        //min_Date = new Date(start_Date.getTime() - 20 * 60 * 1000); //upto 20 minutes from book time
        //var max_Date = new Date(start_Date.getTime() + 2 * 60 * 60 * 1000); //upto 2 hrs from book time
        //initialize datetimepicker
        $('#datetimepicker_trip_start_change').datetimepicker({
            locale: 'en',
            format: "DD/MM/YYYY hh:mm A",
            //minDate: min_Date,
            //maxDate: max_Date,
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $('#datetimepicker_trip_start_change').data("DateTimePicker").date(start_Date);
    });

    //start trip
    $("body").delegate(".trip-start",'click',function() {
        var parent = $(this).closest('tr');
        bookId = parseInt(parent.find(".entry-book-id").html().replace('ID:','').trim());

        $("#datetimepicker_modal").attr('class', 'modal fade trip-start-datetime')
        $("#datetimepicker_modal").find(".modal-title").html('Choose Trip Start date and time for Booking ID: <span class="time_change_book_id">' +
            bookId + '</span>');
        $("#datetimepicker_modal").modal('show');
        //Initialize datetime pickers
        var start_date = new Date(parent.find(".entry-book-start").text().replace('Start:','').trim());
        //min_Date = new Date(start_Date.getTime() - 20 * 60 * 1000); //upto 20 minutes from book time
        //var max_Date = new Date(start_Date.getTime() + 2 * 60 * 60 * 1000); //upto 2 hrs from book time
        //initialize datetimepicker
        $('#datetimepicker_trip_start_change').datetimepicker({
            locale: 'en',
            format: "DD/MM/YYYY hh:mm A",
            //minDate: min_Date,
            //maxDate: max_Date,
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $("#datetimepicker_modal").find("#dt_book_tr").val(parent.attr('id'));
        $('#datetimepicker_trip_start_change').data("DateTimePicker").date(start_date);
    });

    //stop trip
    $("body").delegate(".trip-stop",'click',function() {
        var parent = $(this).closest('tr');
        bookId = parseInt(parent.find(".entry-book-id").html().replace('ID:','').trim());

        $("#datetimepicker_modal").attr('class', 'modal fade trip-stop-datetime');
        $("#datetimepicker_modal").find(".modal-title").html('Choose Trip Stop date and time for Booking ID: <span class="time_change_book_id">' +
            bookId + '</span>');
        $("#datetimepicker_modal").modal('show');
        var min_date = new Date(parent.find(".entry-trip-start").text().replace('Start:','').trim()); //can stop at or after actual trip start
        var durationInfo = $(this).closest("tr").find(".entry-duration").html().split(' ');
        var trip_duration = parseInt(durationInfo[0]);
        var stop_date = durationInfo[1] == "Hours" ?
            new Date(min_date.getTime() + trip_duration * 60 * 60 * 1000) :
            new Date(min_date.getTime() + trip_duration * 24 * 60 * 60 * 1000); // set default time to expected stop time (actual start + duration)
        //var max_Date = new Date(start_Date.getTime() + 2 * 60 * 60 * 1000); //upto 2 hrs from book time
        //initialize datetimepicker
        $('#datetimepicker_trip_start_change').datetimepicker({
            locale: 'en',
            //minDate: min_date,
            format: "DD/MM/YYYY hh:mm A",
            //maxDate: max_Date,
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $("#datetimepicker_modal").find("#dt_book_tr").val(parent.attr('id'));
        $('#datetimepicker_trip_start_change').data("DateTimePicker").date(stop_date);

    });

    //change stop trip
    $("body").delegate(".trip-stop-change",'click',function() {
        var parent = $(this).closest('tr');
        bookId = parseInt(parent.find(".entry-book-id").html().replace('ID:','').trim());
        var fnId = $(this).closest(".admin-function").attr("id")
        if (fnId == "CustomerBookingsViewForm") {
            alert("Not implemented!");
        }
        $("#datetimepicker_modal").attr('class', 'modal fade trip-stop-change-datetime');
        $("#datetimepicker_modal").find(".modal-title").html('Choose Trip Stop date and time for Booking ID: <span class="time_change_book_id">' +
            bookId + '</span>');
        $("#datetimepicker_modal").modal('show');
        var durationInfo = $(this).closest("tr").find(".entry-duration").html().split(' ');
        var trip_duration = parseInt(durationInfo[0]);
        var min_date = new Date(parent.find(".entry-trip-start").text().replace('Start:','').trim()); //can stop at or after actual trip start
        var stop_date = durationInfo[1] == "Hours" ?
            new Date(min_date.getTime() + trip_duration * 60 * 60 * 1000) :
            new Date(min_date.getTime() + trip_duration * 24 * 60 * 60 * 1000); // set default time to expected stop time (actual start + duration)
        //var max_Date = new Date(start_Date.getTime() + 2 * 60 * 60 * 1000); //upto 2 hrs from book time
        //initialize datetimepicker
        $('#datetimepicker_trip_start_change').datetimepicker({
            locale: 'en',
            format: "DD/MM/YYYY hh:mm A",
            //maxDate: max_Date,
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $("#datetimepicker_modal").find("#dt_book_tr").val(parent.attr('id'));
        $('#datetimepicker_trip_start_change').data("DateTimePicker").date(stop_date);

    });

    function parseDate(dateString){
    var time = Date.parse(dateString);
    if(!time){
        time = Date.parse(dateString.replace("T"," "));
        if(!time){
            bound = dateString.indexOf('T');
            var dateData = dateString.slice(0, bound).split('-');
            var timeData = dateString.slice(bound+1, -1).split(':');

            time = Date.UTC(dateData[0],dateData[1]-1,dateData[2],timeData[0],timeData[1],timeData[2]);
        }
}
    return time;
    }

    //set Idle date of In-house driver
    $("body").delegate(".set-idle",'click',function() {
        var parent = $(this).closest('tr');
        driverID = parseInt(parent.find(".driver-id").html().trim());
        driverName = parent.find(".driver-name").html().trim();
        $("#datetimepicker_modal").attr('class', 'modal fade perma-driver-set-idle');
        $("#datetimepicker_modal").find(".modal-title").html('Set date on which #<span class="set_idle-driver_id">'+ driverID + '</span>&nbsp;' + driverName + ': was Idle');
        $("#datetimepicker_modal").modal('show');
        //initialize datetimepicker
        $('#datetimepicker_trip_start_change').datetimepicker({
            format: "DD/MM/YYYY",
            locale: 'en',
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $("#datetimepicker_modal").find("#dt_book_tr").val(parent.attr('id').replace("permaDriver", ""));
        $('#datetimepicker_trip_start_change').data("DateTimePicker").date(new Date());

    });

    //confirm trip time change
    $("#confirm-date-time").click(function() {
        if ($("#datetimepicker_modal").attr('class').indexOf('trip-stop-datetime') != -1) {
            d = new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())
                .toLocaleString('en-GB',
                                    {   day: "numeric",
                                        month: "numeric",
                                        year: "numeric",
                                        hour: 'numeric',
                                        minute: 'numeric',
                                        second: 'numeric',
                                        timeZone: 'UTC'

                                    }
                                ).replace(new RegExp(/\//g), "-").replace(',', '');

            var parent = $("#" + $("#datetimepicker_modal").find("#dt_book_tr").val());
            bookId = parseInt(parent.find(".entry-book-id").html().replace('ID:','').trim());
            if(new Date(parent.find(".entry-trip-start").text().replace('Start:','').trim())>new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())){
                alert("Stop Time Cannot be before start time.");
                return;
            }
            data = new FormData();
            data.append('book_id',bookId);
            data.append('stop_time', d);
            $.ajax({
                    type:"POST",
                    url: window.location.protocol + '//' + window.location.host + '/api/admin/stop_trip',
                    data: data,
                    beforeSend: function(request) {
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                        if (refresh_token) {

                            if (checkRefresh(csrf_token, refresh_token) == false) {
                                alert("Unfortunately, your session has expired. Please login again");
                                window.location  = "/adminLogin";
                            }
                        }
                        request.setRequestHeader('X-CSRF-Token', csrf_token);
                     },
                    dataType: "json",
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if(response['success'] == 1) {

                            //change background
                            $("#infoModal").find(".modal-header").css('background','aquamarine');
                            //change text
                            $("#infoModal").find(".modal-title").html("Trip stopped!");
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                            parent.find('.refresh-entry').trigger('click');
                        }
                        else {
                            //change background
                            $("#infoModal").find(".modal-header").css('background','lightsalmon');
                            //change text
                            $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                        }
                    },
                    error: function() {
                        //Connection error...............................................................
                        //change background
                        $("#infoModal").find(".modal-header").css('background','gold');
                        //change text
                        $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                        //window.location = "/adminLogin";
                    }

                });
            return;
        }
        else if ($("#datetimepicker_modal").attr('class').indexOf('trip-stop-change-datetime') != -1) {
            d = new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())
                .toLocaleString('en-GB',
                                    {   day: "numeric",
                                        month: "numeric",
                                        year: "numeric",
                                        hour: 'numeric',
                                        minute: 'numeric',
                                        second: 'numeric',
                                        timeZone: 'UTC'

                                    }
                                ).replace(new RegExp(/\//g), "-").replace(',', '');

            var parent = $("#" + $("#datetimepicker_modal").find("#dt_book_tr").val());
            bookId = parseInt(parent.find(".entry-book-id").html().replace('ID:','').trim());
            if(new Date(parent.find(".entry-trip-start").text().replace('Start:','').trim())>new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())){
                alert("Stop Time Cannot be before start time.");
                return;
            }
            data = new FormData();
            data.append('book_id',bookId);
            data.append('stop_time', d);
            $.ajax({
                    type:"POST",
                    url: window.location.protocol + '//' + window.location.host + '/api/admin/newstop_trip',
                    data: data,
                    beforeSend: function(request) {
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                        if (refresh_token) {

                            if (checkRefresh(csrf_token, refresh_token) == false) {
                                alert("Unfortunately, your session has expired. Please login again");
                                window.location  = "/adminLogin";
                            }
                        }
                        request.setRequestHeader('X-CSRF-Token', csrf_token);
                     },
                    dataType: "json",
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if(response['success'] == 1) {

                            //change background
                            $("#infoModal").find(".modal-header").css('background','aquamarine');
                            //change text
                            $("#infoModal").find(".modal-title").html("Trip stopped!");
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                            parent.find('.refresh-entry').trigger('click');
                        }
                        else {
                            //change background
                            $("#infoModal").find(".modal-header").css('background','lightsalmon');
                            //change text
                            $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                        }
                    },
                    error: function() {
                        //Connection error...............................................................
                        //change background
                        $("#infoModal").find(".modal-header").css('background','gold');
                        //change text
                        $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                        //window.location = "/adminLogin";
                    }

                });

            return;
        }
        else if ($("#datetimepicker_modal").attr('class').indexOf('trip-start-datetime') != -1) {
            //call trip-start time change
            //send current payment information
            d = new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())
                .toLocaleString('en-GB',
                                    {   day: "numeric",
                                        month: "numeric",
                                        year: "numeric",
                                        hour: 'numeric',
                                        minute: 'numeric',
                                        second: 'numeric',
                                        timeZone: 'UTC'

                                    }
                                ).replace(new RegExp(/\//g), "-").replace(',', '');
            if(new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())>new Date(new Date().getTime() + 10*60000)){
                alert("Start Time Cannot be in future.");
                return;
            }
            data = new FormData();
            data.append('booking_id', $("#datetimepicker_modal").find(".time_change_book_id").html());
            data.append('start_time', d);
            $.ajax({
                    type:"POST",
                    url: window.location.protocol + '//' + window.location.host + '/api/admin/start_trip',
                    data: data,
                    beforeSend: function(request) {
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                        if (refresh_token) {

                            if (checkRefresh(csrf_token, refresh_token) == false) {
                                alert("Unfortunately, your session has expired. Please login again");
                                window.location  = "/adminLogin";
                            }
                        }
                        request.setRequestHeader('X-CSRF-Token', csrf_token);
                     },
                    dataType: "json",
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if(response['success'] == 1) {

                            //change background
                            $("#infoModal").find(".modal-header").css('background','aquamarine');
                            //change text
                            $("#infoModal").find(".modal-title").html("Trip started!");
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                            parent.find('.refresh-entry').trigger('click');
                        }
                        else {
                            //change background
                            $("#infoModal").find(".modal-header").css('background','lightsalmon');
                            //change text
                            $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                        }
                    },
                    error: function() {
                        //Connection error...............................................................
                        //change background
                        $("#infoModal").find(".modal-header").css('background','gold');
                        //change text
                        $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                        //window.location = "/adminLogin";
                    }

                });

            return;
        }
        else if($("#datetimepicker_modal").attr('class').indexOf('trip-start-change-datetime') != -1) {
            //call trip-start time change
            //send current payment information
            d = new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())
                .toLocaleString('en-GB',
                                    {   day: "numeric",
                                        month: "numeric",
                                        year: "numeric",
                                        hour: 'numeric',
                                        minute: 'numeric',
                                        second: 'numeric',
                                        timeZone: 'UTC'

                                    }
                                ).replace(new RegExp(/\//g), "-").replace(',', '');
            if(new Date($('#datetimepicker_trip_start_change').data("DateTimePicker").date())>new Date(new Date().getTime() + 10*60000)){
                alert("Start Time Cannot be in future.");
                return;
            }
            data = new FormData();
            data.append('booking_id', $("#datetimepicker_modal").find(".time_change_book_id").html());
            data.append('starttime', d);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/change_trip_start',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Trip start time updated successfully");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }

    });

    //display drivers list on unallocated trips on click
    $("body").delegate(".pending-list",'click',function() {
        window.pendingSuggestions = [];
        //get booking id
        var bookID = parseInt($(this).parent().parent().parent().parent().find(".entry-book-id").html().replace('ID:','').trim());
        loadPendingList("pending-drivers-list", bookID);
    });

    //allocate on click
    $("body").delegate("#alloc-driver-list > tr", 'click', function() {
        if(!confirm(
                parseInt($("#driversListModal").find('.alloc_book_id').html().replace('ID:','').trim()) +
                " gets driver id:" + parseInt($(this).find('td:eq(0)').html())
            )) return true;
        data = new FormData();
        data.append('booking_id',parseInt($("#driversListModal").find('.alloc_book_id').html().replace('ID:','').trim()));
        data.append('driver_id', parseInt($(this).find('td:eq(0)').html()));
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/allocate_driver',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Allocation successful!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    });

    // broadcast
    $("body").delegate(".broadcast", 'click', function() {
        if(!confirm(
                "Broadcast Booking #" +
                $(this).parent().find(".entry-book-id").html().replace('ID:','').trim() +
                " ?"
            )) return true;
        data = new FormData();
        data.append('booking_id',parseInt($(this).parent().find(".entry-book-id").html().replace('ID:','').trim()));
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/broadcast_trip',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','azure');
                        //change text
                        $("#infoModal").find(".modal-title").html("Booking broadcasted!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    })

    //d4m cancel
    $("body").delegate(".trip-restart", 'click', function() {
        var parent = $(this).parent().parent().parent().parent();
        if(!confirm(
                "Restart Booking #" +
                parent.find(".entry-book-id").html().replace('ID:','').trim() +
                " ?"
            )) return true;
        data = new FormData();
        data.append('book_id',$(this).parent().parent().parent().parent().find(".entry-book-id").html().replace('ID:','').trim());
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/restart_trip',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Trip restarted!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);

                        parent.find('.refresh-entry').trigger('click');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    });


    //d4m cancel
    $("body").delegate(".d4m-cancel", 'click', function() {
        var bookID = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        var cancel_modal = $("#cancel_modal");
        cancel_modal.find('.cancel-book-id').html(bookID);
        cancel_modal.modal('show');
    });
    function cancelCharge() {
        if (reason === "-1") {
            alert("You have not selected any cancellation reason. Please select a reason to cancel the trip.");
            return;
        }
        var cancel_modal = $("#cancel_modal");
        cancel_modal.find('.cancel-book-id').html(bookID);
        var bookID = cancel_modal.find('.cancel-book-id').html();
        var reason = document.getElementById('cancellationReason').value;
        var reason_details;
        if (reason === "0") {
            reason_details = "Change of Plans";
        } else if (reason === "1") {
            reason_details = "Driver Denied";
        } else if (reason === "2") {
            reason_details = "Favorite Driver Not Assigned";
        } else if (reason === "3") {
            reason_details = "Driver Requesting Extra Fare";
        } else if (reason === "4") {
            reason_details = "Driver Asking to Take Offline";
        } else if (reason === "5") {
            reason_details = "Selected Wrong Location";
        } else if (reason === "6") {
            reason_details = "Selected Different Service";
        } else if (reason === "7") {
            reason_details = "Booked by Mistake";
        } else if (reason === "8") {
            reason_details = "Wait Time Too Long";
        } else if (reason === "9") {
            reason_details = "Got Driver Elsewhere";
        } else if (reason === "10") {
            reason_details = "Checking Price Estimate";
        } else if (reason === "11") {
            reason_details = "Taking Too Long to Allocate";
        } else if (reason === "12") {
            reason_details = "Direct Trip";
        } else if (reason === "13") {
            reason_details = "Wrongly Taken";
        } else if (reason === "14") {
            reason_details = "Previous Trip Not Ended";
        } else if (reason === "15") {
            reason_details = "Personal Issue";
        } else if (reason === "16") {
            reason_details = "Transportation Problem";
        } else if (reason === "17") {
            reason_details = "Customer Not Responding";
        } else if (reason === "18") {
            reason_details = "Customer Asked to Cancel";
        } else if (reason === "62") {
            reason_details = "Other";
        } else {
            reason_details = "No Allocation";
        }
        data = new FormData();
        data.append("booking_id", bookID);
        data.append("reason", reason);
        data.append("reason_details",reason_details);
        $.ajax({
            url: window.location.protocol + '//' + window.location.host + '/api/admin/cancel/charge',
            type: 'POST',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(data) {
                console.log("CancelChargeLog", data);
                let status = data.success || -1;
                if (status === 1) {
                    if (data.charge[1] === "-1") {
                        alert("Please select a valid reason.");
                        return;
                    }
                    let confirmation = confirm("Customer cancellation charges will be ₹ " + data.charge[0] + ". \nDriver cancellation charges will be ₹ " + data.charge[1] + ". \nAre you sure you want to cancel?");
                    if (confirmation) {
                        cancelBooking();
                    }
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }
        });
    }
    function cancelBooking(){
        var cancel_modal = $("#cancel_modal");
        var bookID = cancel_modal.find('.cancel-book-id').html();
        var reason = document.getElementById('cancellationReason').value;
        var reason_details;
        if (reason === "0") {
            reason_details = "Change of Plans";
        } else if (reason === "1") {
            reason_details = "Driver Denied";
        } else if (reason === "2") {
            reason_details = "Favorite Driver Not Assigned";
        } else if (reason === "3") {
            reason_details = "Driver Requesting Extra Fare";
        } else if (reason === "4") {
            reason_details = "Driver Asking to Take Offline";
        } else if (reason === "5") {
            reason_details = "Selected Wrong Location";
        } else if (reason === "6") {
            reason_details = "Selected Different Service";
        } else if (reason === "7") {
            reason_details = "Booked by Mistake";
        } else if (reason === "8") {
            reason_details = "Wait Time Too Long";
        } else if (reason === "9") {
            reason_details = "Got Driver Elsewhere";
        } else if (reason === "10") {
            reason_details = "Checking Price Estimate";
        } else if (reason === "11") {
            reason_details = "Taking Too Long to Allocate";
        } else if (reason === "12") {
            reason_details = "Direct Trip";
        } else if (reason === "13") {
            reason_details = "Wrongly Taken";
        } else if (reason === "14") {
            reason_details = "Previous Trip Not Ended";
        } else if (reason === "15") {
            reason_details = "Personal Issue";
        } else if (reason === "16") {
            reason_details = "Transportation Problem";
        } else if (reason === "17") {
            reason_details = "Customer Not Responding";
        } else if (reason === "18") {
            reason_details = "Customer Asked to Cancel";
        } else if (reason === "62") {
            reason_details = "Other";
        } else {
            reason_details = "No Allocation";
        }
        data = new FormData();
        data.append("booking_id", bookID);
        data.append("reason", reason);
        data.append("reason_details",reason_details);
        //uncomment when api ready
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/cancel',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                console.log(response)
                if(response['success'] == 1) {

                    //change background
                    $("#infoModal").find(".modal-header").css('background','azure');
                    //change text
                    $("#infoModal").find(".modal-title").html("Cancelled Successfully!");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    cancel_modal.modal('hide');

                    $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                }
                else {
                    //change background
                    $("#infoModal").find(".modal-header").css('background','lightsalmon');
                    //change text
                    $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    }

    $("body").delegate("#confirm_cancel", 'click', function() {
        cancelCharge();
    });

    //d4m unallocate
    $("body").delegate(".d4m-unallocate", 'click', function() {
        var bookID = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        var cancel_modal = $("#unallocate_modal");
        cancel_modal.find('.unallocate-book-id').html(bookID);
        cancel_modal.modal('show');
    });
    function unallocateCharge() {
        if (reason === "-1") {
            alert("You have not selected any unallocate reason. Please select a reason to cancel the trip.");
            return;
        }
        var cancel_modal = $("#unallocate_modal");
        cancel_modal.find('.unallocate-book-id').html(bookID);
        var bookID = cancel_modal.find('.unallocate-book-id').html();
        var reason = document.getElementById('unallocateReason').value;
        var reason_details;
        if (reason === "0") {
            reason_details = "Change of Plans";
        } else if (reason === "1") {
            reason_details = "Driver Denied";
        } else if (reason === "2") {
            reason_details = "Favorite Driver Not Assigned";
        } else if (reason === "3") {
            reason_details = "Driver Requesting Extra Fare";
        } else if (reason === "4") {
            reason_details = "Driver Asking to Take Offline";
        } else if (reason === "5") {
            reason_details = "Selected Wrong Location";
        } else if (reason === "6") {
            reason_details = "Selected Different Service";
        } else if (reason === "7") {
            reason_details = "Booked by Mistake";
        } else if (reason === "8") {
            reason_details = "Wait Time Too Long";
        } else if (reason === "9") {
            reason_details = "Got Driver Elsewhere";
        } else if (reason === "10") {
            reason_details = "Checking Price Estimate";
        } else if (reason === "11") {
            reason_details = "Taking Too Long to Allocate";
        } else if (reason === "12") {
            reason_details = "Direct Trip";
        } else if (reason === "13") {
            reason_details = "Wrongly Taken";
        } else if (reason === "14") {
            reason_details = "Previous Trip Not Ended";
        } else if (reason === "15") {
            reason_details = "Personal Issue";
        } else if (reason === "16") {
            reason_details = "Transportation Problem";
        } else if (reason === "17") {
            reason_details = "Customer Not Responding";
        } else if (reason === "18") {
            reason_details = "Customer Asked to Cancel";
        } else if (reason === "19") {
            reason_details = "Favorite Driver Not Assigned";
        } else if (reason === "62") {
            reason_details = "Other";
        } else {
            reason_details = "No Allocation";
        }
        data = new FormData();
        data.append("booking_id", bookID);
        data.append("reason", reason);
        data.append("reason_details",reason_details);
        $.ajax({
            url: window.location.protocol + '//' + window.location.host + '/api/admin/cancel/charge',
            type: 'POST',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(data) {
                console.log("UnallocateChargeLog", data);
                let status = data.success || -1;
                if (status === 1) {
                    if (data.charge[1] === "-1") {
                        alert("Please select a valid reason.");
                        return;
                    }
                    let confirmation = confirm("Customer cancellation charges will be ₹ " + data.charge[0] + ". \nDriver cancellation charges will be ₹ " + data.charge[1] + ". \nAre you sure you want to unallocate?");
                    if (confirmation) {
                        unallocateBooking();
                    }
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }
        });
    }
    function unallocateBooking(){
        var cancel_modal = $("#unallocate_modal");
        var bookID = cancel_modal.find('.unallocate-book-id').html();
        var reason = document.getElementById('unallocateReason').value;
        var reason_details;
        if (reason === "0") {
            reason_details = "Change of Plans";
        } else if (reason === "1") {
            reason_details = "Driver Denied";
        } else if (reason === "2") {
            reason_details = "Favorite Driver Not Assigned";
        } else if (reason === "3") {
            reason_details = "Driver Requesting Extra Fare";
        } else if (reason === "4") {
            reason_details = "Driver Asking to Take Offline";
        } else if (reason === "5") {
            reason_details = "Selected Wrong Location";
        } else if (reason === "6") {
            reason_details = "Selected Different Service";
        } else if (reason === "7") {
            reason_details = "Booked by Mistake";
        } else if (reason === "8") {
            reason_details = "Wait Time Too Long";
        } else if (reason === "9") {
            reason_details = "Got Driver Elsewhere";
        } else if (reason === "10") {
            reason_details = "Checking Price Estimate";
        } else if (reason === "11") {
            reason_details = "Taking Too Long to Allocate";
        } else if (reason === "12") {
            reason_details = "Direct Trip";
        } else if (reason === "13") {
            reason_details = "Wrongly Taken";
        } else if (reason === "14") {
            reason_details = "Previous Trip Not Ended";
        } else if (reason === "15") {
            reason_details = "Personal Issue";
        } else if (reason === "16") {
            reason_details = "Transportation Problem";
        } else if (reason === "17") {
            reason_details = "Customer Not Responding";
        } else if (reason === "18") {
            reason_details = "Customer Asked to Cancel";
        } else if (reason === "19") {
            reason_details = "Favorite Driver Not Assigned";
        } else if (reason === "62") {
            reason_details = "Other";
        } else {
            reason_details = "No Allocation";
        }
        data = new FormData();
        data.append("booking_id", bookID);
        data.append("reason", reason);
        data.append("reason_details",reason_details);
        //uncomment when api ready
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/unallocate',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                console.log(response)
                if(response['success'] == 1) {

                    //change background
                    $("#infoModal").find(".modal-header").css('background','azure');
                    //change text
                    $("#infoModal").find(".modal-title").html("Unallocated Successfully!");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    cancel_modal.modal('hide');

                    $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                }
                else {
                    //change background
                    $("#infoModal").find(".modal-header").css('background','lightsalmon');
                    //change text
                    $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    }

    $("body").delegate("#confirm_unallocate", 'click', function() {
        unallocateCharge();
    });

    //change payment mode
    $("body").delegate(".payment-option", 'click', function() {
        var bookID = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        var currPaymentMode = $(this).hasClass("Payment-Mode-Cash") ? "Cash" : "Credit";
        var newPaymentMode = $(this).hasClass("Payment-Mode-Cash") ? "Credit" : "Cash";
        if(!confirm(
                "Change Payment Mode of Booking #" + bookID + " from " + currPaymentMode + " to " + newPaymentMode + "?"
            )) return true;

        data = new FormData();
        data.append('book_id', parseInt(bookID));
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/payment_switch',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightskyblue');
                        //change text
                        $("#infoModal").find(".modal-title").html("Payment mode Changed to <b>"+ newPaymentMode +"</b>!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    });

    //unallocate trip
    $("body").delegate(".unallocate", 'click', function() {
        var bookID = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        if(!confirm(
                "Unallocate driver from Booking #" + bookID + "?"
            )) return true;

        data = new FormData();
        data.append('book_id', parseInt(bookID));
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/unallocate',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','whitesmoke');
                        //change text
                        $("#infoModal").find(".modal-title").html("Booking Successfully unallocated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);

                        $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });

            var booking_entry = $(".entry-book-id").filter(function( index ) {
                    return $(this).text().indexOf(bookID) >= 0;
            }).closest('td').find('.refresh-entry').click();
    });

   $("body").delegate(".triptype-convert", 'click', function() {
        var bookID = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        var triptype_dest_pick_modal = $("#triptype_dest_pick_modal");
        triptype_dest_pick_modal.find('.dest-pick-book-id').html(bookID);
        triptype_dest_pick_modal.modal('show');

    });

    $("body").delegate(".cartype-convert", 'click', function() {
        var bookID = $(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim();
        var car_type_modal = $("#car_type_modal");
        car_type_modal.find('.car-type-book-id').html(bookID);
        car_type_modal.modal('show');

    });

    $("body").delegate("#confirm_car_type", 'click', function() {
        var car_type_modal = $("#car_type_modal");
        var bookID = car_type_modal.find('.car-type-book-id').html();
        var gearType=0;
        var carSelect = document.getElementById("carselect");
        var carType = parseInt(carSelect.value);
        if(document.getElementById('automaticGear').checked){
            carType+=4;
        }
        data = new FormData();
        data.append("booking_id", bookID);
        data.append("new_cartype", carType);

        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/cartype_change',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                console.log(response)
                if(response['success'] == 1) {

                    //change background
                    $("#infoModal").find(".modal-header").css('background','azure');
                    //change text
                    $("#infoModal").find(".modal-title").html("Car Type Updated Successfully!");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                    car_type_modal.modal('hide');

                    $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                }
                else {
                    //change background
                    $("#infoModal").find(".modal-header").css('background','lightsalmon');
                    //change text
                    $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });

    });


    $("body").delegate("#confirm_tripType_convert", 'click', function() {
        var triptype_dest_pick_modal = $("#triptype_dest_pick_modal");
        var bookID = triptype_dest_pick_modal.find('.dest-pick-book-id').html();
        var valid = true;
        if(!confirm(
                "Convert trip: (#" + bookID + ") ?"
            )) return true;

        var tripTypeDest = $("#tripTypeDestSearch").val();
        var tripTypeChange = $('#tripType').find(":selected").val();
        var submissionMessage = "";
        var tripTypeDestLocationObject;
        if(isNullOrEmpty(tripTypeDest))
        {
            $("#tripTypeDestSearch").addClass("input-error");
            valid = false;
            submissionMessage = "Destination Location - Empty";
        }
        else {
            //alert(tripTypeDest);
            tripTypeDestLocationObject = getLatLongMaps(tripTypeDest);
            if(isNullOrEmpty(tripTypeDestLocationObject["latitude"]) || isNullOrEmpty(tripTypeDestLocationObject["longitude"])) {
                $("#tripTypeDestSearch").addClass("input-error");
                valid = false;
                submissionMessage = "Destination Location - Coordinates not found";
            }
        }
        if(valid) {
            data = new FormData();
            data.append("booking_id", bookID);
            data.append("booking_type", tripTypeChange);
            data.append("lat", parseFloat(tripTypeDestLocationObject["latitude"]));
            data.append("lng", parseFloat(tripTypeDestLocationObject["longitude"]));
            data.append("address", tripTypeDest);

            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/b2c_type_convert',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','azure');
                        //change text
                        $("#infoModal").find(".modal-title").html("Successfully converted to trip type!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                        triptype_dest_pick_modal.modal('hide');

                        $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }

        else {
            //change background
            $("#infoModal").find(".modal-header").css('background','gold');
            //change text
            $("#infoModal").find(".modal-title").html(submissionMessage);
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
        }
    });
    //load drivers list with location
    $("#driversView").click(function() {
        loadDriversList("drivers-list", 1, false);
    });

    //load perm drivers list with location
    $("#permaDriversView").click(function() {
        loadDriversList("perma-drivers-list", 1, true);
    });

    //load drivers list with location
    $("#KolDriversView").click(function() {
        loadDriversList("kol-drivers-list", 1, false, 0);
    });

    //load perm drivers list with location
    $("#KolPermaDriversView").click(function() {
        loadDriversList("kol-perma-drivers-list", 1, true, 0);
    });

    //load drivers list with location
    $("#HydDriversView").click(function() {
        loadDriversList("hyd-drivers-list", 1, false, 1);
    });

    //load perm drivers list with location
    $("#HydPermaDriversView").click(function() {
        loadDriversList("hyd-perma-drivers-list", 1, true, 1);
    });

    //load drivers list with location
    $("#NgpDriversView").click(function() {
        loadDriversList("ngp-drivers-list", 1, false, 3);
    });

    //load perm drivers list with location
    $("#NgpPermaDriversView").click(function() {
        loadDriversList("ngp-perma-drivers-list", 1, true, 3);
    });

    //load drivers list with location
    $("#MumDriversView").click(function() {
        loadDriversList("mum-drivers-list", 1, false, 5);
    });

    //load drivers list with location
    $("#DelDriversView").click(function() {
        loadDriversList("del-drivers-list", 1, false, 6);
    });

    //load drivers list with location
    $("#LuckDriversView").click(function() {
        loadDriversList("luck-drivers-list", 1, false, 17);
    });

    //load drivers list with location
    $("#GurDriversView").click(function() {
        loadDriversList("gur-drivers-list", 1, false, 14);
    });

    //load drivers list with location
    $("#NoiDriversView").click(function() {
        loadDriversList("noi-drivers-list", 1, false, 15);
    });

    //load drivers list with location
    $("#BanDriversView").click(function() {
        loadDriversList("ban-drivers-list", 1, false, 8);
    });

    //load drivers list with location
    $("#CheDriversView").click(function() {
        loadDriversList("che-drivers-list", 1, false, 7);
    });

    //load drivers list with location
    $("#BhbDriversView").click(function() {
        loadDriversList("bhb-drivers-list", 1, false, 11);
    });

    //load drivers list with location
    $("#PatDriversView").click(function() {
        loadDriversList("pat-drivers-list", 1, false, 12);
    });

    //load drivers list with location
    $("#RanDriversView").click(function() {
        loadDriversList("ran-drivers-list", 1, false, 13);
    });

    //load drivers list with location
    $("#AhmDriversView").click(function() {
        loadDriversList("ahm-drivers-list", 1, false, 9);
    });

    //load drivers list with location
    $("#JaiDriversView").click(function() {
        loadDriversList("jai-drivers-list", 1, false, 16);
    });

    //load drivers list with location
    $("#GuwDriversView").click(function() {
        loadDriversList("guw-drivers-list", 1, false, 2);
    });

    //load drivers list with location
    $("#SilDriversView").click(function() {
        loadDriversList("sil-drivers-list", 1, false, 10);
    });

    //load drivers list with location
    $("#ChdDriversView").click(function() {
        loadDriversList("chd-drivers-list", 1, false, 18);
    });

    //load perm drivers list with location
    $("#MumPermaDriversView").click(function() {
        loadDriversList("mum-perma-drivers-list", 1, true, 5);
    });

    //load drivers list with location
    $("#PuneDriversView").click(function() {
        loadDriversList("pune-drivers-list", 1, false, 4);
    });

    //load perm drivers list with location
    $("#PunePermaDriversView").click(function() {
        loadDriversList("pune-perma-drivers-list", 1, true, 4);
    });

    //display booking estimate details
    $("body").delegate(".change-estimate", 'click', function() {
        //clear form
        $("#bookingCharge").val('');
        $("#baseFare").val('');
        this_selector = $(this);
        var bookID = this_selector.closest('td').find('.entry-book-id').html().replace('ID:','').trim();
        data = new FormData();
        data.append('booking_id', bookID);
        data.append('action_type', 'estimate');
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/get_fares',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {
                        var price_change_modal = $("#price_change_modal");
                        price_change_modal.find('.price-change').html(bookID);
                        price_change_modal.find('.price-change').attr('class', 'price-change estimate-change-ref');
                        var book_charge = parseFloat(response['book_ch']).toFixed(2);
                        var maxBase = (parseFloat(response['est_max']) - parseFloat(book_charge)).toFixed(2);
                        var minBase = (parseFloat(response['est_min']) - parseFloat(book_charge)).toFixed(2);
                        if(maxBase == minBase)
                            price_change_modal.find('.current-net-estimate-range').html(minBase);
                        else
                            price_change_modal.find('.current-net-estimate-range').html(minBase + " - " + maxBase);
                        price_change_modal.find('.current-booking-charge').html(book_charge);
                        price_change_modal.find('.modal-header').attr('class', 'modal-header ' + this_selector.closest('tr').attr('class'));
                        price_change_modal.find('.modal-footer').attr('class', 'modal-footer ' + this_selector.closest('tr').attr('class'));
                        price_change_modal.modal('show');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    });

    $("body").delegate(".book-comments", 'click', function() {

        var commentModal = $("#commentEdit_modal");
        commentModal.css({"top": "20%", "left": "20%"});
        commentModal.find("#comment").val($(this).closest('tr').find('.book-comments').attr('data-original-title').substring(8));
        commentModal.find(".booking-id").html($(this).closest('tr').find(".entry-book-id").html().replace('ID:','').trim());
        commentModal.modal('show');
        window.comment = commentModal.find("#comment").val();
    })

    //display booking estimate details
    $("body").delegate(".change-fare", 'click', function() {
        //clear form
        $("#bookingCharge").val('');
        $("#baseFare").val('');
        this_selector = $(this);
        var bookID = this_selector.closest('td').find('.entry-book-id').html().replace('ID:','').trim();
        data = new FormData();
        data.append('booking_id', bookID);
        data.append('action_type', 'fare');
        $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/get_fares',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {
                        var price_change_modal = $("#price_change_modal");
                        price_change_modal.find('.price-change').html(bookID);
                        price_change_modal.find('.price-change').attr('class', 'price-change fare-change-ref');
                        var book_charge = parseFloat(response['book_ch']).toFixed(2);
                        var maxBase = (parseFloat(response['est_max']) - parseFloat(book_charge)).toFixed(2);
                        var minBase = (parseFloat(response['est_min']) - parseFloat(book_charge)).toFixed(2);
                        if(maxBase == minBase)
                            price_change_modal.find('.current-net-estimate-range').html(minBase);
                        else
                            price_change_modal.find('.current-net-estimate-range').html(minBase + " - " + maxBase);
                        price_change_modal.find('.current-booking-charge').html(book_charge);
                        price_change_modal.find('.modal-header').attr('class', 'modal-header ' + this_selector.closest('tr').attr('class'));
                        price_change_modal.find('.modal-footer').attr('class', 'modal-footer ' + this_selector.closest('tr').attr('class'));
                        price_change_modal.modal('show');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
    });

    //when price change modal opens focus on base fare input
    $('#price_change_modal').on('shown.bs.modal', function() {
        $('#baseFare').focus();
    });
    //confirm price change
    $("#confirm_price_change").click(function() {
        var validData = true;
        var modalSelector = $("#price_change_modal");
        //amount format regex
        //validate base price input
        var updatedBase = parseFloat($("#baseFare").val().trim());
        if(isNaN(updatedBase)) {
            $("#baseFare").css('border','2px solid red');         //error indication
            //error popover
            $("#baseFare").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#baseFare').data('bs.popover').options.content = "Data missing or in improper format";
            validData = false;
        }
        else {
            $("#baseFare").popover('destroy');
            updatedBase = updatedBase.toFixed(2);
            validData = true;
        }
        //validate booking fare input
        var updatedBookCharge = parseFloat($("#bookingCharge").val().trim());
        if(isNaN(updatedBookCharge)) {
            $("#bookingCharge").css('border','2px solid red');         //error indication
            //error popover
            $("#bookingCharge").popover({
                placement: "bottom",
                trigger: "hover"
            });
            $('#bookingCharge').data('bs.popover').options.content = "Data missing or in improper format";
            validData = false;
        }
        else {
            $("#bookingCharge").popover('destroy');
            updatedBookCharge = updatedBookCharge.toFixed(2);
            validData = true;
        }

        //initiate price change
        if(validData) {
            var bookID = modalSelector.find('.price-change').html().replace('ID:','').trim();
            var bookingType = modalSelector.find('.modal-header').attr('class').replace('modal-header booking-','').trim();
            var changeType = modalSelector.find('.price-change').attr('class').replace('price-change', '').replace('-change-ref', '').trim();
            /*if(!confirm(
                "Make " + changeType + " change for (" + bookingType + ") booking id: " + bookID +
                " Book Fare: " + updatedBookCharge + " & " + " Net Fare: " + updatedBase + " ?")
            ) return true;*/
            modalSelector.modal('hide');
            //alert(bookID + " " + bookingType + " " + changeType + " " + updatedBase + " " + updatedBookCharge);
            var data = new FormData();
            data.append('booking_id', bookID);
            data.append('booking_type', bookingType);
            data.append('change_type', changeType);
            data.append('base_fare', updatedBase);
            data.append('book_charge', updatedBookCharge);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/change_price',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lavender');
                        //change text
                        $("#infoModal").find(".modal-title").html("Price updated.");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }
    });

    //add fine for In-house driver
    $("body").delegate(".add-fine",'click',function() {
        var parent = $(this).closest('tr');
        driverID = parseInt(parent.find(".driver-id").html().trim());
        driverName = parent.find(".driver-name").html().trim();
        $("#perma_accounts_modal").find(".perma-accounts-action-statement").html("Add fine for");
        $("#perma_accounts_modal").find(".perma-accounts-ref").html(driverID);
        $("#perma_accounts_modal").find(".perma-accounts-name").html(driverName);
        $("#perma_accounts_modal").find(".perma-accounts-action-input-label").html("Fine Amount");
        $("#perma_accounts_modal").find(".perma-accounts-timestamp-input-label").html("Fine Date");
        $("#perma_accounts_modal").attr("class", "modal fade perma-fine-addition");
        $('#datetimepicker_perma_account_timestamp').datetimepicker({
            format: "DD/MM/YYYY",
            locale: 'en',
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $('#datetimepicker_perma_account_timestamp').data("DateTimePicker").date(new Date());
        $("#perma_accounts_modal").modal('show');
    });

    //advance payment record for In-house driver
    $("body").delegate(".add-advance",'click',function() {
        var parent = $(this).closest('tr');
        driverID = parseInt(parent.find(".driver-id").html().trim());
        driverName = parent.find(".driver-name").html().trim();
        $("#perma_accounts_modal").find(".perma-accounts-action-statement").html("Record Advance Payment for");
        $("#perma_accounts_modal").find(".perma-accounts-ref").html(driverID);
        $("#perma_accounts_modal").find(".perma-accounts-name").html(driverName);
        $("#perma_accounts_modal").find(".perma-accounts-action-input-label").html("Advance Amount");
        $("#perma_accounts_modal").find(".perma-accounts-timestamp-input-label").html("Advance Sanction Date");
        $("#perma_accounts_modal").attr("class", "modal fade perma-advance-record");
        $('#datetimepicker_perma_account_timestamp').datetimepicker({
            format: "DD/MM/YYYY",
            locale: 'en',
            widgetPositioning: {
                horizontal: 'right',
                vertical: 'bottom'
            }
        });
        $('#datetimepicker_perma_account_timestamp').data("DateTimePicker").date(new Date());
        $("#perma_accounts_modal").modal('show');
    });

    /*$("body").delegate("#pickProfImage", 'click', function() {
       $("#profPickInput").trigger("click");
    });

    $("body").delegate("#profPickInput", 'change', function() {
        readImageURL(this, $("#picChangePreview"));
        $("#uploadProf").removeClass("collapse");
    });*/

    $("body").delegate("#uploadProf", "click", function() {
       var driver_ID = $("#driverProfileModal").find(".modal-title").text().split(":")[0].replace("#", "").trim();
        var form_data = new FormData();
        form_data.append('driver_id', driver_ID);
        if($('#profileImg').val() != ''){
            form_data.append('pic', $('#profileImg').prop('files')[0]);
        }
        if($('#licFImg').val() != ''){
            form_data.append('lic_doc_front', $('#licFImg').prop('files')[0]);
        }
        if($('#licBImg').val() != ''){
            form_data.append('lic_doc_back', $('#licBImg').prop('files')[0]);
        }
        if($('#idFImg').val() != ''){
            form_data.append('id_doc_front', $('#idFImg').prop('files')[0]);
        }
        if($('#idBImg').val() != ''){
            form_data.append('id_doc_back', $('#idBImg').prop('files')[0]);
        }
        if($('#acImg').val() != ''){
            form_data.append('acc_doc', $('#acImg').prop('files')[0]);
        }
        if($('#profileImg').val() == '' && $('#licFImg').val() == '' && $('#licBImg').val() == '' && $('#idFImg').val() == '' && $('#idBImg').val() == '' && $('#acImg').val() == ''){
            alert("Please select Image and Try Again");
            return;
        }
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_update/pic',
            data: form_data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    //change background
                    $("#infoModal").find(".modal-header").css('background','lavender');
                    //change text
                    $("#infoModal").find(".modal-title").html("Profile Picture Changed.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                }
                else {
                    //change background
                    $("#infoModal").find(".modal-header").css('background','lightsalmon');
                    //change text
                    $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });

    $("body").delegate(".driver-profile", 'click', function() {
        $('#driverProfileModal').remove();
        var driverDataSource = $(this).closest("tr");
        $("body").append(
            '<div class="modal fade" id="driverProfileModal" role="dialog">'+
                '<div class="modal-dialog" style="width: 60%!important;">'+

                    '<div class="modal-content">'+
                        '<div class="modal-header">'+
                            '<h4 class="modal-title">'+
                            '# ' + driverDataSource.find(".driver-id").html() + " : " + driverDataSource.find(".driver-name").html() +' <button id ="editFirstName" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editFirstName" data-toggle="modal" data-target="#edit_modal"><span class="glyphicon glyphicon-edit"></span> First Name</button>'  +' <button id ="editLastName" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editLastName" data-toggle="modal" data-target="#edit_modal"><span class="glyphicon glyphicon-edit"></span> Last Name</button>' +
                            ' <button id ="editLocation" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editLocation" data-toggle="modal" data-target="#driver_location_pick_modal"><span class="glyphicon glyphicon-edit"></span> Location</button>' +
                            '</h4>'+
                        '</div>'+
                        '<div class="modal-body">'+
                            '<div class="row">'+
                                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 small-top-padding driver-details-area">'+
                                    '<h2>Profile</h2><hr>'+
                                    '<h4>Contact: <span id="driverContact">'+ driverDataSource.find(".driver-contact").html() +'</h4>'+
                                    '<h5>Licence: <span id="driverLicence">'+ driverDataSource.find(".driver-licence").html() +' <button id ="editLicense" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editLicense" data-toggle="modal" data-target="#edit_modal"><span class="glyphicon glyphicon-edit"></span> Edit</button>' +'</h5>'+
                                    '<h5>Verification Number: <span id="verificationPhone">'+ driverDataSource.find(".verif-phone").val() +' <button id ="editVerificationPhone" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editVerificationPhone" data-toggle="modal" data-target="#edit_modal"><span class="glyphicon glyphicon-edit"></span> Edit</button>' + '</h5>'+
                                    '<h5>A/C Number: <span id="bankAccountNumber">'+ driverDataSource.find(".bank-account-number").val() +' <button id ="editAccount" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editAccount" data-toggle="modal" data-target="#edit_modal"><span class="glyphicon glyphicon-edit"></span> Edit</button>' +'</h5>'+
                                    '<h5>IFSC Code: <span id="bankIfscNumber">'+ driverDataSource.find(".bank-ifsc-number").val() + ' <button id ="editIFSC" type="button" class="btn btn-default btn-sm" data-driver-id=' + driverDataSource.find(".driver-id").html() + ' data-id="editIFSC" data-toggle="modal" data-target="#edit_modal"><span class="glyphicon glyphicon-edit"></span> Edit</button>' +'</h5>'+
                                    '<h5>Rating: <span id="driverRating">'+ driverDataSource.find(".driver-rating").html() +'</h5>'+
                                    //'<button id="pickProfImage" class="btn btn-sm btn-warning"><i class="fa fa-camera" aria-hidden="true"></i>&emsp; Change Profile Picture</button>'+
                                    '<h5>Profile Picture: <input accept="image/*" type="file" id="profileImg"/><input type="button" value="Preview" id="profileImgBtn"></h5>'+
                                    '<h5>License Front: <input accept="image/*" type="file" id="licFImg"/><input type="button" value="Preview" id="licFImgBtn"></h5>'+
                                    '<h5>License Back: <input accept="image/*" type="file" id="licBImg"/><input type="button" value="Preview" id="licBImgBtn"></h5>'+
                                    '<h5>ID Front: <input accept="image/*" type="file" id="idFImg"/><input type="button" value="Preview" id="idFImgBtn"></h5>'+
                                    '<h5>ID Back: <input accept="image/*" type="file" id="idBImg"/><input type="button" value="Preview" id="idBImgBtn"></h5>'+
                                    '<h5>A/C: <input accept="image/*" type="file" id="acImg"/><input type="button" value="Preview" id="acImgBtn"></h5>'+
                                    //'<input type="file" id="profPickInput" style="visibility: hidden;" name="pic" />'+
                                    '<img id="picChangePreview" src="#" style="width: 100%;" />'+
                                    '<button id="uploadProf" class="btn btn-sm btn-success"><i class="fa fa-cloud-upload" aria-hidden="true"></i>&emsp; Confirm Picture Upload</button>'+
                                '</div>'+
                                '<div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 small-top-padding">'+
                                    '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding">'+
                                        '<div class="row">'+
                                            '<div id="ProfileTab" class="col-lg-2 col-md-2 col-sm-2 col-xs-4 small-top-padding picture-tab active">'+
                                            'Profile</div>'+
                                            '<div id="LFTab" class="col-lg-2 col-md-2 col-sm-2 col-xs-4 small-top-padding picture-tab">'+
                                            'LF</div>'+
                                            '<div id="LBTab" class="col-lg-2 col-md-2 col-sm-2 col-xs-4 small-top-padding picture-tab">'+
                                            'LB</div>'+
                                            '<div id="IFTab" class="col-lg-2 col-md-2 col-sm-2 col-xs-4 small-top-padding picture-tab">'+
                                            'IF</div>'+
                                            '<div id="IBTab" class="col-lg-2 col-md-2 col-sm-2 col-xs-4 small-top-padding picture-tab">'+
                                            'IB</div>'+
                                            '<div id="ACTab" class="col-lg-2 col-md-2 col-sm-2 col-xs-4 small-top-padding picture-tab">'+
                                            'A/C</div>'+
                                        '</div>'+
                                    '</div>'+
                                    '<div id="ProfilePictureDisplay" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding picture-area">'+
                                        '<img src= ' + $(this).attr('src') + ' alt="Italian Trulli" style="width: 100%; height: 100%; object-fit: cover; overflow: hidden;">'+
                                    '</div>'+
                                    '<div id="LFDisplay" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding picture-area collapse">'+
                                        '<img src= "https://storage.drivers4me.com/static/uploads/' + driverDataSource.find(".driver-lic-front").val() + '" alt="Italian Trulli" style="width: 100%; height: 100%; object-fit: contain; overflow: hidden;">'+
                                    '</div>'+
                                    '<div id="LBDisplay" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding picture-area collapse">'+
                                        '<img src= "https://storage.drivers4me.com/static/uploads/' + driverDataSource.find(".driver-lic-back").val() + '" alt="Italian Trulli" style="width: 100%; height: 100%; object-fit: contain; overflow: hidden;">'+
                                    '</div>'+
                                    '<div id="IFDisplay" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding picture-area collapse">'+
                                        '<img src= "https://storage.drivers4me.com/static/uploads/' + driverDataSource.find(".driver-id-front").val() + '" alt="Italian Trulli" style="width: 100%; height: 100%; object-fit: contain; overflow: hidden;">'+
                                    '</div>'+
                                    '<div id="IBDisplay" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding picture-area collapse">'+
                                        '<img src= "https://storage.drivers4me.com/static/uploads/' + driverDataSource.find(".driver-id-back").val() + '" alt="Italian Trulli" style="width: 100%; height: 100%; object-fit: contaon; overflow: hidden;">'+
                                    '</div>'+
                                    '<div id="ACDisplay" class="col-lg-12 col-md-12 col-sm-12 col-xs-12 small-top-padding picture-area collapse">'+
                                        '<img src= "https://storage.drivers4me.com/static/uploads/' + driverDataSource.find(".driver-account").val() + '" alt="Italian Trulli" style="width: 100%; height: 100%; object-fit: contain; overflow: hidden;">'+
                                    '</div>'+
                                '</div>'+
                            '</div>'+
                        '</div>'+
                        '<div class="modal-footer">'+
                            '<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>'+
                        '</div>'+
                    '</div>'+
                '</div>'+
            '</div>'
        );

        /*$("#ProfilePictureDisplay").css("background-image", "url(" + $(this).attr('src') + ")");
        $("#LFDisplay").css("background-image", "url(/static/uploads/" + driverDataSource.find(".driver-lic-front").val() + ")");
        $("#LBDisplay").css("background-image", "url(/static/uploads/" + driverDataSource.find(".driver-lic-back").val() + ")");
        $("#IFDisplay").css("background-image", "url(/static/uploads/" + driverDataSource.find(".driver-id-front").val() + ")");
        $("#IBDisplay").css("background-image", "url(/static/uploads/" + driverDataSource.find(".driver-id-back").val() + ")");
        $("#ACDisplay").css("background-image", "url(/static/uploads/" + driverDataSource.find(".driver-account").val() + ")");*/
        $("#driverProfileModal").modal('show');
    });
    $("body").delegate("#profileImg", 'change', function() {
        const [file] = $('#profileImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#profileImgBtn", 'click', function() {
        const [file] = $('#profileImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#licFImg", 'change', function() {
        const [file] = $('#licFImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#licFImgBtn", 'click', function() {
        const [file] = $('#licFImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#licBImg", 'change', function() {
        const [file] = $('#licBImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#licBImgBtn", 'click', function() {
        const [file] = $('#licBImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#idFImg", 'change', function() {
        const [file] = $('#idFImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#idFImgBtn", 'click', function() {
        const [file] = $('#idFImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#idBImg", 'change', function() {
        const [file] = $('#idBImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#idBImgBtn", 'click', function() {
        const [file] = $('#idBImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#acImg", 'change', function() {
        const [file] = $('#acImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#acImgBtn", 'click', function() {
        const [file] = $('#acImg').prop('files');
        if (file) {
            picChangePreview.src = URL.createObjectURL(file)
        }
    });
    $("body").delegate("#editIFSC", 'click', function() {
        $("#editModalLabel").text("Edit IFSC Code");
        $("#editModalText").attr("placeholder", "Enter IFSC Code");
    });

    $("body").delegate("#editAccount", 'click', function() {
        $("#editModalLabel").text("Edit Account Number");
        $("#editModalText").attr("placeholder", "Enter Account Number");
    });

    $("body").delegate("#editVerificationPhone", 'click', function() {
        $("#editModalLabel").text("Edit Verification Phone Number");
        $("#editModalText").attr("placeholder", "Enter Verification Phone Number");
    });
    $("body").delegate("#editLicense", 'click', function() {
        $("#editModalLabel").text("Edit License Number");
        $("#editModalText").attr("placeholder", "Enter License Number");
    });

    $("body").delegate("#editFirstName", 'click', function() {
        $("#editModalLabel").text("Edit First Name");
        $("#editModalText").attr("placeholder", "Enter First Name");
    });

    $("body").delegate("#editLastName", 'click', function() {
        $("#editModalLabel").text("Edit Last Name");
        $("#editModalText").attr("placeholder", "Enter Last Name");
    });

    $('#edit_modal').on('hidden.bs.modal', function () {
        $('#editModalText').val('');
    })
    $('#driver_location_pick_modal').on('hidden.bs.modal', function () {
        $('#driverLocationChange').val('');
    })

    $(function() {
        $("#driver_location_pick_modal").on('show.bs.modal', function(e) {
            var btn = $(e.relatedTarget);
            $('#confirm_driver_location').data('driver-id', btn.data('driver-id'));
            console.log(btn.data('driver-id'));
        });
        $('#confirm_driver_location').on('click', function() {
            var driver_id = $(this).data('driver-id')
            var valid = true;
            var driverLoc = $("#driverLocationChange").val();
            console.log(driverLoc)
            var submissionMessage = "";
            var driverLocObject;
            if(isNullOrEmpty(driverLoc)) {
                $("#driverLocationChange").addClass("input-error");
                valid = false;
                submissionMessage = "Location - Empty";
            }
            else {
                driverLocObject = getLatLongSublocalityMaps(driverLoc);
                console.log(driverLocObject)
                if(isNullOrEmpty(driverLocObject["latitude"]) || isNullOrEmpty(driverLocObject["longitude"])) {
                    $("#driverLocationChange").addClass("input-error");
                    valid = false;
                    submissionMessage = "Location - Coordinates not found";
                }
            }
            if (!valid) {
                $("#infoModal").find(".modal-header").css('background','gold');
                $("#infoModal").find(".modal-title").html(submissionMessage);
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                return;
            }
            data = new FormData();
            data.append("driver_id", driver_id);
            data.append("lat", parseFloat(driverLocObject["latitude"]));
            data.append("lng", parseFloat(driverLocObject["longitude"]));
            data.append("addr", driverLocObject["sublocality"]);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_update/base_loc',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','azure');
                        //change text
                        $("#infoModal").find(".modal-title").html("Successfully Updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                        $('#driver_location_pick_modal').modal('hide');
                        $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        });
    })

    $(function() {
        $('#edit_modal').on('show.bs.modal', function(e) {
            let btn = $(e.relatedTarget); // e.related here is the element that opened the modal, specifically the row button
            //alert(btn);
            let id = btn.data('id'); // this is how you get the of any `data` attribute of an element
            let driver_id = btn.data('driver-id')
            console.log(btn.data('driver-id'));
            $('.saveEdit').data('id', id); // then pass it to the button inside the modal
            $('.saveEdit').data('driver-id', driver_id);
        });

        $('.saveEdit').on('click', function() {
            let id = $(this).data('id'); // the rest is just the same
            let driver_id = $(this).data('driver-id')
            let editModalText = $('#editModalText').val();
            data = new FormData();
            if(id == "editIFSC"){
                data.append("ifsc", editModalText);
            }else if(id == "editAccount"){
                data.append("acc_no", editModalText);
            }else if(id == "editVerificationPhone"){
                data.append("verf_ph", editModalText);
            }else if(id == "editLicense"){
                data.append("lic_no", editModalText);
            }else if(id == "editFirstName"){
                data.append("fname", editModalText);
            }else if(id == "editLastName"){
                data.append("lname", editModalText);
            }
            data.append("driver_id", driver_id);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_update/info',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','azure');
                        //change text
                        $("#infoModal").find(".modal-title").html("Successfully Updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                        $('#edit_modal').modal('hide');
                        $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
                    }
                    else {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        });
    });

    $("body").delegate("#ProfileTab", 'click', function() {
        $("#LFDisplay").addClass('collapse');
        $("#LBDisplay").addClass('collapse');
        $("#IFDisplay").addClass('collapse');
        $("#IBDisplay").addClass('collapse');
        $("#ACDisplay").addClass('collapse');
        $("#ProfilePictureDisplay").removeClass('collapse');
        $(this).addClass('active');
        $("#LFTab").removeClass('active');
        $("#LBTab").removeClass('active');
        $("#IFTab").removeClass('active');
        $("#IBTab").removeClass('active');
        $("#ACTab").removeClass('active');
    });

    $("body").delegate("#LFTab", 'click', function() {
        $("#ProfilePictureDisplay").addClass('collapse');
        $("#LFDisplay").removeClass('collapse');
        $("#LBDisplay").addClass('collapse');
        $("#IFDisplay").addClass('collapse');
        $("#IBDisplay").addClass('collapse');
        $("#ACDisplay").addClass('collapse');
        $(this).addClass('active');
        $("#ProfileTab").removeClass('active');
        $("#LBTab").removeClass('active');
        $("#IFTab").removeClass('active');
        $("#IBTab").removeClass('active');
        $("#ACTab").removeClass('active');
    });

    $("body").delegate("#LBTab", 'click', function() {
        $("#ProfilePictureDisplay").addClass('collapse');
        $("#LFDisplay").addClass('collapse');
        $("#LBDisplay").removeClass('collapse');
        $("#IFDisplay").addClass('collapse');
        $("#IBDisplay").addClass('collapse');
        $("#ACDisplay").addClass('collapse');
        $(this).addClass('active');
        $("#ProfileTab").removeClass('active');
        $("#LFTab").removeClass('active');
        $("#IFTab").removeClass('active');
        $("#IBTab").removeClass('active');
        $("#ACTab").removeClass('active');
    });

    $("body").delegate("#IFTab", 'click', function() {
        $("#ProfilePictureDisplay").addClass('collapse');
        $("#LFDisplay").addClass('collapse');
        $("#LBDisplay").addClass('collapse');
        $("#IFDisplay").removeClass('collapse');
        $("#IBDisplay").addClass('collapse');
        $("#ACDisplay").addClass('collapse');
        $(this).addClass('active');
        $("#ProfileTab").removeClass('active');
        $("#LFTab").removeClass('active');
        $("#LBTab").removeClass('active');
        $("#IBTab").removeClass('active');
        $("#ACTab").removeClass('active');
    });

    $("body").delegate("#IBTab", 'click', function() {
        $("#ProfilePictureDisplay").addClass('collapse');
        $("#LFDisplay").addClass('collapse');
        $("#LBDisplay").addClass('collapse');
        $("#IFDisplay").addClass('collapse');
        $("#IBDisplay").removeClass('collapse');
        $("#ACDisplay").addClass('collapse');
        $(this).addClass('active');
        $("#ProfileTab").removeClass('active');
        $("#LFTab").removeClass('active');
        $("#LBTab").removeClass('active');
        $("#IFTab").removeClass('active');
        $("#ACTab").removeClass('active');
    });

    $("body").delegate("#ACTab", 'click', function() {
        $("#ProfilePictureDisplay").addClass('collapse');
        $("#LFDisplay").addClass('collapse');
        $("#LBDisplay").addClass('collapse');
        $("#IFDisplay").addClass('collapse');
        $("#IBDisplay").addClass('collapse');
        $("#ACDisplay").removeClass('collapse');
        $(this).addClass('active');
        $("#ProfileTab").removeClass('active');
        $("#LFTab").removeClass('active');
        $("#LBTab").removeClass('active');
        $("#IFTab").removeClass('active');
        $("#IBTab").removeClass('active');
    });

    //when price change modal opens focus on base fare input
    $('#perma_accounts_modal').on('shown.bs.modal', function() {
        $('#changeAmount').focus();
    });

    //Code for reset from erroneous fields..........................................................
    $("#baseFare").click(function() {
      $(this).css('border','');
    });
    $("#bookingCharge").click(function() {
      $(this).css('border','');
    });
    $("#changeAmount").click(function() {
      $(this).css('border','');
    });
    $("#CredCust_SearchInput_Phone, #LabelCust_SearchInput_Phone, #LabelDriver_SearchInput_Phone").click(function() {
        $(this).css('border','');
    });
    $("#CredCust_SearchInput_ID").click(function() {
        $(this).css('border','');
    });
    $("#phChange_newPhoneNum, #phChange_currPhoneNum").click(function() {
        $(this).css('border','');
    });


    //switch between pricing tabs
    $(".Customer-booking-customer-cancel").click(function() {
        $(".price-tab").attr('class', 'price-tab Customer-booking-customer-cancel');
        $(this).attr('class', 'price-tab price-tab-active col-lg-2 col-md-2 col-sm-12 col-xs-12');
        $(".pricing-section").attr('class','pricing-section standard-top-padding collapse');
        $("#" + $(this).attr('id').replace('-tab','')).attr('class','pricing-section standard-top-padding');
        $("#" + $(this).attr('id').replace('-tab','')).find(".surge-toggle").attr('class', 'btn btn-xs btn-warning surge-toggle');
        $("#" + $(this).attr('id').replace('-tab','')).find(".surge-estimate").attr('class', 'surge-estimate collapse');
        $("#" + $(this).attr('id').replace('-tab','')).find(".surge").attr('class', 'surge collapse');
        $("#" + $(this).attr('id').replace('-tab','')).find(".normal-estimate").attr('class', ' normal-estimate collapse');
        $("#" + $(this).attr('id').replace('-tab','')).find(".normal").attr('class', 'normal');


    });

    //display base estimate
    //Display Discounted Rate
    $(".hourly-rate").click(function() {
        if($(this).closest(".pricing-section").find(".surge-toggle").attr('class').indexOf('active') != -1) {
            //surge active
            if($(this).find(".surge").attr('class') == 'surge collapse') {
                $(this).find(".surge").attr("class","surge");
                $(this).find(".surge-estimate").attr("class","surge-estimate collapse");
            }
            else {
                $(this).find(".surge").attr("class","surge collapse");
                $(this).find(".surge-estimate").attr("class","surge-estimate");
            }
        }
        else {
            if($(this).find(".normal").attr('class') == 'normal collapse') {
                $(this).find(".normal").attr("class","normal");
                $(this).find(".normal-estimate").attr("class","normal-estimate collapse");
            }
            else {
                $(this).find(".normal").attr("class","normal collapse");
                $(this).find(".normal-estimate").attr("class","normal-estimate");
            }
        }
    });

    //handle surge display
    $(".surge-toggle").click(function() {
        if($(this).attr('class').indexOf('active') != -1) {
            var parentPricingElement = $(this).closest('.pricing-section-surge');
            //return to normal
            parentPricingElement.find(".surge-estimate").attr('class', 'surge-estimate collapse');
            parentPricingElement.find(".surge").attr('class', 'surge collapse');
            parentPricingElement.find(".normal-estimate").attr('class', ' normal-estimate collapse');
            parentPricingElement.find(".normal").attr('class', 'normal');
            $(".discounted").attr('class', 'discounted');
            $(this).attr('class', 'btn btn-xs btn-warning surge-toggle');
            parentPricingElement.attr('class', 'pricing-section standard-top-padding');
        }
        else {
            var parentPricingElement = $(this).closest('.pricing-section');
            //show surge
            parentPricingElement.find(".normal-estimate").attr('class', 'normal-estimate collapse');
            parentPricingElement.find(".normal").attr('class', 'normal collapse');
            parentPricingElement.find(".surge-estimate").attr('class', ' surge-estimate collapse');
            parentPricingElement.find(".surge").attr('class', 'surge');
            $(".discounted").attr('class', 'discounted collapse');
            $(this).attr('class', 'btn btn-xs btn-warning surge-toggle active');
            parentPricingElement.attr('class', 'pricing-section pricing-section-surge standard-top-padding');
        }
    });

    $(".cred-cust-search-criteria").click(function() {
        var searchCriterion = $(this).attr("id").replace("CredCust_SearchCriteria_", "").trim();
        $(".cred-cust-search-input").addClass("collapse");
        $("#CredCust_SearchInput_" + searchCriterion).removeClass("collapse");
        $("#customerCreditsForm").find(".curr-search-criteria").html("By " + searchCriterion);
    });

    $("#Search_Cred_Cust").click(function() {
        var mobile = $("#CredCust_SearchInput_Phone").val().trim();
        var userID = $("#CredCust_SearchInput_ID").val().replace(/^0+/, '').trim();
        var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");           //regex for phone validation
        var UIDRegex = new RegExp("^[1-9]{1,1}[0-9]*$")             //regex for user ID validation
        var searchMethod = $("#customerCreditsForm").find(".curr-search-criteria").html().replace("By ", "").trim();
        var validData = true;

        if(searchMethod === "Mobile") {
            //phone validation
            if (!mobile || mobile === "") {
                $("#CredCust_SearchInput_Phone").css('border','2px solid red');         //error indication
                //error popover
                $("#CredCust_SearchInput_Phone").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#CredCust_SearchInput_Phone').data('bs.popover').options.content = "This field cannot be blank";
                validData = false;
            }

            else if (!phoneRegex.test(mobile)) {
                $("#CredCust_SearchInput_Phone").css('border','2px solid red');         //error indication
                //error popover
                $("#CredCust_SearchInput_Phone").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#CredCust_SearchInput_Phone').data('bs.popover').options.content = "Enter correct mobile number without country-code";
                validData = false;
            }

            else {
                $("#CredCust_SearchInput_Phone").popover('destroy');
                validData = true;
            }

            if(validData) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/api/admin/cred_view',
                    beforeSend: function(request) {
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                        if (refresh_token) {

                            if (checkRefresh(csrf_token, refresh_token) == false) {
                                alert("Unfortunately, your session has expired. Please login again");
                                window.location  = "/adminLogin";
                            }
                        }
                        request.setRequestHeader('X-CSRF-Token', csrf_token);
                     },
                    data: {
                        mobile: mobile
                    },

                    dataType: "json",
                    success: function(user) {
                        if (user.success == 1)
                        {
                            $("#custId_Creds").html(parseInt(user.user_id));
                            $("#custName_Creds").html(user.name);
                            $("#custPhone").html(user.user_mobile);
                            $("#cust_amount_Creds").html(user.credit);
                        }
                        else {
                            alert("Error! (Code: " + e.success + ")");
                        }

                    },
                    error: function(e) {
                        $("#CredCust_SearchInput_Phone").css('border','2px solid red');         //error indication
                        //error popover
                        $("#CredCust_SearchInput_Phone").popover({
                            placement: "bottom",
                            trigger: "hover"
                        });
                        $('#CredCust_SearchInput_Phone').data('bs.popover').options.content = "Authentication Failed! Invalid Credentials";
                    }
                });
            }
        }

        else if(searchMethod === "ID") {
            //phone validation
            if (!userID || userID === "") {
                $("#CredCust_SearchInput_ID").css('border','2px solid red');            //error indication
                //error popover
                $("#CredCust_SearchInput_ID").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#CredCust_SearchInput_ID').data('bs.popover').options.content = "This field cannot be blank";
                validData = false;
            }

            else if (!UIDRegex.test(userID)) {
                $("#CredCust_SearchInput_ID").css('border','2px solid red');            //error indication
                //error popover
                $("#CredCust_SearchInput_ID").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#CredCust_SearchInput_ID').data('bs.popover').options.content = "User ID should be in correct format";
                validData = false;
            }

            else {
                $("#CredCust_SearchInput_ID").popover('destroy');
                validData = true;
            }

            if(validData) {
                alert("To be implemented");
            }
        }

    });

    //********
    // //reset modify amount input
    $("#customerCredits").click(function() {
        $("#cust_amount_change").val('');
    });
    //add to credit amount
    $("#custIncrAmount").click(function() {
        currDue = parseInt($("#cust_curr_creds").html());
        if($("#cust_amount_change").val() == '') amount_change = 0;
        else amount_change = parseInt($("#cust_amount_change").val());
        currDue = currDue + amount_change;
        $("#cust_curr_creds").html(currDue);
        $("#cust_amount_change").val('');
    });
    //subtract from credit amount
    $("#custDecrAmount").click(function() {
        currDue = parseInt($("#cust_curr_creds").html());
        if($("#cust_amount_change").val() == '') amount_change = 0;
        else
        amount_change = parseInt($("#cust_amount_change").val());
        currDue = currDue - amount_change;
        $("#cust_curr_creds").html(currDue);
        $("#cust_amount_change").val('');
    });
    //clear dues
    $("#custClearCredits").click(function() {
        $("#cust_curr_creds").html('0');
    });
    function isValidRemark(remark) {
        const regex = /^[A-Za-z\s]+$/;
        return regex.test(remark.trim());
    }
    //save current credits
    $("#custSaveAmount").click(function() {
        remark = $("#cust_remark").val().trim();

        // Check if remark is empty or not valid
        if (remark === '') {
            $("#remarkWarning").text("Remark is required").show();
            return;
        } else {
            $("#remarkWarning").hide();
        }
        //not a valid customer
        if($("#custId_Creds").html()=='') {
            //change background
            $("#infoModal").find(".modal-header").css('background','lightsalmon');
            //change text
            $("#infoModal").find(".modal-title").html("Customer not found");
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
        }
        else {
            //send current payment information
            data = new FormData();
            data.append('user_id', $("#custId_Creds").html());
            var credit_amount = parseFloat($("#cust_curr_creds").html());
            //var disc = $('#paid_check').is(":checked") ? 0: 1;
            data.append('amount', credit_amount);
            data.append('remark', remark);
            $.ajax({
                type:"POST",
                url: window.location.protocol + '//' + window.location.host + '/api/admin/credit_alter',
                data: data,
                beforeSend: function(request) {
                    var c = getCookie();
                    var csrf_token = c['csrf_access_token'];
                    var refresh_token = c['csrf_refresh_token'];
                    if (refresh_token) {

                        if (checkRefresh(csrf_token, refresh_token) == false) {
                            alert("Unfortunately, your session has expired. Please login again");
                            window.location  = "/adminLogin";
                        }
                    }
                    request.setRequestHeader('X-CSRF-Token', csrf_token);
                 },
                dataType: "json",
                contentType: false,
                processData: false,
                success: function(response) {
                    if(response['success'] == 1) {

                        //change background
                        $("#infoModal").find(".modal-header").css('background','springgreen');
                        //change text
                        $("#infoModal").find(".modal-title").html("Credits successfully updated");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2400);
                        //update due amount
                        $("#cust_amount_Creds").html(response['credit']);
                        $("#cust_curr_creds").html(0);
                        $("#cust_remark").val('')
                    }
                },
                error: function() {
                    //Connection error...............................................................
                    //change background
                    $("#infoModal").find(".modal-header").css('background','gold');
                    //change text
                    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                    //window.location = "/adminLogin";
                }

            });
        }
    });

    if(window.location.hash) {
      if (window.location.hash == "#drivers") {
          $("#driversView").click();
      } else if (window.location.hash == "#perma") {
          $("#permaDriversView").click();
      } else if (window.location.hash == "#kol_drivers") {
          $("#KolDriversView").click();
      } else if (window.location.hash == "#kol_perma") {
          $("#KolPermaDriversView").click();
      } else if (window.location.hash == "#hyd_drivers") {
          $("#HydDriversView").click();
      } else if (window.location.hash == "#hyd_perma") {
          $("#HydPermaDriversView").click();
      } else if (window.location.hash == "#ngp_drivers") {
          $("#NgpDriversView").click();
      } else if (window.location.hash == "#ngp_perma") {
          $("#NgpPermaDriversView").click();
      } else if (window.location.hash == "#mum_drivers") {
          $("#MumDriversView").click();
      } else if (window.location.hash == "#mum_perma") {
          $("#MumPermaDriversView").click();
      } else if (window.location.hash == "#del_drivers") {
          $("#DelDriversView").click();
      } else if (window.location.hash == "#luck_drivers") {
          $("#LuckDriversView").click();
      } else if (window.location.hash == "#gur_drivers") {
          $("#GurDriversView").click();
      } else if (window.location.hash == "#noi_drivers") {
          $("#NoiDriversView").click();
      } else if (window.location.hash == "#ban_drivers") {
          $("#BanDriversView").click();
      } else if (window.location.hash == "#chen_drivers") {
          $("#ChenDriversView").click();
      } else if (window.location.hash == "#bhb_drivers") {
          $("#BhbDriversView").click();
      } else if (window.location.hash == "#pat_drivers") {
          $("#PatDriversView").click();
      } else if (window.location.hash == "#ran_drivers") {
          $("#RanDriversView").click();
      } else if (window.location.hash == "#ahm_drivers") {
          $("#AhmDriversView").click();
      } else if (window.location.hash == "#jai_drivers") {
          $("#JaiDriversView").click();
      } else if (window.location.hash == "#guw_drivers") {
          $("#GuwDriversView").click();
      } else if (window.location.hash == "#sil_drivers") {
          $("#SilDriversView").click();
      } else if (window.location.hash == "#chd_drivers") {
          $("#ChdDriversView").click();
      } else if (window.location.hash == "#cust-creds") {
          $('#customerCredits').click();
      } else if (window.location.hash == "#cust-label") {
          $('#customerLabel').click();
      } else if (window.location.hash == "#driv-label") {
          $('#driverLabel').click();
      } else if (window.location.hash == "#pune_drivers") {
          $("#PuneDriversView").click();
      } else if (window.location.hash == "#pune_perma") {
          $("#PunePermaDriversView").click();
      } else if (window.location.hash == "#cust-dues") {
          $('#customerDues').click();
      } else if (window.location.hash == "#driv-dues") {
          $('#driverDues').click();
      } else if (window.location.hash == "#est-viewers") {
          $("#onlyEstimate").click();
      } else if (window.location.hash == "#anals") {
          $("#analytics").click();
      }  else if (window.location.hash == "#pricing") {
          $("#pricing").click();
      } else if (window.location.hash == "#utilities") {
          $("#utilities").click();
      } else if (window.location.hash == "#mobileLog") {
          $("#mobileLog").click();
      } else if (window.location.hash == "#userlog") {
          $("#userlog").click();
      } else if (window.location.hash == "#deleteLog") {
          $("#deleteLog").click();
      } else if (window.location.hash == "#b2c_bookings") {
          $("#CustomerBookingsView").click();
      } else if (window.location.hash == "#b2b_c24_bookings") {
          $("#C24BookingsView").click();
      } else if (window.location.hash == "#b2b_olx_bookings") {
          $("#OLXBookingsView").click();
      } else if (window.location.hash == "#b2b_zoomcar_bookings") {
          $("#ZoomcarBookingsView").click();
      } else if (window.location.hash == "#b2b_revv_bookings") {
          $("#RevvBookingsView").click();
      } else if (window.location.hash == "#b2b_gujral_bookings") {
          $("#GujralBookingsView").click();
      } else if (window.location.hash == "#b2b_cardekho_bookings") {
          $("#CardekhoBookingsView").click();
      } else if (window.location.hash == "#b2b_bhandari_bookings") {
          $("#BhandariBookingsView").click();
      } else if (window.location.hash == "#b2b_mahindra_bookings") {
          $("#MahindraBookingsView").click();
      } else if (window.location.hash == "#b2b_revv_v2_bookings") {
          $("#RevvV2BookingsView").click();
      } else if (window.location.hash == "#b2b_spinny_bookings") {
          $("#SpinnyBookingsView").click();
      } else if (window.location.hash == "#b2b_pridehonda_bookings") {
        $("#PrideHondaBookingsView").click();
      }
      else {
           $("#CustomerBookingsView").click();
      }
    } else {
        $("#CustomerBookingsView").click();
    }
    // $("#paystatus").hide();
    $("#methodFilter").change(function() {
        console.log('method');
        if ($(this).val() === "Razorpay") {
            console.log('razoor');
            $("#paystatus").removeClass("collapse")
        } else{
            $("#paystatus").addClass("collapse")
        }
    });
    $("#getCreditLog").click(function() {
        let mobileNumber = $("#cmobileNumber").val();
        let methodFilter = $("#methodFilter").val();
        let statusFilter = $("#statusFilter").val();
        let creditError = $("#creditError");
        
        if (mobileNumber.length !== 10 || !/^\d{10}$/.test(mobileNumber)) {
            creditError.text("Please enter a valid 10-digit mobile number.").show();
            $("#transactionLogs").hide();
            return;
        }
        let data = new FormData();
        data.append("mobile", mobileNumber);
        if (methodFilter) {
            data.append("method", methodFilter);
        }
        if (statusFilter) {
            data.append("status", statusFilter);
        }
    
        $.ajax({
            url: "/api/admin/customer_credit_log",
            type: 'POST',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {
                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        creditError.text("Unfortunately, your session has expired. Please login again.").show();
                        $("#transactionLogs").hide();
                        window.location = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
        
            success: function(response) {
                let transactionTableBody = $("#transactionTableBody");
                transactionTableBody.empty();
                
                if (response.success === 1) {
                    // Set user details
                    let userDetails = response.data[0];
                    const roleMapping = {
                        0: 'User ',
                        1: 'Driver',
                        2: 'Admin',
                        127: 'SuperAdmin'
                    };
                    $("#label").text(userDetails.name);
                    $("#userName").text(userDetails.name);
                    $("#userEmail").text(userDetails.email);
                    $("#userSex").text(userDetails.sex);
                    $("#labelfield").html(
                        ((getBit(userDetails.label,0)==1)?' <span title="VIP Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">V</span>' : '') +
                        ((getBit(userDetails.label,1)==1)?' <span title="Known Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">K</span>' : '') +
                        ((getBit(userDetails.label,2)==1)?' <span title="Issue previously" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">I</span>' : '') +
                        ((getBit(userDetails.label,3)==1)?' <span title="Bad Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">B</span>' : '') +
                        ((getBit(userDetails.label,4)==1)?' <span title="Cancels trip" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
                        ((getBit(userDetails.label,5)==1)?' <span title="Does Directly" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">D</span>' : '') )
                    $("#userRole").text(roleMapping[userDetails.role]);
                    $("#userCredit").text(userDetails.credit);
                    $("#userMobile").text(mobileNumber);
                    
                    const statusMapping = {
                        0: 'Initiated',
                        1: 'Completed',
                        2: 'Failed'
                    };
                    // Set transaction logs
                    for (let i = 1; i < response.data.length; i++) {
                        let entry = response.data[i];
                        let timestamp = entry.timestamp;
                        let amount = entry.amt;
                        let status = statusMapping[entry.status] || entry.status; 
                        let method = entry.method;
                        let bookingId = entry.booking_id || "";
                        let adminName = entry.admin_name || "";
                        let adminID = entry.admin_id || "";
                        let ID = adminID?adminID+" | "+adminName:adminName?adminName:'';
                        let remark = entry.remark || "";
    
                        transactionTableBody.append(
                            `<tr>
                                <td>${timestamp}</td>
                                <td>${amount}</td>
                                <td>${status}</td>
                                <td>${method}</td>
                                <td>${bookingId}</td>
                                <td>${ID}</td>
                                <td>${remark}</td>
                            </tr>`
                        );
                    }
                    $("#creditError").hide();
                    $("#transactionLogs").show();
                    $("#userDetails").show();
                } else {
                    creditError.text("There are no such transactions.").show();
                    $("#transactionLogs").hide();
                }
            },
            error: function(xhr, status, error) {
                creditError.text("Please, enter valid mobile number.").show();
                $("#transactionLogs").hide();
                console.log("Error:", xhr.responseText);  // Also log it to the console for detailed inspection
                console.log("Status:", status);  // Log the status for debugging
                console.log("Error:", error);  // Log the error for debugging
            }
        });
    });

    $("#getDeleteLog").click(function() {
        let mobileNumber = $("#deleteNumber").val();
        let deleteError = $("#deleteError");
        
        if (mobileNumber.length !== 10 || !/^\d{10}$/.test(mobileNumber)) {
            deleteError.text("Please enter a valid 10-digit mobile number.").show();
            $("#deleteChangeResult").hide();
            return;
        }
        let data = new FormData();
        data.append("mobile", mobileNumber);
        $.ajax({
            url: "/api/delete_log",
            type: 'POST',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {
                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        deleteError.text("Unfortunately, your session has expired. Please login again.").show();
                        $("#deleteChangeResult").hide();
                        window.location = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            
            success: function(response) {
                if (response.success === 1) {
                    let lastEntry = response.data[response.data.length - 1];
                    $("#changeMobile").text(lastEntry.new_mobile);
                    $("#changeTimestamp").text(lastEntry.timestamp);
                    
                    let chainTableBody = $("#dchainTableBody");
                    chainTableBody.empty();
                    const reasonDictionary = {
                        0: "Have own driver",
                        1: "Notifications issue",
                        2: "Facing issues",
                        3: "Creating new account",
                        4: "Mobile number changed",
                        5: "No longer needed",
                        6: "Found another app",
                        7: "Other",
                        127: "Admin delete"
                    };
                    
                    response.data.forEach(function(entry) {
                        let reason = reasonDictionary[entry.reason] || "Unknown";
                        let row = `<tr>
                            <td>${entry.userid}</td>
                            <td>${entry.name}</td>
                            <td>${entry.new_mobile}</td>
                            <td>${reason}</td>
                            <td>${entry.timestamp}</td>
                        </tr>`;
                        chainTableBody.append(row);
                    });
                    $("#deleteError").hide();
                    $("#deleteChangeResult").show();
                } else {
                    deleteError.text("No logs found for the given mobile number.").show();
                    $("#deleteChangeResult").hide();
                }
            },
            error: function(xhr, status, error) {
                deleteError.text("An error occurred while fetching the mobile log").show();
                $("#deleteChangeResult").hide();
            }
        });
    });

    $("#getMobileLog").click(function() {
        let mobileNumber = $("#mobileNumber").val();
        let mobileError = $("#mobileError");
        
        if (mobileNumber.length !== 10 || !/^\d{10}$/.test(mobileNumber)) {
            mobileError.text("Please enter a valid 10-digit mobile number.");
            mobileError.show();
            $("#mobileChangeResult").hide();
            $("#multipleUserIds").hide();
            return;
        } else {
            mobileError.hide();
        }
        data = new FormData();
        data.append("mobile", mobileNumber);
    
        $.ajax({
            url: "/api/admin/ph_change/log",
            type: 'POST',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {
                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                console.log('in getmobile success');
                $("#mobileChangeResult").hide();
                $("#multipleUserIds").hide();
    
                if (response.success === 1) {
                    if (Array.isArray(response.data) && response.data.length === 0) {
                        mobileError.text("No logs found for the given mobile number.");
                        mobileError.show();
                        $("#mobileChangeResult").hide();
                        $("#multipleUserIds").hide();
                    } else if (response.ids) {
                        let userIdSelect = $("#userIdSelect");
                        userIdSelect.empty();
                        response.ids.forEach(function(id) {
                            userIdSelect.append(new Option(id, id));
                        });
                        $("#multipleUserIds").show();
                    } else {
                        console.log(response);
                        displayLogs(response);
                    }
                } else {
                    mobileError.text(response.message || 'An error occurred while fetching the user logs.');
                    mobileError.show();
                }
            },
            error: function(xhr) {
                mobileError.text("An error occurred while fetching the mobile log: " + xhr.responseText);
                mobileError.show();
            }
        });
    });
    
    $("#getUserLogs").click(function() {
        let userId = $("#userIdSelect").val();
        let mobileNumber = $("#mobileNumber").val();
        let mobileError = $("#mobileError");
        
        $.ajax({
            url: "/api/admin/ph_change/user_logs",
            type: 'POST',
            data: JSON.stringify({ user_id: userId, mobile: mobileNumber }),
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {
                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: "application/json",
            processData: false,
            success: function(response) {
                console.log("in getusers success");
                if (response.success === 1) {
                    displayLogs(response);
                } else {
                    mobileError.text(response.message || 'No user in database with this ID.');
                    mobileError.show();
                    $("#mobileChangeResult").hide();
                }
            },
            error: function(xhr, status, error) {
                mobileError.text("An error occurred while fetching the user logs: " + xhr.responseText);
                mobileError.show();
            }
        });
    });

    function displayLogs(response) {
        console.log("in display logs");
        $("#changeNumber").text('Last Change: '+response.change);
        $("#changeNumberStatus").text('Status: '+response.change_status);
        $("#changeNumberName").text('Name: '+response.name);
        $("#changeNumberID").text('ID: '+response.user);
        $("#changeNumberEmail").text('Email: '+response.email);
        let chainTableBody = $("#mchainTableBody");
        chainTableBody.empty();
        
        response.data.forEach(function(entry) {
            let mobile = Object.keys(entry)[0];
            let newMobile = entry[mobile];
            let timestamp = entry.timestamp;
    
            chainTableBody.append(
                `<tr>
                    <td>${mobile}</td>
                    <td>${newMobile}</td>
                    <td>${timestamp}</td>
                </tr>`
            );
        });
        $("#mobileError").hide();
        $("#mobileChangeResult").show();
    }

    $("#phChange_searchByPhone").click(function() {
        $("#phoneChangeForm").addClass("collapse");
        var mobile = $("#phChange_currPhoneNum").val().trim();
        var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");           //regex for phone validation
        var validData = true;

        //phone validation
            if (!mobile || mobile === "") {
                $("#phChange_currPhoneNum").css('border','2px solid red');          //error indication
                //error popover
                $("#phChange_currPhoneNum").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#phChange_currPhoneNum').data('bs.popover').options.content = "This field cannot be blank";
                validData = false;
            }

            else if (!phoneRegex.test(mobile)) {
                $("#phChange_currPhoneNum").css('border','2px solid red');          //error indication
                //error popover
                $("#phChange_currPhoneNum").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#phChange_currPhoneNum').data('bs.popover').options.content = "Enter correct mobile number without country-code";
                validData = false;
            }

            else {
                $("#phChange_currPhoneNum").popover('destroy');
                validData = true;
            }

            if(validData) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/slack/usermobile',
                    beforeSend: function(request) {
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                        if (refresh_token) {

                            if (checkRefresh(csrf_token, refresh_token) == false) {
                                alert("Unfortunately, your session has expired. Please login again");
                                window.location  = "/adminLogin";
                            }
                        }
                        request.setRequestHeader('X-CSRF-Token', csrf_token);
                     },
                    data: {
                        text: mobile,
                        token: 'EUaoX6gOODTpCZESyT0Sbj7B',
                        team_id: 'TKJ1YUJKB'
                    },

                    dataType: "json",
                    success: function(response) {
                        var responseInfo =  response.text.split("\n");
                        if(!response.text || response.text === "" || responseInfo.length <= 1) {
                            //change background
                            $("#infoModal").find(".modal-header").css('background','gold');
                            //change text
                            $("#infoModal").find(".modal-title").html(response.text);
                            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                            setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
                        }
                        else {
                            var responseName = responseInfo[0].split(":")[1].replace("*","").trim();
                            var responseContactInfo = responseInfo[1].split(":")[1].replace("*","").trim();
                            $("#phChange_userName").text(responseName);
                            $("#phChange_currPhDisp").text(responseContactInfo);
                            $("#phChange_userID").text(responseInfo[0].split(":")[0].replace("*","").replace("#", "").trim());
                            $("#phoneChangeForm").removeClass("collapse");
                        }

                    },
                    error: function(e) {
                        $("#phChange_currPhoneNum").css('border','2px solid red');          //error indication
                        //error popover
                        $("#phChange_currPhoneNum").popover({
                            placement: "bottom",
                            trigger: "hover"
                        });
                        $('#phChange_currPhoneNum').data('bs.popover').options.content = "Search Failed! Ensure phone number is correct";
                    }
                });
            }
    });
    $("#phChange_confPhChange").click(function() {
        var prevMob = $("#phChange_userName").text().trim();
        var mobile = $("#phChange_newPhoneNum").val().trim();
        var uID = parseInt($("#phChange_userID").text().trim());
        var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");           //regex for phone validation
        var UIDRegex = new RegExp("^[1-9]{1,1}[0-9]*$")             //regex for user ID validation
        var validData = true;

        //phone validation
            if (!mobile || mobile === "") {
                $("#phChange_newPhoneNum").css('border','2px solid red');           //error indication
                //error popover
                $("#phChange_newPhoneNum").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#phChange_newPhoneNum').data('bs.popover').options.content = "This field cannot be blank";
                validData = false;
                return;
            }

            else if (!phoneRegex.test(mobile)) {
                $("#phChange_newPhoneNum").css('border','2px solid red');           //error indication
                //error popover
                $("#phChange_newPhoneNum").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#phChange_newPhoneNum').data('bs.popover').options.content = "Enter correct mobile number without country-code";
                validData = false;
                return;
            }

            else if(mobile === prevMob) {
                $("#phChange_newPhoneNum").css('border','2px solid red');           //error indication
                //error popover
                $("#phChange_newPhoneNum").popover({
                    placement: "bottom",
                    trigger: "hover"
                });
                $('#phChange_newPhoneNum').data('bs.popover').options.content = "Cannot be same as existing phone number";
                validData = false;
                return;
            }

            else {
                $("#phChange_newPhoneNum").popover('destroy');
                validData = true;
            }

            // UID validation
            if (!uID || uID === "" || !UIDRegex.test(uID)) {
                //change background
                $("#infoModal").find(".modal-header").css('background','lightsalmon');
                //change text
                $("#infoModal").find(".modal-title").html("Invalid User ID!");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 900);
                validData = false;
            }

            else {
                validData = true;
            }

            if(validData) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/slack/ph_change',
                    beforeSend: function(request) {
                        var c = getCookie();
                        var csrf_token = c['csrf_access_token'];
                        var refresh_token = c['csrf_refresh_token'];
                        if (refresh_token) {

                            if (checkRefresh(csrf_token, refresh_token) == false) {
                                alert("Unfortunately, your session has expired. Please login again");
                                window.location  = "/adminLogin";
                            }
                        }
                        request.setRequestHeader('X-CSRF-Token', csrf_token);
                     },
                    data: {
                        text: uID + " " + mobile,
                        token: 'EUaoX6gOODTpCZESyT0Sbj7B',
                        team_id: 'TKJ1YUJKB'
                    },

                    dataType: "json",
                    success: function(response) {
                        //change background
                        $("#infoModal").find(".modal-header").css('background','aqua');
                        //change text
                        $("#infoModal").find(".modal-title").html("Number successfully changed from - " + prevMob + " to " + mobile);
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1400);
                        $("#phoneChangeForm").addClass("collapse");
                        $("#phChange_newPhoneNum").val("");
                        $("#phChange_currPhoneNum").val("");
                        $("#phChange_userName").text("");
                        $("#phChange_currPhDisp").text("");
                        $("#phChange_userID").text("");
                    },
                    error: function(e) {
                        $("#phChange_newPhoneNum").css('border','2px solid red');           //error indication
                        //error popover
                        $("#phChange_newPhoneNum").popover({
                            placement: "bottom",
                            trigger: "hover"
                        });
                        $('#phChange_newPhoneNum').data('bs.popover').options.content = "Phone Change Failed!";
                        //change background
                        $("#infoModal").find(".modal-header").css('background','lightsalmon');
                        //change text
                        $("#infoModal").find(".modal-title").html("Phone Change Failed!");
                        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1400);
                    }
                });
            }
    });

    $("body").delegate(".cust-link",'click',function() {
        user_id = $(this).attr("id");
        data = new FormData();
        data.append("user_id", user_id);
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/user/info',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    // Clear table
                    $("#cust-trip-list").html('')

                    // Fill metadata
                    $("#ct-name").html(response['user_name']);
                    $("#ct-mob").html(parseInt(response['user_mobile']));
                    $("#ct-completed").html(parseInt(response['user_completed']));
                    $("#ct-canc-unalloc").html(parseInt(response['user_cancelled_unalloc']));
                    $("#ct-canc-alloc").html(parseInt(response['user_cancelled_alloc']));
                    $("#ct-canc-d4m").html(parseInt(response['user_d4m_cancelled']));
                    $("#ct-total-trip").html(parseInt(response['user_total']));

                    // Now fill trip iter info
                    trip_arr = response['trip_list']
                    for (i = 0; i < trip_arr.length; ++i) {
                        var id = trip_arr[i]["id"];
                        var trip_date = trip_arr[i]["trip_date"];
                        var dname = trip_arr[i]["driver_name"];
                        var rating = parseInt(trip_arr[i]["user_rating"]);
                        var status = trip_arr[i]["trip_status"];
                        $("#cust-trip-list").append(
                            '<tr>'+
                            '<td>'+id+ '</td>'+
                            '<td>'+trip_date+ '</td>'+
                            '<td>'+dname+ '</td>' +
                            '<td>'+rating+'</td>'+
                            '<td>'+status+'</td>'+
                            '</tr>'
                        );
                        $("#customerTripModal").modal('show');
                    }
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });

    $("body").delegate(".driv-link",'click',function() {
        driver_id = $(this).attr("id");
        data = new FormData();
        data.append("driver_id", driver_id);
        $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/driver/info',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    // Clear table
                    $("#driv-trip-list").html('')

                    // Fill metadata
                    $("#dt-name").html(response['driver_name']);
                    $("#dt-mob").html(parseInt(response['driver_mobile']));
                    $("#dt-completed").html(parseInt(response['driver_completed']));
                    $("#dt-canc-alloc").html(parseInt(response['driver_cancelled_alloc']));
                    $("#dt-canc-d4m").html(parseInt(response['driver_d4m_cancelled']));
                    $("#dt-total-trip").html(parseInt(response['driver_total']));
                    $("#dt-total-b2b").html(parseInt(response['driver_total_b2b']));
                    $("#dt-total-b2c").html(parseInt(response['driver_total_b2c']));

                    // Now fill trip iter info
                    trip_arr = response['trip_list']
                    for (i = 0; i < trip_arr.length; ++i) {
                        var id = trip_arr[i]["id"];
                        var trip_date = trip_arr[i]["trip_date"];
                        var uname = trip_arr[i]["user_name"];
                        var rating = parseInt(trip_arr[i]["user_rating"]);
                        var status = trip_arr[i]["trip_status"];
                        $("#driv-trip-list").append(
                            '<tr>'+
                            '<td>'+id+ '</td>'+
                            '<td>'+trip_date+ '</td>'+
                            '<td>'+uname+ '</td>' +
                            '<td>'+rating+'</td>'+
                            '<td>'+status+'</td>'+
                            '</tr>'
                        );
                        $("#driverTripModal").modal('show');
                    }
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
    });
});

function getCategoryStr(booking_type) {
    if (C24_BOOKING.includes(booking_type)) {
        bookingCategory = "C24";
    } else if (booking_type == REVV_BOOKING) {
        bookingCategory = "Revv";
    } else if (ZOOM_BOOKING.includes(booking_type)) {
        bookingCategory = "Zoomcar";
    } else if (booking_type == GUJRAL_BOOKING) {
        bookingCategory = "Gujral";
    } else if (OLX_BOOKING.includes(booking_type)) {
        bookingCategory = "OLX";
    } else if (CARDEKHO_BOOKING.includes(booking_type)) {
        bookingCategory = "Cardekho";
    } else if (BHANDARI_BOOKING.includes(booking_type)) {
        bookingCategory = "Bhandari";
    } else if (MAHINDRA_BOOKING.includes(booking_type)) {
        bookingCategory = "Mahindra";
    } else if (REVV_V2_BOOKING.includes(booking_type)) {
        bookingCategory = "RevvV2";
    } else if (SPINNY_BOOKING.includes(booking_type)) {
        bookingCategory = "Spinny";
    } else if (PRIDEHONDA_BOOKING.includes(booking_type)) {
        bookingCategory = "PrideHonda";
    } else {
        bookingCategory = "Customer";
    }
    return bookingCategory;
}

function resolveBookingCategory() {
    if (window.location.hash == "#b2c_bookings") {
          return "Customer";
    } else if (window.location.hash == "#b2b_c24_bookings") {
          return "C24";
    } else if (window.location.hash == "#b2b_revv_bookings") {
          return "Revv";
    } else if (window.location.hash == "#b2b_gujral_bookings") {
          return "Gujral";
    } else if (window.location.hash == "#b2b_olx_bookings") {
          return "olx";
    } else if (window.location.hash == "#b2b_zoomcar_bookings") {
          return "Zoomcar";
    } else if (window.location.hash == "#b2b_cardekho_bookings") {
          return "Cardekho";
    } else if (window.location.hash == "#b2b_bhandari_bookings") {
          return "Bhandari";
    } else if (window.location.hash == "#b2b_mahindra_bookings") {
          return "Mahindra";
    } else if (window.location.hash == "#b2b_revv_v2_bookings") {
          return "RevvV2";
    } else if (window.location.hash == "#b2b_spinny_bookings") {
        return "Spinny";
    } else if (window.location.hash == "#b2b_pridehonda_bookings") {
        return "PrideHonda";
    } else return "Customer";
}

function getCookie() {
    cookieSplit = document.cookie.split("; ");
    var cookieObject = {};
    cookieSplit.forEach( function(value, index) {
       var splitResult = value.split("=");
       cookieObject[splitResult[0]] = splitResult[1];
    });
    return cookieObject;
}

function searchDriver() {
    var data=new FormData();
    $.ajax({
             type:"POST",
             url: window.location.protocol + '//' + window.location.host + '/api/admin_search',
             dataType:"json",
             beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {
                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = window.location.protocol + '//' + window.location.host + "/api/register_cust";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
             processData:false,
             contentType:false,
             success: function (resp) {
                if (resp.driver_id == -1) {
                    alert("No drivers to approve!");
                    //do something to UI!
                } else {
                    $("#driver").text(resp.driver_id);
                    $("#driverName").text(resp.driver_name);
                    $("#driverLicense").text(resp.driver_license);
                    $("#driver_pic").attr('src',  'https://storage.drivers4me.com/static/' + resp.driver_pic);
                    $("#driver_lic").attr('src',  'https://storage.drivers4me.com/static/' + resp.driver_license_doc);
                }
             }
    });
 }

function range(size, startAt = 0) {
    return [...Array(size).keys()].map(i => i + startAt);
}

function checkRefresh(csrf_token, refresh_token) {
    /*var resp = false;
    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/token/verify',
        beforeSend: function(request) {
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        async: false,
        success: function(s) {
            if (s.success != true) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                    beforeSend: function(request) {
                        request.setRequestHeader('X-CSRF-Token', refresh_token);
                    },
                    async: false,
                    success: function(sr) {
                        if (sr.refresh != true) resp =  false;
                        else resp = true;
                    },
                    error: function(er) {
                        resp =  false;
                    }
                });
            } else {
                resp =  true;
            }
        },
        error: function(e) {
            if (e.status == 401) {
                $.ajax({
                    type: "POST",
                    url: window.location.protocol + '//' + window.location.host + '/token/refresh',
                    beforeSend: function(request) {
                        request.setRequestHeader('X-CSRF-Token', refresh_token);
                    },
                    async: false,
                    success: function(sr) {
                        if (sr.refresh != true) resp = false;
                        else resp =  true;
                    },
                    error: function(er) {
                        resp = false;
                    }
                });
            } else {
                resp =  false;
            }
        }
    });*/
    return;
}

function getVehicleType(typeNo) {
    switch (typeNo) {
        case 0:
            return 'HB';
            break;
        case 1:
            return 'Sedan';
            break;
        case 2:
            return 'SUV';
            break;
        case 3:
            return 'Luxury';
            break;
        case 4:
            return 'HB (Auto)';
            break;
        case 5:
            return 'Sedan (Auto)';
            break;
        case 6:
            return 'SUV (Auto)';
            break;
        case 7:
            return 'Luxury (Auto)';
            break;
        default:
            return 'Unknown!';
    }
}

// driver list loader
function loadDriversList(selector, location, permaData, region=-1) {
    if (selector == "dues-driver-list") {
        dues = 1;
    } else {
        dues = 0;
    }
    var needLocation = parseInt(location);
    data = new FormData();
    data.append('location', needLocation);
    data.append('region', region);
    data.append('dues', dues);
    var url = window.location.protocol + '//' + window.location.host + '/api/admin/driver_list';
    // if (permaData) url = flask_util.url_for('admin.perm_drivers');
    window.driverSuggestions = [];
    $.ajax({
            type:"POST",
            url: url,
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {

                    var driver_count = response['data'].length;
                    window.estimates = 0;
                    //clear table
                    $("#"+selector).html('');

                    for (i=0;i<driver_count;i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['data'][i]);
                        if (selector == 'alloc-driver-list' && this_entry.approved == -2) {
                            continue;
                        } else if (selector == 'dues-driver-list' && this_entry.due == 0) {
                            continue;
                        }
                        //get driver name and contact
                        window.driver = {
                            "id" : this_entry.driver_id,
                            "name" : this_entry.fname + " " + this_entry.lname,
                            "mobile" : this_entry.mobile
                        };
                        window.driverSuggestions.push(window.driver["name"]);
                        //get driver licence
                        window.licence = {
                            "num" : this_entry.licence
                        };
                        window.due = this_entry.due
                        //var address = "N.A."
                        if(needLocation == 1) {
                            //get home lat long
                            var latVal = this_entry.lat;
                            var longVal = this_entry.lng;
                            getLatLong(latVal, longVal, this_entry.addr, 0);
                        }

                        //add entry as per availability
                        if (!permaData)
                            addDriver(selector, this_entry.available, needLocation, this_entry.approved, this_entry.ontrip, this_entry.rating, this_entry.inf_rating, this_entry.score, this_entry.perma, this_entry.verif_phone, this_entry.lic_doc_front, this_entry.lic_doc_back, this_entry.id_doc_front
, this_entry.id_doc_back, this_entry.bank_doc, this_entry.pic, this_entry.bank_ac_number, this_entry.bank_ifsc_number, this_entry.region, this_entry.color);
                        else
                            addPermaDriver(selector, this_entry.available, needLocation, this_entry.approved, this_entry.ontrip, this_entry.rating, this_entry.inf_rating, this_entry.score, this_entry.perma,
                                        this_entry.base, this_entry.ta, this_entry.ot, this_entry.alloc, this_entry.pic, this_entry.lic_doc_front, this_entry.region);
                    }
                    if(needLocation == 1) {
                        //conditional display of location column
                        $("#"+selector).parent().find(".driver-base-loc").attr('class', 'driver-base-loc');
                    }
                }
                /*$("#driverListSearch").autocomplete({
                    source: window.driverSuggestions,
                    appendTo: "#driverSuggestions",
                    minLength: 3
                });*/
                //$("#driverSuggestions").find("ul").addClass("dropdown-menu");
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
}

function loadAllocateDriversList(selector, region=-1) {
    data = new FormData();
    data.append('region', region);
    var url = window.location.protocol + '//' + window.location.host + '/api/admin/driver_list_allocate';
    window.driverSuggestions = [];
    $("#"+selector).html('');
    $.ajax({
            type:"POST",
            url: url,
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {

                    var driver_count = response['data'].length;
                    window.estimates = 0;
                    //clear table
                    for (i=0;i<driver_count;i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['data'][i]);
                        if (selector == 'alloc-driver-list' && this_entry.approved == -2) {
                            continue;
                        } else if (selector == 'dues-driver-list' && this_entry.due == 0) {
                            continue;
                        }
                        //get driver name and contact
                        window.driver = {
                            "id" : this_entry.driver_id,
                            "name" : this_entry.fname + " " + this_entry.lname,
                            "mobile" : this_entry.mobile
                        };
                        window.driverSuggestions.push(window.driver["name"]);
                        //get driver licence
                        window.licence = {
                            "num" : this_entry.licence
                        };
                        window.due = this_entry.due
                        //var address = "N.A."

                        //add entry as per availability
                        addDriver(selector, this_entry.available, 0, this_entry.approved,
                                  this_entry.ontrip, this_entry.rating, this_entry.inf_rating, this_entry.score,
                                  this_entry.perma, "", "", "", "", "", "", "", "", "",
                                  this_entry.region, this_entry.color);
                    }
                    window.lastAllocateRegion = region
                }

                /*$("#driverListSearch").autocomplete({
                    source: window.driverSuggestions,
                    appendTo: "#driverSuggestions",
                    minLength: 3
                });*/
                //$("#driverSuggestions").find("ul").addClass("dropdown-menu");
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
}

// pending driver list loader
function loadPendingList(selector, book_id) {
    data = new FormData();
    data.append("booking_id", book_id);
    $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/show_book_pending',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {

                    var pending_count = response['data'].length;
                    //clear table
                    $("#"+selector).html('');

                    for (i=0; i<pending_count; i++) {
                        //process each entry
                        var this_entry = JSON.parse(response['data'][i]);
                        //get driver name and contact
                        window.driver = {
                            "id" : this_entry.driver_id,
                            "name" : this_entry.dname,
                            "mobile" : this_entry.dmob
                        };
                        //get driver licence
                        window.estimateFare = this_entry.estimate;
                        //add entry as per availability
                        addPendingDriver(selector, this_entry.suppressed);
                        window.pendingSuggestions.push(this_entry.dname);
                    }
                    $("#pendingListModal").modal('show');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
}

function showLog(selector, book_id) {
    data = new FormData();
    data.append("book_id", book_id);
    $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/book_log',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    var log_count = response['data'].length;
                    //clear table
                    $("#"+selector).html('');

                    for (i=log_count - 1; i>= 0; i--) {
                        //process each entry
                        var this_entry = response['data'][i];
                        //get driver name and contact
                        window.booklog = {
                            "type": this_entry.type,
                            "driver_name": this_entry.driver_name,
                            "user_name": this_entry.user_name,
                            "cancel_src": this_entry.cancel_src,
                            "fine_driver": this_entry.fine_driver,
                            "fine_user": this_entry.fine_user,
                            "cancel_source": this_entry.cancel_source,
                            "bookid":book_id,
                            "reason_number": this_entry.reason,
                            "reason_detail": this_entry.reason_detail,
                            "timestamp": this_entry.timestamp,
                            "lat": this_entry.lat,
                            "lng": this_entry.lng,
                            "fine": this_entry.fine,
                            "alloc_user": this_entry.alloc_user,
                            "cancel_reversed":this_entry.cancel_reversed
                        };
                        //get driver licence
                        window.estimateFare = this_entry.estimate;
                        //add entry as per availability
                        addBookingLog(selector, this_entry);
                        window.bookingLog.push(this_entry.dname);
                    }
                    $("#bookLogModal").modal('show');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
}

function showDriverDueLog(selector, mobile) {
    data = new FormData();
    data.append("mobile", mobile);
    $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/driver_due_log',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {
                    var log_count = response['data'].length;
                    //clear table
                    $("#"+selector).html('');

                    for (i=0; i<log_count; i++) {
                        //process each entry
                        var this_entry = response['data'][i];
                        //get driver name and contact
                        window.driverduelog = {
                            "dname": this_entry.driver_name,
                            "timestamp": this_entry.timestamp,
                            "paid" : this_entry.paid,
                            "source" : this_entry.source_due,
                            "remarks" : this_entry.remarks
                        };
                        addDriverDueLog(selector);
                    }
                    $("#driverDueLogModal").modal('show');
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
}

function addPermaDriver(selector, available, location, approval, ontrip, rating, inf_rating, score, perma, base, ta, ot, alloc, profilePicFileName, licDocName, region) {
    var allocSel0, allocSel1, allocSel2, allocSel3, allocSel4, allocSel5, allocSel6, allocSel7;
    if (alloc == 0) {
        allocSel0 = "selected"
        allocSel1 = allocSel2 = allocSel3 = allocSel4 = allocSel5 = allocSel6 = allocSel7 = "";
    } else if (alloc == 1) {
        allocSel1 = "selected"
        allocSel0 = allocSel2 = allocSel3 = allocSel4 = allocSel5 = allocSel6 = allocSel7 = "";
    } else if (alloc == 2) {
        allocSel2 = "selected"
        allocSel0 = allocSel1 = allocSel3 = allocSel4 = allocSel5 = allocSel6 = allocSel7 = "";
    } else if (alloc == 3) {
        allocSel3 = "selected"
        allocSel0 = allocSel1 = allocSel2 = allocSel4 = allocSel5 = allocSel6 = allocSel7 = "";
    }else if (alloc == 4) {
        allocSel4 = "selected"
        allocSel0 = allocSel1 = allocSel2 = allocSel3 = allocSel5 = allocSel6 = allocSel7 = "";
    }else if (alloc == 5) {
        allocSel5 = "selected"
        allocSel0 = allocSel1 = allocSel2 = allocSel3 = allocSel4 = allocSel6 = allocSel7 = "";
    }else if (alloc == 6) {
        allocSel6 = "selected"
        allocSel0 = allocSel1 = allocSel2 = allocSel3 = allocSel5 = allocSel6 = allocSel7 = "";
    }
    defaultPicture = "\/assets\/images\/driverProfileIcon.png";
    if(selector == "perma-drivers-list" || selector.split(/-(.+)/)[1] == "perma-drivers-list") {
        $("#"+selector).append('<tr class="driver-list-item" id="permaDriver'+ window.driver['id'] +'">'+
            '<td><span class="driver-id">'+window.driver['id'] + '</span>' +
                '&emsp;<label class="switch">' +
                    '<input class="availability" type="checkbox" checked>' +
                    '<span class="slider round">available</span>' +
                '</label>' +
                '<hr class="narrow-line">' +
                '<div class="form-group col-lg-8 col-md-8 col-sm-8 col-xs-12">' +
                    '<label for="approval-selection">Approval Status:</label>' +
                    '<select class="form-control approval-selection">' +
                        '<option value="1">1:Approved</option>' +
                        '<option value="-1">-1:Verification Pending</option>' +
                        '<option value="-2">-2:Unapproved</option>' +
                        '<option value="-3">-3:Inactive</option>' +
                    '</select>' +
                '</div>' +
            '<span class="driver-action glyphicon glyphicon-floppy-disk"></span></td>' +
            '<td>'+
                '<div class="thumbnail">'+
                    '<img class="driver-profile" src="'+ profilePicFileName +'" alt="Picture Not Found">'+
                    '<input type="hidden" class="driver-lic-doc" name="custId" value="'+ licDocName +'">'+
                    '<div class="caption">'+
                        '<p class="driver-name">'+ window.driver['name'] + '</p>'+
                    '</div>'+
                '</div>'+
                '<div class="dropdown"><br>'+
                '<button class="btn btn-xs btn-primary dropdown-toggle driver-tools" type="button" data-toggle="dropdown">'+
                '&emsp;<i class="fa fa-wrench" aria-hidden="true"></i>'+
                    '&emsp;Tools&emsp;<span class="caret"></span></button>'+
                '<ul class="dropdown-menu">'+
                    '<li class=" tiny-padding"><button class="btn btn-xs btn-primary set-idle" type="button" data-toggle="tooltip" title="Set a date on which this individual is idle">'+
                        '<i class="fa fa-pause"></i>&emsp;Set Idle'+
                    '</button></li>'+
                    '<li class=" tiny-padding"><button class="btn btn-xs btn-primary add-fine" type="button" data-toggle="tooltip" title="Set a fine/penalty amount">'+
                        '<i class="fa fa-legal"></i>&emsp;Add Fine'+
                    '</button></li>'+
                    '<li class=" tiny-padding"><button class="btn btn-xs btn-primary add-advance" type="button" data-toggle="tooltip" title="Record an advance payment">'+
                        '<i class="fa fa-money"></i>&emsp;Advance'+
                    '</button></li>'+
                '</ul>'+
                '</div> '+
            '</td>' +
            '<td class="driver-contact">'+window.driver["mobile"]+'</td>'+
            '<td class="driver-rating">'+rating + ' (' + inf_rating + ')' + ' (' + inf_rating + ')</td>' +
            '<td class="break-long-text"> Licence Number :<br><span class="driver-licence">'+window.licence['num']+'</span></td>'+
            '<td>₹'+base + '</td>' +
            '<td>₹'+ta + '</td>' +
            '<td>₹'+ot + '</td>' +
            '<td><select class="driver-cur-alloc"><option value="0" ' + allocSel0 +'>Customer</option>' +
            '<option value="1" ' + allocSel1 +'>C24</option>' +
            '<option value="2" ' + allocSel2 +'>Revv</option>' +
            '<option value="3" ' + allocSel3 +'>Zoomcar</option>' +
            '<option value="4" ' + allocSel4 +'>Gujral</option>' +
            '<option value="5" ' + allocSel5 +'>OLX</option>' +
            '<option value="6" ' + allocSel6 +'>Cardekho</option>' +
            '<option value="7" ' + allocSel7 +'>Bhandari</option>' +
            '<option value="8" ' + allocSel7 +'>Mahindra</option>' +
            '<option value="9" ' + allocSel7 +'>RevvV2</option>' +
            '<option value="10" ' + allocSel7 +'>Spinny</option>' +
            '<option value="11" ' + allocSel7 +'>PrideHonda</option>' +
            '</select></td>' +
            '</tr>');
            $("#" + selector).find('tr').last().find(".driver-profile").attr("src", "https://storage.drivers4me.com/static/uploads/" + profilePicFileName);
        if(available != '1') $("#" + selector).find('tr').last().find(".availability").prop('checked', false);
        $("#" + selector).find('tr').last().find(".approval-selection option[value="+parseInt(approval)+"]").prop('selected', true);
    }
    else {
        $("#"+selector).append('<tr>'+
            '<td>'+window.driver['id'] +
            '<td>'+window.driver['name'] + '</td>' +
            '<td>'+window.driver["mobile"]+'</td>'+
            '<td class="break-long-text"> Licence Number :<br>'+window.licence['num']+'</td>'+
            '</tr>');
    }

    if(location == 1) {
        $("#"+selector).find('tr').last().append(
            '<td class="location-section" style="max-width: 200px;">' +
                    '<span class="source collapse">Source:&nbsp;' +
                        '<span class="label latitude">'+window.source["latitude"]+'</span>' +
                        '<span class="label longitude">'+window.source["longitude"]+'</span>' +
                    '</span>' +
                '<button type="button" class="btn mapLink source-map">' +
                    '<span class="glyphicon glyphicon-map-marker"></span>&nbsp;Base Location' +
                '</button><button type="button" class="btn curLoc" data="' + window.driver['id'] + '">' +
                    '<span class="glyphicon glyphicon-map-marker"></span>&nbsp;Current Location' +
                '</button><input class="base-loc" value="' + window.source['address'] + '">' +
            '</td>'
            );
    }

    //apply color classes
    var colored = false;
    if(available != '1') {
        if(approval != '1') {
            if(location == 1) {
                $("#" + selector).find('tr').last().addClass('driver-unavailable-unapproved').addClass('collapse');
            }
            else $("#" + selector).find('tr').last().addClass('driver-unavailable-unapproved');
        }
        else {
            $("#" + selector).find('tr').last().addClass('driver-unavailable');
        }
        colored = true;
    }
    else if(approval != '1') {
        if(location == 1) {
                $("#" + selector).find('tr').last().addClass('driver-unapproved').addClass('collapse');
            }
        else $("#" + selector).find('tr').last().addClass('driver-unapproved');
        colored = true;
    }
    if (approval == '1' && perma == '1') {
        $("#" + selector).find('tr').last().addClass('driver-perma');
        colored = true;
    }
    if (!colored) {
        $("#" + selector).find('tr').last().addClass('driver-normal');
    }
    if(ontrip == "True" || ontrip == '1') {
        $("#" + selector).find('tr').last().addClass('booking-ongoing');
        $("#" + selector).find('tr').last().css({'background-color': 'aquamarine'});
    }
}

function addDriver(selector, available, location, approval, ontrip, rating, inf_rating, score, perma, verifPhoneNumber, licDocFront, licDocBack, idDocFront, idDocBack, bankDoc, profilePicFileName, bankAcNumber, bankIfsc, region=0, color=0) {
    //this is lsplit
    defaultPicture = "\/assets\/images\/driverProfileIcon.png";
    if(selector == "drivers-list" || selector.split(/-(.+)/)[1] == "drivers-list") {
        $("#"+selector).append('<tr class="driver-list-item">'+
            '<td><span class="driver-id">'+window.driver['id'] + '</span>' +
                '&emsp;<label class="switch">' +
                    '<input class="availability" type="checkbox" checked>' +
                    '<span class="slider round">available</span>' +
                '</label>' +
                '<hr class="narrow-line">' +
                '<div class="form-group col-lg-8 col-md-8 col-sm-8 col-xs-12">' +
                    '<label for="approval-selection">Approval Status:</label>' +
                    '<select class="form-control approval-selection">' +
                        '<option value="1">1:Approved</option>' +
                        '<option value="-1">-1:Verification Pending</option>' +
                        '<option value="-2">-2:Unapproved</option>' +
                        '<option value="-3">-3:Inactive</option>' +
                    '</select>' +
                '</div>' +
            '<span class="driver-action glyphicon glyphicon-floppy-disk"></span></td>' +
            '<td>'+
                '<div class="thumbnail">'+
                    '<img class="driver-profile" alt="Picture Not Found">'+
                    '<input type="hidden" class="driver-lic-front" name="custId" value="'+ licDocFront +'">'+
                    '<input type="hidden" class="driver-lic-back" name="custId" value="'+ licDocBack +'">'+
                    '<input type="hidden" class="driver-id-front" name="custId" value="'+ idDocFront +'">'+
                    '<input type="hidden" class="driver-id-back" name="custId" value="'+ idDocBack +'">'+
                    '<input type="hidden" class="driver-account" name="custId" value="'+ bankDoc +'">'+
                    '<input type="hidden" class="verif-phone" name="custId" value="'+ verifPhoneNumber +'">'+
                    '<input type="hidden" class="bank-account-number" name="custId" value="'+ bankAcNumber +'">'+
                    '<input type="hidden" class="bank-ifsc-number" name="custId" value="'+ bankIfsc +'">'+
                    '<div class="caption">'+
                        '<p class="driver-name">'+ window.driver['name'] + '</p>'+
                    '</div>'+
                '</div>'+
            '</td>' +
            '<td class="driver-contact">'+window.driver["mobile"]+'</td>'+
            '<td class="driver-rating">'+ rating + ' (' + inf_rating + ')' + ' (' + score + ')</td>' +
            '<td class="break-long-text"> Licence Number :<br><span class="driver-licence">'+window.licence['num']+'</span></td>'+
            '</tr>');
            $("#" + selector).find('tr').last().find(".driver-profile").attr("src", "https://storage.drivers4me.com/static/uploads/" + profilePicFileName);
        if(available != '1') $("#" + selector).find('tr').last().find(".availability").prop('checked', false);
        $("#" + selector).find('tr').last().find(".approval-selection option[value="+parseInt(approval)+"]").prop('selected', true);
    }
    else if (selector == 'dues-driver-list') {
        $("#"+selector).append('<tr>'+
            '<td>'+window.driver['id'] +
            '<td>'+window.driver['name'] + '</td>' +
            '<td>'+window.driver["mobile"]+'</td>'+
            '<td class="break-long-text"> Due :<br>'+window.due+'</td>'+
            '<td class="break-long-text">'+score+'</td>'+
            '</tr>');
    } else {
        $("#"+selector).append('<tr>'+
            '<td>'+window.driver['id'] +
            '<td>'+window.driver['name'] + '</td>' +
            '<td>'+window.driver["mobile"]+'</td>'+
            '<td class="break-long-text">'+window.licence['num']+'</td>'+
            '<td class="break-long-text">'+score+'</td>'+
            '</tr>');
    }

    if(location == 1) {
        $("#"+selector).find('tr').last().append(
            '<td class="location-section" style="max-width: 200px;">' +
                    '<span class="source collapse">Source:&nbsp;' +
                        '<span class="label latitude">'+window.source["latitude"]+'</span>' +
                        '<span class="label longitude">'+window.source["longitude"]+'</span>' +
                    '</span>' +
                '<button type="button" class="btn mapLink source-map">' +
                    '<span class="glyphicon glyphicon-map-marker"></span>&nbsp;Base Location' +
                '</button><button type="button" class="btn curLoc" data="' + window.driver['id'] + '">' +
                    '<span class="glyphicon glyphicon-map-marker"></span>&nbsp;Current Location' +
                '</button><input class="base-loc" value="' + window.source['address'] + '">' +
            '</td>'
            );
    }

    //apply color classes
    var colored = false;
    if(available != '1') {
        if(approval != '1') {
            if(location == 1) {
                $("#" + selector).find('tr').last().addClass('driver-unavailable-unapproved').addClass('collapse');
            }
            else $("#" + selector).find('tr').last().addClass('driver-unavailable-unapproved');
        }
        else {
            $("#" + selector).find('tr').last().addClass('driver-unavailable');
        }
        colored = true;
    }
    else if(approval != '1') {
        if(location == 1) {
                $("#" + selector).find('tr').last().addClass('driver-unapproved').addClass('collapse');
            }
        else $("#" + selector).find('tr').last().addClass('driver-unapproved');
        colored = true;
    }
    if (approval == '1' && perma == '1') {
        $("#" + selector).find('tr').last().addClass('driver-perma');
        colored = true;
    }
    if (!colored) {
        $("#" + selector).find('tr').last().addClass('driver-normal');
    }
    if(ontrip == "True" || ontrip == '1') {
        $("#" + selector).find('tr').last().addClass('booking-ongoing');
        $("#" + selector).find('tr').last().css({'background-color': 'aquamarine'});
    }
    if (color) {
        //console.log("Yes color");
        if (selector == "drivers-list" || selector.split(/-(.+)/)[1] == "drivers-list") {
            $("#" + selector).find('tr').last().find(".driver-name").addClass('new-driver');
            //console.log("driv list");
        } else {
            $("#" + selector).find('tr').last().children().eq(1).addClass('new-driver');
            //console.log("Alloc");
        }
    }
}

function addPendingDriver(selector, suppressed) {
    if(bookingVisibility[parseInt(suppressed)] == "SUPPRESSED") {
        $("#"+selector).append('<tr class="pending-suppressed">'+
            '<td>'+window.driver['id'] + '</td>' +
            '<td class="pending-name">'+window.driver['name'] + '</td>' +
            '<td>'+window.driver["mobile"]+'</td>'+
            '<td>'+window.estimateFare+'</td>'+
            '</tr>');
    }
    else {
        $("#"+selector).append('<tr>'+
            '<td>'+window.driver['id'] + '</td>' +
            '<td class="pending-name">'+window.driver['name'] + '</td>' +
            '<td>'+window.driver["mobile"]+'</td>'+
            '<td>'+window.estimateFare+'</td>'+
            '</tr>');
    }
}
function addBookingLog(selector, this_entry) {
    var html = '';
    html += '<tr>';
    html += '<td>' + (window.booklog.timestamp).replace("GMT", "") + '</td>';//timestamp
    html += '<td>' + (window.booklog.driver_name || "-") + '</td>';//driver name
    html += '<td>' + (window.booklog.user_name || "-") + '</td>';//user name
    html += '<td>' + (window.booklog.alloc_user || "-") + '</td>';//allocated by
    html += '<td>' + (getActionTypeText(window.booklog.type) || "-") + '</td>';//Booking or cancelled etc
    html += '<td>' + (getCancelTypeText(window.booklog.cancel_source) || "-") + '</td>';//Admin/User/driver
    html += '<td>' + (window.booklog.cancel_src || "-") + '</td>';//name of who cancelled
    html += '<td>' + ((window.booklog.fine_driver !== undefined && window.booklog.fine_driver !== null) ?
    (window.booklog.fine_driver !== 0 ? window.booklog.fine_driver : 0) : "-") + '</td>';//driver fine
    html += '<td>' + ((window.booklog.fine_user !== undefined && window.booklog.fine_user !== null) ?
    (window.booklog.fine_user !== 0 ? window.booklog.fine_user : 0) : "-") + '</td>';//user fine
    //html += '<td>' + (window.booklog.reason_number !== undefined ? window.booklog.reason_number : "-") + '</td>';
    html += '<td>' + (window.booklog.reason_detail || "-") + '</td>';//reason detail
    html += '<td>';
    if (window.booklog["lat"] == -1 || window.booklog["lng"] == -1 || window.booklog["lat"] === undefined || window.booklog["lng"] === undefined) {
        html += "-";
    } else {
        html += '<span class="source collapse">Source:&nbsp;';
        html += '<span class="label latitude">' + window.booklog["lat"] + '&deg;N</span>';
        html += '<span class="label longitude">' + window.booklog["lng"] + '&deg;E</span>';
        html += '</span>';
        html += '<button type="button" class="btn mapLink source-map">';
        html += '<span class="glyphicon glyphicon-map-marker"></span>';
        html += '</button>'
    }
    html += '</td>';
    html +='<td>';
    var cancelReasonButton = $('<button>', {
        type: 'button',
        class: 'btn btn-sm btn-primary cancel-reason-change'+this_entry.id,
        text: 'Change',
    });
    if(this_entry.id && this_entry.reason!=20) {
        html += cancelReasonButton[0].outerHTML; // Convert jQuery object to HTML string
    } else {
        html += "-";
    }
    html+='</td>';
    html += '</tr>';
    $("#"+selector).on('click', '.cancel-reason-change'+this_entry.id, function() {
        cancelreasonchange(this_entry)})
    $("#"+selector).append(html);
    if(this_entry.cancel_reversed){
        $("#"+selector).find('.cancel-reason-change'+this_entry.id).prop("disabled",true)
        $("#"+selector).find('.cancel-reason-change'+this_entry.id).hover(function() {
            $(this).text("Reversed");
        }, function() {
                $(this).text("Change");
        })
    }
}
function cancelreasonchange(this_entry) {
    //On clicking the change button this function get executed
    var update_cancel_modal = $("#update_cancel_modal");
    update_cancel_modal.find('#cancellationReasonupdate').val(this_entry.reason);
    update_cancel_modal.find('.update-cancel-id').html(this_entry.id)
    update_cancel_modal.find('.update_cancel-button').addClass("updateid"+this_entry.id);
    update_cancel_modal.modal('show');
    update_cancel_modal.find('.updateid'+this_entry.id).off('click');
    update_cancel_modal.find('.updateid'+this_entry.id).on('click', function() {
        updateCharge();
    });
};
function updateCharge() {
    //On clicking the update button this function shows updated charge
   if (reason === "-1") {
       alert("You have not selected any cancellation reason. Please select a reason to cancel the trip.");
       return;
   }
   var cancelIdSpan=$('.update-cancel-id')
   var cancelID = cancelIdSpan.text();
   var reason = document.getElementById('cancellationReasonupdate').value;
   data = new FormData();
   data.append("cancel_id",cancelID );
   data.append("new_reason", reason);
   $.ajax({
       url: window.location.protocol + '//' + window.location.host + '/api/admin/cancelupdate/charge',
       type: 'POST',
       data: data,
       beforeSend: function(request) {
           var c = getCookie();
           var csrf_token = c['csrf_access_token'];
           var refresh_token = c['csrf_refresh_token'];
           if (refresh_token) {if (checkRefresh(csrf_token, refresh_token) == false) {
            alert("Unfortunately, your session has expired. Please login again");
            window.location  = "/adminLogin";
        }
    }
    request.setRequestHeader('X-CSRF-Token', csrf_token);
 },

dataType: "json",
contentType: false,
processData: false,
success: function(data) {
    let status = data.success || -1;
    if (status === 1) {
        if (data.updatedcharge[1] === "-1") {
            alert("Please select a valid reason.");
            return;
        }
        let confirmation = confirm("Updated Customer cancellation charges will be ₹ " + data.updatedcharge[0] + ". \nUpdated Driver cancellation charges will be ₹ " + data.updatedcharge[1] + ". \nAre you sure you want to cancel?");
        if (confirmation) {
            change_cancel_reason()
        }
    }
},
error: function() {
    //Connection error...............................................................
    //change background
    $("#infoModal").find(".modal-header").css('background','gold');
    //change text
    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
    //window.location = "/adminLogin";
}
});
}
function change_cancel_reason(){
    var update_cancel_modal = $("#update_cancel_modal");
    var cancelIdSpan=$('.update-cancel-id')
    var cancelID = cancelIdSpan.text();
    var reason = document.getElementById('cancellationReasonupdate').value;
    var reason_details;
    if (reason === "0") {
        reason_details = "Change of Plans";
    } else if (reason === "1") {
        reason_details = "Driver Denied";
    } else if (reason === "2") {
        reason_details = "Favorite Driver Not Assigned";
    } else if (reason === "3") {
        reason_details = "Driver Requesting Extra Fare";
    } else if (reason === "4") {
        reason_details = "Driver Asking to Take Offline";
    } else if (reason === "5") {
        reason_details = "Selected Wrong Location";
    } else if (reason === "6") {
        reason_details = "Selected Different Service";
    } else if (reason === "7") {
        reason_details = "Booked by Mistake";
    } else if (reason === "8") {
        reason_details = "Wait Time Too Long";
    } else if (reason === "9") {
        reason_details = "Got Driver Elsewhere";
    } else if (reason === "10") {
        reason_details = "Checking Price Estimate";
    } else if (reason === "11") {
        reason_details = "Taking Too Long to Allocate";
    } else if (reason === "12") {
        reason_details = "Direct Trip";
    } else if (reason === "13") {
        reason_details = "Wrongly Taken";
    } else if (reason === "14") {
        reason_details = "Previous Trip Not Ended";
    } else if (reason === "15") {
        reason_details = "Personal Issue";
    } else if (reason === "16") {
        reason_details = "Transportation Problem";
    } else if (reason === "17") {
        reason_details = "Customer Not Responding";
    } else if (reason === "18") {
        reason_details = "Customer Asked to Cancel";
    } else if (reason === "62") {
        reason_details = "Other";
    } else {
        reason_details = "No Allocation";
    }
    data = new FormData();
    data.append("book_cancel_id", cancelID);
    data.append("new_reason", reason);
    data.append("new_reason_detail",reason_details);
    $.ajax({
    type:"POST",
    url: window.location.protocol + '//' + window.location.host + '/api/admin/change_cancellation_reason',
    data: data,
    beforeSend: function(request) {
    var c = getCookie();
    var csrf_token = c['csrf_access_token'];
    var refresh_token = c['csrf_refresh_token'];
    if (refresh_token) {
        if (checkRefresh(csrf_token, refresh_token) == false) {
            alert("Unfortunately, your session has expired. Please login again");
            window.location  = "/adminLogin";
        }
    }
    request.setRequestHeader('X-CSRF-Token', csrf_token);
    },
    dataType: "json",
    contentType: false,
    processData: false,
    success: function(response) {
    if(response['success'] == 1) {
        var bookID = window.booklog.bookid
        //calling showlog to refresh it in real time
        showLog('book-log-list', bookID);
        $("#infoModal").find(".modal-header").css('background','azure');
        //change text
        $("#infoModal").find(".modal-title").html("Changed Cancel Reason Successfully!");
        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
        setTimeout(function(){ $("#infoModal").modal('hide'); }, 1900);
        update_cancel_modal.modal('hide');
        $(this).parent().parent().parent().find('.refresh-entry').trigger('click');
    }
    else {
        //change background
        $("#infoModal").find(".modal-header").css('background','lightsalmon');
        //change text
        $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
        setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
        setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
    }
    },
    error: function() {
    //Connection error...............................................................
    //change background
    $("#infoModal").find(".modal-header").css('background','gold');
    //change text
    $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
    //window.location = "/adminLogin";
    }
    });
    }
/*function addBookingLogOld(selector, suppressed) {
    var action = getActionTypeText(window.booklog["action"]);
    var cost = window.booklog["cost"] == null ? "_" : window.booklog["cost"];
    var user = window.booklog["user"] == null ? "_" : window.booklog["user"];
    var timestamp = window.booklog['timestamp'].replace("GMT", "");
        $("#"+selector).append('<tr>'+
            '<td>'+timestamp+ '</td>' +
            '<td>'+window.booklog['driver'] + '</td>' +
            '<td>'+user+'</td>'+
            '<td>'+action+'</td>'+
            '<td>'+cost+'</td>'+
            '<td><span class="source collapse">Source:&nbsp;' +
                '<span class="label latitude">'+window.booklog["lat"]+'&deg;N</span>' +
                '<span class="label longitude">'+window.booklog["lng"]+'&deg;E</span>' +
                '</span>' +
                '<button type="button" class="btn mapLink source-map">' +
                    '<span class="glyphicon glyphicon-map-marker"></span>' +
                '</button></td>' +
            '</tr>');
}*/

function addDriverDueLog(selector) {
    var dname = window.driverduelog['dname'];
    var paid = window.driverduelog['paid'];
    var source = window.driverduelog['source'];
    var remarks = window.driverduelog['remarks'];
    var timestamp = window.driverduelog['timestamp'].replace("GMT", "");
    $("#"+selector).append('<tr>'+
        '<td>'+dname+ '</td>'+
        '<td>'+timestamp+ '</td>' +
        '<td>'+paid+'</td>'+
        '<td>'+source+'</td>'+
        '<td>'+remarks+'</td>'+
        '</tr>');
}

function displayDueData() {
    $("#driverId_due").html(window.driver['id']);
    $("#driverName_due").html(window.driver['name']);
    $("#driverPhone").html(window.driver['mobile']);
    $("#driverLicense_due").html(window.licence['num']);
    $("#amount_due").html(window.driver['due']);
    $("#curr_due").html(window.driver['due']);
    console.log(window.driver)
    $("#withdrawable_balance").html(window.driver['withdrawable']);
    $("#total_balance").html(window.driver['withdrawable'] + window.driver['wallet']);
}

//getting proper date time as per locale and timezone
function getDateTime(startdate, starttime) {
    console.log(startdate)
    if (typeof startdate == "undefined") {
        return "Unknown"
    }
    if(typeof starttime !== "undefined") {
        //double param function
        startdate = startdate.split('-').join('/');
        var pmFlag = starttime.split(':')[0] >= 12;
        starttime = pmFlag ? (parseInt(starttime.split(':')[0]) - 12) + ":" + starttime.split(':')[1] : starttime;
        //alert(1+ ""+startdate);
        var DateTimeString = startdate + " " + starttime + " ";
        try {
            DateTimeString = (pmFlag) ? (DateTimeString + "PM") : (DateTimeString + "AM");
            DateTimeString += " UTC";
            //alert(2+ ""+DateTimeString);
            var datetime = new Date(DateTimeString);
            //alert(datetime);
            return datetime;
        }
        catch(err) {
            return new Date();
        }
    }

    else {
        //single param function
        //alert("single param");
        startdate = startdate.split('-').join('/');
        var pmFlag = startdate.split(' ')[1].split(':')[0] >= 12;
        startdate = pmFlag ? startdate.split(' ')[0] + " " + (parseInt(startdate.split(' ')[1].split(':')[0]) - 12) + ":" + startdate.split(' ')[1].split(':')[1] : startdate;
        var DateTimeString = startdate.split(' ')[0] + " " + startdate.split(' ')[1] + " ";
        try {
            DateTimeString = (pmFlag) ? (DateTimeString + "PM") : (DateTimeString + "AM");
            DateTimeString += " UTC";
            //alert(3+ ""+DateTimeString);
            var datetime = new Date(DateTimeString);
            //alert(datetime);
            return datetime;
        }
        catch(err) {
            return new Date();
        }
    }

}

function getLatLong(lat, long, address, destination) {
    if(parseInt(destination) == 0) {
        //source
        window.source = {
            "latitude" : lat,
            "longitude" : long,
            "address" : address
        }
    }
    else {
        //destination
        window.destination = {
            "latitude" : lat,
            "longitude" : long,
            "address" : address
        }
    }
}

function getBookEntryInfo(this_entry) {
    //get booking id
    window.bookID = this_entry.bookId;
    //get booking start datetime
    //utc to ist
    var bookStart = getDateTime(this_entry.startdate, this_entry.starttime);
    //handle different locales in distant future
    window.booking = {
        "start" : bookStart.toLocaleString('en-GB',
                                            {   day: "numeric",
                                                month: "long",
                                                year: "numeric",
                                                hour: 'numeric',
                                                minute: 'numeric',
                                                hour12: true })
    }
    //get trip duration based on trip type
    getDuration(this_entry);
    var bookingType = getBookTypeText(this_entry.booking_type);
    //get customer details
    window.customer = {
        "id" : this_entry.cid,
        "name" : this_entry.cname,
        "mobile" : this_entry.cmob,
        "booking_type" : bookingType,
        "booking_type_id": this_entry.booking_type,
        "comment" : this_entry.comment
    };
    //get car type
    if (C24_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (OLX_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (ZOOM_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (CARDEKHO_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (BHANDARI_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (MAHINDRA_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (SPINNY_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (REVV_V2_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else if (PRIDEHONDA_BOOKING.includes(this_entry.booking_type)) {
        window.vehicleType = this_entry.car_type_no
        if (this_entry.car_type == 0)
            window.vehicleType += ' (Manual)'
        else
            window.vehicleType += ' (Automatic)'
    } else {
        window.vehicleType = getVehicleType(this_entry.car_type);
    }
    //get fare estimate
    window.estimateFare = this_entry.estimate;
    if (parseFloat(this_entry.due) > 0.02) {
        window.due = 'Due: ₹' + this_entry.due
        if (this_entry.payment_type == 1) {
            window.due += " (D4M Cr.)"
        }
    }
    else {
        window.due = "";
        if (this_entry.payment_type == 1) {
            window.due = "D4M Cr."
        }
    }

    //get lat long of source
    var latVal = this_entry.lat;
    var longVal = this_entry.lng;
    var locName = this_entry.loc_name;
    getLatLong(latVal, longVal, locName, 0); //source
    //get lat long of destination (conditional)
    if(hasDestination(this_entry.booking_type)) {
        var latVal = this_entry.destlat;
        var longVal = this_entry.destlng;
        var locName = this_entry.destname;
        getLatLong(latVal, longVal, locName, 1); //destination
    }
    //insert line-breaks
    //locationNameLineSplit(5);
    //get trip details if available
    if(this_entry.tripId.toString() != "") {
        //trip id
        window.tripID = this_entry.tripId;
        //get trip start datetime
        //utc to ist
        if (this_entry.trip_start) {
            var tripStart = getDateTime(this_entry.trip_start);
            var tripStartStr = tripStart.toLocaleString('en-GB',
                                                        {   day: "numeric",
                                                            month: "long",
                                                            year: "numeric",
                                                            hour: 'numeric',
                                                            minute: 'numeric',
                                                            hour12: true })
        } else {
            var tripStartStr = "On the way/Checked In"
        }
        //handle different locales in distant future
        window.trip = {
            "start" : tripStartStr
        }
        //trip stop
        //handle on-going trip
        if(this_entry.trip_stop == "None" || !this_entry.trip_stop) {
            window.tripStop = {
                "datetime" : "---"
            };
        }
        else {
            //get trip start datetime
            //utc to ist
            var tripStop = getDateTime(this_entry.trip_stop);
            //handle different locales in distant future
            window.tripStop = {
                "datetime" : tripStop.toLocaleString('en-GB',
                                                    {   day: "numeric",
                                                        month: "long",
                                                        year: "numeric",
                                                        hour: 'numeric',
                                                        minute: 'numeric',
                                                        hour12: true })
            }
        }
        //get final fare
        window.finalFare = '₹' + this_entry.price;
        window.cash = '₹' + this_entry.cash;
        window.credit = '₹' + this_entry.cred;
    }
    else {
        //trip id
        window.tripID = "-";
        //trip start

        window.trip = {
            "start" : "---"
        };
        //trip stop
        window.tripStop = {
            "datetime" : "---"
        };
        //get final fare
        window.finalFare = '₹0';
        //get cash fare
        window.cash = '₹0';
        window.credit = '₹0';
    }
    //get driver details if available
    if(this_entry.dname != "") {
        window.driver = {
            "id": this_entry.did,
            "name" : this_entry.dname,
            "mobile" : this_entry.dmob
        };
    }
    else {
        window.driver = {
            "id": 1,
            "name" : '-',
            "mobile" : '-'
        };
    }
}

function getDuration(booking_entry) {
    switch(booking_entry.booking_type) {
        case 1:
            //in-city round-trip
            window.duration = getFormattedDuration(booking_entry.dur) + " Hours";
            break;
        case 2:
            //outstation round-trip
            window.duration = booking_entry.days + getFormattedDuration(booking_entry.dur)/24 + " Days " ;
            break;
        case 5:
            //outstation oneway
            window.duration = booking_entry.days + getFormattedDuration(booking_entry.dur)/24 + " Days " ;
            break;
        default:
            window.duration = getFormattedDuration(booking_entry.dur) + " Hours";

    }
}

function getFormattedDuration(durHours) {
    var hours = parseInt(durHours.split(":")[0]);
    if(parseInt(durHours.split(":")[1]) > 0) {
        return (hours + (parseInt(durHours.split(":")[1]) / 60)).toFixed(1);
    }
    else return hours;
}

function dateRange(startDate, endDate) {
    var dateArray = new Array();
    var startTime = startDate.getTime(), endTime = endDate.getTime();
    for(loopTime = startTime; loopTime <= endTime; loopTime += 86400000) {
        var loopDay=new Date(loopTime);
        dateArray.push(loopDay.toISOString().split('T')[0]);
    }
    return dateArray;
}


function nDaysAgo(n, d=new Date()) {
    var ndays_ago = d - n * 86400000;
    nd = new Date(ndays_ago);
    return nd
}
function handleBookIdResponse(response, bookingCategory) {
    window.bookList[bookingCategory] = window.bookList[
        bookingCategory].concat(response["booking_id_list"]);
    //asyncProcessList(bookingCategory);
}

function asyncProcessList(bookingCategory) {
    book_ids = -1
    if (window.lock == false) {
        book_id = window.bookList[bookingCategory].splice(0, 1);
    }
}


//refresh bookings depending on parameter
function refreshBooking(bookingCategory, fullRefresh, from, to, searchName = "", searchCat = -1) {
    searchId = Math.random();
    const sid = searchId;
    var bookCategorySelector = $("#" + bookingCategory + "BookingsViewForm");
    var region = parseInt(getCookie()["region"])
    if (!Number.isInteger(region))
        region = -1;
    window.curIndex[bookingCategory] += 1;
    var sequence = Promise.resolve(); //initialize to empty resolved promise
    bookCategorySelector.find(".booking-stack").html('');
    //window.bookings = 0;
    var showAll = false;
    //This will run once all async operations have successfully finished
    if (bookingCategory == "Customer") {
        tripType = 0;
    } else if (bookingCategory == "C24") {
        tripType = C24_BOOKING[0];
    } else if (bookingCategory == "Revv") {
        tripType = REVV_BOOKING;
    } else if (bookingCategory == "Gujral") {
        tripType = GUJRAL_BOOKING;
    } else if (bookingCategory == "OLX") {
        tripType = OLX_BOOKING[0];
    }  else if (bookingCategory == "Zoomcar") {
        tripType = ZOOM_BOOKING[0];
    }  else if (bookingCategory == "Cardekho") {
        tripType = CARDEKHO_BOOKING[0];
    }  else if (bookingCategory == "Bhandari") {
        tripType = BHANDARI_BOOKING[0];
    }  else if (bookingCategory == "Mahindra") {
        tripType = MAHINDRA_BOOKING[0];
    } else if (bookingCategory == "RevvV2") {
        tripType = REVV_V2_BOOKING[0];
    } else if (bookingCategory == "Spinny") {
        tripType = SPINNY_BOOKING[0];
    } else if (bookingCategory == "PrideHonda") {
        tripType = PRIDEHONDA_BOOKING[0];
    } else {
        tripType = -1;
    }
    deferred = dateRange(from, to).reverse()
    console.log(deferred)
    var unset = false;
    promises = []
    deferred.forEach(function(entry) {
        promises.push(getRefreshDataDailyIDs(bookingCategory, entry, tripType, region, searchName, searchCat));
    })
    Promise.all(promises).then(values => {
        allBookings = []
        values.forEach(function(entry) {
            allBookings = allBookings.concat(entry["booking_id_list"])
        })
        new_deferred = []
        var i,j, temporary, chunk = 10;
        for (i = 0,j = allBookings.length; i < j; i += chunk) {
            new_deferred.push(allBookings.slice(i, i + chunk));
        }
        new_promises = []

        var unset = false;
        new_deferred.every(function(entry) {
            sequence = sequence.then(function() {
                if(sid != searchId){
                    return new Promise(function(resolve){console.log(sid + " " + searchId);});
                }
                return getRefreshDataDaily(entry, window.curIndex[bookingCategory]); // return a new Promise
            }).then(function(response) {
                setTimeout(function() {
                    if (response['success'] == 1 && response['cur_index'] == window.curIndex[bookingCategory]) {
                        if (!unset) {
                            $("#loaderModal").find('.btn-dismiss').trigger('click');
                            unset = true;
                        }
                        /*if (showAll) {
                            $("#loaderModal").find('.btn-dismiss').trigger('click');
                        }*/
                        var booking_entries = response['data'].length;
                        //clear table
                        var i = 0;
                        var reverse = true;
                        while (i < booking_entries && sid == searchId) {
                            //process each entry
                            var this_entry = JSON.parse(response['data'][i]);
                            //load booking info
                            //var c24Booking = (C24_BOOKING.includes(this_entry.booking_type));
                            getBookEntryInfo(this_entry);
                            //add row to table
                            var myID = -1;
                            //console.log(sid + " " +searchId);
                            if (entry == -1)
                                myID = addBookingEntry(bookingCategory, this_entry, true);
                            else
                                myID = addBookingEntry(bookingCategory, this_entry, reverse);
                            //color the row according to status
                            //also, initially hide all except {unallocated, upcoming, driver-cancelled and on-going}
                            bookingCategory = getCategoryStr(this_entry.booking_type);
                            bookingCategoryDash = bookingCategory + "-"
                            if(this_entry.cancelled == 1) {
                                //cancelled before anyone could accept
                                if(window.driver['name'] == '-') {
                                    console.log($("#"+bookingCategoryDash + "booking-quick-cancel").hasClass("removed")/* || showAll || window.customer["comment"]*/);
                                    if(!$("#"+bookingCategoryDash + "booking-quick-cancel").hasClass("removed"))  $("#booking"+myID).attr('class',  bookingCategoryDash + 'booking-quick-cancel');
                                    else $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-quick-cancel collapse');
                                    /*if (c24Booking) {
                                        $("#booking"+myID).removeClass('booking-quick-cancel').addClass('c24-booking-quick-cancel');
                                    }*/
                                    //allocation needed
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                                }
                                //customer cancelled
                                else {
                                    if(!$("#"+bookingCategoryDash + "booking-customer-cancel").hasClass("removed")/* || showAll || window.customer["comment"]*/)  $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-customer-cancel');
                                    else $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-customer-cancel collapse');
                                     /*if (c24Booking) {
                                        $("#booking"+myID).removeClass('booking-customer-cancel').addClass('c24-booking-customer-cancel');
                                      }*/
                                    //allocation needed
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                                }
                            }
                            else if(this_entry.cancelled == -1) {
                                //cancelled by d4m
                                if(!$("#"+bookingCategoryDash + "booking-d4m-cancel").hasClass("removed")/* || showAll || window.customer["comment"]*/)  $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-d4m-cancel');
                                else $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-d4m-cancel collapse');
                                /*if (c24Booking) {
                                    $("#booking"+myID).removeClass('booking-d4m-cancel').addClass('c24-booking-d4m-cancel');
                                }*/
                                //allocation needed
                                $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                            }
                            else if(this_entry.cancelled == 0) {
                                //driver cancelled
                                if(!$("#"+bookingCategoryDash + "booking-driver-cancel").hasClass("removed")/* || showAll || window.customer["comment"]*/)  $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-driver-cancel');
                                else $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-driver-cancel')
                                /*if (c24Booking) {
                                    $("#booking"+myID).removeClass('booking-driver-cancel').addClass('c24-booking-driver-cancel');
                                }*/
                                //allocation needed
                                $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');

                                    //unallocate
                                    /*if(!(this_entry.booking_type < 50))
                                        $("#booking"+myID).find('.dropdown-menu').prepend(
                                            '<button type="button" class="btn btn-xs unallocate" title="Unallocate">'+
                                                '<i class="fa fa-trash">' +
                                            '</button>');*/
                            }
                            else if(this_entry.cancelled == 2) {
                                //no one accepted
                                $("#booking"+myID).find(".book-track-driver-available").removeClass("collapse");
                                $("#booking"+myID).attr('class', bookingCategoryDash + 'booking-new');
                                /*if (c24Booking) {
                                    $("#booking"+myID).removeClass('booking-new').addClass('c24-booking-new');
                                }*/
                                //pending list needed
                                $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary pending-list">Show pending</button></li>');
                                //allocation needed
                                $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                                //d4m cancel
                                $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                                //d4m unallocate
                                $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');
                                //change button for book start
                                // $("#booking"+myID).find('.dropdown-menu').append(
                                //         '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary book-start-change">Change Book Start</button></li>');
                                //change estimate
                                /*$("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                                //broadcast option
                                if(getBookHoldingStatusText(this_entry.suppressed) == "SUPPRESSED") {
                                    var broadcastPlacer = $("#booking"+myID).find('.change-book-comments');
                                    $("<button class=\"btn btn-xs btn-warning broadcast\" type=\"button\" data-toggle=\"tooltip\" title=\"Broadcast Booking request\">" +
                                        "<i class=\"fa fa-bullhorn\"></i></button>").insertAfter(broadcastPlacer);
                                }
                                //customer cancel
                                /*if(!(this_entry.booking_type < 50 || this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                    $("#booking"+myID).find('.dropdown-menu').prepend(
                                        '<button type="button" class="btn btn-xs btn-danger customer-cancel" data-toggle="tooltip" title="Customer Cancelled">'+
                                        '<i class="fa fa-user-times"></i></button>');*/
                                //$("#loaderModal").find('.btn-dismiss').trigger('click');
                            }
                            else if(this_entry.cancelled == 3) {
                                //someone accepted
                                //trip not started
                                if(window.tripID == '-') {
                                    $("#booking"+myID).attr('class', bookingCategoryDash+'booking-accepted');
                                    /*if (c24Booking) {
                                        $("#booking"+myID).removeClass('booking-accepted').addClass('c24-booking-accepted');
                                    }*/
                                    //if re-allocation is needed
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary allocate">Allocate driver</button></li>');
                                    //add trip start option
                                    //if(!(this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                        $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-start">Start Trip</button></li>');
                                    //d4m cancel
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                                    //d4m unallocate
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');
                                    //change button for book start
                                    // $("#booking"+myID).find('.dropdown-menu').append(
                                    //     '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary book-start-change">Change Book Start</button></li>');
                                    //change estimate
                                    /*$("#booking"+myID).find('.dropdown-menu').append(
                                        '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                                    //customer cancel
                                    /*if(!(this_entry.booking_type < 50 || this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                        $("#booking"+myID).find('.dropdown-menu').prepend(
                                            '<button type="button" class="btn btn-xs btn-danger customer-cancel" data-toggle="tooltip" title="Customer Cancelled">'+
                                            '<i class="fa fa-user-times"></i></button>');*/

                                    //driver cancel
                                    /*$("#booking"+myID).find('.dropdown-menu').prepend(
                                        '<button type="button" class="btn btn-xs btn-warning driver-cancel" title="Driver Cancel">'+
                                            '<i class="fa fa-ban text-danger">' +
                                        '</button>');*/

                                    //unallocate
                                    /*if(!(this_entry.booking_type < 50))
                                        $("#booking"+myID).find('.dropdown-menu').prepend(
                                            '<button type="button" class="btn btn-xs unallocate" title="Unallocate">'+
                                                '<i class="fa fa-trash">' +
                                            '</button>');*/
                                    //$("#loaderModal").find('.btn-dismiss').trigger('click');
                                }
                                //ongoing trip
                                else if (!this_entry.trip_start) {
                                    $("#booking"+myID).attr('class', bookingCategoryDash+'booking-driver-cancel');
                                    //d4m cancel
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                                    //d4m unallocate
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');

                                    /*if (c24Booking) {
                                        $("#booking"+myID).removeClass('booking-ongoing').addClass('c24-booking-ongoing');
                                    }*/
                                    //change button for book start
                                    // $("#booking"+myID).find('.dropdown-menu').append(
                                    //     '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary book-start-change">Change Book Start</button></li>');
                                    //add trip start option
                                    //if(!(this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                        $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-start">Start Trip</button></li>');
                                    //driver cancel
                                    /*$("#booking"+myID).find('.dropdown-menu').prepend(
                                        '<button type="button" class="btn btn-xs btn-warning driver-cancel" title="Driver Cancel">'+
                                            '<i class="fa fa-ban text-danger">' +
                                        '</button>');*/
                                    //change estimate
                                    /*$("#booking"+myID).find('.dropdown-menu').append(
                                        '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                                    //$("#loaderModal").find('.btn-dismiss').trigger('click');

                                    //track
                                    $("#booking"+myID).find(".book-track-driver").removeClass("collapse");
                                }
                                else if(this_entry.trip_start && this_entry.trip_stop == "None") {
                                    $("#booking"+myID).attr('class', bookingCategoryDash+'booking-ongoing');
                                    //d4m cancel
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-cancel">Cancel Booking</button></li>');
                                    //d4m unallocate
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary d4m-unallocate">Unallocate Booking</button></li>');

                                    /*if (c24Booking) {
                                        $("#booking"+myID).removeClass('booking-ongoing').addClass('c24-booking-ongoing');
                                    }*/
                                    //change button for book start
                                    // $("#booking"+myID).find('.dropdown-menu').append(
                                    //     '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary book-start-change">Change Book Start</button></li>');
                                    //change button for trip start
                                    $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-start-change">Change Trip Start</button></li>');
                                    //trip stop
                                    //if(!(this_entry.booking_type == 520 || this_entry.booking_type == 521))
                                        $("#booking"+myID).find('.dropdown-menu').append('<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-stop">Stop Trip</button></li>');

                                    //change estimate
                                    /*$("#booking"+myID).find('.dropdown-menu').append(
                                        '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-estimate">Change Estimate</button></li>');*/
                                    //$("#loaderModal").find('.btn-dismiss').trigger('click');

                                    //track
                                    $("#booking"+myID).find(".book-track-driver").removeClass("collapse");
                                }
                                //otherwise, successful trip
                                else {
                                    if(!$("#"+bookingCategoryDash + "booking-completed").hasClass("removed")/* || showAll || window.customer["comment"]*/)  $("#booking"+myID).attr('class', bookingCategoryDash+'booking-completed');
                                    /*if (c24Booking) {
                                        $("#booking"+myID).removeClass('booking-completed').addClass('c24-booking-completed');
                                    }*/
                                    else $("#booking"+myID).attr('class', bookingCategoryDash+'booking-completed collapse');
                                    $("#booking"+myID).find('.dropdown-menu').append(
                                        '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-stop-change">Change Stop Time</button></li>');
                                    $("#booking"+myID).find('.dropdown-menu').append(
                                        '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary trip-restart">Restart Trip</button></li>');
                                    //change fare
                                    $("#booking"+myID).find('.dropdown-menu').append(
                                        '<li class=" tiny-padding"><button type="button" class="btn btn-sm btn-primary change-fare">Change Final Fare</button></li>');

                                    $("#booking"+myID).find(".book-history-driver").removeClass("collapse");
                                }
                            }
                            if (this_entry.c_color)
                                $("#booking"+myID).children().eq(2).addClass('new-customer')
                            if (this_entry.d_color)
                                $("#booking"+myID).children().eq(3).addClass('new-driver')
                            if (this_entry.f_color)
                                $("#booking"+myID).find(".change-book-comments").first().addClass('has-feedback')
                            if (this_entry.m_color)
                                $("#booking"+myID).children().eq(2).addClass('marked-customer')
                            i++;
                            /*if (c24Booking /*&& !$('#c24-select').is(":checked")) {
                                $("#booking"+myID).addClass("collapse")
                            }*/

                        }

                    } else {

                        if (!unset) {
                            $("#loaderModal").find('.btn-dismiss').trigger('click');
                            unset = true;
                        }
                    }
                }, 0);
            }).catch(function() {

                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                window.location = "/adminLogin";
            });
            return true;
        });
    });
    /*deferred.forEach(function(entry) {
        sequence = sequence.then(function() {
            var res = getRefreshDataDaily(entry, tripType, region, searchName, searchCat);
            promises.push(res)
            return res;
            //return getRefreshData(fullRefresh, from, entry, to, window.curIndex[bookingCategory], tripType, region, searchName, searchCat); // return a new Promise
        }).then(function(response) {
            setTimeout(function() {
                console.log(response, bookingCategory)
                handleBookIdResponse(response, bookingCategory);
            });
        }).catch(function() {

            $("#infoModal").find(".modal-header").css('background','gold');
            //change text
            $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
            setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
            setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
            window.location = "/adminLogin";
        });
    });
    Promise.all(promises).then(function() {
        console.log(window.bookList[bookingCategory])
    });*/
}

function getRefreshDataDailyIDs(bookingCategory, day, tripType, region, searchName = "", searchCat = -1) {
    data = new FormData();
    data.append("full_refresh", 0);
    data.append("day_given", 1);
    data.append("trip_type", tripType);
    data.append("date", day);
    data.append("region", region)
    data.append("search_name", searchName);
    data.append("search_cat", searchCat);
    return $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/book_ids',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                handleBookIdResponse(response, bookingCategory);
            }
    });
}

function getRefreshDataDaily(bookingList, curIndex) {
    data = new FormData();
    data.append("full_refresh", 0);
    data.append("booking_list", JSON.stringify(bookingList));
    data.append("curIndex", curIndex);
    return new Promise(function(resolve){ $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/booking',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                resolve(response);
            }
        });
    });
}

function getBit(n, k)//find if kth bit is set from right for integer n
{
    return (n >> k) % 2;
}

function getRefreshData(fullRefresh, base, offset, maxoffset, curIndex, tripType, region, searchName = "", searchCat = -1) {
    data = new FormData();
    data.append("full_refresh", fullRefresh);
    data.append("chunk_base", base);
    data.append("chunk_offset", offset);
    data.append("chunk_maxoffset", maxoffset);
    data.append("curIndex", curIndex);
    data.append("trip_type", tripType);
    data.append("region", region)
    data.append("search_name", searchName);
    data.append("search_cat", searchCat);
    return new Promise(function(resolve){ $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + '/api/admin/booking',
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                resolve(response);
            }
        });
    });
}

function addBookingEntry(bookingCategory, this_entry, reverse=true) {
    bookingCategory = getCategoryStr(this_entry.booking_type);

    var currPaymentOption = this_entry.payment_type == 1 ? "Payment-Mode-Credit" : "Payment-Mode-Cash";
    var bookingCategorySelector = $("#" + bookingCategory + "BookingsViewForm");
    var myID = ++window.bookings;
    var fareString = bookingCategory == "Customer" ? '<br>Cash: ' + window.cash+'<br>Credit: ' + window.credit:"";
    if (reverse) {
        bookingCategorySelector.find(".booking-stack").append('<tr id=booking'+myID+'>'+
            '<td><span class="entry-book-id">ID:&emsp;'+window.bookID + '&emsp;</span>'+
            '<button class="btn btn-xs btn-primary refresh-entry" type="button"><span class="glyphicon glyphicon-refresh"></span></button>'+
            '<button class="btn btn-xs btn-info change-book-comments" type="button">'+
                '<span class="glyphicon glyphicon-pencil"></span></button>'+
            '<button class="btn btn-xs btn-info display-photo" type="button">'+
                '<span class="glyphicon glyphicon-camera"></span></button>'+
            '<span class="dropdown" style="padding-top: 0px; padding-bottom: 0px">'+
                '<button class="btn btn-primary dropdown-toggle booking-tools" type="button" data-toggle="dropdown">Action&emsp;'+
                    '<span class="caret"></span></button>'+
                '<ul class="dropdown-menu">'+
                    '<button class="btn btn-xs btn-success triptype-convert" type="button" data-toggle="tooltip" title="Convert trip type">'+
                        '<i class="fa fa-globe"></i></button>'+
                    '<button class="btn btn-xs btn-secondary cartype-convert" type="button" data-toggle="tooltip" title="Convert Convert Car Type">'+
                        '<i class="fa fa-car"></i></button>'+
                    '<button class="btn btn-xs btn-primary book-log" type="button" data-toggle="tooltip" title="Booking Entry Log">'+
                        '<i class="fa fa-list-alt"></i></button>'+
                '</ul>'+
            '</div> '+
        '<br><span class="entry-book-code">Code:&emsp;'+this_entry.code+'</span>'+
        '<br><span class="entry-book-code">OTP:&emsp;'+this_entry.otp+'</span>'+
        '<br><span class="entry-book-insurance">Insurance:&emsp;'+(!!parseInt(this_entry.insurance) ? 'Yes' : 'No')+'</span>'+
        '<br><span class="entry-book-start">Start:&emsp;'+window.booking['start']+'</span>'+
        '<br>Expected Duration:&emsp;<span class="entry-duration">'+window.duration+'</span></td>'+
        '<td>Car Type:&emsp;'+window.vehicleType+
        '<br><span class="entry-trip-id collapse">ID:&emsp;'+window.tripID + '&emsp;</span>' +
        '<span class="entry-trip-start">Start:&emsp;'+window.trip['start']+'</span>'+
        '<br>Stop:&emsp;'+window.tripStop['datetime']+'</td>'+
        '<td><span class="glyphicon glyphicon-comment book-comments" data-toggle="tooltip"></span>'+
        '<a class="cust-link" id='+ window.customer["id"] + ">" + window.customer["name"]+'</a>' +
        ((getBit(this_entry.user_label,0)==1)?' <span title="VIP Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">V</span>' : '') +
        ((getBit(this_entry.user_label,1)==1)?' <span title="Known Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">K</span>' : '') +
        ((getBit(this_entry.user_label,2)==1)?' <span title="Issue previously" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">I</span>' : '') +
        ((getBit(this_entry.user_label,3)==1)?' <span title="Bad Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">B</span>' : '') +
        ((getBit(this_entry.user_label,4)==1)?' <span title="Cancels trip" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
        ((getBit(this_entry.user_label,5)==1)?' <span title="Does Directly" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">D</span>' : '') +
        '<br>Contact:&emsp;'+window.customer["mobile"] +
        '<br><i>' +
        ((this_entry.booking_type==520)?'<p style="color:red;">' : '<p>') +
        window.customer['booking_type'] + '</p></i></td>'+
        '<td class="entry-driver-details">' +
        '<a class="driv-link" id='+ window.driver["id"] + ">" + window.driver["name"]+'</a>'+
        ((getBit(this_entry.driver_label,30)==1)?' <span title="Lot of Cancelation" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
        ((getBit(this_entry.driver_label,29)==1)?' <span title="Late on Trip" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">L</span>' : '') +
        ((getBit(this_entry.driver_label,28)==1)?' <span title="Behavioural Issues" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">B</span>' : '') +
        ((getBit(this_entry.driver_label,27)==1)?' <span title="Not Groomed" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">G</span>' : '') +
        ((getBit(this_entry.driver_label,26)==1)?' <span title="Doesnt Have T-Shirt" style="font-weight: normal !important;color: #fff !important;" class="label label-success">T</span>' : '') +
        ((getBit(this_entry.driver_label,25)==1)?' <span title="Bad Driving Skill" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">S</span>' : '') +
        ((getBit(this_entry.driver_label,24)==1)?' <span title="Doing Direct" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">D</span>' : '') +
        '<span class="glyphicon glyphicon-map-marker book-track-driver collapse" data-track="'+ window.bookID +'"></span>'+
        '<span class="glyphicon glyphicon-map-marker book-track-driver-available collapse" data-track="'+ window.bookID +'"></span>'+
        '<span class="glyphicon glyphicon-map-marker book-history-driver collapse" data-track="'+ window.bookID +'"></span><br>'+
        'Contact:&emsp;'+window.driver["mobile"]+'<br><span class="region" region="' + this_entry.region + '">' +
        ((parseInt(this_entry.region)==0) ? '<p style="color:DarkSlateBlue;">' : ((parseInt(this_entry.region)==6) ?'<p style="color:Crimson;">' : ((parseInt(this_entry.region)==1)?'<p style="color:#FF6F61;">':'<p style="color:Brown;">'))) +
        'City:&emsp;'+regions[parseInt(this_entry.region)] + '</p></span></td>'+
        '<td>'+window.estimateFare+'&nbsp;<div title="Change Payment Mode" class="payment-option '+ currPaymentOption +'"></div><br>' + window.due + '</td>'+
        '<td>Total Fare: '+window.finalFare+ fareString + '</td>' +
        '<td class="location-section" style="max-width: 200px;">' +
                '<span class="source collapse">Source:&nbsp;' +
                    '<span class="label latitude">'+window.source["latitude"]+'</span>' +
                    '<span class="label longitude">'+window.source["longitude"]+'</span>' +
                '</span>' +
            '<button type="button" class="btn mapLink source-map source-map-booking">' +
                '<span class="glyphicon glyphicon-map-marker"></span>' +
            '</button><span class="addr">'+window.source['address'] + "</span><br>" +
        '</td>'+
        '</tr>');

        if(hasDestination(window.customer['booking_type_id'])) {
            $("#booking" + myID).find(".location-section").append(
                '<span class="destination collapse">Destination:&nbsp;' +
                            '<span class="label latitude">'+window.destination["latitude"]+'</span>' +
                            '<span class="label longitude">'+window.destination["longitude"]+'</span>' +
                        '</span>' +
                    '<button type="button" class="btn btn-info mapLink destination-map destination-map-booking">' +
                        '<span class="glyphicon glyphicon-map-marker"></span>' +
                    '</button><span class="addr">'+window.destination['address'] + "</span><br>"
            );
        }
        // set comment
        var comment = window.customer["comment"] == undefined || window.customer['comment'] == "" ? "" : window.customer['comment'];
        $("#booking" + myID).find(".book-comments").attr('title', "Comment:" + comment);
        if (comment) {
            $("#booking" + myID).find(".book-comments").addClass('has-comment');
            $("#booking" + myID).find(".book-comments").attr('comment', "1");
        }
        $("#booking" + myID).find(".book-comments").tooltip();
    } else {
            bookingCategorySelector.find(".booking-stack").prepend('<tr id=booking'+myID+'>'+
                '<td><span class="entry-book-id">ID:&emsp;'+window.bookID + '&emsp;</span>'+
                '<button class="btn btn-xs btn-primary refresh-entry" type="button"><span class="glyphicon glyphicon-refresh"></span></button>'+
                '<button class="btn btn-xs btn-info change-book-comments" type="button">'+
                    '<span class="glyphicon glyphicon-pencil"></span></button>'+
                '<button class="btn btn-xs btn-info display-photo" type="button">'+
                    '<span class="glyphicon glyphicon-camera"></span></button>'+
                '<span class="dropdown" style="padding-top: 0px; padding-bottom: 0px">'+
                '<button class="btn btn-primary dropdown-toggle booking-tools" type="button" data-toggle="dropdown">Action&emsp;'+
                    '<span class="caret"></span></button>'+
                '<ul class="dropdown-menu">'+
                    '<button class="btn btn-xs btn-success triptype-convert" type="button" data-toggle="tooltip" title="Convert trip type">'+
                        '<i class="fa fa-globe"></i></button>'+
                    '<button class="btn btn-xs btn-secondary cartype-convert" type="button" data-toggle="tooltip" title="Convert Convert Car Type">'+
                        '<i class="fa fa-car"></i></button>'+
                    '<button class="btn btn-xs btn-primary book-log" type="button" data-toggle="tooltip" title="Booking Entry Log">'+
                        '<i class="fa fa-list-alt"></i></button>'+
                '</ul>'+
            '</div> '+
            '<br><span class="entry-book-code">Code:&emsp;'+this_entry.code+'</span>'+
            '<br><span class="entry-book-code">OTP:&emsp;'+this_entry.otp+'</span>'+
            '<br><span class="entry-book-insurance">Insurance:&emsp;'+(!!parseInt(this_entry.insurance) ? 'Yes' : 'No')+'</span>'+
            '<br><span class="entry-book-start">Start:&emsp;'+window.booking['start']+'</span>'+
            '<br>Expected Duration:&emsp;<span class="entry-duration">'+window.duration+'</span></td>'+
            '<td>Car Type:&emsp;'+window.vehicleType+
            '<br><span class="entry-trip-id collapse">ID:&emsp;'+window.tripID + '&emsp;</span>'+'<span class="entry-trip-start">Start:&emsp;'+window.trip['start']+'</span>'+
            '<br>Stop:&emsp;'+window.tripStop['datetime']+'</td>'+
            '<td><span class="glyphicon glyphicon-comment book-comments" data-toggle="tooltip"></span>'+
            '<a class="cust-link" id='+ window.customer["id"] + ">" + window.customer["name"]+'</a>' +
            ((getBit(this_entry.user_label,0)==1)?' <span title="VIP Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">V</span>' : '') +
            ((getBit(this_entry.user_label,1)==1)?' <span title="Known Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">K</span>' : '') +
            ((getBit(this_entry.user_label,2)==1)?' <span title="Issue previously" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">I</span>' : '') +
            ((getBit(this_entry.user_label,3)==1)?' <span title="Bad Customer" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">B</span>' : '') +
            ((getBit(this_entry.user_label,4)==1)?' <span title="Cancels trip" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
            '<br>Contact:&emsp;'+window.customer["mobile"] +
            '<br><i>' +
            ((this_entry.booking_type==520)?'<p style="color:red;">' : '<p>') +
            window.customer['booking_type'] + '</p></i></td>'+
            '<td class="entry-driver-details">'+
            '<a class="driv-link" id='+ window.driver["id"] + ">" + window.driver["name"]+'</a>' +
            ((getBit(this_entry.driver_label,30)==1)?' <span title="Lot of Cancelation" style="font-weight: normal !important;color: #fff !important;" class="label label-warning">C</span>' : '') +
            ((getBit(this_entry.driver_label,29)==1)?' <span title="Late on Trip" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">L</span>' : '') +
            ((getBit(this_entry.driver_label,28)==1)?' <span title="Behavioural Issues" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">B</span>' : '') +
            ((getBit(this_entry.driver_label,27)==1)?' <span title="Not Groomed" style="font-weight: normal !important;color: #fff !important;" class="label label-primary">G</span>' : '') +
            ((getBit(this_entry.driver_label,26)==1)?' <span title="Doesnt Have T-Shirt" style="font-weight: normal !important;color: #fff !important;" class="label label-success">T</span>' : '') +
            ((getBit(this_entry.driver_label,25)==1)?' <span title="Bad Driving Skill" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">S</span>' : '') +
            ((getBit(this_entry.driver_label,24)==1)?' <span title="Doing Direct" style="font-weight: normal !important;color: #fff !important;" class="label label-danger">D</span>' : '') +
            '<span class="glyphicon glyphicon-map-marker book-track-driver collapse" data-track="'+ window.bookID +'"></span>'+
            '<span class="glyphicon glyphicon-map-marker book-track-driver-available collapse" data-track="'+ window.bookID +'"></span>'+
            '<span class="glyphicon glyphicon-map-marker book-history-driver collapse" data-track="'+ window.bookID +'"></span><br>'+
            'Contact:&emsp;'+window.driver["mobile"]+'<br><span class="region" region="' + this_entry.region + '">' +
            ((parseInt(this_entry.region)==0) ? '<p style="color:DarkSlateBlue;">' : ((parseInt(this_entry.region)==6) ?'<p style="color:Crimson;">' : ((parseInt(this_entry.region)==1)?'<p style="color:#FF6F61;">':'<p style="color:Brown;">'))) +
            'City:&emsp;'+regions[parseInt(this_entry.region)] + '</p></span></td>'+
            '<td>'+window.estimateFare+'<div title="Change Payment Mode" class="payment-option '+ currPaymentOption +'"></div>&nbsp;<br>' + window.due + '</td>'+
            '<td>Total Fare: '+window.finalFare + fareString + '</td>' +
            '<td class="location-section" style="max-width: 200px;">' +
                    '<span class="source collapse">Source:&nbsp;' +
                        '<span class="label latitude">'+window.source["latitude"]+'</span>' +
                        '<span class="label longitude">'+window.source["longitude"]+'</span>' +
                    '</span>' +
                '<button type="button" class="btn mapLink source-map source-map-booking">' +
                    '<span class="glyphicon glyphicon-map-marker"></span>' +
                '</button><span class="addr">'+window.source['address'] + "</span><br>" +
            '</td>'+
            '</tr>');

        if(hasDestination(window.customer['booking_type_id'])) {
            $("#booking" + myID).find(".location-section").append(
                '<span class="destination collapse">Destination:&nbsp;' +
                            '<span class="label latitude">'+window.destination["latitude"]+'</span>' +
                            '<span class="label longitude">'+window.destination["longitude"]+'</span>' +
                        '</span>' +
                    '<button type="button" class="btn btn-info mapLink destination-map destination-map-booking">' +
                        '<span class="glyphicon glyphicon-map-marker"></span>' +
                    '</button><span class="addr">'+window.destination['address'] + "</span><br>"
            );
        }
            // set comment
        var comment = window.customer["comment"] == undefined || window.customer['comment'] == "" ? "" : window.customer['comment'];
        $("#booking" + myID).find(".book-comments").attr('title', "Comment:" + comment);
        if (comment) {
            $("#booking" + myID).find(".book-comments").addClass('has-comment');
            $("#booking" + myID).find(".book-comments").attr('comment', "1");
        }
        $("#booking" + myID).find(".book-comments").tooltip();
    }
    return myID;
}


function setFiltersOld(fullDisplay) {
    if(parseInt(fullDisplay) == 1) {
        $("#booking-completed").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
        $("#booking-quick-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
        $("#booking-customer-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
        $("#booking-d4m-cancel").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    }
    else {
        $("#booking-completed").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
        $("#booking-quick-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
        $("#booking-customer-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
        $("#booking-d4m-cancel").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
    }
    $("#booking-new").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#booking-accepted").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#booking-ongoing").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#booking-comment").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#booking-driver-cancel").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');

}

function setFilters(bookingCategory, fullDisplay) {
    catStr = getCategoryStr(bookingCategory)
    if(parseInt(fullDisplay) == 1) {
        $("#" + catStr + "-booking-completed").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
        $("#" + catStr + "-booking-quick-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
        $("#" + catStr + "-booking-customer-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
        $("#" + catStr + "-booking-d4m-cancel").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    }
    else {
        $("#" + catStr + "-booking-completed").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
        $("#" + catStr + "-booking-quick-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
        $("#" + catStr + "-booking-customer-cancel").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
        $("#" + catStr + "-booking-d4m-cancel").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter removed');
    }
    $("#" + catStr + "-booking-new").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#" + catStr + "-booking-accepted").attr('class', 'col-lg-2 col-md-2 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#" + catStr + "-booking-ongoing").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#" + catStr + "-booking-comment").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');
    $("#" + catStr + "-booking-driver-cancel").attr('class', 'col-lg-1 col-md-1 col-sm-12 col-xs-12 standard-top-padding standard-bottom-padding book-filter');

}

function getActionTypeText(actionType) {
    if(actionType in logActionTypes) {
        return logActionTypes[actionType];
    }
    else return logActionTypes["unknown"];
}
function getCancelTypeText(cancelType) {
    if(cancelType in cancelTypes) {
        return cancelTypes[cancelType];
    }
    else return cancelTypes["unknown"];
}

function getBookTypeText(bookType) {
    if(bookType in tripTypes) {
        return tripTypes[bookType];
    }
    else return tripTypes[0];
}

function hasDestination(bookType) {
    if(destTripTypes.includes(bookType)) {
        return true;
    }
    else return false;
}

function getBookHoldingStatusText(holdStat) {
    if(holdStat in bookingVisibility) {
        return bookingVisibility[holdStat];
    }
    else return bookingVisibility[0]; //return allocated by default
}

function getBookingHoldStatus() {
    $.ajax({
        type:"POST",
        url: window.location.protocol + '//' + window.location.host + '/api/admin/get_hold',
        data: '',
        dataType: "json",
        contentType: false,
        beforeSend: function(request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            if (refresh_token) {

                if (checkRefresh(csrf_token, refresh_token) == false) {
                    alert("Unfortunately, your session has expired. Please login again");
                    window.location  = "/adminLogin";
                }
            }
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        processData: false,
        success: function(response) {
            if(response["hold_status"] == true) {
                setHoldActive();
            }
            else {
                setHoldReleased();
            }
        },
        error: function() {

        }

    });
}


function getBookingHoldStatusZoom() {
    $.ajax({
        type:"POST",
        url: window.location.protocol + '//' + window.location.host + '/api/admin/get_hold_zoom',
        data: '',
        dataType: "json",
        contentType: false,
        beforeSend: function(request) {
            var c = getCookie();
            var csrf_token = c['csrf_access_token'];
            var refresh_token = c['csrf_refresh_token'];
            if (refresh_token) {

                if (checkRefresh(csrf_token, refresh_token) == false) {
                    alert("Unfortunately, your session has expired. Please login again");
                    window.location  = "/adminLogin";
                }
            }
            request.setRequestHeader('X-CSRF-Token', csrf_token);
        },
        processData: false,
        success: function(response) {
            if(response["hold_status"] == true) {
                setHoldActiveZoom();
            }
            else {
                setHoldReleasedZoom();
            }
        },
        error: function() {

        }

    });
}

function setHoldActive() {
    $("#globalHold").attr('title', 'Release All Trips');
    $("#globalHold").find('.hold-status-text').html("OFF");
    $("#globalHold").addClass("hold").removeClass("btn-danger");
}
function setHoldReleased() {
    $("#globalHold").attr('title', 'Hold All Trips');
    $("#globalHold").find('.hold-status-text').html("ON");
    $("#globalHold").removeClass("hold").addClass("btn-danger");

}

function setHoldActiveZoom() {
    $("#globalHoldZoom").attr('title', 'Release All Trips');
    $("#globalHoldZoom").find('.hold-status-text').html("OFF");
    $("#globalHoldZoom").addClass("hold").removeClass("btn-danger");
}
function setHoldReleasedZoom() {
    $("#globalHoldZoom").attr('title', 'Hold All Trips');
    $("#globalHoldZoom").find('.hold-status-text').html("ON");
    $("#globalHoldZoom").removeClass("hold").addClass("btn-danger");

}

function setBookingHoldValue(value, zoom=false) {
    url = 'admin.set_hold';
    if (zoom)
        url = 'admin.set_hold_zoom';
    data = new FormData();
    data.append('hold_status', value);
    $.ajax({
            type:"POST",
            url: window.location.protocol + '//' + window.location.host + "/api/admin/set_hold",
            data: data,
            beforeSend: function(request) {
                var c = getCookie();
                var csrf_token = c['csrf_access_token'];
                var refresh_token = c['csrf_refresh_token'];
                if (refresh_token) {

                    if (checkRefresh(csrf_token, refresh_token) == false) {
                        alert("Unfortunately, your session has expired. Please login again");
                        window.location  = "/adminLogin";
                    }
                }
                request.setRequestHeader('X-CSRF-Token', csrf_token);
             },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if(response['success'] == 1) {

                }
                else {
                    //change background
                    $("#infoModal").find(".modal-header").css('background','lightsalmon');
                    //change text
                    $("#infoModal").find(".modal-title").html("Error code : " + response['success']);
                    setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                    setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                }
            },
            error: function() {
                //Connection error...............................................................
                //change background
                $("#infoModal").find(".modal-header").css('background','gold');
                //change text
                $("#infoModal").find(".modal-title").html("Something went wrong. <br> Check your connection to the server and try again.");
                setTimeout(function(){ $("#infoModal").modal('show'); }, 100);
                setTimeout(function(){ $("#infoModal").modal('hide'); }, 2900);
                //window.location = "/adminLogin";
            }

        });
}

function readImageURL(input, imageDisplay) {
  if (input.files && input.files[0]) {
    var reader = new FileReader();

    reader.onload = function(e) {
      imageDisplay.attr('src', e.target.result);
    }

    reader.readAsDataURL(input.files[0]); // convert to base64 string
  }
}
function initMapInputs() {
    var regionBounds = getRegionBounds();
    var tripTypeDestInput = document.getElementById("tripTypeDestSearch");
    var tripTypeDestAutocomplete = new google.maps.places.Autocomplete(tripTypeDestInput);
    //tripTypeDestAutocomplete.setBounds(regionBounds);
    //tripTypeDestAutocomplete.setOptions({strictBounds: true});
    tripTypeDestAutocomplete.setComponentRestrictions(
        {'country': ['in']});
    var driverLocationInput = document.getElementById("driverLocationChange");
    var driverLocationAutocomplete = new google.maps.places.Autocomplete(driverLocationInput);
    driverLocationAutocomplete.setComponentRestrictions(
        {'country': ['in']});

}
function getLatLongSublocalityMaps(address) {
    var locationObject = {
        "latitude":  null,
        "longitude": null,
        "sublocality": null,
        "status": ""
    }
    if(isNullOrEmpty(address)) {
        return locationObject;
    }
    $.ajax({
        async: false,
        type: "POST",
        url: "https://maps.googleapis.com/maps/api/geocode/json?address=" + address + "&key=" + window.messages["GOOGLE_MAPS_API_KEY"],
        beforeSend: function(request) {

        },
        dataType: "json",
        contentType: false,
        processData: false,
        success: function(response) {
            if (response["status"] === google.maps.GeocoderStatus.OK) {
                subLocFound = false;
                res = response["results"]
                locationObject["latitude"] = res[0]["geometry"]["location"]["lat"];
                locationObject["longitude"] = res[0]["geometry"]["location"]["lng"];
                for (i=0; i < res.length; ++i) {
                    r = res[i];
                    address_comps = r["address_components"]
                    if (!address_comps) {
                        continue;
                    }
                    for (j=0; j < address_comps.length; ++j) {
                        a = address_comps[j];
                        types = a["types"]
                        console.log(types.indexOf("sublocality_level_1"))
                        if (types.indexOf("sublocality_level_1") >= 0) {
                            locationObject["sublocality"] = a["long_name"];
                            subLocFound = true;
                        } else if (
                            types.indexOf("administrative_area_level_2") >= 0 ||
                            types.indexOf("sublocality_level_2") >= 0
                        ) {
                            locationObject["sublocality"] = a["long_name"];
                        }
                        if (subLocFound) {
                            break;
                        }
                    }
                    if (subLocFound) {
                        break;
                    }
                }
                if (!subLocFound) {
                    locationObject["sublocality"] = address;
                }
            }
            locationObject["status"] = response["status"];
        }
    });
    return locationObject;
}

function getLatLongMaps(address) {
    var locationObject = {
        "latitude":  null,
        "longitude": null,
        "status": ""
    }
    if(!isNullOrEmpty(address))
    {
        $.ajax({
            async: false,
            type: "POST",
            url: "https://maps.googleapis.com/maps/api/geocode/json?address=" + address + "&key=" + window.messages["GOOGLE_MAPS_API_KEY"],
            beforeSend: function(request) {

            },
            dataType: "json",
            contentType: false,
            processData: false,
            success: function(response) {
                if (response["status"] === google.maps.GeocoderStatus.OK) {
                    locationObject["latitude"] = response["results"][0]["geometry"]["location"]["lat"];
                    locationObject["longitude"] = response["results"][0]["geometry"]["location"]["lng"];
                }
                locationObject["status"] = response["status"];
            }
        });
    }
    return locationObject;
}
/**
 * TO DO - Make a separate JS for this function, it doesn't belong here
 */
function getCookie() {
    cookieSplit = document.cookie.split("; ");
    var cookieObject = {};
    cookieSplit.forEach( function(value, index) {
        var splitResult = value.split("=");
        cookieObject[splitResult[0]] = splitResult[1];
    });
    return cookieObject;
}

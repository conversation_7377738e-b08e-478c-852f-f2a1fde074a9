import pytest
from flask import Flask
from models import Users, Drivers, db,AdminAccess,UserToken,DriverVerify,Bookings,BookDest,Trip,UserTrans,DriverLoc,DriverDetails,BookPricing
from _utils import get_pwd,get_salt
import random
import datetime
import hashlib
import j<PERSON>pickle
import json
from unittest.mock import patch, MagicMock
from sqlalchemy import exc
from flask_jwt_extended import jwt_required, create_access_token,create_refresh_token
from conftest import driver_details,driver_bookings,driver_trip




# API - /api/driver/reverify

def test_reverify_success(client,driver_login):
    # Create a driver with verification status
    state = driver_login
    access_token = state['access_token']
    # Login to get the token

    # Make a POST request to /api/driver/reverify
    headers={
        'Authorization': f'Bearer {access_token}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/api/driver/reverify', headers=headers)
    # Check if the response is successful
    assert response.status_code == 200
    assert response.json['success'] == 1
    assert response.json['bitstring'] == '11111'

def test_reverify_unapproved_driver(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    driver=db.session.query(Drivers).filter(Drivers.id==driver_id).first()
    try:
        driver.approved=0
        driver_verify = DriverVerify(driver.id)
        db.session.add(driver_verify)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers={
        'Authorization': f'Bearer {access_token}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/api/driver/reverify', headers=headers)
    assert response.status_code == 200
    assert response.json['success'] == 1
    assert response.json['bitstring'] == '00000'

def test_reverify_not_verified(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    driver=db.session.query(Drivers).filter(Drivers.id==driver_id).first()
    try:
        driver.approved=0
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers={
        'Authorization': f'Bearer {access_token}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/api/driver/reverify', headers=headers)
    assert response.status_code == 200
    assert response.json['success'] == -2
    assert response.json['bitstring'] == '00000'

def test_reverify_disabled_account(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        driver=db.session.query(Drivers).filter(Drivers.id==driver_id).first()
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.enabled=False
        driver.approved=1
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers={
        'Authorization': f'Bearer {access_token}',
        'User-Agent': 'TestAgent'
    }
    response = client.post('/api/driver/reverify', headers=headers)
    assert response.status_code == 401
    assert response.json['success'] == -2
    assert response.json['bitstring'] == '00000'

# ---------------------------------

#    API - /api/driver/experience/set

def test_exp_set_success(client, driver_login):
 # Create a test user and get a JWT token
    state = driver_login
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'hb_m': 1,
        'sed_m': 2,
        'suv_m': 3,
        'lux_m': 4,
        'hb_a': 5,
        'sed_a': 6,
        'suv_a': 7,
        'lux_a': 8
    }

    response = client.post('/api/driver/experience/set', headers=headers, data=form_data)

    assert response.status_code == 200
    assert response.json['success'] == 1

def test_exp_set_not_driver(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.role=Users.ROLE_USER
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'hb_m': 1,
        'sed_m': 2,
        'suv_m': 3,
        'lux_m': 4,
        'hb_a': 5,
        'sed_a': 6,
        'suv_a': 7,
        'lux_a': 8
    }

    response = client.post('/api/driver/experience/set', headers=headers, data=form_data)

    assert response.status_code == 401
    assert response.json['success'] == -1

def test_exp_set_account_disabled(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        user=db.session.query(Users).filter(Users.id==user_id).first()
        user.enabled = False
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'hb_m': 1,
        'sed_m': 2,
        'suv_m': 3,
        'lux_m': 4,
        'hb_a': 5,
        'sed_a': 6,
        'suv_a': 7,
        'lux_a': 8
    }

    response = client.post('/api/driver/experience/set', headers=headers, data=form_data)
    print(response)
    assert response.status_code == 401
    assert response.json['success'] == -2

def test_exp_set_failure(client, driver_login):
    state = driver_login
    access_token = state['access_token']
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    form_data = {
        'hb_m': 1,
        'sed_m': 2,
        'suv_m': 3,
        'lux_m': 4,
        'hb_a': 5,
        'sed_a': 6,
        'suv_a': 7,
        'lux_a': 8
    }

    # Mock create_driver_exp function to simulate failure
    with patch('drivers.create_driver_exp') as mock_create_driver_exp:
        # Simulate raising an exception in create_driver_exp
        mock_create_driver_exp.side_effect = Exception("Forced failure")

        response = client.post('/api/driver/experience/set', headers=headers, data=form_data)

        # Assert the status code is 200 OK
        assert response.status_code == 200

        # Assert the failure response matches the expected behavior
        assert response.json['success'] == -3


# ---------------------------------

#  API - /api/driver/past_cust

def test_past_cust_success(client,driver_login):
    state = driver_login
    access_token = state['access_token']
    user_id=state['user_id']
    driver_id=state['driver_id']
    try:
        start_dt = datetime.datetime.utcnow() - datetime.timedelta(hours=2)
        end_dt = datetime.datetime.utcnow() - datetime.timedelta(hours=1)
        booking = Bookings(
        user=user_id,
        skey='some_secret_key',
        driver=driver_id,
        lat=0.0,
        long=0.0,
        starttime=start_dt.strftime("%H:%M:%S"),
        startdate=start_dt.strftime("%Y-%m-%d"),
        dur=datetime.datetime.utcnow().strftime("%H:%M:%S"),
        endtime = end_dt.strftime("%H:%M:%S"),
        enddate = end_dt.strftime("%Y-%m-%d"),
        estimate=100,
        pre_tax=90,
        loc='Test Location',
        car_type=0,
        )
        booking.valid=1
        db.session.add(booking)
        db.session.commit()
        cur_dt = datetime.datetime.utcnow()
        past_endtime = cur_dt - datetime.timedelta(hours=1)
        trip= Trip(booking.id,datetime.datetime.utcnow() - datetime.timedelta(hours=2),1,1,0)
        trip.trans="asds"
        db.session.add(trip)
        trip.endtime=past_endtime
        db.session.commit()
        ut=UserTrans(user_id,100)
        db.session.add(ut)
        ut.trans_payment_id=trip.trans
        bookpricing=BookPricing(bid=booking.id,est=100,base=50,est_pre_tax=40,cgst=0,sgst=0,cartype=1,night=10,food=0,booking=5,dist=10,insurance=2,driver_base=30,driver_night=10)
        db.session.add(bookpricing)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {
        'Authorization': f"Bearer {access_token}"
    }
    # Define limits for pagination
    data = {
        'llimit': 0,
        'ulimit': 10
    }
    response = client.post('/api/driver/past_cust', headers=headers, data=data)
    assert response.status_code == 200
    assert isinstance(response.json, list)  # Expecting a list of past bookings
    assert len(response.json) > 0  # Should return at least one past booking
    # Optionally, assert specific fields in the response
    for booking in response.json:  # Assuming response.json is a list of JSON strings
        # Decode the JSON using json.loads
        booking_data = json.loads(booking)  # Convert string to dictionary
        # Check that user data is included
        assert 'name' in booking_data, "Name is missing in booking data"
        # Check that trip data is included
        assert 'endtime' in booking_data, "Endtime is missing in booking data"
        # Ensure trip has a start time and end time
        assert 'starttime' in booking_data, "Start time is missing in booking data"
        # Validate that endtime is less than current UTC datetime
        endtime_str = booking_data['endtime']
        endtime = datetime.datetime.strptime(endtime_str, "%H:%M:%S")  # Parse endtime
        current_time = datetime.datetime.utcnow()  # Get current UTC time
        # Extract date for comparison, since endtime only has time
        trip_date_str = booking_data['startdate']
        trip_date = datetime.datetime.strptime(trip_date_str, "%Y-%m-%d")  # Parse start date
        trip_datetime = datetime.datetime.combine(trip_date.date(), endtime.time())  # Combine date and time for full datetime
        assert trip_datetime < current_time, "Trip endtime should be in the past"
        # Check other properties
        assert booking_data['loc'] == "Test Location", "Location should be 'Test Location'"
        assert booking_data['car_type'] == 0, "Car type should be 0"
        assert booking_data['trip_type'] == 1, "Trip type should be 1"

 # ---------------------------------

 #  API - /api/driver/set_loc

def test_set_driver_loc_success(client,driver_login):
    """Test successful location update"""
    state = driver_login
    access_token = state['access_token']
    user = state['user_id']
    driver_id = state['driver_id']
    try:
        drivloc=DriverLoc(driver_id,12.34,56.78)
        db.session.add(drivloc)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
    headers = {'Authorization': f'Bearer {access_token}'}
    response = client.post('/api/driver/set_loc', data={'lat': 23.45, 'lng': 67.89}, headers=headers)
    assert response.status_code == 200
    data = response.get_json()
    assert data['success'] == 1

    # Verify database has been updated with the new location
    updated_loc = db.session.query(DriverLoc).filter_by(driver_id=driver_id).first()
    assert updated_loc.lat == 23.45
    assert updated_loc.lng == 67.89

 # ---------------------------------

# #  API - /api/driver/earning/monthly

def test_get_earning_monthly(client, driver_login):
    state = driver_login
    # Set the authorization header
    driver_id = state['driver_id']
    access_token = state['access_token']
    user_id = state['user_id']
    headers = {'Authorization': f'Bearer {access_token}'}
    driver_details_id=driver_details(driver_id)
    booking_id=driver_bookings(user_id,driver_id)
    trip_id=driver_trip(booking_id,0)
    # Prepare request data
    current_date = datetime.datetime.now()
    # Create request_data with the current month and year
    request_data = {
        'month': current_date.month,
        'year': current_date.year
    }
    # Make the POST request to the endpoint
    response = client.post('/api/driver/earning/monthly', headers=headers, data=request_data)

    # Assert the response status code and content
    assert response.status_code == 200
    json_data = json.loads(response.data)

    assert json_data['success'] == 1
    assert json_data['trip_count'] == 1  # Assuming 1 trip is added
    assert json_data['earning'] == 500  # The earning from the trip
    assert json_data['hour_count'] == 2  # Hours from the trip (2 hours)
    assert json_data['owed'] == 100
    assert json_data['wallet'] == 200
    assert json_data['withdrawable'] == 50

#   API - /api/driver/earning/total

def test_total_earning(client, driver_login):
    state = driver_login
    # Set the authorization header
    access_token = state['access_token']
    driver_id = state['driver_id']
    user_id = state['user_id']
    driver_details_id=driver_details(driver_id)
    booking_id=driver_bookings(user_id,driver_id)
    trip_id=driver_trip(booking_id,0)
    headers = {'Authorization': f'Bearer {access_token}'}


    # Make the POST request to the endpoint
    response = client.post('/api/driver/earning/total', headers=headers)

    # Assert the response status code and content
    assert response.status_code == 200
    json_data = json.loads(response.data)

    assert json_data['success'] == 1
    assert json_data['trip_count'] == 1  # Assuming 1 trip is added
    assert json_data['earning'] == 500  # The earning from the trip
    assert json_data['hour_count'] == 2  # Hours from the trip (2 hours)
    assert json_data['owed'] == 100
    assert json_data['wallet'] == 200
    assert json_data['withdrawable'] == 50

# ---------------------------------
tags:
  - User
summary: Register Customer
description: This endpoint registers a new customer using the provided details such as mobile number, password, and first name.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: Mobile number of the user (without +91)
  - name: pwd
    in: formData
    type: string
    required: true
    description: Password for the user account
  - name: fname
    in: formData
    type: string
    required: true
    description: First name of the user
  - name: lname
    in: formData
    type: string
    required: false
    description: Last name of the user
  - name: email
    in: formData
    type: string
    required: false
    description: Email address of the user
responses:
  200_a:
    description: Success - Customer registered successfully
    schema:
      type: array
      items:
        type: object
        properties:
          response:
            type: integer
            example: 0
          msg:
            type: string
            example: "Success"
    examples:
      application/json:
        - response: 0
          msg: "Success"
  200_b:
    description: Incomplete form details
    schema:
      type: array
      items:
        type: object
        properties:
          response:
            type: integer
            example: 1
          msg:
            type: string
            example: "Incomplete form"
    examples:
      application/json:
        - response: 1
          msg: "Incomplete form"
  200_c:
    description: Missing first name
    schema:
      type: array
      items:
        type: object
        properties:
          response:
            type: integer
            example: 1
          msg:
            type: string
            example: "Provide first name"
    examples:
      application/json:
        - response: 1
          msg: "Provide first name"
  200_d:
    description: Invalid fields (e.g., mobile or email)
    schema:
      type: array
      items:
        type: object
        properties:
          response:
            type: integer
            example: 2
          msg:
            type: string
            example: "Invalid fields"
    examples:
      application/json:
        - response: 2
          msg: "Invalid fields"
  200_e:
    description: Integrity constraint error
    schema:
      type: array
      items:
        type: object
        properties:
          response:
            type: integer
            example: 4
          msg:
            type: string
            example: "Integrity constraint error"
    examples:
      application/json:
        - response: 4
          msg: "Integrity constraint error"
  200_f:
    description: Bad request method
    schema:
      type: array
      items:
        type: object
        properties:
          response:
            type: integer
            example: 3
          msg:
            type: string
            example: "Bad request"
    examples:
      application/json:
        - response: 3
          msg: "Bad request"
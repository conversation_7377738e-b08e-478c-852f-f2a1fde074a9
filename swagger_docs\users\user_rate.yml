tags:
  - Trips
summary: Rate a Trip
description: >
  This endpoint allows the user to rate a driver for a specific booking. If the user rating is less than 3, a low rating notification is sent.
parameters:
  - name: book_id
    in: formData
    type: integer
    required: true
    description: The booking ID for which the rating is being given.
  - name: user_rating
    in: formData
    type: float
    required: true
    description: Rating given by the user, out of 5.
  - name: comment
    in: formData
    type: string
    required: false
    description: Optional comment for the driver or trip.
responses:
  200_a:  # Success - Rating added successfully
    description: Rating added successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        app_rate:
          type: boolean
          description: Indicates whether the app should request a user rating
          example: True
        message:
          type: string
          description: Success message
          example: "Rating added successfully"
    examples:
      application/json:
        success: 1
        app_rate: True
        message: "Rating added successfully"

  200_b:  # Success - Already rated by this user
    description: Already rated by this user
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "Already rated by this user"
    examples:
      application/json:
        success: 1
        message: "Already rated by this user"

  200_c:  # Success - Failed to create details
    description: Failed to create details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to create details"
    examples:
      application/json:
        success: -1
        message: "Failed to create details"

  201_a:  # Error - Incomplete form details
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"

  401_a:  # Unauthorized - User restricted
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"

  200_d:  # Success - No booking exist
    description: No booking exists for this ID
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "No booking exist"
    examples:
      application/json:
        success: -1
        message: "No booking exist"

  500_a:  # Server error - Database Error
    description: Database Error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "DB Error"
    examples:
      application/json:
        success: -1
        message: "DB Error"

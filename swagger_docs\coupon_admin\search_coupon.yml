tags:
  - Coupon_admin
summary: Search Coupons
description: >
  This endpoint allows admins to search for coupons based on a search term. The term can either be a coupon code or a mobile number. Admin authorization is required.
parameters:
  - in: formData
    name: search_term
    type: string
    required: true
    description: The term to search for (either a mobile number or part of a coupon code).
    example: "SAVE20"
responses:
  200:
    description: Coupons found matching the search term.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              coupon_id:
                type: integer
                description: Unique identifier for the coupon
                example: 123
              coupon_code:
                type: string
                description: The coupon code
                example: "SAVE20"
              percent_off:
                type: number
                description: Percentage off provided by the coupon
                example: 10
              max_off:
                type: number
                description: Maximum discount allowed for the coupon
                example: 50
              flat_off:
                type: number
                description: Flat amount off provided by the coupon
                example: 20
              min_trip_price:
                type: number
                description: Minimum trip price required to apply the coupon
                example: 100
              min_trip:
                type: integer
                description: Minimum number of trips required to apply the coupon
                example: 5
              validTill:
                type: string
                format: date
                description: Expiry date of the coupon
                example: "2024-12-31"
              createdAt:
                type: string
                format: date-time
                description: Date the coupon was created
                example: "2023-10-01T12:00:00Z"
              state:
                type: integer
                description: State of the coupon (1 for active, 0 for inactive)
                example: 1
  400:
    description: Incomplete search term provided.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (2)
          example: 2
        msg:
          type: string
          description: Error message
          example: "Incomplete search term"
  401:
    description: Unauthorized access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  404:
    description: No coupons found matching the search term.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "No coupons found"
  500:
    description: Internal server error during the search.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Error occurred while searching"

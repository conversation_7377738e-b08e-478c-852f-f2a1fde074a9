# nginx.conf
upstream app_servers {
    ip_hash;
    server gunicorn:8000;
}

server {
    listen 80;
    # General HTTP configuration
    location / {
        client_max_body_size 4G;
        proxy_pass http://app_servers;
        proxy_http_version 1.1;  # Ensure HTTP/1.1 for WebSocket compatibility
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket-specific headers
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $http_connection;

        # Timeout settings
        proxy_read_timeout 86400;  # For long-lived WebSocket connections
        proxy_send_timeout 86400;
    }
}
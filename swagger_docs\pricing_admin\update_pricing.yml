tags:
  - Pricing_admin
summary: Update Pricing Details for a Specific City and Trip Type
description: >
  This endpoint allows admins to update pricing details for a specific city and trip type.
  Optionally, an occasion can be specified to update occasion-specific pricing. Admin authorization is required.
parameters:
  - name: city
    in: formData
    required: true
    type: string
    description: The name of the city for which pricing details are being updated.
    example: "Los Angeles"
  - name: trip_type
    in: formData
    required: true
    type: string
    description: The type of trip (inCity or outStation).
    example: "inCity"
  - name: occasion
    in: formData
    required: false
    type: string
    description: The name of the occasion for which specific pricing details are being updated.
    example: "New Year"
  - name: night_charge
    in: formData
    required: false
    type: number
    description: The night charge for the specified trip type and occasion (if applicable).
    example: 50.00
  - name: part_night_charge
    in: formData
    required: false
    type: number
    description: The part night charge for the specified trip type and occasion (if applicable).
    example: 25.00
  - name: base_fare
    in: formData
    required: false
    type: number
    description: The base fare for the specified trip type.
    example: 150.00
  - name: booking_percent
    in: formData
    required: false
    type: number
    description: The booking percent for the specified trip type.
    example: 10.0
  - name: extra_fare
    in: formData
    required: false
    type: number
    description: Extra fare for outStation trips.
    example: 20.00
  - name: start_night_time
    in: formData
    required: false
    type: string
    description: The starting time for the night charge window.
    example: "22:00"
  - name: end_night_time
    in: formData
    required: false
    type: string
    description: The ending time for the night charge window.
    example: "06:00"
  - name: car_fares
    in: formData
    required: false
    type: object
    description: >
      Fare details for different car types, including manual and automatic options for hatchback, sedan, SUV, and luxury cars.
    properties:
      hatch_man:
        type: number
        example: 30.00
      sedan_man:
        type: number
        example: 35.00
      suv_man:
        type: number
        example: 40.00
      lux_man:
        type: number
        example: 50.00
      hatch_auto:
        type: number
        example: 35.00
      sedan_auto:
        type: number
        example: 40.00
      suv_auto:
        type: number
        example: 45.00
      lux_auto:
        type: number
        example: 55.00
responses:
  200:
    description: Successfully updated pricing details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        msg:
          type: string
          description: Success message
          example: "Pricing details updated successfully for trip type inCity in city Los Angeles"
  400:
    description: Request failed due to incomplete data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Incomplete data"
  401:
    description: Unauthorized access
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  404:
    description: Data not found for the specified city, trip type, or occasion
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Occasion New Year not found for trip type inCity in city Los Angeles"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

tags:
  - Booking_admin
summary: Retrieve details of a specific booking
description: >
  This endpoint retrieves detailed information about a booking based on the provided booking ID.
parameters:
  - name: booking_id
    in: formData
    type: integer
    required: true
    description: The unique ID of the booking to retrieve details for.
    example: 12345
responses:
  200:
    description: Successfully retrieved booking details.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        data:
          type: object
          properties:
            booking_details:
              type: object
              properties:
                booking_id:
                  type: integer
                  description: The ID of the booking.
                  example: 12345
                book_code:
                  type: string
                  description: The unique code associated with the booking.
                  example: "ABC123"
                book_otp:
                  type: string
                  description: The one-time password for the booking.
                  example: "789456"
                book_date_time:
                  type: string
                  format: date-time
                  description: The timestamp when the booking was made.
                  example: "2024-10-17 14:30:00"
                book_insurance:
                  type: string
                  description: The insurance details for the booking.
                  example: "Basic Coverage"
                book_insurance_num:
                  type: string
                  description: The insurance number for the booking.
                  example: "INS123456"
                book_location:
                  type: string
                  description: The location associated with the booking.
                  example: "Times Square"
                book_status:
                  type: boolean
                  description: Indicates if the booking is valid.
                  example: true
                book_driver:
                  type: integer
                  description: The ID of the assigned driver.
                  example: 678
                booking_region:
                  type: string
                  description: The region associated with the booking.
                  example: "New York"
            trip_schedule:
              type: object
              properties:
                start_date:
                  type: string
                  format: date
                  description: The start date of the trip.
                  example: "2024-10-17"
                start_time:
                  type: string
                  format: time
                  description: The start time of the trip.
                  example: "14:30:00"
                end_date:
                  type: string
                  format: date
                  description: The end date of the trip.
                  example: "2024-10-17"
                end_time:
                  type: string
                  format: time
                  description: The end time of the trip.
                  example: "16:00:00"
                duration:
                  type: string
                  format: time
                  description: The duration of the trip.
                  example: "01:30:00"
            trip_time:
              type: object
              properties:
                start_date:
                  type: string
                  format: date
                  description: The start date of the trip.
                  example: "2024-10-17"
                start_time:
                  type: string
                  format: time
                  description: The start time of the trip.
                  example: "14:30:00"
  400:
    description: Bad request, booking ID is missing or invalid.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: "Incomplete Form Details"
  404:
    description: Booking not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: "Booking does not exist"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        error:
          type: string
          example: "Internal server error message"

#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  book_ride.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import datetime
from datetime import timedelta
import math
import json
import time as tm
import random
import uuid
from threading import Thread
import pytz
from flask import Blueprint, request, jsonify
from flask import current_app as app
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity
from google.cloud import firestore
from sqlalchemy import or_, and_, exc, desc, literal,text
from sqlalchemy.dialects import mysql
from sqlalchemy.sql import func, or_,not_
from _ops_message import send_slack_msg
import _sms
from _fcm_oauth import send_bulk_notifications
from _rtdb import _update_user_pending, _update_driver_pending
from _fcm import send_fcm_msg, send_fcm_msg_driver, send_fcm_msg_driver_request
from _utils import complete, get_dt_ist, get_safe,  compute_driver_wallet, is_vip_or_faced_issue,distance_on_earth
from _utils_acc import account_enabled
from booking_params import BookingParams, Regions, CountryBoundary
from _utils_booking import booking_has_trip, get_dist_on_map, fetch_booking_trip, check_ongoing_time, get_book_code ,check_ongoing_time_date_time
from price import Price, PriceOutstation, is_trip_night
from payments import PaymentType
from db_config import fb_db
from models import DriverSearch, Users, Drivers, DriverInfo, Bookings, DriverFCM,BookPending, BookDest, db, DriverDetails, BookPricing, BookingCancelled
from models import DriverCancelled, DriverRegion, UserCancelled, UserTrans, Trip, SearchDest, DriverTrans, BookingAlloc, DriverLoc,UserMultipleBooking,StrikeReason
from affiliate_b2b.affiliate_models import Affiliate, AffiliateWalletLogs, AffBookingLogs
from shapely.geometry import Point, Polygon
import time

from socketio_app import send_notification_to_channel, live_update_to_channel
from call_masking import assign_unique_did
from flasgger import swag_from
from live_update_booking import send_live_update_of_booking
from socketio_b2b import send_live_update_to_affiliate, send_notification_to_affiliate
from sqlalchemy.orm import aliased
from sqlalchemy.orm import sessionmaker

def convert_to_ist(dt):
    if dt is None:
        return None
    ist = pytz.timezone('Asia/Kolkata')
    return dt.astimezone(ist)

def combine_and_convert_to_ist(date_val, time_val):
    combined = datetime.datetime.combine(date_val, time_val)
    return convert_to_ist(combined) # timedelta(hours=5, minutes=30)

def split_date_time(dt):
    return dt.date(), dt.time()

ride = [120, 200, 270, 350, 430, 520]

bookride = Blueprint('bookride', __name__)


class DriverBook:
    MIN_HOURS = 6

    def __init__(self, id, fname, lname, mobile, pic, lastlat,
                 lastlong, rating, price, radius, reg=None, details_obj=None,
                 available=False, cancelled_count=0, last_month_trips=0, total_strike_count=0):
        self.id = id
        self.mobile = mobile
        self.name = fname + " " + lname
        self.pic = pic
        self.available = available
        self.homelat = lastlat
        self.homelong = lastlong
        self.rating = rating
        self.estimate = price[9]
        self.base_ch = price[1]
        self.night = price[2]
        self.dist_ch = price[3]
        self.booking_ch = price[4]
        self.car_ch = price[5]
        self.pre_tax = price[6]
        self.cgst = price[7]
        self.sgst = price[8]
        self.insurance = price[10]
        self.no_ins = price[0]
        self.driver_base_ch = price[13] if len(price) > 13 and price[13] else 0
        self.driver_night_ch = price[14] if len(price) > 14 and price[14] else 0
        self.radius = radius
        self.reg = reg
        self.details_obj = details_obj
        self.cancelled_count = int(cancelled_count)
        self.last_month_trips = int(last_month_trips)
        self.total_strike_count = int(total_strike_count)

    def set_score(self, score):
        self.score = round(score, 2)

    def set_ref(self, reflat, reflong):
        self.reflat = reflat
        self.reflong = reflong
        self.dist = distance_on_earth(self.homelat, self.homelong, self.reflat, self.reflong)

    def set_time(self, starttime, startdate, endtime, enddate, dur):
        self.starttime = str(starttime)
        self.startdate = str(startdate)
        self.endtime = str(endtime)
        self.enddate = str(enddate)
        self.dur = str(dur)


def latlong_bounds(lat, dist):
    # Constant approximation for lat & long
    LAT_DEG_DIST = 110.574
    LONG_DEG_DIST_BASE = 111.320
    # Convert lat to radians
    degrees_to_radians = math.pi / 180
    cos_lat = math.cos(lat * degrees_to_radians)
    # The diagonal distance is dist, so the distance along an axis is 1/sqrt(2) * dist
    dist_axis = 1.0 / math.sqrt(2) * dist
    lat_diff = dist_axis / LAT_DEG_DIST
    long_diff = dist_axis / (LONG_DEG_DIST_BASE * cos_lat)
    return lat_diff, long_diff


def compare(d1, d2):
    # DIST_THRESHOLD = 4
    if not (d1.reflat == d2.reflat and d1.reflong == d2.reflong): return 0
    return d1.dist - d2.dist


def compare_rating(d1, d2):
    # DIST_THRESHOLD = 4
    return d1.rating - d2.rating


def cmp_to_key(mycmp):
    'Convert a cmp= function into a key= function'

    class K(object):
        def __init__(self, obj, *args):
            self.obj = obj

        def __lt__(self, other):
            return mycmp(self.obj, other.obj) < 0

        def __gt__(self, other):
            return mycmp(self.obj, other.obj) > 0

        def __eq__(self, other):
            return mycmp(self.obj, other.obj) == 0

        def __le__(self, other):
            return mycmp(self.obj, other.obj) <= 0

        def __ge__(self, other):
            return mycmp(self.obj, other.obj) >= 0

        def __ne__(self, other):
            return mycmp(self.obj, other.obj) != 0

    return K

def get_driver_static_score_did(driver_id):
    driver_entry = db.session.query(Drivers, Users, DriverDetails). \
                        filter(Drivers.user == Users.id). \
                        filter(DriverDetails.driver_id == Drivers.id). \
                        filter(Drivers.id == driver_id).first()
    cancelled_count = db.session.query(func.count(DriverCancelled.id)). \
                        filter(DriverCancelled.driver == driver_id). \
                        first()
    last_month_date = datetime.datetime.utcnow() - datetime.timedelta(days=30)
    last_month_trips = db.session.query(func.count(Bookings.id)). \
                            filter(Bookings.driver == driver_id). \
                            filter(Bookings.startdate > last_month_date). \
                            filter(Bookings.type < 50). \
                            filter(Bookings.valid == 1).first()
    return get_driver_static_score(driver_entry[0], driver_entry[1],
                                   driver_entry[2], int(cancelled_count[0]),
                                   int(last_month_trips[0]))

def get_driver_static_score_new(driver, cancelled_count, last_month_trips):
    res = []

    if not driver.driver_available:
        res.append(-5)

    # Use a local variable for driver_rating and driver_trip_count
    driver_rating = driver.driver_rating if driver.driver_rating != 0 else 5
    driver_trip_count = driver.driver_trip_count if driver.driver_trip_count != 0 else 1

    # Calculate the rating score based on driver_trip_count
    if driver_trip_count == 0:
        rating_score = max(1.5, 5)
    elif driver_trip_count > 0 and driver_trip_count < 10:
        rating_score = max(0, (driver_rating - 3) * 2.5)
    else:
        if driver_rating < 4:
            rating_score = -5
        else:
            rating_score = (driver_rating - 4) * 5

    res.append(rating_score * 1.5)

    # Determine the approval timestamp
    approved = driver.driver_approval_ts if driver.driver_approval_ts else driver.driver_user_reg

    # Calculate last action and approval deltas
    last_action_date = driver.driver_det_timestamp
    last_action_delta = datetime.datetime.utcnow() - last_action_date
    approved_delta = datetime.datetime.utcnow() - approved

    # Calculate score based on driver_trip_count and last action delta
    if approved_delta.days < 30:
        score = min(10, driver_trip_count * 0.25 + random.randrange(10, 15) / 2)
    elif driver_trip_count < 5:
        if last_action_delta.days < 20:
            score = min(10, driver_trip_count * 0.25 + random.randrange(4, 8) / 2)
        elif driver_trip_count > 0:
            score = min(10, driver_trip_count * 0.25)
        else:
            score = -1 * min(5, last_action_delta.days * 0.05)
    else:
        score = min(10, driver_trip_count * 0.25)

    res.append(score / 2)

    # Score for how recent the last action was
    last_activity_score = min(max(9 - (last_action_delta.days - 10) * 0.45, -10), 10)
    res.append(last_activity_score)

    # Score for last month's trips
    if approved_delta.days < 30:
        score = min(10, last_month_trips * 0.5 + random.randrange(10, 15) / 2)
    elif last_month_trips == 0:
        score = -10
    else:
        score = min(10, last_month_trips * 0.5)

    res.append(score / 4)

    # Score for cancellation ratio
    if driver_trip_count > 0:
        cancelled_ratio = cancelled_count / driver_trip_count
        if cancelled_ratio < 0.2:
            res.append((0.2 - cancelled_ratio) * 25)
        else:
            if approved_delta.days >= 15 and driver_trip_count < 5:
                if cancelled_count < 3:
                    res.append(max(1 - cancelled_count, (0.2 - cancelled_ratio) * 25))
                else:
                    res.append(max(-5, (0.2 - cancelled_ratio) * 50))
            else:
                res.append(0)
    else:
        if approved_delta.days >= 30:
            res.append(max(-5, -1 * cancelled_count))
        else:
            res.append(0)

    return res

def get_driver_static_score(driver, user, details_obj, cancelled_count,
                            last_month_trips):
    res = []

    if not driver.available:
        res.append(-5)
    if driver.total_strike_count > 0 :
        # If the driver has strikes, penalize them by -2.5 for each strike
        res.append(driver.total_strike_count * -2.5) 
    if driver.rating == 0:
        driver.rating = 5
    if details_obj.ride_count == 0:
        rating_score = max(1.5, 5)
    elif details_obj.ride_count > 0 and details_obj.ride_count < 10:
        rating_score = max(0, (driver.rating - 3)*2.5)
    else:
        if driver.rating < 4:
            rating_score = -5
        else:
            rating_score = (driver.rating - 4) * 5
    res.append(rating_score * 1.5)

    if not details_obj.approval_ts:
        approved = user.reg
    else:
        approved = details_obj.approval_ts
    if not details_obj.reactivation_ts:
        reactivation_date = user.reg
    else:
        reactivation_date = details_obj.reactivation_ts
    # approx last action was on last change to details table
    # just a metric of activity that doesn't require booking table lookup
    # if a driver is reactivated after inactivity, just alter details table
    # somehow
    # -ve due needs to be considered
    if details_obj.ride_activing_ts:
        last_action_date = details_obj.ride_activing_ts
    else:
        last_action_date = details_obj.timestamp
    
    last_action_delta = datetime.datetime.utcnow() - last_action_date
    approved_delta = datetime.datetime.utcnow() - approved
    reactivation_delta = datetime.datetime.utcnow() - reactivation_date

    if approved_delta.days < 30 or reactivation_delta.days < 30 :
        # new driver, give random good score
        score = min(10, details_obj.ride_count * 0.25
                    + random.randrange(10, 15)/2)
    elif details_obj.ride_count < 5:
        if last_action_delta.days < 20:
            # new driver, give random good score
            score = min(10, details_obj.ride_count * 0.25
                        + random.randrange(4, 8)/2)
        elif details_obj.ride_count > 0:
            score = min(10, details_obj.ride_count * 0.25)
        else:
            # For old drivers, not doing trips is bad
            score = -1 * min(5, last_action_delta.days * 0.05)
    else:
        score = min(10, details_obj.ride_count * 0.25)
    res.append(score/2)

    # then score for how recent last action was
    last_activity_score = min(max(9 - (last_action_delta.days - 10)*0.45, -10), 10)
    res.append(last_activity_score)

    # score for last month trips
    if approved_delta.days < 30:
        # new driver, give random good score
        score = min(10, last_month_trips * 0.5
                    + random.randrange(10, 15)/2)
    elif last_month_trips == 0:
        # Effectively inactive
        score = -10
    else:
        score = min(10, last_month_trips * 0.5)
    res.append(score/4)

    if details_obj.ride_count > 0:
        cancelled_ratio = cancelled_count / details_obj.ride_count
        if cancelled_ratio < 0.2:
            res.append((0.2 - cancelled_ratio) * 25)
        else:
            if approved_delta.days >= 15 and details_obj.ride_count < 5:
                if cancelled_count < 3:
                    res.append(max(1 - cancelled_count, (0.2 - cancelled_ratio) * 25))
                else:
                    res.append(max(-5, (0.2 - cancelled_ratio) * 50))
            else:
                res.append(0)
    else:
        if details_obj.ride_count == 0:
            details_obj.ride_count = 1
        if approved_delta.days >= 30:
            res.append(max(-5, -1 * cancelled_count))
        else:
            res.append(0)
    return res

def can_book_search_entry(search, user_id):
    user_mul_booking = db.session.query(UserMultipleBooking).filter(UserMultipleBooking.user == user_id).first()
    if user_mul_booking:
        return True
    start_time = datetime.datetime(search.date.year, search.date.month, search.date.day, search.time.hour,
                                  search.time.minute, search.time.second)
    end_time = (datetime.datetime(search.date.year, search.date.month, search.date.day, search.time.hour,
                                  search.time.minute, search.time.second)
                + datetime.timedelta(search.days, search.dur.hour * 3600 + search.dur.minute * 60 + search.dur.second))
    have_booking = Bookings.query.filter(Bookings.user == user_id).outerjoin(Trip, Bookings.id == Trip.book_id).filter(
              or_(
                        Trip.status!=Trip.TRIP_STOPPED,
                        Trip.status==None
                    ),
            Bookings.valid >= 0,
            or_(
                and_(
                    Bookings.startdate == end_time.date(),
                    Bookings.starttime < end_time.time()
                ),
                Bookings.startdate < end_time.date()
            ),
            or_(
                    and_(
                        Bookings.enddate == start_time.date(),
                        Bookings.endtime > start_time.time()
                    ),
                    Bookings.enddate > start_time.date()
            )
        )
    hav_book = have_booking.all()
    print(
        "final search query",
        have_booking.statement.compile(
            db.engine,
            dialect=mysql.dialect(),
            compile_kwargs={"literal_binds": True}
        ),
        flush=True
    )

    # have_booking = Bookings.query.filter(Bookings.user == user_id).filter(
    #     or_(and_(Bookings.startdate == end_time.date(), Bookings.starttime < end_time.time()),
    #         (Bookings.startdate < end_time.date()))). \
    #     filter(or_(and_(Bookings.enddate == search.date, Bookings.endtime > search.time),
    #                Bookings.enddate > end_time.date())).filter(Bookings.valid >= 0).all()
    return (not hav_book)


def get_best_aux(_region=None, city=Regions.REGN_KOLKATA):
    all_drivers = db.session.query(Drivers, Users, DriverInfo).filter(Drivers.user == Users.id). \
                    filter(Drivers.approved >= 1).filter(Drivers.perma == False). \
                    filter(Users.region == city).filter(DriverInfo.driver_id == Drivers.id)

    return [a[0].id for a in all_drivers]


def get_best_book(book, user_id=-1, is_functional=False,
             cur_price=None,
             num_to_select=BookingParams.SEL_DRIVER_COUNT,
             cur_radius=0, pref_based=True, sim_mode=False):
    search = db.session.query(DriverSearch, Bookings).filter(DriverSearch.id == Bookings.search_key).filter(Bookings.id == book).first()
    return get_best(search[0], search[1].user, is_functional,
             cur_price,
             num_to_select,
             cur_radius, pref_based, sim_mode)

def get_best(search, user_id, is_functional=False, is_immediate=False,
             cur_price=None, num_to_select=BookingParams.SEL_DRIVER_COUNT,
             cur_radius=0, pref_based=True, sim_mode=False, is_b2b=False):

    trip_type = search.type
    city = search.region
    if trip_type == BookingParams.TYPE_OUTSTATION or trip_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
        print("Entering outstation path", flush=True)
        num_to_select //= 2
        bottom_half = False
        os = True
    else:
        os = False
        bottom_half = True
    # Calculate end_time and end_time_w_buffer
    start_time = datetime.datetime(search.date.year, search.date.month, search.date.day, search.time.hour,
                                  search.time.minute, search.time.second)
    end_time = (datetime.datetime(search.date.year, search.date.month, search.date.day, search.time.hour,
                                  search.time.minute, search.time.second) \
                + datetime.timedelta(search.days, search.dur.hour * 3600 + search.dur.minute * 60 + search.dur.second))

    end_time_w_buffer = end_time + datetime.timedelta(seconds=BookingParams.BUFFER_MINUTES*60)
    start_time_w_buffer = start_time - datetime.timedelta(seconds=BookingParams.BUFFER_MINUTES*60)

    # removed have booking code just added comment for reference

    cur_radius = BookingParams.BOOKING_RADIUS[city]
    city_legal = Regions.driver_service_regions(city)
    ll_bounds = latlong_bounds(search.reflat, cur_radius)
    ne = (search.reflat + ll_bounds[0], search.reflong + ll_bounds[1])
    sw = (search.reflat - ll_bounds[0], search.reflong - ll_bounds[1])

    results = None
    driver_aux = []
    if not sim_mode:
        # st=tm.time()
        #below query takes around 0.018 seconds and include trip check too
        booked_query = db.session.query(Bookings.driver).outerjoin(Trip, Bookings.id == Trip.book_id).filter(
              or_(
                        Trip.status!=Trip.TRIP_STOPPED,
                        Trip.status==None
                    ),
            Bookings.valid > 0,
            or_(
                and_(
                    Bookings.startdate == end_time_w_buffer.date(),
                    Bookings.starttime < end_time_w_buffer.time()
                ),
                Bookings.startdate < end_time_w_buffer.date()
            ),
            or_(
                    and_(
                        Bookings.enddate == start_time_w_buffer.date(),
                        Bookings.endtime > start_time_w_buffer.time()
                    ),
                    Bookings.enddate > start_time_w_buffer.date()
            )
        )

    #     print(
    #     "final search query",
    #     booked_query.statement.compile(
    #         db.engine,
    #         dialect=mysql.dialect(),
    #         compile_kwargs={"literal_binds": True}
    #     ),
    #     flush=True
    # )

        booked = set([drivers.driver for drivers in booked_query.all()])
        # print("Time taken to check booking:", tm.time() - st)
        # print(booked)
    else:
        booked = []

    dist = search.dist
    last_month_date = datetime.datetime.utcnow() - datetime.timedelta(days=30)

    if not cur_price:
        if os:
            cur_price = PriceOutstation.get_price(
                search.date, end_time.date(), 1, search.days * 24 + search.dur.hour,
                search.car_type, trip_type, dist, insurance=search.insurance,
                insurance_num=search.insurance_num if search.insurance_num else search.insurance, city=city)
        else:
            if is_b2b:
                affiliate = db.session.query(Affiliate).filter(Affiliate.id == search.affiliate).first()
                if not affiliate:
                        cur_price = None
                cur_price = Price.get_price(BookingParams.TYPE_B2B, str(search.dur), search.time, end_time.time(),
                                search.dist, search.car_type, search.date, end_time.date(), 0,
                                client_name=affiliate.client_name, client_trip_type=search.trip_type)
            else:
                cur_price = Price.get_price(
                    trip_type, search.dur.hour + search.dur.minute/60, search.time, end_time,
                    dist, search.car_type, search.date, end_time.date(), 0,
                    insurance=search.insurance_num if search.insurance_num else search.insurance, insurance_num=search.insurance_num, city=city)

    all_details = db.session.query(Users, Drivers, DriverInfo, DriverDetails)\
                    .filter(Drivers.user == Users.id)\
                    .filter(Drivers.approved >= 1)\
                    .filter(Drivers.perma == False)\
                    .filter(DriverDetails.driver_id == Drivers.id)\
                    .filter(Users.region.in_(city_legal))\
                    .filter(DriverInfo.driver_id == Drivers.id)

    # print("booking type",is_b2b ,flush=True)
    if is_b2b:
        print("Filtering for B2B drivers", flush=True)
        bit_position = 0
        bit_mask = 1 << bit_position
        all_details = all_details.filter((DriverInfo.driver_trip_pref.op('&')(bit_mask) != 0))

    else:
        print("Filtering for B2C drivers", flush=True)
        bit_position = 1
        bit_mask = 1 << bit_position
        all_details = all_details.filter((DriverInfo.driver_trip_pref.op('&')(bit_mask) != 0))

    all_details = all_details.all()
    if not is_b2b:
        type_filter = Bookings.type < BookingParams.TYPE_C24
    else:
        type_filter = and_(Bookings.type > BookingParams.TYPE_B2B,
                        Bookings.type < BookingParams.TYPE_C24)

    all_last_month_query = (
        db.session.query(Drivers.id, func.count(Bookings.id))
        .filter(Drivers.approved >= 1)
        .filter(Drivers.perma == False)
        .filter(Bookings.driver == Drivers.id)
        .filter(Bookings.valid == 1)
        .filter(Bookings.startdate > last_month_date)
        .filter(type_filter)
        .group_by(Drivers.id)
        .all()
    )
    all_last_month = {e[0]: e[1] for e in all_last_month_query}
    all_cancelled_query = db.session.query(Drivers.id, func.count(DriverCancelled.id))\
                    .filter(Drivers.approved >= 1)\
                    .filter(Drivers.perma == False)\
                    .filter(DriverCancelled.driver == Drivers.id)\
                    .group_by(Drivers.id)\
                    .all()
    all_cancelled = {e[0]: e[1] for e in all_cancelled_query}

    if not all_details:
        print("No driver details found. Returning True, None", flush=True)
        return True, None

    if hasattr(search, 'dest') and search.dest:
        ll_bounds_dest = latlong_bounds(search.dest.lat, cur_radius)
        ne_dest = (search.dest.lat + ll_bounds_dest[0], search.dest.lng + ll_bounds_dest[1])
        sw_dest = (search.dest.lat - ll_bounds_dest[0], search.dest.lng - ll_bounds_dest[1])

    for details in all_details:
        if not sim_mode:
            if details[1].id in booked:
                continue
            # Check driver's current location bounds
            if (details[2].pres_addr_lat >= ne[0] or details[2].pres_addr_lng >= ne[1] or
                details[2].pres_addr_lat <= sw[0] or details[2].pres_addr_lng <= sw[1]):
                if hasattr(search, 'dest'):
                    if not search.dest:
                        print("Illegal dest entry for driver", details[1].id, flush=True)
                        continue
                    if (details[2].pres_addr_lat >= ne_dest[0] or details[2].pres_addr_lng >= ne_dest[1] or
                        details[2].pres_addr_lat <= sw_dest[0] or details[2].pres_addr_lng <= sw_dest[1]):
                        print("Driver", details[1].id, "outside destination bounds, skipping", flush=True)
                        continue
                else:
                    continue
        driver_obj = DriverBook(
            details[1].id, details[0].fname, details[0].lname, details[0].mobile,
            details[1].pic, details[2].pres_addr_lat, details[2].pres_addr_lng,
            details[1].rating, cur_price, 0, details[0].reg, details[3],
            details[1].available, all_cancelled.get(details[1].id, 0),
            all_last_month.get(details[1].id, 0), details[1].total_strike_count
        )
        driver_aux.append(driver_obj)

    if is_immediate:
        n_minutes_ago = datetime.datetime.now() - datetime.timedelta(minutes=BookingParams.MAX_LOC_MINUTES_AGO)
        driver_locs = db.session.query(DriverLoc)\
                            .filter(DriverLoc.timestamp >= n_minutes_ago)\
                            .order_by(desc(DriverLoc.timestamp))\
                            .all()
        user_lat = search.reflat
        user_lng = search.reflong
        drivers_with_distances = {}
        driver_max_distance = BookingParams.MAX_DIST_DRIV_IMM
        for driver_loc in driver_locs:
            driver_lat = driver_loc.lat
            driver_lng = driver_loc.lng
            distance = distance_on_earth(driver_lat, driver_lng, user_lat, user_lng)
            if distance <= driver_max_distance:
                drivers_with_distances[driver_loc.driver_id] = distance
        closest_drivers = []
        for driver in driver_aux:
            if driver.id in drivers_with_distances:
                closest_drivers.append(driver)
        driver_aux = closest_drivers

    driv_score = {}
    for driver in driver_aux:
        driver.set_ref(search.reflat, search.reflong)
        driv_score[driver.id] = []

    if pref_based:
        all_customer_trips = db.session.query(Users, Bookings)\
                                .filter(Users.id == Bookings.user)\
                                .filter(Users.id == user_id)\
                                .all()
        driv_cust_score = {}
        for t in all_customer_trips:
            driver_id = t[1].driver
            if driver_id not in driv_score:
                continue
            if driver_id not in driv_cust_score:
                driv_cust_score[driver_id] = []
            if t[1].valid < 0:
                driv_cust_score[driver_id].append(-2.5)
            elif t[1].valid:
                if is_b2b:
                   driv_cust_score[driver_id].append(0)
                else:
                    driv_cust_score[driver_id].append(max(-5, (t[1].user_rating - 3) * 2.5))

        for driv, scores in driv_cust_score.items():
            driv_score[driv].append(sum(scores)/len(scores))

    for driver in driver_aux:
        if driver.dist < 4:
            driv_score[driver.id].append(5)
        elif driver.dist < 8:
            driv_score[driver.id].append(3.5)
        elif driver.dist < 12:
            driv_score[driver.id].append(1.5)
        else:
            driv_score[driver.id].append(-1 * (driver.dist - 12) * 0.5)
    if is_b2b:
        b2b_counts = dict(
        db.session.query(
            Bookings.driver,
            func.count(Bookings.id)
            )
            .filter(Bookings.type >= BookingParams.TYPE_C24)
            .filter(Bookings.valid == 1)
            .group_by(Bookings.driver)
            .all()
        )
        same_b2b_counts = dict(
        db.session.query(
            Bookings.driver,
            func.count(Bookings.id)
            )
            .join(AffBookingLogs, AffBookingLogs.book_id == Bookings.id)
            .filter(AffBookingLogs.aff_id == user_id)
            .filter(Bookings.type >= BookingParams.TYPE_C24)
            .filter(Bookings.valid == 1)
            .group_by(Bookings.driver)
            .all()
        )
    for driver in driver_aux:
        static_score = get_driver_static_score(driver, driver, driver.details_obj, driver.cancelled_count,
                                                 driver.last_month_trips)
        driv_score[driver.id] += static_score
        if is_b2b:
            num_b2b = b2b_counts.get(driver.id, 0)
            num_same_b2b = same_b2b_counts.get(driver.id, 0)
            driv_score[driver.id].append(min(10, num_b2b * 0.5))   # 20 trips for max rating
            driv_score[driver.id].append(min(10, num_same_b2b * 0.25))  # 20 trips for max rating
        # print("Driver", driver.id, "static score:", static_score,driv_score[driver.id], min(10, num_b2b * 0.5),min(10, num_same_b2b * 0.25))

    for driver in driver_aux:
        total_score = sum(driv_score[driver.id])
        print("Driver", driver.id, "(", driver.name, "): scores:", driv_score[driver.id], "total:", total_score, flush=True)
        driv_score[driver.id] = total_score

    driv_score_sorted = sorted(driv_score.items(), key=lambda x: x[1], reverse=True)
    driver_aux_sorted = []
    driver_aux_dict = {d.id: d for d in driver_aux}
    for k, v in driv_score_sorted:
        driver_aux_sorted.append(driver_aux_dict[k])
        driver_aux_sorted[-1].set_score(v)

    print("Driver sorted order:", [d.id for d in driver_aux_sorted], flush=True)

    if sim_mode:
        num_to_select = len(driver_aux_sorted)

    if bottom_half and len(driver_aux_sorted) > num_to_select + 30:
        results_top_half = driver_aux_sorted[:num_to_select//2]
        results_bottom_half = random.sample(driver_aux_sorted[num_to_select//2:num_to_select + 30],
                                            num_to_select - num_to_select//2)
        results = results_top_half + results_bottom_half
    else:
        results = driver_aux_sorted[:num_to_select]

    for result in results:
        result.set_time(search.time, search.date, end_time.time(), end_time.date(), search.dur)
    return True, results


def get_best_outstation(search, user_id, trip_type=BookingParams.TYPE_OUTSTATION, city=Regions.REGN_KOLKATA, is_functional=False, cur_price=None,
                        num_to_select=BookingParams.SEL_DRIVER_COUNT):
    raise NotImplementedError()

def create_pricing_entry(driver, book):
    try:
        pending_pricing = BookPricing(bid=book.id, est=driver.estimate,
                               base=driver.base_ch, est_pre_tax=driver.pre_tax,
                               cgst=driver.cgst, sgst=driver.sgst,
                               cartype=driver.car_ch, night=driver.night, food=0,
                               booking=driver.booking_ch, dist=driver.dist_ch,
                               insurance=driver.insurance, driver_base=driver.driver_base_ch,
                               driver_night=driver.driver_night_ch)

        db.session.add(pending_pricing)
        db.session.flush()
        return True

    except exc.IntegrityError:
        db.session.rollback()
        return True

    except Exception as e:
        db.session.rollback()
        print(e)
        return False

def create_pending_entry(driver, book, pending_state, score=-99):
    if book.valid != Bookings.UNALLOCATED:
        print("Skipping", driver.id, "for booking", book.id, "because allocated")
        return False
    booking_start_minus_n_minutes, booking_end_plus_n_minutes = check_ongoing_time(book)
    """
    booked = set([drivers.driver for drivers in
                Bookings.query.filter(
                    Bookings.valid > 0
                ).filter(
                    or_(
                        func.timestamp(Bookings.startdate, Bookings.starttime) <= booking_end_plus_n_minutes,
                        Bookings.startdate < book.enddate
                    )
                ).filter(
                    or_(
                        func.timestamp(Bookings.enddate, Bookings.endtime) >= booking_start_minus_n_minutes,
                        Bookings.enddate > book.enddate
                    )
                ).all()])
    """
    booked = []
    if driver.id in booked:
        return False

    try:
        #pb = db.session.query(BookPending).filter(BookPending.book_id==book.id).filter(BookPending.driver==driver.id)
        #if pb.first():
        #    pb.update({BookPending.valid: pending_state})
        #else:
        pending_book = BookPending(bid=book.id, did=driver.id, state=pending_state, score=score)
        db.session.add(pending_book)
        db.session.flush()
    except Exception as e:
        db.session.rollback()
        print("Exception in creating pending booking:", e)
        return False
    return True

def create_pending_entry_loop(driver, book, pending_state, dt_ist):
    notify = False
    try:
        create_success = create_pending_entry(driver, book, pending_state)
        if not create_success:
            return
        available = db.session.query(Drivers).filter(Drivers.id == driver.id).first().available
        if available and pending_state == BookPending.BROADCAST:
            notify = True
    except (exc.InvalidRequestError, exc.IntegrityError, exc.OperationalError) as e:
        db.session.rollback()
        print("Exception in creating entry", e)
        pass
    if notify:
        try:
            send_fcm_msg_driver_request(driver.id, title="New booking!",
                                        smalltext="You have a new booking at " + \
                                                   str(dt_ist.strftime("%d/%m/%Y %I:%M %p")),
                                        bigtext="You have a new booking at " + \
                                                str(dt_ist.strftime("%d/%m/%Y %I:%M %p")) + \
                                                " with estimate of ₹" + str(driver.estimate) + \
                                                ". Open app to accept trip.")

        except Exception as e:
            print(e)
            pass

def create_pending_entries_threaded(book, book_id, loc, pending_state, drivers, cur_search, user):
    drivers = list(set(drivers))
    drivers = random.sample(drivers, len(drivers))
    dt_ist = get_dt_ist(cur_search.date, cur_search.time)
    for driver in drivers:
        thread = Thread(target=create_pending_entry_loop, args=[
            driver, book, pending_state, dt_ist])
        thread.start()
    try:
        user_name = db.session.query(Users).filter(Users.id == user).first().get_name()

        start_time_ist = datetime.datetime(cur_search.date.year, cur_search.date.month,
                                           cur_search.date.day, cur_search.time.hour,
                                           cur_search.time.minute, cur_search.time.second) + \
                         _sms.IST_OFFSET_TIMEDELTA

        estimate = drivers[0].estimate
        msg_content = "#" + str(book.id) + ": " + user_name + " booked for " + str(cur_search.dur.hour) + " hours on " + \
                      start_time_ist.strftime("%I:%M %p %d/%m/%Y") + " from " + loc + ". The estimate is Rs " + \
                      str(estimate) + "."
        #msg_content += " The predicted region is " + LocStr.get_loc_str(cur_search.reflat,
        #                                                                cur_search.reflong) + "."
        msg_content += " This is their " + str(db.session.query(Bookings.id).filter(Bookings.user == user).count()) + "th booking"
        msg_content += " (" + str(len(db.session.query(Bookings, Trip).filter(Bookings.user == user).filter(Trip.book_id == Bookings.id).all())) + " successful)."
        send_slack_msg(1, msg_content)
    except Exception as e:
        print(e)
        pass


def create_pending_entries(book, book_id, loc, pending_state, drivers, cur_search, user):
    drivers = list(set(drivers))
    drivers = random.sample(drivers, len(drivers))
    driver_notify_list = []
    dt_ist = get_dt_ist(cur_search.date, cur_search.time)
    success = 0
    pending_entries=[]
    notifications = []

    driver_ids = [driver.id for driver in drivers]
    # Query all tokens for these drivers in one go
    token_objs = db.session.query(DriverFCM).filter(DriverFCM.driver_id.in_(driver_ids)).all()

    # Build a dictionary mapping driver_id to list of tokens
    driver_tokens = {}
    if book.type==BookingParams.TYPE_B2B:
        is_b2b=True
    else:
        is_b2b=False
    for token_obj in token_objs:
        driver_tokens.setdefault(token_obj.driver_id, []).append(token_obj.token)
    for driver in drivers:
        try:
            # booked = []
            if book.valid != Bookings.UNALLOCATED:
                print("Skipping driver", driver.id, "for booking", book.id, "because allocated", flush=True)
                continue
            existing_entry = BookPending.query.filter_by(book_id=book.id, driver=driver.id).first()
            if not existing_entry:
                pending_book = BookPending(bid=book.id, did=driver.id, state=pending_state, score=driver.score)
                pending_entries.append(pending_book)
            success += 1
            available = db.session.query(Drivers).filter(Drivers.id == driver.id).first().available
            if available and pending_state == BookPending.BROADCAST:
                tokens_list = driver_tokens.get(driver.id, [])
                if not tokens_list:
                    print("No tokens found for driver", driver.id)
                    continue
                for token in tokens_list:
                    notif = {
                        'token': token,
                        'title': "New booking! "+book.code,
                        'small_text': "You have a new booking at " + dt_ist.strftime("%d/%m/%Y %I:%M %p"),
                        'big_text': "You have a new booking at " + dt_ist.strftime("%d/%m/%Y %I:%M %p") +
                                    " with estimate of ₹" + str(driver.estimate if not is_b2b else (driver.driver_base_ch + driver.driver_night_ch)) + ". Open app to accept trip.",
                        'pending': True
                    }
                    notifications.append(notif)
            estimate = driver.estimate
        except (exc.InvalidRequestError, exc.IntegrityError, exc.OperationalError) as e:
            db.session.rollback()
            print("Exception in creating entry", e)
            pass
    try:
        db.session.add_all(pending_entries)
        db.session.commit()
    except Exception as e:
            print("Exception in bulk commit of pending bookings:", e, flush=True)
            db.session.rollback()
    try:
        if notifications:
            send_bulk_notifications(notifications)
        else:
            print("No notifications to send.")
    except Exception as e:
        print("Exception in sending notifications:", e)
        pass
    return success

def check_booking_type(book_type, pointsrc, pointdest, polygon_city):
    if book_type == BookingParams.TYPE_ONEWAY:
        # Both destination and source points must be inside or on the boundary
        return (polygon_city.contains(pointdest) or pointdest.touches(polygon_city)) and \
               (polygon_city.contains(pointsrc) or pointsrc.touches(polygon_city))

    elif book_type in (BookingParams.TYPE_MINIOS_ONEWAY, BookingParams.TYPE_OUTSTATION_ONEWAY):
        # One point is within or touching the polygon, the other is not
        #(also we allow if both src and dest are inside)
        return ((polygon_city.contains(pointdest) or pointdest.touches(polygon_city)) and \
                (polygon_city.contains(pointsrc) or pointsrc.touches(polygon_city))) or \
               (((pointdest.within(polygon_city) or pointdest.touches(polygon_city)) and \
                not pointsrc.within(polygon_city)) or \
               (not pointdest.within(polygon_city) and \
               (pointsrc.within(polygon_city) or pointsrc.touches(polygon_city))))

    else:
        # For other booking types, only the src point is checked
        return polygon_city.contains(pointsrc) or pointsrc.touches(polygon_city)

def point_inside(dest_long, dest_lat, src_long, src_lat, book_type, location_map, city):
    try:
        cityy, cityx = location_map[city]
    except KeyError:
        # If city is not in location_map, return False
        return False
    pointdest = Point(float(dest_long), float(dest_lat))
    pointsrc = Point(float(src_long), float(src_lat))
    country_map=CountryBoundary.COUNTRY_BOUNDARIES
    indiay,indiax=country_map[CountryBoundary.INDIA]
    polygon_coords_india = list(zip(indiay, indiax))
    polygon_coords_city = list(zip(cityy, cityx))
    polygon_india = Polygon(polygon_coords_india)
    polygon_city = Polygon(polygon_coords_city)

    # Check if the book_type is 1 (check only the source)
    if book_type == 1:
        if not pointsrc.within(polygon_india):
            return False
    else:
        # Check if both the source and destination are within India's boundaries
        if not pointsrc.within(polygon_india) or not pointdest.within(polygon_india):
            return False
    # check booking type
    return check_booking_type(book_type, pointsrc, pointdest, polygon_city)

def point_inside_polygon(dest_long, dest_lat, src_long, src_lat, book_type, locations_data, city):
    try:
        if city in Regions.ENABLED_CITIES:
            return point_inside(dest_long, dest_lat, src_long, src_lat, book_type, locations_data, city)
        return False
    except (ValueError, KeyError):
        # Handle invalid coordinates or missing city data gracefully
        return False

def _get_estimate(user, search_id, book_type, car_type, time, dur, reflat, reflong, dest_lat, dest_long, dest_loc, dest_exists, insurance=0, ninsurance=1, city=Regions.REGN_KOLKATA, source='android'):
    locations_data = Regions.LOCATION_MAP
    if not point_inside_polygon(dest_long, dest_lat, reflong, reflat, book_type, locations_data, city):
        return jsonify({'success': -1, 'message': 'Location is outside service area'})
    st_time = tm.time()
    time_arr = []
    time_dur = datetime.datetime.strptime(dur, "%H:%M:%S")
    if BookingParams.is_cust_booktype(book_type) and (book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY) and \
            (time_dur.hour > BookingParams.BOOKING_MAX_DUR or time_dur.hour < BookingParams.BOOKING_MIN_DUR):
        return jsonify({'success': -1, 'id': -1, 'message': 'Invalid booking type or booking duration'}), 200
    date = time.date()
    now = datetime.datetime.utcnow()
    ist = pytz.timezone('Asia/Kolkata')
    if book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
        # encoding: dd:hh:00
        dur = str(time_dur.minute) + ":00:00"
        days = time_dur.hour
        end_time = (datetime.datetime(date.year, date.month, date.day, time.hour, time.minute, time.second)
                    + datetime.timedelta(days, time_dur.minute*3600))
    else:
        days = 0
        end_time = (datetime.datetime(date.year, date.month, date.day, time.hour, time.minute, time.second)
                    + datetime.timedelta(0, time_dur.hour * 3600 + time_dur.minute * 60 + time_dur.second))
    time_arr.append(tm.time() - st_time)
    st_time = tm.time()
    search_entry = DriverSearch(search_id, user, car_type, reflat, reflong, time.time(), date, dur, now, book_type, days,ninsurance, city, source=source)
    if dest_exists:
        min_dist = max_dist = get_dist_on_map(reflat, reflong, dest_lat, dest_long)
    else:
        min_dist = max_dist = 0
    search_entry.dist = max_dist
    time_arr.append(tm.time() - st_time)
    st_time = tm.time()
    # HARD CODED FOR ROUND TRIP
    try:
        db.session.add(search_entry)
        db.session.commit()
        if dest_exists:
            search_dest = SearchDest(search_id, dest_lat, dest_long, dest_loc)
            db.session.add(search_dest)
            db.session.commit()
        time_arr.append(tm.time() - st_time)
        st_time = tm.time()
        user_details_estimate=db.session.query(Users).filter(Users.id == user).first()
        user_name=""
        if user==1:
            user_name="Unknown"
        else:
            user_name=user_details_estimate.get_name()
        recent_search_data = db.session.query(
            func.min(func.timestamp(DriverSearch.date, DriverSearch.time)).label('earliest_search_datetime'),
            func.max(DriverSearch.timestamp).label('recent_search_timestamp')
        ).filter(
                or_(
                    DriverSearch.date > now.date(),
                    and_(DriverSearch.date == now.date(), DriverSearch.time > now.time())
                ),
                DriverSearch.type < 50,
                DriverSearch.user == user,
                DriverSearch.region==city
            ).first()
    # Calculate booking details
        completed_bookings = db.session.query(func.count()).filter(
            Bookings.user == user,
            Bookings.enddate <  datetime.datetime.utcnow().date(),
            Bookings.valid == 1
        ).scalar()
        upcoming_bookings = db.session.query(func.count()).filter(
            Bookings.user == user,
            or_(
                Bookings.enddate > datetime.datetime.utcnow().date(),
                and_(Bookings.enddate ==  datetime.datetime.utcnow().date(), Bookings.endtime >  datetime.datetime.utcnow().time())
            )
        ).scalar()
        # Bad hack to handle night threshold
        if BookingParams.is_cust_booktype(book_type) and book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY:
            min_price = Price.get_price(book_type, time_dur.hour + time_dur.minute/60, time, end_time, min_dist, car_type,
                                        date, end_time.date(), 0, insurance=insurance,
                                        insurance_num=ninsurance, city=city)
            ot_rates = Price.get_ot_rates(date, end_time.date(), city=city)
            night, part_night = is_trip_night(time.hour, end_time.hour)
            try:
                user_details = db.session.query(Users).filter(Users.id == user).first()
                msg_content = "*" + user_details.get_name() + " searched for a trip " + \
                              " (type= " + str(book_type) + ") from " + \
                              str(datetime.datetime(date.year, date.month, date.day, time.hour, time.minute,
                                                    time.second)) + \
                              " to " + str(end_time) + " with estimate " + str(min_price) +"*"
                send_slack_msg(10, msg_content)
            except Exception as e:
                print(e)
                pass
            time_arr.append(tm.time() - st_time)
            st_time = tm.time()
            live_update_data = {
                "userid": user,
                "name": user_name,
                "contactNumber": user_details_estimate.mobile,
                "region": city,
                "recentBookings": recent_search_data.earliest_search_datetime.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.earliest_search_datetime else None,
                "latestSearch": recent_search_data.recent_search_timestamp.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.recent_search_timestamp else None,
                "completed": completed_bookings,
                "upcoming": upcoming_bookings,
                "remark": ''
            }
            live_update_to_channel(live_update_data, room_name='estimate', type='estimate', region=city, channel= 'new_estimate')
            return jsonify({'success': 1, 'id': search_id, 'min_est': min_price[9], 'max_est': min_price[9], 'base_ch': min_price[1] + min_price[3] + min_price[4] + min_price[5],
                            'night': min_price[2],
                            'dist_ch': 0, 'booking_ch': 0,
                            'car_ch': 0,
                            'pretax': min_price[6],
                            'cgst': min_price[7],
                            'sgst': min_price[8],
                            'insurance': min_price[10],
                            'no_ins_price': min_price[0],
                            'surcharge': 0,
                            'ot_rate_0': ot_rates[0], 'ot_rate_1': ot_rates[1], 'ot_rate_2': ot_rates[2],
                            'night_1': int(part_night),
                            'night_2': int(night), 'sgst_pct': Price.SGST, 'cgst_pct': Price.CGST,
                            'time': time_arr})
        elif book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            # dist = get_dist_on_map(reflat, reflong, dest_lat, dest_long)
            min_price = PriceOutstation.get_price(date, end_time.date(), 1, time_dur.hour * 24 + time_dur.minute, car_type, book_type, 
                                min_dist, insurance=insurance, insurance_num=ninsurance, city=city)
            ot_rates = PriceOutstation.get_ot_rates(date, end_time.date())
            time_arr.append(tm.time() - st_time)
            st_time = tm.time()
            try:
                user_details = db.session.query(Users).filter(Users.id == user).first()
                msg_content = user_details.get_name() + " searched for a trip " + \
                              " (type= " + str(book_type) + ") from " + \
                              str(datetime.datetime(date.year, date.month, date.day, time.hour, time.minute,
                                                    time.second)) + \
                              " to " + str(end_time) + " with estimate " + str(min_price)
                send_slack_msg(10, msg_content)
            except Exception as e:
                print(e)
                pass
            time_arr.append(tm.time() - st_time)
            st_time = tm.time()
            estimate_data = {
                    "userid": user,
                    "name": user_name,
                    "contactNumber": user_details_estimate.mobile,
                    "region": city,
                    "recentBookings": recent_search_data.earliest_search_datetime.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.earliest_search_datetime else None,
                    "latestSearch": recent_search_data.recent_search_timestamp.astimezone(ist).strftime("%d %b, %y, %H:%M:%S") if recent_search_data.recent_search_timestamp else None,
                    "completed": completed_bookings,
                    "upcoming": upcoming_bookings,
                    "remark": ''
                }

            live_update_to_channel(estimate_data, room_name='estimate', type='estimate', region=city, channel= 'new_estimate')
            return jsonify({
                    'success': 1, 'id': search_id, 'min_est': min_price[9], 'max_est': min_price[9], 'base_ch': min_price[1] + min_price[4] + min_price[5] + min_price[3],
                    'night': 0,
                    'dist_ch': 0, 'booking_ch': 0,
                    'car_ch': 0,
                    'pretax': min_price[6],
                    'cgst': min_price[7],
                    'insurance': min_price[10],
                    'no_ins_price': min_price[0],
                    'sgst': min_price[8],
                    'ot_rate': ot_rates, 'sgst_pct': Price.SGST, 'cgst_pct': Price.CGST,
                    'time': time_arr
                })
    except exc.IntegrityError as e:
        print(e)
        db.session.rollback()
        return jsonify({'id': -1, 'message': 'DB Error'})

def allocate_driver_to_booking(book, book_id, loc, pending_state, drivers,
                               cur_search, user, cur_price):
    try:
        can_book, drivers = get_best(search=cur_search, user_id=user,
                                     is_functional=True, cur_price=cur_price)
    except (exc.InvalidRequestError, exc.IntegrityError, exc.OperationalError) as e:
        print(e)
        db.session.rollback()
        return False
    if not drivers or len(drivers) < 1:
        return False
    if book_id:
        create_pending_entries(book, book_id, loc, pending_state, drivers,
                               cur_search, user)
    return True


@bookride.route('/api/book', methods=['POST'])
@swag_from('/app/swagger_docs/booking/book_driver.yml')
@jwt_required()
def book_driver():
    USER_BANNED_LIST = [5489, 49392]
    try:
        user = get_jwt_identity()
    except Exception:
        return jsonify({'result': -1, 'message': 'Failed to get indentity'}), 401
    claims = get_jwt()
    if claims['roles'] == Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Invalid role - Driver'}), 401
    if not account_enabled(user):
        return jsonify({'result': -1, 'message': 'User restricted'}), 401
    if not complete(request.form, ['search_id']):
        return jsonify({'result': -1, 'message': 'Incomplete form details'}), 201
    if 'payment_type' not in request.form:
        payment = PaymentType.PAY_CASH
    else:
        payment = PaymentType.set_payment_type(request.form['payment_type'])
    # Idea now: Get driver id and match with current hold
    search_id = request.form['search_id']
    try:
        book_type = int(request.form['type'])
    except Exception:
        book_type = BookingParams.TYPE_ROUNDTRIP
    if search_id == -1:
        return jsonify({'result': -1, 'message': 'Invalid search ID'})
    try:
        loc = request.form['loc']
    except Exception:
        loc = "N/A"
    try:
        dest_lat = request.form['dest_lat']
        dest_long = request.form['dest_long']
        dest_loc = request.form['dest_loc']
        dest_exists = True
    except Exception:
        dest_lat = 0.0
        dest_long = 0.0
        dest_loc = "N/A"
        dest_exists = False
    insurance = bool(int(get_safe(request.form, 'insurance', 0)))
    if user in USER_BANNED_LIST:
        return jsonify({'result': -1, 'message': 'User banned to create booking'})  # no thanks
    existing_booking = db.session.query(Bookings).filter(Bookings.search_key == search_id).first()
    if existing_booking:
        return jsonify({'result': 1, 'exists': 1, 'message': 'Booking already created for this search'})
    cur_search = DriverSearch.query.filter_by(id=search_id).first()
    if not cur_search:
        return jsonify({'result': -1, 'message': 'Invalid search ID'}), 500
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry.credit < 0:
        return jsonify({'result': -3, 'message': 'Not enough credits'})
    end_time = (datetime.datetime(cur_search.date.year, cur_search.date.month, cur_search.date.day,
                                  cur_search.time.hour, cur_search.time.minute, cur_search.time.second) +
                datetime.timedelta(cur_search.days, cur_search.dur.hour * 3600 +
                                   cur_search.dur.minute * 60 + cur_search.dur.second))
    print(request.form.to_dict(), "- booking data")

    locations_data = Regions.LOCATION_MAP
    if not point_inside_polygon(dest_long, dest_lat, cur_search.reflong, cur_search.reflat, book_type, locations_data, cur_search.region):
        return jsonify({'result': -1, 'message': 'Location is outside service area'}), 400

    if cur_search is not None:
        can_book = False
        drivers = None
        city = cur_search.region
        can_book = can_book_search_entry(cur_search, user)
        if not can_book:
            return jsonify({'result': -2, 'message': 'User already have booking in this time frame'})
        try:
            existing_booking_check2 = db.session.query(Bookings).filter(Bookings.search_key == search_id).first()
            if existing_booking_check2:
                return jsonify({'result': 1, 'exists': 1, 'message': 'Booking already created for this search'})
            book = Bookings(user, search_id, BookingParams.BOOKING_DUMMY_ID, cur_search.reflat, cur_search.reflong,
                            cur_search.time,
                            cur_search.date, str(cur_search.dur), end_time.time(), end_time.date(),
                            BookingParams.BOOKING_DUMMY_EST, BookingParams.BOOKING_DUMMY_EST,
                            loc,cur_search.car_type, cur_search.type, cur_search.days, payment_type=payment,
                            region=cur_search.region, insurance=insurance, insurance_num=cur_search.insurance_num)
            db.session.add(book)
            db.session.flush()
            book_id = str(book.id)
            # Generate booking code
            get_book_code(book.id)
            if dest_exists:
                # book_id_2 = db.session.query(Bookings).filter(Bookings.search_key == search_id).first().id
                dest = BookDest(book_id, dest_lat, dest_long, dest_loc)
                db.session.add(dest)
            if not BookingParams.get_no_broadcast():
                pending_state = BookPending.BROADCAST
            else:
                pending_state = BookPending.SUPPRESSED
            # Temporary hack to handle pending entries getting created
            # async
            if BookingParams.is_cust_booktype(book_type) and book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY:
                cur_price = Price.get_price(book_type, cur_search.dur.hour + cur_search.dur.minute/60, cur_search.time, end_time, cur_search.dist, cur_search.car_type,
                                    cur_search.date, end_time.date(), 0, insurance=cur_search.insurance,
                                    insurance_num=cur_search.insurance_num, city=city)
            elif book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
                cur_price = PriceOutstation.get_price(cur_search.date,
                                end_time.date(), 1, cur_search.days * 24 + cur_search.dur.hour,
                                cur_search.car_type, book_type, cur_search.dist, insurance=cur_search.insurance,
                                insurance_num=cur_search.insurance_num, city=city)
            else:
                cur_price = None
            if cur_price:
                temp_driver = db.session.query(Users, Drivers).filter(Drivers.user == Users.id).filter(Drivers.id == 1).first()
                driver_book_entry = DriverBook(temp_driver[1].id, temp_driver[0].fname, temp_driver[0].lname, temp_driver[0].mobile, temp_driver[1].pic,
                                               -1, -1, temp_driver[1].rating, cur_price, 0)
                create_pricing_entry(driver_book_entry, book)
                create_pending_entry(driver_book_entry, book, pending_state)
            _update_user_pending(user)
            db.session.commit()
            # The block below releases trips by finding drivers.
            # This is bad in PA since threads are disabled. So instead use
            # the release script.

            #if book_id:
            #    thread = Thread(target=allocate_driver_to_booking, args=[
            #                    book, book_id, book_type, loc, pending_state,
            #                    drivers, cur_search, user, city, cur_price])
            #    thread.start()
        except Exception as e:
            print(e)
            r = jsonify({'result': -1, 'message': 'DB Error'})
            db.session.rollback()
            return r
    else:
        return jsonify({'result': -1, 'message': 'Invalid search ID'})

    #booking trigger, cur_search.date cur_search.time
    is_within_two_hours = (datetime.datetime.combine(cur_search.date, cur_search.time)
                           - datetime.datetime.now()) < timedelta(minutes=90)
    vip_or_issue_faced = is_vip_or_faced_issue(user_entry.label_bv)
    if is_within_two_hours or vip_or_issue_faced:
        content = f'VIP/Issue faced booking. Code:{book.code}' if vip_or_issue_faced else f'Priority booking. Code:{book.code}'
        notification = {
            'id':book_id,
            'type': claims.get('name', 'unknown'),
            'username': str(temp_driver[0].id),
            'content': content,
            'imageUrl': '/assets/icons/bticons/crown.svg' if vip_or_issue_faced else "/assets/icons/bticons/swatch.svg",
            'timestamp': int(time.time() * 1000)
        }
        send_notification_to_channel(notification, 'Priority Booking:'+str(cur_search.region))

    send_live_update_of_booking(book.id, book.region, 'new_booking')
    live_update_data = {
                "userid": user,
                'book_timestamp':convert_to_ist(datetime.datetime.combine(cur_search.date, cur_search.time)).strftime("%d %b, %y, %H:%M:%S"),
                "search_timestamp": convert_to_ist(cur_search.timestamp).strftime("%d %b, %y, %H:%M:%S")
                }
    live_update_to_channel(live_update_data, room_name='estimate', type='estimate',region=cur_search.region, channel= 'estimate_upcoming_booking')

    return jsonify({'result': 1, 'message': 'Booking created successfully'})

@bookride.route('/api/search', methods=['POST'])
@swag_from('/app/swagger_docs/booking/search.yml')
@jwt_required()
def register_search():
    search_id = uuid.uuid4().urn[9:]
    # TO-DO: VALIDATE
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'id': -1, 'message': 'Failed to fetch identity'}), 401
    if not account_enabled(user):
        return jsonify({'id': -1, 'message': 'User restricted'}), 401

    if not complete(request.form, ['reflat', 'reflong', 'car_type', 'dur', 'time']):
        return jsonify({'id': -1, 'message': 'Incomplete form details'}), 201

    book_type = int(get_safe(request.form, 'type', BookingParams.TYPE_ROUNDTRIP))
    city = int(get_safe(request.form, 'region', Regions.REGN_KOLKATA))
    reflat = request.form['reflat']
    reflong = request.form['reflong']
    try:
        time = datetime.datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
    except ValueError:
        try:
            time = datetime.datetime.strptime(request.form['time'], "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            time = datetime.datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)
    dur = request.form['dur']
    try:
        dest_lat = request.form['dest_lat']
        dest_long = request.form['dest_long']
        dest_loc = request.form['dest_loc']
        dest_exists = True
    except Exception:
        dest_lat = 0.0
        dest_long = 0.0
        dest_loc = "N/A"
        dest_exists = False
    car_type = request.form['car_type']
    insurance = bool(int(get_safe(request.form, 'insurance', 0)))
    ninsurance = int(get_safe(request.form, 'ninsurance', 1))
    if ninsurance == 0 and insurance:
        ninsurance = 1
    target = datetime.datetime(time.year, time.month, time.day, time.hour, time.minute, time.second)
    now = datetime.datetime.utcnow()
    delta = target - now
    delta = delta.days * 86400 + delta.seconds
    source = str(get_safe(request.form, 'source', 'unknown'))
    #if BookingParams.is_cust_booktype(book_type) and (book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY) \
    #        and (delta < BookingParams.BOOKING_MIN_THRESH or delta > BookingParams.BOOKING_MAX_THRESH):
    #    return jsonify({'id': -2})
    return _get_estimate(user, search_id, book_type, car_type, time, dur, reflat, reflong, dest_lat, dest_long, dest_loc, dest_exists, insurance, ninsurance, city, source=source)

@bookride.route('/api/search/dummy', methods=['POST'])
@jwt_required()
def register_search_dummy():
    search_id = uuid.uuid4().urn[9:]
    try:
        user = get_jwt_identity()
    except Exception as e:
        print(e)
        return jsonify({'id': -1}), 401
    if not account_enabled(user):
        return jsonify({'id': -1}), 401

    book_type = int(get_safe(request.form, 'type', BookingParams.TYPE_ROUNDTRIP))
    city = int(get_safe(request.form, 'region', Regions.REGN_KOLKATA))
    reflat = request.form["reflat"]
    reflong = request.form["reflong"]
    try:
        time = datetime.datetime.now(pytz.utc)+ datetime.timedelta(hours=6) #datetime.datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S %z").astimezone(pytz.utc)
    except ValueError:
        try:
            time = datetime.datetime.strptime(request.form['time'], "%d/%m/%Y %I:%M %p %z").astimezone(pytz.utc)
        except ValueError:
            time = datetime.datetime.strptime(request.form['time'], "%d/%m/%Y %H:%M:%S").astimezone(pytz.utc)
    dur = "06:30:00"
    try:
        dest_lat = "22.608501"
        dest_long = "88.387522"
        dest_loc = "11"
        dest_exists = True
    except Exception:
        dest_lat = 0.0
        dest_long = 0.0
        dest_loc = "N/A"
        dest_exists = False
    car_type = "0"
    insurance = bool(int(get_safe(request.form, 'insurance', 0)))
    ninsurance = int(get_safe(request.form, 'ninsurance', 1))
    if ninsurance == 0 and insurance:
        ninsurance = 1
    target = datetime.datetime(time.year, time.month, time.day, time.hour, time.minute, time.second)
    now = datetime.datetime.utcnow()
    delta = target - now
    delta = delta.days * 86400 + delta.seconds
    source = str(get_safe(request.form, 'source', 'unknown'))
    return _get_estimate(user, search_id, book_type, car_type, time, dur, reflat, reflong, dest_lat, dest_long, dest_loc, dest_exists, insurance, ninsurance, city, source=source)

@bookride.route('/api/book/dummy', methods=['POST'])
@jwt_required()
def book_driver_dummy():
    search = register_search_dummy()
    search = json.loads(search.get_data(as_text=True))
    search_id = search["id"]

    USER_BANNED_LIST = [5489, 49392]
    try:
        user = get_jwt_identity()
    except Exception:
        return jsonify({'result': -1}), 401
    claims = get_jwt()
    if claims['roles'] == Users.ROLE_DRIVER:
        return jsonify({'success': -1}), 401
    if not account_enabled(user):
        return jsonify({'result': -1}), 401
    if 'payment_type' not in request.form:
        payment = PaymentType.PAY_CASH
    else:
        payment = PaymentType.set_payment_type(request.form['payment_type'])
    try:
        book_type = int(request.form['type'])
    except Exception:
        book_type = BookingParams.TYPE_ROUNDTRIP
    if search_id == -1:
        return jsonify({'result': -1})
    try:
        loc = request.form['loc']
    except Exception:
        loc = "N/A"
    try:
        dest_lat = request.form['dest_lat']
        dest_long = request.form['dest_long']
        dest_loc = request.form['dest_loc']
        dest_exists = True
    except Exception:
        dest_lat = 0.0
        dest_long = 0.0
        dest_loc = "N/A"
        dest_exists = False
    insurance = bool(int(get_safe(request.form, 'insurance', 0)))
    if user in USER_BANNED_LIST:
        return jsonify({'result': -1})
    existing_booking = db.session.query(Bookings).filter(Bookings.search_key == search_id).first()
    if existing_booking:
        return jsonify({'result': 1, 'exists': 1})
    cur_search = DriverSearch.query.filter_by(id=search_id).first()
    if not cur_search:
        return jsonify({'result': -1}), 500
    user_entry = db.session.query(Users).filter(Users.id == user).first()
    if user_entry.credit < 0:
        return jsonify({'result': -3})
    end_time = (datetime.datetime(cur_search.date.year, cur_search.date.month, cur_search.date.day,
                                  cur_search.time.hour, cur_search.time.minute, cur_search.time.second) +
                datetime.timedelta(cur_search.days, cur_search.dur.hour * 3600 +
                                   cur_search.dur.minute * 60 + cur_search.dur.second))
    print(request.form.to_dict(), "- booking data")

    locations_data = Regions.LOCATION_MAP
    if not point_inside_polygon(dest_long, dest_lat, cur_search.reflong, cur_search.reflat, book_type, locations_data, cur_search.region):
        return jsonify({'result': -1, 'message': 'Location is outside service area'}), 400

    if cur_search is not None:
        can_book = False
        drivers = None
        city = cur_search.region
        can_book = can_book_search_entry(cur_search, user)
        if not can_book:
            return jsonify({'result': -2})
        try:
            existing_booking_check2 = db.session.query(Bookings).filter(Bookings.search_key == search_id).first()
            if existing_booking_check2:
                return jsonify({'result': 1, 'exists': 1})
            book = Bookings(user, search_id, BookingParams.BOOKING_DUMMY_ID, cur_search.reflat, cur_search.reflong,
                            cur_search.time,
                            cur_search.date, str(cur_search.dur), end_time.time(), end_time.date(),
                            BookingParams.BOOKING_DUMMY_EST, BookingParams.BOOKING_DUMMY_EST,
                            loc, cur_search.type, cur_search.days, payment_type=payment,
                            region=cur_search.region, insurance=insurance, insurance_num=cur_search.insurance_num)
            db.session.add(book)
            db.session.commit()
            book_id = str(book.id)
            get_book_code(book_id)
            if dest_exists:
                book_id_2 = db.session.query(Bookings).filter(Bookings.search_key == search_id).first().id
                dest = BookDest(book_id_2, dest_lat, dest_long, dest_loc)
                db.session.add(dest)
                db.session.commit()
            if not BookingParams.get_no_broadcast():
                pending_state = BookPending.BROADCAST
            else:
                pending_state = BookPending.SUPPRESSED

            if BookingParams.is_cust_booktype(book_type) and book_type != BookingParams.TYPE_OUTSTATION and book_type != BookingParams.TYPE_OUTSTATION_ONEWAY:
                cur_price = Price.get_price(book_type, cur_search.dur.hour + cur_search.dur.minute/60, cur_search.time, end_time, cur_search.dist, cur_search.car_type,
                                    cur_search.date, end_time.date(), 0, insurance=cur_search.insurance,
                                    insurance_num=cur_search.insurance_num, city=city)
            elif book_type == BookingParams.TYPE_OUTSTATION or book_type == BookingParams.TYPE_OUTSTATION_ONEWAY:
                cur_price = PriceOutstation.get_price(cur_search.date,
                                end_time.date(), 1, cur_search.days * 24 + cur_search.dur.hour,
                                cur_search.car_type, book_type, cur_search.dist, insurance=cur_search.insurance,
                                insurance_num=cur_search.insurance_num, city=city)
            else:
                cur_price = None
            if cur_price:
                temp_driver = db.session.query(Users, Drivers).filter(Drivers.user == Users.id).filter(Drivers.id == 1).first()
                driver_book_entry = DriverBook(temp_driver[1].id, temp_driver[0].fname, temp_driver[0].lname, temp_driver[0].mobile, temp_driver[1].pic,
                                               -1, -1, temp_driver[1].rating, cur_price, 0)
                create_pricing_entry(driver_book_entry, book)
                create_pending_entry(driver_book_entry, book, pending_state)
            _update_user_pending(user)

        except Exception as e:
            print(e)
            r = jsonify({'result': -1})
            db.session.rollback()
            return r
    else:
        return jsonify({'result': -1})
    return jsonify({'result': 1,'id': book_id})

@bookride.route('/api/decline/user/charge', methods=['POST'])
@swag_from('/app/swagger_docs/booking/decline_user_charge.yml')
@jwt_required()
def cancel_user_charge():
    try:
        booking_id = request.form['booking_id']
    except(KeyError, NameError):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 401
    user_id = get_jwt_identity()
    reason = int(get_safe(request.form, 'reason', 0))
    booking = db.session.query(Bookings).filter(Bookings.user == user_id).filter(Bookings.id == booking_id).first()
    if not account_enabled(user_id):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    trip = fetch_booking_trip(booking_id).first()
    is_ontrip = trip is not None
    if booking.driver != 1:
        last_alloc_time = db.session.query(BookingAlloc).filter(BookingAlloc.booking_id == booking.id).filter(BookingAlloc.driver_id == booking.driver).order_by(BookingAlloc.timestamp.desc()).first().timestamp
        if (reason in BookingCancelled.WAIVER
            and datetime.datetime.utcnow() - last_alloc_time < BookingCancelled.FORGIVE_DELTA or
            booking.driver == 1):
            return jsonify({'success': 1, 'charge': [0,0], 'message': 'No charges applied'})
        if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            penalty, level = PriceOutstation.get_cancel_ch(datetime.datetime.utcnow(), datetime.datetime.combine(booking.startdate,booking.starttime), city=booking.region, cancel_cat=reason,
                                         is_ontrip=is_ontrip)
        else:
            penalty, level = Price.get_cancel_ch(datetime.datetime.utcnow(), datetime.datetime.combine(booking.startdate,booking.starttime), city=booking.region, cancel_cat=reason,
                                         is_ontrip=is_ontrip)
        
        return jsonify({'success': 1, 'charge': penalty, 'message': 'Fetched charges successfully'})
    else:
        return jsonify({'success': 1, 'charge': [0,0], 'message': 'No charges applied'})

@bookride.route('/api/decline/user', methods=['POST'])
@swag_from('/app/swagger_docs/booking/cancel_user.yml')
@jwt_required()
def cancel_user():  # User cancel b2c
    claims = get_jwt()
    user_id = get_jwt_identity()
    with app.app_context():
        engine = db.engine.execution_options(timeout=30)
        Session = sessionmaker(bind=engine)
        session = Session()

        try:
            booking_id = request.form['booking_id']
        except (KeyError, NameError):
            return jsonify({'success': -1, 'message': 'Incomplete form details'}), 401

        reason_detail = str(get_safe(request.form, 'reason_details', ""))
        reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_NO_ALLOC))
        waiver = False
        msg_content = ""
        driver_msg_string = ""

        try:
            # Start an outer transaction block. This block auto-commits on success.
            with session.begin(),session.begin_nested():
                booking = session.query(Bookings).filter(
                    Bookings.user == user_id,
                    Bookings.id == booking_id
                ).with_for_update().first()

                if not booking:
                    return jsonify({'success': -1, 'message': 'Booking does not exist'}), 404

                # Retrieve driver info using the new session
                driver_info = session.query(Users, Drivers).filter(
                    Users.id == Drivers.user,
                    Drivers.id == booking.driver
                ).first()

                if not account_enabled(user_id):
                    return jsonify({'success': -1, 'message': 'User restricted'}), 401
                try:
                    trip = session.query(Trip).filter(Trip.book_id == booking_id).first()
                except Exception as e:
                    trip = None
                is_ontrip = trip is not None

                if trip:
                    if trip.starttime:
                        return jsonify({'success': -2, 'message': 'Trip started already'}), 200
                    else:
                        session.delete(trip)
                    try:
                        fb_db.collection(u'trip_started').document(str(booking.user)).delete()
                    except Exception:
                        pass

                if booking.valid >= 0:
                    # Update booking state in one transaction block.
                    session.query(Bookings).filter(Bookings.id == booking.id).update({
                        Bookings.valid: Bookings.CANCELLED_USER,
                        Bookings.cancelled_dt: datetime.datetime.utcnow(),
                        Bookings.did: "-1",
                        Bookings.did_release: True
                    })

                    _update_user_pending(booking.user)
                    _update_driver_pending(booking.driver)

                    try:
                        fb_db.collection(u'trip_set').document(str(user_id)).update({
                            str(booking_id): firestore.DELETE_FIELD
                        })
                    except Exception:
                        pass

                    if booking.driver != 1:
                        fb_db.collection(u'book_cancelled_driver').document(str(booking.driver)).set({
                            str(booking_id): {
                                u'book_time': booking.starttime.strftime("%H:%M:%S"),
                                u'book_date': booking.startdate.strftime("%Y-%m-%d")
                            }
                        }, merge=True)

                    # Prepare messages and possible penalty calculations
                    try:
                        user = session.query(Users).filter(Users.id == user_id).first()
                        user_name = user.get_name()
                        start_time_ist = datetime.datetime(
                            booking.startdate.year, booking.startdate.month, booking.startdate.day,
                            booking.starttime.hour, booking.starttime.minute, booking.starttime.second
                        ) + _sms.IST_OFFSET_TIMEDELTA

                        if booking.driver != 1 and driver_info:
                            driver_name = driver_info[0].get_name()
                        else:
                            driver_name = ''

                        driver_msg_string = (' The trip was unallocated.' if not driver_name
                                            else ' The trip was allocated to ' + driver_name)
                        msg_content = (user_name + " cancelled their trip (ID: " +
                                    str(booking.code) + ") booked on " +
                                    start_time_ist.strftime("%I:%M %p %d/%m/%Y") + ".")
                        msg_content_json = {"name": user_name, "code": str(booking.code),
                                            "time": start_time_ist.strftime("%I:%M %p %d/%m/%Y")}

                        if booking.driver != 1:
                            msg_content_json = {"name": user_name, "code": str(booking.code),
                                        "time": start_time_ist.strftime("%I:%M %p %d/%m/%Y")}
                    
                            message = (
                                f"{msg_content_json['name']} cancelled their trip (ID: {msg_content_json['code']}) "
                                f"with Drivers4Me booked on {msg_content_json['time']}."
                            )
                            response = _sms.send_bulk_message_gupshup(
                                phone_numbers=[str(driver_info[0].mobile)],
                                message=message,
                                mask= _sms.MASK,
                                dltTemplateId=_sms.TEMPLATE_ID_MAPPING['user-cancelled'],
                                principalEntityId= _sms.PRINCIPAL_ENTITY_ID
                            )

                            starttime = datetime.datetime(
                                booking.startdate.year, booking.startdate.month, booking.startdate.day,
                                booking.starttime.hour, booking.starttime.minute, booking.starttime.second
                            )
                            if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
                                penalty, level = PriceOutstation.get_cancel_ch(datetime.datetime.utcnow(), starttime, city=booking.region, cancel_cat=reason,
                                                            is_ontrip=is_ontrip)
                            else:
                                penalty, level = Price.get_cancel_ch(datetime.datetime.utcnow(), starttime, city=booking.region, cancel_cat=reason,
                                                            is_ontrip=is_ontrip)
                            user_fine, driver_fine = penalty
                            ut_id = None
                            dt_id = None

                            last_alloc = session.query(BookingAlloc).filter(
                                BookingAlloc.booking_id == booking.id,
                                BookingAlloc.driver_id == booking.driver
                            ).order_by(BookingAlloc.timestamp.desc()).first()

                            if last_alloc:
                                last_alloc_time = last_alloc.timestamp
                            else:
                                last_alloc_time = datetime.datetime.utcnow()

                            if (reason in BookingCancelled.WAIVER and
                                    datetime.datetime.utcnow() - last_alloc_time < BookingCancelled.FORGIVE_DELTA):
                                user_fine = 0
                                driver_fine = 0
                                waiver = True

                            if user_fine > 0:
                                uc = UserCancelled(user_id, booking.id, user_fine)
                                ut = UserTrans(
                                    uid=user_id,
                                    amt=(100 * user_fine) * (-1),
                                    method="Cancellation charges for #" + str(booking.code),
                                    status=UserTrans.COMPLETED,
                                    wall_a=user.credit - user_fine,
                                    wall_b=user.credit
                                )
                                session.query(Users).filter(Users.id == user_id).update({
                                    Users.credit: Users.credit - user_fine
                                })
                                driver_msg_string += ". They were charged a penalty of " + str(user_fine) + "."
                                session.add(uc)
                                session.add(ut)
                                session.flush()
                                ut_id = ut.id
                            else:
                                uc = UserCancelled(user_id, booking.id, 0)
                                session.add(uc)

                            if driver_fine > 0:
                                dc = DriverCancelled(booking.driver, booking.id, driver_fine)
                                session.add(dc)
                                details = session.query(DriverDetails).filter(
                                    DriverDetails.driver_id == booking.driver
                                )
                                driver_details = details.first()
                                wallet, withdrawable = compute_driver_wallet(driver_details, driver_fine)
                                dt = DriverTrans(
                                    booking.driver, -driver_fine * 100,
                                    wall_a=wallet, wall_b=driver_details.wallet,
                                    with_a=withdrawable, with_b=driver_details.withdrawable,
                                    method="Cancellation: Booking %s" % str(booking.code),
                                    status=DriverTrans.COMPLETED, stop=True
                                )
                                session.add(dt)
                                session.flush()
                                dt_id = dt.id
                                session.query(DriverDetails).filter(
                                    DriverDetails.driver_id == booking.driver
                                ).update({
                                    DriverDetails.owed: DriverDetails.owed + driver_fine,
                                    DriverDetails.wallet: wallet,
                                    DriverDetails.withdrawable: withdrawable
                                })
                            else:
                                dc = DriverCancelled(booking.driver, booking.id, 0)
                                session.add(dc)

                            bc = BookingCancelled(
                                user=user_id, cancel_source=BookingCancelled.SRC_USER,
                                booking=booking.id, uid=booking.user, did=booking.driver,
                                penalty_user=user_fine, penalty_driver=driver_fine,
                                rsn=reason, reason_detail=reason_detail,
                                utransid=ut_id, dtransid=dt_id
                            )
                        else:
                            uc = UserCancelled(booking.user, booking.id, 0)
                            session.add(uc)
                            bc = BookingCancelled(
                                user=user_id, cancel_source=BookingCancelled.SRC_USER,
                                booking=booking.id, uid=booking.user, did=booking.driver,
                                penalty_user=0, penalty_driver=0, rsn=reason,
                                reason_detail=reason_detail
                            )

                        session.add(bc)
                    except Exception as e:
                        print(e)
                        session.rollback()
                        return jsonify({'success': -1, 'message': 'DB Error during cancellation process'}), 401

                    try:
                        fb_db.collection(u'trip_started').document(str(booking.user)).delete()
                    except Exception:
                        pass

        # End of transaction block – changes are committed now.

            # Send notifications outside the transaction.
            notification = {
                'id': booking_id,
                'type': claims.get('name', 'unknown'),
                'username': str(user_id),
                'content': f"Booking cancelled by user. Code:{booking.code}",
                'imageUrl': '/assets/icons/bticons/ban.svg',
                'timestamp': int(time.time() * 1000)
            }
            send_notification_to_channel(notification, 'Cancelled by User:' + str(booking.region))
            send_live_update_of_booking(booking_id, booking.region)
            if trip:
                driver_data = {
                    'driver_id': driver_info[1].id,
                }   
                live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_info[0].region, channel= 'update_driver')
            send_slack_msg(1, msg_content + driver_msg_string)

            try:
                send_fcm_msg_driver(
                    booking.driver,
                    title="Cancellation of booking #" + str(booking.code),
                    smalltext="Customer cancelled the trip allocated to you.",
                    bigtext=user_name + " cancelled booking #" + str(booking.code) + " that was allocated to you."
                )
            except Exception:
                pass

            return jsonify({'success': 1, 'waiver': int(waiver), 'message': 'Booking cancelled successfully'})

        except exc.IntegrityError as e:
            session.rollback()
            return jsonify({'success': -1, 'message': 'DB Error'}), 401

        except Exception as e:
            session.rollback()
            print(e)
            return jsonify({'success': -1, 'message': 'Unexpected error occurred'}), 500


@bookride.route('/api/decline/driver/charge', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/cancel_driver_charge.yml')
@jwt_required()
def cancel_driver_charge():
    try:
        booking_id = request.form['booking_id']
    except(KeyError, NameError):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401
    reason = int(get_safe(request.form, 'reason', 0))
    driver_user = get_jwt_identity()

    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver = Drivers.query.filter_by(user=driver_user).first().id
    booking = db.session.query(Bookings).filter(Bookings.driver == driver).filter(Bookings.id == booking_id).first()

    if not booking:
        return jsonify({'success': -3, 'message': 'Booking does not exist'}), 200

    trip_ex = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
    has_trip = trip_ex is not None
    if trip_ex and trip_ex.status < Trip.TRIP_INIT:
        return jsonify({'success': -2, 'message': 'Can not cancel after checkIn'}), 200
    starttime = datetime.datetime(booking.startdate.year, booking.startdate.month,
                                          booking.startdate.day, booking.starttime.hour,
                                          booking.starttime.minute, booking.starttime.second)
    if booking.type == BookingParams.TYPE_B2B:
        book_logs, mapped_affiliate = (
                                        db.session.query(AffBookingLogs, Affiliate)
                                        .join(Affiliate, Affiliate.id == AffBookingLogs.mapped_by)
                                        .filter(AffBookingLogs.book_id == booking_id)
                                        .first()
                                    )
        mapped_client = mapped_affiliate.client_name
        penalty, delay = Price.get_cancel_ch(datetime.datetime.utcnow(), starttime, cancel_cat=reason,
                                        has_trip=has_trip, type=booking.type, client_name=mapped_client)
    else:
        if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
            penalty, delay = PriceOutstation.get_cancel_ch(datetime.datetime.utcnow(), starttime, city=booking.region, has_trip=has_trip, cancel_cat=reason)
        else:
            penalty, delay = Price.get_cancel_ch(datetime.datetime.utcnow(), starttime, city=booking.region, has_trip=has_trip, cancel_cat=reason)

    return jsonify({'success': 1, 'cancel_charge': penalty}), 200


@bookride.route('/api/decline/driver', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/cancel_driver.yml')
@jwt_required()
def cancel_driver():
    try:
        booking_id = request.form['booking_id']
    except(KeyError, NameError):
        return jsonify({'success': -1, 'message': 'Incomplete form details'}), 401
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401
    driver_user = get_jwt_identity()
    reason_detail = str(get_safe(request.form, 'reason_details', ""))
    reason = int(get_safe(request.form, 'reason', BookingCancelled.RSN_WRONGLY_TAKEN))
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver = Drivers.query.filter_by(user=driver_user).first().id
    booking = db.session.query(Bookings).filter(Bookings.driver == driver).filter(Bookings.id == booking_id).first()
    if not booking:
        return jsonify({'success': -3, 'message': 'Booking does not exist'}), 200

    trip_ex = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
    has_trip = trip_ex is not None
    if trip_ex and trip_ex.status < Trip.TRIP_INIT:
        return jsonify({'success': -2, 'message': 'Can not cancel after checkIn'}), 200
    driver_info = db.session.query(Users, Drivers).filter(Users.id == Drivers.user). \
        filter(Drivers.id == booking.driver).first()
    if booking.valid >= 0:
        try:
            starttime = datetime.datetime(booking.startdate.year, booking.startdate.month,
                                          booking.startdate.day, booking.starttime.hour,
                                          booking.starttime.minute, booking.starttime.second)
            user_id = None
            if booking.type == BookingParams.TYPE_B2B:
                book_logs, mapped_affiliate, mapped_affiliate_wallet = (
                                    db.session.query(
                                        AffBookingLogs,
                                        mapped_by_alias := aliased(Affiliate),
                                        mapped_wallet_alias := aliased(Affiliate)
                                    )
                                    .join(mapped_by_alias, mapped_by_alias.id == AffBookingLogs.mapped_by)
                                    .join(mapped_wallet_alias, mapped_wallet_alias.id == AffBookingLogs.mapped_wallet)
                                    .filter(AffBookingLogs.book_id == booking_id)
                                    .first()
                                )
                affiliate_id = book_logs.aff_id
                affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
                user_id = affiliate_id
                mapped_client = mapped_affiliate.client_name

                client_name = affiliate.client_name
                penalty, delay = Price.get_cancel_ch(datetime.datetime.utcnow(), starttime, cancel_cat=reason,
                                        has_trip=has_trip, type=booking.type, client_name=mapped_client)
            else:
                if booking.type == BookingParams.TYPE_OUTSTATION or booking.type == BookingParams.TYPE_OUTSTATION_ONEWAY:
                    penalty, delay = PriceOutstation.get_cancel_ch(datetime.datetime.utcnow(), starttime, city=booking.region, has_trip=has_trip, cancel_cat=reason)
                else:
                    penalty, delay = Price.get_cancel_ch(datetime.datetime.utcnow(), starttime, city=booking.region, has_trip=has_trip, cancel_cat=reason)
            
                user_id = booking.user
            user_fine, driver_fine = penalty
            ut_id=None
            if user_fine > 0:
                if booking.type == BookingParams.TYPE_B2B:
                    ut = AffiliateWalletLogs(-user_fine*100, method=f"Cancellation charges #{booking.code}",
                                            from_account=affiliate.mapped_wallet_affiliate, wallet_before=mapped_affiliate_wallet.wallet,
                                            wallet_after=mapped_affiliate_wallet.wallet - user_fine, source=AffiliateWalletLogs.SOURCE_DRIVER)
                    mapped_affiliate_wallet.wallet -= user_fine
                    ut_id = ut.id
                else:
                    user = db.session.query(Users).filter(Users.id == user_id).first()
                    ut = UserTrans(uid=user_id, amt=(100*user_fine)*(-1), method="Cancellation charges for #" + str(booking.code),
                                status=UserTrans.COMPLETED,wall_a=user.credit - user_fine, wall_b = user.credit)
                    user.credit -= user_fine
                    ut_id=ut.id
                db.session.add(ut)

            _ ,dt_id= _unalloc_trip_driver(booking, driver, delay, driver_fine, trip=trip_ex)
            if booking.type == BookingParams.TYPE_B2B:
                bc = BookingCancelled(driver_user, BookingCancelled.SRC_DRIVER, booking.id, None,
                                    driver, user_fine, driver_fine, reason, reason_detail, atransid=ut_id, dtransid=dt_id)
            else:
                uc = UserCancelled(user_id, booking.id, user_fine)
                bc = BookingCancelled(driver_user, BookingCancelled.SRC_DRIVER, booking.id, booking.user,
                                    driver, user_fine, driver_fine, reason, reason_detail, utransid=ut_id, dtransid=dt_id)
                db.session.add(uc)
            Bookings.query.filter(Bookings.id == booking.id).update({Bookings.did: "-1"})
            db.session.add(bc)
            db.session.commit()
            try:
                if booking.type == BookingParams.TYPE_B2B:
                    user_name = client_name
                else:
                    user_name = user.get_name()
                start_time_ist = datetime.datetime(booking.startdate.year, booking.startdate.month,
                                                   booking.startdate.day, booking.starttime.hour,
                                                   booking.starttime.minute, booking.starttime.second) + \
                                 _sms.IST_OFFSET_TIMEDELTA
                driver_name = driver_info[0].get_name()
                msg_content = driver_name + " cancelled the trip allocated" + \
                              "to them of " + user_name + ", booked on " + \
                              start_time_ist.strftime("%I:%M %p %d/%m/%Y") + ". " + \
                              "They were charged Rs. " + str(driver_fine) + " as penalty."
                send_slack_msg(1, msg_content)
                if booking.type < BookingParams.TYPE_C24:
                    send_fcm_msg(booking.user, title="Declined by driver", smalltext="We are searching for alternative drivers for your trip",
                                bigtext="Unfortunately your allocated driver declined the trip. We will be allocating a suitable alternative driver shortly." + \
                                " We apologize for this and will ensure it does not happen again")
                send_fcm_msg_driver(driver, title="Cancellation of booking #" + str(booking.code),
                                smalltext="You were charged a penalty of ₹" + str(driver_fine) + ".",
                                bigtext="You were charged a penalty of ₹" + str(driver_fine) + " for cancelling booking #" + str(booking.code))
            except Exception as e:
                print(e)
                pass

            if booking.type == BookingParams.TYPE_B2B:
                notification = {
                    'book_code': booking.code,
                    'type': 'Cancelled by Driver'
                }
                send_notification_to_affiliate(notification, 'Cancelled by Driver', user_id)
                # send_live_update_to_affiliate(booking_id, booking.user)
            else:
                try:
                    fb_db.collection(u'trip_started').document(str(booking.user)).delete()
                except Exception as e:
                    pass
                driver_name = driver_info[0].get_name()
                notification = {
                    'id': booking_id,
                    'type': claims.get('name', 'unknown'),
                    'username': str(driver_user),
                    'content' : f'Booking canceled by driver {driver_name}. Code:{booking.code}',
                    'imageUrl': '/assets/icons/action/admin-cross.svg',
                    'timestamp': int(time.time() * 1000)
                }
                send_notification_to_channel(notification, 'Cancelled by Driver:'+ str(booking.region))
                send_live_update_of_booking( booking_id, booking.region)
            if trip_ex:
                driver_data = {
                    'driver_id': driver_info[1].id,
                }   
                live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_info[0].region, channel= 'update_driver')

            return jsonify({'success': 1, 'message': 'Booking cancelled successfully'})
        except exc.IntegrityError:
            db.session.rollback()
            return jsonify({'success': -1, 'message': 'DB Error'}), 401
    else:
        return jsonify({'success': 0, 'message': 'Booking already cancelled'})


def _unalloc_trip_driver(booking, driver_id, delay, penalty, to_unalloc=False, trip=None):
    dt_id = None
    if False and delay > 2 and not to_unalloc:
        # delay = Delay level. NOT hours. > 2 => <1 hr left.
        Bookings.query.filter(Bookings.id == booking.id).update({Bookings.valid: Bookings.CANCELLED_DRIVER})
        Bookings.query.filter(Bookings.id == booking.id).update({Bookings.cancelled_dt: datetime.datetime.utcnow()})
    else:
        Bookings.query.filter(Bookings.id == booking.id).update({Bookings.valid: 0})
        Bookings.query.filter(Bookings.id == booking.id).update({Bookings.driver: 1})
        db.session.query(BookPending).filter(BookPending.valid == 0). \
            filter(BookPending.book_id == booking.id).update({BookPending.valid: 1})
        db.session.query(BookPending).filter(BookPending.driver == driver_id). \
            filter(BookPending.book_id == booking.id).update({BookPending.valid: 0})
    dc = DriverCancelled(driver_id, booking.id, penalty)
    db.session.add(dc)

    if penalty>0:
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id)
        driver_details = details.first()
        wallet, withdrawable = compute_driver_wallet(driver_details, penalty)
        dt = DriverTrans(driver_id, -penalty*100,
                        wall_a=wallet, wall_b=driver_details.wallet,
                        with_a=withdrawable, with_b=driver_details.withdrawable,
                        method="Cancellation: Booking %s" % str(booking.code),
                        status=DriverTrans.COMPLETED, stop=True
                        )
        db.session.add(dt)
        dt_id=dt.id
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_id). \
            update({DriverDetails.owed: DriverDetails.owed + penalty,
                    DriverDetails.wallet: wallet,
                    DriverDetails.withdrawable: withdrawable})

    if trip:
        print("Deleting trip due to unalloc")
        db.session.delete(trip)
    db.session.commit()
    if booking.type != BookingParams.TYPE_B2B:
        _update_user_pending(booking.user)

    _update_driver_pending(driver_id)
    return delay > 2,dt_id


def _cancel_trip_driver(booking, driver):
    raise Exception("Not implemented")

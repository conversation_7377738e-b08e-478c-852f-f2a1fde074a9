#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  trip.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON>rachatos Mitra

import datetime
import pytz
import math
import time
import _sms
from db_config import fb_db
from affiliate_api.hook.utils import (
    ZOOMCAR_STATE_STARTED, ZOOMCAR_STATE_PICKUP_INIT,
    ZOOMCAR_STATE_PICKUP_COMPLETE, ZOOMCAR_STATE_IN_TRANSIT,
    ZOOMCAR_STATE_DROP_INIT,
    ZOOMCAR_STATE_DROP_COMPLETE, ZOOMCAR_STATE_COMPLETE,
    _zoomcar_change_state
)
from c24 import c24_stop_trip
from revv import _revv_stop_trip
from zoomcar import zoomcar_stop_trip
from cardekho import cardekho_stop_trip
from gujral import _gujral_stop_trip
from bhandari import bhandari_stop_trip
from mahindra import mahindra_stop_trip
from pridehonda import pridehonda_stop_trip
# from revv_v2 import revv_v2_stop_trip
from spinny import spinny_stop_trip
from olx import olx_stop_trip
from _rtdb import _update_user_pending, _update_driver_pending

from google.cloud import firestore
from _fcm import send_fcm_msg, send_fcm_msg_driver
from flask import Blueprint, request, jsonify
from flask_jwt_extended import (
    jwt_required, get_jwt_identity, get_jwt
)
from numpy import base_repr
from sqlalchemy import exc
from random import randint
from affiliate_b2b.affiliate_models import AffiliateWalletLogs,AffiliateRep, Affiliate, AffBookingLogs,AffiliateCollections
from _ops_message import send_slack_msg
from models import Users, Drivers, Bookings, Trip, DriverDetails, OLXBookings, TripEndPic, CardekhoBookings, TripLog, BhandariBookings, DriverTrans
from models import db, UserTrans, C24Bookings, RevvBookings, ZoomcarBookings, GujralBookings, TripStartPic, DriverPaid, MahindraBookings, RevvV2Bookings, SpinnyBookings
from models import PrideHondaBookings, TripPricing, BookPricing
from _utils import complete, create_driver_details, strfdelta2, get_safe, upload_pic, gen_otp, compute_driver_wallet, get_trip_duration
from _utils_acc import account_enabled
from _utils_booking import fetch_booking_trip
from payments import PaymentType
from booking_params import Rating, BookingParams
from price import Price, PriceOutstation, convert_to_semihours

from socketio_app import send_notification_to_channel, live_update_to_channel
from flasgger import swag_from
from live_update_booking import send_live_update_of_booking
from live_update_aff_book import send_live_aff_booking_table
from b2b import b2b_stop_trip, b2b_upload_pic

trips = Blueprint('trips', __name__)

def convert_to_ist(dt):
    if dt is None:
        return None
    ist = pytz.timezone('Asia/Kolkata')
    return dt.astimezone(ist)

def combine_and_convert_to_ist(date_val, time_val):
    combined = datetime.datetime.combine(date_val, time_val)
    return convert_to_ist(combined) # timedelta(hours=5, minutes=30)

def split_date_time(dt):
    return dt.date(), dt.time()

def convert_timedelta(duration):
    try:
        days, seconds = duration.days, duration.seconds
    except AttributeError:
        days = 0
        seconds = duration.seconds
    hours = days * 24 + seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = (seconds % 60)
    if hours < 10:
        hours = '0' + str(hours)
    if minutes < 10:
        minutes = '0' + str(minutes)
    if seconds < 10:
        seconds = '0' + str(seconds)
    return str(hours), str(minutes), str(seconds)

def _start_trip(book, driver_user, time_start, lat=0, lng=0, b2b=False, booking=None, trip=None):
    if booking == None:
        driver_res = Drivers.query.filter_by(user=driver_user)
        driver = driver_res.first().id
        booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver) \
            .filter(Bookings.valid > 0).first()
    try:
        booking_id = booking.id
    except (TypeError, AttributeError) as exc:
        print("Trip didn't start: ", str(exc), "booking", str(book))
        return jsonify({'success': -3, 'message': 'Trip failed to start, booking issue encountered', 'status': 500})
    if not b2b and booking.type >= BookingParams.TYPE_C24:
        b2b = True
    if trip == None:
        already_trip = db.session.query(Trip).filter(Trip.book_id == booking_id)
    else:
        already_trip = trip
    if not lat:
        lat = booking.lat
    if not lng:
        lng = booking.long
    if not already_trip.first():
        trip = Trip(booking_id, time_start, lat, lng, status=Trip.TRIP_STARTED)
        db.session.add(trip)
    else:
        already_trip.update({Trip.status: Trip.TRIP_STARTED, Trip.starttime: time_start})
        trip = already_trip.first()
    try:
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
        return jsonify({'success': -4, 'message': 'DB Error', 'status': 500})
    if b2b:
        try:
            if booking.type == BookingParams.TYPE_ZOOMCAR:
                _zoomcar_change_state(ZOOMCAR_STATE_IN_TRANSIT, booking.id)
        except Exception as e:
            print("Could not set state: error %s" % str(e))
        
        if booking.type == BookingParams.TYPE_B2B:
            send_live_update_of_booking( book, booking.region) 
            affbook = db.session.query(AffBookingLogs).filter(AffBookingLogs.book_id == booking.id).first()
            send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=affbook.aff_id, booking_region=booking.region)
            # handle live update of new affiliate
            pass

    if not b2b:
        doc_ref_ts = fb_db.collection(u'trip_started').document(str(booking.user))
        doc_ref_tsd = fb_db.collection(u'trip_started_details').document(str(booking.user))
        driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
            filter(Users.id == Drivers.user).first()
        try:
            driver_name = driver_booked[0].get_name()
        except TypeError:
            driver_name = ''

        doc_ref_ts.set({str(booking_id): {'time': time_start, 'driver_id': driver_booked[1].id,
                                      'driver': driver_name, 'driver_pic': driver_booked[1].pic}})
        doc_ref_tsd.set({str(booking_id): {'time': time_start, 'driver_id': driver_booked[1].id,
                                       'driver': driver_name, 'driver_pic': driver_booked[1].pic}})

        target_user = db.session.query(Users).filter(Users.id == booking.user).first()
        start_time_ist = time_start + _sms.IST_OFFSET_TIMEDELTA
        slack_msg_content = "Hi, " + target_user.get_name() + "! Your trip (ID: " + str(booking.code) + ") with DRIVERS4ME was started by " + \
                      driver_booked[0].get_name() + " at approximately " + start_time_ist.strftime("%I:%M %p %d/%m/%Y")
        try:
            send_fcm_msg(target_user.id, title="Trip started", smalltext="Your booking #" + str(booking.code) + " was started by " + driver_booked[0].get_name(), bigtext=slack_msg_content)
            send_slack_msg(2, slack_msg_content)
        except Exception as e:
            print(e)
            pass

        msg_content = {"name": target_user.get_name(), "code": str(booking.code),
                       "driver-name": driver_booked[0].get_name(), "time": start_time_ist.strftime("%I:%M %p %d/%m/%Y")}
        user_mobile = target_user.mobile

        message = (
            f"Hi, {msg_content['name']}! Your trip (ID: {msg_content['code']}) with Drivers4Me "
            f"was started by {msg_content['driver-name']} at approximately {msg_content['time']}."
        )
        response = _sms.send_bulk_message_gupshup(
            phone_numbers=[str(user_mobile)],
            message=message,
            mask= _sms.MASK,
            dltTemplateId=_sms.TEMPLATE_ID_MAPPING['trip-start'],
            principalEntityId= _sms.PRINCIPAL_ENTITY_ID
        )
        if response and "success" in response.lower():
            print("Message sent successfully",flush=True)
        else:
            print("Failed to send message",flush=True)
    
        try:
            fb_db.collection(u'trip_set').document(str(booking.user)).update({str(booking_id): firestore.DELETE_FIELD})
        except Exception:
            pass
        _update_user_pending(booking.user)
        _update_driver_pending(booking.driver)
    
    if booking.type < BookingParams.TYPE_C24:
        send_live_update_of_booking( book, booking.region) 
    elif (booking.type == BookingParams.TYPE_B2B):
        send_live_update_of_booking( book, booking.region) 
        affbook = db.session.query(AffBookingLogs).filter(AffBookingLogs.book_id == booking.id).first()
        send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=affbook.aff_id, booking_region=booking.region)
    
    return jsonify({'success': 1, 'message': 'Trip started successfully','trip_starttime':time_start.strftime("%H:%M:%S"),'trip_startdate':time_start.strftime("%Y-%m-%d")})

def _stop_trip(book, driver_user, time_stop, lat=-1, lng=-1, b2b=False, booking=None, trip=None):
    driver_res = Drivers.query.filter_by(user=driver_user)
    if booking == None:
        booking = db.session.query(Bookings).filter(Bookings.id == book).first()
    if trip == None:
        cur_trip = db.session.query(Trip).filter(Trip.book_id == book).first()
    else:
        cur_trip = trip.first()
    if not cur_trip:
        return jsonify({'success': -1, 'message': 'Trip does not exist','status': 201})
    if not b2b and booking.type >= BookingParams.TYPE_C24:
        b2b = True
    if cur_trip.price != 0 and cur_trip.endtime:
        calc_dur = strfdelta2(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
        return jsonify({'success': 1, 'price': cur_trip.price, 'dur': calc_dur, 'message': 'Trip stopped successfully'})
    elif cur_trip.endtime:
        calc_dur = strfdelta2(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
        return jsonify({'success': 1, 'price': cur_trip.price, 'dur': calc_dur, 'message': 'Trip stopped successfully'})
    est = booking.estimate
    pre_tax = booking.estimate_pre_tax
    if pre_tax == 0 and booking.type < BookingParams.TYPE_C24:
        pre_tax = 0.95 * est
    delta = time_stop - cur_trip.starttime
    
    if delta.total_seconds() < 0:
        return jsonify({'success': -1, 'message': 'Trip does not exist', 'status': 400})
    
    # Bug - only works for 60min calcs
    time_delta = convert_to_semihours(delta, Price.get_hour_ratio())
    estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.get_hour_ratio() + \
                     math.ceil((booking.dur.minute * Price.get_hour_ratio()) / 60)
    # Now calculate price
    price = est
    if b2b:
        price = Price.get_trip_price(book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=pre_tax,
                                     book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                     trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
                                     startdate=cur_trip.starttime.date(), enddate=time_stop.date(), insurance_ch=booking.insurance_cost,
                                     city=booking.region, type=booking.type)
        pre_tax = cgst = sgst = total_due = ot_fare = night_fare = 0
    elif booking.type not in [BookingParams.TYPE_OUTSTATION, BookingParams.TYPE_OUTSTATION_ONEWAY]:
        price, pre_tax, cgst, sgst, total_due, ot_fare, night_fare = Price.get_trip_price(book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=pre_tax,
                                     book_starttime=booking.starttime, book_stoptime=booking.endtime,
                                     trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
                                     startdate=cur_trip.starttime.date(), enddate=time_stop.date(), insurance_ch=booking.insurance_cost,
                                     city=booking.region)
    else:
        price, pre_tax, cgst, sgst, total_due, ot_fare, night_fare = PriceOutstation.get_trip_price(startdate=cur_trip.starttime.date(), enddate=time_stop.date(),
                                                book_delta=estimate_delta, real_delta=time_delta, est=pre_tax, insurance_ch=booking.insurance_cost,
                                                city=booking.region)
    if price > est:
        surcharge = True
    else:
        surcharge = False
    # cur_trip.stop_trip(stop_time, price)
    try:
        d_hr, d_min, d_sec = convert_timedelta(delta)
        dur = d_hr + ':' + d_min + ':' + d_sec
        driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
            filter(Users.id == Drivers.user).first()
        try:
            driver_name = driver_booked[0].get_name()
        except TypeError:
            driver_name = ''
        if not driver_booked[1].perma:
            total_bc = total_due
        else:
            total_bc = price
        user_db = db.session.query(Users).filter(Users.id == booking.user)
        user_mg = user_db.first()
        user_mg.bookcount = user_mg.bookcount + 1
        user_credit = user_mg.credit
        uc_change = 0
        if user_credit < 0:
            pass
            #price -= user_credit

        if booking.payment_type == PaymentType.PAY_CASH:
            # normal
            method = "Cash payment for #" + str(booking.id)
            cash = price
            amt = 0
            if user_credit < 0:
                pass
                #uc_change = -user_credit
        elif booking.payment_type == PaymentType.PAY_D4M_CREDIT:
            # two cases, full & partial payment
            if user_credit >= price:
                # full
                method = "D4M credit payment for #" + str(booking.id)
                amt = price
                cash = 0
                if driver_booked[1].perma:
                    #for perma, due = 0
                    total_bc = 0
                else:
                    #for tempo, deduct from due. -ve is ok!
                    total_bc -= amt
                uc_change = amt
            else:
                # partial
                method = "D4M credit and cash payment for #" + str(booking.id)
                if user_credit < 0:
                    amt = 0
                else:
                    amt = user_credit
                cash = price - amt
                if driver_booked[1].perma:
                    #for perma, due = cash they got
                    total_bc = cash
                else:
                    #for tempo, deduct from due. -ve is ok!
                    total_bc -= amt
                uc_change = amt
        else:
            amt = price
            cash = 0
            total_bc = 0
            method = "B2B trip"
            uc_change = amt
        try:
            if cur_trip.price != 0 and cur_trip.endtime:
                calc_dur = strfdelta2(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
                return jsonify({'success': 1, 'price': cur_trip.price, 'dur': calc_dur, 'message': 'Trip stopped successfully'})
            elif cur_trip.endtime:
                calc_dur = strfdelta2(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
                return jsonify({'success': 1, 'price': cur_trip.price, 'dur': calc_dur, 'message': 'Trip stopped successfully'})
            trans = UserTrans(booking.user, -amt*100, method, UserTrans.COMPLETED, -cash*100, stop=True, wall_b= user_credit, wall_a= user_credit-uc_change)
            db.session.add(trans)
            if not b2b:
                user_db.update({Users.credit: Users.credit - uc_change})
                db.session.flush()
            q = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver)
            driver_details = q.first()
            driver_wallet, driver_withdrawable = compute_driver_wallet(driver_details, total_bc)
            print("Amounts", driver_wallet, driver_withdrawable)
            dt = DriverTrans(booking.driver, -total_bc*100,
                             wall_a=driver_wallet, wall_b=driver_details.wallet,
                             with_a=driver_withdrawable, with_b=driver_details.withdrawable,
                             method="Booking %s" % str(booking.code),
                             status=DriverTrans.COMPLETED, stop=True
                            )
            book_pricing = db.session.query(BookPricing).filter(BookPricing.book_id == booking.id).first()
            trip_pricing = TripPricing(booking.id, book_pricing.base_ch, night_fare, ot_fare, total_due, book_pricing.insurance_ch, -total_bc, 0, 0)
            db.session.bulk_save_objects([dt, trip_pricing])
            q.update({DriverDetails.ride_count: DriverDetails.ride_count + 1,
                      DriverDetails.hour_count: DriverDetails.hour_count + int(d_hr),
                      DriverDetails.earning: DriverDetails.earning + price,
                      DriverDetails.rating: DriverDetails.rating + Rating.RATING_DEFAULT,
                      DriverDetails.owed: DriverDetails.owed + total_bc,
                      DriverDetails.wallet: driver_wallet,
                      DriverDetails.withdrawable: driver_withdrawable})
            
            if b2b:
                q.update({DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count + 1})
            Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: time_stop,
                                            Trip.trans: trans.id, Trip.due: total_bc, Trip.price: price,
                                            Trip.stop_lat: lat, Trip.stop_lng: lng,
                                            Trip.cgst: cgst, Trip.sgst: sgst,
                                            Trip.price_pre_tax: pre_tax,
                                            Trip.driver_trans: dt.id,
                                            Trip.net_rev: total_due,
                                            Trip.status: Trip.TRIP_STOPPED})
            db.session.commit()
        except Exception as e:
            print(e)
            db.session.rollback()
            return jsonify({'success': -1, 'price': -1, 'message': 'DB Error', 'status': 500})
        if b2b:
            return jsonify({'success': 1, 'message': 'Trip stopped successfully'})

        try:
            booking.did_release = True
            new_amount = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver).first().owed
            driver_paid = DriverPaid(booking.driver, -1 * total_bc, booking.id,
                                     new_amount, DriverPaid.SOURCE_TRIP)
            db.session.add(driver_paid)
            db.session.commit()
        except Exception as e:
            print(e)
            db.session.rollback()

        if not b2b:
            try:
                fb_db.collection(u'trip_started').document(str(booking.user)).delete()
            except Exception as e:
                pass
            # hack to fix firebase
            fb_price = cash + 0.0001
            fb_db.collection(u'trip_stopped').document(str(booking.user)).set({str(booking.id):
                                                                                      {'time': time_stop,
                                                                                       'driver': driver_name, 'dur': dur,
                                                                                       'price': fb_price,
                                                                                       'pic': driver_res.first().pic,
                                                                                       'surcharge': int(surcharge)}},
                                                                                 merge=True)

            try:
                target_user = user_db.first()
                stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
                msg_content_slack = "Hi, " + target_user.get_name() + "! Your trip (ID: " + str(booking.code) + ") with DRIVERS4ME was stopped by " + \
                              driver_booked[0].get_name() + " at approximately " + \
                              stop_time_ist.strftime("%I:%M %p %d/%m/%Y") + ". The total fare for the trip is " + \
                              str(price) + ". We hope you enjoyed the trip and will use our services again!"
                user_mobile = target_user.mobile
                msg_content = {"name": target_user.get_name(), "code": str(booking.code),
                               "driver-name": driver_booked[0].get_name(),
                               "stop-time": stop_time_ist.strftime("%I:%M %p %d/%m/%Y"),
                               "fare": str(price)}
                
                message = (
                    f"Hi, {msg_content['name']}! Your trip (ID: {msg_content['code']}) with Drivers4Me "
                    f"was stopped by {msg_content['driver-name']} at approximately {msg_content['stop-time']}. "
                    f"The total fare for the trip is {msg_content['fare']}. "
                    "We hope you enjoyed the trip and will use our services again!"
                )
                response = _sms.send_bulk_message_gupshup(
                    phone_numbers=[str(user_mobile)],
                    message=message,
                    mask= _sms.MASK,
                    dltTemplateId=_sms.TEMPLATE_ID_MAPPING['trip-stop'],
                    principalEntityId= _sms.PRINCIPAL_ENTITY_ID
                )
                
                print("Response: ",response,flush=True)
                #_sms.send_msg_flow(str(user_mobile), _sms.FLOWS["trip-stop"], msg_content)

                send_fcm_msg(target_user.id, title="Trip stopped", smalltext="Your booking #" + str(booking.code) + " was stopped by " + driver_booked[0].get_name(), bigtext=msg_content_slack)

                send_fcm_msg_driver(booking.driver, title="Trip completed.", smalltext="Please collect an amount of ₹" + str(cash) + " from the customer.",
                                    bigtext="Trip #" + str(booking.code) + " completed! Please collect an amount of ₹" + str(cash) + " from the customer.")
                send_slack_msg(2, msg_content_slack + "The cash amount was " + str(cash) + "and the credit used was " +
                              str(amt) + ". The due was " + str(total_bc) + ".")
            except Exception as e:
                print(e)
                pass
    except exc.IntegrityError as e:
        print(e)
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1, 'message': 'DB Error', 'status': 500})
    
    if booking.type < BookingParams.TYPE_C24:
        send_live_update_of_booking( book, booking.region)
    elif (booking.type == BookingParams.TYPE_B2B):
        send_live_update_of_booking( book, booking.region)
        affbook = db.session.query(AffBookingLogs).filter(AffBookingLogs.book_id == booking.id).first()
        send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=affbook.aff_id, booking_region=booking.region)
    live_update_data = {
                "userid": booking.user,
                }
    live_update_to_channel(live_update_data, room_name='estimate', type='estimate',region=booking.region, channel= 'estimate_completed_booking')
    driver_det = db.session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.user == driver_user).first()
    driver_data = {
            'driver_id': driver_det[0].id,
        }   
    live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[1].region, channel= 'update_driver')
    
    return jsonify({'success': 1, 'price': cash, 'due': total_due, 'dur': dur, 'surcharge': int(surcharge), 'cgst': cgst, 'sgst': sgst, 'pretax': pre_tax,
                        'stop_time': str(time_stop), 'sgst_pct': Price.get_sgst(), 'cgst_pct': Price.get_cgst(), 'insurance_ch': Price.get_insurance_ch(book),
                        'total_price': amt+cash})

def _stop_trip_wrapper(book_id, driver_user, time_stop, lat=-1, lng=-1, booking=None, trip=None):
    if booking == None:
        booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()

    if booking.type == BookingParams.TYPE_C24:
        b = db.session.query(C24Bookings).filter(C24Bookings.ref == book_id).first()
        return c24_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_OLX:
        b = db.session.query(OLXBookings).filter(OLXBookings.ref == book_id).first()
        return olx_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_ZOOMCAR:
        b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.ref == book_id).first()
        return zoomcar_stop_trip(b.id, driver_user, time_stop, lat, lng)
    elif booking.type == BookingParams.TYPE_REVV:
        b = db.session.query(RevvBookings).filter(RevvBookings.ref == book_id).first()
        return _revv_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_GUJRAL:
        b = db.session.query(GujralBookings).filter(GujralBookings.ref == book_id).first()
        return _gujral_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_CARDEKHO:
        b = db.session.query(CardekhoBookings).filter(CardekhoBookings.ref == book_id).first()
        return cardekho_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_BHANDARI:
        b = db.session.query(BhandariBookings).filter(BhandariBookings.ref == book_id).first()
        return bhandari_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_MAHINDRA:
        b = db.session.query(MahindraBookings).filter(MahindraBookings.ref == book_id).first()
        return mahindra_stop_trip(b.id, driver_user, time_stop)
    # elif booking.type == BookingParams.TYPE_REVV_V2:
    #     b = db.session.query(RevvV2Bookings).filter(RevvV2Bookings.ref == book_id).first()
    #     return revv_v2_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_SPINNY:
        b = db.session.query(SpinnyBookings).filter(SpinnyBookings.ref == book_id).first()
        return spinny_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_PRIDEHONDA:
        b = db.session.query(PrideHondaBookings).filter(PrideHondaBookings.ref == book_id).first()
        return pridehonda_stop_trip(b.id, driver_user, time_stop)
    elif booking.type == BookingParams.TYPE_B2B:
        return b2b_stop_trip(book_id, driver_user, time_stop, lat, lng, booking=booking, trip=trip)
    return _stop_trip(book_id, driver_user, time_stop, lat, lng, booking=booking, trip=trip)

def _restart_trip(book, driver_user,admin,admin_name):
    driver_res = Drivers.query.filter_by(user=driver_user)
    driver_user_res = Users.query.filter_by(id=driver_user)
    driver = driver_res.first().id
    
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    user_db = db.session.query(Users).filter(Users.id == booking.user).first() # restart - decreasing user book count
    
    user_db.bookcount = user_db.bookcount - 1
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip or not cur_trip.endtime:
        return jsonify({'success': -1, 'message': 'Trip is not stopped'}), 201
    try:
        orig_delta = int(convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0])
    except Exception:
        orig_delta = 0
    trans_amount = db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans).first().amount/100  # so for x D4MC => -x
    try:
        if trans_amount < 0:
            ut = UserTrans(uid=booking.user, amt=(-100*trans_amount), method="Reversal Restart: Booking #" + str(booking.code),
                               status=UserTrans.COMPLETED,admin_id=admin, admin_name=admin_name)
            db.session.add(ut)
        db.session.query(Users).filter(Users.id == booking.user).update({Users.credit: Users.credit - trans_amount})
        db.session.query(TripPricing).filter(
            TripPricing.book_id == book
        ).delete(synchronize_session=False)
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: 0, UserTrans.cash: 0}) # cash also needed for b2c
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count - 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.earning: DriverDetails.earning - cur_trip.price})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating - booking.user_rating})
        if booking.user_rating != Rating.RATING_DEFAULT or booking.driver_rating != Rating.RATING_DEFAULT:
            ref = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver)
            ref_data = ref.first()
            if ref_data:
                new_rating_count = ref_data.rating_count - 1
                ref.update({DriverDetails.rating_count: new_rating_count})
                rating_driver = 0 if new_rating_count <= 0 else ref_data.rating / new_rating_count
                db.session.query(Drivers).filter(Drivers.id == booking.driver).update({Drivers.rating: rating_driver})
            booking.user_rating =Rating.RATING_DEFAULT
            booking.driver_rating =Rating.RATING_DEFAULT
        dt_q = db.session.query(DriverTrans).filter(DriverTrans.id == cur_trip.driver_trans)
        dt = dt_q.first()
        if not dt: 
            raise Exception("No DriverTrans entry found for booking %d" % booking.id)
        wallet_b, wallet_a, withdrawable_b, withdrawable_a = \
            dt.wallet_before, dt.wallet_after, dt.withdrawable_before, dt.withdrawable_after
        wallet_delta = wallet_b - wallet_a
        withdrawable_delta = withdrawable_b - withdrawable_a
        drv_dt_q = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver)
        drv_dt = drv_dt_q.first()
        wallet, withdrawable = drv_dt.wallet, drv_dt.withdrawable
        print(wallet, withdrawable, wallet_a, wallet_b, withdrawable_a, withdrawable_b, "wallets")
        new_withdrawable = withdrawable + withdrawable_delta
        if new_withdrawable < 0:
            # Calculate the shortfall and adjust the wallet accordingly
            shortage = -new_withdrawable  # positive amount that withdrawable is short by
            new_withdrawable = 0
            new_wallet = wallet + wallet_delta - shortage
        else:
            new_wallet = wallet + wallet_delta
        drv_dt_q.update({DriverDetails.owed: DriverDetails.owed - cur_trip.due,
                          DriverDetails.wallet:new_wallet,
                          DriverDetails.withdrawable: new_withdrawable,
                        })
        t_due = cur_trip.due
        Trip.query.filter(Trip.book_id == booking.id).update({
            Trip.endtime: None, Trip.due: 0, Trip.price: 0,
            Trip.net_rev: 0,
            Trip.trans:None,
            Trip.driver_trans:None,
            Trip.status: Trip.TRIP_STARTED})
        t = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
        driver_paid = db.session.query(DriverPaid).filter(DriverPaid.source == booking.id).first()

        # Add new DT entry
        print(cur_trip.due)
        dt = DriverTrans(booking.driver, t_due*100,
                         wall_b=wallet, wall_a=new_wallet,
                         with_b=withdrawable, with_a=new_withdrawable,
                         method="Reversal Restart: Booking %s" % str(booking.code),
                         status=DriverTrans.COMPLETED, stop=True,admin_name=admin_name,admin_user_id=admin
                        )
        db.session.add(dt)
        if driver_paid:
            new_amount = int(drv_dt.owed) - int(cur_trip.due)
            driver_paid_rev = DriverPaid(booking.driver, -1 * int(driver_paid.amount),
                                         booking.id, new_amount,
                                         DriverPaid.SOURCE_TRIP)
            db.session.add(driver_paid_rev)
        try:
            doc_ref_ts = fb_db.collection(u'trip_started').document(str(booking.user))
            doc_ref_tsd = fb_db.collection(u'trip_started_details').document(str(booking.user))

            doc_ref_ts.set({str(booking.id): {'time': t.starttime, 'driver_id': driver_res.first().id,
                                              'driver': driver_user_res.first().get_name(), 'driver_pic': driver_res.first().pic}})
            doc_ref_tsd.set({str(booking.id): {'time': t.starttime, 'driver_id': driver_res.first().id,
                                               'driver': driver_user_res.first().get_name(), 'driver_pic': driver_res.first().pic}})
        except Exception as e:
            print(e)
        db.session.commit()
        _update_user_pending(booking.user)
        _update_driver_pending(booking.driver)
    except AttributeError as e:
        print("Error:", e)
        db.session.rollback()
        return jsonify({'success': -1,'message':'Internal Server Error'})
    
    send_live_update_of_booking( book, booking.region)
    
    return jsonify({'success': 1,'message':'Trip restarted successfully'})


def _restart_trip_b2b(book, driver_user,admin,admin_name):
    driver_res = Drivers.query.filter_by(user=driver_user)
    driver_user_res = Users.query.filter_by(id=driver_user)
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip or not cur_trip.endtime:
        return jsonify({'success': -1, 'message': 'Trip is not stopped'}), 201
    try:
        orig_delta = int(convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0])
    except Exception:
        orig_delta = 0
    try:
        aff_trans = db.session.query(AffiliateWalletLogs).filter(AffiliateWalletLogs.id == cur_trip.aff_trans).first()
        if not aff_trans:
            return jsonify({'success': -1, 'message': 'Affiliate transaction not found'}), 201
        trans_amount = -(aff_trans.amount)/100  
        affiliate_id = db.session.query(AffBookingLogs.aff_id).filter(AffBookingLogs.book_id == booking.id).scalar()
        affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
        awl = AffiliateWalletLogs(
                amt=(100 * trans_amount),
                to_account=affiliate_id,
                method="Reversal Restart: Booking #" + str(booking.code),
                wallet_before=affiliate.wallet,
                wallet_after=affiliate.wallet + trans_amount,
                source=AffiliateWalletLogs.SOURCE_ADMIN,
                admin=admin
            )
        db.session.add(awl)
        affiliate.wallet = affiliate.wallet + trans_amount
        db.session.query(TripPricing).filter(
            TripPricing.book_id == book
        ).delete(synchronize_session=False)
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count - 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count - 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.earning: DriverDetails.earning - cur_trip.price})
        # db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
        #     update({DriverDetails.rating: DriverDetails.rating - booking.user_rating})
        # if booking.user_rating != Rating.RATING_DEFAULT or booking.driver_rating != Rating.RATING_DEFAULT:
        #     ref = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver)
        #     ref_data = ref.first()
        #     if ref_data:
        #         new_rating_count = ref_data.rating_count - 1
        #         ref.update({DriverDetails.rating_count: new_rating_count})
        #         rating_driver = 0 if new_rating_count <= 0 else ref_data.rating / new_rating_count
        #         db.session.query(Drivers).filter(Drivers.id == booking.driver).update({Drivers.rating: rating_driver})
        #     booking.user_rating =Rating.RATING_DEFAULT
        #     booking.driver_rating =Rating.RATING_DEFAULT
        dt_q = db.session.query(DriverTrans).filter(DriverTrans.id == cur_trip.driver_trans)
        dt = dt_q.first()
        if not dt: 
            raise Exception("No DriverTrans entry found for booking %d" % booking.id)
        wallet_b, wallet_a, withdrawable_b, withdrawable_a = \
            dt.wallet_before, dt.wallet_after, dt.withdrawable_before, dt.withdrawable_after
        wallet_delta = wallet_b - wallet_a
        withdrawable_delta = withdrawable_b - withdrawable_a
        drv_dt_q = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver)
        drv_dt = drv_dt_q.first()
        wallet, withdrawable = drv_dt.wallet, drv_dt.withdrawable
        print(wallet, withdrawable, wallet_a, wallet_b, withdrawable_a, withdrawable_b, "wallets")
        new_withdrawable = withdrawable + withdrawable_delta
        if new_withdrawable < 0:
            # Calculate the shortfall and adjust the wallet accordingly
            shortage = -new_withdrawable  # positive amount that withdrawable is short by
            new_withdrawable = 0
            new_wallet = wallet + wallet_delta - shortage
        else:
            new_wallet = wallet + wallet_delta
        drv_dt_q.update({DriverDetails.owed: DriverDetails.owed - cur_trip.due,
                          DriverDetails.wallet: new_wallet,
                          DriverDetails.withdrawable: new_withdrawable,
                        })
        t_due = cur_trip.due
        Trip.query.filter(Trip.book_id == booking.id).update({
            Trip.endtime: None, Trip.due: 0, Trip.price: 0,
            Trip.net_rev: 0,
            Trip.aff_trans:None,
            Trip.driver_trans:None,
            Trip.status: Trip.TRIP_STARTED})
        t = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
        driver_paid = db.session.query(DriverPaid).filter(DriverPaid.source == booking.id).first()

        # Add new DT entry
        print(cur_trip.due)
        dt = DriverTrans(booking.driver, t_due*100,
                       wall_b=wallet, wall_a=new_wallet,
                         with_b=withdrawable, with_a=new_withdrawable,
                         method="Reversal Restart: Booking %s" % str(booking.code),
                         status=DriverTrans.COMPLETED, stop=True,admin_name=admin_name,admin_user_id=admin
                        )
        db.session.add(dt)
        if driver_paid:
            new_amount = int(drv_dt.owed) - int(cur_trip.due)
            driver_paid_rev = DriverPaid(booking.driver, -1 * int(driver_paid.amount),
                                         booking.id, new_amount,
                                         DriverPaid.SOURCE_TRIP)
            db.session.add(driver_paid_rev)
        try:
            doc_ref_ts = fb_db.collection(u'trip_started').document(str(booking.user))
            doc_ref_tsd = fb_db.collection(u'trip_started_details').document(str(booking.user))

            doc_ref_ts.set({str(booking.id): {'time': t.starttime, 'driver_id': driver_res.first().id,
                                              'driver': driver_user_res.first().get_name(), 'driver_pic': driver_res.first().pic}})
            doc_ref_tsd.set({str(booking.id): {'time': t.starttime, 'driver_id': driver_res.first().id,
                                               'driver': driver_user_res.first().get_name(), 'driver_pic': driver_res.first().pic}})
        except Exception as e:
            print(e)
        db.session.commit()
        # _update_user_pending(booking.user)
        _update_driver_pending(booking.driver)
    except AttributeError as e:
        print("Error:", e,flush=True)
        db.session.rollback()
        return jsonify({'success': -1,'message':'Internal Server Error'}),500
    
    send_live_update_of_booking( book, booking.region)
    
    return jsonify({'success': 1,'message':'Trip restarted successfully'})


def _upload_trip_start_pic(book_id, request, extra1=True, extra2=True, extra3=True, extra4=True, booking=None, trip=None):
    if booking == None:
        booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
    if not booking:
        return False
    if booking.type != BookingParams.TYPE_B2B:
        car_left = request.files["cleft"]
        car_right = request.files["cright"]
        car_back = request.files["cback"]
        car_front = request.files["cfront"]
        selfie = request.files["selfie"]
        url_car_left = upload_pic(car_left, path="book-" + str(book_id))
        url_car_right = upload_pic(car_right, path="book-" + str(book_id))
        url_car_back = upload_pic(car_back, path="book-" + str(book_id))
        url_car_front = upload_pic(car_front, path="book-" + str(book_id))
        url_selfie = upload_pic(selfie, path="book-" + str(book_id))
        url_extra1 = url_extra2 = url_extra3 = url_extra4 = ''
        if "extra1" in request.files:
            extra1 = request.files["extra1"]
            url_extra1 = upload_pic(extra1, path="book-" + str(book_id))
        if "extra2" in request.files:
            extra2 = request.files["extra2"]
            url_extra2 = upload_pic(extra2, path="book-" + str(book_id))
        if "extra3" in request.files:
            extra3 = request.files["extra3"]
            url_extra3 = upload_pic(extra3, path="book-" + str(book_id))
        if "extra4" in request.files:
            extra4 = request.files["extra4"]
            url_extra4 = upload_pic(extra4, path="book-" + str(book_id))
        tsp = TripStartPic(book_id=book_id, car_left=url_car_left, car_right=url_car_right,
                        car_back=url_car_back, car_front=url_car_front, selfie=url_selfie,
                        extra1=url_extra1, extra2=url_extra2, extra3=url_extra3, extra4=url_extra4)
        db.session.add(tsp)
    if booking.type == BookingParams.TYPE_B2B:
        success = b2b_upload_pic(booking, request, 'start')
        if not success:
            print("B2B trip stop image upload failed.")
            return False
    if trip == None:
        trip = fetch_booking_trip(book_id)
    if trip and trip.first():
        trip.update({Trip.status: Trip.TRIP_START_PIC})
    else:
        # To-do: Don't allow this
        pass
    try:
        db.session.commit()
    except exc.IntegrityError as e:
        print(e)
        db.session.rollback()
        return False
    if booking.type == BookingParams.TYPE_ZOOMCAR:
        try:
            _zoomcar_change_state(ZOOMCAR_STATE_PICKUP_COMPLETE, booking.id)
        except Exception as e:
            print("Could not set state: error %s" % str(e))
            return False
    return True

def _upload_trip_stop_pic(book_id, request, extra1=True, extra2=True, extra3=True, extra4=True, booking=None, trip=None):
    if booking == None:
        booking = db.session.query(Bookings).filter(Bookings.id == book_id).first()
    if not booking:
        return False
    if trip == None:
        trip = fetch_booking_trip(book_id)
    if trip and trip.first():
        trip.update({Trip.status: Trip.TRIP_STOP_PIC})
    else:
        # This really shouldn't happen...
        pass
    if booking.type != BookingParams.TYPE_B2B:
        car_left = request.files["cleft"]
        car_right = request.files["cright"]
        car_back = request.files["cback"]
        car_front = request.files["cfront"]
        url_car_left = upload_pic(car_left, path="book-" + str(book_id))
        url_car_right = upload_pic(car_right, path="book-" + str(book_id))
        url_car_back = upload_pic(car_back, path="book-" + str(book_id))
        url_car_front = upload_pic(car_front, path="book-" + str(book_id))
        url_extra1 = url_extra2 = url_extra3 = url_extra4 = ''
        if "extra1" in request.files:
            extra1 = request.files["extra1"]
            url_extra1 = upload_pic(extra1, path="book-" + str(book_id))
        if "extra2" in request.files:
            extra2 = request.files["extra2"]
            url_extra2 = upload_pic(extra2, path="book-" + str(book_id))
        if "extra3" in request.files:
            extra3 = request.files["extra3"]
            url_extra3 = upload_pic(extra3, path="book-" + str(book_id))
        if "extra4" in request.files:
            extra4 = request.files["extra4"]
            url_extra4 = upload_pic(extra4, path="book-" + str(book_id))
        tep = TripEndPic(book_id=book_id, car_left=url_car_left, car_right=url_car_right,
                                car_back=url_car_back, car_front=url_car_front,
                                extra1=url_extra1, extra2=url_extra2, extra3=url_extra3, extra4=url_extra4)
        db.session.add(tep)

    if booking.type == BookingParams.TYPE_B2B:
        success = b2b_upload_pic(booking, request, 'stop')
        if not success:
            print("B2B trip stop image upload failed.")
            return False
    try:
        db.session.commit()
    except exc.IntegrityError as e:
        print(e)
        db.session.rollback()
        return False
    
    if booking.type == BookingParams.TYPE_ZOOMCAR:
        try:
            _zoomcar_change_state(ZOOMCAR_STATE_DROP_COMPLETE, booking.id)
        except Exception as e:
            print("Could not set state: error %s" % str(e))
            return False
    return True


@trips.route('/api/trip/stop/pic', methods=['POST'])
@swag_from('/app/swagger_docs/trips/stop_trip_pic.yml')
@jwt_required()
def stop_trip_pic():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Unauthorized: Not a driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'Driver account disabled'}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2, 'message': 'Incomplete form: Missing book_id'}), 201
    tl = TripLog(request.form['book_id'], driver_user, driver_user, TripLog.ACTION_STOP_PIC)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    try:
        extra1 = bool(int(get_safe(request.form, 'ex1', 0)))
        extra2 = bool(int(get_safe(request.form, 'ex2', 0)))
        extra3 = bool(int(get_safe(request.form, 'ex3', 0)))
        extra4 = bool(int(get_safe(request.form, 'ex4', 0)))
        success = _upload_trip_stop_pic(int(request.form['book_id']), request, extra1, extra2, extra3, extra4)
        if not success:
            return jsonify({'success': -4, 'message': 'Failed to upload trip stop pictures'})
        else:
            return jsonify({'success': 1, 'message': 'Trip stop pictures uploaded successfully'})
    except Exception as e:
        print(e)
        return jsonify({'success': -3, 'message': 'Failed to log trip stop action'})


@trips.route('/api/trip/start/pic', methods=['POST'])
@swag_from('/app/swagger_docs/trips/start_trip_pic.yml')
@jwt_required()
def start_trip_pic():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Unauthorized: Not a driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'Driver account disabled'}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2, 'message': 'Incomplete form: Missing book_id'}), 201
    tl = TripLog(request.form['book_id'], driver_user, driver_user, TripLog.ACTION_START_PIC)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print(e)
        db.session.rollback()
    try:
        extra1 = bool(int(get_safe(request.form, 'ex1', 0)))
        extra2 = bool(int(get_safe(request.form, 'ex2', 0)))
        extra3 = bool(int(get_safe(request.form, 'ex3', 0)))
        extra4 = bool(int(get_safe(request.form, 'ex4', 0)))
        success = _upload_trip_start_pic(int(request.form['book_id']), request, extra1, extra2, extra3, extra4)
        if not success:
            return jsonify({'success': -4, 'message': 'Failed to upload trip start pictures'})
        else:
            return jsonify({'success': 1, 'message': 'Trip start pictures uploaded successfully'})
    except GeneratorExit as e:
        print(e)
        return jsonify({'success': -3, 'message': 'Failed to log trip action'})


@trips.route('/api/trip/start', methods=['POST'])
@swag_from('/app/swagger_docs/trips/start_trip.yml')
@jwt_required()
def start_trip():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'User is not a driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2, 'message': 'Incomplete form details'}), 201
    book = request.form['book_id']
    c24 = int(get_safe(request.form, 'c24',  0))
    revv = int(get_safe(request.form, 'revv',  0))
    zoomcar = int(get_safe(request.form, 'zoomcar', 0))
    gujral = int(get_safe(request.form, 'gujral', 0))
    olx = int(get_safe(request.form, 'olx', 0))
    cardekho = int(get_safe(request.form, 'cardekho', 0))
    bhandari = int(get_safe(request.form, 'bhandari', 0))
    lat = float(get_safe(request.form, 'lat', 0))
    lng = float(get_safe(request.form, 'lng', 0))
    b2b_id = True
    if c24 + revv + zoomcar + gujral + cardekho + bhandari > 1:
        return jsonify({'success': -1, 'price': -1, 'message': 'Multiple B2B services selected, only one allowed'}), 201
    if c24 == 1:
        b = db.session.query(C24Bookings).filter(C24Bookings.id == book).first()
    elif revv == 1:
        b = db.session.query(RevvBookings).filter(RevvBookings.id == book).first()
    elif zoomcar == 1:
        b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.id == book).first()
    elif gujral == 1:
        b = db.session.query(GujralBookings).filter(GujralBookings.id == book).first()
    elif olx == 1:
        b = db.session.query(OLXBookings).filter(OLXBookings.id == book).first()
    elif cardekho == 1:
        b = db.session.query(CardekhoBookings).filter(CardekhoBookings.id == book).first()
    elif bhandari == 1:
        b = db.session.query(BhandariBookings).filter(BhandariBookings.id == book).first()
    else:
        b2b_id = False
    if b2b_id and b:
        book = b.ref
    elif b2b_id and not b:
        return jsonify({'success': -1, 'message': 'B2B booking reference not found'})
    # Log action for trip
    tl = TripLog(book, driver_user, driver_user, TripLog.ACTION_START, lat, lng)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print("Trip start log error", e)
        db.session.rollback()
    return _start_trip(book, driver_user, datetime.datetime.utcnow(), lat, lng)


@trips.route('/api/trip/stop', methods=['POST'])
@swag_from('/app/swagger_docs/trips/stop_trip.yml')
@jwt_required()
def stop_trip():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message':'Unauthorised: Not Driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message':'Users restricted'}), 401
    if not complete(request.form, ['book_id']):
        return jsonify({'success': -2, 'message':'Incomplete form details'}), 201
    book = request.form['book_id']
    c24 = int(get_safe(request.form, 'c24',  0))
    revv = int(get_safe(request.form, 'revv',  0))
    zoomcar = int(get_safe(request.form, 'zoomcar', 0))
    gujral = int(get_safe(request.form, 'gujral', 0))
    olx = int(get_safe(request.form, 'olx', 0))
    cardekho = int(get_safe(request.form, 'cardekho', 0))
    bhandari = int(get_safe(request.form, 'bhandari', 0))
    lat = float(get_safe(request.form, 'lat', 0))
    lng = float(get_safe(request.form, 'lng', 0))

    if c24 + revv + zoomcar + gujral + cardekho + bhandari > 1:
        return jsonify({'success': -1, 'price': -1, 'message': 'Multiple B2B services selected, only one allowed'}), 201
    if c24 == 1:
        b = db.session.query(C24Bookings).filter(C24Bookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return c24_stop_trip(book, driver_user, datetime.datetime.utcnow())
    elif revv == 1:
        b = db.session.query(RevvBookings).filter(RevvBookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return _revv_stop_trip(book, driver_user, datetime.datetime.utcnow())
    elif zoomcar == 1:
        b = db.session.query(ZoomcarBookings).filter(ZoomcarBookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return zoomcar_stop_trip(book, driver_user, datetime.datetime.utcnow(), lat, lng)
    elif gujral == 1:
        b = db.session.query(GujralBookings).filter(GujralBookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return _gujral_stop_trip(book, driver_user, datetime.datetime.utcnow())
    elif olx == 1:
        b = db.session.query(OLXBookings).filter(OLXBookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return olx_stop_trip(book, driver_user, datetime.datetime.utcnow())
    elif cardekho == 1:
        b = db.session.query(CardekhoBookings).filter(CardekhoBookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return cardekho_stop_trip(book, driver_user, datetime.datetime.utcnow())
    elif bhandari == 1:
        b = db.session.query(BhandariBookings).filter(BhandariBookings.id == book).first()
        if b:
            # Log action for trip
            tl = TripLog(b.ref, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
            try:
                db.session.add(tl)
                db.session.commit()
            except Exception as e:
                print("Trip stop log error", e)
                db.session.rollback()
            return bhandari_stop_trip(book, driver_user, datetime.datetime.utcnow())
    # Log action for trip
    tl = TripLog(book, driver_user, driver_user, TripLog.ACTION_STOP, lat, lng)
    try:
        db.session.add(tl)
        db.session.commit()
    except Exception as e:
        print("Trip stop log error", e)
        db.session.rollback()

    return _stop_trip_wrapper(book, driver_user, datetime.datetime.utcnow(), lat, lng)


@trips.route('/api/trip/user_rate', methods=['POST'])
@swag_from('/app/swagger_docs/users/user_rate.yml')
@jwt_required()
def user_rate():
    user = get_jwt_identity()
    if not account_enabled(user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    try:
        claims = get_jwt()
        book = int(request.form['book_id'])
        user_rating = float(request.form['user_rating'])
        comment = get_safe(request.form, 'comment', "")
    except Exception:
        return jsonify({'success': -2, 'message': 'Incomplete form details'}), 201

    try:
        driver_booking = db.session.query(Drivers, Bookings).filter(Drivers.id == Bookings.driver). \
                                                filter(Bookings.id == book).first()
        if not driver_booking:
            return jsonify({"success": -1, 'message': 'No booking exist'})
        previous_rating = driver_booking[1].user_rating
        driver_name = db.session.query(Users).filter(Users.id == driver_booking[0].user).first().get_name()
        user_name = db.session.query(Users).filter(Users.id == user).first().get_name()
        msg_content = user_name + " rated " + driver_name + str(user_rating) + " stars"
        # let's hope previous_rating isn't 0!
        if user_rating <= 0:
            msg_content = msg_content + " but this was converted to 5 stars."
            user_rating = Rating.RATING_DEFAULT
        if previous_rating != Rating.RATING_DEFAULT:
            msg_content = msg_content + " but this was ignored."
            send_slack_msg(2, msg_content)
            return jsonify({'success': 1, 'message': 'Already rated by this user'})
        send_slack_msg(2, msg_content)
        Bookings.query.filter(Bookings.id == book).filter(Bookings.user == user) \
            .update({Bookings.user_rating: user_rating})
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_booking[0].id).first()
        if not details:
            if create_driver_details(driver_booking[0].id):
                details = db.session.query(DriverDetails).filter(
                    DriverDetails.driver_id == driver_booking[0].id).first()
                details.rating = Rating.RATING_DEFAULT
                details.ride_count = 1
            else:
                return jsonify({'success': -1, 'message': 'Failed to create details'})
        new_rating = details.rating + user_rating - previous_rating
        if user_rating != 0:
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_booking[0].id). \
                update({DriverDetails.rating: new_rating, DriverDetails.rating_count: DriverDetails.rating_count + 1})
        else:
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_booking[0].id). \
                update({DriverDetails.rating: new_rating})
        try:
            avg_rating = new_rating / details.rating_count
        except ZeroDivisionError:
            avg_rating = driver_booking[0].rating
        db.session.query(Trip).filter(Trip.book_id == book). \
            update({Trip.comment: comment})
        db.session.query(Drivers).filter(Drivers.id == driver_booking[0].id). \
            update({Drivers.rating: avg_rating})
        if user_rating < 3:
            notification = {
                'id':book, 
                'type': claims.get('name', 'unknown'),
                'username': str(driver_booking[0].user),
                'content': f'Booking received a low rating. Code:{driver_booking[1].code}',
                'imageUrl': '/assets/icons/bticons/warn.svg',
                'timestamp': int(time.time() * 1000)
            }
            send_notification_to_channel(notification, 'Low Rating:'+str(driver_booking[1].region))
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
        return jsonify({'success': -1, 'message': 'DB Error', 'status': 500})
    should_rate = True #int(Rating.is_rating_good(user, user_rating))
    
    return jsonify({'success': 1, 'app_rate': should_rate, 'message': 'Rating added successfully'})


@trips.route('/api/trip/driver_rate', methods=['POST'])
@swag_from('/app/swagger_docs/drivers/driver_rate.yml')
@jwt_required()
def driver_rate():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Unauthorized role: not Driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    driver_res = Drivers.query.filter_by(user=driver_user)
    driver = driver_res.first().id
    try:
        book = int(request.form['book_id'])
        driver_rating = float(request.form['driver_rating'])
        if driver_rating < Rating.RATING_DEFAULT:
            driver_rating = Rating.RATING_DEFAULT
        elif driver_rating > Rating.RATING_HIGH:
            driver_rating = Rating.RATING_HIGH
    except Exception:
        return jsonify({'success': -2, 'message': 'Server error'}), 201
    try:
        Bookings.query.filter(Bookings.id == book).filter(Bookings.driver == driver) \
            .update({Bookings.driver_rating: driver_rating})
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
        return jsonify({'success': -1, 'message': 'DB Error', 'status': 500})
    return jsonify({'success': 1, 'message': 'Rating updated successfully'})

@trips.route('/api/trip/user_rate/update', methods=['PUT'])
@jwt_required()
def user_rate_update():
    user = get_jwt_identity()
    if not account_enabled(user):
        return jsonify({'success': -1,'message':'not enabled'}), 401
    try:
        book = int(request.form['book_id'])
        user_rating = float(request.form['user_rating'])
        comment = get_safe(request.form, 'comment', "")
        trip = db.session.query(Trip).filter(Trip.book_id == book).first()
        if comment == "":
            comment = trip.comment
    except Exception:
        return jsonify({'success': -2}), 201

    try:
        if trip.status != Trip.TRIP_STOPPED:
            return jsonify({'success': -1}), 201
            
        driver_booking = db.session.query(Drivers, Bookings).filter(Drivers.id == Bookings.driver). \
                                                filter(Bookings.id == book).first()
        if not driver_booking:
            return jsonify({"success": -1})
        previous_rating = driver_booking[1].user_rating
        driver_name = db.session.query(Users).filter(Users.id == driver_booking[0].user).first().get_name()
        user_name = db.session.query(Users).filter(Users.id == user).first().get_name()
        msg_content = user_name + " rated " + driver_name + str(user_rating) + " stars"
        send_slack_msg(2, msg_content)
        if user_rating <= 0:
            user_rating = Rating.RATING_DEFAULT
        
        Bookings.query.filter(Bookings.id == book).filter(Bookings.user == user) \
            .update({Bookings.user_rating: user_rating})
        details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_booking[0].id).first()

        new_rating = details.rating + user_rating - previous_rating
        if user_rating != 0:
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_booking[0].id). \
                update({DriverDetails.rating: new_rating, DriverDetails.rating_count: DriverDetails.rating_count + 1})
        else:
            db.session.query(DriverDetails).filter(DriverDetails.driver_id == driver_booking[0].id). \
                update({DriverDetails.rating: new_rating})
            
        try:
            avg_rating = new_rating / details.rating_count
        except ZeroDivisionError:
            avg_rating = Rating.RATING_DEFAULT
        db.session.query(Trip).filter(Trip.book_id == book). \
            update({Trip.comment: comment})
        db.session.query(Drivers).filter(Drivers.id == driver_booking[0].id). \
            update({Drivers.rating: avg_rating})
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
        return jsonify({'success': -1})
    should_rate = True 
    return jsonify({'success': 1, 'app_rate': should_rate})


@trips.route('/api/trip/driver_rate/update', methods=['PUT'])
@jwt_required()
def driver_rate_update():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user).first()
    driver_id = driver_res.id
    try:
        book = int(request.form[' book_id'])
        driver_rating = float(request.form['driver_rating'])
        if driver_rating < Rating.RATING_DEFAULT:
            driver_rating = Rating.RATING_DEFAULT
        elif driver_rating > Rating.RATING_HIGH:
            driver_rating = Rating.RATING_HIGH
    except Exception:
        return jsonify({'success': -2}), 201
    try:
        trip = db.session.query(Trip).filter(Trip.book_id == book).first()
        if trip.status != Trip.TRIP_STOPPED:
            return jsonify({'success': -1}), 201
            
        Bookings.query.filter(Bookings.id == book).filter(Bookings.driver == driver_id) \
            .update({Bookings.driver_rating: driver_rating})
        db.session.commit()
    except exc.IntegrityError:
        db.session.rollback()
        return ({'success': -1})
    return jsonify({'success': 1})

def create_new_trip(booking, state_expected, driver_user, user, lat=-1.0, lng=-1.0):
    booking_id = booking.id
    # Create trip
    if state_expected != Trip.TRIP_INIT:
        return False
    trip = Trip(booking_id, status=Trip.TRIP_INIT)
    tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_INIT, lat, lng)
    try:
        db.session.add(tl)
        db.session.add(trip)
        db.session.commit()
    except exc.IntegrityError as e:
        print("Error in creating trip for", booking_id, ":", str(e))
        db.session.rollback()
        return False
    if booking.type < BookingParams.TYPE_C24:
        doc_ref_ts = fb_db.collection(u'trip_started').document(str(booking.user))
        try:
            doc_ref_ts.set({str(booking_id): {'time': datetime.datetime.utcnow(),
                                            'driver_id': booking.driver}})
        except Exception:
            pass
    try:
        if booking.type == BookingParams.TYPE_ZOOMCAR:
            _zoomcar_change_state(ZOOMCAR_STATE_STARTED, booking.id)
    except Exception as e:
        print("Could not set state: error %s" % str(e))
        
    if booking.type == BookingParams.TYPE_B2B:
        # handle live update of affiliate portal
        send_live_update_of_booking( booking.id, booking.region)
        affbook = db.session.query(AffBookingLogs).filter(AffBookingLogs.book_id == booking.id).first()
        send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=affbook.aff_id, booking_region=booking.region)
        pass 
    elif booking.type < BookingParams.TYPE_C24:
        send_live_update_of_booking( booking.id, booking.region)
    driver_det = db.session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.user == driver_user).first()
    driver_data = {
            'driver_id': driver_det[0].id,
        }   
    live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[1].region, channel= 'update_driver')
    
    return True


# def change_trip_state(booking_id, state_expected, driver_user, user, lat=-1.0, lng=-1.0, request=None):
#     """
#     Arguments:
#         booking_id (Int)
#         state_expected (Enum)
#         user (Int)
#         lat (Float)
#         lng (Float)
#         request (Flask Request)

#     Returns:
#         A dictionary with {"success": True} on success, {"success": False} on failure.
#         If state_expected equals the current state, it returns success for idempotency.
#     """
#     print("DEBUG: Attempting to change trip state for booking_id: {}, expected state: {}".format(booking_id, state_expected), flush=True)
#     ret = {"success": True}
#     trip = fetch_booking_trip(booking_id)
#     te = trip.first()
#     booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
#     drivers_user = Users.query.filter_by(id=driver_user).first()

#     if not booking or booking.valid < 0:
#         print("ERROR: Booking not found or invalid for booking_id:", booking_id, flush=True)
#         return {"success": False}

#     if not te:
#         print("DEBUG: No existing trip found for booking_id {}. Creating new trip.".format(booking_id), flush=True)
#         return create_new_trip(booking, state_expected, driver_user, user, lat, lng)

#     if state_expected == te.status:
#         print("DEBUG: Trip state for booking_id {} already at expected state: {}".format(booking_id, state_expected), flush=True)
#         return {"success": True}
#     elif state_expected != te.status - 1:
#         print("ERROR: Invalid state transition for booking_id {}: current status {}, attempted expected state {}".format(booking_id, te.status, state_expected), flush=True)
#         return None

#     tl = None
#     try:
#         if state_expected == Trip.TRIP_REACHED_SRC:
#             print("DEBUG: Transitioning to TRIP_REACHED_SRC for booking_id:", booking_id, flush=True)
#             tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_REACHED_SRC, lat, lng)
#             trip.update({Trip.status: state_expected})
#             try:
#                 if booking.type == BookingParams.TYPE_ZOOMCAR:
#                     print("DEBUG: Setting Zoomcar state to PICKUP_INIT for booking_id:", booking_id, flush=True)
#                     _zoomcar_change_state(ZOOMCAR_STATE_PICKUP_INIT, booking.id)
#             except Exception as e:
#                 print("ERROR: Could not set Zoomcar state (PICKUP_INIT):", str(e), flush=True)
#                 ret = {"success": False}

#         elif state_expected == Trip.TRIP_REACHED_DEST:
#             print("DEBUG: Transitioning to TRIP_REACHED_DEST for booking_id:", booking_id, flush=True)
#             tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_REACHED_DEST, lat, lng)
#             trip.update({Trip.status: state_expected})
#             try:
#                 if booking.type == BookingParams.TYPE_ZOOMCAR:
#                     print("DEBUG: Setting Zoomcar state to DROP_INIT for booking_id:", booking_id, flush=True)
#                     _zoomcar_change_state(ZOOMCAR_STATE_DROP_INIT, booking.id)
#             except Exception as e:
#                 print("ERROR: Could not set Zoomcar state (DROP_INIT):", str(e), flush=True)
#                 ret = {"success": False}

#         elif state_expected == Trip.TRIP_START_PIC:
#             print("DEBUG: Transitioning to TRIP_START_PIC for booking_id:", booking_id, flush=True)
#             tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_START_PIC, lat, lng)
#             res = _upload_trip_start_pic(booking_id, request, booking=booking, trip=trip)
#             if res:
#                 if booking.type == BookingParams.TYPE_BHANDARI:
#                     print("DEBUG: Processing Bhandari SMS flow for booking_id:", booking_id, flush=True)
#                     driver_booked = db.session.query(Users, Drivers).filter(Users.id == Drivers.user).filter(Users.id == driver_user).first()
#                     book_bhandari = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking_id).first()
#                     b2b_client = "Bhandari"
#                     user_mobile = book_bhandari.drop_mob
#                     if book_bhandari.trip_type == BhandariBookings.TRIP_PICKUP:
#                         source = "service center"
#                     elif book_bhandari.trip_type == BhandariBookings.TRIP_DELIVERY:
#                         source = "your location"
#                     msg_content = {
#                         "driver_details": "{}({})".format(driver_booked[0].get_name(), driver_booked[0].mobile),
#                         "b2b_client": b2b_client,
#                         "source": source
#                     }
#                     _sms.send_msg_flow(str(user_mobile), _sms.FLOWS["b2b-pickup-drop"], msg_content)
#             ret = {"success": res}

#         elif state_expected == Trip.TRIP_STOP_PIC:
#             print("DEBUG: Transitioning to TRIP_STOP_PIC for booking_id:", booking_id, flush=True)
#             tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_STOP_PIC, lat, lng)
#             res = _upload_trip_stop_pic(booking_id, request, booking=booking, trip=trip)
#             ret = {"success": res}

#         elif state_expected == Trip.TRIP_STARTED:
#             print("DEBUG: Transitioning to TRIP_STARTED for booking_id:", booking_id, flush=True)
#             tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_START, lat, lng)
#             if "start_time" in request.form:
#                 start_time = datetime.datetime.strptime(request.form["start_time"], '%Y-%m-%d %H:%M:%S.%f')
#             else:
#                 start_time = datetime.datetime.utcnow()
#             print("DEBUG: Starting trip at time:", start_time, flush=True)
#             res = _start_trip(booking_id, driver_user, start_time, lat, lng, booking=booking, trip=trip)
#             print("DEBUG: Trip start response:", res, flush=True)
#             ret = res.get_json(force=True)

#         elif state_expected == Trip.TRIP_STOPPED:
#             print("DEBUG: Transitioning to TRIP_STOPPED for booking_id:", booking_id, flush=True)
#             tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_STOP, lat, lng)
#             if "stop_time" in request.form:
#                 stop_time = datetime.datetime.strptime(request.form["stop_time"], '%Y-%m-%d %H:%M:%S.%f')
#             else:
#                 stop_time = datetime.datetime.utcnow()
#             print("DEBUG: Stopping trip at time:", stop_time, flush=True)
#             res = _stop_trip_wrapper(booking_id, driver_user, stop_time, lat, lng, booking=booking, trip=trip)
#             ret = res.get_json(force=True)

#         else:
#             print("ERROR: State {} not implemented for booking_id: {}".format(state_expected, booking_id), flush=True)
#             ret = {"success": False}

#     except Exception as ex:
#         print("EXCEPTION during state change for booking_id {}: {}".format(booking_id, str(ex)), flush=True)
#         ret = {"success": False}

#     try:
#         if tl:
#             db.session.add(tl)
#             db.session.commit()
#         if state_expected not in (Trip.TRIP_STARTED, Trip.TRIP_STOPPED):
#             send_live_update_of_booking(booking.id, booking.region)
#     except exc.IntegrityError as e:
#         print("ERROR: IntegrityError while changing trip state for booking_id {}: {}".format(booking_id, str(e)), flush=True)
#         db.session.rollback()
#         return {"success": False}

#     print("DEBUG: Returning from change_trip_state for booking_id {} with result: {}".format(booking_id, ret), flush=True)
#     return ret


def change_trip_state(booking_id, state_expected, driver_user, user, lat=-1.0, lng=-1.0, request=None):
    """
    Arguments:
        booking_id (Int)
        state_expected (Enum)
        user (Int)
        lat (Float)
        lng (Float)

    Returns:
        True on success, False on failure.
        If state_expected is -1 of current state, do transition, else
        return True to maintain idempotency.
    """
    ret = {"success": True}
    trip = fetch_booking_trip(booking_id)
    te = trip.first()
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id).first()
    drivers_user = Users.query.filter_by(id=driver_user).first()
    if not booking or booking.valid < 0:
        print('not booking or booking.valid < 0')
        return {"success": False}
    if not te:
        return create_new_trip(booking, state_expected, driver_user, user, lat, lng)
    if state_expected == te.status:
        return {"success": True}
    elif state_expected != te.status - 1:
        return None
    tl = None
    if state_expected == Trip.TRIP_REACHED_SRC:
        tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_REACHED_SRC, lat, lng)
        trip.update({Trip.status: state_expected})
        try:
            if booking.type == BookingParams.TYPE_ZOOMCAR:
                _zoomcar_change_state(ZOOMCAR_STATE_PICKUP_INIT, booking.id)
        except Exception as e:
            print("Could not set state: error %s" % str(e))
            ret = {"success": False}
    elif state_expected == Trip.TRIP_REACHED_DEST:
        tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_REACHED_DEST, lat, lng)
        trip.update({Trip.status: state_expected})
        try:
            if booking.type == BookingParams.TYPE_ZOOMCAR:
                _zoomcar_change_state(ZOOMCAR_STATE_DROP_INIT, booking.id)
        except Exception as e:
            print("Could not set state: error %s" % str(e))
            ret = {"success": False}
    elif state_expected == Trip.TRIP_START_PIC:
        tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_START_PIC, lat, lng)
        # This function updates trip internally
        res = _upload_trip_start_pic(booking_id, request, booking=booking, trip=trip)
        if res:
            if booking.type == BookingParams.TYPE_BHANDARI:
                driver_booked = db.session.query(Users, Drivers).filter(Users.id == Drivers.user).\
                                                                 filter(Users.id == driver_user).first()
                book_bhandari = db.session.query(BhandariBookings).filter(BhandariBookings.ref == booking_id).first()
                b2b_client = "Bhandari"
                user_mobile = book_bhandari.drop_mob
                if book_bhandari.trip_type == BhandariBookings.TRIP_PICKUP:
                    source = "service center"
                elif book_bhandari.trip_type == BhandariBookings.TRIP_DELIVERY:
                    source = "your location"
                msg_content = {
                        "driver_details": f"{driver_booked[0].get_name()}({driver_booked[0].mobile})",
                        "b2b_client": b2b_client,
                        "source": source
                    }

                message = (
                    f"Hi, your car has been successfully picked up from {msg_content['source']} "
                    f"by Drivers4Me driver {msg_content['driver_details']}. "
                    f"For any inquiries, please contact {msg_content['b2b_client']}. Thank you."
                )
                response = _sms.send_bulk_message_gupshup(
                    phone_numbers=[str(user_mobile)],
                    message=message,
                    mask= _sms.MASK,
                    dltTemplateId=_sms.TEMPLATE_ID_MAPPING['b2b-pickup-drop'],
                    principalEntityId= _sms.PRINCIPAL_ENTITY_ID
                )
                
                #_sms.send_msg_flow(str(user_mobile), _sms.FLOWS["b2b-pickup-drop"], msg_content)
        ret = {"success": res}
    elif state_expected == Trip.TRIP_STOP_PIC:
        tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_STOP_PIC, lat, lng)
        # This function updates trip internally
        res = _upload_trip_stop_pic(booking_id, request, booking=booking, trip=trip)
        ret = {"success": res}
    elif state_expected == Trip.TRIP_STARTED:
        tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_START, lat, lng)
        # This function updates trip internally
        if "start_time" in request.form:
            start_time = request.form["start_time"]
            start_time = datetime.datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S.%f')
        else:
            start_time = datetime.datetime.utcnow()
        res = _start_trip(booking_id, driver_user, start_time,
                          lat, lng, booking=booking, trip=trip)
        ret = res.get_json(force=True)
    elif state_expected == Trip.TRIP_STOPPED:
        tl = TripLog(booking_id, driver_user, user, TripLog.ACTION_STOP, lat, lng)
        # This function updates trip internally
        if "stop_time" in request.form:
            stop_time = request.form["stop_time"]
            stop_time = datetime.datetime.strptime(stop_time, '%Y-%m-%d %H:%M:%S.%f')
        else:
            stop_time = datetime.datetime.utcnow()
        res = _stop_trip_wrapper(booking_id, driver_user, stop_time,
                                 lat, lng, booking=booking, trip=trip)
        ret = res.get_json(force=True)
        
    else:
        # Not implemented
        ret = {"success": False}
    try:
        if tl:
            db.session.add(tl)
            db.session.commit()
        if (booking.type < BookingParams.TYPE_C24) and not(Trip.TRIP_STARTED == state_expected or Trip.TRIP_STOPPED == state_expected):
            send_live_update_of_booking( booking.id, booking.region)
        elif (booking.type == BookingParams.TYPE_B2B) and not(Trip.TRIP_STARTED == state_expected or Trip.TRIP_STOPPED == state_expected):
            send_live_update_of_booking( booking.id, booking.region)
            affbook = db.session.query(AffBookingLogs).filter(AffBookingLogs.book_id == booking.id).first()
            send_live_aff_booking_table(booking.id, channel='table', dest_aff_id=affbook.aff_id, booking_region=booking.region)
    except exc.IntegrityError as e:
        print("Error in changing trip state for", booking_id, ":", str(e))
        db.session.rollback()
        return {"success": False}
    return ret

@trips.route('/api/trip/state_change', methods=['POST'])
@swag_from('/app/swagger_docs/trips/trip_state_change.yml')
@jwt_required()
def trip_state_change():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message':'Unauthorized role: not Driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message':'User restricted'}), 401
    print(str(request.form))
    state_expected = int(get_safe(request.form, "state", -1))
    booking_id = int(get_safe(request.form, "booking_id", -1))
    lat = float(get_safe(request.form, "lat", -1))
    lng = float(get_safe(request.form, "lng", -1))
    if booking_id < 0 or state_expected < 0:
        return jsonify({"status": 400, "error":
            { "message": "Invalid params" } }), 400
    res = change_trip_state(booking_id, state_expected, driver_user, driver_user, lat, lng, request)
    print(res)
    if not res:
        return jsonify({"status": 400, "error":
            { "message": "Invalid status requested" } }), 400
    return jsonify({"status": 200, "data": res}), 200

@trips.route('/api/trip/otp_validate', methods=['POST'])
@swag_from('/app/swagger_docs/trips/booking_otp_validate.yml')
@jwt_required()
def booking_otp_validate():
    claims = get_jwt()
    if claims['roles'] != Users.ROLE_DRIVER:
        return jsonify({'success': -1, 'message': 'Unauthorized role: not driver'}), 401
    driver_user = get_jwt_identity()
    if not account_enabled(driver_user):
        return jsonify({'success': -1, 'message': 'User restricted'}), 401
    otp = get_safe(request.form, "otp", "")
    booking_id = int(get_safe(request.form, "booking_id", -1))
    if booking_id < 0:
        return jsonify({"status": 400, "error":
            { "message": "Invalid params" } }), 400
    booking = db.session.query(Bookings).filter(Bookings.id == booking_id)
    be = booking.first()
    if not be or be.otp != otp:
        return jsonify({"status": 403, "error":
            { "message": "OTP did not match" } }), 403
    def _regen_booking_otp(b):
        b.update({Bookings.otp: gen_otp(6)})
        try:
            db.session.commit()
        except Exception as e:
            print("Could not update OTP", str(e))
    _regen_booking_otp(booking)
    return jsonify({"status": 200, 'message': 'Server error'}), 200

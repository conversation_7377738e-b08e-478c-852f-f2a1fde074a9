tags:
  - Booking_admin
summary: Change the cancellation reason for a booking.
description: This API allows an admin to change the cancellation reason and update penalties for a booking.
parameters:
  - name: region
    in: formData
    type: string
    required: true
    description: A comma-separated list of region IDs for filtering
  - name: book_cancel_id
    in: formData
    type: integer
    required: true
    description: The ID of the booking cancellation to be updated.
  - name: new_reason
    in: formData
    type: integer
    required: true
    description: The new reason code for the cancellation.
  - name: new_reason_detail
    in: formData
    type: string
    required: true
    description: Additional details for the new cancellation reason.
responses:
  200:
    description: Success response indicating the cancellation reason was successfully updated.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 1
        message:
          type: string
          example: Changed Cancel Reason Successfully
  400:
    description: Bad request due to incomplete form data or other validation errors.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        message:
          type: string
          example: form details incomplete
  500:
    description: Internal server error indicating an issue while processing the request.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -2
        message:
          type: string
          example: Internal Server Error

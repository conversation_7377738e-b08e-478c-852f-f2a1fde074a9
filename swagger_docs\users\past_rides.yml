tags:
  - User
summary: Get Past User Rides
description: >
  This endpoint retrieves all the past rides of a user, including completed and canceled trips.
responses:
  200_a:  # Success response when past rides are found
    description: Past rides fetched successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "All past trips fetched successfully"
        data:
          type: array
          items:
            type: string
            description: JSON-encoded ride details
    examples:
      application/json:
        success: 1
        message: "All past trips fetched successfully"
        data:
          - "json_encoded_data_1"
          - "json_encoded_data_2"
  200_b:  # Success response when no past rides are found
    description: No past trips found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Failure message
          example: "No past trips"
    examples:
      application/json:
        success: -1
        message: "No past trips"
  401_a:  # First 401 Unauthorized for restricted user
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  401_b:  # Second 401 Unauthorized for user not found
    description: Unauthorized - User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -1
        message: "User not found"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Internal server error"
    examples:
      application/json:
        success: -1
        message: "Internal server error"

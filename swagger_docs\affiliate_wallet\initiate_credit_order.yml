tags:
  - Affiliate_Wallet
summary: Initiate Credit Order for Affiliate Representative
description: >
  This endpoint allows an affiliate representative to initiate a credit order.
parameters:
  - name: aff_id
    in: formData
    type: integer
    required: true
    description: The affiliate ID for whom the credit order is initiated.
  - name: amount
    in: formData
    type: integer
    required: true
    description: The amount to be credited in cents.
  - name: gateway
    in: formData
    type: integer
    required: true
    description: The payment gateway type.
responses:
  200:
    description: Credit order initiated successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        trans_id:
          type: integer
          description: Transaction ID.
          example: 12345
        order_id:
          type: string
          description: Order ID from payment gateway.
          example: "order_abc123"
        checksumhash:
          type: string
          description: Checksum hash for verification.
          example: "xyz123checksum"
        message:
          type: string
          description: Success message.
          example: "Initiated credit order successfully."
  401:
    description: Failed to get identity.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for error).
          example: -1
        message:
          type: string
          description: Error message.
          example: "Failed to get identity."
  404:
    description: Affiliate representative or affiliate not found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message.
          example: "Affiliate not found."
  201:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for incomplete form details).
          example: -2
        message:
          type: string
          description: Error message.
          example: "Incomplete form details."
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-5 for error).
          example: -5
        message:
          type: string
          description: Error message.
          example: "DB Error."

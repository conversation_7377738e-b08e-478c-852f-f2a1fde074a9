from main import app
from affiliate_b2b.affiliate_models import Affiliate,AffiliateCollections
from db_config import db, mdb
from flask import jsonify
from redis_config import redis_client
import json
import schedule
import time
from redis_config import is_redis_available


def read_redis_data(key):
    if is_redis_available():
        data = redis_client.get(key)
        return json.loads(data) if data else {}
    else:
        return {}

def write_redis_data(key, data):
    redis_client.set(key, json.dumps(data))

def put_data_to_redis_from_databases(client_name):
    try:
        affiliate_sql = db.session.query(Affiliate).filter_by(client_name=client_name).first()
        if not affiliate_sql:
            return {'error': 'Affiliate not found in SQL', 'success': 0}, 404

        form_details = {
            "client_name": affiliate_sql.client_name,
            "client_display_name": affiliate_sql.display_name,
            "select_group": ["Global", "Local"],
            "city": affiliate_sql.client_region
        }

        affiliate_mongo = AffiliateCollections.affiliates_details.find_one({'client_name': client_name})
        if not affiliate_mongo:
            return {"success": 0, "message": "Affiliate data not found in MongoDB"}, 404

        affiliate_mongo.pop('_id', None)

        customer_pricing_data = {
            "oneway": affiliate_mongo.get("customer_pricing_oneway"),
            "roundtrip": affiliate_mongo.get("customer_pricing_roundtrip")
        }

        driver_pricing_data = {
            "oneway": affiliate_mongo.get("driver_pricing_oneway"),
            "roundtrip": affiliate_mongo.get("driver_pricing_roundtrip")
        }

        pricing_cancellation_data = affiliate_mongo.get("pricing_cancellation_data", {})

        redis_data = {
            "master": affiliate_sql.master,
            "slaves": affiliate_sql.slave['slaves'],
            "form_field_oneway": affiliate_mongo.get("form_field_oneway"),
            "form_field_round": affiliate_mongo.get("form_field_round"),
            "form_details": form_details,
            "tripTypeLabel": affiliate_mongo.get("tripTypeLabel", ""),
            "tripTypePlaceholder": affiliate_mongo.get("tripTypePlaceholder", ""),
            "tripTypes": affiliate_mongo.get("trip_type")
        }

        redis_key = f'affiliate_{client_name}'

        redis_combined_data = {
            "data": read_redis_data(redis_key),
            "customer": read_redis_data(f'{redis_key}_pricing_customer'),
            "driver": read_redis_data(f'{redis_key}_pricing_driver'),
            'cancellation': read_redis_data(f'{redis_key}_pricing_cancellation')
        }

        db_combined_data = {
            "data": redis_data,
            "customer": customer_pricing_data,
            "driver": driver_pricing_data,
            "cancellation": pricing_cancellation_data
        }

        if redis_combined_data != db_combined_data:
            write_redis_data(redis_key, redis_data)
            write_redis_data(f'{redis_key}_pricing_customer', customer_pricing_data)
            write_redis_data(f'{redis_key}_pricing_driver', driver_pricing_data)
            write_redis_data(f'{redis_key}_pricing_cancellation', pricing_cancellation_data)

        return {'success': 1, 'message': 'Successfully copied data to Redis from MySQL+MongoDB'}, 200

    except Exception as e:
        return {'success': -1, 'error': 'Failed to retrieve affiliate data', 'details': str(e)}, 500

def get_all_affiliate_client_names():
    try:
        clients = db.session.query(Affiliate.id, Affiliate.client_name).all()
        client_names_list = [{'id': client.id, 'client_name': client.client_name} for client in clients]
        return {'success': 1, 'client_names': client_names_list}, 200
    except Exception as e:
        return {'success': -1, 'error': 'Failed to fetch client names', 'details': str(e)}, 500

def sync_all_affiliates_to_redis():
    try:
        clients_response, status_code = get_all_affiliate_client_names()
        if status_code != 200 or clients_response.get('success') != 1:
            return {'success': -1, 'message': 'Failed to fetch client names for synchronization'}

        client_names_list = clients_response.get('client_names', [])
        if not client_names_list:
            return {'success': 0, 'message': 'No client names available for synchronization'}

        failed_clients = []
        for client in client_names_list:
            client_name = client['client_name']
            print(f"Processing client: {client_name}")
            try:
                response, status = put_data_to_redis_from_databases(client_name)
                if status != 200 or response.get('success') != 1:
                    failed_clients.append(client_name)
            except Exception as e:
                print(f"Error processing client {client_name}: {e}")
                failed_clients.append(client_name)

        if failed_clients:
            return {
                'success': 0,
                'message': 'Some clients failed to synchronize',
                'failed_clients': failed_clients
            }

        return {'success': 1, 'message': 'All clients synchronized successfully'}
    except Exception as e:
        return {'success': -1, 'error': 'Failed to synchronize all affiliates', 'details': str(e)}

def schedule_sync():
    print("Starting sync...")
    result = sync_all_affiliates_to_redis()
    print(json.dumps(result, indent=4))

if __name__ == "__main__":
    # Schedule the sync to run every 5 minutes
    with app.app_context():
        schedule.every(1).minutes.do(schedule_sync)

        print("Scheduler initialized. Waiting for the first run...")
        while True:
            schedule.run_pending()
            time.sleep(1)

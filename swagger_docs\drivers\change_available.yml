tags:
  - Driver
summary: Change Driver Availability
description: >
  This API allows a driver to change their availability status (available/unavailable). Only drivers are authorized to use this endpoint.
parameters:
  - name: available
    in: formData
    type: boolean
    required: true
    description: Driver's availability status (true for available, false for unavailable)
responses:
  200:
    description: Driver's availability status was successfully updated.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for successful update)
          example: 1
        message:
          type: string
          description: Success message
          example: "Updated available state"
    examples:
      application/json:
        success: 1
        message: "Updated available state"
  401_a:
    description: Failed to get identity.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for failed identity)
          example: -1
        message:
          type: string
          description: Error message
          example: "Failed to get identity"
    examples:
      application/json:
        success: -1
        message: "Failed to get identity"
  401_b:
    description: User account is restricted.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for restricted account)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  200_b:
    description: Unauthorized role, Not a Driver.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for unauthorized role)
          example: -2
        message:
          type: string
          description: Error message
          example: "Unauthorized role: not Driver"
    examples:
      application/json:
        success: -2
        message: "Unauthorized role: not Driver"
  201_a:
    description: Incomplete form details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-3 for incomplete form)
          example: -3
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -3
        message: "Incomplete form details"
  201_b:
    description: Server error during execution.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-4 for server error)
          example: -4
        message:
          type: string
          description: Error message
          example: "Server error"
    examples:
      application/json:
        success: -4
        message: "Server error"

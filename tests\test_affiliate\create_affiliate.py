from datetime import datetime,<PERSON><PERSON><PERSON>
from models import db
from db_config import mdb
from affiliate_b2b.affiliate_models import Affiliate, DraftAffiliate, AffiliateWalletLogs,AffiliateCollections
import json
from conftest import create_master_affiliate
import redis_config
from redis_config import execute_with_fallback

#ignoring the mongodb part

def test_add_slave_affiliate(client,admin_login):
    auth_headers, admin = admin_login
    master_affiliate,master_redis_key= create_master_affiliate()
    slave_client_name='Slave_Client'

    # Prepare data for the slave affiliate (name: Slave_Client)
    slave_data = {
        "master": master_affiliate.id,
        "draft_title": "",
        "regions": "-1",
        "clientDetails": {
        "clientName": slave_client_name,
        "clientDisplayName": "gff",
        "clientDisplayLogo": "",
        "selectedCities": "0, 1, 6, 8",
        "tripTypeLabel": "Trip Type",
        "tripTypePlaceholder": "Enter Trip Type",
        "wallet_threshold": "20000",
        "cgst_percent": "9",
        "mapped_wallet_affiliate": "1",
        "sgst_percent": "9",
        "tripTypes": [
        {
        "trip_type_name": "Customer To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 1,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Customer To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 2,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Customer To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 3,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Customer",
        "trip_type_category": "One Way",
        "tripIndex": 4,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 5,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Spinny Workshop To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 6,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 7,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 8,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Partner Workshop To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 9,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To OEM",
        "trip_type_category": "One Way",
        "tripIndex": 10,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 11,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Partner Workshop To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 12,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Spinny Workshop To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 13,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Partner Workshop To Customer",
        "trip_type_category": "One Way",
        "tripIndex": 14,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 15,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 16,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 17,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        }
        ]
        },
        "form_fields_details": {
        "one_way": {
        "affiliateIdEnabled": "1",
        "affiliate_id_label": "Spinny Id",
        "affiliate_id_placeholder": "Enter Spinny Id",
        "destination_spoc_name_label": "Drop SPOC Name",
        "destination_spoc_name_placeholder": "Enter Drop SPOC Name",
        "destination_spoc_number_label": "Drop SPOC Number",
        "destination_spoc_number_placeholder": "Enter Drop SPOC Number",
        "dropoff_addr_label": "Drop off Address",
        "dropoff_addr_placeholder": "Enter Drop Off Address",
        "pickup_addr_label": "Pickup Address",
        "pickup_addr_placeholder": "Enter Pickup Address",
        "source_spoc_name_label": " Pickup SPOC Name",
        "source_spoc_name_placeholder": "Enter Pickup Spoc Name",
        "source_spoc_number_label": "Pickup SPOC Number",
        "source_spoc_number_placeholder": "Enter Pickup SPOC Number",
        "vehicleModelEnabled": "1",
        "city_label": "City",
        "city_placeholder": "Enter City",
        "custom_fields_one_way": [
        {
        "fieldType": "Dropdown",
        "title": "Business Category",
        "placeholder": "Enter Category",
        "dropdownFields": [
        "Assured",
        "BCM",
        "Auction"
        ]
        },
        {
        "fieldType": "Dropdown",
        "title": "Business Function",
        "placeholder": "Enter Function",
        "dropdownFields": [
        "Supply",
        "Demand",
        "Refurb",
        "Post Sales",
        "Pre Sales",
        "Servicing"
        ]
        }
        ]
        }
        },
        "customer_pricing_data": {
        "customer": {
        "oneway": {
        "base_breakup": [
        {
        "minDist": "0",
        "HikeType": "Flat hike",
        "Amount": "180"
        },
        {
        "minDist": "16",
        "HikeType": "Flat hike",
        "Amount": "250"
        },
        {
        "minDist": "30",
        "HikeType": "Per km hike",
        "Amount": "5"
        }
        ],
        "additional_charges": {
        "nightChEnabled": "0",
        "nightCharge": "0",
        "chargeTypeNight": "Flat",
        "nightChargeStartTime": "23:30:00",
        "nightChargeEndTime": "05:00:00",
        "defaultDuration": "2 hrs",
        "chargeTypeOvertime": "Flat",
        "overTimeCharge": "0",
        "insuranceChEnabled": "1",
        "insuranceCharge": "0",
        "price_mapping": []
        }
        }
        }
        },
        "driver_pricing_data": {
        "driver": {
        "oneway": {
        "base_breakup": [
        {
        "minDist": "0",
        "HikeType": "Flat hike",
        "Amount": "150"
        },
        {
        "minDist": "20",
        "HikeType": "Per km hike",
        "Amount": "220"
        },
        {
        "minDist": "40",
        "HikeType": "Per km hike",
        "Amount": "4"
        }
        ],
        "additional_charges": {
        "nightChargeStartTime": "23:30:00",
        "nightChargeEndTime": "06:00:00",
        "nightChEnabled": "0",
        "nightCharge": "0",
        "chargeTypeNight": "Flat",
        "defaultDuration": "2 hrs",
        "chargeTypeOvertime": "Flat",
        "overTimeCharge": "0"
        }
        }
        }
        },
        "pricing_cancellation": {
        "cancelCharges": [
        {
        "hourRange": "Max",
        "customerCancelCharge": "0",
        "driverCancelCharge": "0",
        "bothCancelCharge": {
        "customer": "0",
        "driver": "0"
        }
        }
        ],
        "cancelChargeStatic": {
        "staticCustomerCharge": "0",
        "staticDriverCharge": "0",
        "bothStaticCancelCharge": {
        "customer": "0",
        "driver": "0"
        }
        }
        }
        }

    # Send a POST request to add a slave affiliate
    response = client.post('/api/admin/affiliate/create_affiliate',
                            json=slave_data,headers=auth_headers)

    # Verify the response for adding a slave
    assert response.status_code == 201  # Check for correct status code
    res_data = response.get_json()
    assert res_data['success'] == 1  # Check for success response


    # Check if the slave affiliate was added to the RDBMS
    slave_affiliate = Affiliate.query.filter_by(client_name=slave_client_name).first()
    assert slave_affiliate is not None  # Ensure the slave exists
    assert slave_affiliate.master == master_affiliate.id  # Check the master ID

    # Check if the master's slave list is updated in Redis
    master_data_from_redis = execute_with_fallback('get', master_redis_key)
    assert master_data_from_redis is not None  # Ensure Redis key exists
    master_data_dict = json.loads(master_data_from_redis)
    print("Redis data:", master_data_from_redis)

    # Check that the slaves array includes the new slave's ID
    slaves_list = master_data_dict.get("slaves")
    assert slave_affiliate.client_name in slaves_list  # Ensure the slave name is in the master's slaves list

    # Cleanup
    db.session.delete(slave_affiliate)
    db.session.delete(master_affiliate)
    db.session.commit()

    redis_key = f'affiliate_{slave_client_name}'
    redis_key_pricing_customer = f'affiliate_{slave_client_name}_pricing_customer'
    redis_key_pricing_driver = f'affiliate_{slave_client_name}_pricing_driver'
    redis_key_pricing_cancellation = f'affiliate_{slave_client_name}_pricing_cancellation'

    #deleting all redis files after test
    execute_with_fallback('delete', redis_key)
    execute_with_fallback('delete', master_redis_key)
    execute_with_fallback('delete', redis_key_pricing_customer)
    execute_with_fallback('delete', redis_key_pricing_driver)
    execute_with_fallback('delete', redis_key_pricing_cancellation)
    assert execute_with_fallback('get', master_redis_key) is None


def test_get_affiliate_list(client, admin_login):
    auth_headers, admin = admin_login

    # Create sample affiliates for testing
    affiliate1 = Affiliate("Test_Client1", "dummy_name", "0", -1, 10, None, wallet_threshold=330.50, logo= "Logo 1")
    affiliate2 = Affiliate("Test_Client2", "dummy_name2", "0", -1, 10, None, wallet_threshold=1000.00, logo="Logo 2")

    db.session.add_all([affiliate1, affiliate2])
    db.session.commit()

    # Send GET request to the endpoint
    response = client.get('/api/admin/affiliate/affiliate_list', headers=auth_headers,query_string={'regions': 0})
    data = response.get_json()

    # Verify response
    assert response.status_code == 200
    assert data['success'] == 1
    assert len(data['client_details']) > 0

    # Verify details for the test clients
    client_names = [client['client_name'] for client in data['client_details']]
    assert 'Test_Client1' in client_names
    assert 'Test_Client2' in client_names

    # Clean up test data
    db.session.commit()


def test_get_affiliate_data(client, admin_login):
    # Log in as an admin to get headers
    auth_headers, admin = admin_login

    # Setup: Create a master affiliate and define the Redis key
    master_affiliate="Master_Client"
    master_redis_key= f'affiliate_{master_affiliate}'
    master_redis_key_pricing_customer= f'affiliate_{master_affiliate}_pricing_customer'
    master_redis_key_pricing_driver= f'affiliate_{master_affiliate}_pricing_driver'
    master_redis_key_pricing_cancellation= f'affiliate_{master_affiliate}_pricing_cancellation'

    master_data = {
        "master": -1,
        "draft_title": "",
        "regions": "-1",
        "clientDetails": {
        "clientName": master_affiliate,
        "clientDisplayName": "gff",
        "clientDisplayLogo": "",
        "selectedCities": "0, 1, 6, 8",
        "tripTypeLabel": "Trip Type",
        "tripTypePlaceholder": "Enter Trip Type",
        "wallet_threshold": "20000",
        "cgst_percent": "9",
        "mapped_wallet_affiliate": "1",
        "sgst_percent": "9",
        "tripTypes": [
        {
        "trip_type_name": "Customer To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 1,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Customer To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 2,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Customer To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 3,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Customer",
        "trip_type_category": "One Way",
        "tripIndex": 4,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 5,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Spinny Workshop To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 6,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 7,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 8,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Partner Workshop To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 9,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Hub To OEM",
        "trip_type_category": "One Way",
        "tripIndex": 10,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Hub",
        "trip_type_category": "One Way",
        "tripIndex": 11,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Partner Workshop To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 12,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Spinny Workshop To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 13,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "Partner Workshop To Customer",
        "trip_type_category": "One Way",
        "tripIndex": 14,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 15,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Partner Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 16,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        },
        {
        "trip_type_name": "OEM To Spinny Workshop",
        "trip_type_category": "One Way",
        "tripIndex": 17,
        "tripType": "One Way",
        "startImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload selfie image",
        "imageTitle": "Selfie",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "start",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ],
        "stopImages": [
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car left image",
        "imageTitle": "Car Left",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car right image",
        "imageTitle": "Car Right",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car back image",
        "imageTitle": "Car Back",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload stop car front image",
        "imageTitle": "Car Front",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Mandatory"
        },
        {
        "imageDataType": "Image",
        "imageDesc": "Upload Odometer Image",
        "imageTitle": "Odometer",
        "imageTripStage": "stop",
        "imageTripType": "One Way",
        "imageType": "Optional"
        }
        ]
        }
        ]
        },
        "form_fields_details": {
        "one_way": {
        "affiliateIdEnabled": "1",
        "affiliate_id_label": "Spinny Id",
        "affiliate_id_placeholder": "Enter Spinny Id",
        "destination_spoc_name_label": "Drop SPOC Name",
        "destination_spoc_name_placeholder": "Enter Drop SPOC Name",
        "destination_spoc_number_label": "Drop SPOC Number",
        "destination_spoc_number_placeholder": "Enter Drop SPOC Number",
        "dropoff_addr_label": "Drop off Address",
        "dropoff_addr_placeholder": "Enter Drop Off Address",
        "pickup_addr_label": "Pickup Address",
        "pickup_addr_placeholder": "Enter Pickup Address",
        "source_spoc_name_label": " Pickup SPOC Name",
        "source_spoc_name_placeholder": "Enter Pickup Spoc Name",
        "source_spoc_number_label": "Pickup SPOC Number",
        "source_spoc_number_placeholder": "Enter Pickup SPOC Number",
        "vehicleModelEnabled": "1",
        "city_label": "City",
        "city_placeholder": "Enter City",
        "custom_fields_one_way": [
        {
        "fieldType": "Dropdown",
        "title": "Business Category",
        "placeholder": "Enter Category",
        "dropdownFields": [
        "Assured",
        "BCM",
        "Auction"
        ]
        },
        {
        "fieldType": "Dropdown",
        "title": "Business Function",
        "placeholder": "Enter Function",
        "dropdownFields": [
        "Supply",
        "Demand",
        "Refurb",
        "Post Sales",
        "Pre Sales",
        "Servicing"
        ]
        }
        ]
        }
        },
        "customer_pricing_data": {
        "customer": {
        "oneway": {
        "base_breakup": [
        {
        "minDist": "0",
        "HikeType": "Flat hike",
        "Amount": "180"
        },
        {
        "minDist": "16",
        "HikeType": "Flat hike",
        "Amount": "250"
        },
        {
        "minDist": "30",
        "HikeType": "Per km hike",
        "Amount": "5"
        }
        ],
        "additional_charges": {
        "nightChEnabled": "0",
        "nightCharge": "0",
        "chargeTypeNight": "Flat",
        "nightChargeStartTime": "23:30:00",
        "nightChargeEndTime": "05:00:00",
        "defaultDuration": "2 hrs",
        "chargeTypeOvertime": "Flat",
        "overTimeCharge": "0",
        "insuranceChEnabled": "1",
        "insuranceCharge": "0",
        "price_mapping": []
        }
        }
        }
        },
        "driver_pricing_data": {
        "driver": {
        "oneway": {
        "base_breakup": [
        {
        "minDist": "0",
        "HikeType": "Flat hike",
        "Amount": "150"
        },
        {
        "minDist": "20",
        "HikeType": "Per km hike",
        "Amount": "220"
        },
        {
        "minDist": "40",
        "HikeType": "Per km hike",
        "Amount": "4"
        }
        ],
        "additional_charges": {
        "nightChargeStartTime": "23:30:00",
        "nightChargeEndTime": "06:00:00",
        "nightChEnabled": "0",
        "nightCharge": "0",
        "chargeTypeNight": "Flat",
        "defaultDuration": "2 hrs",
        "chargeTypeOvertime": "Flat",
        "overTimeCharge": "0"
        }
        }
        }
        },
        "pricing_cancellation": {
        "cancelCharges": [
        {
        "hourRange": "Max",
        "customerCancelCharge": "0",
        "driverCancelCharge": "0",
        "bothCancelCharge": {
        "customer": "0",
        "driver": "0"
        }
        }
        ],
        "cancelChargeStatic": {
        "staticCustomerCharge": "0",
        "staticDriverCharge": "0",
        "bothStaticCancelCharge": {
        "customer": "0",
        "driver": "0"
        }
        }
        }
        }
    
    redis_config.redis_available = True
    
    response = client.post(
        '/api/admin/affiliate/create_affiliate',
        json= master_data,
        headers=auth_headers
    )

    # Send a POST request to get affiliate data
    response = client.post(
        '/api/admin/affiliate/get_affiliate_data',
        json= {"client_name": master_affiliate,"regions":"0"},
        headers=auth_headers
    )

    print("response:",response.get_json())
    # Check the response status code
    assert response.status_code == 200
    res_data = response.get_json()

    # Verify the response structure
    assert res_data['success'] == 1
    assert res_data['source'] == 'redis'  # Verify that data was fetched from Redis
    assert res_data['data']['form_details']['client_name'] == master_affiliate
    assert res_data['data']['tripTypeLabel'] == "Trip Type"
    assert 'customer_pricing_data' in res_data
    assert 'driver_pricing_data' in res_data
    assert 'pricing_cancellation_data' in res_data

    # Check the data structure when Redis is not available
    execute_with_fallback('delete', master_redis_key)
    assert not execute_with_fallback('get',master_redis_key), "Redis key deletion failed."

    print("Master redis key: ",master_redis_key,flush=True)

    response = client.post(
        '/api/admin/affiliate/get_affiliate_data',
        json={"client_name": master_affiliate,"regions":"0"},
        headers=auth_headers
    )

    # Check if MySQL and MongoDB fallback is working
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert res_data['source'] == 'mysql_mongo'  # Verify that data was fetched from MySQL and MongoDB
    assert res_data['data']['form_details']['client_name'] == master_affiliate

    # Cleanup
    execute_with_fallback('delete', master_redis_key)
    execute_with_fallback('delete', master_redis_key_pricing_customer)
    execute_with_fallback('delete', master_redis_key_pricing_driver)
    execute_with_fallback('delete', master_redis_key_pricing_cancellation)

    assert execute_with_fallback('get',master_redis_key_pricing_cancellation) is None


def test_mark_favourite(client, admin_login):
    # Step 1: Log in as an admin user to get authentication headers
    auth_headers, admin = admin_login

    # Step 2: Setup - Create a test draft belonging to the admin user
    draft = DraftAffiliate(
            client_name="Test_Client",
            display="Test Display",
            client_region="Region_1",
            master=-1,                    # Set according to your application's logic
            admin=4,           # Admin ID
            draft_creator_id=admin.id, # Creator ID
            draft_title="Test Draft Title",
            is_favourite=False,
            mapped_wallet_affiliate=-1
        )
    db.session.add(draft)
    db.session.commit()

    # Step 3: Test a successful marking of a draft as favorite
    response = client.post(
        '/api/admin/affiliate/change_favourite',
        json={"draft_id": draft.id,"regions":"0"},
        headers=auth_headers
    )
    print("response: ", response.get_json())
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert res_data['message'] == 'Draft marked as favourite successfully'

    # Verify that the draft's is_favourite attribute is now True
    db.session.refresh(draft)
    assert draft.is_favourite is True

    response = client.post(
        '/api/admin/affiliate/change_favourite',
        json={"draft_id": draft.id,"regions":"0"},  # No draft_id provided
        headers=auth_headers
    )
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert res_data['message'] == 'Draft unmarked as not favourite successfully'

    # Step 4: Test failure when draft ID is missing
    response = client.post(
        '/api/admin/affiliate/change_favourite',
        json={"regions":"0"},  # No draft_id provided
        headers=auth_headers
    )
    assert response.status_code == 400
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['message'] == 'Draft ID is required'

    # Step 5: Test failure if draft doesn’t exist
    response = client.post(
        '/api/admin/affiliate/change_favourite',
        json={"draft_id": 99999,"regions":"0"},  # Non-existent draft_id
        headers=auth_headers
    )
    assert response.status_code == 404
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['message'] == 'Draft not found or does not belong to you'

    # Step 6: Test failure if the draft belongs to another user
    # Create a draft for another user
    other_user_draft = DraftAffiliate(
        client_name="Test_Client 2",
        display="Test Display 2",
        client_region="Region_2",
        master=-1,                    # Set according to your application's logic
        admin=5,
        draft_creator_id=admin.id + 1,  # Different user ID
        draft_title="Test Draft Title 2",
        is_favourite=False,
        mapped_wallet_affiliate=-1
    )
    db.session.add(other_user_draft)
    db.session.commit()

    response = client.post(
        '/api/admin/affiliate/change_favourite',
        json={"draft_id": other_user_draft.id,"regions":"0"},
        headers=auth_headers
    )
    assert response.status_code == 403
    res_data = response.get_json()
    assert res_data['success'] == -1
    assert res_data['message'] == 'Sorry! you are not authorized to use this Draft!'


def test_delete_draft_affiliate(client, admin_login):
    # Step 1: Log in as an admin user to get authentication headers
    auth_headers, admin = admin_login
    form_data= {
    "master": -1,
    "draft_title": "Draft1",
    "regions": "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19",
    "clientDetails": {
        "clientName": "Car23OS",
        "clientDisplayName": "Car23OS",
        "selectedCities": "0, 6, 15",
        "tripTypeLabel": "Trip Type",
        "tripTypePlaceholder": "Select Trip Type",
        "wallet_threshold": "100.0",
        "cgst_percent":"18",
        "sgst_percent":"18",
        "mapped_wallet_affiliate": -1,
        "tripTypes": [
            {
                "trip_type_name": "Customer to hub",
                "trip_type_category": "Outstation Round",
                "tripIndex": 1,
                "tripType": "Outstation Round",
                "startImages": [
                    {
                        "imageTitle": "OS",
                        "imageType": "Optional",
                        "imageDesc": "dds",
                        "imageTripType": "Outstation Round",
                        "imageTripStage": "start"
                    }
                ],
                "stopImages": [
                    {
                        "imageTitle": "OS-1",
                        "imageType": "Optional",
                        "imageDesc": "cxc",
                        "imageTripType": "Outstation Round",
                        "imageTripStage": "stop"
                    }
                ]
            }
        ]
    },
    "form_fields_details": {
        "round": {
            "affiliateIdEnabled": "1",
            "affiliate_id_label": "Car23 OS ID",
            "affiliate_id_placeholder": "Enter OS Id",
            "noOfBookingsEnabled": "1",
            "no_of_bookings_label": "Number of bookings",
            "no_of_bookings_placeholder": "Enter Number of bookings",
            "destination_spoc_name_label": "Destination Spoc Name",
            "destination_spoc_name_placeholder": "Enter Dest Spoc Name",
            "destination_spoc_number_label": "Destination Spoc Number",
            "destination_spoc_number_placeholder": "Enter Dest Spoc Number",
            "dropoff_addr_label": "Drop off Address",
            "dropoff_addr_placeholder": "Drop off Address",
            "pickup_addr_label": "Pickup Address",
            "pickup_addr_placeholder": "Pickup Address",
            "source_spoc_name_label": "Source Spoc Name",
            "source_spoc_name_placeholder": "Enter Spoc Name",
            "source_spoc_number_label": "Source Spoc Number",
            "source_spoc_number_placeholder": "Enter Spoc Number",
            "vehicleModelEnabled": "",
            "city_label": "City",
            "city_placeholder": "Select Region",
            "custom_fields_round": [
                {
                    "fieldType": "Dropdown",
                    "title": "Business Category",
                    "placeholder": "Select Category",
                    "dropdownFields": [
                        "Assured",
                        "Auction",
                        "BCM"
                    ]
                },
                {
                    "fieldType": "Dropdown",
                    "title": "Business Function",
                    "placeholder": "Select",
                    "dropdownFields": [
                        "Supply",
                        "Demand",
                        "Refurb",
                        "Post-Sales",
                        "Pre-Sales",
                        "Servicing"
                    ]
                }
            ]
        }
    },
    "customer_pricing_data": {
        "customer": {
            "outstationtrip": {
                "base_breakup": {
                    "minHour": "10",
                    "minCharge": "122",
                    "hourlyCharge": "21",
                    "overtimeCharge": "21",
                    "overtimeType": "per min"
                },
                "additional_charges": {
                    "nightChargeStartTime": "23:54:00",
                    "nightChargeEndTime": "05:24:00",
                    "nightChEnabled": "1",
                    "nightCharge": "55",
                    "chargeTypeNight": "Flat"
                }
            }
        }
    },
    "driver_pricing_data": {
        "driver": {
            "outstationtrip": {
                "base_breakup": {
                    "minHour": "11",
                    "minCharge": "33",
                    "hourlyCharge": "22",
                    "overtimeCharge": "21",
                    "overtimeType": "Flat"
                },
                "additional_charges": {
                    "nightChargeStartTime": "23:30:00",
                    "nightChargeEndTime": "05:00:00",
                    "nightChEnabled": "1",
                    "nightCharge": "212",
                    "chargeTypeNight": "Flat"
                }
            }
        }
    },
    "pricing_cancellation": {
        "cancelCharges": [
            {
                "hourRange": "Max",
                "customerCancelCharge": "5",
                "driverCancelCharge": "15",
                "bothCancelCharge": {
                    "customer": "15",
                    "driver": "15"
                }
            },
            {
                "hourRange": "1",
                "customerCancelCharge": "10",
                "driverCancelCharge": "30",
                "bothCancelCharge": {
                    "customer": "25",
                    "driver": "30"
                }
            },
            {
                "hourRange": "2",
                "customerCancelCharge": "20",
                "driverCancelCharge": "50",
                "bothCancelCharge": {
                    "customer": "40",
                    "driver": "50"
                }
            },
            {
                "hourRange": "6",
                "customerCancelCharge": "30",
                "driverCancelCharge": "75",
                "bothCancelCharge": {
                    "customer": "55",
                    "driver": "75"
                }
            },
            {
                "hourRange": "12",
                "customerCancelCharge": "45",
                "driverCancelCharge": "100",
                "bothCancelCharge": {
                    "customer": "75",
                    "driver": "100"
                }
            }
        ],
        "cancelChargeStatic": {
            "staticCustomerCharge": "45",
            "staticDriverCharge": "99",
            "bothStaticCancelCharge": {
                "customer": "99",
                "driver": "99"
            }
        }
    }
}

    response = client.post(
        '/api/admin/affiliate/save_draft',
        json= form_data,
        headers=auth_headers
    )
    assert response.status_code == 201

    draft = DraftAffiliate.query.filter_by(id= 1).first()
    assert draft is not None

    # Step 3: Test a successful deletion of the draft
    response = client.delete(
        '/api/admin/affiliate/delete_draft',
        json={"draft_id": 1,"regions":"0"},
        headers=auth_headers
    )
    assert response.status_code == 200
    res_data = response.get_json()
    assert res_data['success'] == 1
    assert res_data['message'] == 'Draft deleted successfully'

    # Verify that the draft no longer exists in the database
    deleted_draft = DraftAffiliate.query.filter_by(id= 1).first()
    assert deleted_draft is None
    deleted_mongo_data = AffiliateCollections.draft_affiliates.find_one({"mysql_id": 1})
    assert deleted_mongo_data is None, "Draft should be deleted from MongoDB"

# def test_transfer_money(client, admin_login):
#     # Step 1: Log in as an admin to get authentication headers
#     auth_headers, admin = admin_login

#     # Step 2: Setup - Create test affiliates with initial wallet balances
#     affiliate1 = Affiliate(client_name="Affiliate1", display="Affiliate 1", client_region="Region 1", master=-1, admin=admin.id, notification=1,mapped_wallet_affiliate=-1)
#     affiliate1.wallet = 500.0

#     affiliate2 = Affiliate(client_name="Affiliate2", display="Affiliate 2", client_region="Region 2", master=-1, admin=admin.id, notification=1,mapped_wallet_affiliate=-1)
#     affiliate2.wallet = 100.0

#     db.session.add_all([affiliate1, affiliate2])
#     db.session.commit()

#     # Step 3: Test a successful money transfer
#     transfer_payload = {
#         "from_affiliate_id": affiliate1.id,
#         "to_affiliate_id": affiliate2.id,
#         "amount": 200.0,
#         "transaction_type": "transfer",
#         "utr": "UTR12345678"
#     }

#     response = client.post('/api/affiliate/transfer_money', json=transfer_payload, headers=auth_headers)
#     res_data = response.get_json()

#     print("respose: ", res_data)
#     assert response.status_code == 200
#     assert res_data["success"] == 1
#     assert res_data["from_affiliate"]["id"] == affiliate1.id
#     assert res_data["from_affiliate"]["new_wallet_balance"] == 300.0
#     assert res_data["to_affiliate"]["id"] == affiliate2.id
#     assert res_data["to_affiliate"]["new_wallet_balance"] == 300.0

# def test_get_wallet_logs(client, admin_login):
#     # Step 1: Log in as an admin to get authentication headers
#     auth_headers, admin = admin_login

#     # Step 2: Setup - Create test affiliates and wallet log entries
#     affiliate = Affiliate(client_name="AffiliateLogTest", display="Affiliate Log Test", client_region="Region Test", master=-1, admin=admin.id, notification=1)
#     db.session.add(affiliate)
#     db.session.commit()

#     log1 = AffiliateWalletLogs(
#         transaction_id="txn1",
#         transaction_type="transfer",
#         affiliate_id=affiliate.id,
#         wallet_before=500.0,
#         wallet_after=300.0,
#         amount=200.0,
#         utr="UTR123",
#         transaction_hash="hash1"
#     )

#     log2 = AffiliateWalletLogs(
#         transaction_id="txn2",
#         transaction_type="recharge",
#         affiliate_id=affiliate.id,
#         wallet_before=300.0,
#         wallet_after=400.0,
#         amount=100.0,
#         utr="UTR456",
#         transaction_hash="hash2"
#     )

#     db.session.add_all([log1, log2])
#     db.session.commit()

#     # Step 3: Test without filters (fetch all logs)
#     response = client.get('/api/affiliate/wallet_logs', headers=auth_headers)
#     res_data = response.get_json()

#     assert response.status_code == 200
#     assert "logs" in res_data
#     assert len(res_data["logs"]) == 2

#     # Verify log details
#     log_ids = [log["transaction_id"] for log in res_data["logs"]]
#     assert "txn1" in log_ids
#     assert "txn2" in log_ids

# def test_get_affiliate_slaves(client, admin_login):
#     # Step 1: Log in as an admin to get authentication headers
#     auth_headers, admin = admin_login

#     # Step 2: Setup - Create test affiliates with master-slave relationships
#     master_affiliate = Affiliate(
#         client_name="MasterAffiliate",
#         display="Master Affiliate",
#         client_region="Region Master",
#         master=-1,
#         admin=admin.id,
#         notification=1
#     )
#     db.session.add(master_affiliate)
#     db.session.commit()

#     slave_affiliate1 = Affiliate(
#         client_name="SlaveAffiliate1",
#         display="Slave Affiliate 1",
#         client_region="Region Slave 1",
#         master=master_affiliate.id,
#         admin=admin.id,
#         notification=1
#     )

#     slave_affiliate2 = Affiliate(
#         client_name="SlaveAffiliate2",
#         display="Slave Affiliate 2",
#         client_region="Region Slave 2",
#         master=master_affiliate.id,
#         admin=admin.id,
#         notification=1
#     )

#     db.session.add_all([slave_affiliate1, slave_affiliate2])
#     db.session.commit()

#     # Link slaves to the master
#     master_affiliate.slave = {"slaves": [slave_affiliate1.id, slave_affiliate2.id]}
#     db.session.commit()

#     # Step 3: Test fetching slaves for the master affiliate
#     response = client.get(
#         '/api/affiliate/get_slaves',
#         json={"client_name": "MasterAffiliate"},
#         headers=auth_headers
#     )
#     res_data = response.get_json()

#     assert response.status_code == 200
#     assert res_data["success"] == 1
#     assert res_data["client_name"] == "MasterAffiliate"
#     assert len(res_data["slaves"]) == 2

#     slave_ids = [slave["id"] for slave in res_data["slaves"]]
#     assert slave_affiliate1.id in slave_ids
#     assert slave_affiliate2.id in slave_ids

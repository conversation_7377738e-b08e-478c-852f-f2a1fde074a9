<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
    <link rel="shortcut icon" href="{{ url_for("static", filename="assets/images/logo-265x265.png") }}" type="image/x-icon">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.7.2/animate.min.css">
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.4.1/css/bootstrap.min.css" integrity="sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh" crossorigin="anonymous">
	<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/mobiscroll-lite/mobiscroll.jquery.lite.min.css") }}">
    <link href="{{ url_for("static", filename="assets/css/country-code-picker.css") }}" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/utility.css") }}">
	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/custom-elements.css") }}">
	<link rel="stylesheet" type="text/css" href="{{ url_for("static", filename="assets/css/login.css") }}">
    
    <script src="{{ url_for("static", filename="assets/js/jquery-3.2.1.min.js") }}"/></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/mobiscroll-lite/mobiscroll.jquery.lite.min.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/constants.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/util-web.js") }}"></script>
    <script type="text/javascript" src="{{ url_for("static", filename="assets/js/countryCodeHelper.js") }}"></script>
	<script type="text/javascript" src="{{ url_for("static", filename="assets/js/login.js") }}"></script>
	<title>Get professional drivers for your car | Drivers4Me - Sign In/Register</title>
</head>

<body>
	<div id="Login_Card" class="card">
		<div class="card-header">
		</div>
    <div class="flex-container">
      <div id="wrapperphn" class="card-body">
    		<div id="phnBody" class="card-body">
          <div id="phoneStyle">

    			    <h3 class="login-header header-padding">
                <img class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Call.svg") }}">Phone
              </h3>
          </div>
    			<div id="Input_Grp-Phone" class="input-group large-top-padding wrapper-input-group">
                    <div id="ccSelect" class="input-group-prepend">
                        <span id="ccDisplay" class="input-group-text">
                            <div class="cc-picker-flag in"></div>
                            <span class="ccode">91</span>
                        </span>
                    </div>
                    <ul id="cc-picker" class="mbsc-cloak collapse"></ul>
    				<input class="form-control" id="Input_Phone" name="phone" type="number" placeholder="Enter Phone Number">
    			</div>

    			<div id="Input_Grp-BtnContinue" class="input-group large-top-padding wrapper-btn">
    				<button id="continueLogin" class="btn d4m-primary-btn-rounded">Next</button>
    			</div>
    		</div>
      </div>

      <div id="wrapperotp" class="card-body">
    		<div id="otpBody" class="card-body">
          <img id="otpBack" class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Back.svg") }}">
          <div id="otpStyle">
    			    <h3 class="login-header header-padding">
              <img class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/OTP.svg") }}">OTP
              </h3>
          </div>
    			<div id="Input_Grp-OTP" class="input-group large-top-padding wrapper-btn-otp">
    				<input class="form-control" id="Input_OTP" name="otp" type="number" placeholder="Enter the OTP">
    			</div>

          <div id="Input_Grp-ResendText" class="wrapper-btn-otp">
    				<h5 id="resendOTP"class="password-switch-text passwordLoginText">Resend OTP</h5>
    			</div>
    			<div id="Input_Grp-BtnConfirm" class="input-group large-top-padding wrapper-btn-otp">
    				<button id="confirmLogin" class="btn d4m-primary-btn-rounded">Login</button>
    			</div>
          <div id="Input_Grp-PasswordText" class="wrapper-btn-otp">
    				<h5 id="passwordSwitch"class="password-switch-text passwordLoginText">Login with password</h5>
    			</div>
    		</div>
      </div>

      <div id="wrapperpassword" class="card-body">
    		<div id="passwordBody" class="card-body">
          <img id="passwordBack" class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Back.svg") }}">
          <div id="phoneStyle">
    			    <h3 class="login-header header-padding">
                <img class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/key.svg") }}">Password
              </h3>
          </div>
    			<div id="Input_Grp-Password" class="input-group large-top-padding wrapper-input-group">
    				<input class="form-control" id="Input_Pass" name="phone" type="password" placeholder="Enter Your Password">
                    <div class="input-group-append input-visibility-toggle">
                        <span class="input-group-text">
                            <i class="fa fa-eye"></i>
                        </span>
                    </div>
    			</div>

    			<div id="Input_Grp-BtnLogin" class="input-group large-top-padding wrapper-btn">
    				<button id="confirmLoginPass" class="btn d4m-primary-btn-rounded">Login</button>
    			</div>
    		</div>
      </div>

      <div id="wrappername" class="card-body">
        <div id="nameBody" class="card-body">
          <img id="nameBack" class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Back.svg") }}">
          <div id="nameStyle">
              <h3 class="login-header header-padding">
                <img class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Name.svg") }}">Name
              </h3>
          </div>
          <div id="Input_Grp-Name" class="input-group large-top-padding wrapper-input-group">
            <input class="form-control" id="Input_First_Name" name="name" type="text" placeholder="First Name">
            <input class="form-control" id="Input_Last_Name" name="name" type="text" placeholder="Last Name">
          </div>
          <div id="Input_Grp-BtnLogin" class="input-group large-top-padding wrapper-btn">
            <button id="nextName" class="btn d4m-primary-btn-rounded">Next</button>
          </div>
        </div>
      </div>

      <div id="wrappersetpassword" class="card-body">
        <div id="setPasswordBody" class="card-body">
          <img id="setPasswordBack" class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Back.svg") }}">
          <div id="setPasswordStyle">
              <h3 class="login-header header-padding">
                <img class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/key.svg") }}">Set Password
              </h3>
          </div>
          <div id="Input_Grp-Set-Password" class="input-group large-top-padding wrapper-input-set-password-group">
            <input class="form-control" id="Input_Set_Password" name="name" type="password" placeholder="Enter Password">
            <div class="input-group-append input-visibility-toggle">
                <span class="input-group-text">
                    <i class="fa fa-eye"></i>
                </span>
            </div>
          </div>
          <div id="Input_Grp-Set-Password" class="input-group large-top-padding wrapper-input-set-password-group">
            <input class="form-control" id="Input_Set_Re_Password" name="name" type="password" placeholder="Re-Enter Password">
            <div class="input-group-append input-visibility-toggle">
                <span class="input-group-text">
                    <i class="fa fa-eye"></i>
                </span>
            </div>
          </div>
          <div id="Input_Grp-BtnLogin" class="input-group large-top-padding wrapper-btn">
            <button id="nextPassword" class="btn d4m-primary-btn-rounded">Next</button>
          </div>
        </div>
      </div>

      <div id="wrappersetotp" class="card-body">
    		<div id="setOtpBody" class="card-body">
          <img id="setOtpBack" class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/Back.svg") }}">
          <div id="setOtpStyle">
    			    <h3 class="login-header header-padding">
              <img class="input-group-addon-image icon-img" src="{{ url_for("static", filename="assets/images/elements/OTP.svg") }}">OTP
              </h3>
          </div>
    			<div id="Input_Grp-Set-OTP" class="input-group large-top-padding wrapper-btn-set-otp">
    				<input class="form-control" id="Input_Set_OTP" name="otp" type="number" placeholder="Enter the OTP">
    			</div>

          <div id="Input_Grp-ResendText" class="wrapper-btn-set-otp">
    				<h5 id="resendSetOTP"class="password-switch-text passwordLoginText">Resend OTP</h5>
    			</div>
    			<div id="Input_Grp-BtnConfirm" class="input-group large-top-padding wrapper-btn-set-otp">
    				<button id="confirmRegister" class="btn d4m-primary-btn-rounded">Login</button>
    			</div>
    		</div>
      </div>

      </div>
  	</div>

    <!-- SNACKBAR -->
    <div id="snackbar"></div>

    <!-- LOADER -->
    <div id="loader" class="collapse">
        <div class="backdrop"></div>
        <div id="spinner-container">
            <div class="spinner">
                <div class="bounce1"></div><div class="bounce2"></div><div class="bounce3"></div>
            </div>
        </div>
    </div>
</body>

</html>

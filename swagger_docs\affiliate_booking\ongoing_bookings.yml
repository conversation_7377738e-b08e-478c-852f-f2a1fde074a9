tags:
  - Affiliate_Booking
summary: Retrieve Ongoing Bookings for Affiliate
description: >
  This endpoint retrieves ongoing bookings for an affiliate based on the user's role and account status. 
  Only authorized roles can access this resource.
parameters:
  - name: user
    in: formData
    type: string
    required: false
    description: >
      User identity for which ongoing bookings are retrieved. Defaults to the authenticated user's ID if not provided.
responses:
  200:
    description: Ongoing bookings retrieved successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              booking_id:
                type: integer
                description: ID of the ongoing booking.
                example: 456
              booking_start_time:
                type: string
                format: date-time
                description: Start time of the booking (ISO 8601 format).
                example: "2024-12-01T10:00:00Z"
              customer_name:
                type: string
                description: Name of the customer.
                example: "<PERSON>"
              vehicle_model:
                type: string
                description: Model of the vehicle.
                example: "SUV"
              location:
                type: string
                description: Current location of the ongoing booking.
                example: "City Center Garage"
              status:
                type: string
                description: Status of the ongoing booking (e.g., "In Progress").
                example: "In Progress"
        message:
          type: string
          description: Success message.
          example: "Ongoing bookings retrieved successfully."
  400:
    description: Invalid input parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: Error message indicating the problem.
          example: "Invalid input parameter."
  401:
    description: Unauthorized access due to invalid JWT or insufficient permissions.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for unauthorized).
          example: -1
        message:
          type: string
          description: Error message indicating the issue.
          example: "Unauthorized"
  404:
    description: No ongoing bookings found.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for not found).
          example: -1
        message:
          type: string
          description: Error message indicating no results were found.
          example: "No results found"
  500:
    description: Internal server error due to database or unexpected issues.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for error).
          example: 0
        message:
          type: string
          description: General error message.
          example: "Database error"
        details:
          type: string
          description: Detailed error information for debugging.
          example: "IntegrityError: UNIQUE constraint failed."


from affiliate_b2b.affiliate_models import AffiliateCollections
from affiliate_b2b.affiliate_models import Affiliate, AffiliateWalletLogs, AffiliatePriceMapping, AffBookingLogs
from models import Bookings, Trip, Drivers, DriverDetails, DriverTrans, BookPricing, TripPricing,Users
from _utils import upload_pic, convert_timedelta, compute_driver_wallet
from db_config import db
from flask import jsonify
from price import Price, convert_to_semihours
import math
from booking_params import BookingParams, Rating
from sqlalchemy.exc import SQLAlchemyError
from live_update_booking import send_live_update_of_booking
from sqlalchemy.orm import aliased
from socketio_app import live_update_to_channel

KNOWN_FIELDS = {
    "aff_id", "search_ids", "loc", "dest_lat", "dest_long", "dest_loc", "trip_name", "trip_type", "remark", "vehicle_no",
    "appoint_ids", "vehicle_models", "src_nickname_id", "dest_nickname_id", "return_trip_name", "release", "priority"
}

SPOC_FIELDS = {
    "source_spoc_name", "source_spoc_contact" , "dest_spoc_name", "dest_spoc_contact"
}

SEARCH_FIELDS = {
    "trans_type", "city", "reflat", "reflong", "dur", "time", "dashboard"
}

def affiliate_account_enabled(affiliate_id):
    affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
    if affiliate:
        return affiliate.enabled
    else:
        return False

def get_affiliate_price_mapping(affiliate_id, city, trip_type):
    price_map = db.session.query(AffiliatePriceMapping).filter(
        AffiliatePriceMapping.aff_id == affiliate_id,
        AffiliatePriceMapping.mapped_region == city,
        AffiliatePriceMapping.mapped_trip_type == trip_type
    ).first()

    return price_map.mapped_price_affiliate, price_map.mapped_price_affiliate_name

def get_cancel_reason(code: int) -> str:
    cancel_reasons = {
        21: "Driver Not Assigned",
        22: "Driver Denied",
        23: "Taking Too Long to Allocate",
        24: "Booking Rescheduled",
        25: "B2B Cancel",
        26: "D4M Cancel",
        27: "Car Not Ready",
        28: "Customer Not Responding",
        29: "Car Not Available at Location",
        20: "Reverse cancellation",
        63: "No allocation",
        61: "Other"
    }
    return cancel_reasons.get(code, "Unknown")


def b2b_upload_pic(booking, request, trip_stage):
    affiliate_book_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': int(booking.id)})
    if not affiliate_book_mongo:
        print("Booking reference not found in affiliates_book.")
        return False
    
    trip_name = affiliate_book_mongo.get("trip_name")
    # affiliate_id = db.session.query(AffBookingLogs.aff_id).filter(AffBookingLogs.book_id == booking.id).scalar()
    # affiliate_data_mongo = affiliates_details.find_one({'affiliate_id': affiliate_id})
    
    # if not (affiliate_data_mongo and "trip_type" in affiliate_data_mongo):
    #     print("Affiliate data or trip type not found.")
    #     return False
    
    # matched_trip = next((trip for trip in affiliate_data_mongo["trip_type"] if trip["trip_type_name"] == trip_name), None)
    # if not matched_trip:
    #     print("No matching trip type found.")
    #     return False
    
    # if trip_stage == "start":
    #     images_to_upload = matched_trip.get("startImages", [])
    #     mongo_field = "trip_start_images"
    # elif trip_stage == "stop":
    #     images_to_upload = matched_trip.get("stopImages", [])
    #     mongo_field = "trip_stop_images"
    # else:
    #     print("Invalid trip_stage provided. It must be 'start' or 'stop'.")
    #     return False
    if trip_stage == "start":
        images_to_upload = affiliate_book_mongo.get('trip_start_images_structure', [])
        mongo_field = "trip_start_images"
    elif trip_stage == "stop":
        images_to_upload = affiliate_book_mongo.get('trip_stop_images_structure', [])
        mongo_field = "trip_stop_images"
    else:
        print("Invalid trip_stage provided. It must be 'start' or 'stop'.")
        return False
    
    uploaded_urls = {}
    for image in images_to_upload:
        image_title = image["imageTitle"].lower().replace(" ", "_")  # Normalize key
        image_type = image["imageType"]  # Mandatory/Optional

        if image_title in request.files:
            uploaded_urls[image_title] = upload_pic(request.files[image_title], path="book-" + str(booking.id))
        elif image_type == "Mandatory":
            print(f"Missing mandatory image: {image_title}")
    
    pic_data = {image_title: uploaded_urls.get(image_title, '') for image_title in uploaded_urls}
    try:
        AffiliateCollections.affiliates_book.update_one(
            {'book_ref': int(booking.id)},
             {'$set': {mongo_field: pic_data}}
        )
        return True
        
    except Exception as e:
        print(f"Error saving images: {e}")
        db.session.rollback()
        return False



def b2b_stop_trip(book, driver_user, time_stop, lat=-1, lng=-1, booking=None, trip=None):
    try:
        if booking is None:
            booking = db.session.query(Bookings).filter(Bookings.id == book).first()
        if not booking:
            return jsonify({'success': -1, 'message': 'Booking does not exist'}), 201

        if trip is None:
            cur_trip = db.session.query(Trip).filter(Trip.book_id == book).first()
        else:
            cur_trip = trip.first()
        
        if not cur_trip:
            return jsonify({'success': -1, 'message': 'Trip does not exist'}), 201
        
        affiliate_book_mongo = AffiliateCollections.affiliates_book.find_one({'book_ref': int(booking.id)})
        if not affiliate_book_mongo:
            return jsonify({'success': -1, 'message': 'Affiliate booking not found'}), 201
        affiliate_id = affiliate_book_mongo.get("affiliate_id")
        affiliate = db.session.query(Affiliate).filter(Affiliate.id == affiliate_id).first()
        if not affiliate:
            return jsonify({'success': -1, 'message': 'Affiliate does not exist'}), 201

        trip_type = BookingParams.get_b2b_trip_type(affiliate_book_mongo.get("trip_type"))

        result =  (
                    db.session.query(
                        AffBookingLogs,
                        mapped_by_alias := aliased(Affiliate),
                        mapped_wallet_alias := aliased(Affiliate)
                    )
                    .join(mapped_by_alias, mapped_by_alias.id == AffBookingLogs.mapped_by)
                    .outerjoin(mapped_wallet_alias, mapped_wallet_alias.id == AffBookingLogs.mapped_wallet)  # Use outer join
                    .filter(AffBookingLogs.book_id == book)
                    .first()
                )
        if result is None:
            raise ValueError(f"No booking logs found for book_id: {book}")
        book_logs, mapped_affiliate, mapped_affiliate_wallet = result
        if mapped_affiliate_wallet is None:
            mapped_affiliate_wallet = mapped_affiliate
        mapped_client = mapped_affiliate.client_name
        
        delta = time_stop - cur_trip.starttime
        d_hr, d_min, d_sec = convert_timedelta(delta)
        dur = f"{d_hr}:{d_min}:{d_sec}"
        time_delta = convert_to_semihours(delta, Price.get_hour_ratio())

        estimate_delta = (booking.days * 24 + booking.dur.hour) * Price.get_hour_ratio() + \
                         math.ceil((booking.dur.minute * Price.get_hour_ratio()) / 60)
        
        book_pricing = db.session.query(BookPricing).filter(BookPricing.book_id == booking.id).first()
        cust_est = book_pricing.base_ch
        driver_est = book_pricing.driver_base_ch
        price, pre_tax, cgst, sgst, total_bc, driver_fare, cust_night, cust_ot, driver_night, driver_ot = Price.get_trip_price(
            book_id=booking.id, book_delta=estimate_delta, real_delta=time_delta, est=cust_est,
            book_starttime=booking.starttime, book_stoptime=booking.endtime,
            trip_starttime=cur_trip.starttime.time(), trip_stoptime=time_stop.time(),
            startdate=cur_trip.starttime.date(), enddate=time_stop.date(), insurance_ch=booking.insurance_cost,
            city=booking.region, type=booking.type, client_name=mapped_client, client_trip_type=trip_type, 
            driver_est=driver_est)
        
        balance = mapped_affiliate_wallet.wallet - price
        wallet_logs = AffiliateWalletLogs(-price*100, method=f"Booking #{booking.code}", aff_id=affiliate_id,
                                              from_account=mapped_affiliate_wallet.id, wallet_before=mapped_affiliate_wallet.wallet, 
                                              wallet_after=balance, source=AffiliateWalletLogs.SOURCE_ADMIN)
        mapped_affiliate_wallet.wallet = balance
        db.session.add(wallet_logs)

        driver_details = db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver).first()
        if not driver_details:
            return jsonify({'success': -1, 'message': 'Driver details not found'}), 201
        driver_wallet, driver_withdrawable = compute_driver_wallet(driver_details, -driver_fare)
       
        driver_trans = DriverTrans(
            booking.driver, driver_fare * 100, wall_a=driver_wallet, wall_b=driver_details.wallet,
            with_a=driver_withdrawable, with_b=driver_details.withdrawable,
            method=f"Booking {booking.code}", status=DriverTrans.COMPLETED, stop=True
        )

        trip_pricing = TripPricing(booking.id, book_pricing.base_ch, cust_night, cust_ot, total_bc, book_pricing.insurance_ch, 
                        book_pricing.driver_base_ch, driver_night, driver_ot)

        db.session.bulk_save_objects([driver_trans, trip_pricing])

        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver).update({
            DriverDetails.ride_count: DriverDetails.ride_count + 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count + 1,
            DriverDetails.hour_count: DriverDetails.hour_count + int(d_hr),
            DriverDetails.earning: DriverDetails.earning + driver_fare,
            DriverDetails.rating: DriverDetails.rating + Rating.RATING_DEFAULT,
            DriverDetails.owed: DriverDetails.owed - driver_fare,
            DriverDetails.wallet: driver_wallet,
            DriverDetails.withdrawable: driver_withdrawable
        })
        due = -driver_fare
        Trip.query.filter(Trip.book_id == booking.id).update({
            Trip.endtime: time_stop,
            Trip.aff_trans: wallet_logs.id,
            Trip.due: due,
            Trip.price: pre_tax,
            Trip.stop_lat: lat,
            Trip.stop_lng: lng,
            Trip.cgst: cgst,
            Trip.sgst: sgst,
            Trip.price_pre_tax: pre_tax,
            Trip.driver_trans: driver_trans.id,
            Trip.net_rev: total_bc,
            Trip.status: Trip.TRIP_STOPPED
        })
        
        db.session.commit()
        send_live_update_of_booking( book, booking.region)
        driver_det = db.session.query(Drivers, Users).filter(Drivers.user == Users.id).filter(Drivers.user == driver_user).first()
        driver_data = {
                'driver_id': driver_det[0].id,
            }   
        live_update_to_channel(driver_data, room_name='drivers', type='drivers', region=driver_det[1].region, channel= 'update_driver')
        return jsonify({'success': 1, 'message': 'Trip stopped successfully'})

    except SQLAlchemyError as e:
        db.session.rollback()
        print(f"Database error: {str(e)}",flush=True)
        return jsonify({'success': -1, 'message': 'DB Error'}), 500
    except Exception as e:
        db.session.rollback()
        print(f"Unexpected error: {str(e)}",flush=True)
        return jsonify({'success': -1, 'message': 'An unexpected error occurred'}), 500

tags:
  - Customer_admin
summary: Get Customer Details
description: >
  This endpoint retrieves detailed information about a customer based on the provided mobile number. 
  The user must be authenticated and have the appropriate access rights.
parameters:
  - name: mobile
    in: formData
    required: true
    type: string
    description: Mobile number of the customer
    example: "9876543210"
responses:
  200:  # Success response when customer details are found
    description: Customer details fetched successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        user_id:
          type: integer
          description: ID of the user
          example: 123
        user_fname:
          type: string
          description: First name of the user
          example: "<PERSON>"
        user_lname:
          type: string
          description: Last name of the user
          example: "<PERSON><PERSON>"
        user_mobile:
          type: string
          description: Mobile number of the user
          example: "9876543210"
        user_email:
          type: string
          description: Email address of the user
          example: "<EMAIL>"
        user_label:
          type: string
          description: Label associated with the user
          example: "VIP"
        credit:
          type: number
          description: Credit amount of the user
          example: 100.50
        user_enabled:
          type: integer
          description: Flag indicating if the user is enabled (1 for enabled, 0 for disabled)
          example: 1
        user_mul_book_access:
          type: integer
          description: Flag indicating if the user has multiple booking access (1 for access, 0 for no access)
          example: 1
        customer_remark:
          type: string
          description: Remarks associated with the customer
          example: "Regular customer"
    examples:
      application/json:
        success: 1
        user_id: 123
        user_fname: "John"
        user_lname: "Doe"
        user_mobile: "9876543210"
        user_email: "<EMAIL>"
        user_label: "VIP"
        credit: 100.50
        user_enabled: 1
        user_mul_book_access: 1
        customer_remark: "Regular customer"
  201:  # Missing required fields
    description: Missing required fields in the request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
    examples:
      application/json:
        success: -1
  400:  # Bad request due to internal error or exception
    description: Bad request due to internal error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
    examples:
      application/json:
        success: -2
  404:  # Customer not found or role is driver
    description: Customer not found or role is driver
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
    examples:
      application/json:
        success: -1
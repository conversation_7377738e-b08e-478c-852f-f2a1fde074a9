#!/usr/bin/env python
# -*- coding: utf-8 -*-
#
#  affiliate.py
#
#  Copyright 2017-2019 Car Driver4Me Solution Pvt Ltd
#
#  ALL RIGHTS RESERVED.
#  Unauthorized copying of this file, via any medium is strictly prohibited.
#  Proprietary and confidential.
#  Author: <PERSON><PERSON><PERSON><PERSON>

from flask import jsonify
import math
from booking_params import Rating
import jsonpickle
from c24 import convert_timedelta, convert_mindelta
from booking_params import BookingParams
from _ops_message import send_slack_msg
import _sms
from _utils import  strfdelta, get_pic_url
from _utils_acc import account_enabled
import uuid
from price import convert_to_semihours, Price
from payments import PaymentType
from datetime import datetime, timedelta
from models import DriverSearch, RevvBookings, DriverDetails, BookPricing
from models import Bookings, Trip, Drivers, Users, UserTrans, BookPending
from models import db

HUB_LATLONG = [(22.5113339,88.415932,  "Revv Kolkata Hub"), (17.4537496,78.3861003, "Revv Hyderabad Hub"), (-1,-1, ""), (21.089666,79.096632, "Revv Nagpur Hub"),
                (-1,-1, "Revv Pune Hub"), (19.072961,72.867845, "Revv Mumbai Hub")]
HUB_CONTACT = [-1, -1, -1, -1, -1, -1]

class RevvPrice:
    base = [68, 75, 0, 75, 75, 75]

    @staticmethod
    def calc_revv_price(region, req_dur, act_dur):
        return RevvPrice.base[region] * max(req_dur, act_dur)


class BookingRevv:
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, lng, rep_name, rep_mob, shift,
                mobile="", driver_pic=None, loc='N/A'):
        self.success = 1
        self.id = id
        self.name = name
        self.startdate = str(startdate)
        self.starttime = str(starttime)
        self.endtime = str(endtime)
        self.enddate = str(enddate)
        self.dur = str(dur)
        if driver_pic:
            self.driver_pic = get_pic_url(driver_pic)
        self.loc = loc
        self.lat = lat
        self.long = lng
        self.mobile = mobile
        self.shift = shift


class BookingRevvPast(BookingRevv):
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, lng, rep_name, rep_mob, shift,
                mobile="", driver_pic=None, loc='N/A',
                sch_date=None, sch_time=None, ot="00:00:00"):
        super().__init__(id, name, startdate, starttime, enddate, endtime, dur, lat, lng, rep_name, rep_mob, shift,
                            mobile, driver_pic, loc)
        self.sch_startdate = sch_date
        self.sch_starttime = sch_time
        self.ot = ot


class BookingRevvOngoing(BookingRevv):
    def __init__(self, id, name, startdate, starttime, enddate, endtime, dur, lat, lng, rep_name, rep_mob, shift, did,
                mobile="", driver_pic=None, loc='N/A',
                sch_date=None, sch_time=None, ot="00:00:00"):
        super().__init__(id, name, startdate, starttime, enddate, endtime, dur, lat, lng, rep_name, rep_mob, shift,
                            mobile, driver_pic, loc)
        self.driver_id = did


def get_revv_hub(region):
    if region == Users.REGN_KOLKATA:
        return HUB_LATLONG[region]
    elif region == Users.REGN_HYDERABAD:
        return HUB_LATLONG[region]
    elif region == Users.REGN_NAGPUR:
        return HUB_LATLONG[region]
    elif region == Users.REGN_MUMBAI:
        return HUB_LATLONG[region]
    elif region == Users.REGN_PUNE:
        return HUB_LATLONG[region]


def get_revv_spoc(region):
    if region == Users.REGN_KOLKATA:
        contact = HUB_CONTACT[region]
        revv_users = db.session.query(Users).filter(Users.role == Users.ROLE_REVV).filter(Users.region == Users.REGN_KOLKATA)
        spoc = revv_users.filter(Users.mobile == contact).first()
        if not spoc:
            alt_spoc = revv_users.first()
            if not alt_spoc:
                return -1
            return alt_spoc.id
        return spoc.id
    elif region == Users.REGN_HYDERABAD:
        contact = HUB_CONTACT[region]
        revv_users = db.session.query(Users).filter(Users.role == Users.ROLE_REVV).filter(Users.region == Users.REGN_HYDERABAD)
        spoc = revv_users.filter(Users.mobile == contact).first()
        if not spoc:
            alt_spoc = revv_users.first()
            if not alt_spoc:
                return -1
            return alt_spoc.id
        return spoc.id
    elif region == Users.REGN_NAGPUR:
        contact = HUB_CONTACT[region]
        revv_users = db.session.query(Users).filter(Users.role == Users.ROLE_REVV).filter(Users.region == Users.REGN_NAGPUR)
        spoc = revv_users.filter(Users.mobile == contact).first()
        if not spoc:
            alt_spoc = revv_users.first()
            if not alt_spoc:
                return -1
            return alt_spoc.id
        return spoc.id
    elif region == Users.REGN_MUMBAI:
        contact = HUB_CONTACT[region]
        revv_users = db.session.query(Users).filter(Users.role == Users.ROLE_REVV).filter(Users.region == Users.REGN_MUMBAI)
        spoc = revv_users.filter(Users.mobile == contact).first()
        if not spoc:
            alt_spoc = revv_users.first()
            if not alt_spoc:
                return -1
            return alt_spoc.id
        return spoc.id
    elif region == Users.REGN_PUNE:
        contact = HUB_CONTACT[region]
        revv_users = db.session.query(Users).filter(Users.role == Users.ROLE_REVV).filter(Users.region == Users.REGN_PUNE)
        spoc = revv_users.filter(Users.mobile == contact).first()
        if not spoc:
            alt_spoc = revv_users.first()
            if not alt_spoc:
                return -1
            return alt_spoc.id
        return spoc.id
    return -1


def _revv_book(region, user, drivers, dur, shift, book_time, book_date):
    # Note - this will NOT work for other regions. Hub Kolkata user is hardcoded.
    now = datetime.utcnow()
    car_type = 0

    book_type = BookingParams.TYPE_REVV
    reflat, reflong, loc_name = get_revv_hub(int(region))
    spoc = get_revv_spoc(region)
    if spoc == -1:
        spoc = user
    dur_hr = int(dur.split(":")[0])
    estimate = RevvPrice.calc_revv_price(region, dur_hr, dur_hr)
    tot_success = 0
    for i in range(drivers):
        success = _revv_book_main(spoc, car_type, reflat, reflong, loc_name, book_time, book_date, dur, now, estimate, book_type, region, shift)
        tot_success += int(success)
        if not success:
            return jsonify({'success': -1, 'created': tot_success,
                            'failed': drivers - tot_success}), 200
    return jsonify({'success': 1, 'created': drivers, 'failed': 0})


def _revv_book_main(spoc, car_type, reflat, reflong, loc_name, book_time, book_date, dur, now, estimate, book_type, region, shift):
    search_id = uuid.uuid4().urn[9:]
    dur_t = datetime.strptime(dur, "%H:%M:%S")
    search_entry = DriverSearch(search_id, spoc, car_type, reflat, reflong, book_time, book_date, dur, now, book_type, 0)
    db.session.add(search_entry)
    fail = False
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        print(e)
    if fail:
        return False

    end_time = (datetime(search_entry.date.year, search_entry.date.month, search_entry.date.day,
                                  search_entry.time.hour, search_entry.time.minute, search_entry.time.second) +
                timedelta(search_entry.days, dur_t.hour * 3600 +
                                   dur_t.minute * 60 + dur_t.second))
    booking = Bookings(spoc, search_id, BookingParams.BOOKING_DUMMY_ID, search_entry.reflat, search_entry.reflong,
                                search_entry.time,
                                search_entry.date, str(search_entry.dur), end_time.time(), end_time.date(), estimate
                                , 0, loc_name,search_entry.car_type, search_entry.type, search_entry.days, payment_type=PaymentType.PAY_BILL,
                                region=region)

    db.session.add(booking)
    fail = False
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        print(e)
    if fail:
        return False

    pending = BookPending(booking.id, BookingParams.BOOKING_DUMMY_ID, BookPending.BROADCAST)
    db.session.add(pending)

    book_pricing = BookPricing(booking.id)
    db.session.add(book_pricing)
    revv_booking = RevvBookings(booking.id, region, shift)
    db.session.add(revv_booking)
    fail = False
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        print(e)
    if fail:
        return False
    return True


def _revv_start_trip(rv_book_id, driver_user, time_start):
    book = db.session.query(RevvBookings).filter(RevvBookings.id == rv_book_id). \
                        first().ref

    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver) \
        .filter(Bookings.valid > 0).first()
    if booking:
        booking_id = booking.id
    else:
        return jsonify({'success': -3}), 201
    already_trip = db.session.query(Trip).filter(Trip.book_id == booking_id).first()
    if already_trip:
        return jsonify({'success': -2}), 200  # trip exists
    new_trip = Trip(booking_id, time_start)
    db.session.add(new_trip)

    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    target_user = db.session.query(Users).filter(Users.id == booking.user).first()
    start_time_ist = time_start + _sms.IST_OFFSET_TIMEDELTA
    msg_content = "Revv trip, id #" + str(booking.id) + " was started by " + \
                  driver_name + " at approximately " + start_time_ist.strftime("%I:%M %p %d/%m/%Y")

    send_slack_msg(2, msg_content)

    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"

    if fail:
        return jsonify({'success': -4, 'reason': fail_reason})

    return jsonify({'success': 1})


def _revv_stop_trip(rv_book_id, driver_user, time_stop):

    book_val = db.session.query(RevvBookings).filter(RevvBookings.id == rv_book_id). \
                        first()
    book = book_val.ref
    region = book_val.region
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)

    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip:
        return jsonify({'success': -1}), 201
    if cur_trip.price != 0 or cur_trip.endtime:
        calc_dur = strfdelta(cur_trip.endtime - cur_trip.starttime, "{hours}:{minutes}:{seconds}")
        return jsonify({'success': 1, 'price': 0, 'dur': calc_dur})
    est = booking.estimate
    delta = time_stop - cur_trip.starttime
    # VERY IMPORTANT - note the 1 here. This rounds up the number of hours!
    time_delta = convert_to_semihours(delta, 1)
    estimate_delta = booking.dur.hour
    # Now calculate price
    price = est
    price = RevvPrice.calc_revv_price(region, estimate_delta, time_delta)

    if price > est:
        surcharge = True
    else:
        surcharge = False
    d_hr, d_min, d_sec = convert_timedelta(delta)
    dur = d_hr + ':' + d_min + ':' + d_sec
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        trans = UserTrans(booking.user, -price*100, "Revv trip", UserTrans.COMPLETED, 0, stop=True)
        db.session.add(trans)
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: time_stop,
                                        Trip.trans: trans.id, Trip.due: 0, Trip.price: price})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count + 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count + 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count + d_hr})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating + Rating.RATING_DEFAULT})

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1})

    try:
        stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
        msg_content = "Revv trip, id #" + str(booking.id) + " was stopped by " + \
                      driver_name + " at approximately " + \
                      stop_time_ist.strftime("%I:%M %p %d/%m/%Y")
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1, 'price': 0, 'dur': dur, 'surcharge': int(surcharge)})


def _revv_newstop_trip(rv_book_id, driver_user, time_stop):

    book_val = db.session.query(RevvBookings).filter(RevvBookings.id == rv_book_id). \
                        first()
    book = book_val.ref
    region = book_val.region
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)

    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip or not cur_trip.endtime:
        return jsonify({'success': -1}), 201
    est = booking.estimate
    delta = time_stop - cur_trip.starttime
    # VERY IMPORTANT - note the 1 here. This rounds up the number of hours!
    orig_delta = convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0]
    time_delta = convert_to_semihours(delta, 1)
    estimate_delta = booking.dur.hour
    # Now calculate price
    price = est
    price = RevvPrice.calc_revv_price(region, estimate_delta, time_delta)

    if price > est:
        surcharge = True
    else:
        surcharge = False
    d_hr, d_min, d_sec = convert_timedelta(delta)
    dur = d_hr + ':' + d_min + ':' + d_sec
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: -price*100})
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: time_stop, Trip.price: price})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta + d_hr})

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1, 'price': -1})

    try:
        stop_time_ist = time_stop + _sms.IST_OFFSET_TIMEDELTA
        msg_content = "Revv trip, id #" + str(booking.id) + " of " + \
                      driver_name + " was re-stopped at approximately " + \
                      stop_time_ist.strftime("%I:%M %p %d/%m/%Y")
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1, 'price': 0, 'dur': dur, 'surcharge': int(surcharge)})


def _revv_restart_trip(rv_book_id, driver_user):

    book_val = db.session.query(RevvBookings).filter(RevvBookings.id == rv_book_id). \
                        first()
    book = book_val.ref
    driver_res = db.session.query(Drivers).filter(Drivers.user == driver_user)

    if not account_enabled(driver_user):
        return jsonify({'success': -1}), 401
    driver = driver_res.first().id
    booking = db.session.query(Bookings).filter(Bookings.id == book).filter(Bookings.driver == driver).first()
    cur_trip = db.session.query(Trip).filter(Trip.book_id == booking.id).first()
    if not cur_trip or not cur_trip.endtime:
        return jsonify({'success': -1}), 201
    orig_delta = convert_timedelta(cur_trip.endtime - cur_trip.starttime)[0]
    driver_booked = db.session.query(Users, Drivers).filter(Drivers.id == booking.driver). \
        filter(Users.id == Drivers.user).first()
    try:
        driver_name = driver_booked[0].get_name()
    except TypeError:
        driver_name = ''
    try:
        db.session.query(UserTrans).filter(UserTrans.id == cur_trip.trans). \
                            update({UserTrans.amount: 0})
        Trip.query.filter(Trip.book_id == booking.id).update({Trip.endtime: None, Trip.price: 0})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.hour_count: DriverDetails.hour_count - orig_delta})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.ride_count: DriverDetails.ride_count - 1, DriverDetails.b2b_ride_count: DriverDetails.b2b_ride_count - 1})
        db.session.query(DriverDetails).filter(DriverDetails.driver_id == booking.driver). \
            update({DriverDetails.rating: DriverDetails.rating - Rating.RATING_DEFAULT})
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': -1})

    try:
        msg_content = "Revv trip, id #" + str(booking.id) + " of " + \
                      driver_name + " was restarted."
        send_slack_msg(2, msg_content)
    except Exception as e:
        pass
    # cur_trip.
    return jsonify({'success': 1})


def _revv_past_cust(user, to_fetch, cancelled_by):
    # to_fetch: 1 -> completed, 2 -> cancelled, 3 -> both

    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region:
        return jsonify({'success': -1, 'error': 0})

    # This is basically admin panel with the caveat that we fetch all Revv trips of a region
    if user_region.region == -1:
        user_region = [i for i in range(10)]
    else:
        user_region = [user_region.region]

    cur_dt = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

    results = db.session.query(Bookings, Drivers, Users, Trip, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
        filter(Bookings.valid >= 0). \
        filter(Bookings.type == BookingParams.TYPE_REVV). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.endtime < cur_dt). \
        filter(RevvBookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    if cancelled_by == -1:
        results_canceled = db.session.query(Bookings, Drivers, Users, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
            filter(Bookings.type == BookingParams.TYPE_REVV). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(RevvBookings.ref == Bookings.id). \
            filter(Bookings.valid < 0).order_by(Bookings.starttime.desc()).all()
    elif cancelled_by == 0:
        results_canceled = db.session.query(Bookings, Drivers, Users, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
            filter(Bookings.type == BookingParams.TYPE_REVV). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(RevvBookings.ref == Bookings.id). \
            filter(Bookings.valid == Bookings.CANCELLED_USER).order_by(Bookings.starttime.desc()).all()
    elif cancelled_by == 1:
        results_canceled = db.session.query(Bookings, Drivers, Users, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
            filter(Bookings.type == BookingParams.TYPE_REVV). \
            filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
            filter(RevvBookings.ref == Bookings.id). \
            filter(Bookings.valid == Bookings.CANCELLED_D4M).order_by(Bookings.starttime.desc()).all()


    result_json = []

    if to_fetch & 1:
        for result in results:
            d_hr, d_min, d_sec = convert_timedelta(result[3].endtime - result[3].starttime)
            days = float(d_hr) // 24
            d_hr_fl = float(d_hr) - days * 24
            dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
            delta = result[3].endtime - result[3].starttime
            # Bug - only works for 60min calcs
            time_delta = convert_to_semihours(delta, Price.HOUR_RATIO)
            estimate_delta = (result[0].days * 24 + result[0].dur.hour) * Price.HOUR_RATIO + \
                             math.ceil((result[0].dur.minute * Price.HOUR_RATIO) / 60)
            # OT in minutes (ideally we would have a fn for this and above but ehhh)
            overtime = time_delta - estimate_delta
            if overtime < 0:
                ot_str = "00:00:00"
            else:
                oth, otm, ots = convert_mindelta(overtime)
                ot_str = str(oth) + ':' + str(otm) + ':' + str(ots)
            try:
                driver_name = result[2].get_name()
            except TypeError:
                driver_name = ''
            revv_rep = db.session.query(Users).filter(Users.id == result[0].user).first()
            res_data = BookingRevvPast(id=result[4].id, name=driver_name,
                            startdate=result[3].starttime.strftime("%Y-%m-%d"),
                            starttime=result[3].starttime.strftime("%H:%M:%S"),
                            enddate=result[3].endtime.strftime("%Y-%m-%d"),
                            mobile=result[2].mobile,
                            endtime=result[3].endtime.strftime("%H:%M:%S"), dur=dur, driver_pic=result[1].pic,
                            loc=result[0].loc,
                            lat=result[0].lat, lng=result[0].long, rep_name=revv_rep.get_name(), rep_mob=revv_rep.mobile,
                            shift=result[4].shift,
                            sch_date=str(result[0].startdate), sch_time=str(result[0].starttime), ot=ot_str)
            result_json.append(jsonpickle.encode(res_data))

    if to_fetch & 2 == 2:
        for result_c in results_canceled:
            try:
                driver_name = result_c[2].get_name()
            except TypeError:
                driver_name = ''
            revv_rep = db.session.query(Users).filter(Users.id == result_c[0].user).first()
            if result_c[1].id != BookingParams.BOOKING_DUMMY_ID:
                res_data = BookingRevv(id=result_c[3].id, name=driver_name, startdate=result_c[0].startdate,
                        mobile=result_c[2].mobile,
                        starttime=result_c[0].starttime, enddate=result_c[0].enddate, endtime=result_c[0].endtime, dur=result_c[0].dur,
                        lat=result_c[0].lat, lng=result_c[0].long, rep_name=revv_rep.get_name(), rep_mob=revv_rep.mobile,
                        shift=result_c[3].shift,
                        driver_pic=result_c[1].pic,
                        loc=result_c[0].loc)
            else:
                res_data = BookingRevv(id=result_c[3].id, name="Not Allocated", startdate=result_c[0].startdate,
                        starttime=result_c[0].starttime, enddate=result_c[0].enddate, endtime=result_c[0].endtime, dur=result_c[0].dur,
                        lat=result_c[0].lat, lng=result_c[0].long, rep_name=revv_rep.get_name(), rep_mob=revv_rep.mobile,
                        shift=result_c[3].shift,
                        driver_pic=None, loc=result_c[0].loc)
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def _revv_ongoing_cust(user):
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region:
        return jsonify({'success': -1, 'error': 0})

    # This is basically admin panel with the caveat that we fetch all C24 trips of a region
    if user_region.region == -1:
        user_region = [i for i in range(10)]
    else:
        user_region = [user_region.region]
    results = db.session.query(Bookings, Drivers, Users, Trip, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_REVV). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Trip.book_id == Bookings.id). \
        filter(Trip.endtime == None). \
        filter(RevvBookings.ref == Bookings.id). \
        order_by(Trip.starttime.desc()).all()

    result_json = []
    res_data = []
    for result in results:
        d_hr, d_min, d_sec = convert_timedelta(datetime.utcnow() - result[3].starttime)
        days = float(d_hr) // 24
        d_hr_fl = float(d_hr) - days * 24
        dur = str(int(d_hr_fl)) + ':' + d_min + ':' + d_sec
        try:
            driver_name = result[2].get_name()
        except TypeError:
            driver_name = ''
        revv_rep = db.session.query(Users).filter(Users.id == result[0].user).first()
        res_data = BookingRevvOngoing(id=result[4].id, name=driver_name, startdate=result[3].starttime.strftime("%Y-%m-%d"),
                starttime=result[3].starttime.strftime("%H:%M:%S"), enddate="",
                endtime="", dur=dur,
                lat=result[0].lat, lng=result[0].long, rep_name=revv_rep.get_name(), rep_mob=revv_rep.mobile,
                driver_pic=result[1].pic,
                shift=result[4].shift,
                loc=result[0].loc, mobile=result[2].mobile,
                did=result[1].id)
        result_json.append(jsonpickle.encode(res_data))
    print(len(result_json))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})


def _revv_upcoming_cust(to_fetch, user):
    # to_fetch: 1 -> allocated, 2 -> new, 3 -> both
    user_region = db.session.query(Users).filter(Users.id == user).first()
    if not user_region or not to_fetch:
        return jsonify({'success': -1, 'error': 0})

    # This is basically admin panel with the caveat that we fetch all C24 trips of a region
    if user_region.region == -1:
        user_region = [i for i in range(10)]
    else:
        user_region = [user_region.region]
    trip_book = db.session.query(Trip.book_id)
    results = db.session.query(Bookings, Drivers, Users, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_REVV). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 1). \
        filter(Bookings.driver > BookingParams.BOOKING_DUMMY_ID). \
        filter(~Bookings.id.in_(trip_book)). \
        filter(RevvBookings.ref == Bookings.id). \
        order_by(Bookings.startdate.desc(),Bookings.starttime.desc()).all()

    results_uc = db.session.query(Bookings, Drivers, Users, RevvBookings).filter(RevvBookings.region.in_(user_region)). \
        filter(Bookings.type == BookingParams.TYPE_REVV). \
        filter(Bookings.driver == Drivers.id).filter(Drivers.user == Users.id). \
        filter(Bookings.valid == 0). \
        filter(Bookings.driver == BookingParams.BOOKING_DUMMY_ID). \
        filter(RevvBookings.ref == Bookings.id). \
        order_by(Bookings.startdate.desc(),Bookings.starttime.desc()).all()


    result_json = []
    res_data = []

    if to_fetch & 1:
        for result in results:
            try:
                driver_name = result[2].get_name()
            except TypeError:
                driver_name = ''
            revv_rep = db.session.query(Users).filter(Users.id == result[0].user).first()
            res_data = BookingRevv(id=result[3].id, name=driver_name, startdate=result[0].startdate,
                    starttime=result[0].starttime, enddate=result[0].enddate, endtime=result[0].endtime, dur=result[0].dur,
                    lat=result[0].lat, lng=result[0].long, rep_name=revv_rep.get_name(), rep_mob=revv_rep.mobile,
                    shift=result[3].shift,
                    driver_pic=result[1].pic, loc=result[0].loc, mobile=result[2].mobile)
            result_json.append(jsonpickle.encode(res_data))

    if to_fetch & 2 == 2:
        for result_uc in results_uc:
            try:
                driver_name = result_uc[2].get_name()
            except TypeError:
                driver_name = ''
            revv_rep = db.session.query(Users).filter(Users.id == result_uc[0].user).first()
            res_data = BookingRevv(id=result_uc[3].id, name="Not Allocated", startdate=result_uc[0].startdate,
                        starttime=result_uc[0].starttime, enddate=result_uc[0].enddate, endtime=result_uc[0].endtime, dur=result_uc[0].dur,
                        lat=result_uc[0].lat, lng=result_uc[0].long, rep_name=revv_rep.get_name(), rep_mob=revv_rep.mobile,
                        driver_pic=None,
                        shift=result_uc[3].shift,
                        loc=result_uc[0].loc)
            result_json.append(jsonpickle.encode(res_data))
    if len(result_json) <= 0:
        return jsonify({'success': - 1})
    return jsonify({'success': 1, 'data': result_json})



def _revv_cancel(revv_id, user):
    booking_id = db.session.query(RevvBookings).filter(RevvBookings.id == revv_id). \
                        first().ref
    db.session.query(Bookings).filter(Bookings.id == booking_id). \
                update({Bookings.valid: Bookings.CANCELLED_USER})
    fail = False
    fail_reason = ""
    try:
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        fail = True
        fail_reason = "DB Error"
        print(e)
    if fail:
        return jsonify({'success': -1, 'reason': fail_reason})
    return jsonify({'success': 1})
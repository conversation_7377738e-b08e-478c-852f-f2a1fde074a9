tags:
  - Driver_admin
summary: Update and Log Driver Information
description: >
  This endpoint allows for updating various details related to a driver, including personal information, driver documents, and bank details. It logs the changes made during the update process for auditing purposes.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose information is to be updated
    example: 101
  - name: driver_description
    in: formData
    required: false
    type: string
    description: Description related to the driver
    example: "Experienced driver with a clean record"
  - name: approval
    in: formData
    required: false
    type: string
    description: Approval status of the driver update
    example: "Approved"
  - name: editedby
    in: formData
    required: true
    type: string
    description: The ID or name of the admin who made the edits
    example: "admin123"
  - name: remark
    in: formData
    required: true
    type: string
    description: Remarks or comments related to the update process
    example: "Updated driver details as per new regulations"
  - name: pic
    in: formData
    required: false
    type: file
    description: Profile image of the driver
  - name: licDocFront
    in: formData
    required: false
    type: file
    description: Front image of the driver's license document
  - name: licDocBack
    in: formData
    required: false
    type: file
    description: Back image of the driver's license document
  - name: idDocFront
    in: formData
    required: false
    type: file
    description: Front image of the driver's ID document
  - name: idDocBack
    in: formData
    required: false
    type: file
    description: Back image of the driver's ID document
  - name: accDoc
    in: formData
    required: false
    type: file
    description: Bank account document of the driver
responses:
  200:
    description: Successfully updated the driver's information
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Message describing the outcome of the update
          example: "Driver information updated successfully"
  400:
    description: Bad request (driver ID is required or other validation errors)
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for validation failure)
          example: -1
        error:
          type: string
          description: Error message
          example: "driver_id parameter is required"
  404:
    description: Driver not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1 for driver not found)
          example: -1
        error:
          type: string
          description: Error message
          example: "Driver not found"
  500:
    description: Internal server error or exception during the request
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2 for database errors, -3 for unexpected errors)
          example: -2
        message:
          type: string
          description: Error message
          example: "Database commit failed."
        error:
          type: string
          description: Detailed error message
          example: "Unexpected error occurred"

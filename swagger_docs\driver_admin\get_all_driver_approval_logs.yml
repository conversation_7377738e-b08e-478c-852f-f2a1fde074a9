tags:
  - Driver_admin
summary: Fetch Driver Approval Logs
description: >
  This endpoint retrieves approval logs for a specific driver, allowing filtering by date range, search query, approval status, and sorting options. It also supports pagination.
parameters:
  - name: driver_id
    in: formData
    required: true
    type: integer
    description: The ID of the driver whose approval logs are to be fetched.
    example: 12345
  - name: sortby
    in: formData
    type: string
    description: The field to sort by (default is timestamp).
    example: "timestamp"
  - name: sorttype
    in: formData
    type: string
    description: Sorting order (0 for descending, 1 for ascending).
    example: "0"
  - name: fromdate
    in: formData
    type: string
    format: date
    description: Filter logs starting from this date (YYYY-MM-DD).
    example: "2023-01-01"
  - name: todate
    in: formData
    type: string
    format: date
    description: Filter logs up to this date (YYYY-MM-DD).
    example: "2023-12-31"
  - name: search_query
    in: formData
    type: string
    description: A search term to filter logs by changes, change_from, change_to, or remarks.
    example: "approved"
  - name: approval
    in: formData
    type: string
    description: A comma-separated list of approval statuses to filter by (e.g., 1 for approved, 0 for pending).
    example: "1,0"
  - name: editedby
    in: formData
    type: string
    description: Filter logs by the editor's name.
    example: "admin"
  - name: change_from
    in: formData
    type: string
    description: Filter logs by the original value before the change.
    example: "pending"
  - name: change_to
    in: formData
    type: string
    description: Filter logs by the new value after the change.
    example: "approved"
  - name: offset
    in: formData
    type: integer
    description: The starting index from which the logs are fetched.
    example: 0
  - name: limit
    in: formData
    type: integer
    description: The number of logs to retrieve.
    example: 10
responses:
  200:
    description: Successfully retrieved driver approval logs.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates the success of the operation.
          example: 1
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                description: The log entry ID.
                example: 4567
              driver:
                type: integer
                description: The ID of the driver associated with the log.
                example: 12345
              changes:
                type: string
                description: The description of changes made in the log entry.
                example: "Approval status updated"
              approval:
                type: integer
                description: The approval status (e.g., 1 for approved, 0 for pending).
                example: 1
              editedby:
                type: string
                description: The name of the person who edited the log.
                example: "admin"
              change_from:
                type: string
                description: The original value before the change.
                example: "pending"
              change_to:
                type: string
                description: The new value after the change.
                example: "approved"
              remark:
                type: string
                description: Any additional remarks related to the change.
                example: "Driver approved successfully"
              timestamp:
                type: string
                format: datetime
                description: The timestamp of when the log entry was created.
                example: "2023-10-10 12:30:00"
  400:
    description: Invalid input parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Details about the error.
          example: "Driver ID is required"
  500:
    description: Server error during processing.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Indicates failure of the operation.
          example: -1
        error:
          type: string
          description: Details about the error.
          example: "An error occurred while fetching approval logs"

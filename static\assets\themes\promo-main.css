.Event-View-Container {
    z-index: 3;
    position: fixed;
    top: 20vh;
    right: 3vw;
}

#Event-View {
    background-color: white;
    height: 11vh;
    width: 11vh;
    cursor: pointer;
    border-radius: 50%;
    box-shadow: 0 0 8px 4px #bdffbd;
    transition: transform 0.5s border 0.5s box-shadow: 0.5s;
    -ms-transition: -ms-transform 0.5s;
    -webkit-transition: -webkit-transform 0.5s;
    -moz-transition: -moz-transform 0.5s;
    -o-transition: -o-transform 0.5s;
}

#Event-View:hover {
    transform: rotate(20deg) scale(1.2);
    -ms-transform: rotate(20deg) scale(1.2);
    -webkit-transform: rotate(20deg) scale(1.2);
    -moz-transform: rotate(20deg) scale(1.2);
    -o-transform: rotate(20deg) scale(1.2);
    box-shadow: 0px 0px 8px 4px #ffdc9c;
}

#Promo-Modal .theme-header-background {
    background-image: url('/static/assets/images/Themes/2020-trailer-notif.jpg');
}

#Promo-Modal .modal-content {
    border-radius: 20px;
}

#Promo-Modal .modal-header {
    padding: 0;
    border: none;
}

#Promo-Modal .theme-header-background {
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    height: 30vh;
}

#Promo-Modal .modal-title {
    font-size: 40px;
    color: lightcyan;
    text-shadow: 0 0 5px black;
    text-align: center;
    position: absolute;
    width: 100%;
    text-align: center;
    top: 22vh;
}

.no-padding {
    padding: 0;
}

.no-margin {
    margin: 0;
}

#Promo-Modal .event-panel {
    max-height: 50vh;
    overflow-y: scroll;
}

#InfoPanel .panel {
    border: none;
}

#Offers_List {
    list-style-type: none;
}

.offer h3 {
    letter-spacing: 1px;
    margin-bottom: 5px;
    margin-top: 8px;
}

.offer.offer-inactive {
    color: rgba(127, 127, 127, 0.5);
    font-style: italic;
}

.offer.offer-inactive h3 {
    text-shadow: none!important;
}
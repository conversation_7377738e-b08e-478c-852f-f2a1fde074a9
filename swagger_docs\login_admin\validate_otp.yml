tags:
  - Login_admin
summary: Admin OTP Validation
description: >
  This endpoint allows admins to validate a One-Time Password (OTP) for login. Upon successful OTP validation, the response includes JWT tokens and user details.
parameters:
  - in: formData
    name: mobile
    type: string
    required: true
    description: <PERSON><PERSON>'s mobile number used for OTP validation.
    example: "9876543210"
  - in: formData
    name: otp
    type: string
    required: true
    description: One-Time Password (OTP) sent to the admin's mobile.
    example: "123456"
responses:
  200:
    description: <PERSON>TP successfully validated. Returns access and refresh tokens along with user details.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        user_fname:
          type: string
          description: <PERSON><PERSON>'s first name.
          example: "<PERSON>"
        user_mobile:
          type: string
          description: <PERSON><PERSON>'s mobile number.
          example: "9876543210"
        user_email:
          type: string
          description: <PERSON><PERSON>'s email address.
          example: "<EMAIL>"
        user_lname:
          type: string
          description: <PERSON><PERSON>'s last name.
          example: "Doe"
        user_region:
          type: string
          description: Ad<PERSON>'s region.
          example: "Region 1"
        tabs:
          type: string
          description: List of accessible tabs for the admin.
          example: "0,1,2,3,4"
        regions:
          type: string
          description: List of accessible regions for the admin.
          example: "0,1,2,3"
        notification:
          type: string
          description: List of accessible notifications for the admin.
          example: "0,1,2"
        id:
          type: integer
          description: Admin's unique ID.
          example: 1001
        code:
          type: string
          description: Refresh token for the admin.
          example: "eyJhbGciOiJIUzI1NiIsInR..."
        role:
          type: integer
          description: Admin's role in the system.
          example: 1
  400:
    description: Bad request due to missing or invalid parameters.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for failure).
          example: 0
  401:
    description: Unauthorized access or invalid OTP.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for failure).
          example: 0
  403:
    description: Forbidden access for users with invalid roles or no admin access.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0 for failure).
          example: 0
        error:
          type: string
          description: Error message.
          example: "Forbidden access for this role"
  500:
    description: Internal server error.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-5 for failure).
          example: -5
        error:
          type: string
          description: Error message detailing the exception that occurred.
          example: "Internal server error"

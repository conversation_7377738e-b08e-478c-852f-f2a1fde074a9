tags:
  - User
summary: Delete User Account
description: >
  This endpoint allows a user to delete their account. It updates the user's mobile number and logs the deletion in the database.
parameters:
  - name: reason
    in: formData
    type: integer
    required: true
    description: The reason for account deletion
responses:
  201:  # Success response when user account is deleted successfully
    description: User deleted successfully
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        message:
          type: string
          description: Success message
          example: "User deleted successfully"
    examples:
      application/json:
        success: 1
        message: "User deleted successfully"
  401:  # Unauthorized - User restricted
    description: Unauthorized - User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -1
        message: "User restricted"
  400:  # Bad request - Incomplete form details
    description: Incomplete form details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "Incomplete form details"
    examples:
      application/json:
        success: -2
        message: "Incomplete form details"
  403_a:  # Forbidden - User restricted
    description: User restricted
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "User restricted"
    examples:
      application/json:
        success: -2
        message: "User restricted"
  403_b:  # Forbidden - Reached max limit for account deletion
    description: Max limit for account deletion reached
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "Reached max limit for account deletion"
    examples:
      application/json:
        success: -2
        message: "Reached max limit for account deletion"
  404:  # User not found
    description: User not found
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-2)
          example: -2
        message:
          type: string
          description: Error message
          example: "User not found"
    examples:
      application/json:
        success: -2
        message: "User not found"
  500:  # Internal server error
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        message:
          type: string
          description: Error message
          example: "Server Error"
    examples:
      application/json:
        success: -1
        message: "Server Error"

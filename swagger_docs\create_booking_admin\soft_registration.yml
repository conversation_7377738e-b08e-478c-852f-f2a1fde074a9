tags:
  - Create_booking_admin
summary: Register a new customer (soft registration)
description: >
  This endpoint allows an admin to register a new customer with a mobile number and optionally an email address. The admin must have a valid JWT token and appropriate role (Admin or Super Admin). If the customer already exists, the API will return an error.
parameters:
  - name: mobile
    in: formData
    type: string
    required: true
    description: The mobile number of the customer to be registered (without "+91" prefix).
    example: "8650755357"
  - name: email
    in: formData
    type: string
    required: false
    description: The email address of the customer (optional).
    example: "<EMAIL>"
responses:
  200:
    description: Successfully registered the customer.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 0
        new_user_id:
          type: integer
          description: ID of the newly created user.
          example: 123
        msg:
          type: string
          example: "User created successfully"
  400:
    description: Bad request due to incomplete or invalid data.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 2
        msg:
          type: string
          example: "Incomplete mobile number"
  401:
    description: Unauthorized due to missing or invalid JWT token.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Invalid or missing token"
  403:
    description: Unauthorized due to lack of admin permissions.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: -1
        msg:
          type: string
          example: "Unauthorized"
  409:
    description: Conflict when the user already exists.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 3
        msg:
          type: string
          example: "User already exists!"
  500:
    description: Server error due to integrity constraints or other issues.
    schema:
      type: object
      properties:
        success:
          type: integer
          example: 5
        msg:
          type: string
          example: "Integrity constraint error"

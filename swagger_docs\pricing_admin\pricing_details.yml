tags:
  - Pricing_admin
summary: Fetch Pricing Details for a Specific City and Trip Type
description: >
  This endpoint allows admins to fetch detailed pricing information for a specific city and trip type. 
  It requires authorization and the inclusion of city and trip type parameters. Optionally, an occasion can be specified to fetch specific occasion data.
parameters:
  - name: city
    in: formData
    required: true
    type: string
    description: The name of the city for which pricing details are being fetched.
    example: "Los Angeles"
  - name: trip_type
    in: formData
    required: true
    type: string
    description: The type of trip (inCity or outStation).
    example: "inCity"
  - name: occasion
    in: formData
    required: false
    type: string
    description: The name of the occasion for which specific pricing details are being requested.
    example: "New Year"
responses:
  200:
    description: Successfully fetched pricing details
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success)
          example: 1
        result:
          type: object
          description: Pricing details for the specified city and trip type
          example: 
            fare: 150.00
            night_charge: 50.00
            part_night_charge: 25.00
  400:
    description: Request failed due to incomplete data
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Incomplete data"
  401:
    description: Unauthorized access
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Unauthorized"
  404:
    description: Data not found for the specified city, trip type, or occasion
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (-1)
          example: -1
        msg:
          type: string
          description: Error message
          example: "Trip type inCity not found for city Los Angeles"
  500:
    description: Internal server error
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Failure flag (0)
          example: 0
        msg:
          type: string
          description: Error message
          example: "Internal server error"

[general]
filename = jyote.csv
query = SELECT mh_book_id as appointment_id, um_uname, mh_appt_id as apptId,mh_veh_reg as carNo, CASE when book_region = 0 THEN "Kolkata" when book_region = 6 THEN "Delhi" when book_region=19 THEN "Alampur" ELSE "Kolkata" END, CASE mh_trip_type when 0 THEN "Home Delivery" ELSE "Pickup" END, date(addtime(trip_start,"05:30:00")) as date, time(addtime(trip_start,"05:30:00")) as start_time, time(addtime(trip_stop,"05:30:00")) as stop_time, timediff(trip_stop,trip_start) as dur, greatest("00:00:00", timediff(timediff(trip_stop,trip_start),"02:00:00")) as ot, concat(user_fname, concat(' ', user_lname)) as driver, book_loc_name as start_loc, dest_book_name as stop_loc, mh_dist, CASE when mh_dist>25 THEN mh_dist-25 ELSE 0 END, book_comment as d4m_comment from bookings, trip, mahindra_bookings, drivers, users, username_map, book_dest where book_user=um_uid and book_ref=mh_book_ref and book_ref=trip_book and book_driver=driver_id and driver_user=user_id and dest_book_id=book_ref and month(date(addtime(trip_start,"05:30:00")))=MONTH(CURRENT_DATE - INTERVAL 1 MONTH) and year(date(addtime(trip_start,"05:30:00")))=YEAR(CURRENT_DATE - INTERVAL 1 MONTH);
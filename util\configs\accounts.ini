[general]
filename = accounts.csv
query = select driver_id, concat(user_fname, concat(' ', user_lname)) name,user_mobile mobile,driver_license_no license,perm_driver_base base,perm_driver_ta TA,perm_driver_hours Hours,user_region Region,(select sum(trip_due) from trip,bookings where trip_book=book_ref and month(trip_start)=3 and year(trip_start)=2020 and book_driver=driver_id) Due,(select count(*) from trip,bookings where trip_book=book_ref and month(trip_start)=3 and year(trip_start)=2020 and book_driver=driver_id and book_type in (1,2,3,4,50)) TATrips,(select count(*) from trip,bookings where trip_book=book_ref and month(trip_start)=3 and year(trip_start)=2020 and book_driver=driver_id) Trips,(select count(distinct day(trip_start)) from trip,bookings where trip_book=book_ref and month(trip_start)=3 and year(trip_start)=2020 and book_driver=driver_id) Active from users,driver_perma_info,drivers where driver_perma=1 and perm_driver_id=driver_id and driver_user=user_id and driver_approved=1 order by Region,driver_id;
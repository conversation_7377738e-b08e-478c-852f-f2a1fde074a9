tags:
  - Calling
summary: Initiate Call Request
description: >
  This endpoint initiates a masked call request between a driver and user based on the booking code.
parameters:
  - name: bookCode
    in: formData
    type: string
    required: true
    description: Unique booking code for which the call request is made.
responses:
  200:  # Success - Call request log added successfully
    description: Call request log added successfully.
    schema:
      type: object
      properties:
        success:
          type: integer
          description: Success flag (1 for success).
          example: 1
        message:
          type: string
          description: Success message.
          example: "Successfully added call request log."
    examples:
      application/json:
        success: 1
        message: "Successfully added call request log."

  500:  # Error - Database Error
    description: Database Error
    schema:
      type: object
      properties:
        result:
          type: integer
          description: Failure flag (-1 for DB error).
          example: -1
        message:
          type: string
          description: Error message.
          example: "DB Error"
    examples:
      application/json:
        result: -1
        message: "DB Error"

$(document).ready(function() {

    window.OTPMins = 2;
    window.OTPCountdown = 0;
    window.StartTick = 0;
    window.EndTick = 0;
    window.EventStartDate = new Date(2019, 11, 25, 0, 0, 0, 0);
    window.EventEndDate = new Date(2020, 0, 5, 0, 0, 0, 0);

    $("body").append
    (
       '<div class="Event-View-Container">'+
            '<img id="Event-View" class="homepage-chirstmas-reward-element"' +
            'src="static/assets/images/Themes/Christmas-Rewards-Icon.svg"/>'+
            '<i><h3 id="Event-Tick"></h3></i>'+
        '</div>'

    );

    // Initialize Countdown ticker
    var EventStartCountDownDMHS = CountDownDMHS(new Date(), window.EventEndDate);

    $("#Event-Tick").html(EventStartCountDownDMHS["days"] + "D:" +
                            EventStartCountDownDMHS["hours"] + "H:" +
                            EventStartCountDownDMHS["minutes"] + "M");

    // If the count down is over, write some text
    if (EventStartCountDownDMHS["days"] <= 0 && EventStartCountDownDMHS["hours"] <= 0 &&
        EventStartCountDownDMHS["minutes"] <= 0 && EventStartCountDownDMHS["seconds"] <= 0) {
        clearInterval(window.StartTick);
        $("#Event-Tick").html("");
    }

    EventStartCountdownTicker();

   $("body").delegate("#Event-View", "click", function() {
      $("#ThemeAddons-ChristmasRewardsModal").modal("show");
   });

   $("body").delegate(".event-activity-tab", "click", function() {
      if(!$(this).hasClass("event-tab-active")) {
          $(".event-activity-tab").removeClass("event-tab-active");
          $(this).addClass("event-tab-active");
          $(".event-panel").addClass("collapse");
          $("#" + $(this).attr("id").replace("Tab", "Panel")).removeClass("collapse");
      }
   });

   $("body").delegate("#Christmas-Rewards-Auth-Toggle", "click", function() {
       if($(this).find(".authMode").html() === "OTP") {
           $("#Christmas-Rewards-Auth-Toggle").find(".authMode").html("Password");
           $(".Christmas-Rewards-Input-Label").find(".modeOTP").removeClass("collapse");
           $(".Christmas-Rewards-Input-Label").find(".modePass").addClass("collapse");
           $("#Chirstmas-Rewards-Input-Password").addClass("collapse");
           $("#Chirstmas-Rewards-Input-OTP").removeClass("collapse");
           $(".Chirstmas-Rewards-OTP-Chamber").removeClass("collapse");
       }

       else if($(this).find(".authMode").html() === "Password") {
           $("#Christmas-Rewards-Auth-Toggle").find(".authMode").html("OTP");
           $(".Christmas-Rewards-Input-Label").find(".modeOTP").addClass("collapse");
           $(".Christmas-Rewards-Input-Label").find(".modePass").removeClass("collapse");
           $("#Chirstmas-Rewards-Input-Password").removeClass("collapse");
           $("#Chirstmas-Rewards-Input-OTP").addClass("collapse");
           $(".Chirstmas-Rewards-OTP-Chamber").addClass("collapse");
       }

   });

   $("body").delegate("#Chirstmas-Rewards-Button-OTPGen", "click", function() {
        $(this).attr('disabled', true);
        OTPTimer();

    });

    $("body").delegate("#Chirstmas-Rewards-Button-Logoff", "click", function() {
        $("#RewardsPanel").addClass("collapse");
        $("#LoginPanel").removeClass("collapse");
    });

    $("body").delegate("#Chirstmas-Rewards-Button-Submit", "click", function() {
        var mobile = $("#Chirstmas-Rewards-Input-Mobile").val().trim();
    	var password = $("#Chirstmas-Rewards-Input-Password").val();
    	var passcode = $("#Chirstmas-Rewards-Input-OTP").val();
    	//current authentication method is the exact opposite of the toggle value
    	var authenticationMethod = $("#Christmas-Rewards-Auth-Toggle").find(".authMode").html().trim() == "OTP" ? "Password" : "OTP";
    	var phoneRegex = new RegExp("^[6-9][0-9]{9,9}$");			//regex for phone validation
    	var OTPRegex = new RegExp("^[0-9]{4,4}$");			//regex for OTP validation
    	var validData = true;

    	//phone validation
    	if (!mobile || mobile === "") {
          	$("#Chirstmas-Rewards-Input-Mobile").css('border','2px solid red');			//error indication
          	//error popover
          	$("#Chirstmas-Rewards-Input-Mobile").popover({
            	placement: "bottom",
            	trigger: "hover"
          	});
          	$('#Chirstmas-Rewards-Input-Mobile').data('bs.popover').options.content = "This field cannot be blank";
          	validData = false;
      	}

      	else if (!phoneRegex.test(mobile)) {
            $("#Chirstmas-Rewards-Input-Mobile").css('border','2px solid red');			//error indication
            //error popover
            $("#Chirstmas-Rewards-Input-Mobile").popover({
            	placement: "bottom",
            	trigger: "hover"
          	});
          	$('#Chirstmas-Rewards-Input-Mobile').data('bs.popover').options.content = "Enter correct mobile number without country-code";
          	validData = false;
        }

        else {
        	$("#Chirstmas-Rewards-Input-Mobile").popover('destroy');
        	validData = true;
        }

        //password validation
        if(validData) {
            if(authenticationMethod === "Password") {
                if(!password || password.length < 6) {
                    $("#Chirstmas-Rewards-Input-Password").css('border','2px solid red');			//error indication
                    //error popover
                    $("#Chirstmas-Rewards-Input-Password").popover({
                        placement: "bottom",
                        trigger: "hover"
                    });
                    $('#Chirstmas-Rewards-Input-Password').data('bs.popover').options.content = "Incorrect Password";
                    validData = false;
                }
                else {
                	$("#Chirstmas-Rewards-Input-Password").popover('destroy');
                	validData = true;
                }
            }

            //passcode validation
            else if(authenticationMethod === "OTP") {
                if(passcode.length !== 4) {
                    $("#Chirstmas-Rewards-Input-OTP").css('border','2px solid red');			//error indication
                    //error popover
                    $("#Chirstmas-Rewards-Input-OTP").popover({
                        placement: "bottom",
                        trigger: "hover"
                    });
                    $('#Chirstmas-Rewards-Input-OTP').data('bs.popover').options.content = "Enter Valid Passcode";
                    validData = false;
                }
            	else {
                	$("#Chirstmas-Rewards-Input-Password").popover('destroy');
                	validData = true;
                }
            }
        }

        if(validData) {
            $.ajax({
                type: "POST",
                url: window.location.protocol + '//' + window.location.host + '/token/login',
                data: {
                    mobile: mobile,
                    pwd: password,
                    remember: 'false'
                },
                dataType: "json",
                success: function(e) {
                    var msg = JSON.stringify(e);
                    if (e.success == 1)
                    {
                        var userID = parseInt(e.user_id);
                        //Console.log("phone " + e.user_mobile);
                        $("#RewardsPanel").find(".Chirstmas-Rewards-User-Name").html(e.user_fname + " " + e.user_lname);
                        $("#RewardsPanel").removeClass("collapse");
                        $("#LoginPanel").addClass("collapse");

                        //add Progress Information
                        getPhaseProgress(userID);
                    }
                    else {
                        alert("Error! (Code: " + e.success + ")");
                    }

                },
                error: function(e) {
                        if(authenticationMethod === "Password") {
                            $("#Chirstmas-Rewards-Input-Password").css('border','2px solid red');			//error indication
                            //error popover
                            $("#Chirstmas-Rewards-Input-Password").popover({
                                placement: "bottom",
                                trigger: "hover"
                            });
                            $('#Chirstmas-Rewards-Input-Password').data('bs.popover').options.content = "Incorrect Password";
                        }

                        else if(authenticationMethod === "OTP") {
                            $("#Chirstmas-Rewards-Input-OTP").css('border','2px solid red');			//error indication
                            //error popover
                            $("#Chirstmas-Rewards-Input-OTP").popover({
                                placement: "bottom",
                                trigger: "hover"
                            });
                            $('#Chirstmas-Rewards-Input-OTP').data('bs.popover').options.content = "Invalid Passcode";
                        }
                }
            });
        }
    });

    //Code for reset from erroneous fields..........................................................
    $("#Chirstmas-Rewards-Input-Password").click(function() {
        $(this).css('border','');
    });
    $("#Chirstmas-Rewards-Input-Mobile").click(function() {
        $(this).css('border','');
    });
    $("#Chirstmas-Rewards-Input-OTP").click(function() {
        $(this).css('border','');
    });
});

function OTPTimer() {
    var countDownDate = new Date(new Date().getTime() + window.OTPMins * 60 * 1000);
    window.OTPCountdown = setInterval(function() {

        // Get today's date and time
        var OTPTimerDMHS = CountDownDMHS(new Date(), countDownDate);

        $("#Chirstmas-Rewards-OTP-Timer").html(OTPTimerDMHS["hours"] + " : " +
                                                OTPTimerDMHS["minutes"] + " : " +
                                                OTPTimerDMHS["seconds"]);

        // If the count down is over, write some text
        if (distance < 0) {
            clearInterval(window.OTPCountdown);
            $("#Chirstmas-Rewards-OTP-Timer").html("");
            $("#Chirstmas-Rewards-Button-OTPGen").attr('disabled', false);
            window.OTPMins *= 3;
        }
    }, 1000);
}

function EventStartCountdownTicker() {
    window.StartTick = setInterval(function() {
        var EventStartCountDownDMHS = CountDownDMHS(new Date(), window.EventEndDate);

        $("#Event-Tick").html(EventStartCountDownDMHS["days"] + "D:" +
                                EventStartCountDownDMHS["hours"] + "H:" +
                                EventStartCountDownDMHS["minutes"] + "M");

        // If the count down is over, write some text
        if (EventStartCountDownDMHS["distance"] < 0) {
            clearInterval(window.StartTick);
            $("#Event-Tick").html("");
        }
    }, 60000);
}

function CountDownDMHS(fromDate, targetDate) {
    var distance = targetDate - fromDate;
    // Time calculations for days, hours, minutes and seconds
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        hours = hours > 9 ? hours : "0" + hours;
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        minutes = minutes > 9 ? minutes : "0" + minutes;
        var seconds = Math.floor((distance % (1000 * 60)) / 1000);
        seconds = seconds > 9 ? seconds : "0" + seconds;
        return { "days" : days, "hours" : hours, "minutes" : minutes, "seconds" : seconds, "distance" : distance };
}

function getPhaseProgress(userID) {
    var p1Count = 0, p2Count = 0, p3Count = 0;

    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/admin/event_complete_trips_count',
        data: {
            user_id: userID,
            start_date: "25-12-2019",
            end_date: "28-12-2019",
        },
        dataType: "json",
        success: function(response) {
            if (response.success == 1)
            {
                p1Count = parseInt(response.trips);
                var phase1Progress = p1Count > 3 ? 100 : (p1Count / 3 * 100);
                addProgressPhase("ICE", phase1Progress);
                if(p1Count >= 3) $("#Phase-ICE-Marker").addClass("phase-reward");
            }
            else {
                alert("Error! (Code: " + response.success + ")");
            }

        },
        error: function(e) {
            alert("Error! Please Contact System Administrators");
        }
    });

    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/admin/event_complete_trips_count',
        data: {
            user_id: userID,
            start_date: "29-12-2019",
            end_date: "01-01-2020",
        },
        dataType: "json",
        success: function(response) {
            if (response.success == 1)
            {
                p2Count = parseInt(response.trips);
                var phase2Progress = p2Count > 3 ? 100 : (p2Count / 3 * 100);
                addProgressPhase("HOLLY", phase2Progress);
                if(p2Count >= 3) $("#Phase-HOLLY-Marker").addClass("phase-reward");
            }
            else {
                alert("Error! (Code: " + response.success + ")");
            }

        },
        error: function(e) {
            alert("Error! Please Contact System Administrators");
        }
    });

    $.ajax({
        type: "POST",
        url: window.location.protocol + '//' + window.location.host + '/api/admin/event_complete_trips_count',
        data: {
            user_id: userID,
            start_date: "02-01-2020",
            end_date: "05-01-2020",
        },
        dataType: "json",
        success: function(response) {
            if (response.success == 1)
            {
                p3Count = parseInt(response.trips);
                var phase3Progress = p3Count > 4 ? 100 : (p3Count / 4 * 100);
                addProgressPhase("PRESENT", phase3Progress);
                if(p3Count >= 4) $("#Phase-PRESENT-Marker").addClass("phase-reward");
                var totalCount = p1Count + p2Count + p3Count;
                var overallProgress = totalCount > 12 ? 100 : (totalCount / 12 * 100);
                addProgressPhase("STAR", overallProgress);
                if(totalCount >= 12) $("#Phase-STAR-Marker").addClass("phase-reward");
            }
            else {
                alert("Error! (Code: " + e.success + ")");
            }

        },
        error: function(e) {
            alert("Error! Please Contact System Administrators");
        }
    });
}

function addProgressPhase(phase, phaseValue) {
    if(phaseValue >= 0) {
        var completionIndicator = phaseValue >= 100 ? " progress-full" : "";
        $("#Phase-" + phase).html
        (
            '<div class="progress'+ completionIndicator +'">' +
                '<div class="progress-bar progress-bar-striped active" role="progressbar" aria-valuenow="'+ phaseValue +'" aria-valuemin="0" aria-valuemax="100" style="width:' + phaseValue +'%"></div>' +
            '</div>'
        );
    }
}
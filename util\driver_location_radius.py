from main import app
from sqlalchemy import create_engine, text
import pandas as pd
from urllib.parse import quote
import os

# Database connection using SQLAlchemy
engine = create_engine(app.config['SQLALCHEMY_DATABASE_URI'])

# List of locations with their latitudes and longitudes
locations = [
    {"name": "Location 1", "latitude": 22.581510651204404, "longitude": 88.30639137069994}
]

# SQL query template
query_template = text("""
SELECT 
    d.driver_id,
    CONCAT(u.user_fname, ' ', u.user_lname) AS driver_name,
    u.user_mobile AS driver_mobile,
    u.user_registration AS registration_date,
    CASE 
        WHEN d.driver_approved = 1 THEN 'Approved'
        WHEN d.driver_approved = -1 THEN 'Verification Pending'
        WHEN d.driver_approved = -2 THEN 'Unapproved'
        WHEN d.driver_approved = -3 THEN 'Inactive'
        ELSE 'Unknown Status'
    END AS driver_status, 
    di.di_pres_region AS Region,
    di.di_pres_addr_lat AS latitude, 
    di.di_pres_addr_lng AS longitude,
    (6371 * acos(cos(radians(:latitude)) * cos(radians(di.di_pres_addr_lat)) * cos(radians(di.di_pres_addr_lng) - radians(:longitude)) + sin(radians(:latitude)) * sin(radians(di.di_pres_addr_lat)))) AS distance,
    COUNT(b.book_ref) AS completed_trips
FROM 
    drivers d
JOIN 
    driver_info di ON d.driver_id = di.di_driver_id
JOIN 
    users u ON d.driver_user = u.user_id
LEFT JOIN 
    bookings b ON b.book_driver = d.driver_id AND b.book_valid = 1
GROUP BY 
    d.driver_id,
    driver_name,
    driver_mobile,
    registration_date,
    driver_status,
    latitude, 
    longitude
HAVING 
    distance <= 4
ORDER BY 
    distance ASC;
""")

# Function to run the query and save the results to a CSV file
def generate_csv_for_location(location):
    with engine.connect() as connection:
        result = connection.execute(
            query_template,
            {
                "latitude": location["latitude"],
                "longitude": location["longitude"]
            }
        )
        #result = connection.execute(query_template, latitude=location["latitude"], longitude=location["longitude"])
        df = pd.DataFrame(result.fetchall(), columns=result.keys())

    # Ensure the output folder exists
    output_folder = "output"
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # Save to CSV
    csv_filename = f"{location['latitude']}, {location['longitude']}.csv"
    file_path = os.path.join(output_folder, csv_filename)
    df.to_csv(file_path, index=False)
    print(f"Generated CSV: {csv_filename}")

# Generate CSV files for all locations
for location in locations:
    generate_csv_for_location(location)

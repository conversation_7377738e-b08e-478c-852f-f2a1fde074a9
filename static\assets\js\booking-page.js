var step0 = true;
var step1= false;
var step2= false;
var step3= false;
var step4= false;
var tripType=1;
var durSel="";
var dateSel="";
var timeSel	="";
var currentItemId="hatchback";
var carType=0;
var datePicker = DatePicker("#date-picker");
var searchRes = "";
var bookedData = "";
var paymentType = -1;
//var timePicker = TimePicker("#time-picker");

$( document ).ready(function() {

    $('[data-toggle="popover"]').popover({
        content: function() {
            return $('#popover-content').html();
        }
    });

    var cookie = getCookie();
    var refresh = cookie['csrf_refresh_token'];
    var access = cookie['csrf_access_token'];
    if (!access || !refresh) {
        window.location = window.location.protocol + '//' + window.location.host + '/login';
    }
  var carSelect = $('.carSelect').flipster({
      itemContainer: 'ul',
      itemSelector: 'li',
      fadeIn: 400,
      loop: false,
      pauseOnHover: true,
      style: 'flat',
      spacing: 0,
      click: true,
      keyboard: true,
      scrollwheel: true,
      touch: true,
      nav: false,
      start: 0,
      buttons: false,
      onItemSwitch: function(currentItem, previousItem){
		currentItemId = currentItem.id
      }
  });
    mobiscroll.settings = {
        theme: 'ios',
        themeVariant: 'light'
    };

    $("#sourceSearch").click(function() {
        $(".red-circle").addClass("circle-inactive");
        $(".green-circle").removeClass("circle-inactive");
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
    });

    $("#destinationSearch").click(function() {
        $(".red-circle").removeClass("circle-inactive");
        $(".green-circle").addClass("circle-inactive");
        pickupMarker.setVisible(false);
        /*if(!destinationLat && !destinationLong) {
            destinationMarker.setPosition(pickupMarker.getPosition());
        }*/
        if(destinationLat !== -91 && destinationLong !== -91) {
            destinationMarker.setVisible(true);
            map.panTo(destinationMarker.getPosition());
        }
    });


    $('#continueTripType').on("click", function() {
        if(!$("#sourceSearch").val()) {
            showSnackbar("Please specify pickup point", "");
            return;
        }
        else if(destinationLat === -91 && [2,3,4].indexOf(tripType) > -1) {
            showSnackbar("Please specify destination", "");
            return;
        }
        if(!google.maps.geometry.poly.containsLocation(pickupMarker.position, service_area)){
          showSnackbar("Service is currently unavailable in this location", "");
          return;
        }
        if([2,3,4].indexOf(tripType) > -1){
          if(!google.maps.geometry.poly.containsLocation(destinationMarker.position, service_area) && tripType == 2){
            showSnackbar("One Way must be within city", "");
            return;
          }
          destinationMarker.setVisible(true)
          pickupMarker.setVisible(true)
          var latlng = [
              pickupMarker.position,
              destinationMarker.position
          ];
          var latlngbounds = new google.maps.LatLngBounds();
          for (var i = 0; i < latlng.length; i++) {
              latlngbounds.extend(latlng[i]);
          }
          map.fitBounds(latlngbounds);
        }
        pickupMarker.setDraggable(false);
        destinationMarker.setDraggable(false);
        step0=false;
        step1=true;
        $("#locations").addClass("collapse");
        $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
        $("#carType").toggle( "slide", { direction: "down", duration: 500 });
    });
    $('#closeCarType').on("click", function() {
      step0=true;
      step1=false;
      pickupMarker.setDraggable(true);
      destinationMarker.setDraggable(true);
      if(!$(".red-circle").hasClass("circle-inactive")){
        $(".red-circle").addClass("circle-inactive");
        $(".green-circle").removeClass("circle-inactive");
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }else if($(".red-circle").hasClass("circle-inactive")){
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }
      $("#carType").toggle( "slide", { direction: "down", duration: 500 });
      $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
	  $("#locations").removeClass("collapse");
    });
    $('#continueCarType').on("click", function() {
        step1=false;
        step2=true;
		if(currentItemId=='hatchback'){
          carType = 0;
        }
        else if(currentItemId=='sedan'){
          carType = 1;
        }
        else if(currentItemId=='suv'){
          carType = 2;
        }
        else if(currentItemId=='luxury'){
          carType = 3;
        }
		if ($("#auto").is(':checked')) {
			carType += 4;
		}
        $("#carType").toggle( "slide", { direction: "down", duration: 500 });
        $("#schedule").toggle( "slide", { direction: "down", duration: 500 });
		$("#dateText").text("Select Date");
		$("#timeText").text("Select Time");
        if(tripType === 4) {
            $("#durText").text("Select Number of Days");
        }
        else {
            $("#durText").text("Select Duration");
        }
        var today = new Date();
        var endDate = new Date(today.getTime() + 10 * 24 * 60 * 60 * 1000);
    });

    $('#closeSchedule').on("click", function() {
      pickupMarker.setDraggable(true);
      destinationMarker.setDraggable(true);
      step0=true;
      step2=false;
      if(!$(".red-circle").hasClass("circle-inactive")){
        $(".red-circle").addClass("circle-inactive");
        $(".green-circle").removeClass("circle-inactive");
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }else if($(".red-circle").hasClass("circle-inactive")){
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }
      $("#schedule").toggle( "slide", { direction: "down", duration: 500 });
      $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
	  $("#locations").removeClass("collapse");
	  durSel = dateSel = timeSel = "";
    });

    $('#continueSchedule').on("click", function() {
        // Date not provided
        if(!dateSel) {
            showSnackbar("Please select a date!", "");
            return;
        }
        //Time not provided
        else if(!timeSel) {
            showSnackbar("Please specify the time!", "");
            return;
        }
        //Duration not provided
        else if(!durSel) {
            if(tripType === 4) {
                showSnackbar("Please select number of days!", "");
                return;
            }
            else {
                showSnackbar("Please select the duration!", "");
                return;
            }
        }
		// at this point, we have values, so removing if (...)
		var curDate = mobiscroll.date('#date-picker').getVal();
		var curTime = mobiscroll.time('#time-picker').getVal();
		var actDur = getParsedDur(durSel, tripType);

		var actDT = "";
		var date = zeroPad(curDate.getDate());
		var month = zeroPad(curDate.getMonth()+1);
		var year = curDate.getFullYear();
		var hour = zeroPad(curTime.getHours());
		var minute = zeroPad(curTime.getMinutes());
		var sec = zeroPad(curTime.getSeconds());
		var tz = convTZ(curTime.getTimezoneOffset());
		actDT = date + "/" + month + "/" + year + " " + hour + ":" + minute + ":" + sec + " " + tz;
		// check if booking date time is within prescribed bounds
		var scheduledDate = new Date(year,month-1,date,hour,minute,sec);
		/*scheduledDate.setDate(date);
		scheduledDate.setMonth(month);
		scheduledDate.setYear(year);
		scheduledDate.setHours(hour);
		scheduledDate.setMinutes(minute);
		scheduledDate.setSeconds(sec);*/
		console.log(date);
		console.log(scheduledDate);
		if(scheduledDate - new Date() < minBookingThreshold-60000) {
		  showSnackbar("Booking time should be atleast " + (minBookingThreshold / 3600 / 1000) + " hour(s) prior.");
		  return;
		}

	   if ([2,3,4].indexOf(tripType) > -1)
	        getEstimate(tripType, pickupLat, pickupLong, carType, getParsedDur(durSel, tripType), actDT, destinationLat, destinationLong, $("#destinationSearch").val());
	   else
	        getEstimate(tripType, pickupLat, pickupLong, carType, getParsedDur(durSel, tripType), actDT)
		showLoader();
    });

    $('#closeFareEstimate').on("click", function() {
      pickupMarker.setDraggable(true);
      destinationMarker.setDraggable(true);
      step0=true;
      step3=false;
      if(!$(".red-circle").hasClass("circle-inactive")){
        $(".red-circle").addClass("circle-inactive");
        $(".green-circle").removeClass("circle-inactive");
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }else if($(".red-circle").hasClass("circle-inactive")){
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }
      $("#fareEstimate").toggle( "slide", { direction: "down", duration: 500 });
      $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
	  $("#locations").removeClass("collapse");
      dateSel = timeSel = durSel = "";
    });

    $('#confirmFareEstimate').on("click", function() {
		$("#finalAmount").text($("#estimatedAmount").text())
		$("#finalDate").text($(".date-text").text())
		$("#finalTime").text($(".time-text").text())
		$("#finalDur").text($(".dur-text").text())
		$("#finalDurUnit").text($(".pre-book-time-unit-text").text())

		if (paymentType == -1) {
			showSnackbar("Please specify the payment mode!", "");
            return;
        }
		// show animation
		if ([2,3,4].indexOf(tripType) > -1)
			bookTrip(searchRes.id, $("#sourceSearch").val(), tripType, paymentType, destinationLat, destinationLong, $("#destinationSearch").val())
		else
			bookTrip(searchRes.id, $("#sourceSearch").val(), tripType, paymentType)
		showLoader();
    });
    $('#doneConfirmation').on("click", function() {
	  if (bookedData == "") {
	  } else {
      pickupMarker.setDraggable(true);
      destinationMarker.setDraggable(true);
		  step4=false;
		  step0=true;
      if(!$(".red-circle").hasClass("circle-inactive")){
        $(".red-circle").addClass("circle-inactive");
        $(".green-circle").removeClass("circle-inactive");
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }else if($(".red-circle").hasClass("circle-inactive")){
        pickupMarker.setVisible(true);
        destinationMarker.setVisible(false);
        map.panTo(pickupMarker.getPosition());
      }
		  $("#confirmation").toggle( "slide", { direction: "down", duration: 500 });
		  $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
		  $("#locations").removeClass("collapse");
		  bookedData = "";
		  searchRes = "";
	  }
    });

    // EVENT HANDLERS
	// Event handler for clicking on a trip type option
	$(".trip-type").click(function() {

        if([2,3,4].indexOf(tripType) > -1 && parseInt($(this).attr("trip-type-value")) === 1) {
            $("#locations").animate({height:92.5},750);
            $("#locSeparator").toggle( "slide", { direction: "up", duration: 750 });
            $("#destinationAddressMarker").toggle( "slide", { direction: "up", duration: 750 });
            $("#destinationSearch").toggle( "slide", { direction: "up", duration: 750 });
            $("#locSeparator").addClass("collapse");
            $("#destinationAddressMarker").addClass("collapse");
            $("#destinationSearch").addClass("collapse");
            if(!$(".red-circle").hasClass("circle-inactive")){
              $(".red-circle").addClass("circle-inactive");
              $(".green-circle").removeClass("circle-inactive");
              pickupMarker.setVisible(true);
              destinationMarker.setVisible(false);
              map.panTo(pickupMarker.getPosition());
            }
        }

        else if([2,3,4].indexOf(parseInt($(this).attr("trip-type-value"))) > -1 && tripType === 1) {
            $("#locations").animate({height:185}, 750);
            $("#locSeparator").toggle( "slide", { direction: "up", duration: 750 });
            $("#destinationAddressMarker").toggle( "slide", { direction: "up", duration: 750 });
            $("#destinationSearch").toggle( "slide", { direction: "up", duration: 750 });
            $("#locSeparator").removeClass("collapse");
            $("#destinationAddressMarker").removeClass("collapse");
            $("#destinationSearch").removeClass("collapse");
        }
		tripType = parseInt($(this).attr("trip-type-value"));
        var prevImg = $(".trip-type.active").find(".trip-type-image").attr("src");
        $(".trip-type.active").find(".trip-type-image").attr("src", prevImg.replace("_Selected", ""));
		$(".trip-type").removeClass("active");
        var newImg = $(this).find(".trip-type-image").attr("src");
        $(this).find(".trip-type-image").attr("src", newImg.replace(".svg", "_Selected.svg"));
		$(this).addClass("active");
	});

    $("#openDatePicker").click(function() {
        var minDate = new Date();
        minDate.setHours(minDate.getHours() + (minBookingThreshold / 3600 / 1000));
        var endDate = new Date(minDate.getTime() + 10 * 24 * 60 * 60 * 1000);
        mobiscroll.date('#date-picker', {
            display: 'center',
            min: minDate,
            max: endDate,
            onInit: function (event, inst) {
                inst.setVal(minDate, true);
            },
			onSet: function (event, inst) {
			   dateSel = event.valueText;
               var formattedDate = fmtDate(dateSel)
			   $("#dateText").text("Chosen Date: " + formattedDate);
		   },
        });
        $("#date-picker").trigger("click");
    });

    $("#openTimePicker").click(function() {
        var minTime = new Date();
        minTime.setHours(minTime.getHours() + (minBookingThreshold / 3600 / 1000));
        mobiscroll.time('#time-picker', {
            display: 'center',
            onInit: function (event, inst) {
                inst.setVal(minTime, true);
            },
			onSet: function (event, inst) {
			   timeSel = event.valueText;
			   $("#timeText").text("Time: " + timeSel);
		   },
        });
        $("#time-picker").trigger("click");
    });

    $("#openDurationPicker").click(function() {
        mobiscroll.scroller('#dur-picker', {
            display: 'center',
            wheels: [
               [{
                   data: getDurOptions()
               }]
           ],
           circular: false,
		   onSet: function (event, inst) {
			   durSel = event.valueText;
			   if (tripType == 4) {
			      $("#durText").text("Duration: " + durSel + " day(s)")
			   } else {
				  $("#durText").text("Duration: " + durSel + " hours")
			   }
		   },
        });
        $("#dur-picker").trigger("click");

    });

    $("#alterDur").click(function() {
        mobiscroll.scroller('#dur-picker', {
            display: 'center',
            wheels: [
               [{
                   data: getDurOptions()
               }]
           ],
           circular: false,
           onInit: function (event, inst) {
               inst.setVal(durSel, true);
           },
		   onSet: function (event, inst) {
			   durSel = event.valueText;
			   if (tripType == 4) {
			      $(".pre-book-time-unit-text").text(" day(s)")
			   } else {
				  $(".pre-book-time-unit-text").text(" hours")
			   }
               $(".dur-text").text(durSel);
			   var curDate = mobiscroll.date('#date-picker').getVal();
			   var curTime = mobiscroll.time('#time-picker').getVal();
			   var actDT = "";
			   var date = zeroPad(curDate.getDate());
			   var month = zeroPad(curDate.getMonth()+1);
			   var year = curDate.getFullYear();
			   var hour = zeroPad(curTime.getHours());
			   var minute = zeroPad(curTime.getMinutes());
			   var sec = zeroPad(curTime.getSeconds());
			   var tz = convTZ(curTime.getTimezoneOffset());
			   actDT = date + "/" + month + "/" + year + " " + hour + ":" + minute + ":" + sec + " " + tz;
			   if ([2,3,4].indexOf(tripType) > -1)
			        getEstimate(tripType, pickupLat, pickupLong, carType, getParsedDur(durSel, tripType), actDT, destinationLat, destinationLong, $("#destinationSearch").val());
    		   else
    		        getEstimate(tripType, pickupLat, pickupLong, carType, getParsedDur(durSel, tripType), actDT)
			   showLoader();

		   },
        });
        $("#dur-picker").trigger("click");
    });

    $("#alterDateTime").click(function() {
        var scheduledDate = new Date();
        scheduledDate.setHours(scheduledDate.getHours() + (minBookingThreshold / 3600 / 1000));
        var endDate = new Date(scheduledDate.getTime() + 10 * 24 * 60 * 60 * 1000);
        mobiscroll.date('#date-picker', {
            display: 'center',
            min: scheduledDate,
            max: endDate,
            onInit: function (event, inst) {
                inst.setVal(scheduledDate, true);
            },
			onSet: function (event, inst) {
			   dateSel = event.valueText;
               var formattedDate = fmtDate(dateSel);

               // After date change is confirmed, open time picker:
               var scheduledTime = new Date();
               scheduledTime.setHours(scheduledTime.getHours() + (minBookingThreshold / 3600 / 1000));
               mobiscroll.time('#time-picker', {
                   display: 'center',
                   onInit: function (event, inst) {
                       inst.setVal(scheduledTime, true);
                   },
           			onSet: function (event, inst) {
           			   timeSel = event.valueText;
					   $(".date-text").text(formattedDate);
           			   $(".time-text").text(timeSel);
					   var curDate = mobiscroll.date('#date-picker').getVal();
					   var curTime = mobiscroll.time('#time-picker').getVal();
					   var actDT = "";
					   var date = zeroPad(curDate.getDate());
					   var month = zeroPad(curDate.getMonth()+1);
					   var year = curDate.getFullYear();
					   var hour = zeroPad(curTime.getHours());
					   var minute = zeroPad(curTime.getMinutes());
					   var sec = zeroPad(curTime.getSeconds());
					   var tz = convTZ(curTime.getTimezoneOffset());
					   var dSel = $(".dur-text").text();
					   actDT = date + "/" + month + "/" + year + " " + hour + ":" + minute + ":" + sec + " " + tz;
        			   if ([2,3,4].indexOf(tripType) > -1)
        			        getEstimate(tripType, pickupLat, pickupLong, carType, getParsedDur(durSel, tripType), actDT, destinationLat, destinationLong, $("#destinationSearch").val());
            		   else
            		        getEstimate(tripType, pickupLat, pickupLong, carType, getParsedDur(durSel, tripType), actDT)
					   showLoader();
           		   }
               });
               $("#time-picker").trigger("click");

		   },
        });
        $("#date-picker").trigger("click");

    });

    $("#alterPaymentMode").click(function() {
        mobiscroll.image('#payment-picker', {
        display: 'center',
        enhance: true,
        showInput: false,
        onSet:  function (event, inst) {
            if(event.valueText=='0'){
              paymentType = 0;
              $("#payment-type-image").attr("src", "../static/assets/images/elements/Cash.svg");
              $("#payment-type-text").text("Cash");
           } else if(event.valueText=='1'){
             paymentType = 1;
             $("#payment-type-text").text("D4M Credit");
             $("#payment-type-image").attr("src", "../static/assets/images/elements/d4m_credit.svg");
           }
         }
      });
      $("#payment-picker").trigger("click");
    });

    $("#pricingInfo").click(function() {
        if(tripType === 4) {
            $(".popover-body").html($("#estimateBreakdownModal").find(".os-ot-policy").html() + "<br>" + $("#estimateBreakdownModal").find(".os-expense-policy").html());
        }
        else {
            $(".popover-body").html($("#estimateBreakdownModal").find(".night-policy").html() + "<br>" + $("#estimateBreakdownModal").find(".normal-ot-policy").html());
        }
    });

    $("body").delegate(".popover", "click", function() {
       $("#pricingInfo").popover("hide");
    });

    $("#viewFareBreakdown").click(function() {
        /* Make a separate function call */
        $("#estimateBreakdownModal").find(".night-bounds-start").html(NIGHT_START);
        $("#estimateBreakdownModal").find(".night-bounds-end").html(NIGHT_END);
        if(tripType === 4) {
            //$("#estimateBreakdownModal").find(".night-policy").addClass("collapse");
            $("#estimateBreakdownModal").find(".normal-ot-policy").addClass("collapse");
            $("#estimateBreakdownModal").find(".os-ot-policy").removeClass("collapse");
            //$("#estimateBreakdownModal").find(".os-expense-policy").removeClass("collapse");
        }

        else {
            //$("#estimateBreakdownModal").find(".night-policy").removeClass("collapse");
            $("#estimateBreakdownModal").find(".normal-ot-policy").removeClass("collapse");
            $("#estimateBreakdownModal").find(".os-ot-policy").addClass("collapse");
            //$("#estimateBreakdownModal").find(".os-expense-policy").addClass("collapse");
        }
		if (parseInt($("#estimateNight").text()) != 0) {
			$(".estimate-night").removeClass('collapse')
		} else {
			$(".estimate-night").addClass('collapse')
		}
        $("#estimateBreakdownModal").modal("show");
    });

    $("#viewFinalFareBreakdown").click(function() {
        $("#estimateBreakdownModal").find(".night-bounds-start").html(NIGHT_START);
        $("#estimateBreakdownModal").find(".night-bounds-end").html(NIGHT_END);
        if(tripType === 4) {
            //$("#estimateBreakdownModal").find(".night-policy").addClass("collapse");
            $("#estimateBreakdownModal").find(".normal-ot-policy").addClass("collapse");
            $("#estimateBreakdownModal").find(".os-ot-policy").removeClass("collapse");
            //$("#estimateBreakdownModal").find(".os-expense-policy").removeClass("collapse");
        }

        else {
            //$("#estimateBreakdownModal").find(".night-policy").removeClass("collapse");
            $("#estimateBreakdownModal").find(".normal-ot-policy").removeClass("collapse");
            $("#estimateBreakdownModal").find(".os-ot-policy").addClass("collapse");
            //$("#estimateBreakdownModal").find(".os-expense-policy").addClass("collapse");
        }
		if (parseInt($("#estimateNight").text()) != 0) {
			$(".estimate-night").removeClass('collapse')
		} else {
			$(".estimate-night").addClass('collapse')
		}
        $("#estimateBreakdownModal").modal("show");
    });

    $("#onTripExtensionToggle").click(function() {
        if($("#onTripDetails").hasClass("shrinked")) {
            //expand on trip section
            $("#onTripDetails").removeClass("shrinked").addClass("extended-section");
            $("#newBooking").addClass("extended");
            $(this).find("img").attr("src", $(this).find("img").attr("src").replace("up", "down"));
        }
        else {
            //collapse on trip section
            $("#onTripDetails").removeClass("extended-section").addClass("shrinked");
            $("#newBooking").removeClass("extended");
            $(this).find("img").attr("src", $(this).find("img").attr("src").replace("down", "up"));
        }
    });

    $("#currentTripDetails").click(function () {
        $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
        $("#onTripDetails").toggle( "slide", { direction: "down", duration: 500 });
        $("#locations").addClass("collapse");
        $("#newBooking").removeClass("button-hide");
        $("#liveTripRefresh").removeClass("button-hide");
        $(this).addClass("button-hide");
    });

    $("#newBooking").click(function() {
        $("#tripType").toggle( "slide", { direction: "down", duration: 500 });
        $("#onTripDetails").toggle( "slide", { direction: "down", duration: 500 });
        $("#locations").removeClass("collapse");

        // whether or not the current trip details are visible again, would be conditional. Condition to be added
        $("#currentTripDetails").removeClass("button-hide");

        $("#liveTripRefresh").addClass("button-hide");
        $(this).addClass("button-hide");
    });
	var config = {
        apiKey: "AIzaSyCY7Bp_IhHJ8fySdrOswK8PE-kLjU6Z77Y",
        databaseURL: "https://drivers4me-prod-default-rtdb.asia-southeast1.firebasedatabase.app/",
	  };
	  firebase.initializeApp(config);

	// Get a reference to the database service
	var database = firebase.database();
	var userId = getCookie()['user_id']
	if (!userId) userId = 0;
	database.ref('/upcomingCountCustomer/' + userId).on('value', function(snapshot) {
		try {
			var pending = snapshot.val().pending;
			var upcoming = snapshot.val().upcoming;
			$("#pending-count").html(pending);
			if (pending)
				$("#pendingBookings").removeClass("button-hide");
			else
				$("#pendingBookings").addClass("button-hide");
		} catch(err) {
			$("#pendingBookings").removeClass("button-hide");
		}
	});
});

function setEstData(resp, fromMain=false) {
	searchRes = resp;
	if (resp['id'] == -1) {
        showSnackbar("Could not find estimate!", "");
        hideLoader();
        return;
	}
    const s3 = '\u20b9' //é
	var min_est = searchRes.min_est
	var max_est = searchRes.max_est
	if (min_est == max_est)
		$("#estimatedAmount").text(s3+' '+min_est);
	else
		$("#estimatedAmount").text(3+' '+min_est + " - " + s3+' '+ max_est);
	$(".date-text").text(fmtDate(dateSel))
	$(".time-text").text(timeSel)
	if (tripType == 4) //Outstation
		$(".pre-book-time-unit-text").text(" day(s)")
	else
		$(".pre-book-time-unit-text").text(" hours")
	var totalBase = parseInt(searchRes.base_ch) + parseInt(searchRes.booking_ch) +parseInt(searchRes.car_ch);
	var gst = parseInt(searchRes.sgst) + parseInt(searchRes.cgst);
	$("#estimateBase").text(totalBase);
	$("#estimateCar").text(searchRes.car_ch)
	$("#estimateBooking").text(searchRes.booking_ch)
	$("#estimateDist").text(searchRes.dist_ch)
	$("#estimateGST").text(gst);
	/*if (searchRes.dist_ch != 0)
	    $(".estimate-dist").removeClass('collapse');
	else
	    $(".estimate-dist").addClass('collapse');
	if (searchRes.booking_ch != 0)
	    $(".estimate-book").removeClass('collapse');
	else
	    $(".estimate-book").addClass('collapse');
    */
    if(gst !== 0) {
        $(".estimate-gst").removeClass("collapse");
    }
    else {
        $(".estimate-gst").addClass("collapse");
    }
	$("#estimateNight").text(searchRes.night)
	if (min_est == max_est)
		$("#estimateTotal").text(min_est)
	else
		$("#estimateTotal").text(min_est + " - " + s3+' '+ max_est);
	if (tripType == 4) {
		// Outstation
		$(".os-ot-value").text(searchRes.ot_rate)
	} else {
		$(".ot-level-1").text(parseInt(searchRes.ot_rate_0)/60.0.toFixed(2))
		$(".ot-level-2").text(parseInt(searchRes.ot_rate_1)/60.0.toFixed(2))
	}
	setSurchInd(searchRes.surcharge, searchRes.night_1, searchRes.night_2)
    $(".dur-text").html(durSel)


	if (!step3)  {
		if (!fromMain) $("#schedule").toggle( "slide", { direction: "down", duration: 500 });
		$("#fareEstimate").toggle( "slide", { direction: "down", duration: 500 });
		step2=false;
		step3=true;
		// reset payment mode
		paymentType = -1;
		if ('payment' in getCookie()) {
			paymentType = getCookie()['payment'] == 'credit'?1:0
		}
		console.log("test")
		if (paymentType == 1) {
			$("#payment-type-text").text("D4M Credit");
            $("#payment-type-image").attr("src", "../static/assets/images/elements/d4m_credit.svg");
		} else if (paymentType == 0) {
			$("#payment-type-image").attr("src", "../static/assets/images/elements/Cash.svg");
			$("#payment-type-text").text("Cash");
		} else {
			$("#payment-type-image").attr("src", "../static/assets/images/elements/select_payment_method.svg");
			$("#payment-type-text").text("Select Payment Mode");
		}
	}
	hideLoader();
}

// Set surge indicator icons based on estimate parameters
function setSurchInd(surch, night_1, night_2) {
    // order of icons, surge then night
    $("#ind-Surge").addClass("hidden");
    $("#ind-Night1").removeClass("collapse").addClass("hidden");
    $("#ind-Night2").removeClass("hidden").addClass("collapse");
    $("#ind-Night1").insertAfter("#ind-Surge");
    $("#ind-Night2").insertAfter("#ind-Night1");
    if(surch) {
        $("#ind-Surge").removeClass("hidden");
    }

    if(night_2) {
        $("#ind-Night2").removeClass("collapse");
        $("#ind-Night1").removeClass("hidden").addClass("collapse");
    }
    else if(night_1) {
        $("#ind-Night1").removeClass("hidden");
    }
    else {
        $("#ind-Surge").insertAfter("#ind-Night1");
    }
}

function setBookData(resp) {
	bookedData = resp;
	if (bookedData.result == -2) {
		hideLoader();
        showSnackbar("Booking exists for specified time", "");
		return;
	}
	if (!step4) {
		$("#fareEstimate").toggle( "slide", { direction: "down", duration: 500 });
		$("#confirmation").toggle( "slide", { direction: "down", duration: 500 });
		step3=false;
		step4=true;
	}
	hideLoader();
}

function getDurOptions() {
    if(tripType === 4) {
        return ['1','1.5','2','2.5','3','3.5','4','4.5','5','5.5','6','6.5','7','7.5','8','8.5','9','9.5','10'];
    }
    else if(tripType === 3) {
        return ['4','4.5','5','5.5','6','6.5','7','7.5','8','8.5','9','9.5','10','10.5','11','11.5','12','12.5','13','13.5','14'];
    }
    else {
        return ['2','2.5','3','3.5','4','4.5','5','5.5','6','6.5','7','7.5','8','8.5','9','9.5','10'];
    }
}

function convTZ(rawTZ) {
	rawTZ *= -1
	var hours = (rawTZ / 60);
	var rhours = Math.floor(hours);
	var minutes = (hours - rhours) * 60;
	var rminutes = Math.round(minutes);
	var sign = "+"
	if (rawTZ < 0)
		sign = "-"
	return sign + zeroPad(rhours) + zeroPad(rminutes)
}

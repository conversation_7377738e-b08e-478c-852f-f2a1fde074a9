import random
from faker import Faker
from models import Users, db

fake = Faker()

""" Test cases for api: /api/register_cust """

# Test case for incomplete form
def test_incomplete_form(client):
    form_data = {
        'mobile': f'{random.randint(7000000000, 9999999999)}',
        'pwd': fake.text()
    }
    response = client.post('/api/register_cust', data=form_data)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data[0]['response'] == 1
    assert json_data[0]['msg'] == "Incomplete form"

# Test case for invalid field
def test_invalid_fields(client):
    
    form_data = {
        'mobile': f'{random.randint(7000000000, 9999999999)}',
        'pwd': fake.text(),
        'fname': fake.name(),
        'email': 'invalid-email'
    }
    response = client.post('/api/register_cust', data=form_data)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data[0]['response'] == 2
    assert json_data[0]['msg'] == "Invalid fields"

# Test case for successful registration
def test_successful_registration(client):
    
    form_data = {
        'mobile': f'{random.randint(7000000000, 9999999999)}',
        'pwd': fake.text(),
        'fname': fake.name(),
        'lname': 'Doe',
        'email': fake.email()
    }

    response = client.post('/api/register_cust', data=form_data)
    json_data = response.get_json()

    assert response.status_code == 200
    assert json_data[0]['response'] == 0
    assert json_data[0]['msg'] == "Success"

# Test case with existing mobile
def test_registration_with_existing_mobile(client, customer_login):
    auth_header, user = customer_login()
    user = db.session.query(Users).filter(Users.id == user).first()
    form_data = {
        'mobile': user.mobile, 
        'pwd': fake.text(),
        'fname': fake.name(),
        'lname': 'Doe',
        'email': fake.email()
    }
    print(user.mobile)

    response = client.post('/api/register_cust', data=form_data)
    json_data = response.get_json()

    assert response.status_code == 200 

    assert json_data[0]['response'] == 4
    assert json_data[0]['msg'] == "Integrity constraint error"

# Test case with get request
def test_bad_request_method(client):
   
    form_data = {
        'mobile': f'{random.randint(7000000000, 9999999999)}',
        'pwd':  fake.text(),
        'fname': fake.name(),
        'lname': 'Doe',
        'email': fake.email()
    }
    response = client.get('/api/register_cust', data=form_data)

    assert response.status_code == 405